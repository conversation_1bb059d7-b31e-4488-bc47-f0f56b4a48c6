import React, {useState, useEffect, useCallback} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router-dom';
import styled from 'styled-components';
import uuidv4 from 'uuid/v4';
import {isValidFunction, labels} from '../../../../../util/utils';
import Utility from '../../../../../util/utilityfunctions';
import {summaryFilters, entities, validationTypes} from '../../../../../util/uiconstants';
import SummarySection from './SummarySection';
import {
	getValidationCountForIds,
	getValidationsByGroupType,
	GetGuidedWorkFlowValidationCount,
	getValidationFilterBasedOnSummaryFilter
} from '../../../../Common/CanvasForm/Common/ValidationUtility';
import {getHeaderById, getTaskDocumentByDocumentId} from '../../../../../selectors/Ares/selectors';
import {getAllSections} from '../../../../../selectors/Ares/selectors';
import {getValidationsForDocumentId} from '../../../../../selectors/Forms/canvasFormSelectors';
import {useIsDrawerMode} from '../../../../../util/Ares/customHooks';
import FormHeaderName from '../../Common/Form/FormHeaderName';
import FormHeaderAccordion from '../../Common/Form/FormHeaderAccordion';
import FormHeaderAccordionTrigger from '../../Common/Form/FormHeaderAccordionTrigger';
import FormHeaderAccordionContent from '../../Common/Form/FormHeaderAccordionContent';
import ValidationControl from '../../../../Common/CanvasForm/Common/ValidationControl';
import HeaderCommentCounter from '../../../../Common/EngagementComment/HeaderCommentCounter';
import {useClearAllComments} from '../../../../../util/customHooks';
import LoadingIndicator from '../../../../Common/LoadingIndicator/LoadingIndicator';
import CommentsValidationDropdown from './CommentsValidationDropdown';
import _ from 'lodash';

export default function SummaryHeader({
	engagementId,
	documentId,
	taskId,
	documentTypeId,
	headerId,
	summaryFilter,
	activeRoleId
}) {
	const header = useSelector((store) => getHeaderById(store, headerId)),
		validations = useSelector((store) => getValidationsForDocumentId(store, documentId)),
		sections = useSelector((store) => {
			return getAllSections(store);
		});

	const isDrawerMode = useIsDrawerMode();
	const queryParameters = isDrawerMode ? {isDrawer: true} : {};

	const [isOpen, setIsOpen] = useState(false),
		[isLoading, setIsLoading] = useState(false),
		[showComment, setShowComment] = useState(0),
		[newSections, setNewSections] = useState([]),
		[isCommentsDropdownOpen, setIsCommentsDropdownOpen] = useState(false);

	const navigate = useNavigate();

	const {
		unresolvedCount: comments,
		incompleteCount: incomplete,
		guidedWorkFlowValidations: guidedWorkFlowValidations
	} = getValidationCountForIds(
		headerId,
		undefined,
		undefined,
		validations,
		getValidationFilterBasedOnSummaryFilter(summaryFilter),
		documentTypeId
	);

	// Document approvals are signoffs for GuidedWorkFlow.
	const document = useSelector((storeState) => getTaskDocumentByDocumentId(storeState, documentId));

	const showValidationControl = document?.approvals && !_.isEmpty(document?.approvals);

	const {setClearAllComments} = useClearAllComments(
		engagementId,
		documentId,
		documentTypeId,
		entities.FormHeader,
		headerId,
		() => {
			setIsLoading(false);
			setClearAllComments(false);
		}
	);

	const setFilteredSections = () => {
		let filteredSections = [];

		if (
			header &&
			header.id > 0 &&
			(summaryFilter === summaryFilters.INCOMPLETERESPONSES || summaryFilter === summaryFilters.UNRESOLVEDCOMMENTS)
		) {
			if (validations && validations.length > 0) {
				/*get correct validations*/
				let validationsToShow = getValidationsByGroupType(validations, summaryFilter);
				let val = validationsToShow.flatMap((t) => t?.children);

				if (val && val.length > 0) {
					// Get the validations for the current header
					const headerValidations = val.find((vh) => Number(headerId) === Number(vh.validationId));
					if (headerValidations) {
						filteredSections = header.sections?.filter(
							(fsId) => headerValidations.children.findIndex((vs) => Number(vs.validationId) === Number(fsId)) !== -1
						);
					}
				}
			}
		} else {
			filteredSections = header?.sections;
		}

		setNewSections(filteredSections);
	};

	const handleOnFormHeaderAccordionTriggerClick = useCallback(
		(event) => {
			setIsOpen(!isOpen);
			if (!isOpen) {
				setFilteredSections();
			}
		},
		[isOpen]
	);

	const showComments = (count) => {
		setShowComment(count);
	};

	useEffect(() => {
		setFilteredSections();
		setIsOpen(false);
	}, [summaryFilter, activeRoleId]);

	useEffect(() => {
		setFilteredSections();
	}, [validations]);

	let validationLinksData = [];

	if (validations) {
		for (const key of Object.keys(validations)) {
			const item = validations[key];
			for (const childKeyAlpha of Object.keys(item.children)) {
				// Header level
				const itemDataAlpha = item.children[childKeyAlpha];

				if (
					itemDataAlpha &&
					itemDataAlpha.validationTypeId === validationTypes.CanvasFromComments &&
					Number(itemDataAlpha.validationId) === headerId &&
					itemDataAlpha.children &&
					Object.keys(itemDataAlpha.children).length > 0
				) {
					// Section level
					for (const childKeyBeta of Object.keys(itemDataAlpha.children)) {
						const itemDataBeta = itemDataAlpha.children[childKeyBeta];

						if (
							itemDataBeta &&
							itemDataBeta.validationTypeId === validationTypes.CanvasFromComments &&
							itemDataBeta.validationId.length > 0 &&
							!isNaN(Number(itemDataBeta.validationId)) &&
							itemDataBeta.children &&
							Object.keys(itemDataBeta.children).length > 0 &&
							newSections.map((x) => x.toString()).includes(itemDataBeta.validationId) &&
							sections.map((x) => x.id.toString()).includes(itemDataBeta.validationId)
						) {
							const sectionId = itemDataBeta.validationId;
							const sectionData = sections.find((x) => x.id.toString() === sectionId);
							validationLinksData[sectionId] = {
								title: sectionData.name,
								data: {sectionId},
								commentCount: itemDataBeta.validationCount
							};
						}
					}
				}
			}
		}
	}

	const validationLinks =
		validationLinksData.length > 0 && newSections.length > 0
			? newSections
					.filter((sectionId) => validationLinksData[sectionId])
					.map((sectionId) => validationLinksData[sectionId])
			: [];

	const commentsDropdownOnClickHandler = useCallback((link, callbacks) => {
		const {closeDropdown} = callbacks;
		const {sectionId} = link.data;

		if (isValidFunction(closeDropdown)) {
			closeDropdown();
		}

		if (window.location.pathname.indexOf('/summary') > -1) {
			const newURL = Utility.getAresTaskListNavigationUrl(
				documentId,
				headerId,
				sectionId,
				engagementId,
				taskId,
				queryParameters,
				documentTypeId
			);
			navigate(newURL);
		}
	}, []);

	const getShowValidation = () => {
		return (incomplete ?? 0) + (comments ?? 0) + (GetGuidedWorkFlowValidationCount(guidedWorkFlowValidations) ?? 0) > 0;
	};

	return (
		<StyledSummaryHeader
			id={`summary-header-${headerId}`}
			className={`StyledSummaryHeader ${isOpen ? 'isOpen' : 'isClosed'}`}
		>
			<LoadingIndicator fullscreen show={isLoading} />
			<FormHeaderAccordion open={isOpen}>
				<FormHeaderAccordionTrigger
					className="form-header-accordion-trigger"
					onClick={handleOnFormHeaderAccordionTriggerClick}
				>
					<FormHeaderName name={header?.name} />
				</FormHeaderAccordionTrigger>
				<FormHeaderAccordionContent className="AccordionBody">
					{newSections?.length > 0 ? (
						newSections.map((sectionId) => {
							return (
								<SummarySection
									key={sectionId}
									headerId={headerId}
									documentId={documentId}
									documentTypeId={documentTypeId}
									sectionId={sectionId}
									summaryFilter={summaryFilter}
									activeRoleId={activeRoleId}
								/>
							);
						})
					) : (
						<section className="NoRecordsAvailable">{labels.noRecordsAvailable}</section>
					)}
				</FormHeaderAccordionContent>
			</FormHeaderAccordion>
			<span
				className={`StyledRightSideForm  ${showComment ? 'CommentIconVisible' : 'CommentIconNotVisible'}
												 ${showValidationControl && getShowValidation() ? 'ValidationIconVisible' : 'ValidationIconNotVisible'}`}
			>
				<CommentsValidationDropdown
					isOpen={isCommentsDropdownOpen}
					setIsOpen={setIsCommentsDropdownOpen}
					validations={validationLinks}
					onClickItemHandler={commentsDropdownOnClickHandler}
					numberOfLinesToEllipse={1}
				>
					<HeaderCommentCounter
						documentId={documentId}
						headerId={headerId}
						showComments={showComments}
						initialCount={comments}
						documentTypeId={documentTypeId}
						className={isCommentsDropdownOpen ? 'activated' : 'deactivated'}
					/>
				</CommentsValidationDropdown>
				{showValidationControl && getShowValidation() && (
					<span className="header-validationCount SectionValidationWrapper">
						<ValidationControl
							key={uuidv4()}
							documentId={documentId}
							clearComments={() => {
								setIsLoading(true);
								setClearAllComments(true);
							}}
							incompleteResponseCount={incomplete ?? 0}
							unresolvedCommentCount={comments ?? 0}
							entityUId={headerId}
							entityId={entities.FormHeader}
							guidedWorkFlowValidationDetails={guidedWorkFlowValidations || []}
						/>
					</span>
				)}
			</span>
		</StyledSummaryHeader>
	);
}

const StyledSummaryHeader = styled.section`
	position: relative;
	&.isOpen {
		.StyledAccordion {
			.StyledAccordionTrigger {
				min-height: var(--px-40);
			}
		}
	}
	.StyledAccordion {
		min-height: var(--px-80);
		justify-content: center;
	}
	.StyledRightSideForm {
		position: absolute;
		right: var(--px-15);
		top: var(--px-20);
		display: inline-flex;
		width: auto !important;
		max-width: var(--px-180);
		margin: 0 0 0 auto;
		background-color: var(--neutrals-00white);
		border: var(--px-1) solid var(--neutrals-300);
		border-radius: var(--px-20);
		height: var(--px-32);
		&.NoBorder {
			border: 0;
		}
		&.CommentIconNotVisible {
			&.ValidationIconNotVisible {
				border: 0;
				display: none;
			}
		}
		.StyledCommentsMotifDropdownContainer {
			&.StyledCommentsMotifDropdownContainerOpen {
				.StyledSectionCommentCounter {
					background-color: var(--neutrals-900);
					color: var(--neutrals-00white);
					border-radius: var(--px-20) 0 0 var(--px-20);
					svg {
						color: var(--neutrals-00white) !important;
					}
					.CommentsCounter {
						color: var(--neutrals-00white);
					}
				}
			}
		}
		&.CommentIconVisible {
			&.ValidationIconVisible {
				.StyledSectionCommentCounter {
					&:hover,
					&:focus {
						border-radius: var(--px-20) 0 0 var(--px-20) !important;
					}
				}
				.StyledCommentsMotifDropdownContainer {
					&:hover + .SectionValidationWrapper {
						.motif-dropdown-portal {
							.motif-dropdown-trigger {
								.ValidationIcon {
									border-left: var(--px-1) solid var(--neutrals-00white);
								}
							}
						}
					}
					&.StyledCommentsMotifDropdownContainerOpen {
						& + .SectionValidationWrapper {
							.motif-dropdown-portal {
								.motif-dropdown-trigger {
									.ValidationIcon {
										border-left: var(--px-1) solid var(--neutrals-00white);
									}
								}
							}
						}
						.StyledSectionCommentCounter {
							border-radius: var(--px-20) 0 0 var(--px-20) !important;
						}
					}
				}
				.SectionValidationWrapper {
					.motif-dropdown-portal {
						&:hover,
						&:focus,
						&.StyledMotifDropdownPortalOpen {
							border-radius: 0 var(--px-20) var(--px-20) 0 !important;
							.motif-dropdown-trigger {
								.ValidationIcon {
									border-left: var(--px-1) solid var(--neutrals-900);
								}
							}
						}
						.motif-dropdown-trigger {
							.ValidationIcon {
								border-left: var(--px-1) solid var(--neutrals-300);
							}
						}
					}
				}
			}
		}
		&.CommentIconVisible {
			.StyledSectionCommentCounter {
				&:hover,
				&:focus {
					border-radius: var(--px-20) !important;
				}
			}
			.StyledCommentsMotifDropdownContainer {
				&.StyledCommentsMotifDropdownContainerOpen {
					.StyledSectionCommentCounter {
						border-radius: var(--px-20) !important;
					}
				}
			}
		}
		&.ValidationIconVisible {
			.SectionValidationWrapper {
				.motif-dropdown-portal {
					&:hover,
					&:focus,
					&.StyledMotifDropdownPortalOpen {
						border-radius: var(--px-20) !important;
					}
				}
			}
		}
		.StyledSectionCommentCounter {
			display: inline-flex;
			justify-content: center;
			align-items: center;
			padding: 0;
			min-height: var(--px-32);
			min-width: var(--px-60);
			width: auto;
			max-width: var(--px-60);
			font-weight: 600;
			cursor: pointer;
			&:hover,
			&:focus {
				background-color: var(--neutrals-900);
				color: var(--neutrals-00white);
				border-radius: var(--px-20) !important;
				cursor: pointer !important;
				svg {
					color: var(--neutrals-00white) !important;
				}
				.CommentsCounter {
					color: var(--neutrals-00white);
				}
			}
			& + .SectionValidationWrapper {
				.motif-dropdown-portal {
					&.StyledMotifDropdownPortal {
						&:hover,
						&:focus,
						&.StyledMotifDropdownPortalOpen {
							border-radius: 0 var(--px-20) var(--px-20) 0;
						}
					}
				}
			}
			svg {
				cursor: pointer;
				color: var(--neutrals-1000) !important;
				width: var(--px-16) !important;
				height: var(--px-16) !important;
				vertical-align: middle;
			}
		}
		.header-validationCount {
			display: inline-flex;
			justify-content: center;
			min-width: var(--px-60);
			width: auto;
			max-width: var(--px-60);
			font-weight: normal !important;
			.motif-dropdown-portal {
				&.StyledMotifDropdownPortal {
					min-width: var(--px-60);
					padding: 0;
					&:hover,
					&:focus,
					&.StyledMotifDropdownPortalOpen {
						background-color: var(--neutrals-900);
						color: var(--red-600);
						border-radius: var(--px-20);
						svg {
							color: var(--red-600) !important;
						}
						.validationcount {
							color: var(--red-600);
						}
					}
					.motif-dropdown-trigger {
						padding: 0;
						min-height: var(--px-32);
						display: flex;
						align-items: center;
						justify-content: center;
						.ValidationIcon {
							svg {
								height: var(--px-16);
								width: var(--px-16);
							}
						}
					}
				}
			}
			&.SectionValidationWrapper {
				display: inline-flex;
				justify-content: center;
				align-items: center;
				min-width: var(--px-60);
				.motif-dropdown {
					background-color: var(--neutrals-900);
					color: var(--neutrals-00white);
					border-radius: var(--px-15);
					width: auto;
					max-width: var(--px-120);
					padding: 0 var(--px-5);
					align-items: center;
					display: inline-flex;
					justify-content: center;
					.motif-dropdown-trigger {
						display: flex;
						align-items: center;
						svg {
							color: var(--neutrals-00white) !important;
							width: var(--px-16) !important;
							height: var(--px-16) !important;
						}
						.ValidationIcon {
							display: flex;
							align-items: center;
							justify-content: center;
							.ValidationImg {
								color: var(--neutrals-00white) !important;
							}
							.validationcount {
								color: var(--neutrals-00white) !important;
								padding: 0 var(--px-5);
								font-size: var(--px-12);
								font-weight: 600;
								max-width: var(--px-40);
								&.TaskArrow {
									max-width: var(--px-60);
								}
							}
						}
					}
					.motif-dropdown-menu {
						margin-top: var(--px-12);
						.motif-dropdown-item {
							&:first-child {
								background-color: var(--neutrals-100) !important;
								border-bottom: var(--px-1) solid var(--neutrals-300) !important;
								margin-bottom: var(--px-5);
							}
							.validationDropdownHeading {
								padding: 0 !important;
								width: 100%;
								.motif-icon {
									margin: 0 0 0 auto !important;
									width: var(--px-20) !important;
								}
							}
						}
					}
					.motif-dropdown-menu[x-placement^='bottom-right']:before {
						right: var(--px-24);
						@media only screen and (max-width: 1400px) {
							right: var(--px-22);
						}
					}
					.dropdownValidationCount {
						color: var(--neutrals-900);
					}
					.dropdownValidationName {
						&.cursorPointer {
							color: var(--blue-600);
						}
					}
				}
			}
		}
		.StyledCommentsMotifDropdownContainer {
			&:hover,
			&:focus {
				.motif-dropdown-portal {
					background-color: var(--neutrals-900) !important;
					color: var(--neutrals-00white) !important;
					border-radius: var(--px-20);
					#cmtIcon {
						svg {
							color: var(--neutrals-00white) !important;
						}
					}
					.CommentsCounter {
						color: var(--neutrals-00white) !important;
					}
				}
			}
		}
	}
	.form-header-accordion-trigger {
		.form-header-name {
			width: calc(100% - var(--px-160));
		}
	}
	.AccordionBody {
		.StyledSummarySection {
			.DraggableContainer {
				position: fixed;
				top: var(--px-60);
			}
			.SectionBodyWrapper {
				&.DefaultView {
					padding: 0;
					border: 0;
					box-shadow: none;
					margin: var(--px-10) 0;
					&:first-child {
						margin-top: 0;
						& + .DefaultView {
							margin-top: 0;
						}
					}
					& + .DefaultView {
						margin-top: 0;
					}
					& + .CardView {
						margin-top: 0;
					}
					.SectionBodyDescription {
						.to-right {
							// margin-right: var(--px-10);
						}
					}
				}
				&.CardView {
					padding: var(--px-20) var(--px-10) var(--px-20) var(--px-20);
					background-color: var(--neutrals-00white);
					border: var(--px-1) solid var(--neutrals-200);
					border-radius: var(--px-4);
					box-shadow: none;
					margin: var(--px-30) 0;
					&:first-child {
						margin-top: 0;
						& + .CardView {
							margin-top: 0;
						}
					}
					& + .CardView {
						margin-top: 0;
					}
					& + .DefaultView {
						margin-top: 0;
					}
				}
				&.CardViewTop {
					padding: var(--px-20) var(--px-10) var(--px-10) var(--px-20);
					background-color: var(--neutrals-00white);
					border: var(--px-1) solid var(--neutrals-200);
					border-width: var(--px-1) var(--px-1) 0 var(--px-1);
					border-radius: var(--px-4) var(--px-4) 0 0;
					box-shadow: none;
				}
				&.CardViewMiddle {
					padding: var(--px-10) var(--px-10) var(--px-10) var(--px-20);
					background-color: var(--neutrals-00white);
					border: var(--px-1) solid var(--neutrals-200);
					border-width: 0 var(--px-1);
					box-shadow: none;
				}
				&.CardViewMiddleTop {
					padding: 0;
					background-color: var(--neutrals-00white);
					border: var(--px-1) solid var(--neutrals-200);
					border-width: 0 var(--px-1) 0 var(--px-1);
					box-shadow: none;
					.CardViewMiddleTop {
						margin: 0 var(--px-20);
						border-top: var(--px-1) solid var(--neutrals-200);
						padding: var(--px-20) 0;
						width: calc(100% - var(--px-40));
					}
					.StyledToggleSwitchListContainer {
						margin: 0 var(--px-20);
						border-top: var(--px-1) solid var(--neutrals-200);
						padding: var(--px-20) 0;
						width: calc(100% - var(--px-40));
					}
				}
				&.CardViewBottom {
					padding: var(--px-10) var(--px-10) var(--px-20) var(--px-20);
					background-color: var(--neutrals-00white);
					border: var(--px-1) solid var(--neutrals-200);
					border-width: 0 var(--px-1) var(--px-1) var(--px-1);
					border-radius: 0 0 var(--px-4) var(--px-4);
					box-shadow: none;
					margin-bottom: var(--px-30);
					& + .DefaultView {
						margin-top: 0;
					}
				}
				&.CardViewBottomTop {
					padding: 0;
					background-color: var(--neutrals-00white);
					border: var(--px-1) solid var(--neutrals-200);
					border-width: 0 var(--px-1) var(--px-1) var(--px-1);
					border-radius: 0 0 var(--px-4) var(--px-4);
					box-shadow: none;
					margin-bottom: var(--px-30);
					& + .DefaultView {
						margin-top: 0;
					}
					.CardViewBottomTop {
						margin: 0 var(--px-20);
						border-top: var(--px-1) solid var(--neutrals-200);
						padding: var(--px-20) 0;
						width: calc(100% - var(--px-40));
					}
					.StyledToggleSwitchListContainer {
						margin: 0 var(--px-20);
						border-top: var(--px-1) solid var(--neutrals-200);
						padding: var(--px-20) 0;
						width: calc(100% - var(--px-40));
					}
				}
			}
			// Summary Body Accordion
			.AccordionBody {
				.SummarySectionBody {
					display: flex;
					flex-direction: column;
					width: 100%;
					padding-bottom: var(--px-20);
					.SectionBodyWrapper {
						margin-bottom: var(--px-20);
						&:last-child {
							margin-bottom: 0;
						}
					}
					.SectionBodyKnowledgeTitleWrapper {
						.SectionBodyDescription {
							.richText-Body,
							p,
							span {
								font-size: var(--px-20) !important;
								font-weight: 300 !important;
								font-style: normal !important;
							}
							em {
								font-style: normal !important;
							}
							.to-right {
								.commentsIconSpan {
									align-items: center !important;
									.commentsCounts {
										font-size: var(--px-14) !important;
									}
								}
								.motif-dropdown-portal {
									.ValidationIcon {
										.validationcount {
											font-size: 1.2em !important;
											font-weight: 600;
										}
									}
								}
							}
						}
					}
					.FormBodyNameDetails {
						font-size: var(--px-18);
						line-height: var(--px-22);
						margin-bottom: var(--px-30);
						&:last-child {
							margin-bottom: 0;
						}
					}
					.StyledAccountSummaryComponentBody {
						width: calc(100% - var(--px-20));
					}
					.SectionBodyDescription_Slider,
					.SectionBodyDescription_SliderLegend {
						font-weight: 600;
						padding: var(--px-20) var(--px-10) var(--px-20) var(--px-20);
						font-size: var(--px-13);
						.SectionBodyDescriptionRow {
							.AresViewRightIcons {
								// margin-right: var(--px-10);
							}
						}
					}
					.ViewSliderBodyData {
						padding: 0 var(--px-10) 0 var(--px-20);
					}
				}
				.SectionBodyKnowledgeWrapper {
					&.DefaultView {
						.SectionBodyDescription {
							.section-showless-header {
								width: calc(100% - var(--px-80));
								line-height: normal;
							}
						}
					}
				}
				.SectionBodyDescription {
					.section-showless-header-description {
						width: calc(100% - var(--px-200));
						line-height: normal;
						&.RadioButtonHorizontalHeader {
							width: calc(100% - var(--px-80));
							padding: 0 var(--px-10) 0 var(--px-20);
							p {
								font-size: var(--px-13);
								font-weight: 600;
							}
							ul {
								margin-left: var(--px-20) !important;
								li {
									list-style-type: disc;
								}
							}
							ol {
								margin-left: var(--px-20) !important;
								li {
									list-style-type: auto;
								}
							}
							& + section {
								align-items: flex-start;
							}
						}
						.richText-Body {
							width: 100%;
							display: flex;
							align-items: flex-start;
							& > span {
								align-items: flex-start;
							}
						}
					}
					.sectionBodyRightIcons {
						max-width: var(--px-200) !important;
						width: auto !important;
						margin-left: auto;
						.FirstRowIcons {
							&.AresViewFirstRowIcons {
								background-color: var(--neutrals-00white);
								border: var(--px-1) solid var(--neutrals-300);
								border-radius: var(--px-20);
								max-width: 100%;
								width: auto !important;
								height: var(--px-32);
								display: flex;
								align-items: center;
								justify-items: center;
								&:hover,
								&:focus {
									border-radius: var(--px-20) !important;
								}
								&.CommentIconNotVisible {
									&.ValidationIconNotVisible {
										border: 0;
										display: none;
									}
								}
								&.CommentIconVisible {
									.comments-icon {
										&:hover,
										&:focus,
										&.active {
											border-radius: var(--px-20) !important;
										}
									}
								}
								&.ValidationIconVisible {
									.sectionBody-validation {
										.motif-dropdown-portal {
											&:hover,
											&:focus,
											&.StyledMotifDropdownPortalOpen {
												border-radius: var(--px-20) !important;
											}
										}
									}
								}
								&.CommentIconVisible {
									&.ValidationIconVisible {
										.comments-icon {
											&:hover,
											&:focus,
											&.active {
												border-radius: var(--px-20) 0 0 var(--px-20) !important;
											}
											&.active + .sectionBody-validation {
												.motif-dropdown-portal {
													&:hover,
													&:focus,
													&.StyledMotifDropdownPortalOpen {
														.motif-dropdown-trigger {
															.ValidationIcon {
																border-left: var(--px-1) solid var(--neutrals-00white);
															}
														}
													}
													.motif-dropdown-trigger {
														.ValidationIcon {
															border-left: var(--px-1) solid var(--neutrals-00white);
														}
													}
												}
											}
											&:hover + .sectionBody-validation {
												.motif-dropdown-portal {
													.motif-dropdown-trigger {
														.ValidationIcon {
															border-left: var(--px-1) solid var(--neutrals-00white);
														}
													}
												}
											}
										}
										.sectionBody-validation {
											.motif-dropdown-portal {
												&:hover,
												&:focus,
												&.StyledMotifDropdownPortalOpen {
													border-radius: 0 var(--px-20) var(--px-20) 0 !important;
													.motif-dropdown-trigger {
														.ValidationIcon {
															border-left: var(--px-1) solid var(--neutrals-900);
														}
													}
												}
												.motif-dropdown-trigger {
													.ValidationIcon {
														border-left: var(--px-1) solid var(--neutrals-300);
													}
												}
											}
										}
									}
								}
								.comments-icon {
									margin: 0 !important;
									cursor: default;
									min-height: var(--px-32);
									min-width: var(--px-60);
									width: auto;
									max-width: var(--px-60);
									align-items: center;
									justify-content: center;
									&:hover,
									&:focus {
										background-color: var(--neutrals-900);
										color: var(--neutrals-00white);
										cursor: pointer;
										border-radius: var(--px-20);
										svg {
											color: var(--neutrals-00white) !important;
										}
										.commentsCounts {
											color: var(--neutrals-00white) !important;
										}
									}
									.commentsIconSpan {
										padding: var(--px-5);
										min-height: auto;
										height: var(--px-24);
										min-width: var(--px-60);
										justify-content: center;
										align-items: center !important;
										cursor: pointer;
										.motif-icon {
											line-height: var(--px-16) !important;
											svg {
												height: var(--px-16) !important;
												width: var(--px-16) !important;
												color: var(--neutrals-1000);
											}
										}
										.commentsCounts {
											font-size: var(--px-12) !important;
											font-weight: 400 !important;
											line-height: var(--px-18) !important;
											color: var(--neutrals-700);
										}
									}
									&.active {
										background-color: var(--neutrals-900);
										color: var(--neutrals-00white);
										svg {
											color: var(--neutrals-00white) !important;
										}
										.commentsCounts {
											color: var(--neutrals-00white);
										}
									}
								}
								.sectionBody-validation {
									margin: 0 !important;
									cursor: pointer;
									height: auto;
									max-height: var(--px-32);
									min-width: var(--px-60);
									width: auto;
									max-width: var(--px-60);
									.motif-dropdown-portal {
										padding: 0 var(--px-10);
										margin: 0;
										min-height: var(--px-32);
										min-width: var(--px-60);
										width: auto;
										max-width: var(--px-60);
										display: flex;
										align-items: center;
										justify-content: center;
										&:hover,
										&:focus,
										&.StyledMotifDropdownPortalOpen {
											background-color: var(--neutrals-900);
											color: var(--neutrals-00white);
											border-radius: var(--px-20);
											.motif-dropdown-trigger {
												.validationcount {
													color: var(--red-600) !important;
												}
												.ValidationIcon {
													svg {
														color: var(--red-600) !important;
													}
												}
											}
										}
										.motif-dropdown-trigger {
											justify-content: center;
											align-items: center;
											display: flex;
											.validationcount {
												font-size: var(--px-12) !important;
												font-weight: 600 !important;
												line-height: var(--px-16) !important;
												color: var(--red-600);
											}
											.ValidationIcon {
												svg {
													height: var(--px-16) !important;
													width: var(--px-16) !important;
													color: var(--red-600);
												}
											}
										}
									}
								}
							}
						}
						.SecondRowIcons {
							display: none !important;
						}
					}
				}
				.SectionBodyData {
					margin-left: var(--px-20) !important;
					margin-top: var(--px-10) !important;
					&.sectionBodyNoResponse {
						margin-top: 0 !important;
					}
					&.SectionBodyTEMData,
					&.SectionBodyPMData {
						margin-left: 0 !important;
					}
					&.RelateExistingDocumentsBodyData {
						margin-left: 0 !important;
						.relateExistingDocumentsLink {
							// width: calc(100% - var(--px-20));
						}
					}
					.relatedDocumentsWrapperContainer {
						margin-left: 0;
						width: calc(100% - var(--px-20));
						.relatedDocuments__table {
							margin: var(--px-20);
						}
					}
					.NoResponseLabel {
						display: inline-flex;
						margin: 0;
						width: calc(100% - var(--px-150));
						color: var(--red-600);
					}
					/* &.SectionBodyDescriptionWrapper {
						border: 0 !important;
					} */
					&.MultiSelectTileBodyData,
					&.SingleSelectTileBodyData {
						display: flex;
						flex-direction: column;
					}
					&.SectionBodyDataSlider {
						margin-top: 0 !important;
						margin-left: var(--px-20) !important;
					}
					&.SectionBodyDataSliderLegend {
						margin-top: 0 !important;
						margin-left: var(--px-20) !important;
					}
					.section-freeText {
						display: inline-flex;
						color: var(--neutrals-900);
						margin: 0;
						padding: 0;
						width: calc(100% - var(--px-40));
						word-break: break-word;
						.richText-Body {
							font-size: inherit;
							width: 92%;
							line-height: normal;
							ins {
								font-size: inherit;
								line-height: normal;
							}
							del {
								font-size: inherit;
							}
							div {
								font-size: inherit;
								del {
									font-size: inherit;
								}
								strong {
									font-size: inherit;
								}
								em {
									font-size: inherit;
								}
							}
							p {
								font-size: inherit;
								line-height: normal;
								margin-bottom: 0 !important;
								min-height: var(--px-16);
								span {
									font-size: inherit;
									line-height: normal;
								}
								del {
									font-size: inherit;
								}
								strong {
									font-size: inherit;
								}
								em {
									font-size: inherit;
								}
							}
							span {
								width: 100%;
								color: var(--neutrals-900);
								font-size: inherit;
								line-height: normal;
								div {
									font-size: inherit;
									line-height: var(--px-16);
								}
								blockquote {
									font-size: inherit;
									line-height: var(--px-16);
								}
								ul {
									display: block;
									list-style-type: disc;
									margin-block-end: var(--px-10);
									margin-block-start: 0;
									margin-bottom: var(--px-10);
									margin-inline-end: 0;
									margin-inline-start: 0;
									margin-top: var(--px-10);
									padding-inline-start: var(--px-24);
									padding-left: var(--px-24);
									li {
										font-size: inherit;
										margin-bottom: calc(var(--px-1) * -1);
										font-weight: 500;
										list-style-type: disc;
									}
								}
								ol {
									margin-block-end: var(--px-10);
									margin-block-start: 0;
									margin-bottom: var(--px-10);
									margin-inline-end: 0;
									margin-inline-start: 0;
									margin-top: 0;
									padding-inline-start: var(--px-24);
									padding-left: var(--px-24);
									li {
										font-size: inherit;
										margin-bottom:calc(var(--px-1) * -1);
										font-weight: 500;
									}
								}
								h6 {
									font-weight: bold;
									line-height: var(--px-32);
									margin-block-end: 0;
									margin-block-start: 0;
									margin-inline-end: 0;
									margin-inline-start: 0;
									margin: 0;
									padding: 0;
									em {
										line-height: var(--px-40);
									}
								}
								h5 {
									font-weight: bold;
									line-height: var(--px-32);
									margin-block-end: 0;
									margin-block-start: 0;
									margin-inline-end: 0;
									margin-inline-start: 0;
									margin: 0;
									padding: 0;
									em {
										line-height: var(--px-40);
									}
								}
								h4 {
									font-weight: bold;
									font-size: inherit;
									line-height: var(--px-32);
									margin-block-end: 0;
									margin-block-start: 0;
									margin-inline-end: 0;
									margin-inline-start: 0;
									margin: 0;
									padding: 0;
									em {
										font-size: inherit;
										line-height: var(--px-40);
									}
								}
								h3 {
									font-weight: bold;
									line-height: var(--px-32);
									margin-block-end: 0;
									margin-block-start: 0;
									margin-inline-end: 0;
									margin-inline-start: 0;
									margin: 0;
									padding: 0;
									em {
										line-height: var(--px-40);
									}
								}
								h2 {
									font-weight: bold;
									line-height: var(--px-40);
									margin-block-end: 0;
									margin-block-start: 0;
									margin-inline-end: 0;
									margin-inline-start: 0;
									margin: 0;
									padding: 0;
									em {
										line-height: var(--px-50);
									}
								}
								h1 {
									font-weight: bold;
									line-height: var(--px-48);
									margin-block-end: 0;
									margin-block-start: 0;
									margin-inline-end: 0;
									margin-inline-start: 0;
									margin: 0;
									padding: 0;
									em {
										line-height: var(--px-60);
									}
								}
							}
							tr,
							th,
							td {
								font-weight: 500;
								font-size: inherit;
								padding: var(--px-2) var(--px-3);
								border: var(--px-1) solid var(--neutrals-300);
								border-collapse: collapse;
								background-color: transparent;
								span {
									font-size: inherit;
								}
							}
							table {
								max-width: calc(100% - var(--px-100));
								border-collapse: collapse;
								background-color: transparent;
							}
						}
					}
					.highlight-teal {
						font-size: inherit;
						line-height: inherit !important;
						border: var(--px-1) solid #93f0e6;
						background: #93f0e6 !important;
						padding: var(--px-2);
					}
					.diffins {
						background-color: #e6ffe6;
						font-family: inherit;
						text-decoration: none;
					}
					.diffdel {
						color: #999;
						background-color: #ffe6e6;
						font-family: inherit;
					}
					.mod {
						background-color: #ffe1ac;
						font-family: inherit;
					}
					table {
						width: 100%;
						margin: var(--px-5) 0;
						overflow: hidden;
						td {
							border: var(--px-1) solid var(--neutrals-300);
							padding: var(--px-5);
							p {
								margin: 0;
							}
						}
					}
				}
				.SectionBodyKnowledgeTitleWrapper {
					.SectionBodyDescription {
						.richText-Body,
						p,
						span {
							font-style: normal !important;
						}
						em {
							font-style: normal !important;
						}
					}
				}
				.ViewOptions {
					display: flex;
					color: var(--neutrals-700);
					&.active {
						color: var(--neutrals-900);
						font-weight: bold;
						&.ViewDate,
						&.ViewDecimal,
						&.ViewNumeric {
							font-weight: normal;
						}
						.motif-icon {
							visibility: visible;
							margin-right: var(--px-10);
							width: var(--px-20);
							svg {
								fill: var(--green-500);
								width: var(--px-20) !important;
							}
						}
					}
					&:last-child {
						margin-bottom: 0;
					}
					.motif-icon {
						visibility: hidden;
						margin-right: var(--px-10);
						svg {
							width: var(--px-20) !important;
						}
					}
				}
			}
		}
	}
	.StyledToggleSwitchList {
		padding-bottom: 0;
		border: 0;
		.ViewToggleSwitchList {
			width: 95%;
			margin-left: var(--px-20);
		}
	}
`;
