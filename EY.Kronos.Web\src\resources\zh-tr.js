/* eslint-disable prettier/prettier,max-len */

import {
	Entity,
	GALinkStatus,
	confidentialityTypes,
	currencyType,
	gaRegion,
	gaRoleTypes,
	gaScopeType,
	notesFilter,
	pointOfContactTypes,
	validationTypes,
	KnowledgeSectionIds,
	sendInstructionsSwitcherIds,
	accountsFilter,
	scotsFilter,
	rejectionType
} from'../util/uiconstants';

/**
 * Created by calhosh on 4/14/2017.
 * ZH TR resource file
 */
export const labels = {
	addEvidenceBtn: 'Add evidence',
	multipleDocuments: 'Multiple Documents',
	invalidEngagementId: '案件ID無效。請重新整理頁面並重試。若問題仍然存在，請聯絡Help Desk。',
	newComponent: 'new component',
	workingOffline: '離線工作',
	syncInProgress: '同步進行中',
	prepareOffline: '編製離線資料',
	connectionAvailable: '可連線',
	training: '訓練',
	clickForOptions: '點選更多選項',
	navReviewNotes: '複核註記',
	navHeaderManageTeam: '管理團隊',
	navManageGroup: '管理集團',
	manageObjects: '管理物件',
	navCRASummary: 'CRA彙總',
	navAuditPlan: '查核計畫',
	navWorkPlan: '工作計畫',
	navSEM: 'Substantive evaluation matrix',
	navFindings: '發現',
	navContentUpdates: 'Content update',
	navCanvasFormUpdates: 'Canvas form更新',
	navCopyHub: '複製中心',
	navCopyHubNew: 'Copy hub NEW',
	navArchiveChecklist: '歸檔檢查表',
	navExportHub: '匯出中心',
	navReporting: '報表',
	navHelp: 'General help',
	validationNavHelp: 'Validation help',
	leaveUsFeedback: '給我們回饋意見',
	navDashboard: '儀錶板',
	tasksPage: '任務',
	documentsPage: '文件',
	collaborationPage: '合作中心',
	automationPage: '自動化流程',
	documentHelperConnectionIssue: '檢測到 EY Canvas Document Helper 存在問題。點擊<a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">此處</a>取得如何操作的說明以解決這個問題。',
	noContentAvailable: '無內容',
	noSectionsAvailable: '無區塊',
	noInformationAvailable: 'No information available',
	collapse: '收合',
	expand: '展開',
	duplicate: '重複',
	duplicateSection: '複製區塊',
	duplicateSectionHeader: '請確認是否要重複所選區塊？',
	deleteSection: '刪除區塊',
	deleteSectionHeader: 'Are you sure you want to delete the selected section?',
	deleteHeader: '刪除表頭',
	deleteHeaderTitle: '請確認是否要刪除所選表頭？',
	confirmLabel: '確認',
	custom: '自訂',
	selectHeader: '選擇表頭',
	selectSection: '選擇區塊',
	noResultsFound: '未找到結果',
	scot: 'SCOT',
	scotTypes: 'SCOT類型',
	frequency: '頻率',
	SelectFrequency: '選擇頻率',
	SelectControlType: '選擇控制類型',
	itBadge: 'IT',
	soBadge: 'SO',
	noRecordsAvailable: '無紀錄',
	noIncompleteResponseSummaryView: 'No incomplete responses',
	noUnresolvedCommentsSummaryView: 'No unresolved comments',
	edit: '編輯',
	editForm: '編輯表單',
	editControl: '編輯控制',
	delete: '刪除',
	remove: '移除',
	noBodies: '無內文',
	relateDocuments: '連結文件',
	relatedDocuments: '相關文件',
	deleteBody: '刪除內文',
	bodyDescription: '請確認是否要刪除所選內文？',
	description: '說明',
	maxLengthForEditResponse: '內文文字超出最大允許長度',
	maxLengthForEditResponseWithCount: '回覆包含 {#} 個字符，超出了 {##} 個字符的最大值。請透過減少文字或格式來調整回覆，另請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	saveResponse: 'Discarding the changes will not save any edits made to the response text. Closed or deleted comments will remain highlighted. Please confirm if you want to discard all changes.',
	discardChangesModalText: '取消變更',
	seeBodyDescriptionText: '檢視說明',
	hideBodyDescriptionText: '隱藏說明',
	showBodyDescriptionText: '顯示說明',
	okLabel: 'Ok',
	addButtonLabel: '加入',
	addEvidence: '加入證據',
	addTemporaryFiles: '新增暫存檔案',
	notemporaryDocs: '沒有可用的暫存檔案',
	relateFiles: '相關文件',
	uploadFiles: '上傳文件',
	cancelButtonLabel: '取消',
	clickEditResponseToContinue: '點選編輯以繼續',
	editResponse: '編輯回應',
	save: '儲存',
	numericValuePlaceholder: '輸入金額',
	saveLabel: '儲存',
	cancelLabel: '取消',
	closeLabel: '關閉',
	editText: '編輯',
	select: '選擇',
	selectScot: '選擇 SCOTs',
	clearHoverText: '清除',
	optional: '(選擇性)',
	nodocumentsAdded: '無文件',
	errorBanner: '{0} 錯誤',
	NetworkErrorMessage: '應用程式網路發生錯誤。請重新整理頁面並稍後重試。若問題仍然存在，請聯絡Help Desk。',
	of: 'of',
	characters: '字元',
	show: '顯示: ',
	views: 'View: ',
	primaryRelated: 'Primary related Canvas forms',
	secondaryRelated: 'Secondary related Canvas forms',
	singleLineValuePlaceholder: '輸入文字',
	paceInputPlaceholder: 'PACE ID',
	multiLineValuePlaceholder: '輸入文字',
	riskFactorDescribe: 'Describe',
	riskFactorLabel: '相關事件和情況/不實表達風險',
	riskFactorEmptyWarning: 'Event and condition / risk of misstatement missing description',
	riskFactorNoDescription: '建立新的或選擇現有的相關事件和條件/不實表達風險',
	fraudRiskTagMessage: '該相關事件和條件/不實表達風險常會引發舞弊風險，或者，若適用，應指定為不存在重大不實表達風險',
	significantRiskTagMessage: '該相關事件和條件/不實表達風險常會引發重大風險、舞弊風險，或者，若適用，應指定為不存在重大不實表達風險',
	on: 'on',
	sliderDeSelectMessage: '拖移圓圈來指派一個值',
	yearPlaceholder: 'YYYY',
	dayPlaceholder: 'DD',
	monthPlaceholder: 'MM',
	amLabel: '上午',
	pmLabel: '下午',
	relatedEntities: '相關個體',
	eyServiceGateway: 'EY Service Gateway',
	eyAutomation: 'EY Automation',
	eyserviceGatewayAutomation: 'EY Service Gateway & Automation',
	creating: 'Creating...',
	cannotCreateUdp: 'Cannot create UDP. Time Phase cannot be empty',

	// 440GL
	rrdReminderTitle: '複核及批准總結表提示',
	rrdReminderMessage: '報告發布日期為｛rrdDescription｝。請勿忘簽核複核及批准總結表｛rrdPendingDays｝。',
	rrdReminderPendingDays: '在{0}天內',

	//Create or Associate risks
	createAssociateRisksLabel: '建立新的或關聯的風險',
	relatedRiskIsMissingWarning: '缺少相關風險',
	associateRiskDescription: '選擇一個或多個風險，或建立一個新風險，以關聯到問題回覆。',
	createNewRiskLabel: '建立新的風險',
	noRiskIdentifiedLabel: '未發現風險',

	// GuidanceModal
	eyAtlasLink: 'EY Atlas',
	guidanceHeaderMessage: 'This module contains',
	guidanceModalHeader: 'Guidance',
	guidanceModalLabelText: 'Entry',
	guidanceFooter: 'for more information.',
	guidanceSeveralEntriesText: 'several entries: ',
	guidanceVisitText: 'Visit',
	guidanceClickText: 'Click',
	guidanceHereText: 'here',
	guidanceFooterText: 'for more information.',
	analyticsInconsistencies: '在複核分析時，請考量是否存在與我們的預期不一致的變化或活動。 這些情況可能意味著有新的風險、單獨的交易類別、SCOT 的變化或管理階層逾越控制的風險存在。',
	analyticsInconsistenciesOSJE: 'OSJE depicts the double-entry accounting for the selected account being analyzed. We look for new or unusual account pairings.',
	analyticsInconsistenciesActivityBySource: '來源活動呈現了所選科目的每月活動和相關來源。我們關注於不尋常的活動、來源變更或對一個來源的活動量的變化。',
	analyticsInconsistenciesPreparerAnalysis: '編製者分析總結了所選科目在不同期間的總活動量。我們關注於編製者的變化或對其職責範圍以外的科目的活動變化',
	analyticsInconsistenciesAccountMetrics: '科目指標彙整了關於該科目的關鍵資訊，這有助於科目的指定。',
	analyticsLoadingIsInProgress: 'The requested analytic is still being loaded. The tab will be accessible once that completes.',

	aboutDescriptioin: '編輯GAM layers、準則、或語言。編輯將觸發內容更新。',
	aboutContentDescription: 'Edit the content layers, standards, language, or content driving entity. Edits will trigger a content update.',
	about: '關於',
	formType: '表單類型',
	gamLayer: 'GAM layers',
	contentLayer: '內容階層',
	standard: '準則',
	language: '語言',
	createdBy: '建立者',
	createdOn: '建立於',
	contentLastUpdatedBy: '最近一次更新內容者',
	contentLastUpdatedOn: '內容最後更新於',
	notModified: '未修改',

	rolesInsufficientTooltip: '角色無法編輯內容。請向案件管理者取得足夠權限。',
	knowledgeFormToolTip: "Knowledge delivered documents cannot be updated. Update the Engagement Profile to change this form's profile.",
	selectTeamMember: 'Name or email',

	// SeeMore component
	showMore: '顯示更多',
	showLess: '顯示較少',
	showMoreEllipsis: '顯示更多...',
	showLessEllipsis: '顯示較少...',

	relatedITapplicationSOs: '相關IT應用程式/SO',
	aggregateITevaluations: 'Aggregate IT Evaluations',
	lowerRisk: '較低風險',
	controlLowerRisk: '控制為較低風險',
	relatedITApplication: 'Related IT applications',
	relatedITSO: 'Related SOs',
	noITApplicationUsed: 'No IT application used',

	notSel: '未選擇',
	internal: '內部',
	external: '外部',
	notSelected: 'Not selected',
	noOptionSelected: 'Not selected',
	tod: 'TOD',
	sap: 'SAP',
	int: 'INT',
	ext: 'EXT',
	toc: 'TOC',

	placeholderForSearch: '搜尋',
	source: '來源',
	nature: '性質',
	testOfDetail: '細項測試',
	testOfControl: '控制測試',
	substantiveAnalyticalProcedure: '證實分析性程序',
	expandScot: '1. 展開SCOT',
	selectWCGWToDisplayTheResponsiveTask: '2. 選擇重大不實表達以顯示回應任務',
	tasksWithNoRelatedWCGW: '任務無相關重大不實表達風險',
	noTasksAvailable: '無任務',
	noWCGWAvailableForTask: '無ROMM',
	noSubstantiveTasksAvailable: '無相關證實任務',
	selectAssertionToRelateWCGW: '選擇聲明連結風險至WCGW',
	significantAccounts: '重大科目',
	riskName: '風險：',
	accountName: '科目：',
	control: '控制',
	controls: 'Controls',
	noScotsFound: '無相關Scots',
	relatedwcgw: '相關WCGW',
	relatedRisks: '相關風險：',
	boltIconTitle: '相關風險',
	relatedITApp: '相關 IT 應用程式/SO',
	instructions: '指引：',
	expandRisk: '1. 展開風險',
	selectAssertion: '2. 選擇聲明',
	identifyRelatedWCGW: '3. 辨認連結至風險之WCGW',
	clickAccount: '1. 點選科目',
	selectWCGW: '2. 選擇WCGW',
	identifyRelatedTask: '3. 辨認回應WCGW之任務',
	information: '資訊',
	requiredAssertions: '必要聲明',
	wcgwWithoutTasks: 'WCGW無任務',
	rommAssociatedWNotRelyAssertion: 'ROMM連結控制風險不仰賴之聲明或固有風險較高之聲明(若科目有估計)',
	hasRiskAssociated: '相關風險',
	clearSelections: '取消全選',
	romm: 'ROMM',
	riskOfMaterialMisstatementsWithoutRelatedTask: '無相關任務之重大不實表達風險',
	selectOneOrMoreTasksToSeeTheRelatedROMM: '選擇任務以檢視相關ROMM',
	invalidRelatedEntity: '找不到相關科目。請重新整理頁面並重試。若問題仍然存在，請聯絡Help Desk。',
	noResultsAvailable: '找不到結果',
	riskOfMaterialMisstatement: '重大不實表達風險',
	AccountConclusion: '科目結論',
	CanvasForm: 'Canvas Form',
	IndependenceForm: '獨立性表單',
	Profile: '基本資料',
	AccountDetails: '詳細內容',
	Conclusions: '結論',
	accountDetailsTab: '詳細內容',
	conclusionsTab: '結論',
	formsNoContentText: '無內容。',
	formsDocumentNoRelatedObjects: '文件無相關物件',
	formsNoRelatedObjects: '無相關物件',
	formsBodyHeaderControl: '控制',
	formsBodyDesignEffectiveness: '設計有效性',
	formsScotsAndWcgws: 'SCOTs & WCGWs',
	wcgWAndRelatedControls: 'WCGWs 及相關控制',
	controlAndRelatedItSO: 'Controls and related IT apps/ SOs',
	type: '類型',
	designEffectiveness: '設計有效性',
	approch: 'Approach',
	controlOpertaingEffectiveness: '執行有效性',
	iTAppSO: 'IT App/SO',
	iTProcess: 'IT process',
	iTControl: 'IT Control',
	iTRisk: 'IT risks',
	aggregateITEvaluation: '綜合IT評估',
	relatedCanvasForm: '相關Canvas表單',
	relatedSections: 'Related sections',
	validations: '驗證',
	profileV2Validation: '未提交變更',
	profileV2ValidationModalDescription: 'Changes have been made that would result in a content update, but have not been submitted. If the changes are intentional close this modal and submit the new profile answers. If the changes are not intentional, review the Changes view and manually revert the answers back to previous selections.',
	profileV2ValidationCount: '1',
	itProcessWithoutRelatedTechnology: 'IT process without related technology',
	reviewNote: '複核註記',
	editAssociations: '編輯連結',
	editAssociationsLower: 'edit associations',
	riskWCGW: '風險：WCGW關聯性',
	wcgwTask: 'WCGW：任務關聯性',
	noAssertionFound: 'No assertions have been associated. Click {here} to associate assertions',
	limitedRiskAccountIdentifier: 'Limited risk account',
	insignificantAccountIdentifier: 'Insignificant account',
	noWCGWFound: '無WCGW連結至此風險。請點選「編輯連結」以連結WCGW。',
	noRelatedWCGWs: '無相關WCGW',
	noWCGWAvailable: '所選聲明無ROMM',
	expandCollapse: '點選這裡以展開/收合',
	requiredAssertionsInfo: '僅顯示控制風險評估不仰賴聲明以及較高風險估計相關聲明',
	wCGWwithoutTasksInfo: '僅顯示WCGW相關控制風險評估不仰賴聲明以及較高風險估計相關聲明，未連結任何回應證實任務',
	noBuildStepsAvailable: '無建立步驟可展示',
	risk: 'Risks',
	wcgw: 'WCGW',
	riskWcgw: '風險：WCGW',
	wcgwTasks: 'WCGW：任務',
	riskWcgwLabel: '連結風險至WCGW',
	wcgwTasksLabel: '連結WCGW至任務',
	noRiskTypes: '找不到風險類型',
	saveRisk: '儲存',
	noRisksFound: '找不到風險',
	haveBeenIdentified: '已辨認',
	noAccountsFound: '找不到紀錄',
	noResponseAvailable: '無回應',
	noDocumentsAvailable: '無可用文件',
	noValue: 'No value',
	showValidation: '驗證',
	noAccountsIdentified: '未辨認出科目',
	noAssertionsIdentified: '未辨認出聲明',
	noWcgwsIdentified: '未辨認出WCGW',
	pastingImagesNotAllowed: '不允許貼上圖片。若圖片為必要，請上傳作為證據並索引。',
	incompleteResponse: '未完成回應',
	unresolvedComments: '未解決註解',
	inconsistentForms: 'Inconsistent forms',
	limitedRiskAccount: '有限風險科目',
	inherentRiskAssessment: '固有風險評估',
	task: '任務',
	selected: '已選擇',
	displaytoc: '顯示TOC',
	workingoffline: '離線工作',
	syncinprogress: '同步進行中',
	prepareoffline: '編製離線資料',
	connectionavilable: '可連線',
	softwareUpdate: '軟體更新',
	updateLater: '稍後更新',
	updateNow: '現在更新',
	updateMsg: 'EY Canvas有可用的軟體更新。選擇「立即更新」以下載及安裝該等更新。本頁面將被重新整理。',
	searchPlaceholder: '搜尋',
	filter: 'Filter',
	leftNavSearchPlaceholder: 'Search headers and sections',
	back: '返回',
	updateAvailable: '有更新',
	contentUpdateAvailableTooltip: '有可用的內容更新。欲開始更新，請點選此處導引至Canvas表單更新畫面。',
	moreMenu: '更多選單',
	signoffPreparer: '以編製者身分簽名',
	signoffReviewer: '以複核者身分簽名',
	pagingShowtext: '顯示',
	searchDocuments: '搜尋文件',
	noRelatedDocuments: '無相關文件',
	noRelatedObjects: 'No related objects',
	documentName: 'Document name',
	formDetails: '表單細節',
	questionsAndResponses: '問題與回應',
	details: 'Details',
	trackChanges: '追蹤變化',
	goToTrackChanges: 'Go to Track Changes',
	attributes: '屬性',
	relatedActions: 'Related actions',
	createCustom: '建立自訂',
	createCustomButtonLabel: 'Create custom header, section, or body',
	overwriteForm: '覆寫表單',
	decimalNaN: 'NaN - 不是數字',
	noRelatedObjectsApplicable: '物件不須連結到此Canvas',
	objects: '物件',
	objectName: '物件名稱',
	addCustomDescription: '選擇內容類型加入Canvas表單、輸入細節並點選「儲存」',
	headerTitle: '表頭標題',
	sectionTitle: '區塊標題(必須)',
	aresSectionTitle: 'Section title',
	customLabel: '自訂標籤(選擇性)',
	customBodyDescription: '內文說明',
	header: '表頭',
	section: '區塊',
	body: '內文',
	requiredWCGW: '必須',
	headerTitleRequired: '表頭標題為必要',
	bodyDescriptionRequired: '內文說明為必須',
	bodySectionRequired: '區塊為必須',
	bodyHeaderRequired: '表頭為必須',
	sectionTitleRequired: '區塊標題為必須。',
	headerRequiredMessage: '表頭為必須',
	enterDecimalAmount: '輸入小數位數',
	enterPercentage: '輸入百分比',
	completeRiskFactorAssessment: '完成所辨認的事件與情況之評估',
	noScotsEstimatesIdentified: '未辨識重大交易流程或估計',
	// Track changes
	trackChangesResponseLabel: '追蹤修訂版本回覆',
	trackChangesVersionLabel: '追蹤修訂版本',
	noResponseIdentified: '未發現任何回應',

	// Compare responses
	compareResponsesLabel: '比較回覆',
	compareResponsesTitle: '比較個體回覆',
	compareResponseNoDataPlaceholder: '沒有可用資料，因為該案件只有一份相同類型的文件。',
	labelFor: '為了',
	questions: '問題',
	answers: '答案',
	countOfResponses: '回覆數',
	openNotes: 'Open notes',
	clearedNotes: 'Cleared notes',
	click: 'Click',
	clickToViewAnswer: '檢視答案',
	clickToViewQuestionAnswer: '檢視問題及答案',
	selectDocuments: '選擇文件',
	selectedDocumentsCount: '{0} documents selected',
	selectedDocumentCount: '{0} document selected',
	associatedDocuments: 'Related documents',
	noAnswerProvided: '未提供答案',

	// Workspace Engagement
	thisEngagement: '此案件',
	documentLocation: '文件位置',
	otherEngagementsInWorkspace: '工作區中的其他案件',
	added: '新增',
	documentIneligibleForSharingMessage: '機密文件不符合共享條件。',
	fitDocumentCannotbeSelected: 'FIT 文件不能在計畫之間共享。',

	//Helix Configuration
	helixConfigurationTitle: '整合 EY Helix 資料',
	helixConfigurationPageDescription: '驗證連結的 EY Helix 項目並將資料匯入 EY Canvas。 若您在匯入資料後變更了以下任何 EY Helix 設定或變更了 EY Helix 資料，則必須重新匯入資料才能進行更新。',
	linkedEYHelixProjects: 'Linked EY Helix projects: ',
	client: 'Client: ',
	engagement: 'Engagement: ',
	analysisDate: 'Analysis date: ',
	eyHelixProjects: 'EY Helix projects',
	noPrimaryEYHelixproject: 'No primary EY Helix project has been identified.',
	here: 'here',
	identifyEyHelixProjects: 'to identify an EY Helix project and start the work flow.',
	eyHelix: 'EY Helix',
	primary: 'Primary',
	helixSettingsDescription: '點選編輯以選擇匯入 EY Helix Analyzers 時將套用的設定。',
	editButton: 'Edit',
	helixSettingsModalTitle: 'EY Helix Settings',
	currencyType: 'Currency type',
	currencyTypeError: 'Currency type could not be retrieved from EY Helix. Confirm data is set up correctly in EY Helix and try again.',
	shortNumberFormat: 'Short number format',
	shortNumberFormatFooter: 'Rounding to be applied to numerical values displayed in EY Helix tables.',
	eyHelixAnalyzerFilterMetadataError: 'Could not connect to EY Helix. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	functional: 'Functional',
	reporting: 'Reporting',
	currencyCode: 'Currency code',
	businessUnit: 'Business unit',
	roundingNumberFormat: 'Rounding number format',
	eyHelixProjectChangedLine1: 'Linked EY Helix project has been changed since the last time EY Helix settings were saved.',
	eyHelixProjectChangedLine2: 'Click Edit to update settings before data can be imported or re-imported from EY Helix.',
	helixSettingsTimeline: 'EY Helix settings',
	helixMapEntitiesToBU: '將個體對應到營運單位',
	helixNumberOfMapEntities: '對應的營運單位數量',
	importEYHelixDataTimeline: 'Import EY Helix data',
	mapAccountsHelixTimeline: 'Map accounts',
	setEYHelixSettings: "Edit your EY Helix settings below. Once saved and data is imported, the selected comparative date 1 and comparative date 2 dates will be used as comparisons against the designated analysis date in the OAR activity. To compare against only one comparative date, select'None' in the comparative 2 date selection. ",
	eyHelixDataHasChangedLine1: 'Data has changed since the last time settings were saved. Make new selections below and click',
	eyHelixDataHasChangedLine2: 'to update EY Helix settings.',
	all: 'All',
	multiple: 'Multiple',
	notApplicableAbbreviation: 'N/A',
	importEyHelixData: 'Import EY Helix data',
	editScreenshot: 'Edit',
	deleteNote: 'Delete',
	removeAnnotation: '移除註釋',
	addAnnotation: '加入註釋',
	addingBoundingBox: '選擇螢幕截圖上的區域以加入註釋，確認註釋並點選檢查標誌以儲存。',
	cancelBoundingBox: '取消',
	deleteScreenshot: 'Delete screenshot',
	openInFullscreen: 'Open in fullscreen',
	helixURLErrorMessage1: '對應的 EY Helix 項目設定不完整。',
	helixURLErrorMessage2: '請前往{0}頁面進行更新。',
	helixIsNotEnabledMessage: 'EY Helix 未啟用您的案件。',
	helixSetup: 'EY Helix設定',
	openAnalysisInHelix: 'Open analysis in EY Helix',
	helixInvaliddate: 'Invalid date. Select a date prior to the analysis date.',
	helixcomparativedateoptional: 'EY Helix comparative date 2 (optional)',
	helixpriorperioddate: 'EY Helix comparative date 1',
	helixanalysisperiod: 'EY Helix analysis date',
	helixfiscalDropDownLabel: '期間 {0} - {1}',

	helixSettingsEditButtonTitle: '編輯',
	helixImportDataEditButtonTitle: '匯入',
	helixImportInsufficientPermissionsMessage: '權限不足，無法執行 Helix 資料匯入。 請聯繫您的案件管理員並請求啟動 Helix 資料匯入的權限。',
	helixImportNotAllowed: '案件基本資料不允許匯入 Helix 資料',
	helixDeleteImportInsufficientPermissionsMessage: 'Insufficient permissions to delete the EY Helix data Import. Contact your engagement administrator and request permission to delete the EY Helix data.',

	EYAccountMappingStepLabel: 'Manage account mapping',

	EYAccountMappingOptional: '選擇性的',
	EYAccountMappingStepTitleSettingsCompleted: '執行安永科目對應',
	EYAccountMappingStepTitleSettingsIncomplete: 'Complete EY Helix settings to access the EY Helix Mapping Module',
	EYAccountMappingInstructions: 'Map client accounts to EY accounts and process changes. Once processing completes, import the data below.',
	manageAccountMappingButtonLabel: 'Manage account mapping',

	//EY Helix Setup Card
	EYHelixSetupTitle: 'EY Helix',
	EYHelixSetupSubTitle: '配置資料並將其匯入 EY Canvas',
	LastImported: '最後匯入',
	EYHelixSettings: 'EY Helix連接',
	NoEYHelixProjectLinkedLabel1: '此案件與 Primary EY Helix 項目無關。 請訪問',
	NoEYHelixProjectLinkedLabel2: '頁面建立連結。',
	NoEYHelixProjectLinkedHperlink: 'EY Helix Projects',
	NoDataImportedLabel: '尚未從 EY Helix 匯入資料。點選 EY Helix 設定以啟動該過程。',
	noHelixConnections: '點選\“EY Helix連接\”以建立連接。',
	helixConnectionExists: '存在連接',
	helixConnectionsExist: '存在連接',
	helixTrialBalanceImported: '已匯入範圍與策略試算表',
	helixTrialBalancesImported: '已匯入範圍與策略試算表',
	helixNoTrialBalanceImported: '點選\“EY Helix連接\”以匯入範圍與策略試算表。',

	// EY Helix - Map Accounts
	mapAccountsHelixCanvas: 'Click Edit to Map EY Canvas accounts to accounts imported from EY Helix',
	mapAccountsHelixCanvasSubtitle: 'Drag and drop each EY Helix account to map to an EY Canvas account. Click Manage accounts to create or edit EY Canvas accounts.',
	mapAccountsHelixHeaderLabel: 'EY Helix accounts',
	mapAccountsCanvasHeaderLabel: 'EY Canvas accounts',
	mapAccountsConnectedLabel: 'Accounts connected',
	mapAccountsShowMappedLabel: 'Show mapped accounts',
	mapAccountsHideMappedLabel: 'Hide mapped accounts',
	mapAccountsManageLabel: 'Manage accounts',
	mapAccountsAndDisclosuresManageLabel: '管理科目和揭露',
	mapAccountsNoCanvasAccounts: 'No accounts have been identified.',
	mapAccountsNoCanvasAccountsClick: 'Click',
	mapAccountsNoCanvasAccountGetStarted: ' to get started.',
	mapAccountsRemoveAccount: 'Remove',
	mapAccountsReImportHelixAccounts: 'Data import from EY Helix was not successful. Please re-import the data and try again.',
	mapAccountsReImportHelixAccountsHelpDesk: 'If the issue persists, contact the Help Desk',
	mapAccountsNoHelixAccountHasBeenImported: 'No EY Helix accounts have been imported.',
	mapAccountsNoHelixAccountHasBeenImportedCheckData: 'Check data in EY Helix and try again.',

	//Helix Analyzer
	accountNotRelatedToDocumentOnPhaseTwo: '沒有與此文件相關的科目',

	//PM TE SAD Widget
	materialityWidgetLabel: 'PM/TE/SAD',

	// TE labels
	planningmateriality: 'Planning Materiality',
	requiredTePercentage: '必須的TE',
	suggestedtepercentage: 'Suggested TE',
	currentperiodte: 'Current Period TE',
	priorperiodte: 'Prior Period TE',
	pmPriorPeriod: '前期 PM',
	tepercentage: 'TE percentage',
	teamount: 'TE amount',
	teLabel: 'Tolerable error',
	sugestedSAD: '建議之SAD百分比：',
	priorSAD: 'Prior period SAD',
	currentPeriodSAD: '本期SAD',
	sadPercentage: 'SAD percentage',
	sadAmount: 'SAD amount',
	rationaleLabel: '理由',
	suggestedTEPercentageInfo: '若依據案件特定因素，您欲選擇不同百分比，您將於跳出對話框中記錄該等因素。',
	rationaleTEDescription: '請考量上述所選屬性，輸入TE設定為PM某一百分比之理由。',
	teAmmountInvalid: '輸入有效金額或選擇50%或75%',
	highRiskTEAmmountInvalid: '輸入有效金額或選擇 50%',
	highRiskTERequired: '根據對上述考量因素的答覆，確定的 TE 百分比是必須的且不能更改。',

	// EY Helix Map Entities to Business Units Modal
	mapEntitiesModalTitle: '管理個體',
	mapEntitiesModalLeyendDescription: '管理下方 EY Helix 項目中的 EY Canvas 個體和營運單位之間的對應。',
	mapEntitiesModalLeyendNote: '注意：一個營運單位可以與一個或多個 EY Canvas 個體關聯。 儲存並匯入資料後，與 EY Helix 中的營運單位關聯的資料，將顯示為對應到的相關 EY Canvas 個體。',
	mapEntitiesModalEntityCodeLabel: '個體編號',
	mapEntitiesModalEmptyEntitiesList: 'No entities have been created.',
	mapEntitiesRelatedBusinessUnitDropdownPlaceholder: '相關營運單位',
	mapEntitiesSelectedBusinessUnitsCount: '已選擇 {0} 個營運單位',

	//AdjustedBasis
	enterAmount: 'Enter amount',
	basisAmount: 'Basis amount',
	lowEndOfRange: 'Low end of range',
	highEndOfRange: 'High end of range',
	suggestedRange: '基於上述因素，使用{0}之百分比，範圍自 {1} 到 {2} 可能係屬適當。倘若根據案件特定因素，導致所選金額超出範圍，您將須於下方記錄該等原因。',
	suggestedRangeLowPMBracket: '依據上述因素，{0} 的百分比可能比較適當。 若依據案件特定因素選擇的金額超出此百分比，系統將提示您記錄這些因素。',
	middle: 'middle',
	lowerEnd: 'lower end',
	higherEnd: 'higher end',
	lowEnd: 'low end',
	priorPeriodPm: 'Prior period PM: ',
	suggestedRangeSummary: '所建議區間',
	loadingMateriality: 'Loading materiality...',
	pmBasisPercentage: 'PM basis percentage',
	pmAmount: 'PM amount',
	currentPmAmount: '本期重大性水準金額',
	pmAmountPlaceholder: '輸入 PM 數量',
	currentPeriodPm: 'Current period PM: ',
	enterRationale: '請輸入理由',
	rationaleDescription: '請考量上述所選屬性，輸入PM基礎百分比之理由',
	pmValidationMessage: '規劃重大性不可超過該區間之上限',
	sadValidationMessage: '名目金額不可超過該區間之上限',
	sadRationaleDiscription: '考量以上所選屬性，輸入SAD百分比之理由',
	nopriorperiodDocs: '沒有可用的前期文件',
	addPriorPeriodEvidence: '新增前期證據',
	addToEvidenceLabel: '新增至證據',
	moveToEvidenceLabel: 'Move to evidence',
	addToEvidenceModalDescription: '為所選文件建立新名稱或保留現有名稱。',
	GoToSource: 'Go to source',
	//ITRiskITControls
	createNewITGC: '新的IT一般控制',
	relateITGC: 'Relate ITGCs',
	createNewITSP: '新IT證實查核程序',
	relateITSP: '相關IT證實查核程序',
	noITGC: '無IT一般控制',
	itRiskForITGCITSP: 'IT風險名稱（必填）',
	createITGCModalDescription: '在下方輸入IT一般控制詳情，然後點選“<b>{0}</b>”完成。如想另外建立IT一般控制，請點選“<b>{1}</b>”。',
	createITSPModalDescription: '在下方輸入IT證實查核程序詳情，然後點選“<b>{0}</b>”完成。如想另外建立IT證實查核程序，請點選“<b>{1}</b>”。',
	controlDesignEffectiveness: {
		[0]: {
			description: '未選擇'
		},
		[1]: {
			description: '有效'
		},
		[2]: {
			description: '無效'
		}
	},
	controlOperationEffectiveness: {
		[0]: {
			description: '未選擇'
		},
		[1]: {
			description: '有效'
		},
		[2]: {
			description: '無效'
		}
	},
	controlTesting: {
		[0]: {
			description: '未選擇'
		},
		[1]: {
			description: 'Yes'
		},
		[2]: {
			description: 'No'
		}
	},
	itAppTypes: {
		[0]: {
			label: 'IT App'
		},
		[1]: {
			label: 'SO'
		}
	},
	controlType: {
		[0]: {
			controlTypeName: '',
			shortName: '未選擇'
		},
		[1]: {
			controlTypeName: 'IT Application Control',
			shortName: 'Application'
		},
		[2]: {
			controlTypeName: 'IT Dependent Manual Control',
			shortName: 'ITDM'
		},
		[3]: {
			controlTypeName: 'Manual Prevent',
			shortName: 'Manual prevent'
		},
		[4]: {
			controlTypeName: 'Manual Detect',
			shortName: 'Manual detect'
		}
	},
	controlTypeEnumLabel: {
		[0]: {
			controlTypeName: 'Not selected'
		},
		[1]: {
			controlTypeName: 'IT應用程式控制'
		},
		[2]: {
			controlTypeName: '仰賴IT人工控制'
		},
		[3]: {
			controlTypeName: '人工預防'
		},
		[4]: {
			controlTypeName: '人工偵查'
		}
	},
	controlFrequencyType: {
		[0]: {
			controlFrequencyTypeName: 'Not selected'
		},
		[1]: {
			controlFrequencyTypeName: 'Many times per day'
		},
		[2]: {
			controlFrequencyTypeName: 'Daily'
		},
		[3]: {
			controlFrequencyTypeName: 'Weekly'
		},
		[4]: {
			controlFrequencyTypeName: 'Monthly'
		},
		[5]: {
			controlFrequencyTypeName: 'Quarterly'
		},
		[6]: {
			controlFrequencyTypeName: 'Annually'
		},
		[7]: {
			controlFrequencyTypeName: 'IT Application Control'
		},
		[8]: {
			controlFrequencyTypeName: 'Other'
		}
	},
	strategyType: {
		[0]: {
			strategyTypeName: '未選擇'
		},
		[1]: {
			strategyTypeName: '控制'
		},
		[2]: {
			strategyTypeName: '證實'
		},
		[3]: {
			strategyTypeName: '仰賴'
		},
		[4]: {
			strategyTypeName: '不仰賴'
		}
	},
	aggregateITEvaluationType: {
		[0]: {
			aggregateITEvaluationTypeName: 'Not Selected'
		},
		[1]: {
			aggregateITEvaluationTypeName: 'Support'
		},
		[2]: {
			aggregateITEvaluationTypeName: 'Not Support'
		},
		[3]: {
			aggregateITEvaluationTypeName: 'FS & ICFR Support'
		},
		[4]: {
			aggregateITEvaluationTypeName: 'FS Only Support'
		}
	},

	sampleItemFilterLabels: {
		filterTypeOfTags: 'Tags',
		noFiltersAvailable: 'No filters available',
		filterToolTip: 'Filter',
		clearAll: 'Clear all',
		showMore: 'more',
		filters: 'Filters',
		noResults: 'No results found',
	},

	stratergyTypeLabels: {
		[0]: {
			label: 'Not Selected'
		},
		[1]: {
			label: 'In scope'
		},
		[2]: {
			label: 'Out of scope'
		}
	},
	noChangeReasonCommentAvailable: '從文件齒輪選項點選編輯理由以輸入變更理由。',
	changeReasonModalTitle: '編輯變更理由',
	changeReasonModalText: '選擇報告日後變更文件的理由。若已進行多項行政變更，請從下方下拉選擇最重要的變更。若已進行行政變更及非行政變更，於下方選擇非行政。',
	changeReasonUploadModalTitle: '文件上傳理由',
	changeReasonUploadModalText: '選擇報告日後變更文件的理由。若已進行多項行政變更，請從下方下拉選擇最重要的變更。若已進行行政變更及非行政變更，於下方選擇非行政。',
	changeReasonModalComboPlaceholder: '選擇',
	changeReasonModalAnnotationText: '記錄所發生情況及加入資訊之理由；所執行之新增或額外查核程序，所取得之查核證據及所作成結論，以及對查核報告之影響。',
	changeReasonUploadModalAnnotationText: '記錄所發生情況及加入資訊之理由；所執行之新增或額外查核程序，所取得之查核證據及所作成結論，以及對查核報告之影響。',
	changeReasonModalAnnotationPlaceHolder: '輸入變更理由',
	changeReasonModalChangeReasonRequired: '更改儲存原因',
	reasonColumnTitle: '理由',
	shared: '共享',
	shareStatusOwned: '為此案件所有。',
	shareStatusShared: '與此案件分享。',
	lastModifiedBy: '最後修改者',
	fileSize: ' | {1} KB',
	openedLabelText: '已開啟',
	currentlyBeingModifiedBy: '目前修改者',
	OpenGuidedWorkflowDocument: '透過 EY Canvas FIT enablement打開此檔案',
	submitProfile: '提交基本資料',
	submitProfileFit: 'Submit profile',
	contentUpdateUnAuthorizedTooltipMessage: '權限不足以執行內容更新。請聯絡您的案件管理者並請求權限以進行內容更新。',
	submitProfileValidationErrorMessage: '基本資料僅能於已回答全部問題時提交。請篩選未完成問題、回答並再次提交。若問題仍然存在，請聯絡Help Desk。',
	pickADate: '選擇日期',

	/* Sign Offs */
	preparerHoverText: '以編製者身分簽名',
	reviewerHoverText: '以複核者身分簽名',
	preparerTitle: '編製者簽名',
	reviewerTitle: '複核者簽名',
	deleteHoverText: '移除簽名',
	preparer: 'Preparer',
	reviewer: 'Reviewer',
	preparerLabel: 'P',
	reviewerLabel: 'R',
	noSignOffsAvailable: 'No sign-offs available',
	none: '無',
	partnerInChargeLabel: 'PIC',
	eqrLabel: 'EQR',
	documentSignoffRequiredLabel: '簽核要求來自：',

	relatedDocumentsTitle: '相關文件',
	relatedTasksCount: '{0} Related tasks',
	relatedTasksTitle: 'Related tasks',
	relateTemporaryFiles: '相關暫存檔案',
	bodyRelatedDocumentsTitle: 'Documents associated to a body',
	relatedObjectsTitle: 'Related objects',
	relateDocumentsTitle: '管理相關文件',
	relateDocumentsToBodyTitle: '加入證據',
	relateDocumentsDesc: '請選擇文件以連結至Canvas表單',
	relateDocumentsToBodyDesc: '關聯來自此案件或來自此工作區中的其他案件的文件',
	relateDocumentsToTheBody: 'Relate a document from this engagement.',
	priorPeriodEvidencesToTheBody: 'Prior period evidences related to the body',
	relatedDocunentEngdisabed: '文件未與此案件共享',
	showOnlyRelatedDocuments: '僅顯示相關表單',
	manageDocuments: '管理文件',
	documentCount: '{0}文件',
	documentsCount: '{0}文件',
	relateDocumentsSearchPlaceholder: '搜尋文件',
	overwriteFormDesc: 'Select a form to overwrite the responses with the data from the current Canvas form. Note the current Canvas form will be moved to Temporary Files.',
	searchFormPlaceholder: '搜尋表單',
	overwriteLabel: '覆寫',
	confirmOverwriteLabel: '確認覆寫',
	confirmOverwriteDesc: "您確定要將'{0}'表單之內容複製到'{1}'表單?'{2}'表單中的回應將會被覆蓋，但相關證據及物件並不會被覆蓋。覆蓋完成後，所適用的相關證據及物件應被重新連結至'{3}'表單。'{4}'表單將維持原先的基本資料/GAM層級，因此若該基本資料不同於'{5}'表單。請複核並整理所複製之內容。 \n 當覆蓋進行中時，請勿關閉瀏覽器或離開。覆蓋處理完成後，'{6}'表單將被移動至暫存檔案，您將被引導至'{7}'表單。此動作無法復原。 ",
	formSelectionRequired: '請選擇表單進行覆蓋。',

	open: '開啟',
	startCoeditMode: 'Start multi-user editing',
	endCoeditMode: 'End multi-user editing',
	openReadOnly: '開啟唯讀',
	copyLink: 'Copy link',
	rename: '重新命名',
	viewHistory: '檢視歷史紀錄',
	documentOpenModelLabel: '文件正被修改',
	modelUserOpenedTheDocumentText: '此使用者開啟文件',
	modelDocumentOpenedText: '此文件正被修改由',
	modelOpenedDocumentConflictText: '開啟文件可能造成衝突，故建議以唯讀開啟。若您欲成為文件編輯者，則',
	clickHereEnabledText: '點選這裡',
	documentOptions: '文件選項',
	accountDetails: '科目詳細內容',

	// DAAS labels
	coEditModeIsEnding: 'Multi-user editing is ending',
	coEditMode: 'Multi-user editing',
	checkInInProgressMessage: 'Check-in in progress. It may take up to 20 minutes for the document to be checked in. Please refresh for updates',
	checkInInErrorLabel: '檔案簽入失敗',
	checkOutInProgressMessage: 'Check-out in progress. It may take up to 20 minutes for the document to be checked out. Please refresh for updates',
	checkOutInProgressLabel: '正在簽出檔案。',
	checkInInProgressLabel: '正在簽入檔案',
	checkOutInErrorLabel: '檔案簽出失敗。',
	daasErrorMessage: 'Operation cannot be complete at this time. Refresh the page and try again. If the issue persists contact the Help Desk',
	coEditModeIsStarting: 'Multi-user editing is starting',
	daasOpenDocumentWarning: 'Multi-user editing may have been ended by another user. Refresh the page and try again.',
	beingEditedInCoeditMode: 'Being edited in multi-user edit. Edit started by {0}',
	beingEditedInCoeditModeOn: 'on {0}.',
	beingEditedInCoeditModeError: 'Being edited in multi-user edit',
	coEditModeAutomaticallyEnds: '檔案處於多使用者編輯模式，該模式將在{0}天后自動結束。',
	coEditModeAutomaticallyEndsToday: '檔案目前處於多使用者編輯模式，該模式將於今天結束。',
	daasStartCollaborationModeWarning: 'Collaboration mode may have been started by another user. Refresh the page and try again.',
	documentCurrentlyBeingModifiedTitle: 'Document currently being modified',
	documentCurrentlyBeingModifiedHeader: 'This document is currently being modified by {0}. This user opened the document',
	documentCurrentlyBeingModifiedBody: 'Starting multi-user edit mode could cause conflicts, so we recommend discussing with {0} before proceeding. Select {1} to start the multi-user edit mode or {2} to return without starting multi-user edit mode.',
	documentEndMultiUserEditingTitle: '​結束多使用者編輯',
	documentEndMultiUserEditingHeader: 'Warning: Other users may be actively editing this document.  This can be checked by opening the document and seeing if other users are currently in the file. Please confirm that all changes are complete before ending multi-user mode. File changes in multi-user mode may take up to 1 minute to be processed. Therefore, please wait at least 1 minute after exiting the file before ending multi-user mode.',
	documentEndMultiUserEditingBody: 'Select {0} to end the multi-user edit mode or {1} to return without ending multi-user edit mode.',
	startMultiuserEditing: 'Start',

	/* Engagement Comments */
	clear: '清除',
	close: '關閉',
	reOpen: '重新開啟',
	reply: '加入回覆',
	replyLabel: 'Reply',
	unselectComment: 'Unselect comment',
	commentText: '輸入文字',
	replyText: 'Reply text',
	openStatus: '開啟',
	clearedStatus: '已清除',
	closedStatus: '已關閉',
	chartCommentsTitle: '複核註記',
	showComments: '顯示註解',
	noRecordsFound: '找不到紀錄',
	noCommentsFound: '使用下面的輸入發表註解。 將註解指派給用戶並指定優先層級和到期日。',
	newComment: '加入註解',
	addNoteTitle: '新增註記',
	editComment: '編輯註解',
	newReply: 'Add reply',
	editReply: 'Edit reply',
	commentTextRequired: '註解文字為必須',
	replyTextRequired: 'Reply text required',
	myComments: '我的註解',
	assignTo: 'Assign to',
	theCommentMustBeAssigned: '指派為必須',
	priorityRequired: 'Priority required',
	dueDateRequired: 'Due date required',
	assignedTo: '指派給',
	allComments: '全部註解',
	assignedToMe: '指派給我',
	unassigned: '未指派',
	draggableCommentsPlaceholder: 'Enter text to add a new comment',
	draggableNotesPlaceholder: '輸入文字以新增新註記',
	enterReply: 'Enter reply',
	dueDate: 'Due date',
	commentsAmmount: '{count} 註記',
	singleCommentAmmount: '{count} 註記',
	eyInternal: 'EY',
	noneAvailable: 'None available',

	navHelixProjects: 'EY Helix connections',

	evidence: '證據',
	priorPeriod: '前期',
	temporaryFiles: '暫存檔案',
	priorPeriodEvidence: '前期',
	closed: '所有指派事項已解決',

	/*Delete*/
	deleteFileTitle: '刪除文件',
	deleteFileCloseBtnTitle: '取消',
	deleteFileConfirmBtnTitle: '刪除',
	deleteFileCloseTitle: '關閉',
	deleteFileModalMessage: '請確認是否要刪除所選文件？',
	/*Rename*/
	editCanvasformObjects: 'Edit the attributes and click <b>Save</b>.',
	renameFileModalMessage: 'Rename the document and click Save.',
	renameScreenshotModalMessage: '請重新命名此螢幕擷取畫面並點擊確認',

	renameFileTitle: '重新命名文件',
	fileNameRequired: '檔名為必須',
	invalidCharacters: '檔名不能包含: */:<>\\?|"',
	existingFileName: '檔名非唯一。請重新整理頁面或重新命名檔案以移除此訊息。',
	maxLengthExceeded: '文件名稱不能超過115字元。',

	STEntityProfileBannerMessage: 'Changes have been made to one or more of the entity profiles that will result in content updates. Navigate back to the Profile entities page and click "Import content" to receive the new content applicable to the entity\'s profile or revert the answers to a previous state.',
	independenceValidationForOwnForm: 'Changes have been made to the Independence responses, but have not been submitted. If the changes are intentional, ensure that the responses are submitted. If the changes are not intentional, review the Changes view and manually revert the answers back to previous selections.',
	independenceValidationForOthersForm: 'Changes have been made to the Independence responses, but have not been submitted by the team member. Ensure that the team member reviews the changes and submits the same if they were intentional.',
	insufficientRightsForIndependenceSubmission: 'Insufficient permissions to edit content. Contact your engagement administrator and request permission to edit content.',
	submitIndependenceProfileV2Message: '請查看基本資料並確認回覆皆準確。若是，請簽核並繼續案件。',
	submitIndependenceProfileV2EditMessage: '無任何會導致案件內容變更的基本資料變更。 若有需要，使用案件內容更新頁面執行內容更新。',
	insufficientRightsForProfileV2Submission: '編輯基本資料的權限不足。 請聯繫您的案件管理者並請求編輯基本資料的權限。',
	returnToDashboard: 'Return to Dashboard',
	returnToDashboardFit: 'Return to dashboard',
	profileV2ChangeNotSubmittedBannerMessage: 'Changes have been made to the Profile that will result in content updates. Submit the Profile to receive the new content or revert the answers to the previous state.',
	independenceChangeNotSubmittedBannerMessage: 'Changes have been made to the independence form that need to be re-submitted. Submit the independence form or deactivate this user to clear the validation.',
	multiEntityIndividualProfileBannerMessage: 'Insufficient permissions to edit the profile. Contact your engagement administrator and request permission to edit the profile.',
	scotStrategy: 'SCOT策略',
	wcgwStrategy: 'WCGW策略',
	itProcessStrategy: 'IT process strategy',

	/*Edit Wcgw*/
	editWcgw: '編輯WCGW',
	viewWcgw: '檢視WCGW',
	editScot: '編輯SCOT',
	viewScot: '檢視SCOT',
	showIncomplete: '顯示未完成',
	forms: '表單',
	form: '表單',
	comments: '註記',
	changes: '變更',
	editHeader: '編輯表頭',
	editSection: '編輯區塊',
	editBody: '編輯內文',
	editSectionDescription: "Edit the details for the section and click'Save'. ",
	editHeaderDescription: "Edit the details for the header and click'Save'. ",
	editBodyDescription: "Edit the details for the body and click'Save'. ",
	manageObject: '管理物件',
	relatedObjects: 'Related objects',

	/* Manage body objects */
	bro_manage_WCGWTask_title: '連結WCGWs',
	bro_manage_WCGWTask_instructions: '管理適用之WCGWs',
	bro_manage_WCGWTask_noDataLabel: '找不到結果',

	/*Add/Edit ITGC*/
	addITGC: 'Add ITGC',
	addNewITGC: 'Add new ITGC',
	addExistingITGC: 'Add existing ITGC',
	addITGCDescription: 'Enter the ITGC description.',
	itControlNameRequired: 'ITGC Name is required',
	frequencyRequired: 'Frequency is required',
	frequencyITGC: 'Select frequency',
	nameITGC: 'ITGC name (required)',
	iTProcesslabel: 'IT process',
	editITGC: 'Edit ITGC',
	editITSP: 'Edit ITSP',
	editITGCDescription: 'Edit the ITGC and its associated attributes',
	editITSPDescription: 'Edit the ITSP and its associated attributes',
	viewITGC: 'View ITGC',
	viewITSP: 'View ITSP',
	itgcTaskDescription: 'Perform our designed tests of ITGCs to obtain sufficient appropriate audit evidence of their operating effectiveness throughout the period of reliance.',
	/**
	 * Add Edit ITGC
	 */
	addITSPDescription: 'Enter the ITSP description.',
	selectITRisk: 'Select IT risk (required)',
	itRiskRequired: 'IT risk (required)',
	itspNameRequired: 'ITSP name (required)',
	itspTaskDescription: 'Customize this task description to design the nature, timing and extent of IT-substantive procedures to obtain sufficient appropriate audit evidence that the IT risks are addressed effectively throughout the period of reliance.<br />When the IT-substantive procedure is performed as of an interim date, design and perform procedures to obtain additional evidence that the IT risks are addressed for the period covered by our interim procedures through period-end.<br />We conclude on the results of our IT-substantive procedures.',
	itspRequired: 'ITSP Name is required',
	selectTestingStrategy: 'Design the nature, timing and extent of our tests of controls to obtain sufficient appropriate audit evidence that the control operates effectively as designed throughout the period of reliance to prevent or detect and correct material misstatements at the assertion level. <br /> Conclude on the operating effectiveness of controls, by evaluating the results of our tests of controls including when we extended our sample size and performed tests of compensating controls.',
	itControlNameTest: 'Test {0}',

	/*Edit ITControl*/
	editITControl: 'Edit ITGC/ITSP',
	viewITControl: 'View ITGC/ITSP',

	/*Add/Edit ITRisk*/
	editITRisk: 'Edit IT risk',
	editITRiskDescription: 'Edit the IT risk',
	viewITRisk: 'View IT risk',
	addITRisk: 'Add IT risk',
	addITRiskDescription: 'Enter the IT risk description',
	selectITProcess: 'Select IT process (required)',
	itRiskName: 'IT risk',
	itRiskNameRequired: 'IT risk (required)',
	riskNameRequired: 'IT risk is required',
	processIdRequired: 'IT process is required',
	itProcessRequired: 'IT process (required)',
	hasNoITGC: 'There are no ITGCs that address the IT risk',

	/*Edit Risk*/
	editRisk: '編輯風險',
	viewRisk: '檢視風險',

	/*Edit Control*/
	editControl: '編輯控制',
	viewControl: 'View control',
	scotRelatedControls: 'Controls related to',
	applicationControl: 'Application control',
	iTDependentManualControl: 'IT dependent manual control',
	noAapplicationControlAvailable: 'No application controls',
	noITDependentManualControlAvailable: 'No ITDM controls',
	isIPEManuallyTested: 'The automated portion of this ITDM control is only the use of system-generated report(s) that are tested substantively',

	/*Edit ITProcess*/
	editITSOProcess: '編輯IT/SO流程',
	viewITSOProcess: '檢視IT/SO流程',

	/*Edit ITApplication*/
	viewITAppSO: '檢視IT App/SO',
	editITAppSO: '編輯IT App/SO',
	strategy: '策略',
	nameRequired: '名稱為必須',
	name: '名稱',

	/*Snap shot*/
	currentVersion: '目前版本',
	compareVersion: '請選擇版本進行比較',
	snapshotVersionNotAvailable: '無版本可比較',
	snapshots: 'Snapshots',
	sharedFormWarning: "This is a shared Canvas Form. The objects and evidence exists in the original engagement and will not be added to this engagement upon unlink. See <a style='color: #467cbe' href='https://live.atlas.ey.com/#library/104/p/SL33184174-396647/C_33404446/C_38129691' target='_blank'>enablement here</a> for further details. ",
	fullView: '全螢幕檢視',
	defaultView: '預設檢視',
	print: '列印',
	version: '版本',
	navigationUnavailable: '在“追蹤修訂和屬性”視窗中無法前往。 請查看“問題及回覆”以再次啟用前往。',
	snapshotUpdate: '已更新',
	snapshotNew: '新',
	snapshotRemoved: '已移除',
	snapshotRollforward: '在推滾時建立',
	snapshotRestore: '在還原時建立',
	snapshotCopy: '在複製時建立',

	/*Special Body*/
	priorPeriodAmount: '前期金額',

	// Helix special body:
	helixScreenshotListLoading: 'Loading screenshots...',
	helixScreenshotLoading: 'Loading screenshot image...',
	helixScreenshotDeleting: 'Deleting screenshot...',
	helixNotesLoading: '正在下載標記...',
	helixNotesBoundingBoxShow: 'Show annotations',
	helixNotesBoundingBoxHide: 'Hide annotations',
	helixNoteReferenceNumber: '#',
	helixNoteReferenceNumberPlaceholder: 'Enter reference number',
	helixNoteText: 'Note',
	helixNoteTextPlaceholder: '輸入標記內容',
	helixNoteAnnotate: 'Annotate',
	helixNoteAnnotateMessage: 'Select an area on the screenshot to annotate, confirm the annotation, and click the checkmark to save.',
	helixRemoveAnnotation: 'Delete annotation',

	/* User lookup body */
	userLookupInstructionalText: 'Enter name or email and press enter to see results.',
	userLookupShortInstructionalText: 'Enter name or email and press enter',

	/*Guidance*/
	guidance: '指引',
	noIncompleteBodies: '自目錄選擇標題或區塊以檢視內容',
	noUnresolvedComments: 'Select a Header or Section from the navigation menu to view content',
	addComment: '加入註解',

	/*Independence*/
	otherFormIndependenceMessage: 'The content of this independence form has been updated and the user has not logged in again since that happened. As a result certain responses may be incomplete. The prior independence status has been retained for reference.',
	override: '覆蓋',
	grantAccess: '允許登入',
	denyAccess: '拒絕登入',
	overrideSmall: '覆蓋',
	grantAccessSmall: '允許登入',
	denyAccessSmall: '拒絕登入',
	status: '狀態',
	undefined: '未定義',
	incomplete: '未完成',
	noMattersIdentified: '未辨認出事項',
	matterIdentifiedPendingAction: '已辨認事項 - 待處理',
	matterResolvedDeniedAccess: '已解決事項 - 拒絕登入',
	matterResolvedGrantedAccess: '已解決事項 - 允許登入',
	notApplicable: '不適用',
	restored: '已還原',
	overridden: '已覆蓋',
	priorNoMattersIdentified: 'Prior - No Matters Identified',
	priorMatterIdentifiedPendingAction: 'Prior - Matter Identified - Pending Action',
	priorMatterResolvedGrantedAccess: 'Prior - Matter Resolved - Granted Access',
	priorMatterResolvedDeniedAccess: 'Prior - Matter Resolved - Denied Access',
	byOn: 'by {0} on',
	byLabel: 'by',
	onLabel: 'on',
	modifiedBy: 'Modified by',
	reason: '理由',
	submit: '提交',
	submitTemplate: 'Submit template',
	independenceHoverText: '您的角色必須為主辦會計師；案件會計師；或執行總監以允許、拒絕或覆蓋該使用者之登入。',
	enterRationaleText: '請輸入理由',
	enterRationalePlaceholderText: '請輸入理由文字',
	requiredRationaleText: '理由(必須)',
	rationaleTextRequired: '理由為必須',

	sharedExternalWarning: 'This form is shared via Canvas Client Portal and accessible by external team members. Only enter responses and comments that should be shared with external team members.',
	independenceViewTemplateMessage: "This form serves as a template for each team member's individual independence inquiry. <br /> When completing the independence inquiry, there are several questions that relate to the independence requirements applicable to the entity under audit for which each team member must provide a response. Select the appropriate responses to these questions. Responses will be synchronized to each team member's individual independence inquiry. If a team member has selected a different response, they will have to reconfirm their independence when they re-enter the engagement. If they do not re-enter the engagement, then their previous independence status and responses will be preserved. <br /> Only authorized users can make changes to the independence template. Speak to an engagement administrator. Any changes made must be submitted, even if they are manually undone prior to archive. ",

	/**
	 * FORM OBJECTS: SCOT-WCGW-CONTROL
	 */
	fo_instructionalText: '選擇Canvas表單紀錄的物件',
	fsro_instructionalText: '請管理此階段相關物件',
	relObj_title_risk: 'Risks',
	relObj_title_riskType: '風險類型',
	fo_showOnlyRelated: '僅顯示連結物件',
	scotsCount: '{0} SCOTs',
	wcgwsCount: '{0} WCGWs',
	itsoCount: '{0} IT應用程式/服務機構',
	controlsCount: '{0} 控制',
	itControlsCount: '{0} IT控制',
	itGcCount: '{0} ITGCs',
	itSpCount: '{0} ITSPs',
	itProcessesCount: '{0} IT流程',
	risksCount: '{0} 風險',
	accountsCount: '{0} Accounts',

	stEntitiesCount: '{0} Entities',

	componentsCount: '{0} Components',
	view: 'View',
	searchByScotName: '依照SCOT名稱搜尋',
	searchByWcgwName: '依照WCGW名稱搜尋',
	searchByITSOAppName: '依照IT/SO應用程式名稱搜尋',
	searchByControlName: '依照控制名稱搜尋',
	searchByItControlName: '依照IT控制名稱搜尋',
	searchByItProcessName: 'Search by IT process name',
	searchByRiskName: '依照風險名稱搜尋',
	searchByAccountName: 'Search by Account name',
	searchBySTEntityName: 'Search by Entity name',
	searchByEstimateName: 'Search by estimate name',
	searchByComponentName: 'Search by Component name',
	noScotsAvailable: '此案件無Scots。',
	noRisksAvailable: '此案件無風險',
	noControlsAvailable: '此案件無控制。',
	noItControlsAvailable: '此案件無IT控制。',
	noItProcessesAvailable: '此案件無IT流程。',
	noItApplicationsAvailable: '此案件無IT應用程式。',
	noAccountsAvailableLabel: 'No Accounts are available in this engagement.',
	noObjectsRelatedToForm: '沒有物件連結此Canvas表單',
	noDocumentControlsAvailable: 'No controls are associated in this document.',
	noDocumentScotsAvailable: 'No SCOTs are associated to this document.',
	noSTEntitiesAvailable: 'No Entities are available in this engagement.',
	noComponentsAvailable: 'No components are available in this engagement.',
	editObjectDescription: 'Edit the association of objects to this form',
	editObjectsLabel: 'Edit objects',
	noITGCsOrITSPsHaveBeenIdentified: 'No ITGCs or ITSPs have been identified',
	noItProcessIdentified: 'No IT process have been identified',
	noControlsIdentified: 'No controls have been identified',
	noRelatedRisksIdentified: 'No related significant or fraud risks have been identified',
	noItApplicationsIdentified: 'No IT Applications have been identified',
	noSCOTIdentified: 'No SCOTs have been identified',
	noWCGWIdentified: 'No WCGWs have been identified',
	maxLimitLabel: 'Maximum number of objects have been selected.',
	minLimitLabel: 'Minimum number of objects have been selected.',

	relatedITAppsTitle: 'IT流程及相關IT應用系統',
	relatedWCGWTasksTitle: 'WCGWs及相關任務',
	noRelatedTasks: '無相關任務',
	noRelatedWcgw: '無相關WCGWs',
	noRelatedControls: '無相關控制',
	controlRelatedRisksTitle: 'Controls and related risks',
	sCOTRelatedRisksTitle: 'SCOTs及相關風險',
	scotRelatedItApp: 'SCOT相關的 IT應用程式',
	relatedItApps: 'Related IT apps',
	relatedRisksTitle: '相關風險',
	relatedItRisksItProcessesTitle: 'ITGCs and related IT processes and IT risks',
	testingTitle: '測試',
	strategyTitle: '策略',
	yes: '是',
	no: '否',
	noRelatedRisks: '無相關顯著或舞弊風險',
	closeAllComments: '關閉全部註解',
	closeComments: '關閉註解',
	closeCommentsDescription: '所有開啟及清除的註解將會被關閉。您確定要關閉此{0}所有的註解?',
	addCanvasFormDigital: '數位',
	addCanvasFormCore: '核心',
	addCanvasFormNonComplex: '非複雜',
	addCanvasFormComplex: '複雜',
	addCanvasFormListed: '上市櫃',
	addCanvasFormGroupAudit: '集團審計',
	addCanvasFormPCAOBFS: 'PCAOB-FS',
	addCanvasFormPCAOBIA: 'PCAOB-IA',
	addCanvasFormStandards: '準則',
	addCanvasFormLanguage: '語言',
	addCanvasFormNoResultFound: '找不到結果',
	addCanvasFormStandardsNotSelectedMessage: '準則為必要欄位',
	addCanvasFormLanguageNotSelectedMessage: '語言為必要欄位',

	/* Confidentiality */
	confidentialityPlaceholder: '選擇保密',
	confidentiality: '機密',
	confidentialityTitle: '文件機密',
	confidentialityText: '設定開啟此文件所需要之登入層級。登入層級係由案件管理者於「管理團隊」頁面設定。若此文件已設定機密，僅可開啟文件者能夠變更。',
	confidentialityNotOpenable: '無法開啟文件，因您的案件權限不足。 登入層級由案件管理者於「管理團隊」設定。',
	confidentialityTargetNotOpenable: '機密文件僅可從來源案件開啟。',
	backToCCP: 'Back to EY Canvas Client Portal',
	guidanceMessageBackToCCP: '填寫此表單後，返回EY Canvas Client Portal，並提交請求給EY。',
	noProfileInformationFound: 'No profile information found. Refresh the page and try again. If the issue persists contact the Help Desk.',
	confirmUpdate: '確認更新',
	keepVersion: '保留此版本',
	conflictDescription: '{0} has edited this text {1} since it was opened. Select the version that should be retained.',
	currentConflictVersion: '目前版本',
	serverConflictVersion: '伺服器版本',
	conflictShowChanges: '顯示追蹤修訂',
	sectionViewTrackChangesDropdownPlaceholder: 'Select version',
	verifyingIndependence: '驗證獨立性狀態，請稍等。',
	creatingIndependenceForm: '建立獨立性表單。',
	meCallFailed: 'Failed to retrieve user information. Please refresh the page and try again. If the issue persists please contact Help Desk.',
	getUserByIdFailed: 'Failed to retrieve user independence status. Please refresh the page and try again. If the issue persists please contact Help Desk.',
	independenceFormCreationFailed: 'Failed to create user independence form. Please refresh the page and try again. If the issue persists please contact Help Desk.',
	gettingProfile: 'Getting profile information please wait.',
	invalidDocumentId: '文件Id無效。請重新整理頁面並重試。若錯誤仍然存在，請聯絡Help Desk。',
	returnToEditMode: '回到編輯模式',
	saveAndCloseButtonTitle: 'Save & close',
	formCreationFailed: '建立表單失敗。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',

	/*Sign-off requirements*/
	signOffRequirements: '簽核要求',
	signoffRequirementsModalTitle: '簽核要求',
	signoffRequirementsModalDescription1: '調整本文件執行簽核的要求如下。',
	signoffRequirementsModalDescription2: '某些簽核要求無法調整，因為它們是 EY Canvas 的要求。',
	signoffRequirementsModalSaveLabel: '儲存',
	signoffRequirementsModalCancelLabel: '取消',
	signoffRequirementsModalCloseLabel: '關閉',
	signoffRequirementsModalPICLabel: 'PIC',
	signoffRequirementsModalEQRLabel: 'EQR',

	/*<Ares>*/
	/* View changes */
	viewChanges: 'View changes',
	viewChangesModalTitle: 'View changes',
	documentModificationAlert: '此活動最後修改者',
	dismiss: '撤回',

	/*Task List*/
	aresPageTitle: 'EY Canvas FIT enablement',
	aresPageSubtitle: '完成以下步驟，提供要求的關於您的查核資訊。',
	summary: '彙總',
	aresNoDocumentFound: '所選活動無其他資訊',
	taskSubTitleNoValue: '無說明',
	mainActivities: '主要活動',
	unmarkComplete: '取消標示為完成',
	markCompleteTitleTip: '標示為完成',
	disableMarkCompleteTitleTip: '請確認所有連結的文件至少由一個編製者及一個複核者簽核以將該活動標示為完成',
	/*Activity Summary*/
	activitySummary: 'Activity Summary',
	selectedAnswers: 'Selected answers',
	allAnswers: 'All answers',
	incompleteResponses: '未完成回應',
	previous: '前',
	next: '後',
	viewAsLabel: '檢視以',
	rolePreparerLabel: '編製者',
	roleDetailedReviewerLabel: '詳細複核者',
	roleGeneralReviewerLabel: '一般性複核者',
	roleEQRLabel: 'EQR',
	/*Simple Helix*/
	helixPlaceholder: 'Guidance type 7 with guidanceurl is required for helix screenshots.',
	noNotesAvailable: '未建立標記',
	addScreenshot: 'Add screenshot',
	replaceScreenshot: '取代螢幕截圖',
	replaceFrameDescription: '複核下方分析並點選取代，以取代已存在之螢幕截圖',
	addNote: '新增標記',
	notes: '標記',
	noScreenshotsAvailable: 'Click {viewDataAnalytic} to get started',
	viewDataAnalytic: 'View data analytic',
	/* Delete modal Helix screenshot*/
	modalTitle: 'Delete screenshot',
	sureDeleteBeforeName: 'Do you want to delete the screenshot?',
	sureDeleteAfterName: '刪除螢幕截圖後，所有相連結的標記也將被刪除，並且此操作無法撤消。',

	/*uploadDocument body type */
	relateExistingDocuments: 'Relate existing documents',
	fromEngagementOr: '從這個/其他案件或',
	browse: 'browse',
	toUpload: 'to upload',
	signoffs: 'Sign-offs',
	addDocument: 'Add documents',
	uploadDocument: 'Upload document',
	relateDocument: '連結現有文件',
	generateAccountRiskAssessmentPackage: 'Generate group ALRA',
	relateDocumentsToBodyAresTitle: 'Relate documents',
	discardLabel: 'Discard',
	uploadDocumentLabel: 'Upload Document',
	confirm: 'Confirm',
	duplicateDocumentHeader: 'One or more documents with the same name already exist in this engagement (either as Evidence or Temporary files).',
	duplicateDocumentInstruction: 'Select ‘Overwrite’ to upload the document and replace the existing file or ‘Discard’ to cancel. If this existing file is in Evidence, then the document will be uploaded to Evidence. If the existing file is in Temporary files, then the document will be uploaded to Temporary files.',
	maxUploadFilesError: '系統最多可同時上傳10個文件',
	/*</Ares>*/
	noTaskRelatedToThisDocument: 'No task related to this document',
	uncheckTrackChangesToSave: '取消勾選顯示追蹤修訂以儲存版本',
	reviewRoleCloseCommentsTitle: '未解決註解',
	reviewRoleCloseCommentsDesc: '有未解決註解待處理。使用篩選未解決註解。',

	/*Document Upload - PIC/EQR Required Body type*/
	requirementDetails: 'Requirement details',

	//Risk Factors
	riskFactor: '相關事件和情況/不實表達風險',
	manageRisk: '管理風險',
	noRiskFactors: 'No relevant events and conditions / risks of misstatement have been identified',
	relateRiskFactorsToRisks: '決定事件與情況之重大程度',
	riskType: '風險類型',
	relateToRisk: '連結至風險',
	noRisksIdentified: '無辨認風險',
	notDefined: '未定義',
	selectValidRiskType: '請選擇有效的風險類型',
	newRisk: '加入新風險',
	notAROMM: 'Not a Risk of material misstatement',
	describeRationale: '說明理由',
	noRisksIdentifiedForTheSpecificRiskType: '無辨認{0}s',
	addAnAccount: '關聯其他科目',
	selectAnAccount: '選擇科目',
	noAccountsHaveBeenIdentified: '未辨認科目',
	accountSelected: '科目',
	statementType: '報表類型',
	selectAssertions: '選擇聲明',
	noAssertionsIdentifiedForAccount: '此科目未辨認聲明',
	relatedAssertions: '相關聲明',
	editAccount: '編輯科目和揭露',
	addNewDescription: '加入新的說明',
	editRiskFactorDescription: '編輯說明',
	enterRiskFactorDescription: '輸入相關事件與條件/不實表達風險的描述',
	riskFactorDescriptionRequired: '相關事件與條件/不實表達風險的描述為必要的',
	riskFactorDescription: '相關事件和情況/不實表達風險的描述',
	createNewAccount: '建立新科目',
	createAccountLabel: '成功建立該科目 {0}',
	updateAccountLabel: '成功儲存編輯至該科目 {0}',
	deleteAccountLabel: '已成功刪除',
	significanceLabel: '重大性',
	provideRationale: '請提供理由以便儲存您的選擇',
	clearRiskSignificance: '明確的風險意義和說明',
	clearSignificance: '明確的意義和說明',

	// Account Summary
	unavailable: 'Unavailable',
	notAvailable: 'Not available',
	fraudRisk: '舞弊風險',
	significantRisk: '重大風險',
	significantRiskIndicator: 'SR',
	fraudRiskIndicator: 'FR',
	inherentRisk: 'ROMM',
	inherentRiskIndicator: 'ROMM',
	prioryearbalance: '前期餘額：',
	accounts: '科目',
	accountsOther: '科目 - 其他',
	accountsSignDis: '重大揭露',
	xMateriality: 'Multiples of PM',
	xTEMateriality: 'xTE',
	estimateAssociated: '估計相關',
	designation: 'Designation',
	noAccountshasbeenIdentified: '尚未辨認任何科目。',
	noAccountsOtherhasbeenIdentified: '未辨認其他科目。',
	noAccountsSigfhasbeenIdentified: '未辨認重大揭露。',
	addOtherAccounts: '新增科目 - 其他',
	addSignificantDisclosure: '新增重要揭露',
	pydesignation: 'Prior designation: ',
	notapplicable: 'N/A',
	noApplicable: '不適用',
	changeDesignationMessage: 'You are about to change the designation of the account',
	changeDesignationTitle: 'Change account designation',
	continue: '繼續',
	currentYearBalance: '當期',
	currentPeriodBalance: '目前階段',
	priorYear: 'Prior year',
	priorYearDesignation: '前期指定',
	priorYearEstimation: '前期估計',
	priorPeriodChange: '％ 改變',
	analytics: '分析',
	notROMMHeader: '非重大不實表達風險',
	manageEyCanvasAccounts: '管理 EY Canvas 科目',
	manageAccountMapping: '管理科目配對',
	manageAccountMappingCloseButton: '使用頁尾的按鈕關閉。',
	manageAccountMappingToasterMessage: '無法連線到 EY Helix。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	inherentRiskTypeChangeMessage: 'By changing account assertion(s) from relevant to not relevant, certain associations in Canvas, including WCGWs to Assertions, will be removed and PSPs will be demoted to OSPs. Click “Confirm” if you would like to proceed. Click “Cancel” to return without changes.',

	analyticsIconDisabled: 'EY Helix is not enabled for your engagement.',
	//Estimate
	estimatesTitle: '估計',
	relatedAccount: '相關科目',
	relatedAccounts: '相關科目',
	currentBalance: '當前餘額',
	priorBalance: '前期餘額',
	unrelateEstimates: '不相關的估計科目',
	manageEstimates: '管理估計',
	noEstimatesCreated: '尚未建立任何估計值。',
	createEstimates: '建立估計',
	estimateStarted: '開始',
	createEstimateDocument: '建立估計檔案',
	noEstimatesFound: 'No estimates have been identified',
	relateEstimateLink: 'Relate estimates',

	//Journal Source
	noJournalEntrySourcesAreAvailable: '沒有可用的日記帳分錄來源。',
	jeSourceName: 'JE來源',
	jeSourceNameTooltip: 'JE來源',
	changeInUse: '用途變更',
	grossValue: '關聯交易總額',
	relatedTransactions: '關聯交易數',
	relevantTransactions: '與重大交易類別相關',
	expandAll: '展開全部',
	collapseAll: '全部收縮',
	descriptionLabel: '請提供一個關於此來源用途的簡要描述。',
	jeSourceTypesLabel: '這個來源記錄的日記分錄是系統生成的還是人工輸入的？',
	journalEntries: '該來源是否用於記錄非標準日記帳分錄（即非經常性、不尋常的交易或調整）？',
	identifySCOTsLabel: '確定與 JE 來源相關的 SCOT（選擇所有適用項）',
	systemGeneratedLabel: '系統生成',
	manualLabel: '人工的',
	bothLabel: '兩個都',
	accountEstimateLabel: 'Est',
	addSCOTLabel: '新增SCOT',
	newSCOTLabel: '建立重大交易流程',
	addSCOTModalDescription: '您可以建立 SCOTs。儲存後將套用變更。',
	scotnameRequired: 'SCOT 名稱為必填的。',
	scotCategoryRequired: 'SCOT 類別為必填的。',
	estimateRequired: '估計是必須的。',

	jeSourcesLabel: 'JE 來源',
	jeSourcesToSCOTs: 'JE 來源至 SCOTs',
	scotsToSignificantAccounts: 'SCOTs 至重大科目',

	//Modal common labels.
	modalCloseTitle: '關閉',
	modalConfirmButton: '儲存變更',
	modalCancelTitle: '取消',
	modalSave: 'Save',
	modalSaveAndClose: '儲存及關閉',
	modalSaveAndAdd: '儲存並新增',
	modalSaveAndCreateAnother: 'Save and create another',

	//Add & Manage Risks
	addNewRiskModalTitle: '加入新風險',
	manageRisksListModalTitle: '管理風險',
	riskInfoMessage: '儲存後將套用變更。',
	risksListInstructionalText: '您可編輯並刪除現存任務。您亦可加入新增（如需要）。',
	risksListEmptyArray: '沒有可用的風險。新增新風險以開始',
	addNewRiskButtonLabel: '加入新風險',
	labelRiskName: '風險名稱',
	riskDescriptionLabel: '風險說明',
	selectRiskType: '風險類型',
	requiredRiskName: '風險名稱為必須',
	requiredRiskType: '風險類型為必須',
	deleteRiskTrashLabel: '刪除風險',
	undoDeleteRiskTrashLabel: '復原刪除',
	notARommLabel: '無重大不實表達風險',
	identifiedRiskFactors: '已辨識的錯誤事件/條件/風險、重大不實表達風險、重大風險和舞弊風險。',
	noneIdentified: '未辨認',
	countUnassociatedRisk: '事件/條件/不實表達風險不相關/未標記為「非重大不實表達風險/重大不實表達風險」。',

	// Bar Chart / Account Summary
	accountsTotal: '{0} accounts total',
	accountSummary: 'Account summary',
	allAccounts: '全部科目',
	significantAccountsBarChart: '顯著科目',
	limitedAccounts: '有限風險科目',
	insignificantAccounts: '不重大科目',
	noAccountsHasBeenIdentifiedBarChart: '未辨認出{0}。',
	selectedTotalAccountsCounter: '{0}/{1} accounts',
	identifyInsignificantAccounts: 'Identify insignificant accounts',
	identifySignificantAccounts: 'Identify significant accounts',
	identifyLimitedAccounts: 'Identify limited risk accounts',
	preInsigniAccounts: 'Previously insignificant account that is no longer less than TE in the current period.',
	nonDesignatedInsignificant: 'This account cannot be designated as Insignificant. Click the designation drop down to change the designation for this account.',
	tolerableError: 'Tolerable error is not available. Update materiality and try again.',
	documentContainerLabel: 'Document',
	clickcreateformtogenerate: 'Click {0} to generate Limited risk account documentation.',
	createform: 'Create form',
	createLimitedRiskAccountDocumentation: '建立有限風險科目文件',
	limitedAccountDocumentName: '有限風險科目文件',

	//Modal Confirm Switch account
	modalSwitchTitle: '未儲存之變更',
	modalConfirmSwitch: '確認',
	modalConfirmSwitchDescription: '該等變更未被儲存，若您決定繼續將會遺失。請問您要繼續嗎?',

	//Modal Edit Account
	manageAccountsModalTitle: '管理 EY Canvas 科目',
	editAccountLinkLabel: '編輯科目',
	editAccountInstructionalText: '您可以編輯或刪除已存在的EY Canvas科目或建立新科目。一旦儲存，變更將被套用。',
	selectAnAccountLabel: '選擇科目',
	accountNameLabel: '科目名稱',
	accountLabel: '科目',
	accountDesignationLabel: 'Account designation',
	accountStatementTypeLabel: '報表類型',
	accountRelatedAssertionsLabel: '相關聲明',
	accountHasEstimateLabel: '科目是否受到估計影響',
	requiredAccountName: '科目名稱為必須',
	requiredAccountDesignation: 'Account designation is required',
	requiredStatementType: '報表類型為必須',
	requiredRelatedAssertions: '選擇聲明',
	pspIndexDropdownLabel: '選擇PSP index',
	removePSPIndexLabel: '移除PSP index',
	addPSPIndexLink: '加入PSP index',
	pspIsRequired: 'PSP index為必須',

	//Delete account modal
	deleteAccount: '刪除科目',
	deleteAccountModalMessage: '請確認是否要刪除所選科目？',
	cannotBeUndone: '此無法復原。',
	guidedWorkflow: 'EY Canvas FIT enablement',
	scotSummary: 'SCOT summary',
	scopeAndStrategy: 'Scope & strategy',
	ToggleSwitch: {
		Inquire: 'Inquire',
		Completed: 'Completed',
		isOn: 'Yes',
		isOff: 'No'
	},
	navExecution: 'Execution',
	navCanvasEconomics: 'EY Canvas Economics',
	navOversight: 'EY Canvas Oversight',
	navConclusion: 'Conclusion',
	navTeamMemberIndependence: 'Team member independence',
	navGroupAudit: 'Group management',
	navGroupActivityFeed: 'Group activity feed',
	navPrimaryStatus: 'Primary status',
	navComponentStatus: 'Component status',
	navGroupStatus: 'Group status',
	navEngagementManagement: 'Engagement management',
	navProfile: 'Profile',
	navItSummary: 'IT summary',
	nav440GL: 'Changes after report date',
	navGroupStructureSummary: 'Group structure',
	navGroupInstructionSummary: 'Group instructions',
	navGroupInvolvement: 'Group involvement',
	navNotApplicable: 'Not applicable',
	cropScreenshot: 'Crop screenshot',
	cropScreenshotModalDescription: '擷取螢幕截圖以僅包含攸關部分。擷取將移除現有的註釋，標記將被保留並可以再次註記。 擷取無法撤消。',
	crop: 'Crop',
	replace: '取代螢幕截圖',
	nameTheScreenshot: 'Screenshot name',
	nameLabel: 'Name',
	takeScreenshot: 'Add screenshot',
	measurementBasis: 'Measurement basis',
	MeasurementBasisMessage: '基於基礎的 EY Helix 資料配對，所選的計量基礎似乎不在預期位置（例如，在借方位置的稅前收入）。 考慮是否：',
	measurementBasisProjectMappedCorrectly: 'EY Helix 項目中的資料已正確配對，',
	measurementBasisAppropriateValue: '不同的計量基礎可能是合適的，或者',
	measurementBasisAdjustValue: '可能需要調整如下所提供的計量基礎值',
	basisValueFromHelix: 'Trial balance value',
	rationaleDeterminationLabel: 'Rationale for how this amount was determined',
	loginInstructions: 'log in and follow the instructions to set up your account.',
	gotoText: 'Go to,',
	asOfDate: 'As of date',
	annualizedBasisValue: 'Annualized basis value',
	basisValue: 'Basis value',
	isAnnualizedAmountRepresentative: 'Is the annualized basis value representative of the amount expected to be reported as of the period end?',
	isAnnualizedAmountRepresentativeForAssetsOrEquity: 'Is the value representative of the amount expected to be reported at the audit period end?',

	enterExpectedFinancialPerioadAmount: 'Enter expected amount at audit period end',
	enterRationaleAmountDetermined: 'Enter rationale for how this amount was determined',
	printNotAvailable: '{0}沒有內容，因此不顯示任何信息',
	canvasFormPrintNotAvailable: 'Canvas Form Print目前不可使用。 若錯誤仍然存在，請聯絡Help Desk。',
	saving: 'Saving...',
	removing: 'Removing..',
	activitySummaryTitle: 'Activity summary',
	currentLabel: 'Current',
	PMLabel: 'PM',
	planningMaterialityLabel: 'Planning materiality',
	TELabel: 'TE',
	tolerableErrorLabel: 'Tolerable error',
	SADLabel: 'SAD',
	SADNominalAmountLabel: 'SAD nominal amount',
	PriorLabel: 'Prior',
	editRichText: 'Edit text',
	noTypeTwoResponseAvailable: 'No response available. <br /> Click {clickHere} to respond.',
	clickHere: 'here',
	helixNavigationTitle: 'Go to EY Helix Configuration page to link or configure an EY Helix project',
	helixNavigationLink: 'Go to EY Helix',
	measurementBasisValue: 'Measurement basis value',
	inlineSaveUnsavedChanges: 'There are unsaved changes, do you want to continue?',
	rationaleIncomplete: 'Rationale incomplete',
	projectMismatchDisplayMessageOnDataImport: '您的主要 EY Helix 項目已變更。 請確認 EY Helix 設定並從新項目匯入資料。',
	importSuccess: 'EY Helix 資料已成功匯入。',
	importHelix: 'Click Import to import general ledger data from EY Helix.',
	importLabel: 'Import',
	reImportLabel: 'Re-import',
	lastImportedBy: 'Last imported by',
	lastImportedOn: 'Last imported on',
	dataImportedFromProjec: 'Data imported from project',
	reImportConfirmationTitle: 'Re-importing data from EY Helix',
	reImportConfirmationMessagePart1: '從 EY Helix 重新匯入資料將變更現有資料或在相關活動中新增資料，且此操作無法復原。',
	reImportConfirmationMessagePart2: '有關重新匯入資料流程如何影響 EY Canvas FIT enablement的詳細資訊和彙總，請參閱 EY Atlas。',
	defineRisksTitle: 'Define risks',
	assessAndDefineRisksTitle: 'Assess and define risks',
	identifiedRiskFactorsTitle: '所辨認之事件/情況以及相關風險：',
	descriptionIncompleteLabel: 'Description incomplete',
	noRiskHasBeenRelatedMsg: 'No risk has been related',
	rationaleIncompleteMsg: 'Rationale incomplete',
	loading: 'Loading...',
	importInProgressLabel: '匯入正在進行中。 這可能需要幾分鐘的時間。 請重新整理頁面以查看更新狀態。',
	importInProgressMessage: '正在進行 Helix 資料匯入。重新整理頁面即可查看匯入狀態。',
	importHelixProjectError: '從 EY Helix 匯入資料時發生錯誤。 請檢查 EY Helix 項目的狀態是否顯示為 Analytics available，請重新整理頁面，然後再次點選「匯入」或「重新匯入」。若錯誤仍持續，請聯絡Help Desk。',
	importDeletionConfirmationMsg: 'Are you sure you want to delete the import of EY Helix data? Delete import will delete existing data within relevant activities and this cannot be undone.',
	deletePreviousImport: 'Delete EY Helix import',
	//Assess Risks - Summary Page
	assessRisksAccordionLabel: 'Associate Risks',
	assessRisksNoItemsFound: 'No risks have been identified',
	assessRiskAccountsAndRelatedAssertions: 'Accounts and related assertions',
	assessRiskNoAccountsLinked: 'No account associated',
	accountRiskAssessmentSummary: '科目和揭露',
	// Flow chart
	flowchartTitle: 'Flowchart',
	launchFlowchart: 'Launch Flowchart',
	clickherelink: 'Click here',
	orderToCashPlacement: 'Order to cash placement',
	orderToCashPlacementMessage: 'the Program Dept. negotiates agrements with new customers and negotiates renewing contracts and/or modifying contracts with exting customers. A contract holds details such as: pricing, terms and warranties',
	saveFlowChart: 'Save',
	newstep: 'New step',
	zoomIn: 'Zoom in',
	zoomOut: 'Zoom out',
	resetZoom: 'Reset zoom',
	toogleInteractivity: 'Toogle interactivity',
	fitView: 'Fit view',
	numberOfSteps: 'Number of steps',
	flowchartSuccessfullyCreated: 'Flowchart successfully created.',
	flowchartLinkedEvidenceMessage: 'This flowchart has been created in another engagement. Access to the flowchart in this engagement will be removed when the evidence is unlinke.',
	flowchartSmartEvidenceSourceIdNullMessage: 'No SCOT available.',
	noTaskDocumentAvailableFlowchart: '此檔案是暫存檔案。請將任務連結為存取流程圖詳細資訊的證據',
	// Control Attributes
	controlAttributes: 'Control attributes',
	noControlAttributes: 'No control available',
	flowchartStepMoremenu: 'More menu',
	createControlAttributes: 'No control attributes available.<br />Click {clickHere} to create a new control attribute.',
	createNewControlAttribute: 'New attribute',
	editControlAttribute: 'Edit attribute',
	createControlAtttributeInstructions: "Enter attribute details below and select <b>\'Save and close\'</b> to finish. To create another attribute, select <b>\'Save and create another\'.</b> Attributes will be sorted based on attribute index.",
	editControlAttributeInstructions: "Edit the attribute details below and select <b>\'Save\'</b> to finish.  Attributes will be sorted based on attribute index.",
	editAttributeButtonLabel: 'Edit attribute',
	deleteAttributeButtonLabel: 'Delete attribute',
	deleteControlAttributeInstructions: 'Are you sure that you want to delete the selected attribute? This action cannot be undone.',
	// Control Attributes Form
	requiredAttributeIndexLabel: 'Attribute index (required)',
	requiredAttributeDescriptionLabel: 'Attribute description (required)',
	errorMessageAttributeIndexRequired: 'Required',
	errorMessageAttributeDescriptionRequired: 'Required',
	errorMessageAttributeDescriptionMaxLength: 'Response contains {#} characters which exceeds the maximum of {##} characters.  Adjust the description by reducing text or formatting and try again.  If the issue persists, contact the Help Desk.',
	errorMessageAttributeTestingTypesRequired: 'Required',
	proceduresLabel: 'Procedures to be performed',
	modalRequiredProceduresLabel: 'Procedures to be performed (required)',
	attributeTestingTypesLabel: {
		inquiry: 'Inquiry',
		observation: 'Observation',
		inspection: 'Inspection',
		reperformance: 'Reperformance/recalculation'
	},

	/*CRA Badge*/
	ir: 'IR',
	cr: 'CR',
	cra: 'CRA',
	incompleteCra: '不完整的CRA',
	incomplete: '未完成',

	//Progess bar labels
	savingProgress: 'Saving...',
	discardChangesLabel: 'Discard changes',
	removeFromBody: 'Remove from body',
	uploading: 'Uploading...',
	uploadComplete: 'Upload Complete',
	downloadComplete: 'Download Complete',
	processing: 'Processing...',

	/* ISA BODIES */
	/* Common */
	deleteEntityConfirmation: 'Are you sure you want to delete <b>{0}</b>? This action cannot be undone.',
	/* ITAPP-SCOT */
	searchScot: 'Search SCOT',
	addITApp: 'Add IT application',
	relatedItApp: '攸關 IT 應用系統',
	itApplicationHeader: 'IT applications',
	scotNoDataPlaceholder: 'No information available',
	noScotsOrControlsPlaceholder: 'No related SCOTs or controls; {noScotsOrControlsPlaceholderEditAssoc}.',
	noScotsOrControlsPlaceholderEditAssoc: 'edit associations',
	noScotsOrControlsPlaceholderTarget: 'No related SCOTs or Controls.',
	scotHeader: 'SCOTs',
	controlsHeader: 'Controls',
	controlsApplicationHeader: 'Application',
	controlsITDMHeader: 'ITDM',
	itAppScotNoDataPlaceholderLabel: 'No IT applications have been added.',
	itAppScotNoDataPlaceholder: 'No IT applications have been added.<br /> To start, click on {itAppScotNoDataPlaceholderAddItApp}',
	itAppScotNoDataPlaceholderAddItApp: 'Add IT application',
	editItAppOption: 'Edit IT application',
	removeItAppOption: 'Delete IT application',
	viewItAppOption: 'View IT application',
	editScotsISA: 'Edit SCOT',
	viewScotISA: 'View SCOT',
	viewControlISA: 'View controls',
	scotAndRelatedControls: 'SCOTs and controls',
	otherControls: 'Other controls',
	controlTypeLabel: 'Control type',

	/*SCOT-ITAPP*/
	addOrRelateItAppPlaceholder: '{identifyRelatedItApps} or document that the {documentScotHasNoItApps}.',
	identifyRelatedItApps: 'Identify related IT applications',
	documentScotHasNoItApps: 'SCOT has no related IT applications',
	correctScotDocumentationPlaceholder: 'The SCOT has been designated as not being supported by an IT application. Application and/or a ITDM control(s) have been associated to this SCOT, {correctScotDocumentation}.',
	correctScotDocumentation: 'revisit the designation that no IT applications support this SCOT',
	controlsWithoutRelatedItApps: 'Controls without related IT applications',
	controlsWithRelatedItApps: 'Controls with related IT applications',

	/*AddEditITApplication */
	saveAndCreateNewButtonTitle: 'Save & create new',
	instructionalMessage: 'Add IT application and select the related SCOTs. Associate controls related to the IT application, when applicable.',
	ITApplicationNamePlaceholder: 'IT application name (required)',
	scotDropdownPlaceholderText: 'Select SCOTs to be related to the IT application',
	selectScotPlaceholderText: 'Select SCOTs to be related to the IT application',
	selectControlPlaceholderText: 'Select Controls to be related to the IT application',
	noRelatedScotsPlaceholderText: 'No related SCOTs',
	noRelatedControlsPlaceholderText: 'No related Controls',
	CreateSOModelTitle: 'Add service organization',
	CreateSOInstructionalMessage: "Enter the new service organization details below and select'<b>{0}</b>' to finish. To create another service organization, select'<b>{1}</b>'.`, //'Create a new service organization and associate SCOTs and Controls related to it. ",
	saveAndCloseLabel: 'Save and close',
	saveAndCreateLabel: 'Save and create another',
	SONamePlaceholder: 'Service organization name (required)',
	SOSelectScotPlaceholderText: 'Select SCOTs related to the service organization',
	SOSelectControlPlaceholderText: 'Select Controls related to the service organization',
	CreatedSOLabel: 'SO added',
	createdITAppLabel: 'IT application added',
	searchNoResultFoundText: 'No result found',
	searchNoResultsFoundText: 'No results found',
	iTApplicationNameRequired: 'IT application name is required',
	soNameRequired: 'Service organization name is required',
	editITAppDesctiptionLabel: 'Edit the IT application and associated SCOTs and Controls',
	editSODescriptionLabel: "Edit the service organization details below and select <b>'Save'</b> to finish. ",
	viewITApplication: 'View IT application',
	itApplicationName: 'IT application name',
	serviceOrganizationName: 'Service organization name',
	newItApplication: 'New IT application',

	/*Add/Edit ITProcess*/
	itProcessName: 'IT process name',
	addItProcessDescription: 'Create an IT process',
	addItProcess: 'Add IT process',
	itProcessNameRequired: 'IT process name is required',
	editItProcess: 'Edit IT process',
	editItProcessDescription: 'Edit the IT process',
	viewItProcess: 'View IT process',
	taskTitle: 'Understand and document the IT process: {0}',
	taskDescription: 'Document our understanding of the IT process. Attach the relevant form as evidence to support our understanding of the IT process.<br />When ITGCs appear in the Task Attributes section, perform walkthrough procedures to confirm our understanding of the ITGCs and evaluate their design and implementation. Attach evidence of our procedures.',
	newItProcess: 'New IT process',
	noITProcessesFound: '沒有辨識出IT流程',

	/* IT Process - Task */
	itProcessSearchPlaceholder: 'Search IT process',
	itProcessHeader: 'IT process',
	itProcessTasksHeader: 'Tasks',
	itProcessAddUPD: 'Add UDP',
	itProcessEditItProcess: 'Edit IT process',
	itProcessRelateUDP: 'Relate UDP',
	itProcessViewProcess: 'View IT process',
	itProcessNoDataPlaceholder: 'No IT Processes have been added.<br /> To start, click on {addItProcess}.',
	itProcessSourceInstructionalText: '至少應有一個UDP連結至IT流程。{itProcessSourceInstructionalTextCreateUdp} or {itProcessSourceInstructionalTextRelateUdp}',
	itProcessSourceInstructionalTextCreateUdp: 'Create a new UDP',
	itProcessSourceInstructionalTextRelateUdp: 'relate an existing UDP.',
	itProcessTargetInstructionalText: 'No UDP are related to the IT process.',

	/* IT APP IT PROCESSES RELATION*/
	itApplicationHeaderRelate: 'IT applications',
	itProcessesHeaderRelate: 'IT processes',
	itAppNoDataPlaceHolderLabel: 'No IT applications have been identified.',
	itAppNoDataPlaceHolder: 'No IT applications have been identified.<br />{identifyItApp}',
	identifyItApp: 'Identify an IT application.',
	itProcessNoDataPlaceHolderRelationLabel: 'No IT processes have been identified.',
	itProcessNoDataPlaceHolderRelation: 'No IT processes have been identified. <br /> {identifyItProcess}',
	identifyItProcess: 'Identify an IT process.',
	editItApp: 'Edit IT application',
	deleteItApp: 'Delete IT application',
	drag: 'Drag IT process to the related IT applications',
	// editItProcess: 'Edit IT process',
	deleteItProcess: 'Delete IT process',
	unassociatedProcess: '{0} unrelated processes',
	unassociatedItApplications: '{0} unrelated IT applications',
	showOnlyUnrelated: 'Show only unrelated - {0}',
	searchItProcess: 'Search IT process',
	searchItApplication: 'Search IT application',
	itProcessesLabel: 'IT processes',
	itApplicationsLabel: 'IT applications',
	showAllItAplications: 'Show all IT applications',
	showAllItProcesses: 'Show all IT processes',
	relatedToLabel: "List of all <span class='child-entity-count'>{count}</span> <span class='child-entity-name'>{child}</span> related to <span class='parent-entity-object-name'>{parent}</span> ",

	/* IT Process > IT Risk */
	itProcessItRiskNoDataPlaceholder: 'No IT process has been identified.<br/>{identifyItProcess}',
	itProcessItRiskNoDataPlaceholderTarget: 'No IT processes have been identified.',
	itApplication: 'IT Apps',
	itGC: 'ITGCs',
	addItRiskBtnTitle: 'Add IT risk',
	itProcessItRiskUnrelatedITGC: 'unassociated ITGCs',
	itProcessItRiskUnrelatedITGCUppercase: 'Unassociated ITGCs',
	itProcessItRiskNoRisksNoControlsPlaceholder: 'There is no requirement to include the IT risks or ITGCs for this IT process because there are no application or ITDM controls with an effective design evaluation related to the IT applications associated to this IT process.',
	itProcessItRiskNoRisksControlsPlaceholder: 'Based on the identified application and ITDM controls {itRiskIdentify} for this IT process',
	itRiskIdentify: 'IT risks should be identified',
	itProcessItRiskItProcessContentTitle: 'IT risks and ITGCs',
	itProcessItRiskItRiskNoItgcRequiredPlaceholder: 'There are no ITGCs that address the IT risk.',
	itProcessItRiskItRiskItgcRequiredPlaceholder: 'All identified risks must have at least one ITGC identified or a designation that the IT risk has no ITGCs.<br/>Identify a {newITGC} or {existingITGC} that addresses the IT risk or indicate that there are {noItRisksIdentified} that address the IT risk.',
	noItRisksIdentified: 'no ITGCs',
	newITGC: 'new ITGC',
	existingITGC: 'existing ITGC',
	unrelatedItGCModalMessage: 'Delete unassociated ITGCs that are no longer required.',
	unrelatedItGCModalNoDataPlaceholder: 'No unassociated ITGC',
	removeItRisk: 'Remove IT risk',
	deleteItRiskConfirmation: 'Are you sure you want to remove the IT risk <b>{0}</b>? This action will delete the IT risk and cannot be undone.',
	relateItGcTitle: 'Relate ITGC',
	relateItGcEntityTitle: 'IT risk',
	relateItGcDescription: 'Select the ITGCs that are relevant for the IT risk.',
	relateItGcSearchPlaceholder: 'Search ITGC',
	relateItGcShowSelectedOnlyText: 'Show only related ITGCs',
	relateItGcNoDataPlaceholder: 'No ITGCs available. Create a new ITGC to continue.',
	relateITSPTitle: 'Relate ITSP',
	relateITSPDescription: 'Select the ITSPs that are relevant to the IT risk.',
	relateITSPSearchPlaceholder: 'Search by ITSP name',
	relateITSPShowSelectedOnlyText: 'Show only related ITSPs',
	relateITSPNoDataPlaceholder: 'No ITSP available. Create a new ITSP to continue.',

	/* IT Process Task Relationship */
	relateUDP: 'Relate UDP',
	relateUDPDescription: 'Select the UDP tasks that are relevant for the IT process.',
	relateUDPListHeaderItemName: 'Task name',
	relateUDPSearchPlaceholder: 'Search by task name',
	relateUDPNoResultsFoundPlaceholder: 'No results found',
	relateUDPCountLabel: '{0} tasks',
	relateUDPClose: 'Close',
	relateUDPShowOnlyRelatedTasks: 'Show only related tasks',
	relateUDPNoDataFoundPlaceholder: 'No tasks available',
	relateUDPNoDataPlaceHolder: 'No IT process have been identified',

	/* ITGC test strategy */
	itProcessItRiskItGcWithoutDesignEffectiveness: 'ITGCs without design effectiveness',
	searchItGC: 'Search IT process',
	itGCNoDataPlaceHolder: 'No IT process have been identified',
	addItRisks: 'Add IT risks',
	itDMHeader: 'ITDM',
	itAppHeader: 'IT app',
	itTestHeader: 'Test',
	itTestingHeader: 'Testing',
	itgcHeader: 'ITGCs',
	controlsSelectedHeader: 'Controls selected for testing',
	iTRisksAndITGCs: 'IT risks and ITGCs',
	NoITGCForITRiskPlaceholder: 'IT環境未存在ITGC可因應此IT風險。',
	ITGCsNotIdentifiedRiskNoITGCs: 'ITGC未被辨認至此風險。 {identifyAnITGC} or designate that the {itRiskHasNoITGCs}.',
	identifyAnITGC: 'Identify an ITGC',
	itRiskHasNoITGCs: 'IT risk has no ITGCs',

	/**
	 * IT SO > SCOT
	 */
	searchItSO: 'Search service organization',
	addItSOBtnTitle: 'Add service organization',
	itSoNoDataPlaceHolder: 'No service organizations have been identified.<br/><a>{identifyAnSo}<a/>',
	noItSoDataPlaceHolder: 'No Service organizations have been identified.',
	identifyAnSo: 'Identify a Service Organization',
	soHeader: 'Service Organization',
	editSO: 'Edit service organization',
	deleteSO: 'Delete service organization',
	viewSO: 'View Service organization',
	controlRelatedToSO: 'Controls related to SO',

	/**
	 * Manage IT SP
	 */
	addITSP: 'Add ITSP',
	searchPlaceholderManageITSP: 'Search IT process',
	noManageITSPDataPlaceholder: 'No IT process have been identified',
	itRiskColumnHeader: 'IT risks',
	itDesignEffectivenessHeader: 'Design effectiveness',
	itTestingColumnHeader: 'Testing',
	itGCColumnHeader: 'ITGCs',
	itSPColumnHeader: 'ITSPs',
	searchClearButtonTitle: 'Clear',
	itProcessItRiskUnrelatedITSP: ' unassociated ITSPs',
	manageITSPUnrelatedITSPUppercase: 'Unassociated ITSPs',
	unrelatedITSPModalMessage: 'Delete unassociated ITSPs that are no longer required.',
	unrelatedITSPModalNoDataPlaceholder: 'No unassociated ITSP',
	noITGCPlaceholderMessageFragment1: 'All identified risks must have at least one ITGC identified or a designation that the IT risk has no ITGCs: ',
	noITGCPlaceholderMessageFragment2: 'Identify a',
	noITGCPlaceholderMessageFragment3: 'new ITGC',
	noITGCPlaceholderMessageFragment4: 'or',
	noITGCPlaceholderMessageFragment5: 'existing ITGC',
	noITGCPlaceholderMessageFragment6: 'that addresses the IT risk or indicate that there are',
	noITGCPlaceholderMessageFragment7: 'no ITGCs',
	noITGCPlaceholderMessageFragment8: 'that address the IT risk',
	addNewITSP: 'Add new ITSP',
	addExistingITSP: 'Add existing ITSP',
	noITSPPlaceholderMessageFragment1: 'If we have evaluated ITGCs as ineffective or determined there are no ITGCs that exist to address the IT risk, we may be able to perform IT-substantive testing procedures (ITSPs) to obtain reasonable assurance that the IT risk within the IT process associated with the ineffective ITGC was not exploited.',
	noITSPPlaceholderMessageFragment2: 'Identify a new ITSP',
	noITSPPlaceholderMessageFragment3: 'or',
	noITSPPlaceholderMessageFragment4: 'relate an existing ITSP',
	noITSPPlaceholderMessageFragment5: '.',
	noITGCsExitForITRisk: 'No ITGCs exist for the IT risk.',
	noITSPExitForITRisk: 'No ITSP have been identified for the IT risk.',
	manageITSPItemExpansionMessage: 'IT risks',
	noITGCExists: 'There are no ITGCs that address the IT risk.',
	iTGCName: 'ITGC name',
	itSPName: 'ITSP name',
	operationEffectiveness: 'Operation effectiveness',
	savingLabel: 'Saving',
	deletingLabel: 'Deleting',
	removingLabel: 'Removing',
	itFlowModalDescription: 'Go to the {itSummaryLink} to edit/remove these objects that are no longer applicable for the engagement.',
	itSummaryLink: 'IT Summary screen',
	manageITSPYes: 'Yes',
	manageITSPNo: 'No',

	understandITProcess: 'Understand IT processes',
	activity: '活動',
	unsavedPageChangesMessage: '若您選擇繼續，您未儲存之變更將會遺失。您確定要離開此頁面嗎?',
	unsavedChangesTitle: '未儲存之變更',
	unsavedChangesLeave: '離開此頁面',
	unsavedChangesStay: '停留於此頁',

	notificationDownErrorMessage: '通知功能暫時不可使用。請重新整理頁面並重試。若錯誤仍然存在，請聯絡Help Desk。',
	notificationUpbutSomeLoadingErrorMessage: '發生技術錯誤，導致通知功能無法正常運作。請重新整理頁面並重試。',
	markCompleteError: '提交的所有文件必須至少由一個編製者及一個複核者簽核。',
	markCompleteDescription: '所有文件必須至少由一個編製者及一個複核者簽核以將該活動標示為完成',
	lessthan: '少於',
	openingFitGuidedWorkflowFormError: '無法打開 EY Canvas FIT enablement表單',
	timeTrackerErrorFallBackMessage: '時間追蹤功能暫時不可使用。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	timeTrackerLoadingFallbackMessage: '時間追蹤功能暫時不可使用。 它將很快可被使用。',
	priorPeriodRelateDocument: '連結前期證據',
	selectedValue: '選擇值',
	serviceGateway: 'Service Gateway',
	docNameRequired: '名稱不能為空',
	docInvalidCharacters: '名稱不能包括：*/:<>\\?|"',
	invalidComment: '無法新增註解。 如果選擇列表中的多個項目，請只選擇一個項目，然後重試。 若錯誤仍持續，請重新整理頁面並重試或請聯絡Help Desk。',
	inputInvaildCharacters: '輸入不能包含以下字串：*/:<>\\?|"',

	// FIT Navigation panel
	relatedActivities: 'Related activities',
	backToRelatedActivities: 'Back to related activities',
	backToMainActivities: 'Back to main activities',
	formOptions: 'Form options',

	// FIT Sharing
	shareActivity: '分享活動',
	shareLabel: '共享',
	shareInProgress: '分享進行中',
	manageSharing: '共享此活動需要擁有"管理 EY Canvas FIT enablement共享"使用者權限。請前往"管理團隊"頁面管理權限，或聯繫團隊的其他成員進行操作。',
	dropdownPlaceholderSA: '選擇這個案件要分享給',
	fitSharingModalInfo: 'Share this activity with one or more activities from the same engagement or from another engagement in the same workspace. If the activities selected are not already shared, then the responses in the activities selected below will be overwritten. If the activity is shared, then only one can be selected and the responses in this activity will be overwritten.',
	lastModifiedDate: '最後修改時間：',
	noActivityToShare: '沒有可分享的活動',
	activityNotShared: '{0} 未共享',
	activityShareSuccessfull: '{0} 共享成功',
	sharedWithAnotherFITActivity: '此活動與其他活動共享',
	sharedActivityWithAnotherCanvas: '與另一個 EY Canvas FIT enablement共享活動',
	shareActivityModalTitle: '分享活動',
	showRelationshipsTitle: '顯示關係',
	shareActivityEngagement: '案件',
	shareActivityRelationshipsModalTitle: '共享活動關係',
	shareActivityWorkspaceHeading: '此活動正在與同一工作區下的案件及其相關活動共享。',
	shareModalOkTitle: '分享',
	shareModalContinueLabel: 'Continue',
	selectedActivityInfoLabel: '所選案件的最後修改時間為：',
	noSharedActivityInfoLabel: '此案件沒有其他相同類型的文件可供共享。',
	alreadyHasSharedActivityInfoLabel: 'The selected activity is already shared with other activities. Sharing the current activity will sync the responses from the selected activity to the current activity.',
	selectActivityResponsesForSharingLabel: '選擇應以哪個文件的回應取代另一個：',
	selectActivityResponsesForCurrentRadioLabel: '共享目前文件對以上所選文件的回覆',
	selectActivityResponsesForSelectedRadioLabel: 'Share responses from the above selected document to the current document',
	selectActivityResponsesWarningEarlierTimeLabel: "The current activity was modified at an earlier time compared to the selected engagement's activity. Please consider this before confirming the sharing option's below the table.",
	selectActivityResponsesWarningModifiedMoreRecentlyLabel: '與所選文件的活動相比，目前活動是最近修改的。 在確認以上共享選項前，請考量這一點。',
	selectActivityUnsuccessfulMessage: '分享失敗。請重試。若錯誤仍持續，請聯絡EY Help Desk。',
	otherEngagemntDropdownlabel: '工作區的其他案件：',
	documentSearchPlaceholder: '搜尋文件',
	showOnlySelected: 'Show only selected',

	//FIT Copy
	copyLabel: 'Copy',
	copyActivity: 'Copy activity',
	copyInProgress: 'Copy in progress',
	fitCopyModalInfo: 'Copy responses from this activity to one or more activities from the same engagement or from another engagement in the same workspace.',
	dropdownPlaceholderCA: 'Select the engagement to copy to',
	noCopyActivityInfoLabel: 'This engagement does not have document of this same type to copy to.',
	copyActivityHoverLabel: 'This activity is already shared with other activities and cannot be copied into',
	copyActivityWarningEarlierTimeLabel: "The current activity was modified at an earlier time compared to the selected engagement's activity. Please consider this before confirming the copy options.",

	//Unlink
	unlinkModalTitle: '取消連結活動',
	unlinkModalDescription: '您確定要取消所選擇的活動的連結嗎？',
	unlinkLabel: '取消連結',
	insufficientPermissionsLabel: '權限不足',
	unlinkFailMessage: '取消連結失敗。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	unlinkSuccessfulMessage: '取消連結成功',
	unlinkInProgressLabel: '取消連結正在進行中',
	unlinkError: 'Unlink error',
	unlinkInProgressInfo: 'Unlink in progress. This may take up to fifteen minutes. After the unlink is complete, this form will need to be closed and opened again.',

	/** Manage scot modal labels */
	scotName: 'SCOT名稱',
	scotCategory: 'SCOT類別',
	estimate: '估計',
	noScotsAvailablePlaceHolder: '沒有可用的 SCOT。 新增新的 SCOT 開始',
	addScotDisableTitle: '填寫 SCOT 的所有詳細信息以新增新的 SCOT',
	deleteScotTrashLabel: '刪除 scot',
	undoDeleteScotTrashLabel: '撤銷刪除',
	scotNameValidationMessage: 'SCOT 名稱是必須的',
	scotCategoryValidationMessage: 'SCOT 類別是必須的',
	scotWTTaskDescription: '<p>對於所有例行和非例行的 SCOTs 以及重要的揭露流程，我們在每個期間都通過執行穿透測試程序來確認我們的理解。 此外，對於 PCAOB 審計，我們執行估計 SCOTs 的穿透測試程序。<br/>對於我們採取仰賴控制策略時的所有 SCOTs，以及針對重大風險的控制，我們確認已適當設計和實施相關控制。 我們確認我們採取仰賴控制策略的決定仍然是適當的。<br/><br/>我們的結論是我們的文件確實描述了 SCOT 的操作，並且我們已經辨識了所有適當的 WCGWs，包括使用 IT 產生的風險 ，以及相關控制（如適用）。<br/><br/>對於我們使用僅證實性策略時的估計 SCOTs，我們根據我們的證實性程序辨識我們對估計 SCOT 的理解是否適當。</p>',

	relate: '相關',
	unrelate: '取消相關',
	related: '有關的',
	relatedSCOTs: '相關 SCOTs',
	thereAreNoSCOTsIdentified: '沒有發現 SCOTs',
	selectSCOTsToBeRelated: '選擇相關的 SCOTs',

	//OAR Tables
	OARBalanceSheet: '資產負債表',
	OARIncomeStatement: '損益表',
	OARCurrentPeriod: 'Analysis date',
	OARAmountChangeFrom: '變更自',
	OARPercentageChangeFrom: '百分比變化自',
	OARNoDataAvailable: '沒有可用資料。 查看 {0} 頁面並匯入資料以進一步繼續。',
	OARAnnotationLabel: '點擊可查看非預期變更或未達到預期變更的原因',
	OARAnnotationSelectedIcon: '記錄意外更改或缺乏預期更改的原因',
	OARAnnotationModalTitle: '註解',
	OARAnnotationModalPlaceholder: '記錄出現異常、非預期或缺乏預期變化的項目。',
	OARWithAnnotationLabel: '非預期變更的文件',
	OARAnnotation: '註解',
	OARAccTypeWithAnnotationCountLabel: '科目類型中的 {0} 個註解',
	OARSubAccTypeWithAnnotationCountLabel: '科目子類型中的 {0} 個註解',
	OARColumnA: 'A',
	OARColumnB: 'B',
	OARColumnC: 'C',
	OARComparative1Period: 'Comparative date 1',
	OARComparative2Period: 'Comparative date 2',
	OARExpand: '展開科目類別',
	OARCollapse: '收合科目類別',
	OARHelixNavigationLink: '請前往 EY Helix 以瞭解更多資訊',
	OARPrintNoDataAvailable: '無可用資料',
	OARAdjustedBalance: '調整後餘額',
	OARLegendLabel: '帶*的值表示包含調整。前往“調整”模組以瞭解更多詳細資訊。',
	OARAccountType: '科目類型',
	astrixLabel: '*',

	//OAR Helix integration
	helixIntegrationModalDescription: '這是一個待定義的本文',
	OSJETabText: 'Other side of the journal entry',
	activityAnalysisTabText: '活動分析',
	preparerAnalysisTabText: '編製者分析',
	accountMetricsTabText: '科目指標',
	noAnalyticsData: '沒有可顯示的分析',

	printActivitiesTitle: '列印活動',
	printActivitiesModalInfo: '請選擇您想要包含的活動。',
	printActivitiesModalConfirmButton: '編譯PDF',
	printActivitiesDropdownLabel: 'FIT 活動',
	printActivitiesAll: '全部',
	oarSetupText: '前往 {0} 頁面以連結或配置 EY Helix 項目',
	helixNotAvailable: 'EY Helix 不適用於您的案件。',
	dragDropUploadPlaceholder: '拖放一個或多個文件或點選<span>{addDocument}</span>',

	noTaskAssociatedToastMessage: '由於 Canvas 表單位於暫存檔案中，因此新增的文件也已新增至暫存檔案中',

	// chart labels.
	assets: '資產',
	liabilities: '負債',
	equity: '權益',
	revenues: '收入',
	expenses: '費用',
	noAccountsAvailable: '無可用科目',

	// ALRA
	ALRAFilterByAccount: '按科目篩選',
	ALRANoRecords: 'No results found',
	ALRAAssertions: '聲明',
	ALRAInherent: '固有風險因子',
	ALRAHigher: '較高風險因子',
	ALRAAccountDisclosure: '科目/揭露',
	ALRAType: '類型',
	ALRAName: '名稱',
	ALRARisks: '風險',
	ALRAC: 'C',
	ALRAEO: 'E/O',
	ALRAMV: 'M/V',
	ALRARO: 'R&O',
	ALRAPD: 'P&D',
	ALRAR: 'R',
	ALRANoRisksAssociated: '沒有與此科目相關的風險',
	ALRAAccountsDisclosureName: '科目/揭露名稱',
	ALRAHigherRisk: 'Higher risk',
	ALRAHigherInherentRisk: '較高的固有風險',
	ALRAHigherRiskCode: 'H',
	ALRALowerRisk: 'Lower risk',
	ALRALowerInherentRisk: '較低的固有風險',
	ALRALowerRiskCode: 'L',
	ALRALimitedRiskAccount: '該科目已被辨認為有限風險',
	ALRAInsignificantRiskAccount: '該科目已被辨認為不重大',
	ALRADesignations: '名稱',
	ALRABalances: '餘額',
	ALRADesignation: '名稱',
	ALRAAnalysisPeriod: 'Analysis date',
	ALRAxTE: 'xTE',
	ALRAPercentageChangeFrom: '% change from',
	ALRAPriorPeriodDesignation: 'Prior period designation',
	ALRAPriorPeriodEstimate: 'Prior period estimate',
	ALRAComparativePeriod1: 'Comparative date 1',
	ALRAComparativePeriod2: 'Comparative date 2',
	ALRASelectUpToThreeOptions: '最多選擇 3 個',
	ALRASelectUpToTwoOptions: 'Select up to 2 options',
	ALRAValidations: '驗證',
	ALRANoSignOffs: '沒有簽核',
	ALRAIncompleteInherentRisk: '不完整的固有風險',
	ALRARelatedDocuments: '相關文件',
	ALRAGreaterExtent: '較大程度',
	ALRALesserExtent: '較小程度',
	ALRARiskRelatedToAssertion: 'Risk associated',
	ALRAContributesToHigherInherentRisk: 'Risk associated and contributes to the higher inherent risk',

	// Assess inherent risk
	HigherRiskAssertionWithoutRisksThatContributesToTheHigherInherentRisk: "Assertion is identified as higher inherent risk without at least one risk that contributes to the higher inherent risk. Associate risk(s) and identify which risk(s) contribute to the assertion's higher inherent risk.",

	//MEST - Multi-entity account Execution Type selection listing
	account: '科目',
	taskByEntity: '按個體的任務',
	bodyInformation: '您必須點選下方的匯入內容才能儲存所有變更。',

	/*user search component*/
	seachInputRequired: '需要搜尋輸入',
	nameOrEmail: '姓名或電子郵件',
	emailForExternal: '電子郵件',
	noRecord: '未找到結果',
	userSearchPlaceholder: '輸入姓名或電子郵件，然後按 Enter 鍵查看結果。',
	userSearchPlaceholderForExternal: '輸入電子郵件，然後按 Enter 鍵查看結果。',
	clearAllValues: '清除所有值',
	inValidEmail: '請輸入有效的電子郵件',

	//reactive frame
	maxTabsLocked: '已達到最大允許的標籤頁數量。若要開新標籤，請先將其中一個標籤取消固定並關閉。',
	openInNewTab: '在新分頁中開啟',
	unPinTab: 'Unpin tab',
	pinTab: 'Pin tab',
	closeDrawer: '關閉側邊欄',
	minimize: 'Minimize',

	accountHeader: '科目',
	sCOTSummaryAccountNoDataLabel: '每個重大交易流程必須至少與一個重大科目或揭露相連結。請選擇一項現有重大科目或揭露連結到此重大交易流程',
	sCOTSummaryNoDataLabel: '尚未建立重大交易流程',
	scotSearchNoResultsFound: 'No results found',
	scotSummary225TabsName: {
		[0]: {
			label: '按科目顯示'
		},
		[1]: {
			label: '按重大交易流程顯示'
		}
	},

	// Display Account Balances
	currentPeriodAccountBalance: '本期科目餘額：',
	priorPeriodAccountBalance: '前期科目餘額：',

	ALRANoResults: '未找到結果。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	associatedRomsCount: '相關風險總數：{0}',
	alraMessage: "Account designation response not aligned to designation in'Edit account & disclosure'",
	estimateCategoryResponseNotAlignedToDesignation: "Estimate category response not aligned to designation in'Edit estimate' ",


	// Analytics Overview
	analyticsOverviewTitle: 'Analytics Overview',
	noSignificantAccountRecords: '尚未建立重要科目。',
	noSignificantAccountMapped: '沒有對應到所選個體的重大科目。',
	noLimitedAccountMapped: '沒有對應到所選個體的有限風險科目。',
	openAnalyticDocumentation: '開啟分析檔案',
	openLimitedRiskAccountDocumentation: '開啟有限風險科目檔案',
	associatedSCOTs: '關聯 SCOTS：',
	analysisPeriodLabel: 'Analysis date {0}',
	analysisPeriodChangeLabel: '與 {0} 相比的變化百分比',
	xTELabel: 'xTE',
	risksLabel: '風險',
	comparativePeriod1: 'Comparative date 1 {0}',
	analysisPeriodTitle: 'Analysis date',
	analysisPeriodChangeTitle: '% 變化自',
	comparativePeriodTitle: 'Comparative date 1',
	noAccountAvailable: '沒有可用的科目',

	// Estimates
	titleEstimateCategory: 'Estimate category',
	titleRisks: 'Risks',

	voiceNoteNotAvailable: '語音註記和螢幕錄製在側邊欄視窗中不可使用。 請切換至全螢幕畫面以使用這些功能。',

	financialStatementType: {
		[1]: {
			label: '資產'
		},
		[2]: {
			label: '流動資產'
		},
		[3]: {
			label: '非流動資產'
		},
		[4]: {
			label: '負債'
		},
		[5]: {
			label: '流動負債'
		},
		[6]: {
			label: '非流動負債'
		},
		[7]: {
			label: '權益'
		},
		[8]: {
			label: '收入'
		},
		[9]: {
			label: '費用'
		},
		[10]: {
			label: '營業外收入/（支出）'
		},
		[11]: {
			label: '其他綜合利益 (OCI)'
		},
		[12]: {
			label: '其他'
		},
		[13]: {
			label: '科目類型'
		},
		[14]: {
			label: '科目子類型'
		},
		[15]: {
			label: 'Account Class'
		},
		[16]: {
			label: 'Account Sub-Class'
		},
		[17]: {
			label: 'Net (Income) / Expense'
		}
	},
	accountTypes: {
		[1]: {
			label: '重大科目'
		},
		[2]: {
			label: '有限風險科目'
		},
		[3]: {
			label: '不重大科目'
		},
		[4]: {
			label: '其他科目'
		},
		[5]: {
			label: '重大揭露'
		}
	},
	noClientDataAvailable: '沒有可用資料',

	analysisPeriod: 'Analysis date',
	comparativePeriod: 'Comparative date',
	perchangeLabel: '% change',

	entityCreateAccountLabel: '建立科目並揭露',
	insignificantAccount: '不重大',
	noAccountRecords: '尚未辨識任何科目',
	noAccountsForEntity: 'No accounts or disclosures are mapped to the selected entity.',
	noLimitedRiskAccountRecords: '沒有可用的有限風險科目。',
	createAccount: '建立科目',
	createDocument: '建立文件',
	noAccountResults: '未辨識任何科目。',
	createGroupInvolvementDocument: '建立參與表單',
	chooseVersionsToCompare: '選擇要比較的版本',
	noTrackChangesOption: '沒有可用的追蹤修訂版本',
	trackChangesDefaultMessage: '從“選擇要比較的版本”下拉清單中選擇一個版本以繼續。',
	whichRiskContributeToHigherRisk: '哪些風險會導致較高的風險聲明？',

	//multi-entity Entity List
	createMultiEntity: '新個體',
	editMultiEntity: '編輯個體',
	noEntitiesAvailableCreateNewLink: 'Click here',
	noEntitiesAvailable: 'No entities have been created. {noEntitiesAvailableCreateNewLink} to get started',
	noEntitiesFound: 'No results found',
	createMultiEntityProfile: '建立個體基本資料',

	createEntity: '建立個體',
	includeEntities: '多個體清單中必須至少包含一個個體。｛createEntity｝以開始。',
	//multi-entity table
	multiEntityCode: '個體標準索引',
	multiEntityName: '個體名稱',
	multiEntityGroup: '個體群組',
	multiEntityActions: 'Actions',
	relateMultiEntityUngrouped: '未分群組',
	selectAll: '全選',
	entitiesSelected: '選定的個體',
	entitySelected: '選定的個體',
	meNoEntitiesAvailable: '無可用個體',
	meSwitchEntities: '切換個體',
	meSelectEntity: '選擇個體',
	allEntities: 'All entities',
	noEntitiesIdentified: '未辨識個體',
	contentDeliveryInProcessMessage: 'Content delivery in progress.  It may take up to ten minutes for the content to be delivered.',
	importContent: '匯入內容',
	profileSubmit: '提交基本資料',
	importPSPs: 'Import PSPs',
	contentUpdateInsufficienRolesLabel: 'Insufficient roles to update content. Work with an engagement administrator to get sufficient rights.',
	// MEST Switcher
	meEntitySwitcher: '個體切換器',
	//Error Boundary
	errorBoundaryMessage: '發生錯誤。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	sectionName: '欄位名稱',
	maxLength: '文字不能超過 {number} 個字元。',
	required: 'Required',
	yearAgo: '年前',
	yearsAgo: '年前',
	monthAgo: '個月前',
	monthsAgo: '個月前',
	weekAgo: '週前',
	weeksAgo: '週前',
	daysAgo: '天前',
	dayAgo: '天前',
	today: '今天',
	todayLowercase: '今天',
	yesterday: '昨天',
	approaching: '處理中',

	associatedToInherentRiskFactor: '與固有風險因子關聯',

	createMissingDocument: '建立缺少的文件',
	createMissingDocumentBody: '點選「確認」為目前缺少文件的相關項目建立文件。',
	documentCreationSuccessMsg: '文件建立正在進行中。 請重新整理頁面以更新。',

	noRisksRelatedToAssertion: '沒有與此科目聲明相關的風險。',

	noAssertionsRelatedToAccount: '沒有與此科目相關的聲明',

	sharing: '分享',

	risksUnrelatedToAccountAssertion: '風險沒有相關的科目聲明',
	cantCompleteTask: '與相關任務關聯的文件缺少簽核。 請開啟任務，完成缺少的文件，然後重新嘗試標記為完成。',
	cantCompleteTasksTitle: '無法標記為完成',
	ok: 'OK',
	documentsEngagementShareLabel: 'Select the document within the engagement to share with',
	documentsEngagementCopyLabel: 'Select the document within the engagement to copy',
	lastModifiedon: 'Last modified on',
	newAccountAndDisclosure: 'New account & disclosure',
	newAccountORDisclosure: 'New account or disclosure',

	externalDocuments: '外部文件',
	noExternalDocumentsAvailable: 'No external documents available',
	addExternalDocuments: 'Add external documents',
	relateExternalDocuments: '關聯外部文件',

	helixNotMappedToAccount: 'EY Helix data is not mapped to this account. Please update mapping and re-import data to proceed.',
	trackChangesNotAvailableForSpecialBodyDisplayMessage: 'Tracked changes functionality is not available for the response(s) below.',
	noDocumentRelatedObjectsApplicable: 'Objects are not required to be associated to this guided workflow form',
	helixViewerLoader: 'Loading Helix Viewer...',
	trackChangesViewDefaultMessage: 'Certain responses do not have track change functionality. This will be indicated with the following message in the underlying activity details: "Tracked changes functionality is not available for the response(s) below."  Accordingly, lack of notification of changes below is not indicative of whether changes have been made.',

	//Relate task modal
	relateTasksTitle: 'Relate tasks',
	taskLocationLabel: 'Task location',
	relateTaskInstructionalText: 'Add or remove tasks that the document should be related to. If the document is evidence, removing it from the last task will move it to temporary files.',
	noResultFound: 'No results found.',
	relatedTaskCounter: '{0} task',
	relatedTasksCounter: '{0} tasks',
	onlyShowRelatedTasks: 'Only show related tasks',
	relateTaskName: 'Task name',
	relateTaskType: 'Type',

	/*Relate Entities*/
	relateEntitiesTitle: 'Relate entities',
	relateEntitiesSearchPlaceholder: 'Enter to search by name',
	relateEntitiesName: 'Entity name',
	relateEntitiesIndex: 'Entity standard index',
	relatedEntitiesCounter: '{0} entities',
	relatedEntityCounter: '{0} entities',
	onlyShowRelatedEntities: 'Only show related entities',
	entity: 'Entity',

	step01: 'Step 01',
	step02: 'Step 02',
	shareActivityStep1Description: 'Select engagement and document',
	shareActivityStep2Description: 'Choose which document responses should replace the other',
	documentsShareLabel: 'Share response from the below selected document to the rest of the documents.',
	selectedActivity: 'Selected activity',
	sharedHoverLabel: 'This activity is already shared with other activites. Sharing this activity will sync the responses from this activity to all the shared activity',
	noAssertionsRelatedLabel: 'No assertions related.',

	// Bulk mark complete:
	bulkMarkCompleteInstructionalText: 'Below are all the tasks related to this form. In order to mark complete the selected tasks, all documents must be signed-off by at least one preparer and one reviewer.',
	bulkMarkCompleteEngagementColumn: 'Engagement',
	bulkMarkCompleteDocumentsMissingSignOffs: 'Missing sign-offs. Click {bulkMarkCompleteMissingSignOffsClickableText} to sign-off.',
	bulkMarkCompleteMissingSignOffsClickableText: 'here',
	bulkMarkCompleteNoAccessToEngagement: 'You do not have access to the engagement this task is in',
	bulkMarkCompleteInProgressMessage: 'Process is in progress. It may take up to ten minutes. Please refresh for updates',
	bulkMarkCompleteRelatedDocumentsModalTitle: 'Document sign-offs',
	bulkMarkCompleteFilterUnreadyTasks: 'Show only tasks with missing document sign-offs.',
	bulkMarkCompleteNotAllowedModalTitle: 'Unable to mark complete',
	bulkMarkCompleteNotAllowedModalDescription: 'You need to select at least one task to mark complete',
	bulkMarkCompleteRelatedDocumentsModalDescription: 'In order to mark complete the selected task, all documents must be signed off by at least one preparer and one reviewer.',
	bulkMarkCompleteRelatedDocumentsModalRefreshSignoffs: 'Refresh sign-offs and notes',
	selectedTaskCounter: '({0}) selected task',
	selectedTasksCounter: '({0}) selected tasks',

	// Mark complete (old):
	markCompleteNotAllowedModalDescription: 'Documents associated to the related task are missing sign-offs. Open the task, complete the missing documents, and then try Mark complete again',
	markCompleteInstructionalText: 'All documents must be signed-off by at least one preparer and one reviewer to mark the activity as complete',

	// Adobe Analytics
	aaCookieConsentTitle: 'Welcome to',
	aaCookieContentPrompt: 'Do you want to allow cookies?',
	aaCookieConsentExplanation: '<p>In addition to cookies that are strictly necessary to operate this website, we use the following types of cookies to improve your experience and our services: <strong>Functional cookies</strong> to enhance your experience (e.g. remember settings), <strong>Performance cookies</strong> to measure the website\'s performance and improve your experience, <strong>Advertising/Targeting cookies</strong>, which are set by third parties with whom we execute advertising campaigns and allow us to provide you with advertisements relevant to you.</p><p>Review our <a target="_blank" href="https://www.ey.com/en_us/cookie-policy">cookie policy</a> for more information.</p>',
	aaCookieConsentExplanationWithDoNotTrack: '<p>In addition to cookies that are strictly necessary to operate this website, we use the following types of cookies to improve your experience and our services: <strong>Functional cookies</strong> to enhance your experience (e.g. remember settings), <strong>Performance cookies</strong> to measure the website\'s performance and improve your experience, <strong>Advertising/Targeting cookies</strong>, which are set by third parties with whom we execute advertising campaigns and allow us to provide you with advertisements relevant to you.</p><p>We have detected that you have enabled the Do Not Track setting in your browser; as a result, Advertising/Targeting cookies are automatically disabled.</p><p>Review our <a target="_blank" href="https://www.ey.com/en_us/cookie-policy">cookie policy</a> for more information.</p>',
	aaCookieConsentDeclineOptionalAction: 'I decline optional cookies',
	aaCookieConsentAcceptAllAction: 'I accept all cookies',
	aaCookieConsentCustomizeAction: 'Customize cookies',
	aaCookieConsentCustomizeURL: 'https://www.ey.com/en_us/cookie-settings',

	// Cookie Settings
	cookieSettings: {
		title: 'Cookie Settings',
		explanation: 'Please provide your consent for cookie usage on ey.com and the My EY platform. Select one or more of the cookie types listed below, and then save your selection(s). Refer to the listing below for details on the types of cookies and their purpose.',
		emptyCookieListNotice: 'Cookies from this category are not used in this app',
		nameTableHeader: 'Name of cookie',
		providerTableHeader: 'Cookie provider',
		purposeTableHeader: 'Purpose of cookie',
		typeTableHeader: 'Type of cookie',
		durationTableHeader: 'Duration of cookie',
		formSubmit: 'Save my selection',
		requiredCookieListTitle: 'Required Cookies',
		functionalCookieListTitle: 'Functional Cookies',
		functionalCookieAcceptance: 'I accept the functional cookies below',
		functionalCookieExplanation: 'Functionality cookies, which allow us to enhance your experience (for example by remembering any settings you may have selected).',
		performanceCookieListTitle: 'Performance Cookies',
		performanceCookieAcceptance: 'I accept the performance cookies below',
		performanceCookieExplanation: 'Performance cookies, which help us measure the website’s performance and improve your experience. In using performance cookies we do not store any personal data, and only use the information collected through these cookies in aggregated and anonymised form.',
		advertisingCookieListTitle: 'Targeting Cookies',
		advertisingCookieAcceptance: 'I accept the advertising/targeting cookies below',
		advertisingCookieExplanation: 'Advertising/targeting cookies, which we use to track user activity and sessions so that we can deliver a more personalized service, and (in the case of advertising cookies) which are set by the third parties with whom we execute advertising campaigns and allow us to provide advertisements relevant to you.',
		doNotTrackNotice: 'We have detected that you have enabled the Do Not Track setting in your browser; as a result, advertising/targeting cookies are automatically disabled.'
	},
	accountFormsMissing: 'Account forms missing for {0} account(s)',
	createAccountForms: 'Create account form(s)',
	createAccountFormsDescription: "Click'Confirm' to create the document for the related items currently missing a document.",
	createMissingDocuments: 'Related accounts currently missing document(s)',
	accountDocumentsCreated: 'Content delivery in progress.  It may take up to ten minutes for the content to be delivered.',

	evidenceMissingPICSignoffs: 'Evidence missing PIC sign-off(s)',
	evidenceMissingEQRSignoffs: 'Evidence missing EQR sign-off(s)',
	evidenceMissingPICEQRSignoffs: 'Evidence missing PIC and/or EQR sign-off(s)',
	evidenceMissingPICSignoffRequirements: 'Evidence missing PIC sign-off requirement(s)',
	evidenceMissingEQRSignoffRequirements: 'Evidence missing EQR sign-off requirement(s)',
	evidenceMissingPICEQRSignoffRequirements: 'Evidence missing PIC and/or EQR sign-off requirement(s)',
	evidenceMissingSignoffs: 'Evidence missing sign-off(s)',

	// Bulk task relate
	bulkTaskRelateFailureMessage: 'Some of the documents selected were not able to be associated to the selected task(s).',
	/*endoflabels*/
	evidenceMissingPreparerOrReviwerSignoffs: 'Document upload - Missing Preparer or Reviewer Sign-offs',

	manageITProcess: 'Manage IT process',
	manageITRisk: '管理技術風險',
	manageITControl: 'Manage IT control',
	manageITSP: 'Manage ITSP',
	manageITApp: 'Manage IT application',
	manageSCOT: 'Manage SCOTs',
	addAresCustomDescription: 'Select the content type to be added to this guided workflow form, input details, and click save.',

	documentImportSuccess: '{0} has been successfully created. It may take up to ten minutes for the content to be delivered.',
	documentImportFailure: 'Document creation failed. Please refresh or try again after some time. If the issue persists, contact the Help Desk.',
	formNotAvailable: 'No matching Canvas form found.',
	selectTask: 'Select Task to relate to guidance',
	canvas: 'Canvas',
	selectEngagement: 'Select engagement',

	//Modal Manage sub-scope
	manageSubScopeTitle: 'Manage sub-scopes',
	manageSubScopeDescription: 'Create new sub-scopes or edit or delete existing sub-scopes below.',
	addSubScope: 'Add a sub-scope',
	subScopeName: 'Sub-scope name',
	knowledgeScope: 'Knowledge scope',
	subScopeAlreadyExist: 'Sub-scope name already exist',
	subScopes: 'Sub-scopes',
	notAvailableSubScopes: 'There are no sub-scopes available.',
	SubScopeNameValidation: 'Sub scope name length exceeds more than 255 characters.',

	//CRA Summary
	manageAccount: '管理科目',
	newAccount: '新科目',

	noRelatedObjectITProcessFlow: '無相關項目。關聯一個項目以開始。',

	//Add New Flow Chart Steps
	flowChartNewSteps: {
		newStepTitle: "New step",
		placeholderText_1: "Enter step details below and select",
		placeholderText_2: "'Save and close'",
		placeholderText_3: " to finish. To create another step, select",
		placeholderText_4: "'Save and create another'.",
		columnLabel: "Column (required)",
		counterOf: "of",
		counterChar: "characters",
		stepNameLabel: "Step name (required)",
		errorMsgStepNameRequired: "Step name is required",
		stepDescLabel: "Step description (required)",
		stepDescPlaceholder: "Enter step description",
		errorMsgStepDescRequired: "Step description is required",
		required: 'Required',
		errorMsgStepDescExceedMaxLength: "Step description exceeds the maximum allowed characters",
		buttonCancel: "Cancel",
		buttonSaveAndClose: "Save and close",
		buttonSaveAndCreateAnother: "Save and create another",
		errorMsgColumnRequired: "Column is required",
		headerNameForWCGW: "WCGW name",
		headerNameForControl: "Control name",
		headerNameForITApp: 'IT application name',
		headerNameForServiceOrganisation: 'Service organization name',
		relateLabelForWCGW: "Relate WCGWs",
		relateLabelForControl: "Relate controls",
		relateLabelForITApp: 'Relate IT applications',
		relateLabelForServiceOrganisation: 'Relate service organizations',
		designEffectiveness: "Design effectiveness",
		testing: "Testing",
		lowerRisk: "Lower risk",
		wcgwNoRowsMessage: 'No WCGWs have been related. Click {0} to get started',
		controlNoRowsMessage: 'No controls have been related. Click {0} to get started',
		itAppNoRowsMessage: 'No IT applications have been related. Click {0} to get started',
		serviceOrganisationNoRowsMessage: 'No service organizations have been related. Click {0} to get started',
		wgcwTabLabel: "WCGWs",
		controlsTabLabel: "Controls",
		itAppsTabLabel: 'IT applications',
		serviceOrganisationTabLabel: 'Service organizations',
		connectionSuccessMessage: "Connection created successfully.",
		connectionFailedMessage: "Couldnt establish a connection. Please try again.",
		selfConnectFailMessage: "Source and Target cannot be the same.",
		connectionDuplicateMessage: "Connection already exists.",
		connectionDeleteSuccessMessage: "Connection deleted successfully.",
		connectionDeleteFailMessage: "Couldnt delete the connection. Please try again.",
		editStepFailMessage: "Couldn't edit the step. Please try again.",
		flowchartStepGetByIdFailMessage: 'Invalid step, please refresh and try again.',
		flowchartStepGetByIdFailureMessage: 'This flowchart step is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',
		newStepFailureMessage: "Couldn't create new step. Please try again. ",
		deleteConnector: 'Delete connector',
		edgeConnectorOptions: 'Connector options',
		edgeStartPoint: 'Start Point',
		edgeEndPoint: 'End Point',
		relateDocumentToFlowchartStepError: '此時無法完成操作。請重新整理頁面並重試。若問題仍持續，請聯絡Help Desk。',
		relateDocumentsOrObjects: '相關檔案或項目',
		thisstep: '到這一步'
	},

	flowChartWCGW: {
		wcgwsCounter: "{0} WCGWs",
		wcgwCounter: "{0} WCGW",
		headerName: "Relate WCGWs",
		showOnlyRelatedText: 'Show only related',
		noResultsFound: 'No results found'
	},

	flowchartITAPPSO: {
		showOnlyRelatedText: 'Show only related',
		noResultsFound: 'No results found'
	},

	flowChartITApplication: {
		itApplicationsCounter: '{0} IT applications',
		itApplicationCounter: '{0} IT application',
		headerName: 'Relate IT applications',
		columnName: 'IT application name',
		noDataFound: 'No IT applications found'
	},

	flowChartITSO: {
		itSOsCounter: '{0} Service organizations',
		itSOCounter: '{0} Service organization',
		headerName: 'Relate service organizations',
		columnName: 'Service organization name',
		noDataFound: 'No service organizations found'
	},

	flowChartControl: {
		controlsCounter: "{0} Controls",
		headerName: "Relate controls",
		showOnlyRelatedText: 'Show only related',
		noResultsFound: 'No results found',
		noWCGWs: 'No controls have been created'
	},

	relateSCOT: {
		header: "Relate SCOTs",
		estimate: "Estimate",
		scotsCounter: "{0} SCOTs",
		scotCounter: "{0} SCOT",
		headerName: "SCOT name",
		showOnlyRelated: 'Show only related',
		noResultsFound: 'No results found',
		noScotCreated: 'No SCOTs created in the engagement'
	},

	relatedStepObjects: {
		relatedWCGWs: 'Related WCGWs',
		relatedControls: 'Related controls',
		relatedDocuments: 'Related evidence',
		relatedITApplications: 'Related IT applications',
		relatedSOs: 'Related service organizations'
	},

	flowchartEditSteps: {
		nextStep: "Next step",
		previousStep: "Previous step",
		editStepTitle: 'Edit step',
		editPlaceholderText_1: 'Edit steps details and related objects below. Click',
		editPlaceholderText_2: "'Save and close' ",
		editPlaceholderText_3: "to save and return to the flowchart. Navigating to other steps using the options below will save your updates.",
		draftEditStepFailMessage: 'Unable to create flowchart step. Refresh the page and try again. Contact the Help Desk if the error persists.',
	},

	flowChartStepmoreMenu: {
		edit: "Edit",
		delete: 'Delete'
	},

	relateEstimate: {
		scot: "SCOTs",
		strategy: "SCOT strategy",
		type: "Type",
		noSCOT: 'Each estimate must be related to at least one SCOT. Click',
		noSCOTmsg: ' to get started',
		estimate: "Estimate",
		routine: "Routine",
		nonRoutine: "Non Routine",
		notSelected: "Not selected",
		relateSCOTs: "Relate SCOTs",
		remove: '移除',
		noEstimate: 'No estimate available'
	},

	flowChartStepIcons: {
		wcgws: 'WCGWs',
		controls: 'Controls',
		iTApps: 'IT applications',
		serviceOrganisations: 'Service organizations'
	},

	flowChartStepIcon: {
		wcgw: 'WCGW',
		control: '控制',
		iTApp: 'IT Application',
		serviceOrganisation: 'Service Organization',
		evidence: '證據'
	},

	flowChartErrorMessage: {
		stepOutsideOfTheColumns: 'Steps cannot be placed outside of the flowchart area',
		stepBetweenTheColumns: 'Steps cannot be placed between the columns',
		stepOnTopOrTooCloseToAnotherStep: 'Steps cannot be placed on top of the other steps'
	},

	//Delete Flow Chart Steps
	flowChartStepsDelete: {
		deletestep: "Delete step",
		deleteStepModalMessage: 'Are you sure you want to delete this step? All WCGWs, controls, IT applications, service organizations and evidence related to the deleted step will be unrelated.',
		cannotBeUndone: "New or renewed customer contracts",
		deleteStepFailMessage: "Failed to delete the step. Please try again",
		deleteDraftStepErrorMessage: 'The step created in draft was not deleted. To delete this step, please select the step and action the delete again.',
	},
	notEntered: 'Not entered',
	estimateCategory: 'Estimate category',
	noResultsFoundWithPeriod: 'No results found',
	noEstimateAvailable: 'No estimate available',
	noRelatedObject: 'No related object.',
	relateAnObject: 'Relate an object',
	copyrightMessage: 'Copyright © <year> all rights reserved',
	leadsheet: 'Lead sheet',
	controlName: 'Control name',
	noControlAvailable: 'No control available',
	independenceError: 'All incomplete responses must be completed prior to submitting independence.',
	riskTypeNotAssociated: 'Newly added risk does not match the allowed risk type(s) and therefore does not appear below. Add another risk of the allowed type or select from the below list',
	accountsAndRelatedEstimates: 'Accounts and related estimates',
	noEstimatesAssociated: 'No estimates associated',
	noAssertionsAvailable: 'No assertions available',
	noAccountsOrDisclosuresAvailable: 'No accounts or disclosures available',

	relateEstimateToRisk: {
		riskType: 'Risk type',
		risk: "Risk",
		hasestimate: "Has estimate?",
		accounts: "Accounts",
		isItRelevant: "Is it relevant?",
		assertions: "Assertions",
		invalidRiskParentRiskErrMsg: 'Record not found. Please refresh the page to continue.',
		noEstimate: 'No estimate available',
		invalidRelateRiskOrEstimateRelationErrMsg: 'The object has already been related. Please refresh the page to continue.',
		invalidUnRelateRiskOrEstimateRelationErrMsg: 'The object has already been unrelated. Please refresh the page to continue.'
	},

	savingChanges: 'Saving changes',
	showEstimateAccountsWithoutEstimates: 'Show estimate accounts without estimates',
	showEstimateSCOTsWithoutEstimates: 'Show estimate SCOTs without estimates',
	manageSCOTs: 'Manage SCOTs',
	sCOTsAndRelatedEstimates: 'SCOTs and related estimates',
	relateEstimateToRiskNoDataMessage: 'No records available, please relate at least one account and assertion with a risk associated if applicable',
	maps: 'Maps',
	mapsUpbutSomeLoadingErrorMessage: '出現技術錯誤，對應功能無法作業。請更新頁面並重試。',
	mapsDownErrorMessage: '對應功能暫時無法使用。請更新頁面並重試。如果此消息仍然存在，請聯繫Help Desk。',
	financialStatements: 'Financial Statements',
	serviceGatewayAutomation: 'Service Gateway & Automation',
	priorPeriodCategory: 'Prior period category',
	relatedAccountWithColon: 'Related accounts: ',
	noRelatedAccount: 'No related accounts',
	noRetionaleAvailable: 'No rationale available',
	leftNavIconApprovals: 'Approvals',
	editDuplicateSectionHeader: 'Edit the details for the Section and click Save.',

	relatedEvidences: 'Relate evidence',
	relatedEvidencesInstruction: 'Relate an evidence from this engagement.',
	relatedTemporaryFilesInstruction: 'Relate a temporary document from this engagement.',
	noDataLabel: 'No data found',
	editDuplicateSection: 'Edit section',
	showOnlyRelated: 'Show only related',
	aiChatbot: 'EYQ Assurance Knowledge',
	StEntityNoRecords: 'No accounts or disclosures are mapped to the selected entity.',
	versionLabel: 'Version',
	relatedEstimates: 'Related estimates',
	viewEvidenceRelatedToBody: 'View evidence related to the body',
	selectHeaderFromRail: 'Select a header from left navigation pane to proceed',
	manageITProcesses: '管理IT流程',
	rationaleForLR: '有限風險科目的理由',
	rationaleForInsignificant: '不重大科目的理由',
	rationalIsMissing: '未提供理由。',
	craSummaryText1: '每個重大科目或揭露必須至少有一個相關聲明。點選',
	scotDetails223: {
		relatedAccounts: '相關科目',
		scotType: '類型',
		manageScot: '管理重大交易流程',
		editScot: '編輯重大交易流程',
		scotNotAvailableMessage: '重大交易流程不適用於此檔案',
		relatedScotNotAvailableMessage: '沒有相關的重大交易流程。相關屬性頁面的一個重大交易流程以開始',
		risksDocumented: '此穿透測試中記錄的風險',
		risksAvailableHeader: '是',
		risksNotAvailableHeader: '否',
		viewRelatedRisks: '查看相關風險',
		noRelatedAccountsMessage: '沒有相關科目'
	},

	scotDetails226: {
		noscotsidentified: 'No SCOTs have been identified'
	},

	scotDetails224: {
		riskRelatedWalkthrough: '此穿透測試中連結的風險',
		relatedToWTDocuments: 'Related to other WT documents',
		riskNotRelatedWalkthrough: '此穿透測試中未連結的風險”，',
		substantiveNotSufficient: '證實性查核不足',
		journalEntry: '會計分錄',
		noDirectRiskSourcesAreAvailable: '無連結的風險',
		scotNotAvailableMessage: '重大交易流程不適用於此檔案',
		relatedScotNotAvailableMessage: '沒有相關的重大交易流程。相關屬性頁面的一個重大交易流程以開始',
		relatedDocuments: 'Related documents',
		risk: "Risk:",
		riskSpecialCircumstances: 'Risk special circumstances',
		relateInstructionText: "This risk has been identified in another SCOT.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		unrelateInstructionText: "This risk has been identified in the critical path of another walkthrough.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		concurrencyErrorMessage: "This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.",
	},
	ipe: '個體生成的資訊（IPE）',
	scotSummary198: {
		noAccountsDisclosureCreated: '沒有建立重大科目或揭露。',
		noScotEstimateIdentified: '沒有辨識到重大交易流程或估計。',
		noScotIdentified: '沒有辨識到重大交易流程',
		scots: '重大交易流程',
		estimates: '估計',
		errorMessage: '無法完成此操作。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
		noResultsFound: '未發現結果',
		searchAccounts: '搜尋科目',
		searchScotEstimates: '搜尋重大交易流程/估計',
		accounts: '科目',
		scotsOrEstimate: '重大交易流程/估計',
		accountNotRelatedToScotValidation: '每個重大科目或揭露必須至少與一個重大交易流程相關聯。勾選方塊，將相關重大交易流程與該重大科目或重大揭露相關聯。',
		scotNotRelatedToAccountValidation: '每個重大交易流程必須至少與一個重大科目或重大揭露相關聯。勾選方塊，將此重大交易流程與相關的重大科目或重大揭露相關聯。',
		showValidations: '顯示驗證',
	},
	scotSummary225: {
		relatedScots: '已連結重大交易流程',
		relatedAcconts: '連結的科目',
		scotListHeader: '重大交易流程和估計',
		noScotsMessage: '每個重大科目或揭露必須至少與一個重大交易流程相連結。請選擇一個現有重大交易流程與此重大科目或重大揭露相連結',
		noAccountsMessage: 'No significant accounts or disclosures have been created.',
		noAccountsAvailableOnSearch: 'No results found',
		relateAccounts: 'Relate accounts and disclosures',
		noAccountsCreated: 'No accounts have been created',
		noScotsCreated: 'No SCOTs have been created',
		relateScots: 'Relate SCOTs',
	},
	bodyUnavailableInCCP: '此內容無法通過EY Canvas Client Portal登入網站取得。',
	pyBalance: '去年餘額',
	cyBalance: '今年餘額',
	designationNotDefined: '未定義指定事項',
	controlRiskAssessment: '控制風險評估',
	first: '首頁',
	noImportedTrialBalance: '沒有匯入的試算表。',
	placeHolderMessageWhenHelixMappingIsTrue: '點選{0}關聯新分析工具。',
	documentPrintSuccess: 'Document print in progress. It may take up to ten minutes. Once completed, the print will be added to the temporary files.',
	documentPrintError: '檔案列印失敗。請更新或稍後再試。如果問題仍然存在，請聯繫Help Desk。',
	backToEvidenceWarningMessage: 'This action could not be completed. Please refresh and try again. If issue persists, please contact the Help Desk.',
	rationaleMissingForLR: 'Each limited risk account shall have a rationale provided',
	rationaleMissingForIR: 'Each insignificant account shall have a rationale provided',
	craSummaryText2: ' Each account shall have designation determined. Click',
	contentDrivingEntity: 'Content driving entity',
	contentDrivingEntityPlaceholder: 'Content driving entity has not been selected',
	rationaleForPlaceholder: 'Provide rationale for this account designation',
	contentDrivingEntityRequired: 'Content driving entity (required)',
	refreshContentLayers: 'Refresh content layers',
	noAccessLabel: 'Unauthorized. Contact your administrator and try again.',
	copyForHelpDeskDetails: 'Copy for Help Desk details',
	copyForHelpDeskDetailsSuccess: 'Details copied to clipboard',

	//toast activity as guest user
	sharedGuidedworkflowEvidenceWarning: 'This is a shared Guided Workflow Activity. The objects and evidence exist in the original engagement and will not be added to this engagement upon unlink. See <a style="color: #467cbe" href="https://live.atlas.ey.com/#library/104?pref=20058/9/5" target="_blank">enablement here</a> for further details.',
	sharedGuidedworkflowResponseWarning: "This is a shared Guided Workflow Activity. The responses are being shared with other activities in the same workspace. View relationships by accessing the'Show relationships' menu item from this activity's summary section."
};

export const groupStructure = {
	createComponent: '新的組成個體',
	deleteComponent: '刪除組成個體',
	manageComponents: '管理組成個體',
	emptyComponents: 'No components have been created. Create a {newComponent} to get started.',
	scope: '範圍',
	role: '角色',
	pointOfContact: 'Point of contact',
	linkRequest: 'Link request',
	instructions: '指引',
	instructionsSent: '已發送指引',
	status: '狀態',
	createComponentInstructionalText: "Enter component details below and select <b>'Save and close'</b> to finish. To create another component, select <b>'Save and create another'</b>. ",
	componentName: '組成個體名稱',
	region: '地區',
	notUsingCanvas: '無使用 EY Canvas',
	referenceOnly: '僅供參考',
	saveAndCreateAnother: '儲存並建立另一個',
	dueDate: 'Due date',
	components: '組成個體',
	allocation: '分配',
	documents: 'Evidence',
	discussions: '討論',
	EDAP: 'EDAPs',
	siteVisits: 'Site visits',
	reviewWorkComponent: 'Review work performed',
	other: '其他',
	significantUpdates: '重大更新',
	executionComplete: '執行完成',
	gaRoleTypesLabel: [{
		id: 1,
		displayName: '主要的'
	},
	{
		id: 2,
		displayName: 'Regional'
	},
	{
		id: 3,
		displayName: '組成個體'
	}
	],
	gaLinkStatusLabel: [{
		id: GALinkStatus.NotSent,
		displayName: '已發送'
	},
	{
		id: GALinkStatus.Sent,
		displayName: '已發送/未接受'
	},
	{
		id: GALinkStatus.ComponentNotUsingCanvas,
		displayName: 'Not using EY Canvas'
	},
	{
		id: GALinkStatus.ReferenceOnly,
		displayName: '僅供參考'
	},
	{
		id: GALinkStatus.Accepted,
		displayName: '已接受'
	},
	{
		id: GALinkStatus.Rejected,
		displayName: '已拒絕'
	},
	{
		id: GALinkStatus.Unlinked,
		displayName: '未連結'
	},
	{
		id: GALinkStatus.Pending,
		displayName: '已發送/未接受'
	}
	],
	notAvailable: 'Not available',
	search: labels.searchPlaceholder,
	noResultsFound: '未找到結果。',
	noComponentsFound: '未找到任何組成個體。',
	contentSwitcher: [{
		id: gaRoleTypes.primary,
		displayName: '主要的'
	},
	{
		id: gaRoleTypes.component,
		displayName: '組成個體'
	},
	{
		id: gaRoleTypes.regional,
		displayName: 'Region'
	}
	],
	gaRegionTypesLabel: {
		id: gaRegion.notApplicable,
		displayName: '不適用'
	},
	//TODO: To be removed
	pointOfContactValues: [{
		id: pointOfContactTypes.EYcontact,
		displayName: '安永聯絡人'
	},
	{
		id: pointOfContactTypes.externalContact,
		displayName: '外部聯絡人'
	}
	],
	saveAndClose: labels.modalSaveAndClose,
	cancelBtn: labels.modalCancelTitle,
	gaScopesValues: [{
		id: gaScopeType.full,
		displayName: 'Full'
	},
	{
		id: gaScopeType.specific,
		displayName: 'Specific'
	},
	{
		id: gaScopeType.specifiedAuditProcedures,
		displayName: 'Specified procedures'
	},
	{
		id: gaScopeType.review,
		displayName: 'Review'
	}
	],
	edit: labels.edit,
	delete: labels.delete,
	tooltipIcon: '若該組成個體不使用 EY Canvas 接收集團查核指引並提交interoffice deliverables，請選擇此選項。',
	tooltipReferenceIcon: '指定為<b>僅供參考</b>的組成個體僅用於組織架構目的。 這些組成個體案件將不會接收連結請求、指引或任務，且該主查團隊案件也不會接收集團任務。',
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	modalConfirmBtnLabel: labels.confirmLabel,
	clear: '清除',
	clearUpper: labels.clear,
	nameOrEmail: '輸入安永聯絡人電子郵件',
	editComponent: '編輯組成個體',
	editComponentInstructionalText: "編輯下方組成個體詳細資訊，然後選擇<b>\'儲存\'</b> 完成。 ",
	linkAlreadyAcceptedInfo: 'Only the email field can be edited as the link request has already been sent to the Component team.',
	sendAll: '全部發送',
	send: '發送',
	resend: '重新發送',
	scopeAndStrategy: 'Scope & strategy',
	execution: '執行',
	conclusion: '結論',
	reportingForms: 'Reporting Forms',
	manageGroupPermission: '您沒有執行此操作的<b>管理群組</b>權限。 向案件管理員請求<b>管理群組</b>權限。',
	manageComponentModalDesc: '建立新的組成個體或編輯和刪除下方現有組成個體。',
	editLinkInfo: 'Only the email field can be edited as the link request has already been sent to the Component team.',
	invalidPointOfContact: '需要一個聯絡人才能發送連結請求。 編輯組成個體以新增聯絡人。',
	manageComponentModalActions: '行動',
	manageComponentModalComponents: '組成個體',
	manageComponentModalDelete: '刪除',
	noThereAtLeastOneComponentToSendAll: '沒有符合條件的組成個體可以發送連結請求。組成個體的狀態必須為<b>發送</b>或<b>重新發送</b>才能發送連結請求。',
	showKnowledgeDescription: '顯示 Knowledge 中的標題和描述',
	hideKnowledgeDescription: '隱藏 Knowledge 中的標題和描述',
	instructionName: '輸入指引名稱',
	instructionDescriptionPlaceholder: '輸入指引描述',
	selectDueDate: 'Due date (required)',
	show: '顯示',
	allocationHeader: '分配',
	allocationInstructionForKnowledge: 'Knowledge instructions can only be allocated by scope. Select the relevant scope(s) below.',
	allocationInstructionForCustom: 'Custom instructions can be allocated by scope or by component. Select the instruction allocation below, then allocate to the relevant scope(s) or component(s).',
	allocateScope: '分配給範圍',
	allocateComponent: '分配給組成個體',
	pillScopesPlural: '範圍',
	pillScopesSingular: '範圍',
	pillComponentsPlural: '組成個體',
	pillComponentsSingular: '組成個體',
	selectScopesPlaceholder: '選擇範圍',
	selectComponentsPlaceholder: '選擇組成個體',
	searchNoResultFoundText: labels.searchNoResultFoundText,
	newCustomInstruction: '新的自訂指引',
	instructionNameNewCustomInstruction: '指引名稱',
	addCustom: '新增自訂',
	custom: '自訂',
	required: 'Required',
	remove: '移除',
	selectAll: '全選',
	unselectAll: '取消全選',
	lowerPoC: '聯絡人',
	editPoCTooltip: 'Invalid or no point of contact. Edit point of contact in order to send a link request.',
	recomendationType: [{
		id: 1,
		label: '必須的'
	},
	{
		id: 2,
		label: '可選的'
	},
	{
		id: 3,
		label: '不適用'
	}
	],
	confirmLabel: labels.confirmLabel,
	deleteComponentInstructionalText: '<b>Are you sure that you want to delete this component from the Group structure?</b><br />When the component is deleted, the link to the component will be removed and the engagements will no longer be able to exchange documentation. Additionally, all associations between the component and its accounts and instructions will be deleted.',
	noActivitiesAvailable: '沒有可用的活動。',
	relatedComponents: '相關組成個體',
	relatedComponentsSingular: '相關組成個體',
	relatedComponentsPlural: '相關組成個體',
	publish: '發布',
	publishModalHeader: '發布變更',
	publishChangesInstructional: '<b>Are you sure that you want to publish changes to the group instruction summary?</b><br />The previous set of group instructions will be overwritten.  Once the changes are published, the updated instructions can be sent from the group instruction summary.',
	publishManageGroupPermission: '您必須擁有管理群組權限才能執行此操作。 請求案件管理員的許可。',
	lastPublished: '最後發布：',
	publishChangesNotAvailable: '尚不可使用',
	noRecordsFound: labels.noRecordsFound,
	deleteInstruction: 'Delete instruction',
	deleteInstructionInstructionalText: '<b>Are you sure that you want to delete the instruction? </b><br />This action cannot be undone.',
	sendInstructionsTitle: '發送指引',
	sendInstructionsInstructionalText: "Ensure the latest instructions have been published by clicking'Publish' on the underlying page. Then, review the component's instructions below and select'Send' to send the instructions to the component engagement. ",
	instructionsAlreadySent: '最新版本的指引已發送。',
	missingDueDates: '缺少報告截止日期。',
	createInstructionsModalButton: '建立指引',
	createInstructionsModalActionToastMessageStart: '缺乏團體風險評估說明',
	createInstructionsModalActionToastMessageEnd: '  組成個體。',
	createInstructionsModalDescription: '以下完整和特定範圍組成個體沒有被分配集團風險評估指引。 選擇<b>建立</b>將為下方列出的每個組成個體建立集團風險評估指引。',
	createInstructionsModalScope: '  範圍',
	createInstructionsModalHeader: '建立指引',
	createInstructionsModalmodalConfirmBtnLabel: '建立',
	createInstructionsModalmodalCancelBtnLabel: '取消',
	createInstructionsModalmodalCloseBtnTitletip: '關閉',
	createInstructionsModalNewGraInstructionDescription: '隨附上的是與您的組成個體相關的科目風險評估。 複核風險評估並確保您的案件已辨識出這些科目和風險。 當地發現的任何其他風險，或組成團隊不同意的任何其他風險，都應與主查團隊進行溝通，以便主查團隊和組成團隊都可以相應地調整風險評估。',
	createInstructionsModalErrorMessage: '以下組成個體的集團風險評估指引建立失敗：<b>{0}</b>。請重新整理頁面並重試。',
	createInstructionsDuplicatedModalErrorMessage: 'Group risk assessment instruction creation failed. Instruction name cannot be duplicated.',
	gaLinkActionTooltip: {
		NotUsingCanvasLabel: 'Not using EY Canvas',
		NotUsingCanvas: 'Clicking <b>Send</b> will create the Primary <br/> Group tasks for this component but <br/> no instructions will be sent.',
		NotLinkedLabel: 'Not linked',
		NotLinked: 'The link request has not been sent to <br/> the Component team. Send the link <br/> request in order to send instructions.',
		Unlinked: 'Unlinked'
	},
	viewHistory: '檢視歷史紀錄',
	viewSentInstructionsTitle: '檢視發送的指引',
	save: labels.saveLabel,
	cancel: labels.cancelLabel,
	viewHistoryInstructionalText: '選擇指引可檢視發送給組成個體團隊的指引的先前版本。',
	viewHistorySelectInstruction: '選擇指引',
	viewHistoryDateSent: '發送日期：',
	viewHistoryStatus: '狀態：',
	viewHistoryStatusAccepted: '已接受',
	viewHistoryStatusPending: '待辦的',
	viewHistoryStatusRejected: '已拒絕',
	viewHistoryStatusSystemError: 'System error',
	viewHistorySelectVersion: '選擇版本',
	noAccountsFound: 'No accounts or disclosures have been found in this or other engagements. <br />Select {link} to create new or edit existing accounts or disclosures.',
	generalCommunications: 'General communications',
	reportingDeliverables: 'Reporting deliverables',
	changesPublishedNotSent: 'Changes not sent',
	changesPublishedBrNotSent: 'Changes<br/>not sent',
	changesPublishedNotSentYes: 'Yes',
	deleteSubScopeInstructionalTextModal: 'Are you sure that you want to delete <br/> the selected sub-scope?',
	deleteSubScopeTitleModal: 'Delete sub-scope',
	riskAssessmentModal: {
		headerText: 'Risk assessment',
		modalCloseBtnTitletip: labels.close,
		manageAndDisclosures: 'Manage accounts & disclosures link',
		next: 'Next component',
		back: 'Previous component'
	},
	riskAssessment: 'Risk assessment',
	preview: 'Preview',
	accountsAndDisclosureSummary: 'Account and disclosure',
	noAccountSnapshotPlaceholder: 'There is no account data to display for this component.',
	createOversightProjectButtonLabel: 'Create EY Canvas Oversight project',
	createOversightProjectTitle: 'Do you want an EY Canvas Oversight project to be created with this primary engagement?',
	createOversightProjectDescription: 'Regional and/or component EY Canvas engagement(s) identified in this group structure will automatically be populated as part of the EY Canvas Oversight project setup.',
	createOversightModalHeader: 'EY Canvas Oversight project name',
	createOversightModalDescription: 'Enter the name of the EY Canvas Oversight project.',
	createOversightModalTextLabel: 'Project name',
	projectRedirectionButtonLabel: 'EY Canvas Oversight projects',
	projectAssociationTextLabel: 'There are EY Canvas Oversight projects connected to this engagement.',
	sendLinkDisableTooltip: 'This engagement was copied, including components in the Group audit flow. Links cannot be re-sent. Create a new component and send a link, as necessary.',
	instructionsCannotBeSentUntilPublished: 'Instructions cannot be sent until they are published.'
};

export const groupInvolvement = {
	NoComponentsAvailables: '尚未建立任何組成個體。 <b>管理組成個體</b>以開始使用。',
	GroupInvolvementToastMsgStart: '缺少集團參與表單',
	GroupInvolvementToastMsgEnd: '  組成個體）。',
	CreateGroupInvolvementHeader: '建立參與表單',
	GroupInvolvementInstructionalText: '以下組成個體沒有指派給它們的集團參與表單。<br/> 選擇「<b>建立</b>」； 將為下方列出的每個組成個體建立一個集團參與表單。',
	createGroupInvolvementDocumentErrorMessage: '以下組成個體的集團參與文件建立失敗：<b>{0}</b>。請重新整理頁面並重試。',
	createGroupInvolvementDocumentSuccessMessage: '集團參與表單已成功建立。 30 秒後重新整理頁面即可檢視可用文件。',
	involvementTypePlanned: 'Involvement type planned',
	significantUpdatesToPlannedInvolvement: 'Significant updates to planned involvement',
	executionComplete: '執行完成',
	generateGroupInvolvementCommunications: 'Print involvement form(s)',
	generateGroupInvolvementInstructionalText: 'The following component(s) have Group involvement form(s) associated to them. Select which component&#39;s group involvement forms to be included in one document below.<br /><br /> Once the components have been selected, selecting <b>&#39;Create&#39;</b> will create one Group involvement document with each component&#39;s group involvement document listed below.',
	componentTeams: 'Component teams',
	noComponentsSelectedErrorMessage: 'Select components to create a Group involvement communication.',
	documentName: '{taskName} group involvement package',
	selectAll: groupStructure.selectAll,
	unselectAll: groupStructure.unselectAll,
	modalConfirmBtnLabel: groupStructure.createInstructionsModalmodalConfirmBtnLabel,
	modalCancelBtnLabel: groupStructure.cancelBtn,
	modalCloseBtnTitletip: groupStructure.modalCloseBtnTitletip
};

export const itPlanning = {
	supportingITColumnsHeaders: {
		applicationTool: {
			name: 'Applications/Tools'
		},
		network: {
			name: '網路'
		},
		database: {
			name: '資料庫'
		},
		operatingSystem: {
			name: '作業系統'
		}
	},
	relatedITProcessesColumnsHeaders: {
		relatedITProcess: 'Related IT processes',
		category: 'Category'
	},
	itPlanningPlaceholders: {
		smartEvidenceSourceEntityId: '相關技術不可用於此檔案',
		smartEvidenceSourceId: '無相關項目。請連結一個項目以開始。',
	},
	relatedITProcessesPlaceholders: {
		smartEvidenceSourceEntityId: 'Related IT process not available for this document',
		smartEvidenceSourceId: '無相關項目。請連結一個項目以開始。',
		relatedITProcessEmpty: 'No IT process related to the technology'
	},
	noTechnologiesIdentified: '沒有辨識到技術',
	supportingITEmpty: '沒有與技術相關的支援性應用程式/工具',
	supportingITNetworkEmpty: '沒有與技術連結的支援性網路',
	searchPlaceholder: '搜尋',
	newTechnology: '新技術',
	noSupportingDatabases: '沒有與技術相關的支援性資料庫',
	createEntityFormDocument: '建立檔案',
	noSupportingOperatingSystem: 'No supporting operating systems related to the technology',
	manageTechnology: 'Manage technology'
};

export const itRiskFactors = {
	accepted: '已接受',
	rejected: '已拒絕',
	accept: '接受',
	reject: '拒絕',
	rejectionRationale: '拒收理由',
	rejectionCategory: '拒絕類別',
	rejectionRationaleRequired: 'Rejection rationale (required)',
	rejectionCategoryRequired: 'Rejection category (required)',
	riskName: 'Risk name',
	smartEvidenceValidations: {
		smartEvidenceSourceEntityId: '風險因素不可用於此檔案',
		smartEvidenceSourceId: 'No related object. Relate an object to get started.'
	},
	manageChangePlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: '已拒絕所有風險'
	},
	manageOperationsPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	},
	manageAccessPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	},
	SDLCPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: '已拒絕所有風險'
	},
	manageSecuritySettingsPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	}
};

export const rejectionTypeResource = [{
	id: rejectionType.itRiskOther,
	label: 'ITRisk Other'
},
{
	id: rejectionType.itRiskOption2,
	label: 'ITRisk - "Option 2"'
},
{
	id: rejectionType.itRiskOption3,
	label: 'ITRisk - "Option 3"'
}
];

export const sampleList = {
	newSample: 'New sample',
	createSampleModalDescription: `Enter sample details below and select'<b>{0}</b>' to finish. To create another sample, select'<b>{1}</b>'.`,
	saveAndCreateAnother: '儲存並建立另一個',
	saveAndClose: '儲存並關閉',
	sampleDescription: 'Sample description (required)',
	sampleDate: 'Sample date (required)',
	sampleListId: 'Sample List Id',
	ok: 'Ok',
	addSample: 'Add Sample',
	cancel: 'Cancel',
	saveAndCloseForHeader: 'Save and close',
	saveAndCreateAnotherHeader: 'Save and create another',
	required: 'Required',
	description: 'Sample description',
	date: 'Date',
	attributeStatus: 'Attributes',
	tags: 'Tags',
	open: '開啟',
	notApplicableLabel: 'Not applicable',
	notPresent: 'Not present',
	present: 'Present',
	pagingShowtext: '顯示',
	placeHolderMessage: 'No samples available. Click {clickHere} to get started.',
	noSampleListAvailable: 'No sample list available',
	editSample: 'Edit sample',
	editSampleDescription: `Edit sample details below and select'<b>{0}</b>' to finish.`,
	editSampleSave: 'Save',
	sampleCanNotBeCreated: 'Samples cannot be created for this document.',
	noRelatedObject: 'No related object. Relate an object to get started.',
	noResultsFound: '未找到結果'
};

export const AdditionDocumentationLabels = {
	addAdditionalDocumentation: 'Add Additional documentation',
	editAdditionalDocTitle: 'Edit additional documentation',
	removeAdditionalDocumentation: 'Remove Additional documentation',
	cancel: 'Cancel',
	save: '儲存',
	of: 'of',
	additionalDocTitlePlaceholder: 'Additional documentation (required)',
	additionalDocTitle: 'Additional documentation (required)',
	remove: '移除',
	enterAdditionalDocTitle: "Enter additional documentation below and select <b>'{0}'</b> to finish. ",
	editAdditionalDocDesc: "Edit additional documentation below and select <b>'{0}'</b> to finish. ",
	characters: '字元',
	required: 'Required',
	descriptionMaxLengthError: 'Response exceeds maximum allowed.',
	attributeIndexLabel: 'Attribute index'
};

export const sampletAttributeConstants = [{
	id: 1,
	label: 'Open'
},
{
	id: 3,
	label: 'Present'
},
{
	id: 7,
	label: 'Present with comments'
},
{
	id: 5,
	label: 'Not present'
},
{
	id: 4,
	label: 'Not applicable'
},
];

export const groupInstructions = {
	ALRAPackageModalTitle: 'ALRA package name',
	ALRAPackageModalInstructionalText: '輸入您要新增至證據的 ALRA package的名稱。',
	ALRAPackageModalNameField: '輸入名稱',
	ALRAPackageSuccessToastMessage: 'package建立過程已開始。 最多可能需要十分鐘才能完成。',
	ALRAPackageInProgressToastMessage: 'package建立過程正在進行中，可能需要十分鐘才能完成。',
	delete: labels.delete,
	deleteSectionModalTitle: labels.deleteSection,
	deleteSectionInstructionalText: '<b>Are you sure that you want to delete the section?</b><br />This action cannot be undone.',
	deleteSectionTooltipText: 'Instructions must be deleted<br />before section can be deleted.',
	modalConfirmBtnLabel: labels.confirmLabel,
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	missing: '缺失的',
	sendAllModalTriggerButton: '全部發送',
	sendAllModalTooltipText: '沒有可發送給組成個體團隊的指引。',
	publishModalTooltipText: 'Group instructions need to be published before they are sent. When instructions are published, any changes are saved as new instructions, overriding the previous version of instructions. These new instructions can then be sent to component teams.',
	sendAllModalErrorMessage: 'Group instructions for the following Components were not sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk. <br /> <b>{0}</b>',
	sendAllModalHeaderText: '發送所有指引',
	sendAllModalConfirmBtnLabel: '傳送',
	sendAllModalCancelBtnLabel: '取消',
	sendAllModalCloseBtnTitletip: '關閉',
	sendAllModalDescription: '選擇<b>發送</b>將向以下組成個體團隊發送指引。',
	generateGroupRiskAssessmentCommunications: 'Generate group ALRA',
	bulkALRAPackageName: '{instructionName}科目層級風險評估包',
	groupInstructionSummaryReport: 'Group instruction summary report',
	groupInstructionSummaryReportTitletip: 'View and export Group instruction details, instruction history and changes to Component/Account mapping.',
	exportGroupRiskAssessment: 'Export summary',
	reportingDeliverables: groupStructure.reportingDeliverables,
	groupRiskAssessment: 'Group risk assessment'
};

export const sectionTitles = [{
	id: KnowledgeSectionIds.GeneralCommunications,
	sectionTitle: groupStructure.generalCommunications
},
{
	id: KnowledgeSectionIds.ScopeOfWork,
	sectionTitle: 'Scope of work'
},
{
	id: KnowledgeSectionIds.ReportingForms,
	sectionTitle: groupStructure.reportingDeliverables
},
{
	id: KnowledgeSectionIds.ProceduresPerformedCentrally,
	sectionTitle: 'Procedures performed centrally'
},
{
	id: KnowledgeSectionIds.GroupRiskAssessment,
	sectionTitle: groupInstructions.groupRiskAssessment
},
{
	id: KnowledgeSectionIds.OtherCommunications,
	sectionTitle: 'Other communications'
}
];

export const groupAuditToolbar = {
	search: labels.placeholderForSearch
};

export const AccountType = [{
	id: 1,
	accounttypename: '重大科目'
},
{
	id: 2,
	accounttypename: '有限風險科目'
},
{
	id: 3,
	accounttypename: '不重大科目'
},
{
	id: 4,
	accounttypename: '其他科目'
},
{
	id: 5,
	accounttypename: '重大揭露'
}
];

export const PriorityType = [{
	value: 1,
	label: 'Low'
},
{
	value: 2,
	label: 'Medium'
},
{
	value: 3,
	label: 'High'
},
{
	value: 4,
	label: 'Critical'
}
];

export const AccountSummaryAccountType = [{
	id: '0',
	accounttypename: '全部科目'
},
{
	id: '1',
	accounttypename: '重大科目'
},
{
	id: '2',
	accounttypename: 'Limited risk accounts'
},
{
	id: '3',
	accounttypename: '不重大科目'
},
{
	id: '4',
	accounttypename: '科目 - 其他'
},
{
	id: '5',
	accounttypename: '重大揭露'
}
];

export const TaskStatus = [{
	id: 1,
	status: 'Open'
},
{
	id: 2,
	status: 'In progress'
},
{
	id: 3,
	status: 'In review'
},
{
	id: 4,
	status: 'Complete'
},
{
	id: 5,
	status: 'Removed'
}
];

export const reviewNoteLabels = {
	/*Review Notes*/
	emptyNoteDetailsMessage: '請選擇註記以檢視詳細內容。為啟動批次控制，請使用control或shift鍵並選擇多個複核註記。若您欲處理單一註記，則請從清單選擇該註記。',
	documentReviewNotesLabel: '文件註記',
	addNewReviewNoteButtonText: '加入註記',
	noNotesAssociatedWithDocumentLabel: '無註記連結至此文件',
	allNotesLabel: '全部註記',
	charactersLabel: '字元',
	myNotesLabel: '我的註記',
	showClearedLabel: '顯示已清除',
	showClosedLabel: '顯示已解決',
	toLabel: 'to',
	toUserLabel: 'To',
	ofLabel: 'of',
	textAreaPlaceholder: '輸入註記',
	addNewNoteModalClose: '關閉',
	addNewNoteModalTitleLabel: '加入新註記',
	editNoteModalTitleLabel: '編輯註記',
	deleteIconHoverText: '刪除',
	deleteIconModalAcceptText: '刪除',
	deleteIconModalConfirmMessage: '請確認是否要刪除您對此註記的回覆？',
	deleteIconModalConfirmMessageParent: '請確認是否要刪除所選註記？',
	deleteIconModalTitleLabel: '刪除註記',
	deleteReplyIconModalTitle: '刪除回覆',
	emptyRepliesMessage: '尚無回覆',
	replyInputPlaceholder: '回覆此註記',
	replyText: 'Reply text',
	editReplyModelTitle: '編輯回覆',
	noteDueDateLabel: '到期',
	fromUserLabel: '從',
	priorityLabel: '優先順序',
	dueDateLabel: '到期日',
	dueLabel: '到期',
	status: '狀態',
	noteModifiedDateLabel: '已修改：',
	cancelLabel: '取消',
	saveLabel: '儲存',
	clearedBy: '已清除，由',
	closedBy: '已解決，由',
	reopenedBy: '已重新開啟，由',
	reply: 'Reply',
	editIconHoverTextLabel: '編輯',
	required: 'Required',
	closeTitle: '關閉',
	otherEngagementNotes: '其他案件註記',
	closeLabel: '關閉',
	showMore: '顯示更多',
	showLess: '顯示較少',
	showMoreEllipsis: '顯示更多...',
	showLessEllipsis: '顯示較少...',
	noResultFound: 'No results found',
	engagementNameLabel: 'Engagement name: ',
	drag: 'Drag',
	formMaxLength: '文字不能超過 {number} 個字符。',
	voiceNoteButtonLabel: '語音註記',
	stopRecordingButtonLabel: '停止',
	reopen: '重新開啟',
	noNotesFound: '沒有找到註記',
	noNotesFoundInstructional: '使用下面的輸入留下註記。將註記指派給使用者並指定優先順序和截止日期。',
	microphoneBlockedMessage: '允許瀏覽器訪問您的麥克風以使用語音註記。若已允許，請重新整理頁面並重試。',
	microphoneBlockedOnVideoMessage: '允許瀏覽器存取您的麥克風以在螢幕錄製中使用語音。 若已允許，請刷新並重試。',
	notInMainWindowVoice: '側邊欄內不允許錄音，請在新分頁中開啟文件以執行該操作。',
	notInMainWindowScreen: '側邊欄內不允許進行螢幕錄製，請在新分頁中開啟文件以執行該操作。',
	voiceNoteNotAvailable: '語音註記和螢幕錄製在側邊欄視窗中不可使用。 請切換至全螢幕畫面以使用這些功能。',
	playButtonTitle: '開始',
	deleteButtonTitle: '刪除',
	pauseButtonTitle: '暫停',
	screenRecord: '螢幕錄製',
	playbackReview: '回放回顧'
};

export const IndividualAccountAttributeLabels = {
	attributesNotAvailableForDocument: '科目屬性不適用於此檔案。',
	noRelatedOnject: '無相關項目。相關一個項目以開始。',
	noAttributesAvailable: '沒有可用屬性',
	noRisksAvailable: '沒有可用風險',
	attributeStandardRomms: '屬性 標準重大不實表達風險',
	continueButtonTitle: '繼續',
	closeButtonTitle: '取消',
	newAssertionModalPlaceholder: '這一選擇將導致以下聲明被辨識為較低的固有風險，而這些聲明之前並未被辨識為相關聲明。是否繼續？',
	assertion: '聲明',
	inherentRiskType: '固有風險',
	assertionModalTitle: '建立聲明',
	riskType: '較低'
};

export const entities = [{
	id: 0,
	name: 'All'
},
{
	id: 1,
	name: 'Document'
},
{
	id: 2,
	name: 'LeadSchedule'
},
{
	id: 3,
	name: 'Account'
},
{
	id: 4,
	name: 'SCOT'
},
{
	id: 5,
	name: 'IT Process'
},
{
	id: 6,
	name: 'Audit Plan'
},
{
	id: 7,
	name: 'Risk'
},
{
	id: 8,
	name: 'Task'
},
{
	id: 9,
	name: 'Misstatement'
},
{
	id: 10,
	name: 'Deficiency'
},
{
	id: 11,
	name: 'GA Component'
},
{
	id: 12,
	name: 'GA Component Instruction'
},
{
	id: 13,
	name: 'GA Component Evidence'
},
{
	id: 14,
	name: 'GA Scope'
},
{
	id: 15,
	name: 'GA Primary Instruction'
},
{
	id: 16,
	name: 'GA Primary'
},
{
	id: 17,
	name: 'Client Request'
},
{
	id: 18,
	name: 'WCGW'
},
{
	id: 19,
	name: 'Control'
},
{
	id: 20,
	name: 'IT Application'
},
{
	id: 21,
	name: 'Canvas Form'
},
{
	id: 22,
	name: 'Form Section'
},
{
	id: 23,
	name: 'Form Body'
},
{
	id: 24,
	name: 'Assertion'
},
{
	id: 25,
	name: 'Client Engagement'
},
{
	id: 26,
	name: 'Client Group'
},
{
	id: 27,
	name: 'Engagement Tag'
},
{
	id: 28,
	name: 'Engagement'
},
{
	id: 29,
	name: 'Form Header'
},
{
	id: 30,
	name: 'Form Status'
},
{
	id: 31,
	name: 'Engagement User'
},
{
	id: 32,
	name: 'Client Group User'
},
{
	id: 33,
	name: 'PSP Index'
},
{
	id: 34,
	name: 'ITGC'
},
{
	id: 35,
	name: 'IT Risk'
},
{
	id: 36,
	name: 'Automation Line Item'
}
];

export const PaceType = [{
	id: 1,
	paceTypename: '低'
},
{
	id: 2,
	paceTypename: '中'
},
{
	id: 3,
	paceTypename: '高'
},
{
	id: 4,
	paceTypename: '密切監督'
}
];

export const DocumentHelper = {
	401: '無法完成操作。重新整理頁面並重試。 若錯誤仍持續，請聯絡Help Desk。',
	413: '文件超過最大允許文件大小 (250mb)，無法上傳。請減小文件大小並重試。',
	412: '此案件中已存在相同名稱的文件',
	414: '名稱超過最大長度(120字元)。',
	4099: '已經存在相同名稱檔案。',
	/*this is a hack as we dont always know why conflict happened.*/
	410: '此文件已被刪除，因此無法打開。',
	411: '不允許空白文件。'
};

export const Errors = {
	/*Doc Helper Custom Messages */
	0: '失去連線。請重新連結並重試。若問題仍然存在，請聯絡Help desk。',
	10: 'EY Canvas Document Helper已檢測到問題。請點選<a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">此處</a>取得解決問題的指引。',
	101: '無效案件狀態。',
	102: '找不到有效案件使用者。',
	103: '缺少案件使用者獨立性遵循。',
	104: '缺少所須Azure AD範圍',
	105: '取得案件權限時發生錯誤。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	106: "Unauthorized. Contact your administrator and try again.",
	107: 'Valid engagement user not found. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	108: 'Engagement profile is incomplete. Please go to Landing page and Complete Profile.',
	303: '相同名稱之文件目前已上傳。',
	403: 'Access to this document is not available.  If this document is shared ensure you have access to the source engagement.  Refresh the page and try again.  If the error persists contact the Help Desk.',
	406: '文件不能為空白。',
	412: '此案件中已存在同名文件。',
	414: '名稱超過最大長度(120字元)。',
	411: '不允許空白文件。',
	500: '失去連線。請重新連結並重試。若問題仍然存在，請聯絡Help desk。',
	600: 'Operation cannot be complete at this time. Refresh the page and try again. If the issue persists contact the Help Desk.',
	601: 'Error downloading the screenshot. Refresh the page and try again. If the issue persists, contact the Help desk.',
	602: 'Document is already in collaboration',
	935: '使用者沒有足夠的權限來執行該操作。',
	zip: '無法上傳.zip檔案，因包含未支援檔案類型，或超過嵌入.zip檔案最大數量。',
	401000: '偵測到網路變更並請重新載入頁面以繼續',

	/*Accounts*/
	1001: '建立科目失敗。',
	1002: '缺少科目名稱。',
	1003: 'The selected Account has been deleted. Close this modal to see the updated list.',
	1004: '未找到結果。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	1005: '無效的案件Id。',
	1006: '無效的科目類型。',
	1007: '無效的說明類型。',
	1008: 'The selected account has been deleted. Close this modal to see the updated list.',
	1009: '透過Id取得科目失敗。',
	1010: '透過案件Id取得科目失敗。',
	1011: '由於無效的請求取得SEM彙總科目失敗。',
	1012: '無效的彙總類型。',
	1013: '建立科目複核失敗。',
	1014: '刪除科目複核失敗。',
	1015: '無效的科目複核建立請求。',
	1016: '無效的科目複核ID。',
	1017: '科目非此案件的一部分或已被刪除。',
	1018: '科目複核由其他使用者建立。',
	1019: '科目已被其他使用者刪除。請重新整理並重試。',
	1020: '科目須更新PSP',
	1024: '科目名稱超過 500 個字符。',
	1025: '無法估計有限風險或非重大科目',
	1026: '科目不能有重複的聲明',
	1027: '聲明 ID 無效',
	1037: '不能有重複的 PSP 索引',
	1039: '所選科目已被刪除。關閉此模式以查看更新的清單。',
	1048: '科目的執行類型無效。',
	1053: 'Account has Risk or Estimate associated, can not set to Limited Risk Account or Insignificant Account.',
	1054: 'Assertions to be deleted have associated with Risk or Estimate',
	1065: 'You cannot make changes to assertion(s) that have a Significant risk, or Fraud risk, or Risk of material misstatement or an estimate related.You need to remove these relations first.',
	1070: '包含EY Helix時，試算表ID不能空白。',
	1072: '無法完成操作。請更新頁面並重試。如果問題仍然存在，請聯繫Help Desk。',

	1266: '案件的多使用者編輯模式檔案已達到最大數量。請選擇部分檔案進行重新簽入，然後重試。若問題仍持續，請聯絡Help Desk。',
	1267: 'Document is conflicted. Resolve conflicts and try again.  If the issue persists, contact the Help Desk.',
	1268: 'Document is already in co-edit mode. end co-edit mode and try again.  If the issue persists, contact the Help Desk.',
	1269: 'Document is a shared evidence. Unlink and try again.  If the issue persists, contact the Help Desk.',
	1270: '在多使用者編輯模式下，無法刪除或更新檔案版本。請結束多使用者編輯，然後重試。若問題仍持續，請聯絡Help Desk。',
	1271: '檔案未處於共同編輯模式或正在結束共同編輯模式。如果問題仍然存在，請聯繫Help Desk',

	/*Assertions*/
	2001: '無效的建立請求。',
	2002: '缺少聲明名稱。',
	2003: '缺少聲明。',
	2004: '取得聲明失敗。',
	2005: '無效的案件Id。',
	2006: '透過Id取得聲明失敗。',
	2007: '取得聲明WCGW失敗。',

	/*Risks*/
	4001: '目前無法完成動作。請重新整理頁面並重試。若問題仍然存在，請聯絡Help desk。',
	4002: '缺少風險名稱。',
	4003: '缺少風險。',
	4004: '取得風險失敗。',
	4005: '無效案件Id。',
	4006: '透過Id取得風險失敗。',
	4007: '無效的詢問請求。',
	4008: 'This estimate is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',
	4009: '無效的更新請求。',
	4010: '特定WCGW已指派給此風險',
	4011: 'WCGW清單不得為空值。',
	4012: '取得風險類型失敗。',
	4013: '無效建立請求。',
	4014: '相關聲明無效。請重新整理頁面並重試。若此錯誤仍然存在，請聯絡Help Desk。',
	4015: 'WCGW無效。請重新整理頁面並重試。若此錯誤仍然存在，請聯絡Help Desk。',
	4016: 'The selected Risk/Estimate has been deleted. Close this modal to see the updated list.',
	4017: '風險之聲明無效。請重新整理頁面並重試。若此錯誤仍然存在，請聯絡Help Desk。',
	4018: '風險類型id通過無效。',
	4019: '風險名稱無效。',
	4020: '無效的文件Id。',
	4021: '風險名稱不得超過500字元。',
	4023: '聲明Ids清單不得為空值。',
	4024: 'Limited risk documentation form could not be created due to an error. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	4025: 'Invalid account id.',
	4026: 'Invalid assertion id.',
	4027: 'AssertionRisk Model cannot be empty',
	4031: 'Invalid Risk or Form Body option.',
	4035: '無法編輯重大風險或舞弊風險的 IsHigherRisk。',
	4036: 'KnowledgeAssertionId cannot be empty if no AssertionId is passed in. KnowledgeAssertionId must be in enums',
	4037: '此科目的 KnowledgeAssertionId 已存在。',
	4038: 'RiskTypeId does not match permissible options.',
	4062: 'This SCOT is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',
	4063: 'SCOT relation cannot be edited. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	4076: 'This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	4079: 'This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*TASK*/
	5001: 'Get all tasks failed.',
	5002: '此案件中不再有此任務。',
	5003: '取得任務文件失敗。無效的請求參數。',
	5004: '取得文件相關任務失敗。無效的請求參數。',
	5005: '透過Id取得任務失敗。',
	5006: '取得任務類別失敗。',
	5007: '取得任務子類別失敗。',
	5008: '取得任務說明失敗。',
	5009: '取得任務客戶請求失敗。',
	5010: '儲存任務客戶請求失敗。',
	5011: '您要連結項目之任務已被刪除或拒絕。',
	5012: '您要連結之項目已被刪除。',
	5013: '您要連結項目的任務已被刪除或拒絕。',
	5014: '您要連結的文件已被刪除。',
	5015: '取得任務證據失敗。',
	5016: '取得WCGW任務失敗。',
	5017: '案件Id應大於零。',
	5018: 'Cannot complete association. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	5019: '任務說明為空白',
	5020: '所選任務已被刪除或拒絕。因此目前無法完成此動作。',
	5021: '缺少來源案件id。',
	5022: 'Save Annotation Failed',
	5023: '找不到案件使用者。',
	5024: '刪除任務失敗。',
	5025: '刪除任務失敗。',
	5026: '任務清單為空白',
	5027: '找不到複核。',
	5028: '檔案名稱為必須。',
	5029: '檔案副檔名為必須。',
	5030: '檔案名稱不可包括：*/:<>\\?|"',
	5031: '更新文件名稱錯誤。',
	5032: '無效的文件id。',
	5033: 'Operation type not found.',
	5034: '變更任務狀態失敗',
	5035: '您嘗試執行的動作目前無法完成。請稍後重試。若此錯誤仍然存在，請聯絡Help Desk。',
	5036: 'Body cannot be null or empty in the call.',
	5037: 'Request cannot be null in the call.',
	5038: '請輸入特殊檔案名稱以繼續。',
	5039: '檔案名稱為必須。',
	5040: '檔案名稱限制為100字元。',
	5041: '檔案名稱不可包括：*/:<>\\?|"',
	5042: '所選任務已被拒絕。',
	5043: 'The Task is a build step Task.',
	5044: '該任務為里程碑任務。',
	5045: '該任務非PSP或OSP類型。',
	5046: '超過字元限制。',
	5047: '超過字元限制。',
	5048: '所須欄位。',
	5049: '所選文件不可從此任務移除。請重新整理頁面並重試。若問題仍然存在，請聯絡Help Desk。',
	5050: '任務群組不應為零或無效',
	5051: '任務區塊id不應為零或無效',
	5052: '加入此文件至目前任務時發生錯誤。失敗。',
	5053: '目前任務更新此文件時發生錯誤。失敗。',
	5054: '目前任務加入此文件副本時發生錯誤。失敗。',
	5055: '重新命名文件時發生錯誤。失敗。',
	5056: 'Cannot edit task title for knowledge or group task',
	5057: 'Task type should be Ost Type',
	5058: 'This document can not be removed from the task because is system associated.',
	5059: 'Invalid time phase value.',
	5060: '加入證據錯誤。失敗',
	5061: '其他使用者已更新所顯示資訊。請重新整理頁面並重試。若問題仍然存在，請聯絡Help Desk。',
	5062: '所請求文件不在您案件所選EY Atlas Channel。請聯絡Help Desk以提供資訊給內容編輯者於未來加入。',
	5063: '無效的補釘作業。',
	5064: '所選任務已被刪除或拒絕。故目前無法完成此動作。',
	5065: '無法更新任務來源類型。請求無效。',
	5066: '取得指引錯誤。失敗。',
	5067: '無法更新任務性質類型。請求無效。',
	5068: '無法更新任務性質類型。請求無效。',
	5069: '刪除任務指派事項失敗。',
	5070: '所選任務已被刪除。請重試或聯絡Help Desk若問題仍然存在。',
	5071: '無法更新指派事項。請求無效。',
	5072: '找不到編製者。請求無效。',
	5073: '找不到指派事項。',
	5074: '儲存任務指派事項失敗',
	5075: '相同團隊成員僅可被指派至除「編製者」外之一項任務指派事項。請重試，若問題仍然存在，請聯絡Help Desk。',
	5076: '所選團隊成員非此案件之使用中成員。請重試，若問題仍然存在，請聯絡Help Desk。',
	5077: '所選任務已被刪除。請重試，若問題仍然存在，請聯絡Help Desk。',
	5078: '所選任務指派事項已被移除。請重試，若問題仍然存在，請聯絡Help Desk。',
	5079: '所選任務已被刪除。請重試，若問題仍然存在，請聯絡Help Desk。',
	5080: '文件版本紀錄不存在。',
	5081: '目前指派給此任務的使用者無法被重新指派為「編製者」。請重試，若問題仍然存在，請聯絡Help Desk。',
	5082: '更新失敗。案件之文件名稱應為唯一。請重新整理頁面以移除此訊息。',
	5083: '超過任務詳細內容之字元限制。',
	5084: '任務文件建立失敗。',
	5085: '任務文件刪除失敗。',
	5086: '任務移交建立失敗。',
	5087: 'Task patch call failed.',
	5088: '此任務必須包括證據以進行移交。請重試或聯絡Help Desk。',
	5089: '連結至此任務之所有證據(紙本檔案除外)必須至少有一位編製者及複核者以標示完成。請重試，若問題仍然存在，請聯絡Help Desk。',
	5091: '紙本檔案名稱已存在於案件中。',
	5092: '名稱不能包含下列任何字元: */:<>\\?|\\',
	5093: '紙本檔案名稱超過最大長度 (100 字元)。',
	5111: '所選科目、聲明或有限風險科目已刪除。請重新整理頁面並重試。若錯誤仍然存在，請聯絡 Help Desk。',
	5116: '文件類型無效。',
	5139: 'Cannot complete association. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	5131: 'Cannot complete association. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	5146: '無法將任務標記為已完成。',
	5156: '相關任務無法編輯。請重新整理頁面並重試。若問題仍持續，請聯絡Help Desk。',

	/*WCGW*/
	6001: '建立WCGW失敗。',
	6002: '缺少WCGW名稱。',
	6003: '缺少WCGW。',
	6004: '取得WCGW失敗。',
	6005: '無效的案件Id。',
	6006: '無效的聲明Id。',
	6007: 'Get WCGW by Id call failed.',
	6008: '無效的請求。',
	6009: '無效的WCGWId。',
	6010: '任務無法連結至WCGW。',
	6011: '所選WCGW已被刪除。',
	6012: '任務及WCGW未連結至相同聲明。',
	6013: '所選任務不屬於相同案件。',
	6014: '任務無法從WCGW取消連結。',
	6015: '任務無法連結至WCGW。',
	6016: '任務被拒絕且無法連結至WCGW。',
	6017: '該任務非里程碑任務且無法連結至WCGW。',
	6018: 'The task is not a build step task and cannot be associated to the WCGW.',
	6019: '該任務非PSP或OSP且無法連結至WCGW。',
	6020: '任務及WCGW連結到相同聲明且無法連結至WCGW。',

	/*Engagement*/
	7001: 'Get by ID not found.',
	7002: 'Get engagements by workspace ID call failed.',
	7003: 'GetAll engagement entities call failed.',
	7004: 'Get engagement by ID call failed.',
	7005: 'GetAll engagement users call failed.',
	7006: '姓氏不能超過250字元。',
	7007: '無效的使用者類型錯誤。',
	7008: '名字不能超過250字元。',
	7009: 'User GUI can not be null.',
	7010: '無效的使用者狀態錯誤。',
	7011: '建立案件使用者失敗。',
	7012: '無法邀請{0} {1}因其已是使用中或待進入之團隊成員。',
	7013: '縮寫不得超過3字元。',
	7014: '目前無法登入EY Canvas Landing Page。請重試且若問題仍然存在，請聯絡Help Desk。',
	7015: '無法邀請{0} {1}因以下登入群組：{2}已從案件刪除。請重新整理並重試。',
	7016: '無法邀請{0} {1}因使用者已是以下登入群組：{2}之使用中團隊成員。',
	7017: '所選群組不允許網域：{0}',
	7018: '電子郵件地址不能為空值。',
	7019: '名字不能為空值。',
	7020: '姓氏不能為空值。',
	7021: '使用者縮寫不能為空值。',
	7022: '使用者主要辦公室不能為空值。',
	7023: '使用者登入名稱不能為空值。',
	7024: '使用者EY角色不能為空值。',
	7025: '使用者案件角色不能為空值。',
	7026: '無法完成動作。請重試且若問題仍然存在，請聯絡Help Desk。',
	7027: '無效的電子郵件地址',
	7028: 'Patch Engagement User call failed.',
	7029: '案件使用者 - 無效的案件使用者狀態Id。',
	7030: '案件使用者 - 無效的案件使用者角色Id。',
	7031: '找不到案件使用者id。',
	7032: '電子郵件不能超過250字元。',
	7033: 'Requested User cannot be Null.',
	7034: 'Universal Message Processor Queue Failed.',
	7035: '縮寫不得超過3字元。',
	7036: '名字不得超過250字元。',
	7037: '姓氏不得超過250字元。',
	7038: '建立外部使用者失敗。',
	7039: '無法邀請使用者，因其已為使用中或待進入之團隊成員。請重新整理頁面並重試，若問題仍然存在，請聯絡Help Desk',
	7040: '名字不得超過250字元。',
	7041: '姓氏不得超過250字元。',
	7042: '縮寫不得超過3字元。',
	7043: 'GPN不得超過250字元。',
	7044: 'GUI不得超過250字元。',
	7045: '透過ID取得外部使用者失敗。',
	7046: '使用者不得為空值。',
	7047: '目前無法儲存變更。請重試且若問題仍然存在，請聯絡Help Desk。',
	7048: 'EY Canvas載入頁面目前無法登入且需要編輯。請重試且若問題仍然存在，請聯絡Help Desk。',
	7049: '更新案件使用者失敗。',
	7050: '無法停用成員。案件必須至少一位成員有權限管理案件。請更新您的選項並重試。若問題仍然存在，請聯絡Help Desk。',
	7051: '透過ID取得內部使用者失敗。',
	7052: '找不到使用者。',
	7053: '透過ID取得找不到。',
	7054: '透過案件ID取得快速連結失敗。',
	7055: '權限不足以加入前期成員。請告知有適當權限之案件團隊成員執行此動作。',
	7056: 'EY Canvas目前無法儲存變更。請重試且若問題仍然存在，請聯絡Help Desk。',
	7057: '權限不足以加入新成員。請告知有適當權限之案件團隊成員執行此動作。',
	7058: '使用者狀態已變更。請重新整理頁面並重試。若問題仍然存在，請聯絡Help Desk。',
	7062: '目前無法登入EY Canvas Client Portal，需要更新現有成員資訊。請重試且若問題仍然存在，請聯絡Help Desk。',
	7063: '無法停用外部成員。請重新整理頁面並重試。若問題仍然存在，請聯絡Help Desk。',
	7064: 'Invalid Patch Operation.',
	7065: '登入群組無法啟用{0} {1}。所選群組不允許網域：{2}。',
	7066: '{0}非有效使用者。',
	7067: 'Put external user call failed.',
	7068: '目前無法登入EY Canvas Client Portal，需要更新現有成員資訊。請重試且若問題仍然存在，請聯絡Help Desk。',
	7069: '所選登入群組為非使用中: {0}。請移除並重試。',
	7072: '取消連結案件流程時發生錯誤。請重新整理頁面並重試。若問題仍然存在，請聯絡Help Desk。',
	7074: '無法儲存變更。案件必須至少有一位使用中成員有權限管理案件且已解決獨立性。若問題仍然存在，請聯絡Help Desk。',
	7079: '提交獨立性失敗。',
	7080: 'Invalid User ID or Independence submission is not allowed as user ID does not belong to logged in user.',
	7081: '點選「提交」前請完成全部問題。使用「顯示未完成」選項以篩選未完成問題。若問題仍然存在，請聯絡Help Desk。',
	7082: '此請求找不到獨立性文件。',
	7083: 'SDM Summary call failed.',
	7084: 'User ID not valid or Independence Action is not allowed for the logged in user.',
	7085: 'Independence comment should be less than 4,000 characters.',
	7086: '獨立性動作失敗。',
	7087: '您的角色必須為主辦會計師；案件會計師；或執行總監以允許、拒絕或覆蓋該使用者之登入。',
	7088: '獨立性提交變更失敗，請稍後重試。',
	7098: 'Invalid Pacetype Id.',
	7099: '無獨立性表單，請重試，且若問題仍然存在，請聯絡Help Desk。',
	7154: '目前的canvas form找不到使用者',
	7155: '還原歸檔案件不允許提交基本資料',
	7156: '內容已重新整理。請稍後重試。若問題仍然存在請聯絡IT Help Desk。',
	7158: 'Document is not available. Refresh the page and try again. If the issue persists, contact the help desk',

	/*SCOT*/
	8001: '建立Scot失敗。',
	8002: '缺少Scot名稱。',
	8003: '缺少Scot。',
	8004: '取得Scot失敗。',
	8005: '無效的案件Id。',
	8006: '無效的聲明Id。',
	8007: '透過Id取得Scot失敗。',
	8008: '無效的請求。',
	8009: 'The selected SCOT has been deleted. Close this modal to see the updated list.',
	8010: 'SCOT ID cannot be null or empty.',
	8011: 'SCOT ID should be greater than zero.',
	8012: '文件ID無效。',
	8013: 'Scot更新失敗。',
	8014: 'Scot名稱不得為空值或空白。',
	8015: 'Scot名稱不得超過500字元。',
	8016: 'Scot策略類型無效。',
	8017: 'Scot類型無效。',
	8018: 'Scot IT應用程式無效。',
	8019: 'Scot ITApplication should be empty when HasNoITApplication is applied.',
	8028: 'The selected SCOT has been deleted. Close this modal to see the updated list.',

	/*User*/
	10001: '找不到登入使用者偏好。',
	10002: 'User GetAll call failed.',
	10003: 'User Presence Get call failed.',
	10005: 'Unable to retrieve user details',

	/*Risk Type*/
	11001: 'Invalid create request.',
	11002: '缺少風險類型名稱。',
	11003: '缺少風險類型。',
	11004: '取得風險類型失敗。',
	11005: '無效的案件Id。',

	/*TaskDocuments*/
	80004: '一個或多個任務無效。',
	80005: '檔案ID無效。',

	/*Edit Control*/
	83001: 'Failed to get controls.',
	83002: 'Failed to get control by id.',
	83003: 'Control Id is null or empty.',
	83004: 'Invalid control Id.',
	83005: 'Document Id is invalid.',
	83006: 'Missing control name.',
	83007: 'Invalid control name length.',
	83008: 'Invalid request.',
	83009: 'Invalid Control FrequencyId.',
	83010: 'Invalid Control TypeId.',
	83011: 'Invalid Control ITApplications.',
	83012: 'Invalid Control DesignEffectivenessTypeId.',
	83013: 'Invalid ITApplicationIds.',
	83014: 'Only SO type ITApplications are allowed, If the control type is manual prevent or manual detect type.',
	83015: 'Only ITDM may have manual IPE testing selected.',
	83016: 'Lower Risk controls are not allowed for engagements with this profile.',
	83017: 'Duplicate FilterSCOTs.',
	83018: 'Control name is more than 500 characters.',
	83019: 'Invalid WCGW Ids.',
	83020: 'Duplicate WCGW Ids are not allowed.',
	83021: 'Duplicate ITApplication Ids are not allowed.',
	83022: 'Parameter {0} is not valid.',
	83023: 'Invalid current page.',
	83024: 'Invalid page size.',
	83025: 'Search String should not be more than 100 characters.',
	83026: 'IT APP without service organisation can only be associated to control of type ITDependentManualControl or ITApplicationControl',
	83027: 'Duplicate FilterWCGWs.',
	83028: 'Invalid knowledge control Id.',

	112000: '未找到來源檔案和目標檔案。',
	112001: 'Unable to perform call because the request is null.',
	112002: 'Request body cannot be null or empty in the call.',
	112003: 'Source and target documentId should not be equal.',
	112004: 'Source documentId should not be null or empty.',
	112005: 'Target documentId should not be null or empty.',
	112006: 'Source EngagementId should not be null or empty.',
	112007: 'Target EngagementId should not be null or empty.',
	112008: 'Source document is not valid for given engagement.',
	112009: 'Target document is not valid for given engagement.',
	112010: 'Target engagementId not found.',
	112011: 'Insufficient roles to link forms for source enagagment. Work with an engagement administrator to get sufficient rights.',
	112012: 'Insufficient roles to link forms for target enagagment. Work with an engagement administrator to get sufficient rights.',
	112013: 'Invalid user for source engagement.',
	112014: 'Invalid user for target engagement.',
	112015: 'Invalid source document type for sharing.',
	112016: 'Invalid target document type for sharing.',
	112017: 'Source and target document types are not matched.',
	112018: 'Source and target knowledge formIds are not matched.',
	112019: 'Sharing link already exists for source and destination documents.',
	112020: 'Source and target engagements workspace not matched.',
	112021: 'Target document cannot be a target.',
	112022: 'The selected activity is already shared with other activities, and it cannot be further selected for sharing.',
	112023: 'Source document cannot be a target.',
	112024: 'Cannot find engagement Id of target documentId.',
	112025: 'Cannot find engagement Id of source documentId.',
	112026: 'Source document of source and target documentId should not be equal.',
	112027: '由於案件共享了 EY Canvas FIT enablement活動，因此無法繼續提交基本資料。 取消連結活動才能繼續。',
	112028: 'Source and target EngagementId should not be equal.',
	112029: 'Source or Target EngagementId and documentId should match with route EngagementId and documentId.',
	112030: 'Document is not valid for given engagement.',
	112031: 'DocumentId should not be null or empty.',
	112032: 'EngagementId should not be null or empty.',
	112033: 'Target Document IDs Should Be Unique.',
	112034: 'Target or source document(s) already shared.',
	112035: 'For existing linked response relataion document there can only be single target.',

	/*MissingDocument*/
	116001: 'Create form failed.',
	116002: '未找到對應文件類型ID的知識庫文件。',
	116004: 'Document creation failed. Please refresh or try again after some time. If the issue persists, contact the Help desk.',

	/* Annotation Errors*/
	12001: '無效的請求參數。',
	12002: 'Create Annotation call failed.',
	12003: 'Get Annotation failed.',
	12004: '無效的案件ID。',
	12005: 'IDs in collection must be greater than zero.',
	12006: 'ID collection cannot be empty.',
	12007: '無效的任務ID。',
	12008: '無效的文件ID。',
	12009: '必須為有效文件Id或任務ID。',
	12010: 'Replies require parent.',
	12011: '無效的狀態ID。',
	12012: 'Document type should be 440GL.',
	12013: 'Invalid annontation type.',
	12014: '無效的案件使用者。',
	12015: '文件已被其他使用者刪除。',
	12016: 'Update Annotation call failed.',
	12017: '您正在回覆的註記已被其他團隊成員刪除。請重新整理頁面並重試。',
	12018: 'Annotation change action type should not be Null.',
	12019: '複核註記已刪除。',
	12020: 'Invalid action to perform.',
	12021: 'User is not author of annotation.',
	12022: '缺少編輯使用者',
	12023: "Authoring user doesn't exist or belong to the engagement. ",
	12024: '必須指派使用者。',
	12025: 'Assigned user does not exist or belong to the engagement.',
	12026: '無效的註解。',
	12027: '註解不得為空白。',
	12028: '必須有到期日。',
	12029: 'Valid priority required.',
	12030: '註記狀態已變更。您無法編輯或回覆註記。請關閉並重新開啟視窗以檢視更新資料並繼續編輯。',
	12031: 'Annotation must be top level.',
	12032: 'Annotation must not be top level.',
	12033: '以下值至少一項不得為空白：優先順序類型、到期日、狀態、或所指派使用者。',
	12034: '註解超過最大長度(4,000字元)。',
	12035: '搜尋串超過最大長度(500字元)。',
	12036: '僅接受任務Id或文件Id，不允許二者。',
	12037: '無效的補釘作業。',
	12038: '您嘗試編輯之註記已不存在。請重新整理頁面並重試。',
	12039: '您嘗試編輯與其他團隊成員相同之註記。請重新整理頁面並重試。',
	12040: '取得註釋使用者失敗。',
	12041: '取得註釋使用者失敗。無效詢問值。',
	12042: '文件類型無效，無法建立註釋。',
	12043: '您嘗試加入註記之任務或文件已不存在。請重新整理頁面並重試。',
	12044: '您嘗試編輯回覆之註記已不存在。請重新整理頁面並重試。',
	12045: '找不到複核註記。',
	12046: 'The selected note has already been deleted by another user.',
	12047: '您僅可刪除註記為「未解決」狀態之回覆。請重新整理頁面並重試。',
	12048: '您嘗試變更狀態之註記已不存在。請重新整理頁面並重試。',
	12049: '您嘗試刪除回覆之註記已被其他團隊成員刪除。請重新整理頁面並重試。',
	12050: '您僅可刪除為「已解決」狀態之註記。',
	12051: 'Comment type annotations can only be created for valid Helix documents.',
	12052: 'The Comment type annotation that you are looking for is deleted.',
	12060: 'Reference number is required, must be greater than 0 and less than 1000.',
	12061: '參考編號必須為空白。',
	12062: '註解只能建立註解。',
	12066: '您正在嘗試回覆未打開的註記。',
	12067: '您正在嘗試刪除對已被其他團隊成員刪除的註解的回覆。請重新整理頁面並重試。',
	12068: '您正在嘗試新增包含無效或已不存在紀錄的註記。 請重新整理頁面並重試。',
	12069: '您正在嘗試更新包含無效或已不存在紀錄的註記。 請重新整理頁面並重試。',
	12070: '每份財務報表只能新增一條註解。 請編輯現有的。',
	12071: '刪除資訊失敗。 請重試。若錯誤仍持續，請聯絡Help Desk。',

	/*FlowchartStepControl*/
	123054: 'Control relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',
	123045: 'This control is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepWCGW*/
	123022: 'This flowchart step is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',
	123023: 'This flowchart step is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',
	123055: 'WCGW relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepITApplicationSO*/
	123056: 'IT Application / service organization relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the issue persists.',



	/*FlowchartStepDocument*/
	123048: 'Document relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',
	123033: 'This flowchart step is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',
	123002: 'This document is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*Configuration*/
	13001: '找不到配置。',
	13002: '取得配置API失敗。',

	/*Documents*/
	14001: '無法執行因請求為空值。',
	14002: '找不到文件。失敗。',
	14003: '案件Id應大於零。',
	14004: '文件Id不應為空值或空白。',
	14005: '嘗試取得相關任務發生錯誤。失敗。',
	14006: '所選文件無法刪除。請稍後重試。若問題仍然存在，請聯絡Help Desk。',
	14007: '發生非預期錯誤。',
	14008: '簽名時發生非預期錯誤。',
	14009: '加入簽名時發生錯誤。',
	14010: '核准ID應大於零。',
	14011: '刪除簽名時發生錯誤。',
	14012: '內文不得為空白。',
	14013: '所選文件無法取消連結。請稍後重試。若問題仍然存在，請聯絡Help Desk。',
	14014: '透過文件Id取得科目失敗。',
	14015: '無效的相關個體。',
	14016: '取得文件核准失敗。',
	14017: '取得全部文件發現失敗。',
	14018: '取得全部文件失敗。',
	14019: '未有簽核。',
	14020: '無效的動作值。',
	14021: '無效的發現類型。',
	14022: '文件不屬於案件。',
	14023: '文件變動類型無效。',
	14024: '取得全部文件失敗。參數無效。',
	14025: '建立文件複核時發生錯誤。API失敗。',
	14026: '刪除文件複核時發生錯誤。API失敗。',
	14027: '案件ID不應為空值或空白。',
	14028: '使用者ID無效。',
	14029: '使用者無權限執行此動作。',
	14030: '此案件找不到passed ID之文件。',
	14031: '文件複核ID無效。',
	14032: '找不到文件複核。',
	14033: '文件已核准。',
	14034: '工作區中其他案件正在取消連結或此文件已取消連結。請重新整理頁面並重試。若問題仍然存在，請聯絡Help Desk。',
	14035: '文件未與指定案件共享。',
	14036: '版本編號應大於零。',
	14037: '目前無法完成動作。請重新整理頁面並重試。若問題仍然存在，請聯絡Help Desk。',
	14038: '無法取得文件變更理由。',
	14039: '無法透過ID取得文件變更理由。',
	14040: '無法更新變更理由。',
	14041: '無效的變更理由。',
	14042: '更新變更理由失敗。',
	14043: '無法建立變更理由。',
	14044: '無法刪除變更理由。',
	14045: '無效的變更理由Id。',
	14046: '文件不存在。請重新整理頁面並重試。若問題仍然存在，請聯絡Help Desk。',
	14047: '文件已指派變更理由。',
	14048: '文件有衝突，請先解決後繼續。',
	14049: '無效的資料無法檢查是否加入文件給團隊或使用者。',
	14050: '移除參照文件發生錯誤。',
	14052: '搜尋文字超過最大允許長度(500字元)。',
	14053: '無法執行因請求為空值。',
	14054: '無效的補釘作業。',
	14055: '解決文件歷史紀錄失敗。',
	14056: '嘗試排序訊息時發生錯誤。',
	14057: '案件中已存在相同名稱之文件。',
	14058: '已找到多個具有相同編號的文件版本。請聯絡Help Desk以獲得進一步的幫助。',
	14059: '找不到所選擇的文件版本。 請重新整理頁面以查看更新的資料。 若錯誤仍持續，請聯絡Help Desk以獲得進一步的幫助。',
	14060: '該操作當時無法完成。請稍後再試。請重新整理頁面以查看更新的資料。 若錯誤仍持續，請聯絡Help Desk以獲得進一步的幫助。',
	14061: '文件未被分享，無法取消連結文件。若錯誤仍持續，請聯絡Help Desk以獲得進一步的幫助。',
	14062: '文件保密類型 ID 無效。',
	14063: '無法變更此文件類型的保密類型。',
	14064: '使用者沒有權限。',
	14065: '自定義說明無效。',
	14066: '更新範圍失敗。',
	14067: '範圍無效。',
	14068: '取得文件範圍失敗。',
	14069: '此文件已被其他團隊成員取消連結。 請重新整理頁面，若錯誤仍持續，請聯絡Help Desk。 ',
	14070: 'Invalid file type.',
	14071: 'The selected document version is no longer available. Please close and reopen this window to see the latest set of historical versions for the document.',
	14072: '來源文件 ID 不應為 null 或為空白。',
	14073: 'Canvasform id 不應為 null 或為空白。',
	14074: 'Canvasform id 不應重複。',
	14075: '無法將文件連結到canvas表單。',
	14076: '未找到與canvas表單相連結的來源文件。',
	14077: '未找到來源文件。通知失敗。',
	14078: '目前文件已與特定的canvas表單相連結。',
	14079: 'This document has been deleted and therefore it cannot be opened.',
	14080: 'The source approval user id is invalid.',
	14081: 'The source approval user id should valid GUID and must not be empty GUID.',
	14082: 'The modify user id is invalid.',
	14083: 'The modify user id should be a valid GUID and must not be empty GUID.',
	14084: 'File name cannot include: */:<>\\?|""',
	14085: 'The document name exceeded maximum length allowed.',
	14086: 'DocService failed while updating document details.',
	14087: 'The input is not valid.',
	14088: 'A input has duplicate document names.',
	14089: 'The bookmark observation is not valid.',
	14090: 'Request status has changed. Please refresh the page and try again if required. If the issue persists, contact the help desk.',
	14091: 'This request has been deleted. Refresh the page to view updated data. If the issue persists, contact the Help Desk.',
	14092: 'Document not eligible for update. Refresh the page and try again.  If the issue persists, contact the Help Desk.',

	/*SEM*/
	15001: '由於無效的請求，為科目id取得SEM彙總失敗。',
	15002: '無效的科目Id。',
	15003: '無效的案件Id。',
	15004: '無效的彙總類型。',
	15005: '找不到相關科目。請重新整理頁面並重試。若問題仍然存在，請聯絡Help Desk。',

	/*Timephase*/
	16001: '取得時段失敗。',
	16002: '案件必須大於零。',
	16003: '時段必須大於零。',
	16004: '無效的任務ID值。',
	16005: '無效的時段值。',

	/*Validations*/
	17001: '缺少建立步驟id或文件類型id。',
	17003: 'The document could not be found. Refresh the page and try again. If the issue persists, contact Help Desk.',

	/*TaskGroupSection*/
	18001: '取得所有任務群組段落失敗。',

	/*Assignments*/
	19001: '建立指派事項失敗。',
	19002: '取得指派事項失敗。',

	/*Client Request*/
	21001: '客戶請求連結失敗。',

	/*Related Components*/
	22001: '取得相關組成個體失敗。',

	/*Component Ceation*/
	22022: '組成個體名稱已存在於此案件中',
	22024: '您嘗試向其發送指引的組成個體已不可使用或已被刪除。',
	22027: '發現集團指引沒有截止日期。',
	22028: '該組成個體的範圍說明尚未發布。',
	22029: '沒有新的指引可發布給組成個體',

	22040: 'Instructions could not be sent. Check engagement type is correct.',
	22048: 'The component you are trying to update is copied from another engagement while copy engagement.',

	/*Send Instruction*/
	22049: 'Group instructions cannot be sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk.',

	/*User Presence*/
	23001: '找不到登入使用者狀態。',
	23002: '使用者狀態取得失敗。',
	23003: '使用者狀態文件驗證失敗。',
	23004: '使用者狀態刪除失敗。',
	23005: '文件未被其他使用者開啟。請重新整理頁面並重試。若問題仍然存在，請聯絡Help Desk。',

	/* Forms */
	24001: '取得表單失敗。',
	24002: '無效的案件ID。',
	24003: '文件Id不應為空值或空白。',
	24005: 'Form header not found.',
	24004: 'The document could not be found. Refresh the page and try again. If the issue persists, contact Help Desk.',
	24006: 'Section is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24007: '无效的请求参数。',
	24008: '標題ID不應為null或空白。',
	24009: '段落ID不應為null或空白。',
	24010: '表格正文回复更新操作失败。',
	24011: '對表單內文回覆更新的請求無效。',
	24012: 'Body is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24013: '無效的表單內文選項 ID。',
	24014: '無效的表單內文類型 ID。',
	24015: '給予內文類型 ID 的請求內文無效。',
	24016: '給予內文類型 ID 的不受限內容無效。',
	24017: '最大字元包括富文字格式標記。請減少長度或移除不必要的格式並重試。',
	24018: '對於給予內文類型ID回覆不被允許。',
	24019: '內文ID不應為null或空白。',
	24020: '無效的請求內文。',
	24021: '內文已刪除。',
	24022: '國家不得為空值或空白。',
	24023: '語言不得為空值或空白。',
	24024: '次服務線不得為空值或空白。',
	24025: 'GamLayers不得為空值或空白。',
	24026: '表頭建立失敗。',
	24027: '標題建立請求無效。',
	24028: '重複建立失敗。',
	24029: '無效的表單類型。',
	24030: '段落已被刪除。請重新整理頁面並重試。若錯誤仍然存在，請聯絡Help Desk。',
	24031: '內文非自訂內文。',
	24032: '表頭建立請求無效。',
	24033: '提供的文件個體ID無效。',
	24034: '提供的個體ID無效。',
	24035: '建立相關文件時出現錯誤。',
	24036: '建立相關文件時出現錯誤。',
	24037: '相關文件Id不可為空值或空白。',
	24038: '文件無效或不存在。',
	24039: '相關文件無效或不存在。',
	24040: '自定義內文建立失敗。',
	24041: '內文建立請求無效。',
	24042: '區塊建立請求無效。',
	24043: '透過id取得區塊失敗。',
	24044: '區塊建立失敗。',
	24045: '目前頁面無效。',
	24046: '無效的頁面大小。',
	24047: '與文件相關的對象ID無效。',
	24048: '物件已連結Canvas表單',
	24049: 'Object not found.',
	24050: '企業Uid無效。',
	24051: '若企業 Id已提供，則企業Uid必須提供，反之亦然。',
	24052: 'Canvas form snapshot creation failed.',
	24053: '從Id取得標題失敗。',
	24054: '從Id取得內文失敗。',
	24055: '建立表單基本資料時出現錯誤。',
	24056: '文件表單基本資料已存在。',
	24057: '文件表單基本資料驗證失敗。',
	24058: '文件表單基本資料不存在。',
	24059: '無法自訂區塊。',
	24060: '文件表單基本資料驗證失敗。若PCAOB-IA為True則PCAOB-FS應為True。',
	24061: '文件表單基本資料驗證失敗。若PCAOB-FS為False則PCAOB-IA應為False。',
	24062: "文件表單基本資料驗證失敗。若為'Non- complex'則'PCAOB - FS'/'PCAOB - IA'應為False。 ",
	24063: '國家Id無效。',
	24064: '語言Id無效。',
	24065: '無法自訂標題。',
	24066: '表單區塊物件連結建立失敗。',
	24067: '找不到物件。',
	24068: 'WCGW未連結SCOT。',
	24069: '參數{0}無效。',
	24070: '母公司企業Uid無效。',
	24071: '表單區塊物件連結取得失敗。',
	24072: '無區塊。請重新整理頁面並重試。若問題仍然存在，請聯絡 Help Desk。',
	24073: '無法檢索Snapshot。請重新整理頁面並重試。若問題仍然存在，請聯絡 Help Desk。',
	24074: '快照不適用於所選文件。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	24075: '無效Snapshot Id',
	24076: '企業 Id不能為空值或空白',
	24077: 'The passed in form section related object ID is not valid.',
	24078: 'Form Section Related Object ID should not be null or empty.',
	24079: '提供的母公司ID無效。',
	24080: '表單區塊相關物件：找不到母公司紀錄。',
	24081: 'Object not found.',
	24082: 'Object not found.',
	24083: '表單基本資料更新發生錯誤。',
	24084: '文件表單基本資料不存在。',
	24085: '語言id應大於0。',
	24086: '國家id應大於0。',
	24087: 'Knowledge delivered documents cannot be updated. Update Engagement Profile to change this forms profile.',
	24088: '角色無法編輯內容。請向案件管理者取得足夠權限。',
	24089: 'Custom Header Name has exceeded the maximum length (500 characters). Please adjust the name and try again.',
	24090: 'Custom Section Name has exceeded the maximum length (500 characters). Please adjust the name and try again.',
	24091: 'Section Custom Label has exceeded the maximum length (100 characters). Please adjust the name and try again.',
	24092: 'Custom Body Name has exceeded the maximum length (500 characters). Please adjust the name and try again.',
	24093: '自訂區塊名稱不應為空值或空白。',
	24094: '內文名稱不應為空值或空白。',
	24096: '自訂表頭名稱不應為空值或空白。',
	24097: '目前無法完成覆寫。',
	24098: '來源或目標文件類型id無效。',
	24099: '來源或目標文件knowledge表單id不同。',
	24100: 'Source Document ID should not be null or empty.',
	24101: 'Target Document ID should not be null or empty.',
	24103: '表頭已被刪除。請重新整理頁面並重試。若問題仍然存在，請聯絡Help Desk。',
	24104: '無法編輯表頭。',
	24105: '無法編輯內文。',
	24106: '更新動作無效。',
	24107: '文件表單基本資料驗證失敗。若為PCAOB-FS則應為複雜。',
	24108: '文件表單基本資料驗證失敗。若為PCAOB-IA則應為PCAOB-FS。',
	24110: '內容更新目前進行中。當完成目前內容更新，於Canvas表單內容更新頁面手動更新此表單內容。',
	24111: '對應與取消連結值不可相同。',
	24112: '來源文件不應共享。',
	24114: '加入證據失敗。請重新整理頁面並重試。',
	24115: '移除證據失敗。請重新整理頁面並重試。',
	24116: '提交基本資料失敗。請重新整理頁面並重試。',
	24117: '表單回覆未完成。請重新整理頁面並重試。',
	24118: 'Document ID and RelatedDocument ID cannot be the same.',
	24119: 'Editing body related objects failed.',
	24120: 'Body related object ID should not be null or empty.',
	24121: 'Object not found.',
	24122: 'Missing concurrency token',
	24124: 'Invalid target document(s) found.',
	24125: 'Target canvas form(s) not found.',
	24126: 'Request should not be null or empty.',
	24127: 'The import of data from EY Helix was not successful. Import data again from EY Helix settings and try again. If the issue persists contact the Help Desk.',
	24155: 'Submit profile is not allowed for restored engagement.',
	24164: 'You do not have appropriate permissions to make changes to the template.',
	24166: '不允許儲存基本資料。 表格不適用於此基本資料。',
	24167: 'Body type cannot be updated',
	24168: 'Modify User Id should not be null or empty',
	24169: 'Modify User Id can only have value when linkedResponseUpdate is false',
	24170: 'Invalid task id in request',
	24171: 'Duplicate body ids in request',
	24172: 'Section compare call failed',
	24173: 'Document Ids cannot be empty. As well it cannot exceed distinct 50 documents at a time',
	24174: 'Route document should be part of request body documentids',
	24175: 'Invalid smart evidence entity id in request body',
	24176: 'Body documentids should have same knowledge form id',
	24177: 'Invalid section id',
	24178: 'Invalid combination of DocumentID and DocumentIDList',
	24179: 'DocumentIds list cannot be greater than 50',
	24180: 'Materiality could not be updated',
	24181: 'Invalid Form Body ID',
	24182: 'Invalid Knowledge Form ID Or Knowledge Form Body ID',
	24183: 'An error has occurred while relating the document to this form. Please refresh the page and try again. If the issue persists, contact Help Desk.',
	24184: 'User is not active for this EY Canvas engagement. Invite the user via manage team and then try again. If the issue persists contact the Help Desk',
	24188: 'One or more records are no longer available. Please refresh the page',

	/*Document Change Types*/
	25001: '取得文件變更類型失敗。',
	25002: '無效的案件ID。',
	25003: '無效的請求。',

	/* Manage team */
	26001: '取得全部客戶使用者集團失敗。',
	26002: '建立新客戶群組/電子郵件網域失敗。',
	26003: '群組名稱不應為空值。',
	26004: '電子郵件網域不應為空值。',
	26005: '{0} 電子郵件網域標籤不應超過263字元。',
	26006: '{0} 電子郵件網域不應超過263字元。',
	26007: '{0} 電子郵件網域第一部分可為 * 或字母數字，不允許其他特殊字元。',
	26008: '{0} If the first part is a wild card, then it must have two or more parts following.',
	26009: '{0} 第一部分可為 * 或字母數字，其他網域部分不允許特殊字元。',
	26010: '電子郵件網域應為唯一。',
	26011: '所提供客戶登入群組ID無效。',
	26012: '刪除登入群組時發生錯誤。請確保無請求/外部任務指派給此群組或此群組成員並重試。',
	26013: '登入群組已被刪除，請重新整理頁面以取得最新資訊。',
	26014: '至少需要一個電子郵件網域。',
	26015: 'EY Canvas Client Portal目前無法登入且需要更新現有成員資訊。請重試且若問題仍然存在，請聯絡Help Desk。',
	26016: '刪除作業發生錯誤。',
	26017: '取得資料作業失敗。',
	26018: '同時發生問題，登入群組不再為使用中。',
	26019: '儲存作業失敗。',
	26020: '當有指派使用中成員時不可移除電子郵件網域。變動尚未儲存。',

	/* TimePhaseTypes - Milestones */
	27001: '取得里程碑詳細內容失敗。',

	/*Client Request Counts*/
	28001: '取得客戶請求資訊未獲取任何結果。',

	/*Content updates Error messages*/
	29001: '透過ID取得找不到。',
	29002: 'Acton type not found.',
	29003: '找不到內容ID。',
	29004: '內容更新API失敗。',
	29005: '內容更新進行中。請稍後重試且若問題仍然存在，請聯絡Help Desk。',
	29006: '無效的請求參數。',

	/*IT Process*/
	30001: '取得全部IT流程失敗。',
	30002: '取得全部IT流程 - 無效的案件ID。',
	30003: 'IT流程名稱不得為空白。',
	30004: 'IT流程名稱不得超過500字元。',
	30005: '無法完成此操作。請重新整理頁面並重試。若問題仍持續，請聯絡Help Desk。',
	30006: '透過id取得it流程失敗。',
	30007: '無效請求。',
	30008: 'IT流程不再可用。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	30009: 'Document not found.',
	30010: 'Cannot complete deletion. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	30012: 'Cannot complete deletion. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	30017: '無法完成此操作。請重新整理頁面並重試。若問題仍持續，請聯絡Help Desk。',
	30018: '無法完成此操作。請重新整理頁面並重試。若問題仍持續，請聯絡Help Desk。',
	30019: '無法完成此操作。請重新整理頁面並重試。若問題仍持續，請聯絡Help Desk。',

	/*Checklist*/
	31001: '取得全部檢查表失敗。',
	31002: '檢查表失敗。',
	31003: '無效的檢查表參數。',
	31004: '無效的案件錯誤。',
	31005: '無效的請求參數錯誤。',
	31006: '無效的已檢查請求參數錯誤。',

	/*Archive*/
	32001: '案件狀態ID無效。',
	32002: '歸檔失敗。',
	32003: 'V1 ARC Call失敗，請重新整理頁面、解決驗證、並重試歸檔流程。若問題仍然存在，請聯絡Help Desk。',
	32004: 'LDC案件狀態更新失敗。',
	32005: '案件緩存錯誤無效。',
	32006: '內容更新進行中。內容更新完成前您無法歸檔此案件。請稍後重試並聯絡Help Desk若問題仍然存在。',
	32007: 'ArcGUID is null Or empty.',
	32008: 'FileGuid is null Or empty.',
	32009: 'FileStoreHostTcp is null Or empty.',
	32200: '此案件有未解決驗證。請重新整理頁面、解決驗證、並重試歸檔流程。若問題仍然存在，請聯絡Help Desk。',
	32300: '此案件有未解決驗證。請重新整理頁面、解決驗證、並重試歸檔流程。若問題仍然存在，請聯絡Help Desk。',

	/*RBAC*/
	33001: '找不到案件ID。',
	33002: '找不到使用者ID。',

	/*Helix Linked Projects*/
	34001: '無法執行因請求為空值。',
	34002: '案件ID不應為空值或空白。',
	34003: '內文不得為空值或空白。',
	34004: 'Project ID不得為空值或空白。',
	34005: 'Project名稱不得為空值或空白。',
	34006: 'Project已連結，請重新頁面並重試。',
	34007: 'GetAll Helix Projects call failed.',
	34008: '案件ID應大於零。',
	34010: 'Save could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	34009: 'Project ID has changed. Refresh the page and try again.',
	34011: 'Currency Type cannot be null in the call.',
	34012: 'Currency Code cannot be null in the call.',
	34013: 'Business Unit cannot be null or empty in the call.',
	34014: 'Linked EY Helix Project has changed, cannot update the settings.',
	34017: 'Could not connect to EY Helix. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	34018: 'Operation could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	34019: 'Import completed but data not valid. Refresh the page and click Import to try again.',
	34027: 'Helix項目正在進行中。',
	34036: '無法連結到 EY Helix。 重新整理頁面頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	34039: '此 EY Helix 項目不再是您案件的主要案件。請重新整理頁面並重試。',
	34040: 'You must have the <b>EY Helix Import</b> permission to take this action. Speak with an engagement administrator to gain access.',

	/* PAANS */
	35001: '「EY政策編輯、核准及通知服務」目前無法使用。我們無法確認相關政策是否已完成。若您尚未檢視相關政策，請重新整理頁面並重試。若問題仍然存在，請聯絡Help Desk。 ',

	/*Engagement Comments*/
	38001: '建立 EngagementComments 回應失敗',
	38002: 'GetAll EngagementComments 回應失敗',
	38003: '無法完成此動作。請重新整理頁面並重試。若錯誤仍然存在，請聯絡 Help Desk',
	38004: '無法完成此動作。請重新整理頁面並重試。若錯誤仍然存在，請聯絡 Help Desk',
	38005: '無法完成此動作。請重新整理頁面並重試。若錯誤仍然存在，請聯絡 Help Desk',
	38006: '無法完成此動作。請重新整理頁面並重試。若錯誤仍然存在，請聯絡 Help Desk',
	38007: '無法完成此動作。請重新整理頁面並重試。若錯誤仍然存在，請聯絡 Help Desk',
	38008: '無法完成此動作。請重新整理頁面並重試。若錯誤仍然存在，請聯絡 Help Desk',
	38009: '無法完成此動作。請重新整理頁面並重試。若錯誤仍然存在，請聯絡 Help Desk',
	38010: '正文不應為空白',
	38011: 'CommentText 不應為 null 或為空白',
	38012: '個體不存在於給定的案件中',
	38013: 'EngagementCommentStatusId 應大於 0',
	38014: 'ParentEngagementCommentId 應大於 0',
	38015: '沒有符合指定條件的Canvas表單正文',
	38016: '您正在回覆的註記已被其他團隊成員刪除。請重新整理頁面並重試',
	38017: '提供的母案件註解已清除',
	38018: '提供的母案件註解本身就是回覆',
	38019: '所提供的註解文字不應短於 1 且不超過 4000 個字符',
	38020: '提供的個體 uid 無效',
	38021: '提供的母公司個體 uid 無效',
	38022: '刪除 EngagementComments 回應失敗',
	38023: '註解id不能為空白',
	38024: '該操作應是有效的操作',
	38025: '您正嘗試編輯一個已不存在的註解。請更新頁面並重試。',
	38026: '您不是註解的所有者',
	38027: '沒有找到要更新的註解',
	38028: '不允許更新已清除的註解',
	38029: '只有作者可以變更評論的內文',
	38030: '案件註解 ID 無效',
	38031: '註解狀態Id不能為空白',
	38032: '請求的使用者和案件之間沒有關係記錄。請重新整理頁面並重試。若錯誤仍然存在，請聯絡Help Desk。',
	38033: '更新操作無效',
	38034: '請求的 CommentId 已被其他註解使用',
	38035: '提供的註解 ID 無效',
	38036: '文件 ID 不應為 null 或為空白',
	38037: '將指派註解的用戶的 ID 不應為空白',
	38038: '打開或關閉註解時不應重新指派註解',
	38039: '您要從中刪除回覆的註記已被刪除。 重新整理頁面並重試',
	38040: '回覆不應有到期日',
	38041: '回覆不應有優先事項',
	38042: '註解應該有到期日',
	38043: '註解應該有優先事項',
	38044: '您正在嘗試編輯對不再存在的註解的回覆。請重新整理頁面並重試',
	38045: '不允許同時編輯案件狀態 ID 及其指派給用戶或優先事項的內容',
	38046: '更新 EngagementComments 回應失敗',
	38047: '您只能回覆開啟的註記',
	39004: 'This tag group is no longer available. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*Risk-Estimate*/
	4064: 'This risk is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',
	4065: 'Risk relation cannot be edited. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	4066: 'This estimate is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*IT App/SO*/
	81001: 'Get all ITApplication failed.',
	81002: 'Get all ITApplication - invalid engagementid.',
	81003: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81004: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81005: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81006: 'Invalid strategy type.',
	81007: 'Invalid Document Id.',
	81008: 'Get ITApplication by ID failed.',
	81009: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81010: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81011: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81012: 'ITApplicationID should not be null or empty.',
	81013: '無法編輯相關連結。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	81014: 'Delete ITApplicationITProcess call failed.',
	81015: 'ITApplicationITProcessId should not be empty.',
	81016: 'Invalid ITApplicationITProcessId.',
	81017: '無法編輯相關連結。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	81018: 'Create ITApplicationITProcess call failed.',
	81019: 'Passed Service Organization instead of ITApplication.',
	81020: 'Cannot complete deletion. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81039: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81041: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/* ITControl */
	84001: 'Failed to get IT controls.',
	84002: 'Failed to get IT control by id.',
	84003: 'IT Control Id is null or empty.',
	84004: 'Invalid IT control Id.',
	84005: 'Document Id is invalid.',
	84006: 'Missing IT control name.',
	84007: 'Invalid IT control name length.',
	84008: 'Invalid IT Control FrequencyId.',
	84009: 'Invalid IT Control ApprochTypeId.',
	84010: 'Invalid IT Control DesignEffectivenessTypeId.',
	84011: 'Invalid IT Control Testing value.',
	84012: 'Invalid IT Control OperatingEffectivenessTypeId.',
	84013: 'Missing IT control frequency.',
	84014: 'Delete IT Control failed.',
	84015: 'Search String should not be more than 100 characters.',
	84016: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	84018: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	84019: 'Cannot complete update. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*ITRisk*/
	86001: 'ITRisk name cannot be empty.',
	86002: 'ITRisk name should not be more than 500 characters.',
	86003: '無法完成此操作。請重新整理頁面並重試。若問題仍持續，請聯絡Help Desk。',
	86004: 'Get ITRisk by id call failed.',
	86005: '無法完成此操作。請更新頁面並重試。如果問題仍然存在，請聯繫Help Desk。',
	86006: 'Cannot complete association. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	86007: '無法完成此操作。請更新頁面並重試。如果問題仍然存在，請聯繫Help Desk。',
	86008: 'Cannot complete association. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	86009: '無法完成此操作。請更新頁面並重試。如果問題仍然存在，請聯繫Help Desk。',
	86010: '刪除技術風險失敗。',

	/*RiskFactorFormHeaders*/
	89001: '找不到風險因子連結。',
	89002: '找不到文件。',
	89003: '無文件id。',
	89004: '理由長度超過4000字元。',
	89005: '風險無法連結到所選風險因子。請重新整理頁面並重試。若問題仍然存在，請聯絡Help desk。',
	89014: '風險ID無效',
	89020: '風險因子無法儲存。請重新整理頁面並重試。若問題仍然存在，請聯絡Help Desk',

	/*Materiality*/
	91001: 'Materiality not found.',
	91002: 'Data could not be saved. Refresh the page and try again. If the issue persists contact the Help Desk.',
	91003: 'Invalid Materiality update request.',
	91004: 'Missing IsAnnualizedAmountExpectedToBeReported.',
	91005: 'When updating IsAnnualizedAmountExpectedToBeReported to true, it is mandatory to specify a BasisForecastAmount.',
	91006: 'Basis forecast amount should not be more than 15 digits or have any decimals.',
	91007: 'When updating IsAnnualizedAmountExpectedToBeReported to true, BasisForecastAmountRationale must be null.',
	91008: '基礎預測金額理由太短或太長。',
	91009: '無效的重要性 ID',
	91010: '無效的重要性內文類型 ID',
	91011: '名目值不能超過範圍的上限',
	91012: '重要性金額不應超過 15 位數字和 4 位小數',

	/*Group Structure - Sub Scopes */
	92013: 'Sub-scope cannot be deleted because there is at least one related region/component team.',
	92016: 'Sub-scope name already exist',

	/*Helix Account Mappings */
	94001: 'Account mapping could not be saved. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	94004: 'One or more EY Canvas accounts have been deleted and could not be mapped. Refresh the page and try again.',
	94005: '無法恢復計畫。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',

	/*Group Instructions */
	98001: '收回集團指引時發生錯誤。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	98002: '一個或多個Knowledge指引段落 ID 無效。',
	98003: '建立集團指引時發生錯誤。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	98004: '指引名稱不能為空白。',
	98005: '指引名稱不能超過 500 個字元。',
	98006: '指引描述不能為空白。',
	98007: '指引描述不能超過 30,000 個字元。',
	98009: '指引名稱不能重複。',
	98010: '截止日期不能為空白。',
	98011: '指引已刪除。',
	98012: '未找到指引。',
	98013: '指引 ID 必須大於零。',
	98014: '儲存集團指引時發生錯誤。請重新整理頁面並重試。若錯誤仍持續，請聯絡Help Desk。',
	98015: '指引強制範圍無法刪除。',
	98016: '指引已刪除',
	98017: '集團任務已刪除。',
	98018: '刪除後編輯集團任務。',
	98019: '未找到個體。',

	98020: 'Unable to generate the risk assessment package. Refresh the page and try again. If the issue persists, please contact the Help Desk.',

	98021: '文件名稱不能為空白。',
	98022: '文件名稱不能超過 115 個字元。',
	98023: '文件名稱不能包含無效的 XML 字元。',
	98024: 'package建立過程正在進行中，可能需要十分鐘才能完成',
	98025: 'Some instruction(s) do not have related component(s). Allocate component(s) to instructions and then Generate Group risk assessment communications again.',
	98026: 'Some component(s) do not have related account(s). Relate account(s) to component(s) and then Generate Group risk assessment communications again.',
	98027: 'Some Account(s) do not have Account document(s). Create Account document and then Generate Group risk assessment communications again.',
	98028: '此Canvas表單未與有效任務關聯。',
	98029: '一個或多個組成個體僅供參考。',
	98030: '一個或多個組成個體不是來自此主查團隊案件。',
	98031: '一個或多個範圍不是來自此主查團隊案件。',
	98032: '文件數量已超出限制。',
	98033: 'Required scopes cannot be deleted from the instruction.',

	/*Estimate */
	115017: '未找到估計',

	/* Group Audit */
	106003: '錯誤。請重新整理頁面並重試。',

	/* TasksOverview */
	117001: '取得所有 TaskOverview 呼叫失敗。',
	117002: '取得所有TaskOverview 請求不能為空白。',
	117003: '案件 ID 無效。',
	117004: '任務類別無效。',
	117005: '視圖值無效。',
	117006: 'DocumentCategorySearch 值無效。',
	117007: '重複的任務類別 ID。',
	117008: '重複的時間階段 ID。',
	117009: '重複的任務 ID。',
	117010: '重複的文件 ID。',
	117011: '重複的指派給使用者 ID。',

	/* Multientity */
	114001: 'Get all MultiEntity failed.',
	114002: 'STEntityName cannot be empty.',
	114003: 'STEntityName should not be more than 500 characters.',
	114004: 'STLegalName cannot be empty.',
	114005: 'STLegalName should not be more than 500 characters.',
	114006: 'Multi Entity can be created only with MEST Engagements.',
	114007: 'Create Multi Entity Account call failed.',
	114008: 'The selected STEntity has been deleted. Close this modal to see the updated list.',
	114009: 'Invalid account id.',
	114010: 'STEntityShortName should not be more than 100 characters.',
	114011: 'STEntity submit profile request is invalid.',
	114012: 'STEntity submit profile request body is invalid.',
	114013: 'STEntity submit profile request body should have distinct STEntityIDs.',
	114014: 'STEntityIDs for submit profile request are invalid.',
	114015: 'Form has incomplete responses. Refresh the page and try again.',
	114016: 'The content update is disabled for this.',
	114017: 'Engagement has no Multi Entity Individual Profile document.',
	114018: 'STEntity IDs missing Multi entity individual profile.',
	114019: '您正在對應的一個或多個個體在案件中已不再存在。請重新整理頁面並重試。',
	114020: 'STEntityShortName cannot be empty.',
	114021: 'Invalid DocumentId.',
	114022: 'Invalid EngagementId.',
	114023: 'STEntityDocument record already exists.',
	114024: 'STEntityDocument record does not exist.',
	114025: 'STEntityDocument record IsSystemAssociated.',
	114026: 'Invalid Request Body.',
	114028: '每個個體都沒有基本資料文件。',
	114035: 'ST個體關聯關係已存在。',
	114036: '請求所有個體更新時，至少應有一個基本資料文件有效。',
	114037: 'ST個體和科目的關係已被刪除。',
	114038: 'Get all MultiEntity layers failed.',
	114039: 'Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',
	114040: 'Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',

	/* Sample List */
	121101: 'Invalid Sample List ID.',
	121008: 'Invalid sample list ID.',
	121011: 'This sample is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',
	121013: 'This sample is no longer available in this engagement. Refresh the page and try again. Contact the Help Desk if the error persists.',
	121016: 'This sample is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',
	121037: 'An error occurred while taking this action. Refresh the page and try again. Contact the Help Desk if the error persists.',
	121012: 'Attribute status cannot be updated. Refresh the page and try again. Contact the Help Desk if the error persists.',
	121025: 'This document is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',
	121027: 'Document relation cannot be edited. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	121028: 'Document relation cannot be edited. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	121014: 'Attribute status cannot be updated. Refresh the page and try again. Contact the Help Desk if the error persists.',
	121029: 'This attribute is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',
	121021: 'Attribute status cannot be updated. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*Control Attributes */
	122018: 'Could not complete action. Refresh the page and try again. If the issue persists contact the Help Desk.',
	122021: 'An error occurred while taking this action. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*Flow chart*/
	123031: 'Form Body Id cannot not be linked to more than one flowcharts.',

	1034: 'This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	1035: 'This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	/*Group Instructions */
	196033: '指引不能有 0 個分配項目，必須至少有 1 個。',

	/*Information Security */
	200001: 'The action taken was forbidden by EY Information Security. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*Tags */
	40007: 'This tag is no longer available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	40029: 'Tag relation cannot be edited. Refresh the page and try again. If the issue persists, contact the Help Desk.',
};

export const roleForMember = [{
	id: 1,
	role: '主辦會計師(Partner in Charge)',
	roleAbbreviation: 'PIC'
},
{
	id: 2,
	role: '案件會計師(Engagement Partner)',
	roleAbbreviation: 'EP'
},
{
	id: 3,
	role: '執行總監(Executive Director)',
	roleAbbreviation: 'ED'
},
{
	id: 4,
	role: '負責人(Principal)',
	roleAbbreviation: 'Principle'
},
{
	id: 5,
	role: '資深經理(Senior Manager)',
	roleAbbreviation: 'Sr. Manager'
},
{
	id: 6,
	role: '經理(Manager)',
	roleAbbreviation: 'Manager'
},
{
	id: 7,
	role: '組長(Senior)',
	roleAbbreviation: 'Senior'
},
{
	id: 8,
	role: '組員(Staff)',
	roleAbbreviation: 'Staff'
},
{
	id: 9,
	role: '實習生(Intern)',
	roleAbbreviation: 'Intrn'
},
{
	id: 10,
	role: '案件品質複核者(Engagement Quality Reviewer)',
	roleAbbreviation: 'EQR'
},
{
	id: 11,
	role: '其他會計師(Other Partner)',
	roleAbbreviation: 'Other Partner'
},
{
	id: 12,
	role: 'GDS - Staff',
	roleAbbreviation: 'GDS Staff'
},
{
	id: 13,
	role: 'Advisory (ITRA,TAS,Human Capital or Other)',
	roleAbbreviation: 'ADV'
},
{
	id: 14,
	role: 'Tax',
	roleAbbreviation: 'Tax'
},
{
	id: 15,
	role: 'Executive Support Services',
	roleAbbreviation: 'ESS'
},
{
	id: 16,
	role: '總顧問(General Counsel)',
	roleAbbreviation: 'GCO'
},
{
	id: 17,
	role: '查核品質複核者(Audit Quality Reviewer)',
	roleAbbreviation: 'AQR'
},
{
	id: 18,
	role: 'ML組成個體查核團隊(Component Team)',
	roleAbbreviation: 'MCT'
},
{
	id: 19,
	role: '客戶主管(Client Supervisor)',
	roleAbbreviation: 'C. Supervisor'
},
{
	id: 20,
	role: '客戶員工(Client Staff)',
	roleAbbreviation: 'C. Staff'
},
{
	id: 21,
	role: '內部稽核主管(Internal Audit Supervisor)',
	roleAbbreviation: 'IA Supervisor'
},
{
	id: 22,
	role: '內部稽核員工(Internal Audit Staff)',
	roleAbbreviation: 'IA Staff'
},
{
	id: 23,
	role: '主管機關(Regulator)',
	roleAbbreviation: 'Regulator'
},
{
	id: 24,
	role: '其他 (例如：due diligence review)',
	roleAbbreviation: 'Other'
},
{
	id: 25,
	role: '辦公室',
	roleAbbreviation: 'Office'
},
{
	id: 26,
	role: '地區',
	roleAbbreviation: 'Area'
},
{
	id: 27,
	role: '行業',
	roleAbbreviation: 'IND'
},
{
	id: 28,
	role: 'National',
	roleAbbreviation: 'NAT'
},
{
	id: 29,
	role: 'Global',
	roleAbbreviation: 'GBL'
},
{
	id: 30,
	role: 'GDS - Senior',
	roleAbbreviation: 'GDS Senior'
},
{
	id: 31,
	role: 'GDS - Manager',
	roleAbbreviation: 'GDS Manager'
}
];

export const accountConclusionTabs = {
	conclusions: '結論'
};

export const assertions = [{
	id: 1,
	assertionname: '完整性',
	assertionabbreviation: 'C',
	statementtypeid: 2,
	displayorder: 1
},
{
	id: 2,
	assertionname: '存在性',
	assertionabbreviation: 'E',
	statementtypeid: 2,
	displayorder: 2
},
{
	id: 3,
	assertionname: '評價',
	assertionabbreviation: 'V',
	statementtypeid: 2,
	displayorder: 3
},
{
	id: 4,
	assertionname: '權利與義務',
	assertionabbreviation: 'R&O',
	statementtypeid: 2,
	displayorder: 4
},
{
	id: 5,
	assertionname: '表達與揭露',
	assertionabbreviation: 'P&D',
	statementtypeid: 2,
	displayorder: 5
},
{
	id: 6,
	assertionname: '完整性',
	assertionabbreviation: 'C',
	statementtypeid: 1,
	displayorder: 6
},
{
	id: 7,
	assertionname: '發生',
	assertionabbreviation: 'O',
	statementtypeid: 1,
	displayorder: 7
},
{
	id: 8,
	assertionname: '分攤',
	assertionabbreviation: 'M',
	statementtypeid: 1,
	displayorder: 8
},
{
	id: 9,
	assertionname: '表達與揭露',
	assertionabbreviation: 'P&D',
	statementtypeid: 1,
	displayorder: 9
},
{
	id: 10,
	assertionname: '完整性',
	assertionabbreviation: 'C',
	statementtypeid: 3,
	displayorder: 10
},
{
	id: 11,
	assertionname: '存在/發生',
	assertionabbreviation: 'E/O',
	statementtypeid: 3,
	displayorder: 11
},
{
	id: 12,
	assertionname: '分攤/評價',
	assertionabbreviation: 'M/V',
	statementtypeid: 3,
	displayorder: 12
},
{
	id: 13,
	assertionname: '權利與義務',
	assertionabbreviation: 'R&O',
	statementtypeid: 3,
	displayorder: 13
},
{
	id: 14,
	assertionname: '表達與揭露',
	assertionabbreviation: 'P&D',
	statementtypeid: 3,
	displayorder: 14
}
];

export const documentChangeTypesOptions = [{
	value: 1,
	label: '非行政變更'
},
{
	value: 2,
	label: '當使用追蹤修訂功能時接受修改'
},
{
	value: 3,
	label: '對已存在之證據加入額外交叉索引'
},
{
	value: 4,
	label: '加入先前收到傳真或電子郵件回函之函證回函正本'
},
{
	value: 5,
	label: '格式變更'
},
{
	value: 6,
	label: '完成Form AP和證實性角色評估'
},
{
	value: 7,
	label: '刪除或銷毀被更新之文件'
},
{
	value: 8,
	label: '編製管理階層建議書'
},
{
	value: 9,
	label: '完成與歸檔流程相關之檢查表並簽名'
},
{
	value: 10,
	label: '分類、核對並交叉索引最終文件',
},
{
	value: 12,
	label: 'MEST only: 修改個體相關證據(報告日晚於EY Canvas報告日)'
},
{
	value: 11,
	label: 'When adjusted to the local time zone, the modification is on or before the report date (Americas only)',
}
];

export const KnowledgeFormProfileAnswer = [{
	value: 1,
	label: '複雜',
	display: true
},
{
	value: 2,
	label: '不複雜',
	display: true
},
{
	value: 3,
	label: '上市櫃',
	display: true
},
{
	value: 4,
	label: '非上市櫃',
	display: false
},
{
	value: 5,
	label: ' PCAOB - IA',
	display: true
},
{
	value: 6,
	label: ' Non-PCAOB-IA',
	display: false
},
{
	value: 7,
	label: ' PCAOB - FS',
	display: true
},
{
	value: 8,
	label: ' Non-PCAOB-FS',
	display: false
},
{
	value: 9,
	label: '集團查核',
	display: true
},
{
	value: 10,
	label: '非集團查核',
	display: false
},
{
	value: 11,
	label: ' Digital',
	display: true
},
{
	value: 12,
	label: 'Core',
	display: true
}
];

export const strategyType = [{
	StrategyTypeId: 1,
	StrategyTypeName: '控制'
},
{
	StrategyTypeId: 2,
	StrategyTypeName: '證實'
}
];

export const controlTypeName = {
	1: 'Application',
	2: 'ITDM',
	3: '人工預防',
	4: '人工偵查'
};

export const inCompleteList = [{
	value: 1,
	label: '未完成',
	title: '未完成'
}];

export const scotTypes = [{
	value: 1,
	label: '例行性',
	title: '例行性',
	isDisabled: false
},
{
	value: 2,
	label: '非例行性',
	title: '非例行性',
	isDisabled: false
},
{
	value: 3,
	label: '估計',
	title: '估計',
	isDisabled: false
}
];

export const scotTypesNew = [{
	value: 1,
	label: '例行性',
	title: '例行性',
	isDisabled: false
},
{
	value: 2,
	label: '非例行性',
	title: '非例行性',
	isDisabled: false
},
{
	value: 3,
	label: '估計',
	title: '估計',
	isDisabled: false
},
{
	value: 4,
	label: 'FSCP',
	title: 'FSCP',
	isDisabled: false
}
];

export const estimationTypes = [{
	value: 1,
	label: '否',
	title: '否',
	isDisabled: false
},
{
	value: 2,
	label: '是',
	title: '是',
	isDisabled: false
}
];

/* IT Control */
export const itApproachType = [{
	ITApproachTypeId: 1,
	ITApproachTypeName: 'Controls'
},
{
	ITApproachTypeId: 2,
	ITApproachTypeName: 'Substantive'
}
];

/*Controls */
export const controlFrequencyType = [{
	value: 1,
	label: '一日多次',
	title: '一日多次'
},
{
	value: 2,
	label: '每日',
	title: '每日'
},
{
	value: 3,
	label: '每週',
	title: '每週'
},
{
	value: 4,
	label: '每月',
	title: '每月'
},
{
	value: 5,
	label: '每季',
	title: '每季'
},
{
	value: 6,
	label: '每年',
	title: '每年'
},
{
	value: 8,
	label: ' Other',
	title: ' Other'
}
];

export const controlTypes = [{
	value: 1,
	label: 'IT application control'
},
{
	value: 2,
	label: 'IT dependent manual control'
},
{
	value: 3,
	label: 'Manual prevent'
},
{
	value: 4,
	label: 'Manual detect'
}
];

export const strategyTypeCheck = {
	1: 'Controls',
	2: 'Substantive'
};

export const designEffectivenessType = [{
	DesignEffectivenessTypeId: 1,
	DesignEffectivenessTypeName: '有效'
},
{
	DesignEffectivenessTypeId: 2,
	DesignEffectivenessTypeName: '無效'
}
];

export const controlEffectivenessType = [{
	ControlEffectivenessTypeId: 1,
	ControlEffectivenessTypeName: '有效'
},
{
	ControlEffectivenessTypeId: 2,
	ControlEffectivenessTypeName: '無效'
}
];

export const testing = [{
	testingId: 1,
	testingDescription: '是'
},
{
	testingId: 2,
	testingDescription: '否'
}
];

export const controlType = [{
	value: 1,
	label: 'IT應用程式控制',
	title: 'IT應用程式控制'
},
{
	value: 2,
	label: '仰賴IT人工控制',
	title: '仰賴IT人工控制'
},
{
	value: 3,
	label: '人工預防',
	title: '人工預防'
},
{
	value: 4,
	label: '人工偵查',
	title: '人工偵查'
}
];

export const aggregateITEvaluationType = [{
	value: 1,
	label: 'Support',
	title: 'Support'
},
{
	value: 2,
	label: 'Not Support',
	title: 'Not Support'
},
{
	value: 3,
	label: 'FS & ICFR Support',
	title: 'FS & ICFR Support'
},
{
	value: 4,
	label: 'FS Only Support',
	title: 'FS Only Support'
}
];

export const KnowledgeFormProfileQuestion = [{
	value: 1,
	label: '複雜'
},
{
	value: 2,
	label: '上市櫃'
},
{
	value: 3,
	label: ' PCAOB - IA'
},
{
	value: 4,
	label: ' PCAOB - FS'
},
{
	value: 5,
	label: '地點'
},
{
	value: 6,
	label: '語言'
},
{
	value: 7,
	label: '集團查核'
},
{
	value: 8,
	label: 'Digital'
}
];

export const KnowledgeLanguage = [{
	value: 1,
	label: ' English'
},
{
	value: 2,
	label: ' Spanish (Latin America)'
},
{
	value: 3,
	label: ' French (Canada)'
},
{
	value: 4,
	label: ' Dutch'
},
{
	value: 5,
	label: ' Croatian'
},
{
	value: 6,
	label: ' Czech'
},
{
	value: 7,
	label: ' Danish'
},
{
	value: 8,
	label: ' Finnish'
},
{
	value: 9,
	label: ' German (Germany Austria)',
},
{
	value: 10,
	label: ' Hungarian'
},
{
	value: 11,
	label: ' Italian'
},
{
	value: 12,
	label: ' Japanese (Japan)'
},
{
	value: 13,
	label: ' Norwegian (Norway)'
},
{
	value: 14,
	label: ' Polish'
},
{
	value: 15,
	label: ' Slovak'
},
{
	value: 16,
	label: ' Slovenian'
},
{
	value: 17,
	label: ' Swedish'
},
{
	value: 18,
	label: ' Arabic'
},
{
	value: 19,
	label: ' Simplified Chinese (China)'
},
{
	value: 20,
	label: ' Traditional Chinese (Taiwan)'
},
{
	value: 21,
	label: ' Greek'
},
{
	value: 22,
	label: ' Hebrew (Israel)'
},
{
	value: 23,
	label: ' Indonesian'
},
{
	value: 24,
	label: ' Korean (Republic of Korea)'
},
{
	value: 25,
	label: ' Portuguese (Brazil)'
},
{
	value: 26,
	label: ' Romanian'
},
{
	value: 27,
	label: '  Russian (Russia)'
},
{
	value: 28,
	label: ' Thai'
},
{
	value: 29,
	label: ' Turkish'
},
{
	value: 30,
	label: ' Vietnamese'
},
{
	value: 31,
	label: 'PCAOB - English'
},
{
	value: 32,
	label: 'PCAOB - Spanish (Latin America)'
},
{
	value: 33,
	label: 'PCAOB - French (Canada)'
},
{
	value: 34,
	label: 'PCAOB - Dutch'
},
{
	value: 35,
	label: 'PCAOB - Croatian'
},
{
	value: 36,
	label: 'PCAOB - Czech'
},
{
	value: 37,
	label: 'PCAOB - Danish'
},
{
	value: 38,
	label: 'PCAOB - Finnish'
},
{
	value: 39,
	label: 'PCAOB - German (Germany, Austria)'
},
{
	value: 40,
	label: 'PCAOB - Hungarian'
},
{
	value: 41,
	label: 'PCAOB - Italian'
},
{
	value: 42,
	label: 'PCAOB - Japanese (Japan)'
},
{
	value: 43,
	label: 'PCAOB - Norwegian (Norway)'
},
{
	value: 44,
	label: 'PCAOB - Polish'
},
{
	value: 45,
	label: 'PCAOB - Slovak'
},
{
	value: 46,
	label: 'PCAOB - Slovenian'
},
{
	value: 47,
	label: 'PCAOB - Swedish'
},
{
	value: 48,
	label: 'PCAOB - Arabic'
},
{
	value: 49,
	label: 'PCAOB - Simplified Chinese (China)'
},
{
	value: 50,
	label: 'PCAOB - Traditional Chinese (Taiwan)'
},
{
	value: 51,
	label: 'PCAOB - Greek'
},
{
	value: 52,
	label: 'PCAOB - Hebrew (Israel)'
},
{
	value: 53,
	label: 'PCAOB - Indonesian'
},
{
	value: 54,
	label: 'PCAOB - Korean (Republic of Korea)'
},
{
	value: 55,
	label: 'PCAOB - Portuguese (Brazil)'
},
{
	value: 56,
	label: 'PCAOB - Romanian'
},
{
	value: 57,
	label: 'PCAOB - Russian (Russia)'
},
{
	value: 58,
	label: 'PCAOB - Thai'
},
{
	value: 59,
	label: 'PCAOB - Turkish'
},
{
	value: 60,
	label: 'PCAOB - Vietnamese'
}
];

export const KnowledgeCountry = [{
	value: 1,
	label: ' Mayotte'
},
{
	value: 2,
	label: ' British Virgin Islands'
},
{
	value: 3,
	label: ' Spain'
},
{
	value: 4,
	label: ' Belize'
},
{
	value: 5,
	label: ' Peru'
},

{
	value: 6,
	label: ' Slovakia'
},
{
	value: 7,
	label: ' Venezuela'
},
{
	value: 8,
	label: ' Norway'
},
{
	value: 9,
	label: ' Falkland Islands (Malvinas)'
},
{
	value: 10,
	label: ' Mozambique'
},

{
	value: 11,
	label: ' China'
},
{
	value: 12,
	label: ' Sudan'
},
{
	value: 13,
	label: ' Israel'
},
{
	value: 14,
	label: ' Belgium'
},
{
	value: 15,
	label: ' Saudi Arabia'
},

{
	value: 16,
	label: ' Gibraltar'
},
{
	value: 17,
	label: ' Guam'
},
{
	value: 18,
	label: ' Norfolk Islands'
},
{
	value: 19,
	label: ' Zambia'
},
{
	value: 20,
	label: ' Reunion'
},

{
	value: 21,
	label: ' Azerbaijan'
},
{
	value: 22,
	label: ' Saint Helena'
},
{
	value: 23,
	label: ' Iran'
},
{
	value: 24,
	label: ' Monaco'
},
{
	value: 25,
	label: ' Saint Pierre and Miquelon'
},

{
	value: 26,
	label: ' New Zealand'
},
{
	value: 27,
	label: ' Cook Islands'
},
{
	value: 28,
	label: ' Saint Lucia'
},
{
	value: 29,
	label: ' Zimbabwe'
},
{
	value: 30,
	label: ' Iraq'
},

{
	value: 31,
	label: ' Tonga'
},
{
	value: 32,
	label: ' American Samoa'
},
{
	value: 33,
	label: ' Maldives'
},
{
	value: 34,
	label: ' Morocco'
},
{
	value: 35,
	label: ' International Standards on Auditing (ISA)'
},

{
	value: 36,
	label: ' Albania'
},
{
	value: 37,
	label: ' Afghanistan'
},
{
	value: 38,
	label: ' Gambia'
},
{
	value: 39,
	label: ' Burkina Faso'
},
{
	value: 40,
	label: ' Tokelau'
},

{
	value: 41,
	label: ' Libya'
},
{
	value: 42,
	label: ' Canada'
},
{
	value: 43,
	label: ' United Arab Emirates'
},
{
	value: 44,
	label: ' KoreaDemocratic Peoples Republic of',
},
{
	value: 45,
	label: ' Montserrat'
},

{
	value: 46,
	label: ' Greenland'
},
{
	value: 47,
	label: ' Rwanda'
},
{
	value: 48,
	label: ' Fiji'
},
{
	value: 49,
	label: ' Djibouti'
},
{
	value: 50,
	label: ' Botswana'
},

{
	value: 51,
	label: ' Kuwait'
},
{
	value: 52,
	label: ' Madagascar'
},
{
	value: 53,
	label: ' Isle of Man'
},
{
	value: 54,
	label: ' Hungary'
},
{
	value: 55,
	label: ' Namibia'
},

{
	value: 56,
	label: ' Malta'
},
{
	value: 57,
	label: ' Jersey'
},
{
	value: 58,
	label: ' Thailand'
},
{
	value: 59,
	label: ' Saint Kitts and Nevis'
},
{
	value: 60,
	label: ' Bhutan'
},

{
	value: 61,
	label: ' Panama'
},
{
	value: 62,
	label: ' Somalia'
},
{
	value: 63,
	label: ' Bahrain'
},
{
	value: 64,
	label: ' Bosnia and Herzegovina'
},
{
	value: 65,
	label: 'France'
},

{
	value: 66,
	label: ' KoreaRepublic of',
},
{
	value: 67,
	label: ' Iceland'
},
{
	value: 68,
	label: ' Portugal'
},
{
	value: 69,
	label: ' Tunisia'
},
{
	value: 70,
	label: ' Ghana'
},

{
	value: 71,
	label: ' Cameroon'
},
{
	value: 72,
	label: ' Greece'
},
{
	value: 73,
	label: ' French Southern Territories'
},
{
	value: 74,
	label: ' Heard and McDonald Islands'
},
{
	value: 75,
	label: ' Andorra'
},

{
	value: 76,
	label: ' Luxembourg'
},
{
	value: 77,
	label: ' Samoa'
},
{
	value: 78,
	label: ' Anguilla'
},
{
	value: 79,
	label: ' Netherlands'
},
{
	value: 80,
	label: ' Guinea-Bissau'
},

{
	value: 81,
	label: ' Nicaragua'
},
{
	value: 82,
	label: ' Paraguay'
},
{
	value: 83,
	label: ' Antigua and Barbuda'
},
{
	value: 84,
	label: ' International Financial Reporting Standard (IFRS)'
},
{
	value: 85,
	label: ' Niger'
},

{
	value: 86,
	label: ' Egypt'
},
{
	value: 87,
	label: ' Vatican City State'
},
{
	value: 88,
	label: ' Latvia'
},
{
	value: 89,
	label: ' Cyprus'
},
{
	value: 90,
	label: ' US Minor Outlying Islands'
},

{
	value: 91,
	label: ' Russia'
},
{
	value: 92,
	label: ' Saint Vincent and the Grenadines'
},
{
	value: 93,
	label: ' Guernsey'
},
{
	value: 94,
	label: ' Burundi'
},
{
	value: 95,
	label: ' Cuba'
},

{
	value: 96,
	label: ' Equatorial Guinea'
},
{
	value: 97,
	label: ' British Indian Ocean Territory'
},
{
	value: 98,
	label: ' Sweden'
},
{
	value: 99,
	label: ' Uganda'
},
{
	value: 100,
	label: ' Macedonia,the Former Yugoslav Republic of',
},

{
	value: 101,
	label: ' Swaziland'
},
{
	value: 102,
	label: ' El Salvador'
},
{
	value: 103,
	label: ' Kyrgyzstan'
},
{
	value: 104,
	label: ' Ireland'
},
{
	value: 105,
	label: ' Kazakhstan'
},

{
	value: 106,
	label: ' Honduras'
},
{
	value: 107,
	label: ' Uruguay'
},
{
	value: 108,
	label: ' Georgia'
},
{
	value: 109,
	label: ' Trinidad and Tobago'
},
{
	value: 110,
	label: ' Palestinian Authority'
},

{
	value: 111,
	label: ' Martinique'
},
{
	value: 112,
	label: ' Aland Islands'
},
{
	value: 113,
	label: ' French Polynesia'
},
{
	value: 114,
	label: ' Ivory Coast'
},
{
	value: 115,
	label: ' Montenegro'
},

{
	value: 116,
	label: ' South Africa'
},
{
	value: 117,
	label: ' South Georgia and the South Sandwich Islands'
},
{
	value: 118,
	label: ' Yemen'
},
{
	value: 119,
	label: ' Hong Kong China'
},
{
	value: 120,
	label: ' Kenya'
},

{
	value: 121,
	label: ' Chad'
},
{
	value: 122,
	label: ' Colombia'
},
{
	value: 123,
	label: ' Costa Rica'
},
{
	value: 124,
	label: ' Angola'
},
{
	value: 125,
	label: ' Lithuania'
},

{
	value: 126,
	label: ' Syria'
},
{
	value: 127,
	label: ' Malaysia'
},
{
	value: 128,
	label: ' Sierra Leone'
},
{
	value: 129,
	label: ' Serbia'
},
{
	value: 130,
	label: ' Poland'
},

{
	value: 131,
	label: ' Suriname'
},
{
	value: 132,
	label: ' Haiti'
},
{
	value: 133,
	label: ' Nauru'
},
{
	value: 134,
	label: ' Sao Tome and Principe'
},
{
	value: 135,
	label: ' Svalbard and Jan Mayen'
},

{
	value: 136,
	label: ' Singapore'
},
{
	value: 137,
	label: ' Moldova'
},
{
	value: 138,
	label: 'Taiwan'
},
{
	value: 139,
	label: ' Senegal'
},
{
	value: 140,
	label: ' Gabon'
},

{
	value: 141,
	label: ' Mexico'
},
{
	value: 142,
	label: ' Seychelles'
},
{
	value: 143,
	label: ' MicronesiaFederated States of',
},
{
	value: 144,
	label: ' Algeria'
},
{
	value: 145,
	label: ' Italy'
},

{
	value: 146,
	label: ' San Marino'
},
{
	value: 147,
	label: ' Liberia'
},
{
	value: 148,
	label: ' Brazil'
},
{
	value: 149,
	label: ' Croatia'
},
{
	value: 150,
	label: ' Faroe Islands'
},

{
	value: 151,
	label: ' Palau'
},
{
	value: 152,
	label: ' Finland'
},
{
	value: 153,
	label: ' Philippines'
},
{
	value: 154,
	label: ' Jamaica'
},
{
	value: 155,
	label: ' French Guiana'
},

{
	value: 156,
	label: ' Cape Verde'
},
{
	value: 157,
	label: ' Myanmar'
},
{
	value: 158,
	label: ' Lesotho'
},
{
	value: 159,
	label: ' US Virgin Islands'
},
{
	value: 160,
	label: ' Cayman Islands'
},

{
	value: 161,
	label: ' Niue'
},
{
	value: 162,
	label: ' Togo'
},
{
	value: 163,
	label: ' Belarus'
},
{
	value: 164,
	label: ' Dominica'
},
{
	value: 165,
	label: ' Indonesia'
},

{
	value: 166,
	label: ' Uzbekistan'
},
{
	value: 167,
	label: ' Nigeria'
},
{
	value: 168,
	label: ' Wallis and Futuna'
},
{
	value: 169,
	label: ' Barbados'
},
{
	value: 170,
	label: ' Sri Lanka'
},

{
	value: 171,
	label: ' United Kingdom'
},
{
	value: 172,
	label: ' Ecuador'
},
{
	value: 173,
	label: ' Guadeloupe'
},
{
	value: 174,
	label: ' Laos'
},
{
	value: 175,
	label: ' Jordan'
},

{
	value: 176,
	label: ' Solomon Islands'
},
{
	value: 177,
	label: ' East Timor'
},
{
	value: 178,
	label: ' Lebanon'
},
{
	value: 179,
	label: ' Central African Republic'
},
{
	value: 180,
	label: ' India'
},

{
	value: 181,
	label: ' Christmas Island'
},
{
	value: 182,
	label: ' Vanuatu'
},
{
	value: 183,
	label: ' Brunei'
},
{
	value: 184,
	label: ' Bangladesh'
},
{
	value: 185,
	label: '  Antarctica'
},

{
	value: 186,
	label: '  Bolivia'
},
{
	value: 187,
	label: ' Turkey'
},
{
	value: 188,
	label: ' Bahamas'
},
{
	value: 189,
	label: ' Comoros'
},
{
	value: 190,
	label: ' Western Sahara'
},

{
	value: 191,
	label: ' Czech Republic'
},
{
	value: 192,
	label: ' Ukraine'
},
{
	value: 193,
	label: ' Estonia'
},
{
	value: 194,
	label: ' Bulgaria'
},
{
	value: 195,
	label: ' Mauritania'
},

{
	value: 196,
	label: ' CongoThe Democratic Republic of the',
},
{
	value: 197,
	label: ' Liechtenstein'
},
{
	value: 198,
	label: ' Pitcairn'
},
{
	value: 199,
	label: ' Denmark'
},
{
	value: 200,
	label: ' Marshall Islands'
},

{
	value: 201,
	label: ' Japan'
},
{
	value: 202,
	label: ' Austria'
},
{
	value: 203,
	label: ' Oman'
},
{
	value: 204,
	label: ' Mongolia'
},
{
	value: 205,
	label: ' Tajikistan'
},

{
	value: 206,
	label: ' Switzerland'
},
{
	value: 207,
	label: ' Guatemala'
},
{
	value: 208,
	label: ' Eritrea'
},
{
	value: 209,
	label: ' Nepal'
},
{
	value: 210,
	label: ' Mali'
},

{
	value: 211,
	label: ' Slovenia'
},
{
	value: 212,
	label: ' Northern Mariana Islands'
},
{
	value: 213,
	label: ' (Not Applicable)'
},
{
	value: 214,
	label: ' Aruba'
},
{
	value: 215,
	label: ' Congo'
},

{
	value: 216,
	label: ' Qatar'
},
{
	value: 217,
	label: ' Guinea'
},
{
	value: 218,
	label: ' United States'
},
{
	value: 219,
	label: ' Ethiopia'
},
{
	value: 220,
	label: ' Other'
},

{
	value: 221,
	label: ' Guyana'
},
{
	value: 222,
	label: ' Germany'
},
{
	value: 223,
	label: ' Bermuda'
},
{
	value: 224,
	label: ' Turks and Caicos Islands'
},
{
	value: 225,
	label: ' Australia'
},

{
	value: 226,
	label: ' Kiribati'
},
{
	value: 227,
	label: ' Puerto Rico'
},
{
	value: 228,
	label: ' Pakistan'
},
{
	value: 229,
	label: ' Mauritius'
},
{
	value: 230,
	label: ' Malawi'
},

{
	value: 231,
	label: ' Turkmenistan'
},
{
	value: 232,
	label: ' Cambodia'
},
{
	value: 233,
	label: 'Chile'
},
{
	value: 234,
	label: ' New Caledonia'
},
{
	value: 235,
	label: ' Papua New Guinea'
},

{
	value: 236,
	label: ' Bouvet Island'
},
{
	value: 237,
	label: ' Tuvalu'
},
{
	value: 238,
	label: ' Curacao'
},
{
	value: 239,
	label: ' Dominican Republic'
},
{
	value: 240,
	label: ' Vietnam'
},

{
	value: 241,
	label: ' Cocos (Keeling) Islands'
},
{
	value: 242,
	label: ' Grenada'
},
{
	value: 243,
	label: ' Tanzania'
},
{
	value: 244,
	label: ' Argentina'
},
{
	value: 245,
	label: ' Macau China',
},

{
	value: 246,
	label: ' Benin'
},
{
	value: 247,
	label: '  Romania'
},
{
	value: 248,
	label: ' Armenia'
},
{
	value: 249,
	label: ' global'
},
{
	value: 250,
	label: ' IFRS for SMEs'
},

{
	value: 251,
	label: ' US GAAP'
},
{
	value: 252,
	label: ' AICPA financial reporting framework for small and medium sized entities'
},
{
	value: 253,
	label: ' South Sudan'
}
];

export const pagingSvgHoverText = {
	first: '第一頁',
	previous: '前一頁',
	next: '下一頁',
	last: '最終頁'
};

export const priorityTypesForDropdown = [{
	value: 1,
	label: 'Low',
	className: 'Low'
},
{
	value: 2,
	label: 'Medium',
	className: 'Medium'
},
{
	value: 3,
	label: 'High',
	className: 'High'
},
{
	value: 4,
	label: 'Critical',
	className: 'Critical'
}
];

export const reviewNoteFilterTypes = [{
	value: 0,
	label: '全部'
},
{
	value: 1,
	label: '開啟'
},
{
	value: 2,
	label: '已清除'
},
{
	value: 3,
	label: '已關閉'
}
];

export const reviewStatus = [{
	id: 1,
	name: '開啟'
},
{
	id: 2,
	name: '已清除'
},
{
	id: 3,
	name: '已關閉'
}
];

export const reviewNoteOpenStatusOption = [{
	value: 2,
	label: '清除'
},
{
	value: 3,
	label: '關閉'
}
];

export const reviewNoteClearedStatusOption = [{
	value: 1,
	label: '重新開啟'
},
{
	value: 3,
	label: '關閉'
}
];

export const reviewNoteBulkClearedStatusOption = [{
	value: 1,
	label: '重新開啟'
},
{
	value: 2,
	label: '清除'
},
{
	value: 3,
	label: '關閉'
}
];

export const reviewNoteClosedStatusOption = [{
	value: 1,
	label: '重新開啟'
},
{
	value: 4,
	label: '刪除'
}
];

export const taskTypeBadge = {
	1: 'OST',
	2: 'PST',
	3: 'WT',
	4: 'TOC',
	5: 'OSP',
	6: 'PSP',
	7: 'RT',
	8: 'GT',
	9: 'PIC',
	10: 'EQR',
	11: 'PIC/EQR',
	22: 'ACT',
	23: 'UDP'
};

export const riskTypes = [{
	id: 1,
	name: '顯著風險',
	abbrev: 'SR',
	label: 'Significant',
	title: '顯著風險'
},
{
	id: 2,
	name: '舞弊風險',
	abbrev: 'FR',
	label: 'Fraud',
	title: '舞弊風險'
},
{
	id: 3,
	name: '重大不實表達風險',
	abbrev: 'R',
	label: '重大不實表達風險',
	title: '重大不實表達風險'
},
{
	id: 4,
	name: '非常低的風險估計',
	abbrev: 'VLRS',
	label: '非常低的風險估計',
	title: '非常低的風險估計'
},
{
	id: 5,
	name: '較低風險估計',
	abbrev: 'LRE',
	label: '較低風險估計',
	title: '較低風險估計'
},
{
	id: 6,
	name: '較高風險估計',
	abbrev: 'HRE',
	label: '較高風險估計',
	title: '較高風險估計'
},
{
	id: 7,
	name: '估計-未選擇',
	abbrev: 'ENS',
	label: '估計-未選擇',
	title: '估計-未選擇'
}

];

export const relatedRisksDropdownRiskTypes = [{
	id: 1,
	name: '重大風險',
	abbrev: 'SR',
	label: '特別',
	title: '重大風險'
},
{
	id: 2,
	name: '舞弊風險',
	abbrev: 'FR',
	label: '舞弊',
	title: '舞弊風險'
},
{
	id: 3,
	name: '重大不實表達風險',
	abbrev: 'R',
	label: '重大不實表達風險',
	title: '重大不實表達風險'
}
];

export const estimateTypes = [{
	id: 4,
	name: 'Very low risk estimate',
	abbrev: 'VLRE',
	label: 'Very Low',
	title: 'Very low risk estimate'
},
{
	id: 5,
	name: 'Lower risk estimate',
	abbrev: 'LRE',
	label: 'Lower',
	title: 'Lower risk estimate'
},
{
	id: 6,
	name: 'Higher risk estimate',
	abbrev: 'HRE',
	label: 'Higher',
	title: 'Higher risk estimate'
},
{
	id: 7,
	name: 'Estimate - Not selected',
	abbrev: 'NA',
	label: 'Not selected',
	title: 'Estimate - Not selected'
}
];

export const statementTypes = [{
	id: 1,
	name: '損益表'
},
{
	id: 2,
	name: '資產負債表'
},
{
	id: 3,
	name: 'Both'
}
];

export const RbacErrors = {
	106: 'Insufficient permissions to edit content. Work with an engagement administrator to get sufficient rights.'
};

export const HelixProjectValidationErrors = {
	800: 'It looks like you have not accessed EY Helix before? Go to',
	801: 'You are not an authorized team member of the linked project. Contact the EY Helix project administrator to get access.',
	901: 'The selected EY Helix Project is no longer available. Click EY Helix projects to link a new project.',
	902: 'The selected EY Helix Project is marked for deletion. Click EY Helix projects to link a new project.',
	903: 'The selected EY Helix Project is marked for storage. Click EY Helix projects to link a new project.',
	926: 'The selected analyzer is not available in EY Helix. Refresh the page and try again. If the issue persists contact the Help Desk.',
	927: 'Analytics are not available in the linked project. Go to EY Helix and complete the data & analytics processing steps to continue.',
	928: 'Invalid or missing analyzer in EY Helix. Refresh the page and try again. If the issue persists contact the Help Desk.',
	929: 'An error occurred related to the linked EY Helix project. Data cannot be imported.'
};

export const EngagementProfileRequirementErrors = {
	108: 'Engagement Profile is not complete.'
};

export const IndependenceRequirementErrors = {
	103: 'Missing Engagement user independence compliance.'
};

export const strategyTypes = [{
	id: 3,
	name: '範圍內'
},
{
	id: 4,
	name: '範圍外'
}
];

export const itAppTypes = [{
	value: 0,
	label: 'IT應用程式'
},
{
	value: 1,
	label: '服務機構'
}
];

export const confidentialityLevels = {
	[confidentialityTypes.DEFAULT]: 'Default',
	[confidentialityTypes.LOW]: 'Low',
	[confidentialityTypes.MODERATE]: 'Moderate',
	[confidentialityTypes.HIGH]: 'High'

	// This has been disabled for release 2.5, uncomment if required
	// [confidentialityTypes.CONFIDENTIAL]: 'Confidential'
};

export const formBodyOptionRiskTypes = [{
	id: 1,
	label: '顯著風險'
},
{
	id: 2,
	label: '舞弊風險'
},
{
	id: 3,
	label: '重大不實表達風險'
}
];

export const formViewTypes = [{
	value: 0,
	label: 'Form'
},
{
	value: 1,
	label: 'Changes'
},
{
	value: 2,
	label: 'Details'
}
];

export const railFilterValidations = [{
	value: 0,
	label: 'All'
},
{
	value: 1,
	label: 'Incomplete responses'
},
{
	value: 2,
	label: '未解決的註解'
}
];

export const aresRiskTypes = [{
	id: 1,
	name: '顯著風險'
},
{
	id: 2,
	name: '舞弊風險'
},
{
	id: 3,
	name: '重大不實表達風險'
},
{
	id: 4,
	name: 'Very low risk estimate'
},
{
	id: 5,
	name: 'Lower risk estimate'
},
{
	id: 6,
	name: 'Higher risk estimate'
},
{
	id: 7,
	name: 'Estimate - Not selected'
}
];

export const materialityTypes = [{
	value: 1,
	label: '稅前收入'
},
{
	value: 2,
	label: 'EBIT(息稅前利潤)'
},
{
	value: 3,
	label: 'EBITDA(息稅折舊攤銷前盈餘)',
},
{
	value: 4,
	label: '毛利率'
},
{
	value: 5,
	label: '收入'
},
{
	value: 6,
	label: '營業費用'
},
{
	value: 7,
	label: '權益'
},
{
	value: 8,
	label: '資產'
},
{
	value: 9,
	label: '基於活動衡量（其他）'
},
{
	value: 10,
	label: '稅前虧損'
},
{
	value: 11,
	label: '基於權益衡量（其他）'
},
{
	value: 12,
	label: '基於收入衡量（其他）'
}
];

export const helixCurrencyType = {
	[currencyType.Functional]: 'Functional',
	[currencyType.Reporting]: 'Reporting'
};

export const controlRiskType = [{
	id: 1,
	name: 'Rely'
},
{
	id: 2,
	name: 'Not Rely'
},
{
	id: 3,
	name: 'Test MP'
}
];

export const inherentRiskType = [{
	id: 1,
	name: 'Higher'
},
{
	id: 2,
	name: 'Lower'
},
{
	id: 3,
	name: 'Very Low'
}
];

export const AlraInherentRiskType = [{
	id: 3,
	name: 'Not Relevant'
},
{
	id: 2,
	name: 'Lower'
},
{
	id: 1,
	name: 'Higher'
}
];

export const scotInherentRiskType = [{
	id: 1,
	name: 'Higher'
},
{
	id: 2,
	name: 'Lower'
},
{
	id: 3,
	name: 'Non-routine SCOT'
}
];

export const CRAStrings = {
	Minimal: 'Minimal',
	Low: 'Low',
	'Low +SC': 'Low + SC',
	Moderate: 'Moderate',
	High: 'High',
	'High +SC': 'High + SC'
};

export const priorityType = [{
	id: 1,
	name: 'Low',
	className: 'Low',
	label: 'L'
},
{
	id: 2,
	name: 'Medium',
	className: 'Medium',
	label: 'M'
},
{
	id: 3,
	name: 'High',
	className: 'High',
	label: 'H'
},
{
	id: 4,
	name: 'Critical',
	className: 'Critical',
	label: 'C'
}
];

export const kendoLabels = {
	addComment: '新增註記',
	addColumnBefore: '新增左方欄',
	addColumnAfter: '新增右方欄',
	addInlineComment: '新增行內註解',
	addRowAbove: ' Add row above',
	addRowBelow: ' Add row below',
	alignLeft: ' Justify left',
	alignRight: ' Justify right',
	alignCenter: ' Justify center',
	alignFull: ' Justify full',
	backgroundColor: '背景顏色',
	bulletList: '插入無序列表',
	bold: ' Bold',
	backColor: ' Highlight',
	createLink: '插入超連結',
	createTable: ' Create table',
	cleanFormatting: ' Clean formatting',
	deleteRow: ' Delete row',
	deleteColumn: ' Delete column',
	deleteTable: '刪除表格',
	fontSizeInherit: ' Font size',
	foreColor: '字體顏色',
	format: '格式',
	fontSize: ' Font size',
	hyperlink: '插入連結',
	italic: ' Italic',
	indent: ' Indent',
	insertTableHint: '建立一個 {0} 使用 {1} 表格',
	huge: '巨大',
	'hyperlink-dialog-content-address': '網址',
	'hyperlink-dialog-title': '插入超連結',
	'hyperlink-dialog-content-title': '標題',
	'hyperlink-dialog-content-newwindow': '在新視窗開啟連結',
	'hyperlink-dialog-cancel': '取消',
	'hyperlink-dialog-insert': '插入',
	large: '大',
	noDataPlaceholder: '輸入文字',
	normal: '正常',
	orderedList: '插入有序列表',
	outdent: ' Outdent',
	paragraphSize: '段落大小',
	print: '列印',
	pdf: '匯出至pdf',
	redo: '重做',
	removeFormatting: '刪除格式',
	strikethrough: ' Strikethrough',
	small: '小',
	subscript: ' Subscript',
	superscript: ' Superscript',
	underline: ' Underline',
	undo: '復原',
	unlink: '取消連結'
};

export const kendoFormatOptions = [{
	text: '段落',
	value: 'p'
},
{
	text: '標題1',
	value: 'h1'
},
{
	text: '標題2',
	value: 'h2'
},
{
	text: '標題3',
	value: 'h3'
},
{
	text: '標題4',
	value: 'h4'
},
{
	text: '標題5',
	value: 'h5'
},
{
	text: '標題6',
	value: 'h6'
}
];

export const kendoFontSize = [{
	text: '8',
	value: '8px'
},
{
	text: '9',
	value: '9px'
},
{
	text: '10',
	value: '10px'
},
{
	text: '11',
	value: '11px'
},
{
	text: '12',
	value: '12px'
},
{
	text: '14',
	value: '14px'
},
{
	text: '16',
	value: '16px'
},
{
	text: '18',
	value: '18px'
},
{
	text: '20',
	value: '20px'
},
{
	text: '22',
	value: '22px'
},
{
	text: '24',
	value: '24px'
},
{
	text: '26',
	value: '26px'
},
{
	text: '28',
	value: '28px'
},
{
	text: '36',
	value: '36px'
},
{
	text: '48',
	value: '48px'
},
{
	text: '72',
	value: '72px'
}
];

export const ItFlowValidationLabels = {
	ITAppWithoutAtLeastOneRelatedITProcess: 'Unrelated IT application',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: 'Unrelated ITGC',
	ITSPHasNorelatedITRisk: 'Unrelated ITSP',
	ITProcessHasNoRelatedITApplication: 'Unrelated IT process',
	ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication: 'Missing IT risk',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: 'Missing ITGC or mark none exists',
	ITDMorITACWithNoRelatedITApplication: 'App/ITDM control with no IT app',
	ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'ITSP to be deleted',
	ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'ITGC to be deleted',
	ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'IT risk to be deleted',
	ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed: 'ITGC with invalid testing strategy',
	ITGCWithoutASelectedDesignEffectiveness: 'Missing ITGC design evaluation',
	SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero: 'Unrelated SCOT',
	AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed: 'Inconsistent response for testing controls',
	SCOTWithHasNoITApplicationHasITDMOrAppControls: 'IT application in SCOT missing'
};

export const ISA315ITFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: ItFlowValidationLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: ItFlowValidationLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: ItFlowValidationLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: ItFlowValidationLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: ItFlowValidationLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed,
	label: ItFlowValidationLabels.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: ItFlowValidationLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
}
];

/* Notes modal labels */

export const reviewNoteModalLabels = {
	/*Review Notes*/
	engagement: 'Engagement',
	emptyReplyErrorMsg: 'Add text to continue',
	lengthReplyErrorMsg: 'The reply cannot exceed 4000 characters.',
	documentLabel: 'Document',
	task: 'Task',
	allEngagementFilterLabel: 'All other engagements',
	otherEngagementComments: '其他案件註記',
	notesModalInstructionalText: '檢視並回應以下所選{0}註記。',
	commentThread: 'Note thread',
	singleNoteInstructionalText: '檢視並回應以下所選註記。',
	emptyNoteDetailsMessage: '選擇註記以檢視詳細內容。如要啟動批次控制，請使用control或shift鍵並選擇多個複核註記。若您欲操作個別註記，請從列表中選擇該註記。',
	documentReviewNotesLabel: '文件註記',
	addNewReviewNoteButtonText: '加入註記',
	noNotesAssociatedWithDocumentLabel: '使用下方的輸入留下註解。 將註記指派給使用者並指定優先順序和截止日期。',
	noNotesFound: '沒有找到註記',
	noNotesAssociatedWithTaskLabel: '該任務無相關{0}註記。',
	allNotesLabel: '所有註記',
	charactersLabel: 'characters',
	myNotesLabel: '我的註記',
	showClearedLabel: 'Show cleared',
	showClosedLabel: 'Show closed',
	assignedToLabel: 'Assigned to',

	ofLabel: 'of',
	enterNoteText: '輸入註記',
	addNewNoteModalClose: 'Close',
	addNewNoteModalTitleLabel: '加入新註記',
	editNoteModalTitleLabel: '編輯註記',
	deleteIconHoverText: 'Delete',
	deleteIconModalAcceptText: 'Delete',
	deleteIconModalConfirmMessage: '請確認是否要刪除您對本註記之回覆？',
	deleteIconModalConfirmMessageParent: '請確認是否要刪除所選註記？',
	deleteIconModalTitleLabel: '刪除註記',
	deleteReplyIconModalTitle: 'Delete reply',
	emptyRepliesMessage: 'No replies yet',
	replyInputPlaceholder: '回覆本註記',
	replyInputPlaceholderEdit: '使用註記或語音註記編輯回覆',
	noteInputPlaceholderEdit: '使用註記或語音註記進行編輯',
	replyText: 'Reply text',
	editReplyModelTitle: 'Edit reply',
	editReplyPlaceholder: 'Enter reply',
	noteDueDateLabel: 'Due',

	priorityLabel: 'Priority',
	dueDateLabel: 'Due date',
	dueLabel: 'Due',
	status: 'Status',
	noteModifiedDateLabel: 'Modified',
	cancelLabel: 'Cancel',
	saveLabel: 'Save',
	clearedBy: 'Cleared by',
	closedBy: 'Closed by',
	reopenedBy: 'Reopened by',
	reply: 'Reply',
	editIconHoverTextLabel: 'Edit',
	required: 'Required',
	closeTitle: 'Close',
	otherEngagementNotes: '其他案件註記',
	closeLabel: 'Close',
	showMore: 'Show more',
	showLess: 'Show less',
	showMoreEllipsis: 'Show more...',
	showLessEllipsis: 'Show less...',
	noResultFound: 'No results found',
	engagementNameLabel: 'Engagement name: ',
	taskReviewNotesLabel: '任務註記',
	fromUserLabel: 'From',
	toUserLabel: 'To',
	view: 'View',
	dueDateRequiredTextError: '截止日期是必須的'
};

export const notesFilterLabels = [{
	id: notesFilter.allNotes,
	label: '全部註記',
	value: notesFilter.allNotes
},
{
	id: notesFilter.myNotes,
	label: '我的註記',
	value: notesFilter.myNotes
},
{
	id: notesFilter.authoredByMeNotes,
	label: 'Assigned to me',
	value: notesFilter.authoredByMeNotes
}
];

export const reviewerAssignments = {
	taskLayoutHeaderAssignments: '指派事項',
	manageAssigmentsStep2: '編輯任務指派事項',
	editAssignment: '編輯指派事項',
	deleteAssignment: '刪除指派事項',
	manageAssigmentsStep3: '完成指派事項',
	taskAssigmentStatusHeader: '指派事項狀態',
	taskAssignmentName: '指派事項',
	dueDateAssigment: '到期',
	editDueDate: '選擇性：編輯早於結束日期的天數',
	teamMemberAssigmentLabel: '團隊成員',
	currentAssigmentLabel: '目前',
	handOffToAssigmentLabel: '遞交給：',
	priorToEndDateLabel: '早於結束日期',
	noTimePhaseAssigmentLabel: '無指派的時段',
	closedByAssigmentLabel: '關閉者',
	onAssingmentLabel: '於',
	preparerAssigmentOpenTitleTip: '遞交本任務以關閉您的任務指派事項',
	reviewerAssigmentOpenTitleTip: '標記任務指派事項為已解決',
	reviewerAssigmentClosedTitleTip: '標記任務指派事項為未解決',
	AssigmentLabel: '遞交本任務以關閉您的任務指派事項',
	timePhaseName: '時段：',
	timePhaseEndDate: '結束日期：',
	AssignmentType: [{
		id: 1,
		displayName: '編製者'
	},
	{
		id: 2,
		displayName: '詳細複核者'
	},
	{
		id: 3,
		displayName: '一般複核者'
	},
	{
		id: 4,
		displayName: 'Partner'
	},
	{
		id: 5,
		displayName: 'EQR'
	},
	{
		id: 6,
		displayName: '其他'
	}
	],
	AssignmentStatus: [{
		id: 1,
		displayName: '未完成'
	},
	{
		id: 2,
		displayName: '進行中'
	},
	{
		id: 3,
		displayName: '已關閉'
	},
	{
		id: 4,
		displayName: '未指派'
	}
	],
	assignmentTableColumnHeader: '指派事項',
	teamMemberTableColumnHeader: '團隊成員',
	dueDaysTableColumnHeader: '到期',
	daysPriorToEndDate: '結束日期前天數',
	handoffButton: '提交'
};
/* Notes modal labels */

export const handOffModal = {
	title: '移交',
	description: '移交此任務給下一位團隊成員。欲簽核證據檔案，請從下列選項中選擇。',
	dropdownLabel: '移交給',
	closeTitle: '取消',
	confirmButton: '移交',
	evidence: '證據',
	evidenceSignOffTitle: '簽核所有：',
	existingSignOffs: '已存在之簽核',
	noDocumentsAvailable: '無可用文件'
};

// manage scot modal labels
export const manageSCOTModal = {
	title: '管理 SCOTs',
	description: '您可以建立、編輯或刪除現有的 SCOTs。 儲存後將應用更改。',
	addSCOTLink: '新增SCOT'
};

export const deleteSCOTModal = {
	title: '刪除SCOT',
	description: '以下 SCOT(s) 將被刪除。 該操作無法撤銷。'
};

export const manageITAppModalLabels = {
	title: '管理 IT 應用程序',
	description: 'Create new IT applications or edit and delete existing IT applications below.',
	inputNameTitle: 'IT 應用程序名稱',
	deleteConfirmMessage: '您辨識要刪除 IT 應用程序 <b>{0}</b> 嗎？ 這不能被撤銷。',
	addSuccessMessage: "已成功建立 IT 應用程序'{0}'",
	editSuccessMessage: "已成功儲存對 IT 應用程序'{0}' 的編輯",
	deleteSuccessMessage: "'{0}' 已成功刪除"
};

export const manageSOModalLabels = {
	title: '管理服務組織',
	description: 'Create new service organizations or edit and delete existing service organizations below.',
	inputNameTitle: '服務機構名稱',
	deleteConfirmMessage: '您確定要刪除服務組織<b>{0}</b>嗎？ 這不能被撤銷。',
	addSuccessMessage: "已成功建立服務組織'{0}'",
	editSuccessMessage: "已成功儲存對服務組織'{0}' 的編輯",
	deleteSuccessMessage: "'{0}' 已成功刪除",
	addServiceOrganization: '新增服務機構',
	editServiceOrganization: '編輯服務組織',
	deleteServiceOrganization: '刪除服務機構'
};

export const customNameModal = {
	title: 'JE 來源名稱字尾',
	closeTitle: '取消',
	save: '儲存',
	suffixRequired: '需要字尾！',
	suffix: '字尾',
	addSuffix: '新增字尾',
	editSuffix: '編輯字尾'
};

export const GuidedWorkFlowLabels = {
	RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM: '不相關的事件和條件/不實表達風險',
	RisksUnrelatedToAnAssertionForGuidedWorkflow: '未連結之風險',
	IncompleteMeasurementBasisForecastAmount: '未完成基礎',
	IncompleteMeasurementBasisForecastAmountRationale: '基礎之理由為必要',
	IncompleteMeasurementBasisAdjustedAmount: '未完成調整金額',
	IncompletePlanningMateriality: '未完成PM',
	PlanningMaterialityGreaterThanMaximumAmount: 'PM過大',
	IncompletePlanningMaterialityRationale: 'PM理由為必要',
	IncompleteTolerableError: '未完成TE',
	TENotWithinRangeOfAllowedValues: '無效TE百分比',
	IncompleteTolerableErrorRationale: 'TE之理由為必要',
	IncompleteSAD: '未完成SAD',
	SADGreaterThanMaximum: 'SAD過大',
	IncompleteSADRationale: 'SAD之理由為必要',
	IncompletePACESelection: '未完成PACE',
	AccountWithoutIndividualRiskAssessmentForm: '科目{0}缺少文件',
	EstimateWithoutIndividualEstimateForm: '估計{0}缺少文件',
	AccountWithoutIndividualAnalyticForm: '科目{0}缺少文件',
	MultiEntityWithoutIndividualProfileForm: '個體沒有多個體單獨基本資料的文件',
	AccountAccountTypeIDDoesNotMatchAction: '科目名稱選擇不一致',
	AccountHasEstimateDoesNotMatchAction: '科目估計選擇不一致',
	AccountFormOptionHasRelatedRisksNotAssociatedToAccount: '風險沒有相關科目關聯',
	AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk: '較高的固有風險聲明無風險',
	AccountMissingSubstantiveProcedure: '無證實程序的科目/個體',
	MultiEntityNotRelatedToALLPSTACTForRelatedAccount: '科目/個體需要更新內容',
	ComponentWithoutGroupInvolvementForm: '沒有集團參與表單的組成個體（不包含僅供參考的組成個體）',
	ComponentWithoutRelatedGroupAssessmentInstruction: '沒有集團風險評估說明的組成個體',
	IncompleteAssertionRiskLevel: '不完整的聲明風險層級',
	EstimateAccountWithoutEsimatePSPIndex: '沒有估計 PSP 索引的估計科目',
	AccountExecutedWithoutRelatedComponent: 'Group - Accounts (Executed in other engagements) without a related Full or Specific scope Component',
	MultiEntityAccountWithoutRelatedToAnyMultiEntity: '無相關個體的科目',
	ChangeNotSubmittedMultiEntityFullProfile: '未提交變更',
	ChangeNotSubmittedMultiEntityIndividualDocument: '未提交變更',
	AccountTypeWithMissingInformation: '缺少科目資訊',
	DocumentUploadMissingRequiredPICEQRSignOffs: 'Evidence missing sign-off(s)',
	DocumentUploadMissingRequiredPICEQRSignOffRequirements: 'Evidence missing sign-off requirement(s)',
	DocumentUploadMissingPreparerOrReviewerSignOffs: 'Document upload - Missing Preparer or Reviewer Sign-offs',
	ITAppWithoutAtLeastOneRelatedITProcess: 'Unrelated IT application',
	ITProcessHasNoRelatedITApplication: 'Unrelated IT process',
	ITGCWithoutASelectedDesignEffectiveness: 'Missing ITGC design evaluation',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: 'Unrelated ITGC',
	ITSPHasNorelatedITRisk: 'Unrelated ITSP',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: 'Missing ITGC or mark none exists',
	EstimateWithoutAccountRelated: 'Estimate without account related',
	EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount: 'Higher/lower risk estimate related to non-estimate account',
	RiskEstimateRiskTypeIDDoesNotMatchAction: "Estimate category response not aligned to designation in'Edit estimate'",
	LowerorHigherRiskEstimateWithoutEstimateSCOT: 'Estimate without valid SCOT',
	EstimateWithoutIndividualEstimateDocument: 'Estimate missing individual document',
	EstimateAccountWithoutHigherOrLowerRiskEstimate: 'Account without valid estimate',
	EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary: 'Estimate account without Higher or Lower risk Estimate',
	EstimateScotWithoutHigherOrLowerRiskEstimate: 'Estimate SCOT without Higher or Lower risk Estimate',
	HigherRiskEstimateWithoutRisk: 'Higher risk estimate without valid risk related',
	PICEQRSignOffRequirements: 'PIC or EQR Signoff requirement does not match response',
	AdjustmentsWithoutAnyEvidence: 'Adjustments without evidence',
	AdjustmentsThatDoNotNet: 'Adjustments that do not net',
	DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration: 'Document checked out or in the process of being checked out for collaboration',
	NonEngagementWideTasksMissingEvidence: 'Non-Engagement wide tasks missing evidence',
	EstimatesMustBeMarkedHigherRisk: 'Significant/Fraud risk related to an estimate that is not a Higher risk',
	SCOTEstimateNoRelatedWalkthroughForm: '無穿透測試的重大交易流程/估計',
	SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2: '無連結科目的重大交易流程',
	AccountSignificantDisclosureWithNoRelatedSCOTV2: 'Account - Significant Account / Significant Disclosure that is not a CT only Account with no related SCOT or an Estimate when it is an Estimate Account',
	ITApplicationWithoutITAppRiskAssessmentIndividualDocument: 'Technology risk assessment missing document',
	ITApplicationWithoutITAppPlanningIndividualDocument: 'Technology missing document',
	FormContentWithoutHeader: 'Form Content without Header',
	RisksWithoutAnyRelatedAssertions: 'There are risks that have not been related to at least one assertion',
	AssertionsWithIncompleteCRA: 'There are assertions missing an inherent and/or control risk assessment',
	LimitedRiskOrInsignificantAccountMissingRationale: 'All limited risk and insignificant accounts shall have rationale provided',
	ITProcessWithoutWalkthroughDocument: 'ITProcess without IT process - Walkthrough - Individual',
	ITProcessIsUncategorized: 'IT Process - ITProcessTypeID is Uncategorized',
	ITProcessWithNoRelatedITApplication: 'ITProcess - ITProcess with no related IT Application'
};

export const GuidedWorkFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.RiskEstimateRiskTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.RiskEstimateRiskTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.FormContentWithoutHeader,
	label: GuidedWorkFlowLabels.FormContentWithoutHeader
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary
},
{
	validationId: validationTypes.EstimateScotWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateScotWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.HigherRiskEstimateWithoutRisk,
	label: GuidedWorkFlowLabels.HigherRiskEstimateWithoutRisk
},
{
	validationId: validationTypes.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM,
	label: GuidedWorkFlowLabels.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.RisksUnrelatedToAnAssertionForGuidedWorkflow,
	label: GuidedWorkFlowLabels.RisksUnrelatedToAnAssertionForGuidedWorkflow
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmount
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmountRationale,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmountRationale
},
{
	validationId: validationTypes.IncompleteMeasurementBasisAdjustedAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisAdjustedAmount
},
{
	validationId: validationTypes.IncompletePlanningMateriality,
	label: GuidedWorkFlowLabels.IncompletePlanningMateriality
},
{
	validationId: validationTypes.PlanningMaterialityGreaterThanMaximumAmount,
	label: GuidedWorkFlowLabels.PlanningMaterialityGreaterThanMaximumAmount
},
{
	validationId: validationTypes.IncompletePlanningMaterialityRationale,
	label: GuidedWorkFlowLabels.IncompletePlanningMaterialityRationale
},
{
	validationId: validationTypes.IncompleteTolerableError,
	label: GuidedWorkFlowLabels.IncompleteTolerableError
},
{
	validationId: validationTypes.TENotWithinRangeOfAllowedValues,
	label: GuidedWorkFlowLabels.TENotWithinRangeOfAllowedValues
},
{
	validationId: validationTypes.IncompleteTolerableErrorRationale,
	label: GuidedWorkFlowLabels.IncompleteTolerableErrorRationale
},
{
	validationId: validationTypes.IncompleteSAD,
	label: GuidedWorkFlowLabels.IncompleteSAD
},
{
	validationId: validationTypes.SADGreaterThanMaximum,
	label: GuidedWorkFlowLabels.SADGreaterThanMaximum
},
{
	validationId: validationTypes.IncompleteSADRationale,
	label: GuidedWorkFlowLabels.IncompleteSADRationale
},
{
	validationId: validationTypes.IncompletePACESelection,
	label: GuidedWorkFlowLabels.IncompletePACESelection
},
{
	validationId: validationTypes.AccountWithoutIndividualRiskAssessmentForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualRiskAssessmentForm
},
{
	validationId: validationTypes.EstimateWithoutIndividualEstimateForm,
	label: GuidedWorkFlowLabels.EstimateWithoutIndividualEstimateForm
},
{
	validationId: validationTypes.AccountWithoutIndividualAnalyticForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualAnalyticForm
},
{
	validationId: validationTypes.MultiEntityWithoutIndividualProfileForm,
	label: GuidedWorkFlowLabels.MultiEntityWithoutIndividualProfileForm
},
{
	validationId: validationTypes.AccountAccountTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountAccountTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.AccountHasEstimateDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountHasEstimateDoesNotMatchAction
},
{
	validationId: validationTypes.AccountFormOptionHasRelatedRisksNotAssociatedToAccount,
	label: GuidedWorkFlowLabels.AccountFormOptionHasRelatedRisksNotAssociatedToAccount
},
{
	validationId: validationTypes.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk,
	label: GuidedWorkFlowLabels.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk
},
{
	validationId: validationTypes.MultiEntityNotRelatedToALLPSTACTForRelatedAccount,
	label: GuidedWorkFlowLabels.MultiEntityNotRelatedToALLPSTACTForRelatedAccount
},
{
	validationId: validationTypes.AccountMissingSubstantiveProcedure,
	label: GuidedWorkFlowLabels.AccountMissingSubstantiveProcedure
},
{
	validationId: validationTypes.ComponentWithoutGroupInvolvementForm,
	label: GuidedWorkFlowLabels.ComponentWithoutGroupInvolvementForm
},
{
	validationId: validationTypes.ComponentWithoutRelatedGroupAssessmentInstruction,
	label: GuidedWorkFlowLabels.ComponentWithoutRelatedGroupAssessmentInstruction
},
{
	validationId: validationTypes.EstimateAccountWithoutEstimatePSPIndex,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutEsimatePSPIndex
},
{
	validationId: validationTypes.AssertionInherentRiskWithoutRelatedHigherRisk,
	label: GuidedWorkFlowLabels.IncompleteAssertionRiskLevel
},
{
	validationId: validationTypes.AccountGroupWithoutAComponent,
	label: GuidedWorkFlowLabels.AccountExecutedWithoutRelatedComponent
},
{
	validationId: validationTypes.MultiEntityAccountWithoutRelatedToAnyMultiEntity,
	label: GuidedWorkFlowLabels.MultiEntityAccountWithoutRelatedToAnyMultiEntity
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityFullProfile,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityFullProfile
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityIndividualDocument,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityIndividualDocument
},
{
	validationId: validationTypes.AccountWithMissingValues,
	label: GuidedWorkFlowLabels.AccountTypeWithMissingInformation
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffs
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffRequirements
},
{
	validationId: validationTypes.DocumentUploadMissingPreparerOrReviewerSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingPreparerOrReviewerSignOffs
},
{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: GuidedWorkFlowLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: GuidedWorkFlowLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: GuidedWorkFlowLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: GuidedWorkFlowLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: GuidedWorkFlowLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
},
{
	validationId: validationTypes.EstimateWithoutAccountRelated,
	label: GuidedWorkFlowLabels.EstimateWithoutAccountRelated
},
{
	validationId: validationTypes.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount,
	label: GuidedWorkFlowLabels.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount
},
{
	validationId: validationTypes.LowerorHigherRiskEstimateWithoutEstimateSCOT,
	label: GuidedWorkFlowLabels.LowerorHigherRiskEstimateWithoutEstimateSCOT
},
{
	validationId: validationTypes.PICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.PICEQRSignOffRequirements
},
{
	validationId: validationTypes.EstimatesMustBeMarkedHigherRisk,
	label: GuidedWorkFlowLabels.EstimatesMustBeMarkedHigherRisk
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.NonEngagementWideTasksMissingEvidence,
	label: GuidedWorkFlowLabels.NonEngagementWideTasksMissingEvidence
},
{
	validationId: validationTypes.AdjustmentsWithoutAnyEvidence,
	label: GuidedWorkFlowLabels.AdjustmentsWithoutAnyEvidence
},
{
	validationId: validationTypes.AdjustmentsThatDoNotNet,
	label: GuidedWorkFlowLabels.AdjustmentsThatDoNotNet
},
{
	validationId: validationTypes.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration,
	label: GuidedWorkFlowLabels.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration
},
{
	validationId: validationTypes.ITApplicationWithoutITAppRiskAssessmentIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppRiskAssessmentIndividualDocument
},
{
	validationId: validationTypes.SCOTEstimateNoRelatedWalkthroughForm,
	label: GuidedWorkFlowLabels.SCOTEstimateNoRelatedWalkthroughForm
},
{
	validationId: validationTypes.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2,
	label: GuidedWorkFlowLabels.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2
},
{
	validationId: validationTypes.AccountSignificantDisclosureWithNoRelatedSCOTV2,
	label: GuidedWorkFlowLabels.AccountSignificantDisclosureWithNoRelatedSCOTV2
},
{
	validationId: validationTypes.ITApplicationWithoutITAppPlanningIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppPlanningIndividualDocument
},
{
	validationId: validationTypes.RisksWithoutAnyRelatedAssertions,
	label: GuidedWorkFlowLabels.RisksWithoutAnyRelatedAssertions
},
{
	validationId: validationTypes.AssertionsWithIncompleteCRA,
	label: GuidedWorkFlowLabels.AssertionsWithIncompleteCRA
},
{
	validationId: validationTypes.LimitedRiskOrInsignificantAccountMissingRationale,
	label: GuidedWorkFlowLabels.LimitedRiskOrInsignificantAccountMissingRationale
},
{
	validationId: validationTypes.ITProcessWithoutWalkthroughDocument,
	label: GuidedWorkFlowLabels.ITProcessWithoutWalkthroughDocument
},
{
	validationId: validationTypes.ITProcessIsUncategorized,
	label: GuidedWorkFlowLabels.ITProcessIsUncategorized
},
{
	validationId: validationTypes.ITProcessWithNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessWithNoRelatedITApplication
}

];

// Label overrides (redefine here labels / objects that apply for a different part of the application)
export const resourceOverrides = {
	['Ares']: {
		labels: {
			notAROMM: '不存在重大不實表達風險',
			fraudRisk: '舞弊風險',
			significantRisk: '重大風險',
			identifiedRiskFactors: '已辨識的事件/情況、重大不實表達風險、重大風險和舞弊風險',
			countUnassociatedRisk: '事件/條件不相關/未標記為「非重大不實表達風險」。'
		},
		riskTypes: [{
			id: 1,
			name: '重大風險',
			abbrev: 'S',
			label: '重大的',
			title: '重大風險'
		},
		{
			id: 2,
			name: '舞弊風險',
			abbrev: 'F',
			label: '舞弊',
			title: '舞弊風險'
		},
		{
			id: 3,
			name: '重大不實表達風險',
			abbrev: 'R',
			label: '重大不實表達風險',
			title: '重大不實表達風險'
		}
		]
	}
};

export const jeSourceTypes = [{
	value: 1,
	label: '系統生成'
},
{
	value: 2,
	label: '人工的'
},
{
	value: 3,
	label: '兩個都'
}
];

export const hasJournalEntriesOption = [{
	value: 1,
	label: '是'
},
{
	value: 2,
	label: '否'
}
];

export const filterReviewNoteStatus = [{
	value: 1,
	label: '開啟'
},
{
	value: 2,
	label: '已清除'
},
{
	value: 3,
	label: '已關閉'
}
];

export const EntitiesLabels = {
	close: '關閉',
	cancel: '取消',
	repNoRecordMessage: '未找到結果',
	edit: '編輯',
	delete: '刪除',
	actions: '行動',
	show: 'Show',
	first: 'First',
	last: 'Last',
	prev: '上一頁',
	next: '下一頁',
	search: 'Search',
	primary: 'Primary',
	knowledgeRiskLabel: 'Risks from knowledge cannot be edited or deleted',


	[Entity.Account]: {
		manageEntity: 'Manage accounts and disclosures',
		searchEntity: 'Search accounts',
		createEntity: '新科目',
		entityName: '科目',
		entityNameCaps: '科目',
		entityNamePlural: '科目',
		placeholderText: 'Create new accounts and disclosures or edit and delete existing accounts and disclosures below.',
		deleteConfirmLabel: 'Are you sure you want to delete this account? All existing relationships will be removed. This action cannot be undone.'
	},
	[Entity.Estimate]: {
		manageEntity: 'Manage estimates',
		searchEntity: 'Search estimates',
		createEntity: '新的估計',
		entityName: '估計',
		entityNameCaps: '估計',
		entityNamePlural: '估計',
		placeholderText: '建立新的估計或編輯並刪除下方現有估計。',
		deleteConfirmLabel: 'Are you sure you want to delete this estimate? All existing relationships will be removed. This action cannot be undone.'
	},
	[Entity.Risk]: {
		manageEntity: '管理風險',
		searchEntity: '搜尋風險',
		createEntity: '新風險',
		entityName: '風險',
		entityNameCaps: '風險',
		entityNamePlural: '風險',
		placeholderText: '建立新風險或編輯並刪除下方現有風險。',
		deleteConfirmLabel: 'Are you sure you want to delete this risk? All existing relationships will be removed. This action cannot be undone.'
	},
	[Entity.STEntity]: {
		manageEntity: '管理個體',
		searchEntity: '搜尋個體',
		createEntity: '新個體',
		entityName: '個體',
		entityNameCaps: '個體',
		entityNamePlural: '個體',
		placeholderText: '建立新個體或編輯和刪除下方現有個體。',
		deleteEntity: 'Delete entity',
		deleteConfirmLabel: 'Are you sure you want to delete this entity? All existing relationships will be removed. This action cannot be undone.'
	},
	[Entity.Control]: {
		manageEntity: 'Manage Control',
		searchEntity: 'Search control',
		createEntity: 'New control',
		entityName: 'control',
		entityNameCaps: 'Control',
		entityNamePlural: 'Controls',
		placeholderText: 'Create new controls or edit and delete existing controls below.',
		deleteEntity: 'Delete control',
		deleteConfirmLabel: 'Are you sure you want to delete this control? All existing relationships will be removed. This action cannot be undone.'
	},
	[Entity.ITProcess]: {
		manageEntity: 'Manage IT processes',
		searchEntity: 'Search IT process',
		createEntity: 'New IT process',
		entityName: 'IT process',
		entityNameCaps: 'IT process',
		entityNamePlural: 'IT processes',
		placeholderText: 'Create new IT process or edit and delete existing IT processes below.',
		deleteEntity: 'Delete IT process',
		deleteConfirmLabel: 'Are you sure you want to delete this IT process? All existing relationships will be removed. This action cannot be undone.'
	},
	[Entity.ITRisk]: {
		manageEntity: '管理技術風險',
		searchEntity: '搜尋技術風險',
		createEntity: '新技術風險',
		entityName: '技術風險',
		entityNameCaps: '技術風險',
		entityNamePlural: '技術風險',
		placeholderText: '建立新技術風險或編輯並刪除以下已有風險。',
		deleteEntity: '刪除技術風險',
		deleteConfirmLabel: '是否確定刪除此風險？所有現有關係將被一同刪除。此操作無法撤銷。'
	},
	[Entity.ITControl]: {
		ITGC: {
			manageEntity: 'Manage ITGCs',
			searchEntity: 'Search ITGCs',
			createEntity: 'New ITGC',
			editEntity: 'Edit ITGC',
			viewEntity: 'View ITGC',
			entityName: 'ITGC',
			entityNameCaps: 'ITGC',
			entityNamePlural: 'ITGCs',
			placeholderText: 'Create new ITGC or edit and delete existing ITGCs below.',
			deleteEntity: 'Delete ITGC',
			close: '關閉',
			cancel: '取消',
			processIdRequired: 'IT process is required',
			save: '儲存',
			confirm: 'Confirm',
			iTProcesslabel: 'IT process',
			saveAndCloseLabel: '儲存並關閉',
			saveAndCreateLabel: '儲存並建立另一個',
			deleteConfirmLabel: 'Are you sure you want to delete this ITGC? All existing relationships will be removed. This action cannot be undone.',
			operationEffectiveness: 'Operation effectiveness',
			itDesignEffectivenessHeader: 'Design effectiveness',
			itTestingColumnHeader: 'Testing',
			testingTitle: 'Testing',
			frequency: 'Frequency',
			controlOpertaingEffectiveness: 'Operating effectiveness',
			designEffectiveness: 'Design effectiveness',
			frequencyITGC: 'Select frequency',
			nameITGC: 'ITGC name (required)',
			itspNameRequired: 'ITSP name (required)',
			noResultsFound: 'No results found',
			selectITRisk: '選擇技術風險（必選）',
			itRiskRequired: '技術風險（必填）',
			itRiskName: '技術風險',
			inputInvaildCharacters: 'Input cannot include the following string of characters: */:<>\\?|"',
			itControlNameRequired: 'ITGC name is required',
			itgcTaskDescription: 'Perform our designed tests of ITGCs to obtain sufficient appropriate audit evidence of their operating effectiveness throughout the period of reliance.',
			selectITProcess: 'Select IT process (required)',
			itProcessRequired: 'IT process (required)',
			riskNameRequired: '技術風險為必填',
			addModalDescription: 'Enter the ITGC description.',
			editModalDescription: 'Edit the ITGC and its associated attributes.',
			controlDesignEffectiveness: {
				[0]: {
					description: 'Not selected'
				},
				[1]: {
					description: 'Effective'
				},
				[2]: {
					description: 'Ineffective'
				}
			},
			controlTesting: {
				[0]: {
					description: 'Not selected'
				},
				[1]: {
					description: 'Yes'
				},
				[2]: {
					description: 'No'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: 'Not selected'
				},
				[1]: {
					description: 'Effective'
				},
				[2]: {
					description: 'Ineffective'
				}
			}
		},
		ITSP: {
			manageEntity: 'Manage ITSPs',
			searchEntity: 'Search ITSPs',
			createEntity: 'New ITSP',
			editEntity: 'Edit ITSP',
			viewEntity: 'View ITSP',
			inputInvaildCharacters: 'Input cannot include the following string of characters: */:<>\\?|"',
			addModalDescription: 'Enter the ITSP description.',
			editModalDescription: 'Edit the ITSP and its associated attributes.',
			entityName: 'ITSP',
			selectITProcess: 'Select IT process (required)',
			entityNameCaps: 'ITSP',
			processIdRequired: 'IT process is required',
			entityNamePlural: 'ITSPs',
			itspRequired: 'ITSP name is required',
			close: '關閉',
			cancel: '取消',
			iTProcesslabel: 'IT process',
			save: '儲存',
			confirm: 'Confirm',
			saveAndCloseLabel: '儲存並關閉',
			riskNameRequired: '技術風險為必填',
			saveAndCreateLabel: '儲存並建立另一個',
			placeholderText: 'Create new ITSP or edit and delete existing ITSPs below.',
			deleteEntity: 'Delete ITGC',
			deleteConfirmLabel: 'Are you sure you want to delete this ITSP? All existing relationships will be removed. This action cannot be undone.',
			itspTaskDescription: '自行確定關於IT證實查核程序的性質、時間和範圍設計的任務描述，以取得充分、適當的審計證據，證明技術風險在整個依賴期間得到有效應對。<br />當IT證實查核程序在中期日執行時，設計和執行程序，以取得額外的證據，證明我們對從中期程序所涵蓋的期間到期末之間期間的IT風險進行了應對。我們對IT證實查核程序的結果得出結論。',
			operationEffectiveness: 'Operation effectiveness',
			itDesignEffectivenessHeader: 'Design effectiveness',
			itTestingColumnHeader: 'Testing',
			testingTitle: 'Testing',
			frequency: 'Frequency',
			controlOpertaingEffectiveness: 'Operating effectiveness',
			designEffectiveness: 'Design effectiveness',
			frequencyITGC: 'Select frequency',
			nameITGC: 'ITGC name (required)',
			itspNameRequired: 'ITSP name (required)',
			noResultsFound: 'No results found',
			selectITRisk: '選擇技術風險（必選）',
			itRiskRequired: '技術風險（必填）',
			itRiskName: '技術風險',
			itProcessRequired: 'IT process (required)',
			controlDesignEffectiveness: {
				[0]: {
					description: 'Not selected'
				},
				[1]: {
					description: 'Effective'
				},
				[2]: {
					description: 'Ineffective'
				}
			},
			controlTesting: {
				[0]: {
					description: 'Not selected'
				},
				[1]: {
					description: 'Yes'
				},
				[2]: {
					description: 'No'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: 'Not selected'
				},
				[1]: {
					description: 'Effective'
				},
				[2]: {
					description: 'Ineffective'
				}
			},
		}
	},
	[Entity.ITSOApplication]: {
		manageEntity: '管理IT應用程式',
		searchEntity: '搜尋IT應用程式',
		createEntity: '建立IT應用程式',
		entityName: 'IT應用程式',
		entityNameCaps: 'IT應用程式',
		entityNamePlural: 'IT應用程式',
		placeholderText: 'Create new IT applications or edit and delete existing IT applications below.',
		deleteEntity: '刪除IT應用程式',
		deleteConfirmLabel: 'Are you sure you want to delete this IT application? All existing relationships will be removed. This action cannot be undone.'
	},
	[Entity.SCOT]: {
		manageEntity: 'Manage SCOTs',
		searchEntity: 'Search SCOTs',
		createEntity: 'New SCOT',
		entityName: 'SCOT',
		entityNameCaps: 'SCOTs',
		entityNamePlural: 'SCOTs',
		placeholderText: 'Create new SCOTs or edit and delete existing SCOTs below.',
		deleteEntity: 'Delete SCOT',
		deleteConfirmLabel: 'Are you sure you want to delete this SCOT? All existing relationships will be removed. This action cannot be undone.'
	},
	[Entity.SampleItem]: {
		manageEntity: 'Manage sample tags',
		searchEntity: 'Search tags',
		createEntity: 'New tag',
		createManageTagEntity: 'Manage tag groups',
		entityName: 'sample tag',
		entityNamePlural: 'Tags',
		entityNameForTagGroupPlural: 'Tag group',
		placeholderText: "Create new tags or edit and delete existing tags below. If you need to create new tag groups, click on <b>'Manage tag groups'</b>",
		deleteEntity: 'Delete sample tag',
		deleteConfirmLabel: 'Are you sure that you want to delete selected tag? It will be removed from all the samples it is related to. This action cannot be undone.'
	},
	[Entity.SampleTagGroups]: {
		manageEntity: 'Manage sample tag groups',
		searchEntity: 'Search tag groups',
		createEntity: 'New tag group',
		entityName: 'sample tag group',
		entityNameCaps: 'Tag group',
		entityNamePlural: 'Tag groups',
		placeholderText: 'Create new tag groups or edit and delete existing sample tag groups below.',
		deleteConfirmLabel: 'Are you sure that you want to delete selected tag? It will be removed from all the samples it is related to. This action cannot be undone.'
	},
};

export const inherentRiskFactorTypes = [{
	id: 1,
	label: '複雜性',
	displayOrder: 1
},
{
	id: 2,
	label: '主觀不確定性',
	displayOrder: 2
},
{
	id: 3,
	label: '舞弊或錯誤',
	displayOrder: 3
},
{
	id: 4,
	label: '變更',
	displayOrder: 4
},
{
	id: 5,
	label: '科目性質',
	displayOrder: 5
},
{
	id: 6,
	label: '關聯方',
	displayOrder: 6
}
];

export const executionType = [{
	id: 1,
	label: 'PT',
	toolTip: '此案件中的程序[僅限主查團隊]',
	value: '在此案件中[僅限主查團隊]'
},
{
	id: 2,
	label: 'CT',
	toolTip: '其他案件中的程序[僅限組成個體案件團隊]',
	value: '在其他案件中[僅限組成個體案件團隊]'
},
{
	id: 3,
	label: 'PT/CT',
	toolTip: '此案件和其他案件的程序[主查團隊/組成個體案件團隊CT]',
	value: '在此案件和其他案件中[主查團隊/組成個體案件團隊]'
}
];

export const createEditAccountModalLabels = {
	createModalDescription: "Enter the new account details below and select'<b>{0}</b>' to finish. To create another account, select'<b>{1}</b>'.",
	editModalDescription: "編輯下方的科目詳細資訊並選擇'<b>{0}</b>' 完成。",
	close: '關閉',
	cancel: '取消',
	createAccount: '新科目',
	editAccount: '編輯科目',
	newSignificantDisclosure: '新的重大揭露',
	save: '儲存',
	confirm: '確認',
	saveAndCloseLabel: '儲存並關閉',
	saveAndCreateLabel: '儲存並建立另一個',
	accountNameLabel: '科目名稱（必填）',
	accountDesignationLabel: 'Designation',
	accountExecutionTypeLabel: '該科目的程序將在哪個 Canvas 案件中執行並記錄？',
	accountEstimateLabel: '此科目是否受到估計的影響？',
	yes: '是',
	no: '否',
	accountStatementTypeLabel: 'Statement type',
	pspIndexDropdownLabel: 'PSP index (required, select up to 5)',
	removePSPIndexLabel: '刪除 PSP 索引',
	assertionsLabel: '選擇相關聲明',
	accountTypeOptions: AccountType,
	assertionOptions: assertions,
	executionTypeOptions: executionType,
	statementTypeOptions: statementTypes,
	noOptionsMessage: '未找到結果',
	accountNameErrorMsg: 'Required',
	pspIndexErrorMsg: 'Required',
	assertionWarningMessage: 'You cannot make changes to assertion(s) that have a Significant risk, or Fraud risk, or Risk of material misstatement or an estimate related. You need to remove these relations first.',
	confirmChanges: '確認變更',
	executionTypeWarningMessage: 'The changes you are going to save to this account will affect the existing assertions and PSPs, the links will be removed. Are you sure that you want to proceed? This action can not be undone.',
	contentUpdateToastMessage: 'Content update is available for {0}. Please initiate the content update from the Content update page.',
	assertionsRequired: '應至少選擇一個聲明',
	pspIndexDisabledLabel: 'Select up to five PSP Indexes. Deselect one or more options to continue.'
};

export const createEditRiskModalLabels = {
	createModalDescription: "Enter the new risk details below and select'<b>{0}</b>' to finish. To create another risk, select'<b>{1}</b>'.",
	editModalDescription: '編輯下方的風險詳細資訊並選擇“<b>{0}</b>”完成。',
	close: '關閉',
	cancel: '取消',
	createRisk: '新風險',
	editRisk: '編輯風險',
	riskType: '風險類型（必要）',
	riskTypeOptions: [{
		value: 1,
		label: '重大風險'
	},
	{
		value: 2,
		label: '舞弊風險'
	},
	{
		value: 3,
		label: '重大不實表達風險'
	}
	],
	save: '儲存',
	saveAndCloseLabel: '儲存並關閉',
	saveAndCreateLabel: '儲存並建立另一個',
	riskNameLabel: '風險名稱（必要）',
	relatedAccountsAssertionsLabel: '相關科目和聲明（可選）',
	relateAccounts: '相關科目',
	assertionsLabel: '選擇相關聲明',
	riskNameErrorMsg: 'Required',
	riskTypeErrorMsg: 'Required',
	assertionOptions: assertions,
	removeAccountLabel: '刪除科目',
	required: '必須的',
	assertionsRequired: '應至少選擇一個聲明'
};

export const CreateEditMestLabels = {
	createModalTitle: '新個體',
	createModalDescription: "Enter the new entity details below and select'<b>{0}</b>' to finish. To create another entity, select'<b>{1}</b>'.",
	close: '關閉',
	cancel: '取消',
	save: '儲存',
	confirm: 'Confirm',
	primary: 'Primary',
	saveAndCloseLabel: '儲存並關閉',
	saveAndCreateLabel: '儲存並建立另一個',
	entityNameLabel: '個體名稱（必要）',
	entityStandardIndexLabel: '個體標準指標（必要）',
	entityDescriptionLabel: '個體描述',
	entityNameErrorMsg: 'Required',
	entityStandardIndexErrorMsg: 'Required',
	editModalTitle: '編輯個體',
	editModalDescription: "Edit the entity details below and select'<b>{0}</b>' to finish.",
	primaryEntitySelectionLabel: 'Select as the primary entity',
	primaryEntitySelectionMsg: "Only one entity in the engagement can be selected as the primary entity, which will be the determinant for the content delivered to the engagement. An entity will need to be selected as the primary to be able to submit the engagement profile. \'Update content\' permission is required to make or edit the primary entity selection.",
	primaryEntityDisableSelectionLabel: "To change the primary entity designation, select from the \'Edit\' of the entity you wish to designate as primary",
	noAccessLabel: 'Unauthorized. Contact your administrator and try again.',
	primaryEntityConfirmationLabel: 'Primary entity confirmation',
	primaryEntityConfirmationDisplay: '{0} is currently selected as the primary entity. Are you sure you want to change the primary entity?',
	profileV2ChangeNotSubmittedBannerMessage: 'Changes have been made to the profile that will result in content updates. Submit the profile to receive the new content or revert the answers to the previous state.',
};

export const CreateEditITProcessLabels = {
	close: 'Close',
	cancel: 'Cancel',
	yes: '是',
	no: '否',
	delete: 'delete',
	save: '儲存',
	saveAndCloseLabel: '儲存並關閉',
	saveAndCreateLabel: '儲存並建立另一個',
	newITProcessLabel: 'New IT process',
	editITProcessLabel: 'Edit IT process',
	viewITProcessLabel: 'View IT process',
	addDescriptionLabel: "Enter the new IT process details below and select'<b>{0}</b>' to finish. To create another IT process, select'<b>{1}</b>'.",
	editDescriptionLabel: "Edit the IT process details below and select'<b>{0}</b>' to finish.",
	iTProcessNameLabel: 'IT process name (required)',
	confirm: 'Confirm',
	confirmChanges: 'Confirm',
	iTProcessNameErrorMsg: 'Required',
	inputInvaildCharacters: 'Input cannot include the following string of characters: */:<>\\?|"',
	remove: 'Remove'
};

export const CreateEditITRiskLabels = {
	close: 'Close',
	cancel: 'Cancel',
	yes: 'Yes',
	no: 'No',
	delete: 'delete',
	save: 'Save',
	saveAndCloseLabel: 'Save and close',
	saveAndCreateLabel: 'Save and create another',
	newITRiskLabel: '新技術風險',
	editITRiskLabel: '編輯技術風險',
	itRiskNameLabel: '技術風險（必填）',
	confirm: '確認',
	confirmChanges: '確認',
	itRiskNameErrorMsg: 'Required',
	itProcessNotSelectedErrorMsg: 'Required',
	hasNoITGCLabel: '沒有應對技術風險的IT一般控制',
	editModalDescription: '編輯技術風險描述。',
	createModalDescription: '輸入技術風險描述',
	selectITProcess: 'Select IT process (required)',
	noITProcessAvailable: 'No IT processes created',
	relatedITProcessLabel: 'Related IT process',
	inputInvaildCharacters: 'Input cannot include the following string of characters: */:<>\\?|"',
	remove: 'Remove'
};

export const CreateEditEstimateLabels = {
	createModalDescription: "Enter the new estimate details below and select'<b>{0}</b>' to finish. To create another estimate, select'<b>{1}</b>'.",
	editModalDescription: `Edit the estimate details below and select'<b>{0}</b>' to finish.`,
	close: 'Close',
	cancel: 'Cancel',
	save: 'Save',
	saveAndCloseLabel: 'Save and close',
	saveAndCreateLabel: 'Save and create another',
	createModalTitle: 'New estimate',
	editEstimateLabel: 'Edit estimate',
	estimateNameLabel: 'Estimate name (required)',
	analysisPeriodBalance: 'Analysis date balance (required)',
	analysisPeriodDate: 'Analysis date (required)',
	comparativePeriodBalance: 'Comparative date balance (required)',
	comparativePeriodDate: 'Comparative date (required)',
	estimateCategory: 'Estimate category (required)',
	confirm: 'Confirm',
	estimateNameErrorMsg: 'Required',
	analysisPeriodBalanceErrorMsg: 'Required',
	analysisPeriodDateErrorMsg: 'Required',
	comparativePeriodBalanceErrorMsg: 'Required',
	comparativePeriodDateErrorMsg: 'Required',
	estimateCategoryErrorMsg: 'Required',
	remove: 'Remove',
	balanceNOTApplicable: 'Balances not applicable',
	wtDetailPrefixForEstimate: '<p>Complete the estimate walkthrough task.</p>',

	riskLevelOptions: [{
		value: 4,
		label: 'Very low risk'
	},
	{
		value: 5,
		label: 'Lower risk'
	},
	{
		value: 6,
		label: 'Higher risk'
	},
	{
		value: 7,
		label: 'Not selected'
	}
	]
};

export const CreateEditControlLabels = {
	createModalTitle: 'New control',
	editModalTitle: 'Edit control',
	viewModalTitle: 'View control',
	close: 'Close',
	cancel: 'Cancel',
	save: 'Save',
	saveAndCloseLabel: 'Save and close',
	saveAndCreateLabel: 'Save and create another',
	controlNameLabel: 'Control name (required)',
	frequency: 'Frequency',
	controlType: 'Control type',
	frequencyTypeOptions: controlFrequencyType,
	controlTypeOptions: controlTypes,
	designEffectiveness: 'Design effectiveness',
	operatingEffectiveness: 'Operating effectiveness',
	testingLabel: 'Testing',
	lowerRiskLabel: 'Is control lower risk?',
	effective: 'Effective',
	ineffective: 'Ineffective',
	yes: 'Yes',
	no: 'No',
	required: 'Required',
	remove: 'Remove',
	noOptionsMessage: 'No results found',
	disabledTabTooltipMessage: "Select'Control type' as'IT application control' or'IT dependent manual control' to relate IT applications",
	itAppLabels: {
		tabLabel: 'IT apps',
		dropdownLabel: 'Relate IT applications',
		noRelatedItems: 'No related IT applications',
		itApplications: 'IT applications'
	},
	soLabels: {
		tabLabel: 'SOs',
		dropdownLabel: 'Relate service organizations',
		noRelatedItems: 'No related service organizations',
		serviceOrganizations: 'Service organizations'
	},
	controlNameErrorMsg: 'Required',
	createModalDescriptionLabel: "Enter the new control details below and select'<b>{0}</b>' to finish. To create another control, select'<b>{1}</b>'.",
	editModalDescriptionLabel: "Edit the control details below and select'<b>{0}</b>' to finish.",
	viewModalDescriptionLabel: 'View the control and related IT applications and service organizations.',
	wcgwLabel: 'WCGW'
};

export const ITApplicationTypeLabels = [{
	value: 1,
	label: '應用程式/工具'
},
{
	value: 2,
	label: '資料庫'
},
{
	value: 3,
	label: '作業系統'
},
{
	value: 4,
	label: '網路'
},
{
	value: 6,
	label: '未分類'
}
];

export const CreateEditITApplicationLabels = {
	close: 'Close',
	cancel: 'Cancel',
	yes: 'Yes',
	no: 'No',
	delete: 'Delete',
	save: 'Save',
	saveAndCloseLabel: 'Save and close',
	saveAndCreateLabel: 'Save and create another',
	newITApplicationLabel: '建立IT應用程式',
	editITApplicationLabel: '編輯IT應用程式',
	iTApplicationNameLabel: 'IT應用程式名稱',
	confirm: 'Confirm',
	confirmChanges: 'Confirm changes',
	noOptionsMessage: 'No results found',
	iTAppNameErrorMsg: 'Required',
	controls: 'Controls',
	substantive: 'Substantive',
	remove: 'Remove',
	iTApplicationStrategyLabel: 'IT應用程式策略',
	SCOTsLabel: 'SCOT names',
	StrategyLabel: 'Strategy',
	ControlsLabel: 'Controls',
	ControlTypeLabel: 'Type',
	addDescriptionLabel: "在下方輸入新IT應用程式詳情，然後點選'<b>{0}</b>'完成。如想另外建立IT應用程式，請點選'<b>{1}</b>'。",
	editDescriptionLabel: "在下方編輯IT應用程式詳情，然後點選'<b>{0}</b>'完成。",
	scotErrorMessage: '重大交易流程可能並非與IT應用程式無關，因為存在連結的控制。',
	SCOTsLabels: {
		tabLabel: 'SCOTs',
		dropdownLabel: 'Relate SCOTs',
		noRelatedItems: 'No related SCOTs'
	},
	ControlsLabels: {
		tabLabel: 'Controls',
		dropdownLabel: 'Relate Controls',
		noRelatedItems: 'No related Controls'
	},
	strategyType: {
		1: 'Controls',
		2: 'Substantive',
		3: 'Rely',
		4: 'Not Rely'
	},
	controlType: {
		1: 'IT Application',
		2: 'IT Dependent Manual',
		3: 'Manual Prevent',
		4: 'Manual Detect'
	},
	technologyTypeOptions: ITApplicationTypeLabels,
	technologyType: 'Select technology type'
};

export const CreateEditSCOTLabels = {
	createModalTitle: 'New SCOT',
	editModalTitle: 'Edit SCOT',
	viewModalTitle: 'View SCOT',
	createModalDescription: "Enter the new SCOT details below and select'<b>{0}</b>' to finish. To create another SCOT, select'<b>{1}</b>'.",
	editModalDescription: "Edit the SCOT details below and select'<b>{0}</b>' to finish.",
	close: 'Close',
	cancel: 'Cancel',
	save: 'Save',
	saveAndCloseLabel: 'Save and close',
	saveAndCreateLabel: 'Save and create another',
	scotNameLabel: 'SCOT name (required)',
	scotStrategyLabel: 'SCOT strategy',
	scotTypeLabel: 'SCOT type',
	hasEstimateLabel: 'Is this SCOT impacted by an estimate?',
	itAPPUsedLabel: 'Are any IT applications used?',
	routine: 'Routine',
	nonRoutine: 'Non-Routine',
	controls: 'Controls',
	substantive: 'Substantive',
	yes: 'Yes',
	scotNameErrorMsg: 'Required',
	remove: 'Remove',
	noOptionsMessage: 'No results found',
	disabledTabTooltipMessage: "Select'Are any IT applications used?' to relate IT applications",
	itAppLabels: {
		itApplications: 'Related IT applications',
		tabLabel: 'IT apps',
		dropdownLabel: 'Relate IT applications',
		noRelatedItems: 'No related IT applications'
	},
	soLabels: {
		serviceOrganizations: 'Related service organizations',
		tabLabel: 'SOs',
		dropdownLabel: 'Relate service organizations',
		noRelatedItems: 'No related service organizations'
	},
	wtDetailPrefix: '<p>For all routine and non-routine SCOTs and significant disclosure processes, we confirm our understanding every period by performing walkthrough procedures. In addition, for PCAOB audits, we perform walkthrough procedures of estimation SCOTs.<br/>For all SCOTs when we take a controls reliance strategy, and for controls that address significant risks, we confirm that relevant controls have been appropriately designed and implemented. We confirm that our decision to take a controls reliance strategy is still appropriate.<br/><br/>We conclude that our documentation accurately describes the operation of the SCOT and we have identified all appropriate WCGWs, including risks that arise from using IT, and relevant controls (when applicable).<br/><br/> For estimation SCOTs when we use a substantive only strategy, we determine whether our understanding of the estimation SCOT is appropriate based on our substantive procedures.</p>',
};

export const ViewSampleItemLabels = {
	previous: 'Previous',
	next: 'Next',
	sampleDateLabel: 'Sample date',
	attributesHeader: 'Attributes',
	statusHeader: 'Status',
	noAttributesLabel: 'No attributes available.',
	present: 'Present',
	presentWithComments: 'Present with comments',
	notPresent: 'Not present',
	notApplicatable: 'Not applicable',
	naLabel: 'N/A',
	additionDocumentation: 'Additional documentation',
	deleteSampleHeader: 'Delete sample',
	deleteSmapleDescription: 'Are you sure that you want to delete the selected sample? This action cannot be undone.',
	deleteSamplePreText: 'Sample description',
	relateTagModalTitle: 'Relate tags to sample',
	relateTagModalDescription: "Relate one or more tags to the sample. To add a new tag, click <b>'Manage tags'</b>. Tag associations will not be archived but the tags themselves will be archived so they are available for use on rollforward.",
	relateTagTableHeader: 'Tag name',
	relateTagTableSubHeader: 'Tag group',
	tagsCounter: '{0} Tags',
	tagCounter: '{0} Tag',
	relateTagGroupLabel: 'Tag group',
	relateTagSearchPlaceholder: 'Search',
	relateTagClearSearch: 'Clear',
	relateTagShowSelectedOnly: 'Show only related',
	manageTagsLabel: 'Manage tags',
	addTag: 'Add tag',
	supportingDocumentationTitle: 'Supporting documentation',
	dropdownAll: 'All',
	noResultsLabel: 'No results found',
	noDataLabel: 'No data found',
	attributeStatusModalTitle: 'Mark all as present',
	attributeStatusModalCancelButton: 'Cancel',
	attributeStatusModalConfirmButton: 'Save',
	attributeStatusModalDescription: 'Are you sure you want to mark attributes as present? Only attributes that have no status selected will be marked as present.',
	attributeModalDeleteErrorMessage: 'Attribute status cannot be updated. Refresh the page and try again. Contact the Help Desk if the error persists.',
};

export const ShortRiskTypeForAres = {
	1: 'Significant',
	2: 'Fraud',
	3: 'Inherent',
	4: 'Very low risk',
	5: 'Lower risk',
	6: 'Higher risk',
	7: 'Not selected'
};

export const RelateEstimateToAssertionLabels = {
	relateAccountsAndAssertions: 'Relate accounts and assertions',
	relateAccountsToEstimate: 'Relate accounts to estimate',
	accounts: 'Accounts',
	designation: 'Designation',
	relatedAssertions: 'Related assertions',
	accountNameField: 'accountName',
	accountTypeIdField: 'accountTypeId',
	assertionsField: 'assertions',
	executionTypeIdField: 'executionTypeId',
	notSelected: 'Not selected',
	pathField: 'path',
	noAccountsAvailable: 'No accounts available',
	noRelatedAccounts: 'No accounts related to estimate',
	accountId: 'accountId',
	remove: 'Remove'
};

//Send instructions switcher
export const sendIntructionsSwitcherLabels = {

	[sendInstructionsSwitcherIds.groupInstructions]: 'Group instructions',
	[sendInstructionsSwitcherIds.groupRiskAssessment]: 'Group risk assessment'
};

export const RelateEstimateToAccountLabels = {
	relatEstimatesToAccount: 'Relate estimates to account',
	showOnlyRelatedEstimates: 'Show only related estimates',
	noEstimatesResult: labels.noResultsFound,
	noEstimatesLabel: 'No estimates created in the engagement',
	estimateNameHeader: 'Estimate name',
	relatedEstimateCounter: '{0} estimate',
	relatedEstimatesCounter: '{0} estimates',
	relatedAccount: 'Account/disclosure',
	close: labels.close
};

export const RelateAccountsToEstimateLabels = {
	relateAccountsToEstimate: 'Relate accounts to estimate',
	showOnlyRelatedAccounts: 'Show only related accounts',
	noAccountsResult: labels.noResultsFound,
	noAccountsLabel: 'No accounts created in the engagement',
	accountNameHeader: 'Account name',
	relatedAccountCounter: '{0} account',
	relatedAccountsCounter: '{0} accounts',
	relatedEstimate: labels.estimate,
	close: labels.close
};

export const SupportingDocumentationLabels = {
	evidence: 'Evidence',
	priorPeriod: 'Prior period',
	temporaryFiles: 'Temporary files',
	externalDocuments: 'External documents',
	addEvidenceBtn: 'Add evidence',
	addTemporaryFilesBtn: 'Add temporary file',
	notes: 'Notes',
	signOffs: 'Sign-offs',
	name: 'Name',
	supportingDocumentationTitle: 'Supporting documentation',
	temporaryFilesEmptyPlaceholder1: 'No related temporary documents.',
	temporaryFilesEmptyPlaceholder2: 'To relate a temporary document click {addTemporaryFiles}.',
	evidencePlaceholderLine1: 'No related evidence.',
	evidencePlaceholderLine2: 'To relate an evidence click {addEvidenceBtn}.',
	removeFromSample: 'Remove from sample',
	unlink: 'Unlink',
	retailControlEvidenceLabel: 'Did we retain control evidence to support our testing of the attribute(s) for this specific sample item?',
	removeEvidenceModalTitle: 'Remove evidence from sample',
	removeEvidenceModalDesc: 'Are you sure you want to remove all evidence from this sample?',
	removeEvidenceErrorMessage: 'These documents are no longer available in this sample. Refresh the page and try again. Contact the Help Desk if the error persists.'
}

export const deleteSampleItemAttributeModal = {
	modalDescription: 'Are you sure that you want to remove the selection for this attribute? Additional documentation will be deleted.',
	modalTitle: 'Remove selection',
	modalConfirmButton: 'Remove',
	modalCancelButton: 'Cancel',
	additionalDocumentationLabel: 'Additional documentation'
}
export const accountsFilterLabels = [{
	id: accountsFilter.allAccounts,
	label: 'All accounts',
	value: accountsFilter.allAccounts
},
{
	id: accountsFilter.accountsWithRelatedEstimates,
	label: 'Accounts with related estimate',
	value: accountsFilter.accountsWithRelatedEstimates
},
{
	id: accountsFilter.accountsWithoutRelatedEstimates,
	label: 'Accounts without related estimate',
	value: accountsFilter.accountsWithoutRelatedEstimates
}
];

export const changeSampleItemAttributeModal = {
	modalDescription: 'Are you sure that you want to change the selection for this attribute? Additional documentation will be deleted.',
	modalTitle: 'Change selection',
	modalConfirmButton: 'Change',
	modalCancelButton: 'Cancel'
}
export const scotsFilterLabels = [{
	id: scotsFilter.allScots,
	label: 'All SCOTs',
	value: scotsFilter.allScots
},
{
	id: scotsFilter.scotsWithRelatedEstimates,
	label: 'SCOTs with related estimate',
	value: scotsFilter.scotsWithRelatedEstimates
},
{
	id: scotsFilter.scotsWithoutRelatedEstimates,
	label: 'SCOTs without related estimate',
	value: scotsFilter.scotsWithoutRelatedEstimates
}
];

export const CreateEditTagGroupLabels = {
	createModalTitle: 'New sample tag group',
	editModalTitle: 'Edit sample tag group',
	createModalDescription: "Enter tag group details below and select <b>\'Save and close\'</b> to finish. To create another tag group, select <b>\'Save and create another\'</b>.",
	editModalDescription: "Edit the tag group details below and select'<b>{0}</b>' to finish.",
	close: 'Close',
	cancel: 'Cancel',
	save: 'Save',
	saveAndCloseLabel: 'Save and close',
	saveAndCreateLabel: 'Save and create another',
	tagGroupNameLabel: 'Tag group name (required)',
	required: 'Required'
};

export const CreateEditTagLabels = {
	createModalTitle: 'New sample tag',
	editModalTitle: 'Edit sample tag',
	createModalDescription: "Enter tag details below and select'<b>Save and close</b>' to finish. To create another tag, select'<b>Save and create another</b>'.",
	editModalDescription: `Edit the tag details below and select'<b>{0}</b>' to finish.`,
	tagNameLabel: 'Tag name (required)',
	tagGroupNameLabel: 'Tag group name (required)',
	tagColorLabel: 'Color (required)',
	saveAndCloseLabel: 'Save and close',
	saveAndCreateLabel: 'Save and create another',
	cancelLabel: 'Cancel',
	required: 'Required',
	save: 'Save',
	noresultsLabel: 'No tag groups available',
	tagColors: [{
		text: 'Red',
		color: 'red'
	},
	{
		text: 'Orange',
		color: 'orange'
	},
	{
		text: 'Teal',
		color: 'teal'
	},
	{
		text: 'Blue',
		color: 'blue'
	},
	{
		text: 'Purple',
		color: 'purple'
	},
	{
		text: 'Green',
		color: 'green'
	}
	]
};

export const ITProcessFlowLabels = {
	itProcess: 'IT流程',
	technology: '技術',
	technologies: '技術',
	technologyName: '技術名稱',
	supportingTechnologyName: '支援性技術名稱',
	technologyType: '技術類型',
	showOnlyRelated: '僅顯示已關聯',
	technologiesCounter: '{0}技術',
	technologyCounter: '{0}技術',
	supportingTechnologyLabel: '支援性技術',
	relatedITAppNoDataPlaceholder: '沒有與IT流程相關的技術。',
	relateTechnology: '相關技術',
	supportingITPAppNoDataPlaceholder: '沒有支援IT流程的技術',
	itRiskHeader: 'IT風險',
	itgcHeader: 'IT一般控制',
	itspHeader: 'IT證實查核程序',
	noDataPlaceholderITGC: '必須至少辨識一個IT一般控制，或明確IT風險沒有IT一般控制。建立{createNewITGC}、{relateITGC}或明確存在應對IT風險的{noITGC}。',
	noDataPlaceholderITSP: '如果我們將IT一般控制評估為無效，或確定不存在應對IT風險的IT一般控制，則執行IT證實測試程序，以合理保證無效IT一般控制的相關IT流程內的風險未被不當利用。建立{createNewITSP}或{relateITSP}。',
	noRecordsFound: '沒有對此IT流程辨識到IT風險',
	noITGCPlaceholder: '沒有應對該IT風險的IT一般控制。',
	relateSupportingTechnology: '相關支援性技術',
	relatedTechnologiesNotAvailable: '相關技術不適用於此檔案',
	supportingTechNotAvailable: '支援性技術不適用於此檔案',
	relatedITRisksNotAvailable: '相關IT風險不適用於本檔案',
	relateITGC: 'Relate ITGCs',
	itRisk: 'IT風險',
	itgcCounter: '{0}IT一般控制',
	itgcsCounter: '{0}IT一般控制',
	itgcName: 'IT一般控制名稱',

};

export const ITRisksFlowLabels = {
	itRisk: 'IT風險',
	relatedITRiskNoDataPlaceholder: '沒有與IT流程相關的IT風險。',
	newITRisk: '新IT風險',
	relatedITRisksNotAvailable: '相關IT風險不適用於本檔案',
	deleteConfirmLabel: '是否確定刪除已選定IT風險? 此操作無法撤銷。',
	deleteITRisk: '刪除IT風險',
	CreateITFlowITRiskLabels: {
		close: '關閉',
		cancel: '取消',
		yes: '是',
		no: '否',
		delete: '刪除',
		save: '儲存',
		saveAndCloseLabel: '儲存並關閉',
		saveAndCreateLabel: '儲存並另外建立',
		newITRiskLabel: '新IT風險',
		itRiskNameLabel: 'IT風險名稱（必填）',
		itRiskCharactersLabel: '字元',
		itRiskOfLabel: '的',
		confirm: '確認',
		confirmChanges: '確認',
		itRiskNameErrorMsg: '必填',
		itProcessNotSelectedErrorMsg: '必填',
		hasNoITGCLabel: '沒有應對該IT風險的IT一般控制',
		createModalDescription: '在下方輸入IT詳請，然後選擇<b>“儲存並關閉”</b>完成。若要建立另一個IT風險，請選擇“<b>儲存並另外建立</b>”。',
		relatedITProcessLabel: 'IT流程',
		inputInvaildCharacters: '輸入資訊不能包含以下字串：*/:<>\\?|"',
		remove: 'Remove',
		editModalDescription: 'Edit the IT risk details below and select"<b>Save</b>" to finish.',
		editITRiskLabel: 'Edit IT risk'
	}
};

export const ITProcessListingLabels = {
	all: '全部',
	manageChange: '管理變更',
	manageAccess: '管理存取權限',
	manageSecuritySettings: '管理安全設置',
	itOperations: 'IT運行',
	systemImplementation: '系統實施',
	category: '類別',
	uncategorized: '未分類',
	technologies: '技術',
};

export const ITProcessQuickFilterOptions = {
	0: ITProcessListingLabels.all,
	1: ITProcessListingLabels.manageChange,
	2: ITProcessListingLabels.manageAccess,
	3: ITProcessListingLabels.manageSecuritySettings,
	4: ITProcessListingLabels.itOperations,
	5: ITProcessListingLabels.systemImplementation
}

// Relate Technology Modal
export const RelateTechnologyModalLabels = {
	relateTechnology: '連結技術',
	relatedTechnologiesDescription: '技術名稱',
	supportingTechnologyName: '支援性技術名稱',
	technology: '{0} technology',
	technologies: '{0} technologies'
};

export const AccountStandardROMMListingLabels = {
	accountRisksNotAvailableForDocument: '該檔案不支援科目風險分析。',
	noRelatedObject: '無相關項目。關聯一個項目以開始。',
	noResultsFound: '無可用風險',
	acceptedText: '已接受',
	rejectedText: '已拒絕',
	allRisksRejected: '已拒絕所有風險',
	relevantAssertions: '相關聲明',
	rejectLabel: '拒絕',
	acceptLabel: '接受',
	rejectionRationaleLabel: '拒收理由',
	rejectionCategoryText: '拒絕類別',
	editRejectionRationaleText: '編輯拒絕理由',
	rejectionRationalePlaceholder: "Are you sure you want to reject the selected risk? Enter the details below and select <strong>\'Reject\'</strong>.",
	cancel: 'Cancel',
	rejectionRationaleTextAreaPlaceholder: 'Rationale (required)',
	rejectionCategoryDropdownPlaceholder: 'Rejection category (required)',
	required: 'Required',
	preRejectedText: 'Pre-rejected',
	additionalContextLabel: 'Additional context',
	additionalContextwhyRiskShouldBeRejected: 'Click {0} to add additional context specific to this client for why this risk should be rejected.',
	hereLink: 'here',
	editAdditionalContextText: 'Edit additional context'
}

export const RejectionCategory = [{
	id: 1,
	label: '可能性-不存在發生的合理可能性（不考慮控制）'
},
{
	id: 2,
	label: '規模-潛在不實表達不重大'
},
{
	id: 3,
	label: '風險對本案件不重大'
},
{
	id: 4,
	label: '重大不實表達風險已於另一個案件中連結（僅限集團查核）'
},
]

export const formLabels = {
	required: labels.required,
	maxLength: labels.maxLength
};

export const paginationLabels = {
	show: labels.pagingShowtext,
	first: '首頁',
	last: '最後一頁',
	prev: '上一頁',
	next: '下一頁'
};
