/**
 * Validation.js
 * Created by calhosh on 7/9/2020.
 */

import React, {useState, useContext, useMemo, useEffect} from 'react';
import {useSelector} from 'react-redux';
import {Link} from 'react-router-dom';
import styled from 'styled-components';
import EllipseControl from '../../EllipsesControl/EllipsesControl';
import {currentResource, labels} from '../../../../util/utils';
import Utility from '../../../../util/utilityfunctions';
import ConfirmationModal from '../../Modal/Modal';
import {entities, documentTypes, clientSideEventNames, validationTypes, formBodyType} from '../../../../util/uiconstants';
import {DocumentTypeIdContext} from '../../../Main/Forms/CanvasFormContainer';
import {
	itFlowValidations,
	guidedWorkFlowValidationsDisplay,
	GetGuidedWorkFlowValidationCount,
	sortedGuidedWorkFlowValidations
} from './ValidationUtility';
import ITFlowValidationModalBody from '../../CanvasForm/Common/ITFlowValidationModalBody';
import Icon from '../../BaseComponentWrappers/MotifIcon';
import DropdownItem from '../../BaseComponentWrappers/MotifDropdownItem';
import Dropdown from '../../BaseComponentWrappers/MotifDropdown';
import {useUserPreferences} from '../../../../util/customHooks';
import {getTaskDocumentByDocumentId} from '../../../../selectors/Ares/selectors';
import ALRAIndividualAccountCreator from '../../../../components/Common/ALRAIndividualAccountCreator/ALRAIndividualAccountCreator';
import {IconoirNavArrowDown, IconoirXmark} from '@ey-xd/motif-icon';
import {IconoirCustomValidation} from '@ey/motif-react-wrapper/assets/icons/custom';
import env from '../../../../util/env';

export default function ValidationControl(props) {
	const {
		documentId,
		incompleteResponseCount,
		unresolvedCommentCount,
		inconsistentFormsCount,
		itFlowValidationCount,
		hideIcon,
		railExpanded,
		darkBg,
		clearComments,
		entityId,
		entityUId,
		changesNotSubmittedCount,
		isTask = false,
		showValidationDropDonIcon = true,
		documentSignOffs,
		itFlowFormValidations = [],
		guidedWorkFlowValidationDetails = [],
		styleName,
		displayFor,
		setShowCreateDocumentModal,
		isReadOnly = false,
		bodyTypeId,
		createMissingDocumentIsReadOnly,
		isSharedExternally = false,
		disableClick = false
	} = props;

	const [isOpen, setIsOpen] = useState(false),
		[showIsaFormsValidation, setShowIsaFormsValidationModal] = useState(false),
		[showValidationsModal, setShowValidationsModal] = useState(false),
		[currentEntityUid, setCurrentEntityUid] = useState(entityUId),
		[modalTitle, setModalTitle] = useState(''),
		[modalDescription, setModalDescription] = useState(''),
		[confirmButtonTitle, setConfirmButtonTitle] = useState(''),
		[showOnlyCloseButton, setShowOnlyCloseButton] = useState(false),
		[showButtons, setShowButton] = useState(false),
		[isExternallyShared, setIsExternallyShared] = useState(isSharedExternally);

	const userPreferences = useUserPreferences(),
		documentTypeId = useContext(DocumentTypeIdContext);
	const formData = useSelector((storeState) => getTaskDocumentByDocumentId(storeState, documentId)),
		engagement = useSelector((store) => store.engagement);

	const handleToggle = (event) => {
		setCurrentEntityUid(event.currentTarget.id.substring(0, event.currentTarget.id.indexOf('_')));
		setIsOpen(!isOpen);
		window.dispatchEvent(new CustomEvent(clientSideEventNames.closeCommentsValidationModal));
	};

	const clearCommentValidations = () => {
		if (userPreferences && (userPreferences.isCEAPUser === true || userPreferences.isGuestUser === true)) {
			setShowValidationsModal(true);
			setModalTitle(labels.reviewRoleCloseCommentsTitle);
			setModalDescription(labels.reviewRoleCloseCommentsDesc);
			setShowOnlyCloseButton(true);
		} else {
			setShowValidationsModal(true);
			setModalTitle(labels.closeComments);
			setModalDescription(setClearCommentsDescription());
			setConfirmButtonTitle(labels.closeAllComments);
			setShowOnlyCloseButton(false);
			setShowButton(true);
		}
	};

	const showProfileV2Validations = () => {
		setModalTitle(labels.profileV2Validation);
		setModalDescription(getChangeNotSubmittedResource());
		setShowOnlyCloseButton(true);
		setShowValidationsModal(!showValidationsModal);
		setShowButton(false);
	};

	const getChangeNotSubmittedResource = () => {
		if (documentTypeId === documentTypes.CANVASFORMEVIDENCE || documentTypeId === documentTypes.CANVASFORMTEMPORARY) {
			return labels.incompleteResponse;
		} else {
			return documentTypeId === documentTypes.PROFILEV2
				? labels.profileV2ValidationModalDescription
				: documentTypeId === documentTypes.INDEPENDENCEFORMINDIVIDUAL &&
				  userPreferences.id !== formData?.independenceInformation?.userId
				? labels.independenceValidationForOthersForm
				: labels.independenceValidationForOwnForm;
		}
	};

	const confirmActionClick = () => {
		setShowValidationsModal(false);
		if (modalTitle === labels.closeComments) {
			clearComments(entityId, currentEntityUid);
		}
	};

	const setClearCommentsDescription = () => {
		switch (entityId) {
			case entities.Document:
				if (Utility.isDocumentTypeFit(documentTypeId)) {
					return labels.closeCommentsDescription.replaceAll('{0}', labels.activity);
				} else {
					return labels.closeCommentsDescription.replaceAll('{0}', labels.CanvasForm);
				}
			case entities.FormHeader:
				return labels.closeCommentsDescription.replaceAll('{0}', labels.header);
			case entities.FormSection:
				return labels.closeCommentsDescription.replaceAll('{0}', labels.section);
			case entities.FormBody:
				return labels.closeCommentsDescription.replaceAll('{0}', labels.body);
		}
	};

	const validationCount = () => {
		let count = 0;
		count += incompleteResponseCount || 0;
		count += unresolvedCommentCount || 0;
		count += changesNotSubmittedCount || 0;
		count += itFlowValidationCount || 0;
		count += inconsistentFormsCount || 0;
		count += GetGuidedWorkFlowValidationCount(guidedWorkFlowValidationDetails);
		return count;
	};

	const openMissingDocumentModal = () => {
		if (displayFor === 'body') {
			setShowCreateDocumentModal && setShowCreateDocumentModal(true);
		}
	};

	const onCreationFinished = () => {
		window.dispatchEvent(new CustomEvent(clientSideEventNames.getValidations));
		window.dispatchEvent(new CustomEvent(clientSideEventNames.getAccountsForBody));
		window.dispatchEvent(new CustomEvent(clientSideEventNames.hideHasMissingDocuments));
	};

	const accountMissingDocumentsValidation = guidedWorkFlowValidationDetails?.find(
		(x) =>
			x.validationId === validationTypes.AccountWithoutIndividualRiskAssessmentForm ||
			x.validationId === validationTypes.AccountWithoutIndividualAnalyticForm
	);
	const skipConfirmation = false;
	let labelAccountMissingDocuments;
	if (accountMissingDocumentsValidation) {
		const validationLabel = currentResource.GuidedWorkFlowValidationTypeResourceMapping.find(
			(t) => t.validationId === accountMissingDocumentsValidation.validationId
		).label;

		labelAccountMissingDocuments =
			accountMissingDocumentsValidation?.validationCount > 1
				? validationLabel.replaceAll('{0}', 's')
				: validationLabel.replaceAll('{0}', '');
	}

	const accountMissingDocumentsRender = useMemo(() => {
		return (
			<ALRAIndividualAccountCreator
				text={
					<>
						<section className="dropdownValidationName">{labelAccountMissingDocuments}</section>
						<section className="dropdownValidationCount">
							<EllipseControl
								disabled={createMissingDocumentIsReadOnly ?? isReadOnly}
								content={accountMissingDocumentsValidation?.validationCount}
								tooltip={accountMissingDocumentsValidation?.validationCount}
								noOfLines={1}
							/>
						</section>
					</>
				}
				skipConfirmation={skipConfirmation}
				onCreationFinished={onCreationFinished}
				bodyTypeId={bodyTypeId}
			/>
		);
	}, [accountMissingDocumentsValidation]);

	useEffect(() => {
		if (formData?.isSharedExternally) {
			setIsExternallyShared(true);
		}
	}, [formData]);

	const getValidationName = () => {
		if(documentTypeId === documentTypes.IT_PROCESS_WALKTHROUGH_INDIVIDUAL && bodyTypeId === formBodyType.ITProcessUnderstandRelateITApps) {
			return labels.itProcessWithoutRelatedTechnology;
		} else {
			return labels.incompleteResponse;
		}
	}
	return (
		(documentTypeId === documentTypes.INDEPENDENCEFORMINDIVIDUAL ||
			documentTypeId === documentTypes.PROFILEV2 ||
			documentTypeId === documentTypes.MULTI_ENTITY_INDIVIDUAL_PROFILE_V2_NO_SIGN_OFF_REQUIRED ||
			documentTypeId === documentTypes.INDEPENDENCEFORMTEMPLATE ||
			Utility.isDocumentCanvasForm(documentTypeId) ||
			documentTypeId === documentTypes.ESTIMATESDOCUMENT ||
			documentTypeId === documentTypes.GUIDEDWORKFLOWV2 ||
			documentTypeId === documentTypes.IT_PROCESS_WALKTHROUGH_INDIVIDUAL ||
			isTask) &&
		!(isExternallyShared && userPreferences.isGuestUser) &&
		(incompleteResponseCount || 0) +
			(unresolvedCommentCount || 0) +
			(changesNotSubmittedCount || 0) +
			(inconsistentFormsCount || 0) +
			(itFlowValidationCount || 0) +
			GetGuidedWorkFlowValidationCount(guidedWorkFlowValidationDetails) >
			0 && (
			<>
				<StyledMotifDropdownPortal
					handleClickOutside={() => {
						setIsOpen(false);
					}}
					handleOnClose={() => {
						setIsOpen(false);
					}}
					//closeOnOutsideClick={false}
					open={isOpen}
					portalClassName="showHiddenValidationsWrapper"
					className={`motif-dropdown-portal StyledMotifDropdownPortal ${isOpen ? 'StyledMotifDropdownPortalOpen' : ''}`}
					usePortal
					trigger={
						<section className="motif-dropdown-trigger">
							<span
								id={`${currentEntityUid}_validation`}
								role="button"
								key={validationCount()}
								className={`ValidationIcon ${darkBg ? 'ValidationIconDark' : ''}`}
								title={labels.validations}
								onClick={(e) => {
									handleToggle(e);
								}}
							>
								<>
									<span className="validationcount">
										<EllipseControl
											content={validationCount()}
											id={''}
											tooltip={validationCount()}
											displayId={''}
											noOfLines={1}
										/>
									</span>
									<span className={`ValidationImg ${hideIcon && !railExpanded ? 'hide' : ''}`}>
										<Icon
											src={IconoirCustomValidation}
											title={labels.validation}
											className={'medium ValidationImgIcon'}
											size={'16'}
										/>
									</span>
									{isTask && showValidationDropDonIcon && <Icon src={IconoirNavArrowDown} />}
								</>
							</span>
						</section>
					}
					children={
						<StyledvalidationDropdownWrapper className={`StyledvalidationDropdownWrapper ${styleName}`}>
							<DropdownItem
								className="validationDropdownHeadingItem"
								children={
									<section className="validationDropdownHeading">
										<EllipseControl
											content={labels.validations}
											id={''}
											tooltip={labels.validations}
											displayId={''}
											noOfLines={1}
										/>
										<Icon
											src={IconoirXmark}
											title={labels.close}
											onClick={(e) => {
												setIsOpen(false);
												e.stopPropagation();
											}}
										/>
									</section>
								}
							/>
							<section className="validationDropdownContainerWrapper customScrollbar">
								{incompleteResponseCount > 0 && (
									<DropdownItem
										className={'validationDropdownContainer validationDropdownContainerDisabled'}
										children={
											<>
												<section className="dropdownValidationName">
													<EllipseControl
														disabled={isReadOnly}
														content={getValidationName()}
														id={''}
														tooltip={getValidationName()}
														displayId={''}
														noOfLines={1}
													/>
												</section>
												<section className="dropdownValidationCount">
													<EllipseControl
														disabled={isReadOnly}
														content={incompleteResponseCount}
														id={''}
														tooltip={incompleteResponseCount}
														displayId={''}
														noOfLines={1}
													/>
												</section>
											</>
										}
									/>
								)}
								{unresolvedCommentCount !== undefined && unresolvedCommentCount > 0 && (
									<DropdownItem
										className={`validationDropdownContainer unresolvedCommentCount ${
											isReadOnly || userPreferences.permissions.hasIndependenceOnlyRole
												? 'validationDropdownContainerDisabled'
												: ''
										}`}
										children={
											<>
												<section
													className={`dropdownValidationName ${!disableClick ? ' cursorPointer' : 'disabledClick'} ${userPreferences.permissions
														.hasIndependenceOnlyRole && 'disabled'}`}
													onClick={!disableClick ? () => !isReadOnly && clearCommentValidations() : null}
												>
													<EllipseControl
														disabled={isReadOnly || userPreferences.permissions.hasIndependenceOnlyRole}
														content={labels.unresolvedComments}
														id={''}
														tooltip={labels.unresolvedComments}
														displayId={''}
														noOfLines={1}
													/>
												</section>
												<section
													className={`dropdownValidationCount ${!disableClick ? ' ' : 'disabledClick'} ${userPreferences.permissions.hasIndependenceOnlyRole &&
														'disabled'}`}
													onClick={!disableClick ? () => !isReadOnly && clearCommentValidations() : null}
												>
													<EllipseControl
														disabled={isReadOnly || userPreferences.permissions.hasIndependenceOnlyRole}
														content={unresolvedCommentCount}
														id={''}
														tooltip={unresolvedCommentCount}
														displayId={''}
														noOfLines={1}
													/>
												</section>
											</>
										}
										disabled={userPreferences.permissions.hasIndependenceOnlyRole}
									/>
								)}
								{accountMissingDocumentsValidation !== undefined && (
									<DropdownItem
										className={`validationDropdownContainer ${
											createMissingDocumentIsReadOnly ?? isReadOnly ? 'validationDropdownContainerDisabled' : ''
										}`}
									>
										<>{accountMissingDocumentsRender}</>
									</DropdownItem>
								)}
								{changesNotSubmittedCount !== undefined && changesNotSubmittedCount > 0 && (
									<DropdownItem
										className={`validationDropdownContainer ${isReadOnly ? 'validationDropdownContainerDisabled' : ''}`}
										children={
											<>
												<section
													className="dropdownValidationName cursorPointer"
													onClick={() => !isReadOnly && showProfileV2Validations()}
												>
													<EllipseControl
														disabled={isReadOnly}
														content={labels.profileV2Validation}
														id={''}
														tooltip={labels.profileV2Validation}
														displayId={''}
														noOfLines={1}
													/>
												</section>
												<section className="dropdownValidationCount">
													<EllipseControl
														disabled={isReadOnly}
														content={1}
														id={''}
														tooltip={1}
														displayId={''}
														noOfLines={1}
													/>
												</section>
											</>
										}
									/>
								)}
								{inconsistentFormsCount !== undefined && inconsistentFormsCount > 0 && (
									<DropdownItem
										className={`validationDropdownContainer ${isReadOnly ? 'validationDropdownContainerDisabled' : ''}`}
										children={
											<>
												<section className="dropdownValidationName cursorPointer">
													<EllipseControl
														disabled={isReadOnly}
														content={labels.inconsistentForms}
														id={''}
														tooltip={labels.inconsistentForms}
														displayId={''}
														noOfLines={1}
													/>
												</section>
												<section className="dropdownValidationCount">
													<EllipseControl
														disabled={isReadOnly}
														content={inconsistentFormsCount}
														id={''}
														tooltip={inconsistentFormsCount}
														displayId={''}
														noOfLines={1}
													/>
												</section>
											</>
										}
									/>
								)}
								{itFlowValidationCount !== undefined &&
									itFlowValidationCount > 0 &&
									itFlowFormValidations.length > 0 &&
									itFlowFormValidations.map((validation) => {
										return <>{itFlowValidations(validation, undefined, true)}</>;
									})}
								{guidedWorkFlowValidationDetails !== undefined &&
									guidedWorkFlowValidationDetails.length > 0 &&
									sortedGuidedWorkFlowValidations(guidedWorkFlowValidationDetails).map((validation) => {
										if (
											validation.validationId !== validationTypes.AccountWithoutIndividualAnalyticForm &&
											validation.validationId !== validationTypes.AccountWithoutIndividualRiskAssessmentForm
										) {
											return (
												<>
													{guidedWorkFlowValidationsDisplay(
														validation,
														isReadOnly,
														openMissingDocumentModal,
														displayFor,
														bodyTypeId
													)}
												</>
											);
										}
									})}
							</section>
							<DropdownItem
								className="validationDropdownFooterItem"
								children={
									<Link to={env.getURL('validation_help_web')} className="validationDropdownFooter" target="_blank">
										<EllipseControl
											content={labels.validationNavHelp}
											id={''}
											tooltip={labels.validationNavHelp}
											displayId={''}
											noOfLines={1}
										/>
									</Link>
								}
							/>
							<ConfirmationModal
								show={showIsaFormsValidation}
								title={modalTitle}
								onOkClick={() => {
									setShowIsaFormsValidationModal(false);
								}}
								onHide={() => {
									setShowIsaFormsValidationModal(false);
								}}
								closeTitle={labels.closeLabel}
								closeBtnTitle={labels.closeLabel}
								modalsize="small"
								modalbodystyle="UnresolvedCommentsConfirmationModalBody"
								modalContainerClass="UnresolvedCommentsConfirmationModalContainer"
							>
								<p>{modalDescription}</p>
								<ITFlowValidationModalBody engagement={engagement} displayLanguage={userPreferences?.displayLanguage} />
							</ConfirmationModal>
						</StyledvalidationDropdownWrapper>
					}
				/>
				<ConfirmationModal
					show={showValidationsModal}
					modalsize="small"
					showButtons={showButtons}
					showOnlyCloseButton={showOnlyCloseButton}
					confirmBtnTitle={confirmButtonTitle}
					onOkClick={confirmActionClick}
					onHide={() => {
						setShowValidationsModal(false);
					}}
					closeBtnTitle={labels.cancelLabel}
					closeTitle={labels.cancelLabel}
					title={modalTitle}
				>
					<p>{modalDescription}</p>
				</ConfirmationModal>
			</>
		)
	);
}

const StyledvalidationDropdownWrapper = styled.section`
	padding: var(--px-16) 0;
	width: var(--px-390);
	box-shadow: 0 0 var(--px-6) 0 var(--neutrals-1000) 33 !important;
	background-color: var(--neutrals-00white) !important;
	border-radius: var(--px-4);
	.motif-dropdown-item {
		padding: 0;
		&:focus-visible {
			outline: none !important;
			background: transparent;
		}
		&.validationDropdownHeadingItem {
			line-height: var(--px-16);
			min-height: unset;
			height: var(--px-16);
			margin-bottom: var(--px-6);
			padding: 0 var(--px-16);
			color: var(--neutrals-900) !important;
			width: 100%;
			.validationDropdownHeading {
				display: flex;
				font-weight: 600;
				font-size: var(--px-12);
				line-height: var(--px-16);
				padding: 0;
				width: 100%;
				cursor: pointer;
				.ellipses {
					display: inline-flex;
					width: calc(100% - var(--px-30));
				}
				.motif-icon {
					justify-content: flex-end;
					width: var(--px-30);
					display: inline-flex;
					margin-right: 0;
					svg {
						fill: var(--neutrals-900) !important;
						width: var(--px-16) !important;
						height: var(--px-16) !important;
					}
				}
			}
		}
		&.validationDropdownFooterItem {
			line-height: var(--px-16);
			min-height: unset;
			height: var(--px-16);
			margin-bottom: var(--px-6);
			padding: 0 var(--px-16);
			color: var(--neutrals-900) !important;
			width: 100%;
			.validationDropdownFooter {
				display: flex;
				font-weight: 400;
				font-size: var(--px-12);
				line-height: var(--px-18);
				color: var(--blue-600);
				margin-top: var(--px-12);
				padding: 0;
				width: 100%;
				.ellipses {
					display: inline-flex;
					width: calc(100% - var(--px-30));
				}
			}
		}
		&.validationDropdownContainer {
			margin: var(--px-6) var(--px-16);
			width: calc(100% - var(--px-32));
			min-height: var(--px-20);
			height: 100%;
			color: var(--neutrals-900) !important;
			display: flex;
			align-items: flex-start;
			&.disabled {
				cursor: not-allowed;
			}
			.dropdownValidationName {
				cursor: pointer;
				display: inline-flex;
				width: calc(100% - var(--px-30));
				font-weight: 300;
				font-size: var(--px-12);
				line-height: var(--px-20);
				margin-right: 0;
				padding-right: var(--px-5);
				padding-left: 0;
				&:hover {
					text-decoration: underline;
				}
				&.ALRAValidationName {
					width: 100%;
					padding-right: 0;
					&:hover {
						text-decoration: none;
					}
				}
				&.disabled {
					cursor: not-allowed;
					pointer-events: none;
					color: var(--neutrals-700) !important;
					opacity: 0.5;
				}
				&.disabledClick {	 
					cursor: default !important;
					pointer-events: none;
				}   				
			}
			.dropdownValidationCount {
				display: inline-flex;
				width: var(--px-30);
				font-weight: 300;
				font-size: var(--px-12);
				line-height: var(--px-20);
				justify-content: flex-end;
				padding-right: 0;
				.ellipses {
					justify-content: right;
				}
				&.disabled {
					cursor: not-allowed;
					pointer-events: none;
					color: var(--neutrals-700) !important;
					opacity: 0.5;
				}
				&.disabledClick {	 
					cursor: default !important;
					pointer-events: none;
				}                   
			}
			&:hover,
			&:focus {
				color: var(--neutrals-900) !important;
				background: none !important;
				cursor: default;
			}
			&.missingvalidationDropdownContainer {
				height: auto;
				.dropdownValidationName {
					cursor: default;
					&:hover,
					&:focus {
						text-decoration: none;
					}
				}
			}
			&.validationDropdownContainerDisabled {
				.dropdownValidationName,
				.dropdownValidationCount {
					cursor: default;
					&.disabled {
						cursor: default;
						color: var(--neutrals-900) !important;
						opacity: 1;
					}
					.ellipses {
						&.disabled {
							cursor: default;
							div {
								cursor: default;
							}
						}
					}
					&:hover {
						text-decoration: none;
						color: var(--neutrals-900) !important;
						opacity: 1;
					}
				}
			}
		}
		&:hover {
			background: none !important;
			cursor: default;
		}
	}
	&.taskControlValidation {
		.validationDropdownHeadingItem {
			&:hover {
				background: transparent;
				cursor: default;
			}
		}
		.validationDropdownContainer {
			&:hover {
				background: transparent;
				cursor: default;
			}
		}
		.unresolvedCommentCount {
			&:hover {
				background: transparent;
				cursor: pointer;
			}
		}
	}
	.validationDropdownContainerWrapper {
		max-height: var(--px-130);
		min-height: var(--px-36);
	}
`;

const StyledMotifDropdownPortal = styled(Dropdown)`
	&.motif-dropdown-portal {
		height: 100%;
		.motif-dropdown-menu {
			width: var(--px-320);
		}
		.ValidationIcon {
			margin: 0;
			padding-right: var(--px-7);
			color: var(--neutrals-900);
			font-size: var(--px-14);
			min-width: var(--px-60);
			cursor: pointer;
			display: flex;
			align-items: center !important;
			justify-content: center !important;
			height: var(--px-24);
			.ValidationImg {
				display: flex;
			}
			svg {
				margin: 0;
				vertical-align: middle;
				color: var(--red-600);
				width: var(--px-16) !important;
				height: var(--px-16) !important;
			}
			&.loading-validation {
				background-color: var(--neutrals-300);
				margin: 0;
			}
			.validationcount {
				padding: 0 var(--px-5);
				color: var(--red-600);
				display: inline-block;
				max-width: var(--px-60);
				font-size: var(--px-12) !important;
				font-weight: 400 !important;
				line-height: var(--px-18) !important;
			}
			&.ValidationIconDark {
				svg {
					color: var(--neutrals-00white);
				}
				.validationcount {
					color: var(--neutrals-00white);
				}
			}
		}
		.validationDropdownHeading {
			display: flex;
			font-size: var(--px-12);
			font-weight: bold;
			padding: var(--px-10) 0 0;
			width: 100%;
			cursor: pointer;
			.motif-icon {
				margin-right: 0;
				svg {
					width: var(--px-20) !important;
					height: var(--px-20) !important;
				}
			}
		}
		.validationDropdownContainer {
			padding: var(--px-5) 0;
			width: 100%;
			min-height: var(--px-20);
			height: var(--px-30);
			.dropdownValidationName {
				cursor: pointer;
				display: inline-flex;
				width: calc(100% - var(--px-30));
				font-size: var(--px-12);
				margin-right: 0;
				padding-right: var(--px-5);
				padding-left: var(--px-2);
				&:hover {
					text-decoration: underline;
				}
			}
			.dropdownValidationCount {
				display: inline-flex;
				width: var(--px-30);
				font-size: var(--px-12);
				justify-content: flex-end;
				color: var(--red-600);
				.ellipses {
					justify-content: right;
				}
			}
		}
		.dropdownValidationName {
			display: inline-flex;
			width: calc(100% - var(--px-50));
			font-size: var(--px-12);
			margin-right: var(--px-10);
			padding-left: var(--px-2);
			&:hover {
				text-decoration: underline;
			}
		}
		.dropdownValidationCount {
			cursor: pointer;
			display: inline-flex;
			width: var(--px-40);
			font-size: var(--px-12);
			justify-content: flex-end;
			color: var(--red-600);
			.ellipses {
				justify-content: right;
			}
		}
	}
`;
