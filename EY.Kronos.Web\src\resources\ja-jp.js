/* eslint-disable prettier/prettier,max-len */

import {
	Entity,
	GALinkStatus,
	confidentialityTypes,
	currencyType,
	gaRegion,
	gaRoleTypes,
	gaScopeType,
	notesFilter,
	pointOfContactTypes,
	validationTypes,
	KnowledgeSectionIds,
	sendInstructionsSwitcherIds,
	accountsFilter,
	scotsFilter,
	rejectionType
} from '../util/uiconstants';

/**
 * Created by calhosh on 4/14/2017.
 * JA JP resource file
 */
export const labels = {
	addEvidenceBtn: 'エビデンスの追加',
	multipleDocuments: '複数の文書',
	invalidEngagementId: 'エンゲージメントIDが無効です。ページを更新して、再試行してください。エラーが解消しない場合は、ヘルプデスクに連絡してください。',
	newComponent: '新しい構成単位',
	workingOffline: 'オフライン作業',
	syncInProgress: '同期中',
	prepareOffline: 'オフライン用のデータ生成中',
	connectionAvailable: '接続可能',
	training: 'トレーニング',
	clickForOptions: 'クリックしてその他のオプションを表示',
	navReviewNotes: 'レビューノート',
	navHeaderManageTeam: 'チームの管理',
	navManageGroup: 'グループの管理',
	manageObjects: 'オブジェクトの管理',
	navCRASummary: 'CRAサマリー',
	navAuditPlan: '監査計画',
	navWorkPlan: '作業計画',
	navSEM: 'Substantive evaluation matrix',
	navFindings: '発見事項',
	navContentUpdates: 'コンテンツアップデート',
	navCanvasFormUpdates: 'Canvasフォームのアップデート',
	navCopyHub: 'コピーハブ',
	navCopyHubNew: 'コピーハブNEW',
	navArchiveChecklist: 'アーカイブチェックリスト',
	navExportHub: 'エクスポートハブ',
	navReporting: 'レポーティング',
	navHelp: '一般的なヘルプ',
	validationNavHelp: '検証のヘルプ',
	leaveUsFeedback: 'Leave us feedback',
	navDashboard: 'ダッシュボード',
	tasksPage: 'タスク',
	documentsPage: '文書',
	collaborationPage: 'コラボレーション',
	automationPage: 'オートメーション',
	documentHelperConnectionIssue: 'EY Canvas Document Helperについて問題が見つかりました。問題の解消方法については<a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">こちら</a>クリックして下さい。',
	noContentAvailable: '利用可能なコンテンツはありません',
	noSectionsAvailable: '利用可能なセクションはありません',
	noInformationAvailable: '利用可能な情報はありません',
	collapse: '折りたたむ',
	expand: '展開',
	duplicate: '複製',
	duplicateSection: '重複するセクション',
	duplicateSectionHeader: '選択されたセクションを本当にコピーしますか？',
	deleteSection: 'セクションの削除',
	deleteSectionHeader: '選択したセクションを削除してよろしいですか？',
	deleteHeader: 'ヘッダーの削除',
	deleteHeaderTitle: '選択したヘッダーを削除しますか？',
	confirmLabel: '確認',
	custom: 'カスタム',
	selectHeader: 'ヘッダの選択',
	selectSection: 'セクションの選択',
	noResultsFound: '結果が見つかりません',
	scot: 'SCOT',
	scotTypes: 'SCOTタイプ',
	frequency: '頻度',
	SelectFrequency: '頻度の選択',
	SelectControlType: '統制タイプの選択',
	itBadge: 'IT',
	soBadge: 'SO',
	noRecordsAvailable: '利用可能なレコードはありません',
	noIncompleteResponseSummaryView: '不完全な回答はありません。',
	noUnresolvedCommentsSummaryView: '未解決のコメントはありません。',
	edit: '編集',
	editForm: 'フォームの編集',
	editControl: '統制の編集',
	delete: '削除',
	remove: '削除',
	noBodies: '利用可能な本文がありません',
	relateDocuments: '関連する文書',
	relatedDocuments: '関連する文書',
	deleteBody: '本文を削除',
	bodyDescription: '選択した本文を削除しますか？',
	description: '詳細',
	maxLengthForEditResponse: '本文テキストが最大文字数を超えています',
	maxLengthForEditResponseWithCount: '回答は {#} 文字となっており、最大文字数({##})を超えています。テキスト数を減らすか書式を変更することで回答を調整し、再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	saveResponse: '変更を破棄すると回答テキストに対する編集は保存されません。 クローズ又は削除されたコメントがハイライトされたままになります。 すべての変更を破棄するか否かを確認してください。',
	discardChangesModalText: '変更の破棄',
	seeBodyDescriptionText: '詳細の参照',
	hideBodyDescriptionText: '説明を非表示にする',
	showBodyDescriptionText: '説明を表示する',
	okLabel: 'OK',
	addButtonLabel: '追加',
	addEvidence: 'エビデンスの追加',
	addTemporaryFiles: '一時ファイルを追加',
	notemporaryDocs: '利用可能な一時ファイルがありません',
	relateFiles: 'ファイルの関連付け',
	uploadFiles: 'ファイルのアップロード',
	cancelButtonLabel: 'キャンセル',
	clickEditResponseToContinue: '続行するには編集アイコンをクリックしてください。',
	editResponse: '回答の編集',
	save: '保存',
	numericValuePlaceholder: '数値の入力',
	saveLabel: '保存',
	cancelLabel: 'キャンセル',
	closeLabel: '閉じる',
	editText: '編集',
	select: '選択',
	selectScot: 'SCOTsの選択',
	clearHoverText: 'クリア',
	optional: '(任意)',
	nodocumentsAdded: '利用可能な文書はありません',
	errorBanner: '{0} 件のエラー',
	NetworkErrorMessage: 'アプリケーションネットワークにエラーが生じています。ページを更新の上再度試して下さい。エラーが解消しない場合ヘルプデスクにコンタクトして下さい。',
	of: '/',
	characters: '文字数',
	show: '表示:',
	views: '表示:',
	primaryRelated: '第1関連Canvasフォーム',
	secondaryRelated: '第2関連Canvasフォーム',
	singleLineValuePlaceholder: '文字を入力',
	paceInputPlaceholder: 'PACE ID',
	multiLineValuePlaceholder: '文字を入力',
	riskFactorDescribe: '説明',
	riskFactorLabel: '関連する事象及び状況/虚偽表示リスク',
	riskFactorEmptyWarning: '説明がない事象及び状況/虚偽表示リスク',
	riskFactorNoDescription: '新しく作成するか既存の関連する事象及び状況/虚偽表示リスクを選択してください',
	fraudRiskTagMessage: 'この関連する事象及び状況/虚偽表示リスクは、常に不正リスクを生じさせるか、該当する場合は、重要な虚偽表示リスクではないと指定する必要があります。',
	significantRiskTagMessage: 'この関連する事象及び状況/虚偽表示のリスクは、常に特別な検討を必要とするリスクもしくは不正リスクを生じさせるか、該当する場合は、重要な虚偽表示リスクではないと指定する必要があります。',
	on: 'on',
	sliderDeSelectMessage: '円をドラッグして値を割当てます',
	yearPlaceholder: 'YYYY',
	dayPlaceholder: 'DD',
	monthPlaceholder: 'MM',
	amLabel: 'AM',
	pmLabel: 'PM',
	relatedEntities: '関連するエンティティ',
	eyServiceGateway: 'JA JP EY Service Gateway',
	eyAutomation: 'JA JP EY Automation',
	eyserviceGatewayAutomation: 'JA JP EY Service Gateway & Automation',
	creating: 'JA JP Creating...',
	cannotCreateUdp: 'JA JP Cannot create UDP. Time Phase cannot be empty',

	// 440GL
	rrdReminderTitle: '査閲承認要約表(RAS)リマインダー',
	rrdReminderMessage: '報告書日は{rrdDescription}です。査閲承認要約表(RAS){rrdPendingDays}に必ず署名してください。',
	rrdReminderPendingDays: '{0}日以内',

	//Create or Associate risks
	createAssociateRisksLabel: '新しいリスクを作成するもしくはリスクを関連付ける',
	relatedRiskIsMissingWarning: '関連するリスクはありません',
	associateRiskDescription: '質問への回答に関連付けるためには、1つ以上のリスクを選択するか新しいリスクを作成して下さい。',
	createNewRiskLabel: '新しいリスクの作成',
	noRiskIdentifiedLabel: 'リスクが識別されていません',

	// GuidanceModal
	eyAtlasLink: 'EY Atlas',
	guidanceHeaderMessage: 'このモジュールの要素',
	guidanceModalHeader: 'ガイダンス',
	guidanceModalLabelText: '入力',
	guidanceFooter: '詳細',
	guidanceSeveralEntriesText: '複数入力',
	guidanceVisitText: 'アクセス',
	guidanceClickText: 'クリック',
	guidanceHereText: 'こちら',
	guidanceFooterText: '詳細',
	analyticsInconsistencies: '分析をレビューするときは、予想と矛盾する変化やアクティビティがないかどうかを検討してください。 これらの状況は、新たなリスク、別の同種類取引、SCOT の変更、または経営管理者による内部統制の無効化のリスクを示している可能性があります。',
	analyticsInconsistenciesOSJE: 'OJSE は分析対象の選択された勘定科目の貸借を示します。 新しい又は通例でない勘定科目の組み合わせを探します。',
	analyticsInconsistenciesActivityBySource: 'ソース別アクティビティ分析は選択した勘定科目の月間総活動と関連ソースを示します。通例でないアクティビティ、ソース又はソースに対するアクティビティの変化に焦点を当てます。',
	analyticsInconsistenciesPreparerAnalysis: '作成者分析では選択した勘定科目の作成者が記録した期間中の総アクティビティが要約されます。作成者の変更または役割外の勘定科目でのアクティビティに焦点を当てます。',
	analyticsInconsistenciesAccountMetrics: '勘定科目マトリックスは勘定科目に関する重要な情報を要約したもので勘定科目の指定に役立ちます。',
	analyticsLoadingIsInProgress: '要求された分析の読込中です。完了後、タブにアクセスできるようになります。',

	aboutDescriptioin: 'GAMレイヤー、基準、言語の編集。編集によりコンテンツがアップデートされます。',
	aboutContentDescription: 'コンテンツレイヤー、基準又は言語の編集。編集によりコンテンツがアップデートされます。',
	about: '詳細',
	formType: 'フォームタイプ',
	gamLayer: 'GAM レイヤー',
	contentLayer: 'コンテンツレイヤー',
	standard: '標準',
	language: '言語',
	createdBy: '作成者：',
	createdOn: '作成日：',
	contentLastUpdatedBy: '最終更新者',
	contentLastUpdatedOn: '最終更新日：',
	notModified: '未編集',

	rolesInsufficientTooltip: 'コンテンツを編集する権限が不足しています。エンゲージメント管理者と協議し適切な権限を取得してください。',
	knowledgeFormToolTip: "JA JP Knowledge delivered documents cannot be updated. Update the Engagement Profile to change this form's profile.",
	selectTeamMember: '名前又はEメールアドレス',

	// SeeMore component
	showMore: 'さらに表示',
	showLess: '表示を減らす',
	showMoreEllipsis: 'さらに表示 ...',
	showLessEllipsis: '表示を減らす...',

	relatedITapplicationSOs: '関連するITアプリケーション/サービス受託会社',
	aggregateITevaluations: 'IT総合評価',
	lowerRisk: '低リスク',
	controlLowerRisk: '統制は低リスクです',
	relatedITApplication: '関連するITアプリケーション',
	relatedITSO: '関連するSO',
	noITApplicationUsed: 'ITアプリケーションは使用されていません',

	notSel: '未選択',
	internal: '内部',
	external: '外部',
	notSelected: '選択されていません',
	noOptionSelected: '選択されていません',
	tod: 'TOD',
	sap: 'SAP',
	int: 'INT',
	ext: 'EXT',
	toc: 'TOC',

	placeholderForSearch: '検索',
	source: 'ソース',
	nature: '種類',
	testOfDetail: '詳細テスト',
	testOfControl: '統制テスト',
	substantiveAnalyticalProcedure: '分析的実証手続',
	expandScot: '1. SCOTを展開する',
	selectWCGWToDisplayTheResponsiveTask: '2. 重要な虚偽表示リスクを選択し、対応するタスクを表示する',
	tasksWithNoRelatedWCGW: '関連する重要な虚偽表示リスクがないタスク',
	noTasksAvailable: '利用可能なタスクはありません',
	noWCGWAvailableForTask: '利用可能なROMMはありません',
	noSubstantiveTasksAvailable: '関連する実証タスクはありません',
	selectAssertionToRelateWCGW: 'アサーションを選択して、リスクをWCGWに関連付けます',
	significantAccounts: '重要な勘定科目',
	riskName: 'リスク：',
	accountName: '勘定科目：',
	control: '統制',
	controls: '統制',
	noScotsFound: '関連するSCOT無し',
	relatedwcgw: '関連するWCGW',
	relatedRisks: '関連するリスク：',
	boltIconTitle: 'JA JP Related risk',
	relatedITApp: '関連するIT App/SO',
	instructions: 'インストラクション：',
	expandRisk: '1. リスクを展開します',
	selectAssertion: '2. アサーションを選択します',
	identifyRelatedWCGW: '3. リスクに関連するWCGWを特定します',
	clickAccount: '1. 勘定科目をクリックします',
	selectWCGW: '2. WCGWを選択します',
	identifyRelatedTask: '3. WCGWに対応するタスクを特定します',
	information: '情報',
	requiredAssertions: '対応が必要なアサーション',
	wcgwWithoutTasks: 'タスクのないWCGW',
	rommAssociatedWNotRelyAssertion: 'ROMMが、依拠しないとした統制リスクを伴うアサーション又は固有リスク高の見積勘定科目のアサーションに関連付けられています',
	hasRiskAssociated: '関連付けられたリスク',
	clearSelections: '全ての選択を解除',
	romm: 'ROMM',
	riskOfMaterialMisstatementsWithoutRelatedTask: '関連タスクの無い重要な虚偽表示リスク',
	selectOneOrMoreTasksToSeeTheRelatedROMM: '1つ以上のタスクを選択して、関連するROMMを表示します',
	invalidRelatedEntity: '関連する勘定科目が見つかりません。ページを更新して、再試行してください。問題が解決しない場合は、ヘルプデスクに連絡してください。',
	noResultsAvailable: '結果が見つかりません',
	riskOfMaterialMisstatement: '重要な虚偽表示リスク',
	AccountConclusion: '勘定科目に対する結論',
	CanvasForm: 'Canvasフォーム',
	IndependenceForm: '独立性フォーム',
	Profile: 'プロファイル',
	AccountDetails: '詳細',
	Conclusions: '結論',
	accountDetailsTab: '詳細',
	conclusionsTab: '結論',
	formsNoContentText: '利用可能なコンテンツはありません。',
	formsDocumentNoRelatedObjects: '文書に関連するオブジェクトがありません',
	formsNoRelatedObjects: '関連するオブジェクトがありません',
	formsBodyHeaderControl: '統制',
	formsBodyDesignEffectiveness: 'デザインの有効性',
	formsScotsAndWcgws: 'SCOTs & WCGWs',
	wcgWAndRelatedControls: 'WCGWs及び関連する統制',
	controlAndRelatedItSO: '統制及び関連するIT apps/ SOs',
	type: 'タイプ',
	designEffectiveness: '整備状況の有効性',
	approch: 'アプローチ',
	controlOpertaingEffectiveness: '運用状況の有効性',
	iTAppSO: 'IT App/SO',
	iTProcess: 'ITプロセス',
	iTControl: 'IT統制',
	iTRisk: 'ITリスク',
	aggregateITEvaluation: 'IT総合評価',
	relatedCanvasForm: '関連するCanvasフォーム',
	relatedSections: '関連するセクション',
	validations: '検証',
	profileV2Validation: '未提出の変更',
	profileV2ValidationModalDescription: 'コンテンツが更新される変更が加えられましたが、提出されていません。 変更が意図したものである場合は、このモーダルを閉じて、新しいプロファイルの回答を提出してください。変更が意図したものではない場合は、変更ビューを確認し、手動で回答を元の選択に戻してください。',
	profileV2ValidationCount: '1',
	itProcessWithoutRelatedTechnology: 'JA JP IT process without related technology',
	reviewNote: 'レビューノート',
	editAssociations: '関連付けの編集',
	editAssociationsLower: '関連付けの編集',
	riskWCGW: 'リスク：WCGW 関係',
	wcgwTask: 'WCGW：タスク 関係',
	noAssertionFound: 'アサーションが関連付けられていません。{here}をクリックしてアサーションを関連付けます。',
	limitedRiskAccountIdentifier: 'リスクが限定的な勘定科目',
	insignificantAccountIdentifier: '軽微な勘定科目',
	noWCGWFound: 'このリスクに関連するWCGWがありません。[関連付けの編集]をクリックして、1つ以上のWCGWを関連付けます。',
	noRelatedWCGWs: '関連するWCGWはありません',
	noWCGWAvailable: '選択されたアサーションに利用できるROMMはありません',
	expandCollapse: '展開/折りたたみするには、ここをクリック',
	requiredAssertionsInfo: '統制リスク評価を依拠しないとしたアサーション及び固有リスク高の見積勘定科目のアサーションのみを表示する',
	wCGWwithoutTasksInfo: '統制リスク評価を依拠しないとしたアサーションに関連するWCGW及び固有リスク高の見積勘定科目のアサーションであって、対応する実証タスクが関連付けられてないものを表示する',
	noBuildStepsAvailable: '表示可能なビルドステップはありません。',
	risk: 'リスク',
	wcgw: 'WCGW',
	riskWcgw: 'リスク：WCGW',
	wcgwTasks: 'WCGW：タスク',
	riskWcgwLabel: 'リスクとWCGWの関連付け',
	wcgwTasksLabel: 'WCGWとタスクの関連付け',
	noRiskTypes: 'リスクの種類が見つかりません',
	saveRisk: '保存',
	noRisksFound: 'リスクが見つかりませんでした',
	haveBeenIdentified: '識別済',
	noAccountsFound: 'レコードが見つかりません',
	noResponseAvailable: '回答がありません',
	noDocumentsAvailable: '利用可能な文書がありません',
	noValue: '値がありません',
	showValidation: '検証',
	noAccountsIdentified: '勘定科目が特定されていません',
	noAssertionsIdentified: 'アサーションが特定されていません',
	noWcgwsIdentified: 'WCGWが特定されていません',
	pastingImagesNotAllowed: '画像の貼り付けはできません。画像が必要な場合は、エビデンスとしてアップロードの上、リファレンスを付してください。',
	incompleteResponse: '不完全な回答',
	unresolvedComments: '未解決のコメント',
	inconsistentForms: '一貫性のないフォーム',
	limitedRiskAccount: 'リスクが限定的な勘定科目',
	inherentRiskAssessment: '固有リスク評価',
	task: 'タスク',
	selected: '選択済',
	displaytoc: 'TOCを表示',
	workingoffline: 'オフライン作業',
	syncinprogress: '同期中',
	prepareoffline: 'オフライン用のデータ生成中',
	connectionavilable: '接続可能',
	softwareUpdate: 'ソフトウェアのアップデート',
	updateLater: '後で更新',
	updateNow: '今すぐ更新',
	updateMsg: 'EY Canvas用ソフトウェアの更新が利用可能です。「今すぐ更新」を選択して更新をインストールしてください。ページが更新されます。',
	searchPlaceholder: '検索',
	filter: 'フィルター',
	leftNavSearchPlaceholder: 'ヘッダー及びセクションを検索',
	back: '戻る',
	updateAvailable: '利用可能なアップデート',
	contentUpdateAvailableTooltip: "コンテンツの更新が利用可能です。アップデートを実施するにはこちらをクリックして、'Canvasフォームの更新'に移動してください。 ",
	moreMenu: 'その他のメニュー',
	signoffPreparer: '作成者としてサインオフ',
	signoffReviewer: '査閲者としてサインオフ',
	pagingShowtext: '表示',
	searchDocuments: '文書を検索',
	noRelatedDocuments: '関連する文書がありません',
	noRelatedObjects: '関連するオブジェクトがありません',
	documentName: '文書名',
	formDetails: 'フォームの詳細',
	questionsAndResponses: '質問と回答',
	details: '詳細',
	trackChanges: '変更履歴',
	goToTrackChanges: '変更履歴へ移動',
	attributes: '属性',
	relatedActions: '関連付けられたアクション',
	createCustom: 'カスタムの作成',
	createCustomButtonLabel: 'JA JP Create custom header, section, or body',
	overwriteForm: 'フォームの上書き',
	decimalNaN: 'NaN - 数値ではない',
	noRelatedObjectsApplicable: 'このCanvasフォームに関連するオブジェクトは要求されません',
	objects: 'オブジェクト',
	objectName: 'オブジェクト名',
	addCustomDescription: 'このCanvas Form の詳細インプットに追加すべきコンテンツタイプを選択し保存をクリックする',
	headerTitle: 'ヘッダータイトル',
	sectionTitle: 'セクションタイトル (必須)',
	aresSectionTitle: 'セクションタイトル',
	customLabel: 'カスタムラベル (任意)',
	customBodyDescription: '本文の説明',
	header: 'ヘッダー',
	section: 'セクション',
	body: '本文',
	requiredWCGW: '必須',
	headerTitleRequired: 'ヘッダータイトルが必要です',
	bodyDescriptionRequired: '本文の記載は必須です。',
	bodySectionRequired: 'セクションは必須です。',
	bodyHeaderRequired: 'ヘッダーは必須です。',
	sectionTitleRequired: 'セクションタイトルは必須です',
	headerRequiredMessage: 'ヘッダーは必須です。',
	enterDecimalAmount: '小数点以下を入力',
	enterPercentage: 'パーセンテージの入力',
	completeRiskFactorAssessment: '識別した事象及び状況の評価を完了する',
	noScotsEstimatesIdentified: 'SCOTs又は見積りが識別されていません',
	// Track changes
	trackChangesResponseLabel: '変更履歴バージョンの回答',
	trackChangesVersionLabel: 'バージョンの変更履歴',
	noResponseIdentified: '回答は識別されていません',

	// Compare responses
	compareResponsesLabel: '回答を比較',
	compareResponsesTitle: 'エンティティの回答を比較',
	compareResponseNoDataPlaceholder: 'エンゲージメントには同じ種類の文書が1つしかないため、利用できるデータがありません。',
	labelFor: 'for',
	questions: '質問',
	answers: '回答',
	countOfResponses: '回答数',
	openNotes: 'オープンノート',
	clearedNotes: 'クリア済ノート',
	click: 'クリック',
	clickToViewAnswer: '回答へ',
	clickToViewQuestionAnswer: '質問と回答へ',
	selectDocuments: '文書の選択',
	selectedDocumentsCount: '{0}文書選択中',
	selectedDocumentCount: '{0}文書選択中',
	associatedDocuments: '関連する文書',
	noAnswerProvided: '回答が提供されていません',

	// Workspace Engagement
	thisEngagement: 'このエンゲージメント',
	documentLocation: '文書のロケーション',
	otherEngagementsInWorkspace: 'ワークスペースにある他のエンゲージメント',
	added: '追加済',
	documentIneligibleForSharingMessage: '機密文書は共有できません。',
	fitDocumentCannotbeSelected: 'FIT文書はエンゲージメント間で共有できません。',

	//Helix Configuration
	helixConfigurationTitle: 'EY Helixデータの統合',
	helixConfigurationPageDescription: 'リンクされたEY Helixプロジェクトを検証し、EY Canvasにデータをインポートします。以下のEY Helixの設定を変更した場合やデータ取込後にEY Helixデータを変更した場合は、更新のためにデータを再インポートしなければなりません。',
	linkedEYHelixProjects: 'リンクされたEY Helixプロジェクト：',
	client: 'クライアント：',
	engagement: 'エンゲージメント：',
	analysisDate: '分析日：',
	eyHelixProjects: 'EY Helixプロジェクト',
	noPrimaryEYHelixproject: '主要なEY Helixプロジェクトが識別されていません。',
	here: 'こちら',
	identifyEyHelixProjects: 'EY Helixプロジェクトを識別して、ワークフローを開始してください。',
	eyHelix: 'EY Helix',
	primary: 'Primary',
	helixSettingsDescription: '編集をクリックしてEY Helix Analyzersを読み込む際に適用される設定を選択します。',
	editButton: '編集',
	helixSettingsModalTitle: 'EY Helixの設定',
	currencyType: '通貨タイプ',
	currencyTypeError: 'EY Helixから通貨タイプを取得できませんでした。EY Helix上、データが正しく設定されているかを確認し再試行してください。',
	shortNumberFormat: '短縮桁数表示',
	shortNumberFormatFooter: 'EY Helixのテーブルに表示される数値に適用される端数処理',
	eyHelixAnalyzerFilterMetadataError: 'EY Helixに接続できませんでした。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	functional: '機能',
	reporting: '報告',
	currencyCode: '通貨コード',
	businessUnit: 'ビジネスユニット',
	roundingNumberFormat: '端数処理の形式',
	eyHelixProjectChangedLine1: 'リンクされたEY Helixプロジェクトは前回の設定が保存された以降変更されています。',
	eyHelixProjectChangedLine2: 'EY Helixからデータをインポート (又は再インポート)するためには編集をクリックし設定を更新してください。',
	helixSettingsTimeline: 'EY Helixの設定',
	helixMapEntitiesToBU: 'エンティティをビジネスユニットにマッピングする',
	helixNumberOfMapEntities: 'マッピングされたビジネスユニットの数',
	importEYHelixDataTimeline: 'EY Helixデータのインポート',
	mapAccountsHelixTimeline: '勘定科目のマッピング',
	setEYHelixSettings: '以下のEY Helixの設定を編集してください。一度保存/データのインポートをすると、選択した比較日付1及び比較日付2の日付がOARアクティビティにおける指定された分析日付との比較として使用されます。1つの比較日付の日付のみと比較するには、比較2の日付選択で「なし」を選択します。',
	eyHelixDataHasChangedLine1: 'データは設定の前回保存時以降に変更されています。新たな選択を以下から行いクリックしてください',
	eyHelixDataHasChangedLine2: 'EY Helixの設定の更新',
	all: '全て',
	multiple: '複数',
	notApplicableAbbreviation: 'N/A',
	importEyHelixData: 'EY Helixデータのインポート',
	editScreenshot: '編集',
	deleteNote: '削除',
	removeAnnotation: '注釈の削除',
	addAnnotation: '注釈の追加',
	addingBoundingBox: 'スクリーンショットで注釈を付ける範囲を選択し、注釈を確認後、チェックマークをクリックして保存してください。',
	cancelBoundingBox: 'キャンセル',
	deleteScreenshot: 'スクリーンショットの削除',
	openInFullscreen: '全画面で開く',
	helixURLErrorMessage1: 'マッピングされたHelixプロジェクトの設定が不完全です。',
	helixURLErrorMessage2: '{0}ページに移動し更新してください。',
	helixIsNotEnabledMessage: 'EY Helixはエンゲージメントに対して有効になっていません。',
	helixSetup: 'EY Helixの設定',
	openAnalysisInHelix: 'EY Helixで分析を開く',
	helixInvaliddate: '無効な日付。分析日より前の日付を選択してください。',
	helixcomparativedateoptional: 'EY Helix比較日付2（任意）',
	helixpriorperioddate: 'EY Helix比較日付1',
	helixanalysisperiod: 'EY Helix分析日',
	helixfiscalDropDownLabel: '期間 {0} - {1}',

	helixSettingsEditButtonTitle: '編集',
	helixImportDataEditButtonTitle: 'インポート',
	helixImportInsufficientPermissionsMessage: 'Helixデータインポートを実行するための権限が不足しています。エンゲージメント管理者に連絡し、Helixデータインポートするための権限をリクエストしてください。',
	helixImportNotAllowed: 'エンゲージメントプロファイルではHelixデータのインポートが許可されていません',
	helixDeleteImportInsufficientPermissionsMessage: 'EY Helixデータインポートを削除する権限がありません。エンゲージメント管理者に連絡し、EY Helixデータを削除する許可をリクエストしてください。',

	EYAccountMappingStepLabel: '勘定科目マッピングの管理',

	EYAccountMappingOptional: '任意',
	EYAccountMappingStepTitleSettingsCompleted: 'EYの勘定科目マッピングを実施する',
	EYAccountMappingStepTitleSettingsIncomplete: 'EY HelixマッピングモジュールにアクセスするためにEY Helixの設定を完了する',
	EYAccountMappingInstructions: 'クライアント勘定科目をEY勘定科目にマッピングし、変更を処理します。処理が完了したら、以下のデータをインポートします。',
	manageAccountMappingButtonLabel: '勘定科目マッピングの管理',

	//EY Helix Setup Card
	EYHelixSetupTitle: 'EY Helix',
	EYHelixSetupSubTitle: 'EY Canvasへのデータの設定とインポート',
	LastImported: '最終インポート：',
	EYHelixSettings: 'EY Helix接続',
	NoEYHelixProjectLinkedLabel1: 'エンゲージメントが主要なEY Helixプロジェクトにリンクされていません。詳細については',
	NoEYHelixProjectLinkedLabel2: 'ページにアクセスしリンク付けをしてください。',
	NoEYHelixProjectLinkedHperlink: 'EY Helixプロジェクト',
	NoDataImportedLabel: 'データがEY Helixからインポートされていません。 「EY Helix setup」 をクリックしてプロセスを開始してください。',
	noHelixConnections: "\'EY Helix connections\'をクリックして接続を作成します。 ",
	helixConnectionExists: '接続が存在します',
	helixConnectionsExist: '接続が存在します',
	helixTrialBalanceImported: 'スコープ及び戦略のインポートされた試算表',
	helixTrialBalancesImported: 'スコープ及び戦略のインポートされた試算表',
	helixNoTrialBalanceImported: "\'EY Helix接続\'をクリックして、スコープ及び戦略の試算表をインポートします。 ",

	// EY Helix - Map Accounts
	mapAccountsHelixCanvas: '「編集」をクリックすると、EY Canvasの勘定科目をEY Helixからインポートされた勘定科目にマッピングできます。',
	mapAccountsHelixCanvasSubtitle: '各EY Helixの勘定科目をドラッグアンドドロップすると、EY Canvasの勘定科目にマッピングできます。 「勘定科目の管理」をクリックすると、EY Canvasの勘定科目を作成または編集できます。 ',
	mapAccountsHelixHeaderLabel: 'EY Helixの勘定科目',
	mapAccountsCanvasHeaderLabel: 'EY Canvasの勘定科目',
	mapAccountsConnectedLabel: '関連付けられた勘定科目',
	mapAccountsShowMappedLabel: 'マッピングされた勘定科目を表示',
	mapAccountsHideMappedLabel: 'マッピングされた勘定科目を非表示にする',
	mapAccountsManageLabel: '勘定科目の管理',
	mapAccountsAndDisclosuresManageLabel: '勘定科目と開示の管理',
	mapAccountsNoCanvasAccounts: '勘定科目が識別されていません',
	mapAccountsNoCanvasAccountsClick: 'クリック',
	mapAccountsNoCanvasAccountGetStarted: 'して開始します。',
	mapAccountsRemoveAccount: '削除',
	mapAccountsReImportHelixAccounts: 'EY Helixからのデータインポートが成功しませんでした。データインポートをやり直してください。',
	mapAccountsReImportHelixAccountsHelpDesk: '問題が続く場合は、ヘルプデスクに連絡してください。',
	mapAccountsNoHelixAccountHasBeenImported: 'EY Helixの勘定科目がインポートされませんでした。',
	mapAccountsNoHelixAccountHasBeenImportedCheckData: 'EY Helixにおいてデータを確認して再試行してください。',

	//Helix Analyzer
	accountNotRelatedToDocumentOnPhaseTwo: 'この文書に関連付けられた勘定科目がありません',

	//PM TE SAD Widget
	materialityWidgetLabel: 'PM/TE/SAD',

	// TE labels
	planningmateriality: 'PM',
	requiredTePercentage: '要求されるTE',
	suggestedtepercentage: '提案されたTE',
	currentperiodte: '当期のTE',
	priorperiodte: '前期のTE',
	pmPriorPeriod: '前期PM',
	tepercentage: 'TEの算定パーセンテージ',
	teamount: 'TEの金額',
	teLabel: '許容誤謬額',
	sugestedSAD: '提案されたSAD算定パーセンテージ:',
	priorSAD: '前期のSAD',
	currentPeriodSAD: '当期のSAD',
	sadPercentage: 'SADの算定パーセンテージ',
	sadAmount: 'SADの金額',
	rationaleLabel: '論拠',
	suggestedTEPercentageInfo: 'エンゲージメント固有の要因に基づいて別のパーセンテージが選択された場合、以下にそれらの要因を文書化するように求められます。',
	rationaleTEDescription: '上記で選択した属性を考慮して、PMのパーセンテージとしてTEの論拠を入力してください。',
	teAmmountInvalid: '有効な値を入力するか50％又は75％を選択してください',
	highRiskTEAmmountInvalid: '有効な値を入力するか50%を選択してください',
	highRiskTERequired: '以上の検討事項への回答によると、指定されているTEパーセンテージは要求事項であり、変更できません。',

	// EY Helix Map Entities to Business Units Modal
	mapEntitiesModalTitle: 'エンティティの管理',
	mapEntitiesModalLeyendDescription: 'EY Canvasのエンティティと以下のEY Helixプロジェクトにおけるビジネスユニットとのマッピングを管理する。',
	mapEntitiesModalLeyendNote: '注: ビジネスユニットは、1つまたは複数のEY Canvasエンティティに関連付けることができます。保存してデータをインポートすると、EY Helixのビジネスユニットに関連付けられたデータが、関連するEY Canvasエンティティにマッピングされて表示されます。',
	mapEntitiesModalEntityCodeLabel: 'エンティティコード',
	mapEntitiesModalEmptyEntitiesList: 'エンティティは作成されていません。',
	mapEntitiesRelatedBusinessUnitDropdownPlaceholder: '関連するビジネスユニット',
	mapEntitiesSelectedBusinessUnitsCount: '{0} ビジネスユニット選択済',

	//AdjustedBasis
	enterAmount: '金額の入力',
	basisAmount: '基礎となる値',
	lowEndOfRange: 'レンジの下限値',
	highEndOfRange: 'レンジの上限値',
	suggestedRange: '上記の要因に基づくと、以下のレンジにおける{0}（{1}～{2}）を使用することが適切である場合があります。もし、エンゲージメント固有の要因により、レンジ外の数値を選択する場合、以下にその要因の文書化が求められます。',
	suggestedRangeLowPMBracket: '上記の要因に基づくと、{0} パーセントが適切です。エンゲージメント固有の要因に基づいてこの範囲外の金額が選択された場合、それらの要因を文書化するように求められます。',
	middle: '中',
	lowerEnd: '下限',
	higherEnd: '上限',
	lowEnd: '下限',
	priorPeriodPm: '前期PM：',
	suggestedRangeSummary: '提案されたレンジ',
	loadingMateriality: '重要性のロード中…',
	pmBasisPercentage: 'PMの基本パーセンテージ',
	pmAmount: 'PMの金額',
	currentPmAmount: '当期PMの金額',
	pmAmountPlaceholder: 'PMの金額の入力',
	currentPeriodPm: '当期PM：',
	enterRationale: '論拠を入力',
	rationaleDescription: '上記で選択した属性を考慮して、PMの決定に使用したパーセンテージの論拠を入力してください',
	pmValidationMessage: '監査計画上の重要性の基準値は範囲の上限を超えることはできません',
	sadValidationMessage: '監査差異要約表記載基準額は範囲の上限を超えることはできません。',
	sadRationaleDiscription: '上で選択した属性を考慮して、SADパーセンテージの根拠を入力して下さい',
	nopriorperiodDocs: '利用可能な前期の文書はありません',
	addPriorPeriodEvidence: '前期のエビデンスを追加',
	addToEvidenceLabel: 'エビデンスに追加',
	moveToEvidenceLabel: 'エビデンスに移動',
	addToEvidenceModalDescription: '選択した文書について新しい名前を作成する又は現在の名前をそのまま使用してください。',
	GoToSource: 'ソースへ進む',
	//ITRiskITControls
	createNewITGC: '新しいITGC',
	relateITGC: 'Relate ITGCs',
	createNewITSP: '新しいITSP',
	relateITSP: 'ITSPの関連付け',
	noITGC: 'ITGCsなし',
	itRiskForITGCITSP: 'ITリスク名（必須）',
	createITGCModalDescription: "以下にITGCの詳細を入力し'<b>{0}</b>'を選択して終了してください。他のITGCを作成する場合は'<b>{1}</b>'を選択してください。 ",
	createITSPModalDescription: "以下に ITSPの詳細を入力し'<b>{0}</b>'を選択して終了してください。他の ITSPを作成する場合は'<b>{1}</b>'を選択してください。 ",
	controlDesignEffectiveness: {
		[0]: {
			description: '未選択 '
		},
		[1]: {
			description: '有効 '
		},
		[2]: {
			description: '非有効 '
		}
	},
	controlOperationEffectiveness: {
		[0]: {
			description: '未選択 '
		},
		[1]: {
			description: '有効 '
		},
		[2]: {
			description: '非有効 '
		}
	},
	controlTesting: {
		[0]: {
			description: '未選択 '
		},
		[1]: {
			description: 'はい '
		},
		[2]: {
			description: 'いいえ '
		}
	},
	itAppTypes: {
		[0]: {
			label: 'IT App '
		},
		[1]: {
			label: 'SO '
		}
	},
	controlType: {
		[0]: {
			controlTypeName: '',
			shortName: '未選択 '
		},
		[1]: {
			controlTypeName: 'ITアプリケーション統制',
			shortName: 'アプリケーション '
		},
		[2]: {
			controlTypeName: 'IT依存手作業統制',
			shortName: 'IT依存手作業統制 '
		},
		[3]: {
			controlTypeName: '手作業防止',
			shortName: '手作業防止 '
		},
		[4]: {
			controlTypeName: '手作業発見',
			shortName: '手作業発見 '
		}
	},
	controlTypeEnumLabel: {
		[0]: {
			controlTypeName: '選択されていません '
		},
		[1]: {
			controlTypeName: 'ITアプリケーション統制 '
		},
		[2]: {
			controlTypeName: 'IT依存手作業統制 '
		},
		[3]: {
			controlTypeName: '手作業防止 '
		},
		[4]: {
			controlTypeName: '手作業発見 '
		}
	},
	controlFrequencyType: {
		[0]: {
			controlFrequencyTypeName: '選択されていません '
		},
		[1]: {
			controlFrequencyTypeName: '毎日何度も '
		},
		[2]: {
			controlFrequencyTypeName: '日次 '
		},
		[3]: {
			controlFrequencyTypeName: '週次 '
		},
		[4]: {
			controlFrequencyTypeName: '月次 '
		},
		[5]: {
			controlFrequencyTypeName: '四半期毎 '
		},
		[6]: {
			controlFrequencyTypeName: '年次 '
		},
		[7]: {
			controlFrequencyTypeName: 'ITアプリケーション統制 '
		},
		[8]: {
			controlFrequencyTypeName: 'その他 '
		}
	},
	strategyType: {
		[0]: {
			strategyTypeName: '未選択 '
		},
		[1]: {
			strategyTypeName: '統制 '
		},
		[2]: {
			strategyTypeName: '実証 '
		},
		[3]: {
			strategyTypeName: '依拠する '
		},
		[4]: {
			strategyTypeName: '依拠しない '
		}
	},
	aggregateITEvaluationType: {
		[0]: {
			aggregateITEvaluationTypeName: '未選択 '
		},
		[1]: {
			aggregateITEvaluationTypeName: 'サポートする '
		},
		[2]: {
			aggregateITEvaluationTypeName: 'サポートしない '
		},
		[3]: {
			aggregateITEvaluationTypeName: 'FS & ICFRをサポートする '
		},
		[4]: {
			aggregateITEvaluationTypeName: 'FSのみサポートする '
		}
	},

	sampleItemFilterLabels: {
		filterTypeOfTags: 'タグ',
		noFiltersAvailable: '利用可能なフィルターがありません',
		filterToolTip: 'フィルター',
		clearAll: '全てクリア',
		showMore: 'さらに表示',
		filters: 'フィルター',
		noResults: '結果が見つかりません。',
	},

	stratergyTypeLabels: {
		[0]: {
			label: '未選択 '
		},
		[1]: {
			label: 'スコープ内 '
		},
		[2]: {
			label: 'スコープ外 '
		}
	},
	noChangeReasonCommentAvailable: '変更の理由を入れるために文書のオプション（歯車のアイコン）から編集理由をクリックしてください。',
	changeReasonModalTitle: '変更する理由を編集する',
	changeReasonModalText: '報告書日後に文書を変更した理由を選択する。複数の事務的な変更を行った場合には、最も重要な変更を以下のドロップダウンから選択する。事務的な変更、事務的でない変更の両方を行った場合には、以下より事務的でない変更を選択する。',
	changeReasonUploadModalTitle: '文書アップロードの理由',
	changeReasonUploadModalText: '報告書日後に文書を変更した理由を選択する。複数の事務的な変更を行った場合には、最も重要な変更を以下のドロップダウンから選択する。事務的な変更、事務的でない変更の両方を行った場合には、以下より事務的でない変更を選択する。',
	changeReasonModalComboPlaceholder: '選択',
	changeReasonModalAnnotationText: '直面した状況及び情報を追加する理由を文書化する。新規に又は追加で実施した監査手続、入手した監査証拠、及び到達した結論並びに監査報告書への影響',
	changeReasonUploadModalAnnotationText: '直面した状況及び情報を追加する理由を文書化する。新規に又は追加で実施した監査手続、入手した監査証拠、及び到達した結論並びに監査報告書への影響',
	changeReasonModalAnnotationPlaceHolder: '変更理由の入力',
	changeReasonModalChangeReasonRequired: '理由を変更し保存',
	reasonColumnTitle: '理由',
	shared: '共有済',
	shareStatusOwned: '共有元',
	shareStatusShared: '共有先',
	lastModifiedBy: '最終編集者',
	fileSize: ' | {1} KB',
	openedLabelText: 'オープン済 ',
	currentlyBeingModifiedBy: '現在の編集者',
	OpenGuidedWorkflowDocument: 'EY Canvas FITイネーブルメントからこの文書を開く',
	submitProfile: 'プロファイルの提出',
	submitProfileFit: 'プロファイルの提出',
	contentUpdateUnAuthorizedTooltipMessage: 'コンテンツアップデートを実行するための権限が不足しています。エンゲージメント管理者に連絡してコンテンツアップデートを実施するための権限をもらってください。',
	submitProfileValidationErrorMessage: '全ての質問に回答した場合のみプロファイルが提出されます。未完了の質問を抽出、完了し、再提出してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	pickADate: '日付を選択',

	/* Sign Offs */
	preparerHoverText: '作成者としてサインオフ',
	reviewerHoverText: '査閲者としてサインオフ',
	preparerTitle: '作成者サインオフ',
	reviewerTitle: '査閲者サインオフ',
	deleteHoverText: 'サインオフの削除',
	preparer: '実施者',
	reviewer: '査閲者',
	preparerLabel: 'P',
	reviewerLabel: 'R',
	noSignOffsAvailable: 'サインオフがありません',
	none: '該当なし',
	partnerInChargeLabel: 'PIC',
	eqrLabel: 'EQR',
	documentSignoffRequiredLabel: '必須のサインオフ：',

	relatedDocumentsTitle: '関連文書',
	relatedTasksCount: '{0} の関連するタスク',
	relatedTasksTitle: '関連タスク',
	relateTemporaryFiles: '一時ファイルの関連付け',
	bodyRelatedDocumentsTitle: '本文に関連付けられた文書',
	relatedObjectsTitle: '関連するオブジェクト',
	relateDocumentsTitle: '関連文書の管理',
	relateDocumentsToBodyTitle: 'エビデンスの追加',
	relateDocumentsDesc: 'Canvasフォームへの関連付けのため文書を選択する',
	relateDocumentsToBodyDesc: 'このエンゲージメント又はこのワークスペースの別のエンゲージメントから文書を関連付けてください。',
	relateDocumentsToTheBody: 'JA JP Relate a document from this engagement.',
	priorPeriodEvidencesToTheBody: '本文に関連する前期のエビデンス',
	relatedDocunentEngdisabed: '文書がこのエンゲージメントに共有されていません。',
	showOnlyRelatedDocuments: '関連文書のみ表示',
	manageDocuments: '文書の管理',
	documentCount: '{0} 文書',
	documentsCount: '{0} 文書',
	relateDocumentsSearchPlaceholder: '文書の検索',
	overwriteFormDesc: 'フォームを選択し現行のCanvasフォームを上書きします。現行のフォームは一時ファイルに移動されます。',
	searchFormPlaceholder: 'フォームの検索',
	overwriteLabel: '上書き',
	confirmOverwriteLabel: '上書きの確認',
	confirmOverwriteDesc: "{0}’フォームの内容を'{1}’フォームにコピーしてよろしいですか？この操作では、'{2}’で上書きされますが、関連するエビデンスとオブジェクトは上書きされません。上書きが完了した後、該当する関連エビデンスとオブジェクトを'{3}’フォームに再度関連付ける必要があります。'{4}'フォームは既存のプロファイリング/GAMレイヤーを維持するので、そのプロファイリングが'{5}'フォームと異なる場合はコピーしたコンテンツを再確認してクリアしてください。上書きプロセスの進行中は、ブラウザを閉じたり、別のページに移動したりしないでください。上書きが完了すると、'{6}’フォームは一時ファイルに移動し、'{7}’フォームに進みます。 この操作は元に戻せません。 ",
	formSelectionRequired: '上書きするフォームの選択',

	open: '開く',
	startCoeditMode: 'マルチユーザー編集の開始',
	endCoeditMode: 'マルチユーザー編集の終了',
	openReadOnly: '読み取り専用で開く',
	copyLink: 'リンクのコピー',
	rename: '名前の変更',
	viewHistory: '閲覧履歴',
	documentOpenModelLabel: '文書は現在編集中です',
	modelUserOpenedTheDocumentText: 'このユーザ－は文書を開いています',
	modelDocumentOpenedText: 'この文書を編集中のユーザー：',
	modelOpenedDocumentConflictText: '文書を開くとコンフリフトが生じる可能性がありますので、読み取り専用で開いてください。文書を編集したい場合は、',
	clickHereEnabledText: 'ここをクリック',
	documentOptions: '文書オプション',
	accountDetails: '勘定科目詳細',

	// DAAS labels
	coEditModeIsEnding: 'マルチユーザー編集を終了しています',
	coEditMode: 'マルチユーザー編集',
	checkInInProgressMessage: 'チェックインが進行中です。チェックインには20分程度かかることがあります。更新してアップデートしてください。',
	checkInInErrorLabel: 'チェックインに失敗しました',
	checkOutInProgressMessage: 'チェックアウトが進行中です。チェックアウトには20分程度かかることがあります。更新してアップデートしてください。',
	checkOutInProgressLabel: 'チェックアウトが進行中です。',
	checkInInProgressLabel: 'チェックインが進行中です',
	checkOutInErrorLabel: 'チェックアウトに失敗しました。',
	daasErrorMessage: 'オペレーションが完了できません。ページを更新して再試行してください。問題が解決しない場合は、ヘルプデスクにご連絡ください。',
	coEditModeIsStarting: 'マルチユーザー編集の開始',
	daasOpenDocumentWarning: 'JA JP Multi-user editing may have been ended by another user. Refresh the page and try again.',
	beingEditedInCoeditMode: 'JA JP Being edited in multi-user edit. Edit started by {0}',
	beingEditedInCoeditModeOn: 'JA JP on {0}.',
	beingEditedInCoeditModeError: 'JA JP Being edited in multi-user edit',
	coEditModeAutomaticallyEnds: '文書はマルチユーザー編集モードで編集されており、{0}日後に自動的に終了します。',
	coEditModeAutomaticallyEndsToday: '文書はマルチユーザー編集モードとなっており、このモードは本日中に終了します。',
	daasStartCollaborationModeWarning: 'JA JP Collaboration mode may have been started by another user. Refresh the page and try again.',
	documentCurrentlyBeingModifiedTitle: 'JA JP Document currently being modified',
	documentCurrentlyBeingModifiedHeader: 'JA JP This document is currently being modified by {0}. This user opened the document',
	documentCurrentlyBeingModifiedBody: 'JA JP Starting multi-user edit mode could cause conflicts, so we recommend discussing with {0} before proceeding. Select {1} to start the multi-user edit mode or {2} to return without starting multi-user edit mode.',
	documentEndMultiUserEditingTitle: '​マルチユーザー編集を終了',
	documentEndMultiUserEditingHeader: 'JA JP Warning: Other users may be actively editing this document.  This can be checked by opening the document and seeing if other users are currently in the file. Please confirm that all changes are complete before ending multi-user mode. File changes in multi-user mode may take up to 1 minute to be processed. Therefore, please wait at least 1 minute after exiting the file before ending multi-user mode.',
	documentEndMultiUserEditingBody: 'JA JP Select {0} to end the multi-user edit mode or {1} to return without ending multi-user edit mode.',
	startMultiuserEditing: 'JA JP Start',

	/* Engagement Comments */
	clear: 'クリア',
	close: 'クローズ',
	reOpen: '再オープン',
	reply: '返信の追加',
	replyLabel: '返信',
	unselectComment: 'コメントの選択を解除',
	commentText: '文字の入力',
	replyText: '回答テキスト',
	openStatus: 'オープン',
	clearedStatus: 'クリア済',
	closedStatus: '完了',
	chartCommentsTitle: 'レビューノート',
	showComments: 'コメントの表示',
	noRecordsFound: 'レコードが見つかりません',
	noCommentsFound: '以下の入力欄を使用してコメントを残してください。コメントをユーザーに割当て、優先度と期日を指定します。',
	newComment: 'コメントの追加',
	addNoteTitle: 'ノートの追加',
	editComment: 'コメントの編集',
	newReply: '回答の追加',
	editReply: '回答の編集',
	commentTextRequired: 'コメントの入力は必須です',
	replyTextRequired: '回答テキストは必須です',
	myComments: '私のコメント',
	assignTo: '割当て先',
	theCommentMustBeAssigned: '必須の割当て',
	priorityRequired: '優先度が必要です',
	dueDateRequired: '期日が必要です',
	assignedTo: '割当て先',
	allComments: '全てのコメント',
	assignedToMe: '私に割当て',
	unassigned: '割当て未了',
	draggableCommentsPlaceholder: '文章を入力して新しいコメントを追加する',
	draggableNotesPlaceholder: 'テキストを入力して新しいノートを追加',
	enterReply: '回答の入力',
	dueDate: '期日',
	commentsAmmount: '{count} コメント',
	singleCommentAmmount: '{count} コメント',
	eyInternal: 'EY',
	noneAvailable: '利用できません',

	navHelixProjects: 'EY Helix接続',

	evidence: 'エビデンス',
	priorPeriod: '前期',
	temporaryFiles: '一時ファイル',
	priorPeriodEvidence: '前期',
	closed: 'すべての割当てがクローズしました',

	/*Delete*/
	deleteFileTitle: '文書の削除',
	deleteFileCloseBtnTitle: 'キャンセル',
	deleteFileConfirmBtnTitle: '削除',
	deleteFileCloseTitle: 'クローズ',
	deleteFileModalMessage: '選択した文書を削除しますか？',
	/*Rename*/
	editCanvasformObjects: '属性を編集し<b>保存</b>をクリックしてください。',
	renameFileModalMessage: '文書名を変更し保存をクリック',
	renameScreenshotModalMessage: 'スクリーンショットの名前を変更し、「確認」をクリックします。',

	renameFileTitle: '文書名の変更',
	fileNameRequired: 'ファイル名は必須です',
	invalidCharacters: 'ファイル名に以下を含めることはできません：*/:<>\\?|"',
	existingFileName: 'ファイル名が固有ではありません。このメッセージを削除するには、ページを更新するかファイル名を変更してください。',
	maxLengthExceeded: '文書名は115文字を超えることはできません。',

	STEntityProfileBannerMessage: '1つ以上のエンティティプロファイルが変更され、コンテンツが更新されます。プロファイルエンティティページに戻り、「コンテンツのインポート」をクリックして、エンティティのプロファイルに適用可能な新しいコンテンツを受け取るか、回答を以前の状態に戻します。',
	independenceValidationForOwnForm: '独立性の回答が変更されましたが提出されていません。 変更する場合回答が提出されたことを確認してください。 誤って変更した場合、変更画面をレビューし、マニュアルで回答を従前の選択に戻してください。',
	independenceValidationForOthersForm: 'チームメンバーにより独立性の回答が変更されましたが提出されていません。チームメンバーに対し変更の要否及び提出を確認してください。',
	insufficientRightsForIndependenceSubmission: 'コンテンツを編集する権限が不十分です。エンゲージメント管理者に連絡しコンテンツを編集する権限をリクエストしてください。',
	submitIndependenceProfileV2Message: 'プロファイルをレビューし回答が正確であることを確認してください。 その上でサインオフしエンゲージメントを続行してください。',
	submitIndependenceProfileV2EditMessage: 'エンゲージメントコンテンツの変更をもたらすプロファイルへの変更は行われていません。必要な場合エンゲージメントコンテンツ更新ページを使用しコンテンツ更新を行ってください。',
	insufficientRightsForProfileV2Submission: 'プロファイルを編集する権限がありません。 エンゲージメント管理者に連絡して、プロファイルを編集するための権限をもらってください。',
	returnToDashboard: 'ダッシュボードに戻る',
	returnToDashboardFit: 'ダッシュボードに戻る',
	profileV2ChangeNotSubmittedBannerMessage: 'JA JP Changes have been made to the Profile that will result in content updates. Submit the Profile to receive the new content or revert the answers to the previous state.',
	independenceChangeNotSubmittedBannerMessage: '独立性フォームに再提出を要する変更がありました。 バリデーションをクリアするには独立性フォームを提出するかこのユーザーを非アクティブにしてください。',
	multiEntityIndividualProfileBannerMessage: 'プロファイルを編集する権限がありません。 エンゲージメント管理者に連絡して、プロファイルを編集するための権限をリクエストしてください。',
	scotStrategy: 'SCOT戦略',
	wcgwStrategy: 'WCGW戦略',
	itProcessStrategy: 'ITプロセス戦略',

	/*Edit Wcgw*/
	editWcgw: 'WCGWの編集',
	viewWcgw: 'WCGWの表示',
	editScot: 'SCOTの編集',
	viewScot: 'SCOTの表示',
	showIncomplete: '未了項目を表示',
	forms: 'フォーム',
	form: 'フォーム',
	comments: 'ノート',
	changes: '変更',
	editHeader: 'ヘッダの編集',
	editSection: 'セクションの編集',
	editBody: '本文の編集',
	editSectionDescription: 'セクションの詳細を編集し、「保存」をクリックします。',
	editHeaderDescription: 'ヘッダーの詳細を編集し、「保存」をクリックします。',
	editBodyDescription: '本文の詳細を編集し、「保存」をクリックします。',
	manageObject: 'オブジェクトの管理',
	relatedObjects: '関連するオブジェクト',

	/* Manage body objects */
	bro_manage_WCGWTask_title: 'WCGWsの関連付け',
	bro_manage_WCGWTask_instructions: '該当するWGCWsの管理',
	bro_manage_WCGWTask_noDataLabel: '該当するものはありません',

	/*Add/Edit ITGC*/
	addITGC: 'ITGCの追加',
	addNewITGC: '新しいITGCを追加',
	addExistingITGC: '既存のITGCを追加',
	addITGCDescription: 'ITGCの説明入力',
	itControlNameRequired: 'ITGC名称が必要です',
	frequencyRequired: '頻度が必要です',
	frequencyITGC: '頻度の選択',
	nameITGC: 'ITGC名称（必須）',
	iTProcesslabel: 'ITプロセス',
	editITGC: 'ITGCの編集',
	editITSP: 'ITSPの編集',
	editITGCDescription: 'ITGCとその関連属性の編集',
	editITSPDescription: 'ITSPとその関連属性の編集',
	viewITGC: 'ITGCの表示',
	viewITSP: 'ITSPの表示',
	itgcTaskDescription: 'ITGCに関する設計したテストを実施して、依拠する期間全体におけるITGCの運用状況の有効性に関する十分かつ適切な監査証拠を入手してください。',
	/**
	 * Add Edit ITGC
	 */
	addITSPDescription: 'ITSPの説明を入力してください。',
	selectITRisk: 'ITリスクを選択（必須）',
	itRiskRequired: 'ITリスク（必須）',
	itspNameRequired: 'ITSP名（必須）',
	itspTaskDescription: '依拠対象期間の全体にわたってITリスクに有効に対応されているという十分かつ適切な監査証拠を得るため、このタスクの説明をカスタマイズして、IT実証手続の種類、実施時期及び実施範囲を立案する。<br />IT実証手続が期中に実施された場合、ITリスクに対して期中手続の対象期間から期末までの期間をカバーするように追加の監査証拠を得るための手続を立案し、実施する。<br />IT実証手続の結果について結論付ける。',
	itspRequired: 'ITSP名が必要です',
	selectTestingStrategy: '依拠対象期間の全体にわたって統制がデザインどおりに有効に機能しアサーションレベルで重要な虚偽表示を防止、発見及び修正しているという十分かつ適切な監査証拠を得るため、このタスクの説明をカスタマイズして、IT実証手続の種類、実施時期及び実施範囲を立案する。<br />統制テストの結果の評価（サンプルサイズの拡大や補完的統制のテストの実施を含む）により統制の運用状況の有効性を結論付ける。',
	itControlNameTest: 'テスト {0}',

	/*Edit ITControl*/
	editITControl: 'ITGC/ITSPの編集',
	viewITControl: 'ITGC/ITSPの参照',

	/*Add/Edit ITRisk*/
	editITRisk: 'ITリスクの編集',
	editITRiskDescription: 'ITリスクの編集',
	viewITRisk: 'ITリスクの表示',
	addITRisk: 'ITリスクの追加',
	addITRiskDescription: 'ITリスクの説明入力',
	selectITProcess: 'ITプロセスの選択（必須）',
	itRiskName: 'ITリスク',
	itRiskNameRequired: 'ITリスク（必須）',
	riskNameRequired: 'ITリスクは必須です',
	processIdRequired: 'ITプロセスは必須です',
	itProcessRequired: 'ITプロセス（必須）',
	hasNoITGC: 'ITリスクに対応するITGCsが存在しません',

	/*Edit Risk*/
	editRisk: 'リスクの編集',
	viewRisk: 'リスクの表示',

	/*Edit Control*/
	editControl: '統制の編集',
	viewControl: '統制の表示',
	scotRelatedControls: '関連する統制',
	applicationControl: 'アプリケーション統制',
	iTDependentManualControl: 'IT依存手作業統制',
	noAapplicationControlAvailable: 'アプリケーション統制がありません',
	noITDependentManualControlAvailable: 'このITリスクに対処するためのITGCがIT環境に存在しません',
	isIPEManuallyTested: 'このIT依存手作業統制の自動化部分は実証テストで検証されたシステム生成レポートの利用のみ',

	/*Edit ITProcess*/
	editITSOProcess: 'IT/SOプロセスの編集',
	viewITSOProcess: 'IT/SOプロセスの表示',

	/*Edit ITApplication*/
	viewITAppSO: 'IT App/SOの表示',
	editITAppSO: 'IT App/SOの編集',
	strategy: '戦略',
	nameRequired: '名前が必要です',
	name: '名前',

	/*Snap shot*/
	currentVersion: '現在のバージョン',
	compareVersion: '比較するバージョンを選択する',
	snapshotVersionNotAvailable: '比較するバージョンがありません',
	snapshots: 'スナップショット',
	sharedFormWarning: "共有されたCanvasフォームです。オブジェクトとエビデンスは元のエンゲージメントにあり、リンクを解除しても、このエンゲージメントに追加されません。詳細については<a style='color: #467cbe' href='https://live.atlas.ey.com/#library/104/p/SL33184174-396647/C_33404446/C_38129691' target='_blank'>こちらのイネーブルメント</a>を参照してください。 ",
	fullView: 'フルビュー',
	defaultView: 'デフォルトビュー',
	print: '印刷',
	version: 'バージョン',
	navigationUnavailable: '「トラックの変更及び属性」画面ではナビゲーションを使用できません。ナビゲーションを再度有効にするためには、「質問及び回答」を表示してください。',
	snapshotUpdate: '更新済',
	snapshotNew: '新規',
	snapshotRemoved: '削除済',
	snapshotRollforward: 'ロールフォワード時に作成',
	snapshotRestore: 'リストア時に作成',
	snapshotCopy: 'コピー時に作成',

	/*Special Body*/
	priorPeriodAmount: '前期の金額',

	// Helix special body:
	helixScreenshotListLoading: 'スクリーンショットを読み込んでいます…',
	helixScreenshotLoading: 'スクリーンショット画像を読み込んでいます…',
	helixScreenshotDeleting: 'スクリーンショットを削除しています…',
	helixNotesLoading: 'チェックマークを読み込んでいます…',
	helixNotesBoundingBoxShow: '注釈の表示',
	helixNotesBoundingBoxHide: '注釈の非表示',
	helixNoteReferenceNumber: '#',
	helixNoteReferenceNumberPlaceholder: 'リファレンス番号の入力',
	helixNoteText: 'ノート',
	helixNoteTextPlaceholder: 'チェックマークテキストを入力',
	helixNoteAnnotate: '注釈',
	helixNoteAnnotateMessage: 'スクリーンショットで注釈を付ける範囲を選択し、注釈を確認後、チェックマークをクリックして保存してください。',
	helixRemoveAnnotation: '注釈の削除',

	/* User lookup body */
	userLookupInstructionalText: '名前又はEメールアドレスを入力してEnterキーを押し結果を確認してください',
	userLookupShortInstructionalText: '名前またはEメールアドレスを入力しEnterキーを押してください',

	/*Guidance*/
	guidance: 'ガイダンス',
	noIncompleteBodies: 'コンテンツを表示するにはナビゲーションメニューからヘッダーまたはセクションを選択してください。',
	noUnresolvedComments: 'コンテンツを表示するために、ナビゲーションメニューからヘッダーまたはセクションを選択してください',
	addComment: 'コメントの追加',

	/*Independence*/
	otherFormIndependenceMessage: 'この独立性フォームの内容は更新されており、それ以降、ユーザーは再度ログインをしていません。その結果、特定の設問が未完了となる場合があります。以前の独立性ステータスは参照用に保持されています。',
	override: '上書き',
	grantAccess: 'アクセス許可',
	denyAccess: 'アクセス拒否',
	overrideSmall: '上書き',
	grantAccessSmall: 'アクセス許可',
	denyAccessSmall: 'アクセス拒否',
	status: 'ステータス',
	undefined: '未設定',
	incomplete: '未完了',
	noMattersIdentified: '除外事項なし',
	matterIdentifiedPendingAction: '除外事項あり－アクション待ち',
	matterResolvedDeniedAccess: '除外事項解決 - アクセス拒否',
	matterResolvedGrantedAccess: '除外事項解決 - アクセス許可',
	notApplicable: '該当なし',
	restored: 'リストア済',
	overridden: '上書き済',
	priorNoMattersIdentified: '前期 - 除外事項なし',
	priorMatterIdentifiedPendingAction: '前期 - 除外事項あり - アクション未了',
	priorMatterResolvedGrantedAccess: '前期 - 除外事項解消済み - アクセス許可',
	priorMatterResolvedDeniedAccess: '前期 - 除外事項解消済み - アクセス拒否',
	byOn: '実施者・日付：{0} ',
	byLabel: 'by',
	onLabel: 'on',
	modifiedBy: '編集者',
	reason: '理由',
	submit: '提出',
	submitTemplate: 'テンプレートを送信',
	independenceHoverText: 'このユーザーのアクセスを承認、拒否または上書きするには、筆頭業務執行社員、業務執行社員、エグゼクティブディレクターの役割でなければなりません。',
	enterRationaleText: '以下の論拠を入力：',
	enterRationalePlaceholderText: '論拠テキストを入力',
	requiredRationaleText: '論拠（必須）',
	rationaleTextRequired: '論拠が必要です',

	sharedExternalWarning: 'このフォームはCanvas Client Portal経由で共有され外部チームメンバーがアクセス可能です。外部チームメンバーと共有する必要がある回答とコメントのみを入力してください。',
	independenceViewTemplateMessage: 'このフォームは、各チームメンバーの独立性の質問テンプレートとなります。 <br /> 独立性の質問を完了する際に、監査対象のエンティティに適用される独立性の要件に関連するいくつかの質問があり、各チームメンバーが回答する必要があります。これらの質問に対する適切な回答を選択してください。回答は、各チームメンバーの独立性の質問に同期されます。チームメンバーが別の回答を選択した場合、エンゲージメントに再び参加する際にチームメンバーは独立性を再確認する必要があります。チームメンバーがエンゲージメントに再び参加しない場合、彼らの以前の独立性のステータスと回答は保持されます。 <br />独立性のテンプレートに変更を加えることができるのは許可されたユーザーのみです。エンゲージメントの管理者に相談してください。アーカイブ前に手動で元に戻した場合でも、加えられた変更はすべて提出する必要があります。',

	/**
	 * FORM OBJECTS: SCOT-WCGW-CONTROL
	 */
	fo_instructionalText: 'Canvasフォームで文書化しているオブジェクトを選択してください。',
	fsro_instructionalText: 'このセクションに関係するオブジェクトの管理',
	relObj_title_risk: 'リスク',
	relObj_title_riskType: 'リスクタイプ',
	fo_showOnlyRelated: '関連したオブジェクトのみを表示します',
	scotsCount: '{0} SCOTs',
	wcgwsCount: '{0} WCGW',
	itsoCount: '{0} ＩＴアプリケーション / サービス受託会社',
	controlsCount: '{0} 統制',
	itControlsCount: '{0} ＩＴ統制',
	itGcCount: '{0} ITGCs',
	itSpCount: '{0} ITSPs',
	itProcessesCount: '{0} ITプロセス',
	risksCount: '{0} リスク',
	accountsCount: '{0}勘定科目',

	stEntitiesCount: '{0} エンティティ',

	componentsCount: '{0} 構成単位',
	view: '表示',
	searchByScotName: 'SCOT名で検索',
	searchByWcgwName: 'WCGW名で検索',
	searchByITSOAppName: 'IT/サービス受託会社アプリケーション名で検索',
	searchByControlName: '統制名で検索',
	searchByItControlName: 'IT統制名で検索',
	searchByItProcessName: 'ITプロセス名で検索',
	searchByRiskName: 'リスク名で検索',
	searchByAccountName: '勘定科目名で検索',
	searchBySTEntityName: 'エンティティ名で検索',
	searchByEstimateName: '見積り名で検索',
	searchByComponentName: '構成単位名で検索',
	noScotsAvailable: 'このエンゲージメントでSCOTsが利用できません。',
	noRisksAvailable: 'このエンゲージメントに使用できるリスクはありません。',
	noControlsAvailable: 'このエンゲージメントでは統制が利用できません。',
	noItControlsAvailable: 'このエンゲージメントではIT統制が利用できません。',
	noItProcessesAvailable: 'このエンゲージメントではITプロセスが利用できません。',
	noItApplicationsAvailable: 'このエンゲージメントではITアプリケーションが利用できません。',
	noAccountsAvailableLabel: 'このエンゲージメントで利用可能な勘定科目はありません。',
	noObjectsRelatedToForm: 'このCanvasフォームに関連するオブジェクトはありません',
	noDocumentControlsAvailable: '文書に関連付けられた統制がありません。',
	noDocumentScotsAvailable: 'この文書に関連付けられたSCOTはありません。',
	noSTEntitiesAvailable: 'このエンゲージメントで利用可能なエンティティはありません。',
	noComponentsAvailable: 'このエンゲージメントで利用可能な構成単位はありません。',
	editObjectDescription: 'このフォームへのオブジェクトの関連付けを編集',
	editObjectsLabel: 'オブジェクトの編集',
	noITGCsOrITSPsHaveBeenIdentified: '識別されたITGCsまたはITSPsがありません',
	noItProcessIdentified: 'ITプロセスが識別されていません',
	noControlsIdentified: '識別された統制がありません',
	noRelatedRisksIdentified: '識別された関連する特別な検討を必要とするリスク又は不正リスクがありません',
	noItApplicationsIdentified: '識別されたITアプリケーションがありません',
	noSCOTIdentified: '識別されたSCOTがありません',
	noWCGWIdentified: '識別されたWCGWsがありません',
	maxLimitLabel: '最大数のオブジェクトが選択されています。',
	minLimitLabel: '最小数のオブジェクトが選択されています。',

	relatedITAppsTitle: 'ITプロセス及び関連するアプリケーション',
	relatedWCGWTasksTitle: 'WCGW及び関連するタスク',
	noRelatedTasks: '関連するタスクがありません',
	noRelatedWcgw: '関連するWCGWがありません',
	noRelatedControls: '関連する統制がありません',
	controlRelatedRisksTitle: '統制及び関連するリスク',
	sCOTRelatedRisksTitle: 'SCOTs及び関連するリスク',
	scotRelatedItApp: 'IT Appsに関連するSCOT',
	relatedItApps: '関連するIT apps',
	relatedRisksTitle: '関連するリスク',
	relatedItRisksItProcessesTitle: 'ITGCs及び関連するITプロセス及びITリスク',
	testingTitle: 'テスト',
	strategyTitle: '戦略',
	yes: 'はい',
	no: 'いいえ',
	noRelatedRisks: '関連する特別な検討を必要とするリスクまたは不正リスクはありません',
	closeAllComments: 'コメントを全て閉じてください',
	closeComments: 'コメントを閉じてください',
	closeCommentsDescription: '全てのオープン及びクリア済コメントは閉じられます。この{0}に対する全てのコメントを閉じてよろしいですか？',
	addCanvasFormDigital: 'Digital',
	addCanvasFormCore: 'Core',
	addCanvasFormNonComplex: '複雑でない',
	addCanvasFormComplex: '複雑',
	addCanvasFormListed: '上場',
	addCanvasFormGroupAudit: 'グループ監査',
	addCanvasFormPCAOBFS: 'PCAOB-FS',
	addCanvasFormPCAOBIA: 'PCAOB-IA',
	addCanvasFormStandards: '基準',
	addCanvasFormLanguage: '言語',
	addCanvasFormNoResultFound: '結果が見つかりません',
	addCanvasFormStandardsNotSelectedMessage: '基準は必須フィールドです',
	addCanvasFormLanguageNotSelectedMessage: '言語は必須フィールドです',

	/* Confidentiality */
	confidentialityPlaceholder: '機密性を選択する',
	confidentiality: '機密性',
	confidentialityTitle: '文書の機密性',
	confidentialityText: 'この文書を開くために必要なアクセスレベルを設定してください。アクセスレベルの設定はチーム管理ページのエンゲージメント管理で行います。 この文書の機密性が設定済みである場合、文書を開くことができるユーザーのみが文書を変更できます。',
	confidentialityNotOpenable: 'エンゲージメントの権限が十分ではないため、文書を開けません。アクセスレベルの設定はチーム管理ページのエンゲージメント管理で行います。',
	confidentialityTargetNotOpenable: '機密文書はソースエンゲージメントからのみ開くことができます。',
	backToCCP: 'EY Canvas Client Portalに戻る',
	guidanceMessageBackToCCP: 'このフォームに入力後、EY Canvas Client Portalに戻り、EYにリクエストを提出してください。',
	noProfileInformationFound: 'プロファイル情報が見つかりません。ページを更新し再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	confirmUpdate: 'アップデートの確認',
	keepVersion: 'このバージョンを維持する',
	conflictDescription: '{0}は、このテキストが開かれてから{1}において編集されています。保持するバージョンを選択してください。',
	currentConflictVersion: '現在のバージョン',
	serverConflictVersion: 'サーバーのバージョン',
	conflictShowChanges: 'トラックの変更を表示',
	sectionViewTrackChangesDropdownPlaceholder: 'バージョンの選択',
	verifyingIndependence: '独立性ステータスを確認しています。お待ちください。',
	creatingIndependenceForm: '独立性フォームを作成します。',
	meCallFailed: 'ユーザー情報の取得に失敗しました。ページを更新し再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	getUserByIdFailed: '独立性ステータスの取得に失敗しました。ページを更新し再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	independenceFormCreationFailed: 'ユーザー独立性フォームの作成に失敗しました。ページを更新し再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	gettingProfile: 'プロファイル情報を取得しています。お待ちください。',
	invalidDocumentId: '文書Idが無効です。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	returnToEditMode: '編集モードに戻る',
	saveAndCloseButtonTitle: '保存して閉じる',
	formCreationFailed: 'フォームの作成に失敗しました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',

	/*Sign-off requirements*/
	signOffRequirements: 'サインオフ要件',
	signoffRequirementsModalTitle: 'サインオフ要件',
	signoffRequirementsModalDescription1: 'この文書のエグゼクティブサインオフの要件を以下で調整します。',
	signoffRequirementsModalDescription2: '一部のサインオフ要件は、EY Canvasで必須のため調整できません。',
	signoffRequirementsModalSaveLabel: '保存',
	signoffRequirementsModalCancelLabel: 'キャンセル',
	signoffRequirementsModalCloseLabel: '閉じる',
	signoffRequirementsModalPICLabel: 'PIC',
	signoffRequirementsModalEQRLabel: 'EQR',

	/*<Ares>*/
	/* View changes */
	viewChanges: '変更を表示',
	viewChangesModalTitle: '変更を表示',
	documentModificationAlert: 'このアクティビティの最終編集者：',
	dismiss: '却下',

	/*Task List*/
	aresPageTitle: 'EY Canvas FITイネーブルメント',
	aresPageSubtitle: 'この監査業務に関する下記必須情報の登録を完了してください。',
	summary: 'サマリー',
	aresNoDocumentFound: '選択したアクティビティで利用できる他の情報がありません',
	taskSubTitleNoValue: '利用できる説明がありません',
	mainActivities: '主な活動',
	unmarkComplete: '完了のマークを解除する',
	markCompleteTitleTip: '完了とマーク',
	disableMarkCompleteTitleTip: 'このアクティビティを完了とマークするには、全ての関連文書が少なくとも1人の作成者と1人のレビューアーにサインオフされていることを確かめて下さい。',
	/*Activity Summary*/
	activitySummary: 'アクティビティサマリー',
	selectedAnswers: '選択された回答',
	allAnswers: '全ての回答',
	incompleteResponses: '不完全な回答',
	previous: '前',
	next: '次',
	viewAsLabel: '表示方法',
	rolePreparerLabel: '作成者',
	roleDetailedReviewerLabel: '詳細レビューアー',
	roleGeneralReviewerLabel: '一般レビューアー',
	roleEQRLabel: 'EQR',
	/*Simple Helix*/
	helixPlaceholder: 'Helixスクリーンショットには、ガイダンスurlを含むガイダンスタイプ7が必要です。',
	noNotesAvailable: 'チェックマークは未作成です',
	addScreenshot: 'スクリーンショットの追加',
	replaceScreenshot: 'スクリーンショットの置き換え',
	replaceFrameDescription: '以下の分析をレビューして、置き換えをクリックして既存のスクリーンショットを置き換えてください',
	addNote: 'チェックマークの追加',
	notes: 'チェックマーク',
	noScreenshotsAvailable: '開始するには{viewDataAnalytic}をクリックしてください',
	viewDataAnalytic: 'データ分析の表示',
	/* Delete modal Helix screenshot*/
	modalTitle: 'スクリーンショットの削除',
	sureDeleteBeforeName: 'スクリーンショットを削除してもよろしいですか？',
	sureDeleteAfterName: 'スクリーンショットを削除すると関連するすべてのチェックマークも削除されます。この操作は元に戻すことができません。',

	/*uploadDocument body type */
	relateExistingDocuments: '既存文書の関連付け',
	fromEngagementOr: 'この/他のエンゲージメントから又は',
	browse: 'ブラウズ',
	toUpload: 'アップロード',
	signoffs: 'サインオフ',
	addDocument: '文書の追加',
	uploadDocument: '文書のアップロード',
	relateDocument: '既存文書を関連付ける',
	generateAccountRiskAssessmentPackage: 'グループALRAの生成',
	relateDocumentsToBodyAresTitle: '文書を関連付ける',
	discardLabel: '破棄',
	uploadDocumentLabel: '文書のアップロード',
	confirm: '確認',
	duplicateDocumentHeader: '同じ名前の文書がエンゲージメント内にすでに存在します（エビデンスまたは一時ファイルも含む）。',
	duplicateDocumentInstruction: '「上書き」を選択して文書をアップロードし、既存のファイルを置き換えるか、「破棄」を選択してキャンセルします。 この既存のファイルがエビデンスにある場合、文書はエビデンスにアップロードされます。 既存のファイルが一時ファイルにある場合、文書は一時ファイルにアップロードされます。 ',
	maxUploadFilesError: 'アップロードできる文書数は同時に最大10個までです',
	/*</Ares>*/
	noTaskRelatedToThisDocument: 'この文書に関連付けられたタスクはありません',
	uncheckTrackChangesToSave: 'JA JP Unselect the track changes option to save',
	reviewRoleCloseCommentsTitle: '未解決のコメント',
	reviewRoleCloseCommentsDesc: '対応すべき未解決のコメントがあります。フィルターを使用すれば容易に未解決のコメントを特定することができます。',

	/*Document Upload - PIC/EQR Required Body type*/
	requirementDetails: '要件の詳細',

	//Risk Factors
	riskFactor: '関連する事象及び状況/虚偽表示リスク',
	manageRisk: 'リスクの管理',
	noRiskFactors: '関連する事象及び状況/虚偽表示リスクが識別されていません',
	relateRiskFactorsToRisks: '事象及び状況の重要性を決定する',
	riskType: 'リスクタイプ',
	relateToRisk: 'リスクに関連付け',
	noRisksIdentified: 'リスクが識別されていません',
	notDefined: '定義されていません',
	selectValidRiskType: '有効なリスクタイプを選択してください',
	newRisk: '新しいリスクを追加する',
	notAROMM: '重要な虚偽表示リスクではない',
	describeRationale: '理由を記述',
	noRisksIdentifiedForTheSpecificRiskType: '{0}が識別されていません',
	addAnAccount: '追加した勘定科目を関連付ける',
	selectAnAccount: '勘定科目の選択',
	noAccountsHaveBeenIdentified: '勘定科目が識別されていません',
	accountSelected: '勘定科目',
	statementType: '文書種類',
	selectAssertions: '1つ以上のアサーションの選択',
	noAssertionsIdentifiedForAccount: 'この勘定科目にアサーションが識別されていません',
	relatedAssertions: '関連するアサーション',
	editAccount: '勘定科目及び開示の編集',
	addNewDescription: '新しい説明の追加',
	editRiskFactorDescription: '説明の編集',
	enterRiskFactorDescription: '関連する事象及び状況/虚偽表示リスクについての説明を入力',
	riskFactorDescriptionRequired: '関連する事象及び状況/虚偽表示リスクについての説明が必要',
	riskFactorDescription: '関連する事象及び状況/虚偽表示のリスクについての説明',
	createNewAccount: '新しい勘定科目の作成',
	createAccountLabel: 'アカウント {0} が正常に作成されました',
	updateAccountLabel: 'アカウント {0} への編集が正常に保存されました',
	deleteAccountLabel: 'は正常に削除されました',
	significanceLabel: '重要性',
	provideRationale: '選択内容を保存するには理由を入力してください',
	clearRiskSignificance: 'リスクの重要性と説明のクリア',
	clearSignificance: '重要性及び説明をクリアする',

	// Account Summary
	unavailable: '利用できません',
	notAvailable: '利用できません',
	fraudRisk: '不正リスク',
	significantRisk: '特別な検討を必要とするリスク',
	significantRiskIndicator: 'SR',
	fraudRiskIndicator: 'FR',
	inherentRisk: 'ROMM',
	inherentRiskIndicator: 'ROMM',
	prioryearbalance: '前期残高：',
	accounts: '勘定科目',
	accountsOther: '勘定科目-その他',
	accountsSignDis: '重要な開示',
	xMateriality: 'PMの倍数',
	xTEMateriality: 'x TE',
	estimateAssociated: '関連する見積り',
	designation: '指定',
	noAccountshasbeenIdentified: '勘定科目は識別されていません。',
	noAccountsOtherhasbeenIdentified: '他の勘定科目は認識されていません',
	noAccountsSigfhasbeenIdentified: '他の重要な開示は認識されていません',
	addOtherAccounts: '勘定科目-その他を追加する',
	addSignificantDisclosure: '重要な開示を追加する',
	pydesignation: '以前の指定:',
	notapplicable: '該当なし',
	noApplicable: '該当なし',
	changeDesignationMessage: '勘定科目の指定を変更しようとしています',
	changeDesignationTitle: '勘定科目の指定を変更',
	continue: '続行',
	currentYearBalance: '当期',
	currentPeriodBalance: '当期',
	priorYear: '前年度',
	priorYearDesignation: '前期の指定',
	priorYearEstimation: '前期の見積り',
	priorPeriodChange: '%変動',
	analytics: '分析',
	notROMMHeader: '重要な虚偽表示リスクではない',
	manageEyCanvasAccounts: 'EY Canvas勘定科目の管理',
	manageAccountMapping: '勘定科目のマッピングの管理',
	manageAccountMappingCloseButton: '終了するにはページ下部のボタンをクリックしてください。',
	manageAccountMappingToasterMessage: 'EY Helixに接続できませんでした。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	inherentRiskTypeChangeMessage: 'この勘定科目のアサーションを関連ありから関連なしに変更すると、WCGWsからアサーションまでを含むCanvasの特定の関連付けが削除され、関連するPSPsがOSPsに変換されます。続行する場合は「確認」をクリックしてください。変更せずに戻るには、「キャンセル」をクリックしてください。',

	analyticsIconDisabled: 'EY Helixはエンゲージメントに対して有効になっていません。',
	//Estimate
	estimatesTitle: '見積り',
	relatedAccount: '関連する勘定科目',
	relatedAccounts: '関連する勘定科目',
	currentBalance: '当期残高',
	priorBalance: '前期残高',
	unrelateEstimates: '関連付けられていない見積勘定',
	manageEstimates: '見積りの管理',
	noEstimatesCreated: '見積りが作成されていません。',
	createEstimates: '見積りの作成',
	estimateStarted: 'して開始',
	createEstimateDocument: '見積りの文書化の作成',
	noEstimatesFound: '見積りが識別されていません',
	relateEstimateLink: '見積りの関連付け',

	//Journal Source
	noJournalEntrySourcesAreAvailable: '利用可能な仕訳の入力ソースはありません。',
	jeSourceName: '仕訳ソース',
	jeSourceNameTooltip: '仕訳ソース',
	changeInUse: '用途の変更',
	grossValue: '関連取引総額',
	relatedTransactions: '関連取引数',
	relevantTransactions: '重要な同種類取引に関連する',
	expandAll: '全てを展開する',
	collapseAll: '全てを折りたたむ',
	descriptionLabel: 'このソースの目的について簡潔に説明してください。',
	jeSourceTypesLabel: 'このソースの仕訳入力はシステム生成ですかマニュアルですか？',
	journalEntries: 'このソースは非標準的な仕訳入力に用いられていますか。（つまり、非経常的な、通例ではない取引または修正）',
	identifySCOTsLabel: '仕訳ソースに関連するSCOTを識別して下さい（対応するものを全て選択）',
	systemGeneratedLabel: 'システム出力',
	manualLabel: 'マニュアル',
	bothLabel: '両方',
	accountEstimateLabel: '見積',
	addSCOTLabel: 'SCOTの追加',
	newSCOTLabel: '新しいSCOT',
	addSCOTModalDescription: 'SCOTを作成できます。保存すると変更が適用されます。',
	scotnameRequired: 'SCOT名は必須です。',
	scotCategoryRequired: 'SCOTカテゴリーは必須です。',
	estimateRequired: '見積りは必須です。',

	jeSourcesLabel: '仕訳ソース',
	jeSourcesToSCOTs: 'SCOTに対応する仕訳ソース',
	scotsToSignificantAccounts: '重要勘定に対応するSCOT',

	//Modal common labels.
	modalCloseTitle: '閉じる',
	modalConfirmButton: '変更を保存する',
	modalCancelTitle: 'キャンセル',
	modalSave: '保存',
	modalSaveAndClose: '保存して閉じる',
	modalSaveAndAdd: '追加して保存する',
	modalSaveAndCreateAnother: '保存して別のものを作成',

	//Add & Manage Risks
	addNewRiskModalTitle: '新しいリスクを追加する',
	manageRisksListModalTitle: 'リスクの管理',
	riskInfoMessage: '保存すると変更が適用されます。',
	risksListInstructionalText: '既存のリスクを編集できます。必要に応じて、新しく追加することもできます。',
	risksListEmptyArray: 'リスクがありません。新しいリスクを追加して開始してください。',
	addNewRiskButtonLabel: '新しいリスクを追加する',
	labelRiskName: 'リスク名',
	riskDescriptionLabel: 'リスクの説明',
	selectRiskType: 'リスクタイプ',
	requiredRiskName: 'リスク名は必須です。',
	requiredRiskType: 'リスクタイプは必須です。',
	deleteRiskTrashLabel: 'リスクの削除',
	undoDeleteRiskTrashLabel: '削除の取り消し',
	notARommLabel: '重要な虚偽表示リスクがありません',
	identifiedRiskFactors: '識別された事象/状況/虚偽表示リスク、重要な虚偽表示リスク、特別な検討を必要とするリスク、不正リスク',
	noneIdentified: '識別なし',
	countUnassociatedRisk: '事象/状況/虚偽表示リスクが、「重要な虚偽表示リスクではない/重要な虚偽表示リスク」に関連付いていない/マークされていない。',

	// Bar Chart / Account Summary
	accountsTotal: '{0} 勘定科目の合計',
	accountSummary: '勘定科目サマリー',
	allAccounts: '全ての勘定科目',
	significantAccountsBarChart: '重要な勘定科目',
	limitedAccounts: 'リスクが限定的な勘定科目',
	insignificantAccounts: '軽微な勘定科目',
	noAccountsHasBeenIdentifiedBarChart: '{0} は識別されていません。',
	selectedTotalAccountsCounter: '{0}/{1} 勘定科目',
	identifyInsignificantAccounts: '軽微な勘定科目の識別',
	identifySignificantAccounts: '重要勘定の識別',
	identifyLimitedAccounts: 'リスクが限定的な勘定科目の識別',
	preInsigniAccounts: '従前軽微な勘定科目と識別されていたもののうち当年度TEを下回らないもの',
	nonDesignatedInsignificant: 'この勘定科目は軽微に指定できません。ドロップダウンより指定の変更を行ってください。',
	tolerableError: 'TEが指定されていません。重要性を更新の上再試行してください',
	documentContainerLabel: '文書',
	clickcreateformtogenerate: '{0}をクリックするとリスクが限定的な勘定科目の文書を生成できます',
	createform: 'フォームの作成',
	createLimitedRiskAccountDocumentation: 'リスクが限定的な勘定科目の文書化の作成',
	limitedAccountDocumentName: 'リスクが限定的な勘定科目の文書化',

	//Modal Confirm Switch account
	modalSwitchTitle: '未保存の変更',
	modalConfirmSwitch: '確認',
	modalConfirmSwitchDescription: '変更は保存されず、続行すると失われます。続行しますか?',

	//Modal Edit Account
	manageAccountsModalTitle: 'EY Canvas勘定科目の管理',
	editAccountLinkLabel: '勘定科目の編集',
	editAccountInstructionalText: '既存のEY Canvas 勘定科目を編集または削除したり、新規勘定科目を作成できます。保存すると変更が適用されます。',
	selectAnAccountLabel: '勘定科目の選択',
	accountNameLabel: '勘定科目名',
	accountLabel: '勘定科目',
	accountDesignationLabel: '勘定科目の指定',
	accountStatementTypeLabel: '文書種類',
	accountRelatedAssertionsLabel: '関連するアサーション',
	accountHasEstimateLabel: '勘定科目は見積りの影響を受けますか？',
	requiredAccountName: '勘定科目名は必須です',
	requiredAccountDesignation: '勘定科目の指定は必須です',
	requiredStatementType: '文書種類は必須です',
	requiredRelatedAssertions: 'アサーションの選択',
	pspIndexDropdownLabel: 'PSPインデックスの選択',
	removePSPIndexLabel: 'PSPインデックスの削除',
	addPSPIndexLink: 'PSPインデックスの追加',
	pspIsRequired: 'PSPインデックスは必須です',

	//Delete account modal
	deleteAccount: '勘定科目の削除',
	deleteAccountModalMessage: '選択した勘定科目を削除してよろしいですか？',
	cannotBeUndone: '元に戻すことはできません。',
	guidedWorkflow: 'EY Canvas FITイネーブルメント',
	scotSummary: 'SCOTサマリー ',
	scopeAndStrategy: 'スコープ及び戦略',
	ToggleSwitch: {
		Inquire: '質問',
		Completed: '完了',
		isOn: 'はい',
		isOff: 'いいえ '
	},
	navExecution: '実施',
	navCanvasEconomics: 'EY Canvas Economics',
	navOversight: 'EY Canvas Oversight',
	navConclusion: '結論',
	navTeamMemberIndependence: 'チームメンバーの独立性',
	navGroupAudit: 'グループ管理',
	navGroupActivityFeed: 'グループアクティビティフィード',
	navPrimaryStatus: 'プライマリーステータス',
	navComponentStatus: 'コンポーネントステータス',
	navGroupStatus: 'グループステータス',
	navEngagementManagement: 'エンゲージメント管理',
	navProfile: 'プロファイル',
	navItSummary: 'ITサマリー',
	nav440GL: '報告日後の変更',
	navGroupStructureSummary: 'グループ構造',
	navGroupInstructionSummary: 'グループインストラクション',
	navGroupInvolvement: 'Group involvement',
	navNotApplicable: '該当なし',
	cropScreenshot: 'スクリーンショットの切り取り',
	cropScreenshotModalDescription: 'スクリーンショットをトリミングして、関連する部分のみを含めることができます。トリミングすると既存の注釈は削除されますが、チェックマークは保持され、再度チェックマークを付けることができます。トリミングは元に戻せません。',
	crop: '切り取り',
	replace: '置き換え',
	nameTheScreenshot: 'スクリーンショット名',
	nameLabel: '名称',
	takeScreenshot: 'スクリーンショットの追加',
	measurementBasis: '測定の基礎',
	MeasurementBasisMessage: '基礎となるEY Helixデータマッピングに基づくと、選択された算定基礎が予想ポジションにないようです (例：借方ポジションの税引前利益)。次の点を考慮してください。',
	measurementBasisProjectMappedCorrectly: 'EY Helixプロジェクトのデータは正しくマッピングされているか、',
	measurementBasisAppropriateValue: '別の算定基礎が適切な場合があるか、または、',
	measurementBasisAdjustValue: '以下のように算定基礎値を調整することが適切な場合があるか。',
	basisValueFromHelix: '試算表の値',
	rationaleDeterminationLabel: '金額決定の根拠',
	loginInstructions: 'ログインの上インストラクションに従いアカウントをセットアップしてください',
	gotoText: 'Go to',
	asOfDate: '基準日',
	annualizedBasisValue: '年換算額',
	basisValue: '基礎値',
	isAnnualizedAmountRepresentative: '年換算額が報告期間の見込額となっていますか？',
	isAnnualizedAmountRepresentativeForAssetsOrEquity: '監査期間終了時において想定される値となっていますか？',

	enterExpectedFinancialPerioadAmount: '監査期間終了時において想定される値を入力してください',
	enterRationaleAmountDetermined: '当該金額の決定根拠を入力',
	printNotAvailable: '{0} にはコンテンツがないため、情報は表示されません',
	canvasFormPrintNotAvailable: 'Canvasフォームの印刷が現在利用できません。エラーが続く場合は、再試行するか、ヘルプデスクに連絡してください。',
	saving: '保存中',
	removing: '削除中…',
	activitySummaryTitle: 'Activity summary',
	currentLabel: '当年度',
	PMLabel: 'PM',
	planningMaterialityLabel: '監査計画上の重要性の基準値',
	TELabel: 'TE',
	tolerableErrorLabel: '許容誤謬額',
	SADLabel: 'SAD',
	SADNominalAmountLabel: '監査差異要約表記載基準額',
	PriorLabel: '前年度',
	editRichText: '文書の編集',
	noTypeTwoResponseAvailable: '利用可能な応答がありません。<br /> {clickHere} をクリックして返信してください。',
	clickHere: 'こちら',
	helixNavigationTitle: 'EY Helix設定ページよりEY Helixプロジェクトのリンクや設定を実施してください',
	helixNavigationLink: 'EY Helixへ移動',
	measurementBasisValue: '測定の基礎値',
	inlineSaveUnsavedChanges: '未保存の変更があります。続行しますか',
	rationaleIncomplete: '不十分な根拠',
	projectMismatchDisplayMessageOnDataImport: '主要なEY Helixプロジェクトが変更されました。EY Helixの設定を確認し新しいプロジェクトからデータをインポートしてください。',
	importSuccess: 'EY Helixデータのインポートに成功しました。',
	importHelix: 'インポートをクリックすると、EY HelixからGLデータがインポートできます。',
	importLabel: 'インポート',
	reImportLabel: '再インポート',
	lastImportedBy: '最終インポート者',
	lastImportedOn: '最終インポート日',
	dataImportedFromProjec: 'プロジェクトからインポートされたデータ',
	reImportConfirmationTitle: 'EY Helixからのデータの再インポート',
	reImportConfirmationMessagePart1: 'EY Helixからデータの再取り込みを行うと、関連するアクティビティ内で既存のデータが変更される、もしくは、新しいデータが追加され、これを元に戻すことはできません。',
	reImportConfirmationMessagePart2: 'データの再取り込みプロセスがEY Canvas FITイネーブルメントに及ぼす影響の詳細と概要については、EY Atlasを参照してください。',
	defineRisksTitle: 'リスクの定義付け',
	assessAndDefineRisksTitle: 'リスクの評価及び定義付け',
	identifiedRiskFactorsTitle: '識別された事象/状況及び関連するリスク：',
	descriptionIncompleteLabel: '不十分な説明',
	noRiskHasBeenRelatedMsg: '関連付けられたリスクなし',
	rationaleIncompleteMsg: '不十分な根拠',
	loading: '読込み中…',
	importInProgressLabel: 'インポート中です。これには数分かかる場合があります。ページを更新して更新されたステータスを確認してください。',
	importInProgressMessage: 'EY Helixデータのインポート中です。ページを更新してインポートのステータスを確認してください。',
	importHelixProjectError: 'EY Helixからのデータのインポート中にエラーが発生しました。EY Helixプロジェクトのステータスが「Analytics利用可能」と表示されていることを確認し、ページを更新して、再度「インポート」または「再インポート」をクリックします。問題が続く場合は、ヘルプデスクに連絡してください。',
	importDeletionConfirmationMsg: 'EY Helixデータのインポートを削除してよろしいですか？インポートを削除すると関連するアクティビティ内で既存のデータが削除され、これを元に戻すことはできません。',
	deletePreviousImport: 'EY Helixインポートの削除',
	//Assess Risks - Summary Page
	assessRisksAccordionLabel: 'リスクの関連付け',
	assessRisksNoItemsFound: '識別されたリスクなし',
	assessRiskAccountsAndRelatedAssertions: '勘定科目及び関連アサーション',
	assessRiskNoAccountsLinked: '関連付けられた勘定科目なし',
	accountRiskAssessmentSummary: '勘定科目と開示',
	// Flow chart
	flowchartTitle: 'フローチャート',
	launchFlowchart: 'フローチャートの開始',
	clickherelink: 'ここをクリック',
	orderToCashPlacement: '受注から現金回収',
	orderToCashPlacementMessage: 'プログラム部門は、新規顧客との契約交渉、既存顧客との契約更新や契約変更の交渉を行う。契約には、価格、条件、保証などの詳細が含まれる。',
	saveFlowChart: '保存',
	newstep: '新しいステップ',
	zoomIn: '拡大',
	zoomOut: '縮小',
	resetZoom: '拡大をリセットする',
	toogleInteractivity: 'トグルの双方向性',
	fitView: '全体表示',
	numberOfSteps: 'ステップの数',
	flowchartSuccessfullyCreated: 'フローチャートが正常に作成されました。',
	flowchartLinkedEvidenceMessage: 'このフローチャートは別のエンゲージメントで作成されました。エビデンスのリンクが解除されると、このエンゲージメントのフローチャートへのアクセス権も削除されます。',
	flowchartSmartEvidenceSourceIdNullMessage: '利用可能なSCOTはありません。',
	noTaskDocumentAvailableFlowchart: 'この文書は一時ファイルです。フローチャートの詳細にアクセスするには、エビデンスとしてタスクに関連付けてください。',
	// Control Attributes
	controlAttributes: '統制の属性',
	noControlAttributes: '利用可能な統制がありません',
	flowchartStepMoremenu: 'その他のメニュー',
	createControlAttributes: '利用可能な統制の属性がありません。<br /> {clickHere}をクリックし新しい統制の属性を作成します。',
	createNewControlAttribute: '新しい属性',
	editControlAttribute: '属性の編集',
	createControlAtttributeInstructions: "以下の属性の詳細を入力し、<b>'保存して閉じる'</b>を選択して完了します。別の属性を作成するには、 <b>\'保存して別のものを作成\'</b>を選択してください。属性は属性インデックスに基づきソートされます。 ",
	editControlAttributeInstructions: "以下の属性の詳細を編集し<b>\'保存する\'</b>を選択して終了します。属性は属性インデックスに基づきソートされます。 ",
	editAttributeButtonLabel: '属性の編集',
	deleteAttributeButtonLabel: '属性の削除',
	deleteControlAttributeInstructions: '選択した属性を削除してよろしいですか？この操作は元に戻すことができません。',
	// Control Attributes Form
	requiredAttributeIndexLabel: '属性インデックス（必須）',
	requiredAttributeDescriptionLabel: '属性の説明（必須）',
	errorMessageAttributeIndexRequired: '必須',
	errorMessageAttributeDescriptionRequired: '必須',
	errorMessageAttributeDescriptionMaxLength: '回答が{#}文字であり、最大の文字数である{##}文字を超えています。テキストの文字数を減らす、書式を変更することで説明を調整し、再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	errorMessageAttributeTestingTypesRequired: '必須',
	proceduresLabel: '実施すべき手続',
	modalRequiredProceduresLabel: '実施すべき手続（必須）',
	attributeTestingTypesLabel: {
		inquiry: '質問',
		observation: '観察',
		inspection: '閲覧',
		reperformance: '再実施/再計算',
	},

	/*CRA Badge*/
	ir: 'IR',
	cr: 'CR',
	cra: 'CRA',
	incompleteCra: '未完了のCRA',
	incomplete: '未完了',

	//Progess bar labels
	savingProgress: '保存中',
	discardChangesLabel: '変更の破棄',
	removeFromBody: '本文から削除',
	uploading: 'アップロード中…',
	uploadComplete: 'アップロード完了',
	downloadComplete: 'ダウンロード完了',
	processing: '処理中…',

	/* ISA BODIES */
	/* Common */
	deleteEntityConfirmation: ' <b>{0}</b>を削除してよろしいですか？この操作は元に戻せません。',
	/* ITAPP-SCOT */
	searchScot: 'SCOTの検索',
	addITApp: 'ITアプリケーションの追加',
	relatedItApp: '関連するIT Apps',
	itApplicationHeader: 'ITアプリケーション',
	scotNoDataPlaceholder: '利用可能な情報はありません',
	noScotsOrControlsPlaceholder: '関連するSCOTsまたは統制がありません; {noScotsOrControlsPlaceholderEditAssoc}。',
	noScotsOrControlsPlaceholderEditAssoc: '関連付けの編集',
	noScotsOrControlsPlaceholderTarget: '関連するSCOTsまたは統制がありません',
	scotHeader: 'SCOTs',
	controlsHeader: '統制',
	controlsApplicationHeader: 'アプリケーション',
	controlsITDMHeader: 'IT依存手作業統制',
	itAppScotNoDataPlaceholderLabel: '追加されたITアプリケーションがありません',
	itAppScotNoDataPlaceholder: '追加されたITアプリケーションはありません。<br />始めるにはこちらをクリック{itAppScotNoDataPlaceholderAddItApp}',
	itAppScotNoDataPlaceholderAddItApp: 'ITアプリケーションの追加',
	editItAppOption: 'ITアプリケーションの編集',
	removeItAppOption: 'ITアプリケーションの削除',
	viewItAppOption: 'ITアプリケーションの表示',
	editScotsISA: 'SCOTの編集',
	viewScotISA: 'SCOTの表示',
	viewControlISA: '統制の表示',
	scotAndRelatedControls: 'SCOTs及び統制',
	otherControls: 'その他の統制',
	controlTypeLabel: '統制のタイプ',

	/*SCOT-ITAPP*/
	addOrRelateItAppPlaceholder: '{identifyRelatedItApps}または{documentScotHasNoItApps}を文書化してください。',
	identifyRelatedItApps: '関連するITアプリケーションの識別',
	documentScotHasNoItApps: 'SCOTに関連するITアプリケーションがありません',
	correctScotDocumentationPlaceholder: 'このSCOTはITアプリケーションでサポートされていないものとして指定されています。アプリケーション統制または IT依存手作業統制がこのSCOT、{correctScotDocumentation}に関連付けられています。',
	correctScotDocumentation: 'このSCOTをサポートするITアプリケーションはないという指定を再検討してください',
	controlsWithoutRelatedItApps: '関連するITアプリケーションのない統制',
	controlsWithRelatedItApps: '関連するITアプリケーションがある統制',

	/*AddEditITApplication */
	saveAndCreateNewButtonTitle: '保存して新規作成',
	instructionalMessage: 'ITアプリケーションを追加し関連するSCOTsを選択してください。必要に応じて、ITアプリケーションに統制を関連付けてください。',
	ITApplicationNamePlaceholder: 'ITアプリケーション名称（必須）',
	scotDropdownPlaceholderText: 'ITアプリケーションに関連付けるSCOTsを選択',
	selectScotPlaceholderText: 'ITアプリケーションに関連付けるSCOTsを選択',
	selectControlPlaceholderText: 'ITアプリケーションに関連付ける統制を選択',
	noRelatedScotsPlaceholderText: '関連するSCOTsがありません',
	noRelatedControlsPlaceholderText: '関連する統制がありません',
	CreateSOModelTitle: 'サービス受託会社の追加',
	CreateSOInstructionalMessage: "新しいサービス受託会社の詳細を以下に入力し、'<b>{0}</b>'を選択して終了します。別のサービス受託会社を作成するには、'<b>{1}</b>'を選択します。`, //'新しいサービス受託会社を作成し、関連するSCOT及び統制を関連付けます。 ",
	saveAndCloseLabel: '保存して閉じる',
	saveAndCreateLabel: '保存して別のものを作成',
	SONamePlaceholder: 'サービス受託会社名称（必須）',
	SOSelectScotPlaceholderText: 'サービス受託会社に関連するSCOTsの選択',
	SOSelectControlPlaceholderText: 'サービス受託会社に関連する統制の選択',
	CreatedSOLabel: '追加したサービス受託会社',
	createdITAppLabel: '追加したITアプリケーション',
	searchNoResultFoundText: '結果が見つかりません',
	searchNoResultsFoundText: '結果が見つかりません。',
	iTApplicationNameRequired: 'ITアプリケーション名称は必須です',
	soNameRequired: 'サービス受託会社名称は必須です',
	editITAppDesctiptionLabel: 'ITアプリケーションと関連するSCOTs及び統制の編集',
	editSODescriptionLabel: '以下のサービス受託会社の詳細を編集し、<b>「保存」</b>を選択して終了します。',
	viewITApplication: 'ITアプリケーションの表示',
	itApplicationName: 'ITアプリケーション名',
	serviceOrganizationName: 'サービス受託会社名',
	newItApplication: '新しいITアプリケーション',

	/*Add/Edit ITProcess*/
	itProcessName: 'ITプロセス名称',
	addItProcessDescription: 'ITプロセスの作成',
	addItProcess: 'ITプロセスの追加',
	itProcessNameRequired: 'ITプロセス名称は必須です',
	editItProcess: 'ITプロセスの編集',
	editItProcessDescription: 'ITプロセスの編集',
	viewItProcess: 'ITプロセスの表示',
	taskTitle: 'ITプロセスの理解及び文書化：{0}',
	taskDescription: 'ITプロセスの理解を文書化してください。ITプロセスの理解をサポートするエビデンスとして関連フォームを添付してください。<br />タスク属性セクションにITGCが表示される場合、ウォークスルー手続を実施してITGCの理解を確認し、デザイン及び業務への適用の評価を行ってください。実施した手続のエビデンスを添付してください。',
	newItProcess: '新しいITプロセス',
	noITProcessesFound: 'ITプロセスが識別されていません',

	/* IT Process - Task */
	itProcessSearchPlaceholder: 'ITプロセスの検索',
	itProcessHeader: 'ITプロセス',
	itProcessTasksHeader: 'タスク',
	itProcessAddUPD: 'UDPの追加',
	itProcessEditItProcess: 'ITプロセスの編集 ',
	itProcessRelateUDP: 'UDPに関連付けてください',
	itProcessViewProcess: 'ITプロセスの表示',
	itProcessNoDataPlaceholder: 'ITプロセスが追加されていません。<br /> 開始するには、{addItProcess}をクリックしてください。',
	itProcessSourceInstructionalText: '少なくとも１つのUDPがITプロセスに関連していなければなりません。{itProcessSourceInstructionalTextCreateUdp}または {itProcessSourceInstructionalTextRelateUdp}',
	itProcessSourceInstructionalTextCreateUdp: '新しいUDPの作成',
	itProcessSourceInstructionalTextRelateUdp: '既存UDPの関連付け',
	itProcessTargetInstructionalText: 'ITプロセスに関連するUDPはありません。',

	/* IT APP IT PROCESSES RELATION*/
	itApplicationHeaderRelate: 'ITアプリケーション',
	itProcessesHeaderRelate: 'ITプロセス',
	itAppNoDataPlaceHolderLabel: '識別されたITアプリケーションがありません',
	itAppNoDataPlaceHolder: 'ITアプリケーションが識別されていません。<br />{identifyItApp}',
	identifyItApp: 'ITアプリケーションを識別する。',
	itProcessNoDataPlaceHolderRelationLabel: '識別されたITプロセスがありません',
	itProcessNoDataPlaceHolderRelation: 'ITプロセスが識別されていません。 <br /> {identifyItProcess}',
	identifyItProcess: 'ITプロセスを識別する。',
	editItApp: 'ITアプリケーションの編集',
	deleteItApp: 'ITアプリケーションの削除',
	drag: 'ITプロセスをドラッグしてITアプリケーションに関連付けます',
	// editItProcess: 'Edit IT process',
	deleteItProcess: 'ITプロセスの削除',
	unassociatedProcess: '{0}関連付けられていないプロセス',
	unassociatedItApplications: '{0} 関連付けられていないITアプリケーション',
	showOnlyUnrelated: '非関連のみ表示 - {0}',
	searchItProcess: 'ITプロセスの検索',
	searchItApplication: 'ITアプリケーションの検索',
	itProcessesLabel: 'ITプロセス',
	itApplicationsLabel: 'ITアプリケーション',
	showAllItAplications: '全てのITアプリケーションの表示',
	showAllItProcesses: '全てのITプロセスの表示',
	relatedToLabel: " <span class='parent-entity-object-name'>{parent}</span>に関連するすべての <span class='child-entity-count'>{count}</span> <span class='child-entity-name'>{child}</span> のリスト ",

	/* IT Process > IT Risk */
	itProcessItRiskNoDataPlaceholder: 'ITプロセスが識別されていません。<br/>{identifyItProcess}',
	itProcessItRiskNoDataPlaceholderTarget: 'ITプロセスが識別されていません。',
	itApplication: 'IT Apps',
	itGC: 'ITGCs',
	addItRiskBtnTitle: 'ITリスクの追加',
	itProcessItRiskUnrelatedITGC: '関連付けられていないITGCs',
	itProcessItRiskUnrelatedITGCUppercase: '関連付けられていないITGCs',
	itProcessItRiskNoRisksNoControlsPlaceholder: 'このITプロセスのITアプリケーションに関連する有効なデザインの評価のアプリケーションまたは IT依存手作業統制がないため、このITプロセスに ITリスクまたはITGCsを含める必要はありません。',
	itProcessItRiskNoRisksControlsPlaceholder: '識別されたアプリケーション及びIT依存手作業統制に基づいて、このITプロセスに対する{itRiskIdentify}',
	itRiskIdentify: 'ITリスクを識別してください',
	itProcessItRiskItProcessContentTitle: 'ITリスク及びITGCs',
	itProcessItRiskItRiskNoItgcRequiredPlaceholder: 'ITリスクに対応するITGCsが存在しません。',
	itProcessItRiskItRiskItgcRequiredPlaceholder: '識別された全てのリスクには少なくとも1つのITGCが識別されているか、ITリスクに ITGCsがないと指定する必要があります。<br/>ITリスクに対応する{newITGC}または{existingITGC}を識別するか、ITリスクに対応する{noItRisksIdentified}を示してください。',
	noItRisksIdentified: 'ITGCsなし',
	newITGC: '新規のITGC',
	existingITGC: '既存のITGC',
	unrelatedItGCModalMessage: '不要になった関連付けられていないITGCsは削除してください。',
	unrelatedItGCModalNoDataPlaceholder: '関連付けられていないITGCはありません',
	removeItRisk: 'ITリスクの削除',
	deleteItRiskConfirmation: 'ITリスクを削除してよろしいですか <b>{0}</b>? これによってITリスクは削除され元に戻すことはできなくなります。',
	relateItGcTitle: 'ITGCの関連付け',
	relateItGcEntityTitle: 'ITリスク',
	relateItGcDescription: 'ITリスクに対応するITGCsを選択する',
	relateItGcSearchPlaceholder: 'ITGCの検索',
	relateItGcShowSelectedOnlyText: '関連するITGCsのみ表示',
	relateItGcNoDataPlaceholder: 'ITGCsがありません。新しくITGCを作成して続けて下さい。',
	relateITSPTitle: 'ITSPの関連付け',
	relateITSPDescription: 'ITリスクに対応するITSPsを選択する',
	relateITSPSearchPlaceholder: 'ITSP名で検索',
	relateITSPShowSelectedOnlyText: '関連するITSPsのみ表示',
	relateITSPNoDataPlaceholder: 'ITSPがありません。新しくITSPを作成して続けて下さい。',

	/* IT Process Task Relationship */
	relateUDP: 'UDPの関連付け',
	relateUDPDescription: 'ITプロセスに対応するUDPタスクを選択する',
	relateUDPListHeaderItemName: 'タスク名',
	relateUDPSearchPlaceholder: 'タスク名で検索',
	relateUDPNoResultsFoundPlaceholder: '該当ありません',
	relateUDPCountLabel: '{0} タスク',
	relateUDPClose: '閉じる',
	relateUDPShowOnlyRelatedTasks: '関連するタスクのみ表示',
	relateUDPNoDataFoundPlaceholder: 'タスクがありません',
	relateUDPNoDataPlaceHolder: 'ITプロセスが識別されていません',

	/* ITGC test strategy */
	itProcessItRiskItGcWithoutDesignEffectiveness: 'デザインが有効でないITGC',
	searchItGC: 'ITプロセスの検索',
	itGCNoDataPlaceHolder: 'ITプロセスが識別されていません',
	addItRisks: 'ITリスクの追加',
	itDMHeader: 'IT依存手作業統制',
	itAppHeader: 'ITアプリケーション',
	itTestHeader: 'テスト',
	itTestingHeader: 'テスト',
	itgcHeader: 'ITGCs',
	controlsSelectedHeader: 'テストのために選択された統制',
	iTRisksAndITGCs: 'ITリスク及びITGCs',
	NoITGCForITRiskPlaceholder: 'このITリスクに対応するITGCsはIT環境にありません。',
	ITGCsNotIdentifiedRiskNoITGCs: 'このリスクに対するITGCsが識別されていません。{identifyAnITGC} または{itRiskHasNoITGCs}を指定します。',
	identifyAnITGC: 'ITGCの識別',
	itRiskHasNoITGCs: 'ITリスクにITGCsなし',

	/**
	 * IT SO > SCOT
	 */
	searchItSO: 'サービス受託会社の検索',
	addItSOBtnTitle: 'サービス受託会社の追加',
	itSoNoDataPlaceHolder: 'サービス受託会社が識別されていません。<br/><a>{identifyAnSo}<a/>',
	noItSoDataPlaceHolder: 'サービス受託会社が識別されていません。',
	identifyAnSo: 'サービス受託会社の識別',
	soHeader: 'サービス受託会社',
	editSO: 'サービス受託会社の編集',
	deleteSO: 'サービス受託会社の削除',
	viewSO: 'サービス受託会社の表示',
	controlRelatedToSO: 'サービス受託会社に関連する統制',

	/**
	 * Manage IT SP
	 */
	addITSP: 'ITSPの追加',
	searchPlaceholderManageITSP: 'ITプロセスの検索',
	noManageITSPDataPlaceholder: 'ITプロセスが識別されていません',
	itRiskColumnHeader: 'ITリスク',
	itDesignEffectivenessHeader: 'デザインの有効性',
	itTestingColumnHeader: 'テスト',
	itGCColumnHeader: 'ITGCs',
	itSPColumnHeader: 'ITSPs',
	searchClearButtonTitle: 'クリア',
	itProcessItRiskUnrelatedITSP: ' 関連付けられていないITSPs',
	manageITSPUnrelatedITSPUppercase: '関連付けられていないITSPs',
	unrelatedITSPModalMessage: '不要な関連付けられていないITSPsを削除する',
	unrelatedITSPModalNoDataPlaceholder: '関連付けられていないITSP無し',
	noITGCPlaceholderMessageFragment1: '識別された全てのリスクには少なくとも1つのITGCが識別されているか、ITリスクにITGCsがないことを指定する必要があります',
	noITGCPlaceholderMessageFragment2: '識別',
	noITGCPlaceholderMessageFragment3: '新規のITGC  ',
	noITGCPlaceholderMessageFragment4: 'または',
	noITGCPlaceholderMessageFragment5: '既存のITGC ',
	noITGCPlaceholderMessageFragment6: 'ITリスクに対応するか、又は',
	noITGCPlaceholderMessageFragment7: 'ITGCsなし',
	noITGCPlaceholderMessageFragment8: 'ITリスクに対応する',
	addNewITSP: '新規のITSPの追加',
	addExistingITSP: '既存のITSPの追加',
	noITSPPlaceholderMessageFragment1: 'ITGCsが非有効と評価した場合、またはITリスクに対応するITGCsが存在しないと判断した場合に、非有効のITGCに関連するITプロセス内のITリスクが悪用されていないという合理的な保証を得るために、IT-実証テスト手続(ITSPs) を実施することがある。',
	noITSPPlaceholderMessageFragment2: '新規のITSPの識別',
	noITSPPlaceholderMessageFragment3: 'または',
	noITSPPlaceholderMessageFragment4: '既存のITSPに関連付ける',
	noITSPPlaceholderMessageFragment5: '.',
	noITGCsExitForITRisk: 'ITリスクに対応するITGCがありません。',
	noITSPExitForITRisk: 'ITリスクに対応するITSPが識別されていません。',
	manageITSPItemExpansionMessage: 'ITリスク',
	noITGCExists: 'ITリスクに対応するITGCsが存在しません。',
	iTGCName: 'ITGC名称',
	itSPName: 'ITSP名',
	operationEffectiveness: '運用の有効性',
	savingLabel: '保存中',
	deletingLabel: '削除中',
	removingLabel: '削除中',
	itFlowModalDescription: ' {itSummaryLink} に進み、エンゲージメントに不要なオブジェクトを編集/削除する。',
	itSummaryLink: 'ITサマリー画面',
	manageITSPYes: 'はい',
	manageITSPNo: 'いいえ',

	understandITProcess: 'ITプロセスの理解',
	activity: 'アクティビティ',
	unsavedPageChangesMessage: '続行すると失われる未保存の変更があります。このページから移動してよろしいですか？',
	unsavedChangesTitle: '未保存の変更',
	unsavedChangesLeave: 'このページから移動',
	unsavedChangesStay: 'このページにとどまる',

	notificationDownErrorMessage: '通知機能は一時的に利用できません。ページを更新し再試行してください。このメッセージが続く場合は、ヘルプデスクに連絡してください。',
	notificationUpbutSomeLoadingErrorMessage: '技術的なエラーが発生し、通知機能が動作しません。ページを更新し再試行してください。',
	markCompleteError: '提示されるすべての文書には、少なくとも1人の作成者と1人のレビューアーがサインオフすることが必要です。',
	markCompleteDescription: 'このアクティビティを完了とマークするには、全ての文書が少なくとも1人の作成者と1人のレビューアーにサインオフされている必要があります。 ',
	lessthan: 'より少ない',
	openingFitGuidedWorkflowFormError: 'EY Canvas FITイネーブルメントフォームを開けません',
	timeTrackerErrorFallBackMessage: 'タイムトラッキング機能は一時的に利用できません。ページを更新し再試行してください。このメッセージが続く場合は、ヘルプデスクに連絡してください。',
	timeTrackerLoadingFallbackMessage: 'タイムトラッキング機能は一時的に利用できません。まもなく利用可能になります。',
	priorPeriodRelateDocument: '前期のエビデンスを関連付け',
	selectedValue: '選択した値',
	serviceGateway: 'サービスゲートウェイ',
	docNameRequired: '名前を空欄にはできません',
	docInvalidCharacters: '以下は名前に使用できません： */:<>\\?|"',
	invalidComment: 'コメントを追加できませんでした。リスト内の複数の項目を選択した場合は、1つの項目だけを選択して再試行してください。エラーが続く場合は、ページを更新し再試行するかヘルプデスクに連絡してください。',
	inputInvaildCharacters: 'インプット名として「: */:<>\\?|"」の文字は使用できません',

	// FIT Navigation panel
	relatedActivities: '関連するアクティビティ',
	backToRelatedActivities: '関連するアクティビティに戻る',
	backToMainActivities: '主要アクティビティに戻る',
	formOptions: 'フォームオプション',

	// FIT Sharing
	shareActivity: 'アクティビティの共有',
	shareLabel: '共有',
	shareInProgress: '共有中',
	manageSharing: 'このアクティビティの共有には「EY Canvas FIT イネーブルメントの共有管理」のユーザー権限が必要です。「チーム管理」のページに移動し、権限を管理するか、他のチームメンバーに連絡してください',
	dropdownPlaceholderSA: '共有するエンゲージメントを選択する',
	fitSharingModalInfo: 'このアクティビティを同じエンゲージメント又は同じワークスペースの他のエンゲージメントのアクティビティと共有してください。選択したアクティビティがまだ共有されていない場合、以下で選択したアクティビティの応答は上書きされます。アクティビティが共有されている場合、選択できるのは1つだけで、このアクティビティの応答は上書きされます。',
	lastModifiedDate: '最終更新日：',
	noActivityToShare: '共有できる利用可能なアクティビティはありません',
	activityNotShared: '{0} が共有されませんでした。',
	activityShareSuccessfull: '{0} の共有に成功しました。',
	sharedWithAnotherFITActivity: 'このアクティビティは別のアクティビティと共有されています。',
	sharedActivityWithAnotherCanvas: 'アクティビティを別のEY Canvas FITイネーブルメントと共有',
	shareActivityModalTitle: 'アクティビティの共有',
	showRelationshipsTitle: '関連性を表示',
	shareActivityEngagement: 'エンゲージメント',
	shareActivityRelationshipsModalTitle: '共有されたアクティビティの関連性',
	shareActivityWorkspaceHeading: 'このアクティビティは以下のエンゲージメントで共有されており、同じワークスペース間で関連付けがなされています。',
	shareModalOkTitle: '共有',
	shareModalContinueLabel: '続行',
	selectedActivityInfoLabel: '選択中のエンゲージメントの最終編集日：',
	noSharedActivityInfoLabel: 'このエンゲージメントには、共有する同じ種類の別の文書がありません。',
	alreadyHasSharedActivityInfoLabel: '選択したアクティビティは既に他のアクティビティと共有されています。現在のアクティビティを共有することは選択しているアクティビティの回答を現在のアクティビティへ同期することになります。',
	selectActivityResponsesForSharingLabel: 'どの文書の回答を他に置き換えるかを選択してください：',
	selectActivityResponsesForCurrentRadioLabel: '現在の文書からの回答を上記の選択中の文書へ共有する',
	selectActivityResponsesForSelectedRadioLabel: '上記で選択した文書からの回答を現在の文書へ共有する',
	selectActivityResponsesWarningEarlierTimeLabel: "JA JP The current activity was modified at an earlier time compared to the selected engagement's activity. Please consider this before confirming the sharing option's below the table.",
	selectActivityResponsesWarningModifiedMoreRecentlyLabel: '現在のアクティビティは、選択した文書のアクティビティよりも最近変更されました。上記の共有された選択肢を確認する前に、この点を考慮してください。',
	selectActivityUnsuccessfulMessage: '共有できませんでした。再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	otherEngagemntDropdownlabel: 'ワークスペースにある他のエンゲージメント：',
	documentSearchPlaceholder: '文書を検索',
	showOnlySelected: '選択中のみ表示',

	//FIT Copy
	copyLabel: 'コピー',
	copyActivity: 'アクティビティのコピー',
	copyInProgress: 'コピー中',
	fitCopyModalInfo: 'このアクティビティの回答を、同じエンゲージメントの1つ以上のアクティビティ又は同じワークスペース内の別のエンゲージメントからコピーします。',
	dropdownPlaceholderCA: 'コピー先のエンゲージメントを選択してください',
	noCopyActivityInfoLabel: 'このエンゲージメントには、コピー先と同じタイプの文書がありません。',
	copyActivityHoverLabel: 'このアクティビティは既に他のアクティビティと共有されているため、コピーできません',
	copyActivityWarningEarlierTimeLabel: '現在のアクティビティは、選択したエンゲージメントのアクティビティと比較して早い時期に変更されました。コピーの選択肢を確認する前に、この点を考慮してください。',

	//Unlink
	unlinkModalTitle: 'アクティビティのリンク解除',
	unlinkModalDescription: '選択したアクティビティのリンクを解除してもよろしいですか?',
	unlinkLabel: 'リンク解除',
	insufficientPermissionsLabel: '権限不足',
	unlinkFailMessage: 'リンク解除できませんでした。更新し再試行してください。問題が続く場合は、ヘルプデスクに連絡してください。',
	unlinkSuccessfulMessage: 'リンク解除しました',
	unlinkInProgressLabel: 'リンク解除中',
	unlinkError: 'リンク解除エラー',
	unlinkInProgressInfo: 'リンク解除中です。完了するまでに最大15分かかる場合があります。リンク解除が完了した後、このフォームを閉じて再度開く必要があります。',

	/** Manage scot modal labels */
	scotName: 'SCOT名',
	scotCategory: 'SCOTカテゴリー',
	estimate: '見積',
	noScotsAvailablePlaceHolder: '利用可能なSCOTはありません。開始するには新しいSCOTを追加してください。',
	addScotDisableTitle: 'SCOTの全ての詳細を入力して、新しいSCOTを追加します',
	deleteScotTrashLabel: 'SCOTの削除',
	undoDeleteScotTrashLabel: '削除を元に戻す',
	scotNameValidationMessage: 'SCOT名は必須です',
	scotCategoryValidationMessage: 'SCOTカテゴリーは必須です',
	scotWTTaskDescription: '<p>全ての経常及び非経常のSCOT及び重要な開示プロセスについて、ウォークスルー手続を実施することで、全ての期間を理解していることを確認します。さらに、PCAOB監査では、見積SCOTのウォークスルー手続を実施します。<br/>全てのSCOTに対して、統制依拠戦略を採用する場合、及び特別な検討を必要とするリスクに対処する統制については、関連する統制が適切にデザインおよび適用されていることを確認します。統制依拠戦略を採用するという決定が依然として適切であることを確認します。<br/><br/>文書化によりSCOTの運用を正確に記述しており、ITの使用から生じるリスクや関連する統制(該当する場合)を含む適切なWCGWを全て識別していると結論付けています。<br/><br/> 実証戦略を使用する場合の見積SCOTについては、実証手続に基づいて、見積SCOTの理解が適切かどうかを判断します。</p>',

	relate: '関連付け',
	unrelate: '関連付けの解除',
	related: '関連',
	relatedSCOTs: '関連するSCOTs',
	thereAreNoSCOTsIdentified: 'SCOTsが識別されていません。',
	selectSCOTsToBeRelated: '関連付けるSCOTsを選択',

	//OAR Tables
	OARBalanceSheet: '貸借対照表',
	OARIncomeStatement: '損益計算書',
	OARCurrentPeriod: '分析日',
	OARAmountChangeFrom: '変更元',
	OARPercentageChangeFrom: '％変動 対',
	OARNoDataAvailable: '利用可能なデータがありません。 {0} ページを確認しデータをインポートして先に進んでください。',
	OARAnnotationLabel: 'クリックして予期しない変動が発生したこと、又は予期した変動が発生していないことについての理由を確認する',
	OARAnnotationSelectedIcon: '予期しない変化(または予期した変化が生じなかったこと)について理由を文書化してください。',
	OARAnnotationModalTitle: '注釈',
	OARAnnotationModalPlaceholder: '通例でない項目、予期しない変動が発生したこと、又は予期した変動が発生してないことについて文書化する。',
	OARWithAnnotationLabel: '予期しない変動の文書化',
	OARAnnotation: '注釈',
	OARAccTypeWithAnnotationCountLabel: '勘定科目タイプへの {0} の注釈',
	OARSubAccTypeWithAnnotationCountLabel: '勘定科目サブタイプへの {0} の注釈',
	OARColumnA: 'A',
	OARColumnB: 'B',
	OARColumnC: 'C',
	OARComparative1Period: '比較日付1',
	OARComparative2Period: '比較日付2',
	OARExpand: '勘定科目クラスを展開する',
	OARCollapse: '勘定科目クラスを折りたたむ',
	OARHelixNavigationLink: '詳細をEY Helixで確認する',
	OARPrintNoDataAvailable: '利用可能なデータがありません',
	OARAdjustedBalance: '調整後残高',
	OARLegendLabel: '*が付いている値は調整が含まれていることを示します。詳細については、調整モジュールを参照してください。',
	OARAccountType: '勘定科目タイプ',
	astrixLabel: '*',

	//OAR Helix integration
	helixIntegrationModalDescription: '定義が未了のテキストがあります',
	OSJETabText: 'OSJE',
	activityAnalysisTabText: 'アクティビティ分析',
	preparerAnalysisTabText: '作成者分析',
	accountMetricsTabText: '勘定科目マトリックス',
	noAnalyticsData: '表示可能な分析がありません',

	printActivitiesTitle: 'アクティビティの印刷',
	printActivitiesModalInfo: '含めるアクティビティを選択してください。',
	printActivitiesModalConfirmButton: 'PDFのコンパイル',
	printActivitiesDropdownLabel: 'FITアクティビティ',
	printActivitiesAll: '全て',
	oarSetupText: ' {0} ページよりEY Helixプロジェクトのリンクや設定を実施してください',
	helixNotAvailable: 'EY Helixはこのエンゲージメントで利用できません。',
	dragDropUploadPlaceholder: '1つ以上の文書をドラッグアンドドロップするか、<span>{addDocument}</span>をクリックしてください',

	noTaskAssociatedToastMessage: 'Canvasフォームが一時ファイルに格納されているため、追加した文書も一時ファイルに保存されます。',

	// chart labels.
	assets: '資産',
	liabilities: '負債',
	equity: '純資産',
	revenues: '収益',
	expenses: '費用',
	noAccountsAvailable: '使用可能な勘定科目がありません',

	// ALRA
	ALRAFilterByAccount: '勘定科目でフィルター',
	ALRANoRecords: '結果が見つかりません',
	ALRAAssertions: 'アサーション',
	ALRAInherent: '固有リスク要因',
	ALRAHigher: '高リスク要因',
	ALRAAccountDisclosure: '勘定科目/開示',
	ALRAType: 'タイプ',
	ALRAName: '名前',
	ALRARisks: 'リスク',
	ALRAC: 'C',
	ALRAEO: 'E/O',
	ALRAMV: 'M/V',
	ALRARO: 'R&O',
	ALRAPD: 'P&D',
	ALRAR: 'R',
	ALRANoRisksAssociated: 'この勘定科目に関連するリスクはありません',
	ALRAAccountsDisclosureName: '勘定科目/開示名',
	ALRAHigherRisk: '高リスク',
	ALRAHigherInherentRisk: '［高(higher)］の固有リスク',
	ALRAHigherRiskCode: 'H',
	ALRALowerRisk: '低リスク',
	ALRALowerInherentRisk: '［低(lower)］の固有リスク',
	ALRALowerRiskCode: 'L',
	ALRALimitedRiskAccount: 'この勘定科目はリスクが限定的な勘定科目として識別されました',
	ALRAInsignificantRiskAccount: 'この勘定科目は軽微な勘定科目として識別されました',
	ALRADesignations: '指定',
	ALRABalances: '残高',
	ALRADesignation: '指定',
	ALRAAnalysisPeriod: '分析日',
	ALRAxTE: 'xTE',
	ALRAPercentageChangeFrom: '%の増減率',
	ALRAPriorPeriodDesignation: '前期の指定',
	ALRAPriorPeriodEstimate: '前期の見積',
	ALRAComparativePeriod1: '比較日付1',
	ALRAComparativePeriod2: '比較日付2',
	ALRASelectUpToThreeOptions: '最大3つのオプションを選択',
	ALRASelectUpToTwoOptions: '最大2つの選択肢を選択してください',
	ALRAValidations: '検証',
	ALRANoSignOffs: 'サインオフがない',
	ALRAIncompleteInherentRisk: '未完了の固有リスク',
	ALRARelatedDocuments: '関連する文書',
	ALRAGreaterExtent: '広範囲',
	ALRALesserExtent: '狭い範囲',
	ALRARiskRelatedToAssertion: 'リスクに関連する',
	ALRAContributesToHigherInherentRisk: 'リスクに関連し、「高」の固有リスクに寄与する',

	// Assess inherent risk
	HigherRiskAssertionWithoutRisksThatContributesToTheHigherInherentRisk: '1つ以上のリスクが固有リスクを高める要因となっている場合、そのアサーションの固有リスクは［高(higher)］として識別されます。リスクを関連付けることで、どのリスクがアサーションの［高(higher)］の固有リスクに寄与しているかを特定します。',

	//MEST - Multi-entity account Execution Type selection listing
	account: '勘定科目',
	taskByEntity: 'エンティティ別タスク',
	bodyInformation: '変更を保存するには、コンテンツのインポートをクリックする費用があります。',

	/*user search component*/
	seachInputRequired: '検索入力が必要です。',
	nameOrEmail: '名前又はEメールアドレス',
	emailForExternal: 'Eメールアドレス',
	noRecord: '結果が見つかりません',
	userSearchPlaceholder: '名前またはEメールアドレスを入力し、Enterキーを押して結果を確認してください',
	userSearchPlaceholderForExternal: 'Eメールアドレスを入力し、Enterキーを押して結果を確認してください',
	clearAllValues: '全ての入力値をクリアしてください。',
	inValidEmail: '有効なEメールアドレスを入力してください',

	//reactive frame
	maxTabsLocked: 'タブの上限に達しました。タブの1つのピン留めを解除し閉じてから新しいタブを開いてください。',
	openInNewTab: '新しいタブで開く',
	unPinTab: 'タブのピン留めを解除',
	pinTab: 'タブのピン留め',
	closeDrawer: 'ドロワーを閉じる',
	minimize: '最小化',

	accountHeader: '勘定科目',
	sCOTSummaryAccountNoDataLabel: '各SCOTは、少なくとも１つの重要な勘定科目又は重要な開示に関連付けられる必要があります。このSCOTに関連付ける既存の重要な勘定科目又は開示を選択してください。',
	sCOTSummaryNoDataLabel: 'SCOTsが作成されていません',
	scotSearchNoResultsFound: 'JA JP No results found',
	scotSummary225TabsName: {
		[0]: {
			label: '勘定科目別に表示 '
		},
		[1]: {
			label: 'SCOT別に表示 '
		}
	},

	// Display Account Balances
	currentPeriodAccountBalance: '当期勘定科目残高:',
	priorPeriodAccountBalance: '前期勘定科目残高:',

	ALRANoResults: '結果が見つかりません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	associatedRomsCount: '関連するリスクの合計: {0}',
	alraMessage: '勘定科目の指定の回答が「勘定科目及び開示の編集」で指定した項目と一致していません',
	estimateCategoryResponseNotAlignedToDesignation: '見積りカテゴリの回答が「見積りの編集」の指定と一致していません',


	// Analytics Overview
	analyticsOverviewTitle: 'アナリティクスの概要',
	noSignificantAccountRecords: '軽微な勘定科目は作成されていません。',
	noSignificantAccountMapped: '選択したエンティティにマッピングされている重要な勘定科目がありません。',
	noLimitedAccountMapped: '選択したエンティティにマッピングされているリスクが限定的な勘定科目がありません。',
	openAnalyticDocumentation: 'アナリティクス文書を開く',
	openLimitedRiskAccountDocumentation: 'リスクが限定的な勘定科目の文書化を開く',
	associatedSCOTs: '関連するSCOTSs: ',
	analysisPeriodLabel: '分析日{0}',
	analysisPeriodChangeLabel: '増減率（%）、対{0}',
	xTELabel: 'xTE',
	risksLabel: 'リスク',
	comparativePeriod1: '比較日付1{0}',
	analysisPeriodTitle: '分析日',
	analysisPeriodChangeTitle: '増減率（%）、対',
	comparativePeriodTitle: '比較日付1',
	noAccountAvailable: '利用可能な勘定科目がありません',

	// Estimates
	titleEstimateCategory: '見積カテゴリー',
	titleRisks: 'リスク',

	voiceNoteNotAvailable: 'ボイスノートと画面録画は、ドロワービューでは利用できません。これらの機能を使用するには、全画面表示に切り替えてください。',

	financialStatementType: {
		[1]: {
			label: '資産 '
		},
		[2]: {
			label: '流動資産 '
		},
		[3]: {
			label: '非流動資産 '
		},
		[4]: {
			label: '負債 '
		},
		[5]: {
			label: '流動負債 '
		},
		[6]: {
			label: '非流動負債 '
		},
		[7]: {
			label: '純資産 '
		},
		[8]: {
			label: '収益 '
		},
		[9]: {
			label: '費用 '
		},
		[10]: {
			label: '営業外収益/(費用) '
		},
		[11]: {
			label: 'その他の包括利益(OCI) '
		},
		[12]: {
			label: 'その他 '
		},
		[13]: {
			label: '勘定科目タイプ '
		},
		[14]: {
			label: '勘定科目サブタイプ '
		},
		[15]: {
			label: '勘定科目クラス '
		},
		[16]: {
			label: '勘定科目サブクラス '
		},
		[17]: {
			label: '純(利益)/損失 '
		}
	},
	accountTypes: {
		[1]: {
			label: '重要な勘定科目 '
		},
		[2]: {
			label: 'リスクが限定的な勘定科目 '
		},
		[3]: {
			label: '軽微な勘定科目 '
		},
		[4]: {
			label: '他の勘定科目 '
		},
		[5]: {
			label: '重要な開示 '
		}
	},
	noClientDataAvailable: '利用可能なデータがありません',

	analysisPeriod: '分析日',
	comparativePeriod: '比較日付',
	perchangeLabel: '%の増減率',

	entityCreateAccountLabel: '勘定科目と開示の作成',
	insignificantAccount: '軽微な勘定科目',
	noAccountRecords: '勘定科目が識別されていません',
	noAccountsForEntity: '選択したエンティティにマッピングされている勘定科目又は開示がありません。',
	noLimitedRiskAccountRecords: 'リスクが限定的な勘定科目がありません。',
	createAccount: '勘定科目の作成',
	createDocument: '文書の作成',
	noAccountResults: '識別された勘定科目がありません。',
	createGroupInvolvementDocument: '関与フォームの作成',
	chooseVersionsToCompare: '比較するバージョンを選択してください',
	noTrackChangesOption: '利用可能な変更履歴のバージョンはありません',
	trackChangesDefaultMessage: '「比較するバージョンを選択」ドロップダウンからバージョンを選択して続行します。',
	whichRiskContributeToHigherRisk: 'どのリスクが高リスクアサーションに影響していますか？',

	//multi-entity Entity List
	createMultiEntity: '新しいエンティティ',
	editMultiEntity: 'エンティティの編集',
	noEntitiesAvailableCreateNewLink: 'ここをクリック',
	noEntitiesAvailable: 'エンティティが作成されていません。 {noEntitiesAvailableCreateNewLink} にて作成を開始します',
	noEntitiesFound: '結果が見つかりません。',
	createMultiEntityProfile: 'エンティティプロファイルの作成',

	createEntity: 'エンティティの作成',
	includeEntities: 'マルチエンティティの一覧には、少なくとも1つのエンティティが含まれている必要があります。{エンティティの作成}を開始します。',
	//multi-entity table
	multiEntityCode: 'エンティティ標準インデックス',
	multiEntityName: 'エンティティ名',
	multiEntityGroup: 'エンティティグループ',
	multiEntityActions: 'アクション',
	relateMultiEntityUngrouped: 'グルーピングされていない',
	selectAll: '全て選択',
	entitiesSelected: '選択されたエンティティ',
	entitySelected: '選択されたエンティティ',
	meNoEntitiesAvailable: '利用可能なエンティティがありません',
	meSwitchEntities: 'エンティティを切り替える',
	meSelectEntity: 'エンティティの選択',
	allEntities: 'すべてのエンテイティ',
	noEntitiesIdentified: 'エンティティが識別されていません',
	contentDeliveryInProcessMessage: 'コンテンツ配信中です。コンテンツ配信には10分ほど要する場合があります。',
	importContent: 'コンテンツのインポート',
	profileSubmit: 'プロファイルの提出',
	importPSPs: 'PSPのインポート',
	contentUpdateInsufficienRolesLabel: 'コンテンツを更新するための権限がありません。エンゲージメント管理者と連携して、十分な権限を取得してください。',
	// MEST Switcher
	meEntitySwitcher: 'エンティティスイッチャー',
	//Error Boundary
	errorBoundaryMessage: 'エラーが発生しました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	sectionName: 'セクション名',
	maxLength: '文字数は {number} 字以内にしてください。',
	required: '必須',
	yearAgo: '年前',
	yearsAgo: '年前',
	monthAgo: 'ヶ月前',
	monthsAgo: 'ヶ月前',
	weekAgo: '週間前',
	weeksAgo: '週間前',
	daysAgo: '日前',
	dayAgo: '日前',
	today: '今日',
	todayLowercase: '今日',
	yesterday: '昨日',
	approaching: '期日が近い',

	associatedToInherentRiskFactor: '固有リスク要因に関連付ける',

	createMissingDocument: '不足している文書の作成',
	createMissingDocumentBody: '「確認」をクリックして、現在不足している関連アイテムの文書を作成します。',
	documentCreationSuccessMsg: '文書の作成が進行中です。ページを更新してアップデートしてください。',

	noRisksRelatedToAssertion: 'この勘定科目のアサーションに関連するリスクがありません。',

	noAssertionsRelatedToAccount: 'この勘定科目に関連付けられたアサーションがありません',

	sharing: '共有',

	risksUnrelatedToAccountAssertion: '勘定アサーションに関連付けられていないリスク',
	cantCompleteTask: '関連するタスクに関連付けされた文書にサインオフがありません。タスクを開いて不足している文書を完了させ、もう一度完了とマークしてください。',
	cantCompleteTasksTitle: '完了とマークすることができません',
	ok: 'OK',
	documentsEngagementShareLabel: '共有するエンゲージメント内の文書を選択してください',
	documentsEngagementCopyLabel: 'コピーするエンゲージメント内の文書を選択してください',
	lastModifiedon: '最終更新日',
	newAccountAndDisclosure: '新規の勘定科目及び開示',
	newAccountORDisclosure: '新しい勘定科目又は開示',

	externalDocuments: '外部文書',
	noExternalDocumentsAvailable: '外部文書がありません',
	addExternalDocuments: '外部文書を追加する',
	relateExternalDocuments: '外部文書を関連付ける',

	helixNotMappedToAccount: 'EY Helixのデータがこの勘定科目にマッピングされていません。続行するには、マッピングを更新し、データを再インポートしてください。',
	trackChangesNotAvailableForSpecialBodyDisplayMessage: '変更履歴機能は、以下の回答では利用できません。',
	noDocumentRelatedObjectsApplicable: 'オブジェクトをこのガイド付きワークフローフォームに関連付ける必要はありません',
	helixViewerLoader: 'Helix Viewerをロード中...',
	trackChangesViewDefaultMessage: '一部の回答には変更履歴機能がありません。これは、以下のメッセージがアクティビティの詳細に表示されます：「変更履歴機能は、以下の回答では利用できません。」したがって、以下の変更通知がないことが、変更が行われたかどうかを示しているわけではありません。',

	//Relate task modal
	relateTasksTitle: '関連タスク',
	taskLocationLabel: 'タスクロケーション',
	relateTaskInstructionalText: '文書に関連するタスクを追加又は削除します。文書がエビデンスである場合、最後に関連付けられたタスクから文書を削除すると、文書は一時ファイルに移動します。',
	noResultFound: '結果が見つかりません。',
	relatedTaskCounter: '{0} タスク',
	relatedTasksCounter: '{0} タスク',
	onlyShowRelatedTasks: '関連するタスクのみ表示',
	relateTaskName: 'タスク名',
	relateTaskType: 'タイプ',

	/*Relate Entities*/
	relateEntitiesTitle: 'エンティティの関連付け',
	relateEntitiesSearchPlaceholder: '名前で検索に入力',
	relateEntitiesName: 'エンティティ名',
	relateEntitiesIndex: 'エンティティ標準インデックス',
	relatedEntitiesCounter: '{0} エンティティ',
	relatedEntityCounter: '{0} エンティティ',
	onlyShowRelatedEntities: '関連するエンティティのみ表示',
	entity: 'エンティティ',

	step01: 'ステップ01',
	step02: 'ステップ02',
	shareActivityStep1Description: 'エンゲージメントおよび文書の選択',
	shareActivityStep2Description: 'どの文書回答を他の文書回答と置き換えるかを選択してください',
	documentsShareLabel: '以下で選択された文書からの回答を残りの文書に共有します。',
	selectedActivity: '選択されたアクティビティ',
	sharedHoverLabel: 'このアクティビティはすでに他のアクティビティと共有されています。このアクティビティを共有すると、このアクティビティからの回答がすべての共有アクティビティに同期されます。',
	noAssertionsRelatedLabel: '関連付けられたアサーションはありません',

	// Bulk mark complete:
	bulkMarkCompleteInstructionalText: 'このアクティビティを完了とマークするには、全ての文書が少なくとも1人の作成者と1人のレビューアーにサインオフされている必要があります。',
	bulkMarkCompleteEngagementColumn: 'エンゲージメント',
	bulkMarkCompleteDocumentsMissingSignOffs: 'サインオフがありません。 {bulkMarkCompleteMissingSignOffsClickableText}をクリックしてサインオフしてください。',
	bulkMarkCompleteMissingSignOffsClickableText: 'こちら',
	bulkMarkCompleteNoAccessToEngagement: 'このタスクが含まれているエンゲージメントへのアクセス権がありません',
	bulkMarkCompleteInProgressMessage: 'プロセスが進行中です。最大10分かかる場合があります。更新してアップデートしてください。',
	bulkMarkCompleteRelatedDocumentsModalTitle: '文書のサインオフ',
	bulkMarkCompleteFilterUnreadyTasks: '文書のサインオフがないタスクのみを表示',
	bulkMarkCompleteNotAllowedModalTitle: '完了とマークすることができません。',
	bulkMarkCompleteNotAllowedModalDescription: '完了とマークするには、少なくとも1つのタスクを選択する必要があります。',
	bulkMarkCompleteRelatedDocumentsModalDescription: '選択されたタスクを完了とマークするには、全ての文書が少なくとも1人の作成者と1人の査閲者にサインオフされている必要があります。',
	bulkMarkCompleteRelatedDocumentsModalRefreshSignoffs: 'サインオフ及びノートの更新',
	selectedTaskCounter: '選択したタスク({0})',
	selectedTasksCounter: '選択したタスク({0})',

	// Mark complete (old):
	markCompleteNotAllowedModalDescription: '関連するタスクに関連付けされた文書にサインオフがありません。タスクを開いて不足している文書を完了させ、もう一度完了とマークしてください。',
	markCompleteInstructionalText: 'このアクティビティを完了とマークするには、全ての文書が少なくとも1人の作成者と1人の査閲者にサインオフされている必要があります。',

	// Adobe Analytics
	aaCookieConsentTitle: 'ようこそ',
	aaCookieContentPrompt: 'クッキーを許可しますか？',
	aaCookieConsentExplanation: '<p>このウェブサイト運営上厳密に必要なクッキーに加え、エクスペリエンス及びサービス向上のため以下のタイプのクッキーを使用します:   (設定の記憶等）エクスペリエンス向上目的の<strong>機能性クッキー</strong>、ウェブサイトパフォーマンス測定及びエクスペリエンス向上目的の<strong>パフォーマンスクッキー</strong>、広告キャンペーン実施先を対象に第三者により設定され、かつ、関連する広告を提供する目的の<strong>広告/ターゲティングクッキー </strong> </p><p>詳細については<a target="_blank" href="https://www.ey.com/en_us/cookie-policy">クッキーポリシー</a>を参照してください。</p>',
	aaCookieConsentExplanationWithDoNotTrack: '<p>このウェブサイト運営上厳密に必要なクッキーに加え、エクスペリエンス及びサービス向上のため以下のタイプのクッキーを使用します: (設定の記憶等）エクスペリエンス向上目的の<strong>機能性クッキー</strong>、ウェブサイトパフォーマンス測定及びエクスペリエンス向上目的の<strong>パフォーマンスクッキー</strong>、広告キャンペーン実施先を対象に第三者により設定され、かつ、関連する広告を提供する目的の<strong>広告/ターゲティングクッキー </strong> </p><p>ブラウザで「追跡しない」設定が有効になっていることを検出しました。その結果、広告/ターゲティングクッキーは自動的に無効になります。</p><p>詳細については<a target="_blank" href="https://www.ey.com/en_us/cookie-policy">クッキーポリシー</a>を参照してください。</p>',
	aaCookieConsentDeclineOptionalAction: '任意のクッキーを拒否します',
	aaCookieConsentAcceptAllAction: '全てのクッキーを承諾します',
	aaCookieConsentCustomizeAction: 'クッキーのカスタマイズ',
	aaCookieConsentCustomizeURL: 'https://www.ey.com/en_us/cookie-settings',

	// Cookie Settings
	cookieSettings: {
		title: 'クッキーの設定',
		explanation: 'ey.com及びMy EYプラットフォームでのクッキーの使用に同意してください。以下にリストされているクッキーの種類から1つ以上を選択し、選択内容を保存してください。クッキーの種類とその目的の詳細については、以下のリストを参照してください。',
		emptyCookieListNotice: 'このカテゴリのクッキーはこのアプリでは使用されていません',
		nameTableHeader: 'クッキー名',
		providerTableHeader: 'クッキーの提供者',
		purposeTableHeader: 'クッキーの目的',
		typeTableHeader: 'クッキーの種類',
		durationTableHeader: 'クッキーの有効期限',
		formSubmit: '選択内容を保存',
		requiredCookieListTitle: '必須クッキー',
		functionalCookieListTitle: '機能性クッキー',
		functionalCookieAcceptance: '以下の機能性クッキーを受け入れます',
		functionalCookieExplanation: '機能性クッキーにより、例えば設定の記憶等が行われ、エクスペリエンスを向上します。',
		performanceCookieListTitle: 'パフォーマンスクッキー',
		performanceCookieAcceptance: '以下のパフォーマンスクッキーを受け入れます',
		performanceCookieExplanation: 'パフォーマンスクッキーはWebサイトのパフォーマンスを測定し、エクスペリエンスの向上に繋がります。パフォーマンスクッキーを使用するにあたり、個人データは保存されず、これらのクッキーを通じて収集された情報を集約され匿名化された形式でのみ使用します。',
		advertisingCookieListTitle: 'ターゲティングクッキー',
		advertisingCookieAcceptance: '以下の広告/ターゲティングクッキーを受け入れます',
		advertisingCookieExplanation: '使用する広告/ターゲティングクッキーはよりパーソナライズされたサービスを提供できるように、ユーザーのアクティビティとセッションを追跡するために使用します。また、広告クッキーの場合、広告キャンペーンを実施する第三者により設定され、関連する広告を提供します。',
		doNotTrackNotice: 'ブラウザで「追跡しない」設定が有効になっていることを検出しました。その結果、広告/ターゲティングクッキーは自動的に無効になります。',
	},
	accountFormsMissing: '{0} 勘定科目に勘定科目フォームがありません',
	createAccountForms: '勘定科目フォームの作成',
	createAccountFormsDescription: '「確認」をクリックして、現在不足している関連アイテムの文書を作成します。',
	createMissingDocuments: '現在文書が不足している関連する勘定科目',
	accountDocumentsCreated: 'コンテンツ配信中です。コンテンツ配信には10分ほど要する場合あります。 ',

	evidenceMissingPICSignoffs: 'PICのサインオフがないエビデンス',
	evidenceMissingEQRSignoffs: 'EQRのサインオフがないエビデンス',
	evidenceMissingPICEQRSignoffs: 'PIC及び/又はEQRのサインオフがないエビデンス',
	evidenceMissingPICSignoffRequirements: 'PICのサインオフ要件がないエビデンス',
	evidenceMissingEQRSignoffRequirements: 'EQRのサインオフ要件がないエビデンス',
	evidenceMissingPICEQRSignoffRequirements: 'PIC及び/又はEQRのサインオフ要件がないエビデンス',
	evidenceMissingSignoffs: 'サインオフのないエビデンス',

	// Bulk task relate
	bulkTaskRelateFailureMessage: '選択した文書の一部は、選択したタスクに関連付けることができませんでした。',
	/*endoflabels*/
	evidenceMissingPreparerOrReviwerSignoffs: '文書のアップロード - 作成者又はレビューアーのサインオフがありません',

	manageITProcess: 'ITプロセスの管理',
	manageITRisk: 'テクノロジーリスクの管理',
	manageITControl: 'IT統制の管理',
	manageITSP: 'ITSPの管理',
	manageITApp: 'ITアプリケーションの管理',
	manageSCOT: 'SCOTsの管理',
	addAresCustomDescription: 'このガイド付きワークフローフォームに追加するコンテンツタイプを選択し、詳細を入力して、保存をクリックしてください。',

	documentImportSuccess: '{1} が作成されました。コンテンツ配信には10分ほど要する場合あります。',
	documentImportFailure: '文書の作成に失敗しました。ページを更新ししばらくしてから再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	formNotAvailable: '一致するCanvasフォームが見つかりませんでした。',
	selectTask: 'ガイダンスに関連するタスクを選択',
	canvas: 'Canvas',
	selectEngagement: 'エンゲージメントの選択',

	//Modal Manage sub-scope
	manageSubScopeTitle: 'サブスコープの管理',
	manageSubScopeDescription: '新しいサブスコープを作成するか以下の既存のサブスコープを編集又は消去してください。',
	addSubScope: 'サブスコープの追加',
	subScopeName: 'サブスコープ名',
	knowledgeScope: 'ナレッジスコープ',
	subScopeAlreadyExist: 'サブスコープ名が既に存在しています',
	subScopes: 'サブスコープ',
	notAvailableSubScopes: '利用可能なサブスコープがありません',
	SubScopeNameValidation: 'サブスコープ名の長さが255文字を超えています。',

	//CRA Summary
	manageAccount: '勘定科目の管理',
	newAccount: '新しい勘定科目',

	noRelatedObjectITProcessFlow: '関連オブジェクトはありません。オブジェクトを関連付けて開始します。',

	//Add New Flow Chart Steps
	flowChartNewSteps: {
		newStepTitle: '新しいステップ',
		placeholderText_1: '以下にステップの詳細を入力し、',
		placeholderText_2: '「保存して閉じる」を選択して',
		placeholderText_3: '終了します。別のステップを作成するには、',
		placeholderText_4: '「保存して別のものを作成」を選択します。',
		columnLabel: '列（必須）',
		counterOf: '／',
		counterChar: '文字',
		stepNameLabel: 'ステップ名（必須）',
		errorMsgStepNameRequired: 'ステップ名は必須です',
		stepDescLabel: 'ステップの説明（必須）',
		stepDescPlaceholder: 'ステップの説明を入力してください',
		errorMsgStepDescRequired: 'ステップの説明は必須です',
		required: '必須',
		errorMsgStepDescExceedMaxLength: 'ステップの説明が文字数の上限を超えています',
		buttonCancel: 'キャンセル',
		buttonSaveAndClose: '保存して閉じる',
		buttonSaveAndCreateAnother: '保存して別のものを作成',
		errorMsgColumnRequired: '列が必要です',
		headerNameForWCGW: 'WCGW名',
		headerNameForControl: '統制名',
		headerNameForITApp: 'ITアプリケーション名',
		headerNameForServiceOrganisation: 'サービス受託会社名',
		relateLabelForWCGW: 'WCGWsの関連付け',
		relateLabelForControl: '統制の関連付け',
		relateLabelForITApp: 'ITアプリケーションの関連付け',
		relateLabelForServiceOrganisation: 'サービス受託会社の関連付け',
		designEffectiveness: 'デザインの有効性',
		testing: 'テスト',
		lowerRisk: '低リスク',
		wcgwNoRowsMessage: '関連付けられたWCGWｓはありません。{0}をクリックして開始してください。',
		controlNoRowsMessage: '関連付けられた統制はありません。{0}をクリックして開始してください。',
		itAppNoRowsMessage: '関連付けられたITアプリケーションはありません。{0}をクリックして開始してください。',
		serviceOrganisationNoRowsMessage: '関連付けられたサービス受託会社はありません。{0}をクリックして開始してください。',
		wgcwTabLabel: 'WCGWs',
		controlsTabLabel: '統制',
		itAppsTabLabel: 'ITアプリケーション',
		serviceOrganisationTabLabel: 'サービス受託会社',
		connectionSuccessMessage: '接続が成功しました。',
		connectionFailedMessage: '接続が確立できませんでした。再試行してください。',
		selfConnectFailMessage: 'ソースとターゲットは同じにできません。',
		connectionDuplicateMessage: '接続が存在します。',
		connectionDeleteSuccessMessage: '接続の削除が成功しました。',
		connectionDeleteFailMessage: '接続が削除できませんでした。再試行してください。',
		editStepFailMessage: 'ステップの編集ができませんでした。再試行してください。',
		flowchartStepGetByIdFailMessage: '無効なステップです。ページを更新し再試行してください。',
		flowchartStepGetByIdFailureMessage: 'このフローチャートステップは利用できなくなりました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
		newStepFailureMessage: '新しいステップが作成できませんでした。再試行してください。',
		deleteConnector: 'コネクタを削除',
		edgeConnectorOptions: 'コネクタオプション',
		edgeStartPoint: 'スタートポイント',
		edgeEndPoint: 'End Point',
		relateDocumentToFlowchartStepError: '現時点では操作を完了できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
		relateDocumentsOrObjects: '文書又はオブジェクトの関連付け',
		thisstep: 'このステップに '
	},

	flowChartWCGW: {
		wcgwsCounter: '{0} WCGW',
		wcgwCounter: '{0} WCGW',
		headerName: 'WCGWsの関連付け',
		showOnlyRelatedText: '関連付けのあるもののみを表示',
		noResultsFound: '結果が見つかりません。 '
	},

	flowchartITAPPSO: {
		showOnlyRelatedText: '関連付けのあるもののみを表示',
		noResultsFound: '結果が見つかりません。 '
	},

	flowChartITApplication: {
		itApplicationsCounter: '{0} ITアプリケーション',
		itApplicationCounter: '{0} ITアプリケーション',
		headerName: 'ITアプリケーションの関連付け',
		columnName: 'ITアプリケーション名',
		noDataFound: 'ITアプリケーションが見つかりません '
	},

	flowChartITSO: {
		itSOsCounter: '{0} サービス受託会社',
		itSOCounter: '{0} サービス受託会社',
		headerName: 'サービス受託会社の関連付け',
		columnName: 'サービス受託会社名',
		noDataFound: 'サービス受託会社が見つかりません '
	},

	flowChartControl: {
		controlsCounter: '統制の関連付け {0}',
		headerName: '統制の関連付け',
		showOnlyRelatedText: '関連付けのあるもののみを表示',
		noResultsFound: '結果が見つかりません。',
		noWCGWs: '作成済の統制がありません '
	},

	relateSCOT: {
		header: 'SCOTsの関連付け',
		estimate: '見積り',
		scotsCounter: '{0} SCOTs',
		scotCounter: '{0} SCOT',
		headerName: 'SCOT名',
		showOnlyRelated: '関連付けのあるもののみを表示',
		noResultsFound: '結果が見つかりません。',
		noScotCreated: 'このエンゲージメントに作成されたSCOTがありません '
	},

	relatedStepObjects: {
		relatedWCGWs: '関連するWCGWs',
		relatedControls: '関連する統制',
		relatedDocuments: '関連するエビデンス',
		relatedITApplications: '関連するITアプリケーション',
		relatedSOs: '関連するサービス受託会社 '
	},

	flowchartEditSteps: {
		nextStep: '次のステップ',
		previousStep: '前のステップ',
		editStepTitle: 'ステップの編集',
		editPlaceholderText_1: '以下のステップの詳細及び関連オブジェクトを編集します。こちらをクリック（',
		editPlaceholderText_2: "'保存して閉じる' ",
		editPlaceholderText_3: '）すると、保存してフローチャートに戻ります。以下のオプションを使用して他のステップに移動すると、更新内容が保存されます。 ',
		draftEditStepFailMessage: 'フローチャートステップを作成できません。ページを更新して再試行してください。問題が解決しない場合は、ヘルプデスクにご連絡ください。',
	},

	flowChartStepmoreMenu: {
		edit: '編集',
		delete: '削除 '
	},

	relateEstimate: {
		scot: 'SCOTs',
		strategy: 'SCOT戦略',
		type: 'タイプ',
		noSCOT: '各見積りは、少なくとも1つのSCOTに関連付ける必要があります。以下をクリックし',
		noSCOTmsg: '開始してください',
		estimate: '見積り',
		routine: '経常',
		nonRoutine: '非経常',
		notSelected: '選択されていません',
		relateSCOTs: 'SCOTsの関連付け',
		remove: '削除',
		noEstimate: '利用可能な見積りはありません',
	},

	flowChartStepIcons: {
		wcgws: 'WCGWs',
		controls: '統制',
		iTApps: 'ITアプリケーション',
		serviceOrganisations: 'サービス受託会社 '
	},

	flowChartStepIcon: {
		wcgw: 'WCGW',
		control: '統制',
		iTApp: 'ITアプリケーション',
		serviceOrganisation: 'サービス受託会社',
		evidence: 'エビデンス '
	},

	flowChartErrorMessage: {
		stepOutsideOfTheColumns: 'JA JP Steps cannot be placed outside of the flowchart area',
		stepBetweenTheColumns: 'JA JP Steps cannot be placed between the columns',
		stepOnTopOrTooCloseToAnotherStep: 'JA JP Steps cannot be placed on top of the other steps'
	},

	//Delete Flow Chart Steps
	flowChartStepsDelete: {
		deletestep: 'ステップの削除',
		deleteStepModalMessage: 'このステップを削除してよろしいですか？削除されるステップのすべてのWCGWs、統制、ITアプリケーション、サービス受託会社、エビデンスの関連付けが解除されます。',
		cannotBeUndone: '新規または変更された顧客との契約',
		deleteStepFailMessage: 'ステップの削除ができませんでした。再試行してください。',
		deleteDraftStepErrorMessage: 'ドラフトで作成されたステップは削除されませんでした。このステップを削除するには、ステップを選択し、再度削除を実行してください。',
	},
	notEntered: '入力されていません',
	estimateCategory: '見積カテゴリー',
	noResultsFoundWithPeriod: '結果が見つかりません',
	noEstimateAvailable: '利用可能な見積りはありません',
	noRelatedObject: '関連するオブジェクトがありません。',
	relateAnObject: 'オブジェクトの関連付け',
	copyrightMessage: 'Copyright © <year> all rights reserved',
	leadsheet: 'リードシート',
	controlName: '統制名',
	noControlAvailable: '利用可能な統制がありません',
	independenceError: '全ての不完全な回答は、独立性の回答の提出前に完了する必要があります。',
	riskTypeNotAssociated: '新たに追加されたリスクは、許可されたリスクタイプと一致しないため、以下に表示されません。許可されたタイプの別のリスクを追加するか、以下のリストから選択します。',
	accountsAndRelatedEstimates: '勘定科目及び関連する見積り',
	noEstimatesAssociated: '見積りが関連付けられていません',
	noAssertionsAvailable: '利用可能なアサーションがありません',
	noAccountsOrDisclosuresAvailable: '利用可能な勘定科目又は開示がありません',

	relateEstimateToRisk: {
		riskType: 'リスクタイプ',
		risk: 'リスク',
		hasestimate: "Has estimate?",
		accounts: '勘定科目',
		isItRelevant: '関連性はありますか？',
		assertions: 'アサーション',
		invalidRiskParentRiskErrMsg: 'レコードが見つかりません。継続するにはページを更新してください。',
		noEstimate: '利用可能な見積りはありません',
		invalidRelateRiskOrEstimateRelationErrMsg: 'オブジェクトは既に関連付けられています。続行するにはページを更新してください。',
		invalidUnRelateRiskOrEstimateRelationErrMsg: 'オブジェクトは既に関連付けられていません。続行するにはページを更新してください。 '
	},

	savingChanges: '変更の保存',
	showEstimateAccountsWithoutEstimates: '見積りのない見積勘定科目を表示する',
	showEstimateSCOTsWithoutEstimates: '見積りのない見積SCOTsを表示する',
	manageSCOTs: 'SCOTsの管理',
	sCOTsAndRelatedEstimates: 'SCOTs及び関連付けられた見積り',
	relateEstimateToRiskNoDataMessage: '利用可能なレコードはありません。該当する場合は、少なくとも１つの勘定科目とアサーションをリスクに関連付けてください。',
	maps: 'マッピング',
	mapsUpbutSomeLoadingErrorMessage: '技術的なエラーが発生し、マッピング機能が動作しません。ページを更新し再試行してください。',
	mapsDownErrorMessage: 'マッピング機能は一時的に利用できません。ページを更新し再試行してください。このメッセージが続く場合は、ヘルプデスクに連絡してください。',
	financialStatements: '財務諸表',
	serviceGatewayAutomation: 'サービスゲートウェイ＆オートメーション',
	priorPeriodCategory: '前期カテゴリー',
	relatedAccountWithColon: '関連する勘定科目: ',
	noRelatedAccount: '関連する勘定科目がありません。',
	noRetionaleAvailable: '利用可能な根拠はありません',
	leftNavIconApprovals: '承認者',
	editDuplicateSectionHeader: 'セクションの詳細を編集し保存をクリックしてください。',

	relatedEvidences: 'エビデンスの関連付け',
	relatedEvidencesInstruction: 'このエンゲージメントからエビデンスを関連付ける。',
	relatedTemporaryFilesInstruction: 'このエンゲージメントから一時文書を関連付ける。',
	noDataLabel: 'データがありません',
	editDuplicateSection: 'セクションの編集',
	showOnlyRelated: '関連付けのあるもののみを表示',
	aiChatbot: 'JA JP EYQ Assurance Knowledge',
	StEntityNoRecords: '選択したエンティティにマッピングされている勘定科目又は開示がありません。',
	versionLabel: 'バージョン',
	relatedEstimates: '関連する見積り',
	viewEvidenceRelatedToBody: 'JA JP View evidence related to the body',
	selectHeaderFromRail: 'JA JP Select a header from left navigation pane to proceed',
	manageITProcesses: 'ITプロセスの管理',
	rationaleForLR: 'リスクが限定的な勘定科目の根拠',
	rationaleForInsignificant: '軽微な勘定科目の根拠',
	rationalIsMissing: '根拠が記載されていません。',
	craSummaryText1: 'それぞれの重要な勘定科目又は開示に少なくとも1つのアサーションが必要です。以下をクリックしてください。',
	scotDetails223: {
		relatedAccounts: '関連する勘定科目',
		scotType: 'タイプ',
		manageScot: 'SCOTsの管理',
		editScot: 'SCOTsの編集',
		scotNotAvailableMessage: 'この文書ではSCOTsを利用できません',
		relatedScotNotAvailableMessage: '関連しないSCOT。属性ページからSCOTを関連付けて開始する',
		risksDocumented: 'このウォークスルーで文書化されたリスク',
		risksAvailableHeader: 'はい',
		risksNotAvailableHeader: 'いいえ',
		viewRelatedRisks: '関連するリスクの表示',
		noRelatedAccountsMessage: '関連する勘定科目がありません。 '
	},

	scotDetails226: {
		noscotsidentified: 'JA JP No SCOTs have been identified'
	},

	scotDetails224: {
		riskRelatedWalkthrough: 'このウォークスルーに関連付けされたリスク',
		relatedToWTDocuments: 'JA JP Related to other WT documents',
		riskNotRelatedWalkthrough: 'このウォークスルーに関連付けされていないリスク',
		substantiveNotSufficient: '実証が不十分',
		journalEntry: '仕訳',
		noDirectRiskSourcesAreAvailable: '関連するリスクなし',
		scotNotAvailableMessage: 'この文書ではSCOTsを利用できません',
		relatedScotNotAvailableMessage: '関連しないSCOT。属性ページからSCOTを関連付けて開始する',
		relatedDocuments: 'JA JP Related documents',
		risk: "JA JP Risk:",
		riskSpecialCircumstances: 'JA JP Risk special circumstances',
		relateInstructionText: "JA JP This risk has been identified in another SCOT.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		unrelateInstructionText: "JA JP This risk has been identified in the critical path of another walkthrough.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		concurrencyErrorMessage: "JA JP This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.",
	},
	ipe: 'IPE',
	scotSummary198: {
		noAccountsDisclosureCreated: '重要な勘定科目又は開示が作成されていません',
		noScotEstimateIdentified: 'SCOTs又は見積りが識別されていません。',
		noScotIdentified: 'SCOTsは識別されていません',
		scots: 'SCOTs',
		estimates: '見積り',
		errorMessage: 'この操作を完了できませんでした。ページを更新して再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
		noResultsFound: '結果が見つかりません',
		searchAccounts: '勘定科目の検索',
		searchScotEstimates: 'SCOTs/見積りの検索',
		accounts: '勘定科目',
		scotsOrEstimate: 'SCOTs/見積り',
		accountNotRelatedToScotValidation: '各重要な勘定科目又は開示は、少なくとも１つのSCOTに関連付けられる必要があります。関連するSCOTをこの重要な勘定科目又は重要な開示に関連付けるには、チェックボックスを選択してください。',
		scotNotRelatedToAccountValidation: '各SCOTは、少なくとも1つの重要な勘定科目又は重要な開示に関連付けられる必要があります。このSCOTを関連する重要な勘定科目又は重要な開示に関連付けるには、チェックボックスを選択してください。',
		showValidations: 'バリデーションを表示',
	},
	scotSummary225: {
		relatedScots: '関連するSCOTs',
		relatedAcconts: '関連する勘定科目',
		scotListHeader: 'SCOTs及び見積り',
		noScotsMessage: '各重要な勘定科目又は開示は、少なくとも１つのSCOTに関連付けられる必要があります。この重要な勘定科目又は重要な開示に関連付ける既存のSCOTを選択してください。',
		noAccountsMessage: 'JA JP No significant accounts or disclosures have been created.',
		noAccountsAvailableOnSearch: 'JA JP No results found',
		relateAccounts: 'JA JP Relate accounts and disclosures',
		noAccountsCreated: 'JA JP No accounts have been created',
		noScotsCreated: 'JA JP No SCOTs have been created',
		relateScots: 'JA JP Relate SCOTs',
	},
	bodyUnavailableInCCP: 'このコンテンツは、EY Canvas Client Portalでは利用できません。',
	pyBalance: 'PY残高',
	cyBalance: 'CY残高',
	designationNotDefined: '定義されていない指定',
	controlRiskAssessment: '統制リスク評価',
	first: '最初のページ',
	noImportedTrialBalance: 'インポート済の試算表がありません。',
	placeHolderMessageWhenHelixMappingIsTrue: '{0}をクリックし新しいアナライザーを関連付けてください。',
	documentPrintSuccess: 'JA JP Document print in progress. It may take up to ten minutes. Once completed, the print will be added to the temporary files.',
	documentPrintError: '文書の印刷に失敗しました。ページを更新ししばらくしてから再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	backToEvidenceWarningMessage: 'JA JP This action could not be completed. Please refresh and try again. If issue persists, please contact the Help Desk.',
	rationaleMissingForLR: 'JA JP Each limited risk account shall have a rationale provided',
	rationaleMissingForIR: 'JA JP Each insignificant account shall have a rationale provided',
	craSummaryText2: 'JA JP Each account shall have designation determined. Click ',
	contentDrivingEntity: 'Content driving entity',
	contentDrivingEntityPlaceholder: 'JA JP Content driving entity has not been selected',
	rationaleForPlaceholder: 'JA JP Provide rationale for this account designation',
	contentDrivingEntityRequired: 'JA JP Content driving entity (required)',
	refreshContentLayers: 'JA JP Refresh content layers',
	noAccessLabel: 'JA JP Unauthorized. Contact your administrator and try again.',
	copyForHelpDeskDetails: 'JA JP Copy for Help Desk details',
	copyForHelpDeskDetailsSuccess: 'JA JP Details copied to clipboard',

	//toast activity as guest user
	sharedGuidedworkflowEvidenceWarning: 'JA JP This is a shared Guided Workflow Activity. The objects and evidence exist in the original engagement and will not be added to this engagement upon unlink. See <a style="color: #467cbe" href="https://live.atlas.ey.com/#library/104?pref=20058/9/5" target="_blank">enablement here</a> for further details.',
	sharedGuidedworkflowResponseWarning: "JA JP This is a shared Guided Workflow Activity. The responses are being shared with other activities in the same workspace. View relationships by accessing the 'Show relationships' menu item from this activity's summary section."
};

export const groupStructure = {
	createComponent: '新しい構成単位',
	deleteComponent: '構成単位の削除',
	manageComponents: '構成単位の管理',
	emptyComponents: '構成単位が作成されていません。開始するには、{newComponent}の作成を選択してください。',
	scope: 'スコープ',
	role: '役割',
	pointOfContact: '連絡先',
	linkRequest: 'リンクリクエスト',
	instructions: 'インストラクション',
	instructionsSent: '送信されたインストラクション',
	status: 'ステータス',
	createComponentInstructionalText: "以下に構成単位の詳細を入力し、<b>'保存して閉じる'</b>を選択して終了します。別の構成単位を作成するには、<b>'保存して別の構成単位を作成'</b>を選択します。 ",
	componentName: '構成単位名',
	region: 'リージョン',
	notUsingCanvas: 'EY Canvas 未使用',
	referenceOnly: '参照専用',
	saveAndCreateAnother: '保存して別のコンポーネントを作成',
	dueDate: '期日',
	components: '構成単位',
	allocation: '割当て',
	documents: 'Evidence',
	discussions: 'ディスカッション',
	EDAP: 'EDAPs',
	siteVisits: '往査',
	reviewWorkComponent: '実施された作業のレビュー',
	other: 'その他',
	significantUpdates: '重要な更新',
	executionComplete: '実施の完了',
	gaRoleTypesLabel: [{
		id: 1,
		displayName: 'プライマリー '
	},
	{
		id: 2,
		displayName: 'リージョナル '
	},
	{
		id: 3,
		displayName: '構成単位 '
	}
	],
	gaLinkStatusLabel: [{
		id: GALinkStatus.NotSent,
		displayName: '送信 '
	},
	{
		id: GALinkStatus.Sent,
		displayName: '送信済/承諾未了 '
	},
	{
		id: GALinkStatus.ComponentNotUsingCanvas,
		displayName: 'EY Canvas未使用 '
	},
	{
		id: GALinkStatus.ReferenceOnly,
		displayName: '参照専用 '
	},
	{
		id: GALinkStatus.Accepted,
		displayName: '承諾済 '
	},
	{
		id: GALinkStatus.Rejected,
		displayName: '差戻済 '
	},
	{
		id: GALinkStatus.Unlinked,
		displayName: 'リンク解除済 '
	},
	{
		id: GALinkStatus.Pending,
		displayName: '送信済/承諾未了 '
	}
	],
	notAvailable: '利用できません',
	search: labels.searchPlaceholder,
	noResultsFound: '結果が見つかりません。',
	noComponentsFound: '構成単位が見つかりません。',
	contentSwitcher: [{
		id: gaRoleTypes.primary,
		displayName: 'プライマリー '
	},
	{
		id: gaRoleTypes.component,
		displayName: '構成単位 '
	},
	{
		id: gaRoleTypes.regional,
		displayName: 'リージョン '
	}
	],
	gaRegionTypesLabel: {
		id: gaRegion.notApplicable,
		displayName: '該当なし '
	},
	//TODO: To be removed
	pointOfContactValues: [{
		id: pointOfContactTypes.EYcontact,
		displayName: 'EY担当者 '
	},
	{
		id: pointOfContactTypes.externalContact,
		displayName: '外部の連絡先 '
	}
	],
	saveAndClose: labels.modalSaveAndClose,
	cancelBtn: labels.modalCancelTitle,
	gaScopesValues: [{
		id: gaScopeType.full,
		displayName: 'フル '
	},
	{
		id: gaScopeType.specific,
		displayName: 'スペシフィック '
	},
	{
		id: gaScopeType.specifiedAuditProcedures,
		displayName: '特定の手続 '
	},
	{
		id: gaScopeType.review,
		displayName: 'レビュー '
	}
	],
	edit: labels.edit,
	delete: labels.delete,
	tooltipIcon: 'グループ監査のインストラクションを受領し、インターオフィスデリバラブルズを提出するために、構成単位がEY Canvasを使用していない場合は、このオプションを選択します。',
	tooltipReferenceIcon: '<b>参照専用</b>に指定された構成単位チームではリンクリクエスト、インストラクション又はタスクを受領できません。また、プライマリーチームもグループタスクを受領しません。これらはグループ監査人の状況を把握する目的でのみ使用されます。',
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	modalConfirmBtnLabel: labels.confirmLabel,
	clear: 'クリア',
	clearUpper: labels.clear,
	nameOrEmail: 'EY連絡先Eメールアドレスを入力',
	editComponent: '構成単位の編集',
	editComponentInstructionalText: "以下の構成単位の詳細を編集し、<b>\'保存\'</b> を選択して終了してください。 ",
	linkAlreadyAcceptedInfo: 'リンクリクエストはすでに構成単位チームに送信されているため、、Eメールフィールドのみ編集できます。',
	sendAll: '全て送信',
	send: '送信',
	resend: '再送信',
	scopeAndStrategy: 'スコープ及び戦略',
	execution: '実施',
	conclusion: '結論',
	reportingForms: '報告フォーム',
	manageGroupPermission: '<b>グループの管理</b>を実行する権限がありません。 エンゲージメント管理者に<b>グループの管理</b> 権限をリクエストしてください。',
	manageComponentModalDesc: '新しい構成単位を作成するか、以下の既存の構成単位を編集または削除してください。',
	editLinkInfo: 'リンクリクエストはすでに構成単位チームに送信されているため、、Eメールフィールドのみ編集できます。',
	invalidPointOfContact: 'リンクリクエストを送信するには連絡先が必要です。構成単位を編集して連絡先を追加します。',
	manageComponentModalActions: 'アクション',
	manageComponentModalComponents: '構成単位',
	manageComponentModalDelete: '削除',
	noThereAtLeastOneComponentToSendAll: 'リンク リクエストを送信できる構成単位はありません。リンクリクエストを送信するには、構成単位のステータスが<b>送信</b>または<b>再送信</b>である必要があります。',
	showKnowledgeDescription: 'ナレッジからタイトルと説明を表示',
	hideKnowledgeDescription: 'ナレッジからタイトルと説明を非表示',
	instructionName: 'インストラクション名の入力',
	instructionDescriptionPlaceholder: 'インストラクションの説明の入力',
	selectDueDate: '期日（必須）',
	show: '表示',
	allocationHeader: '割当て',
	allocationInstructionForKnowledge: 'ナレッジインストラクションは、スコープ毎にのみ割当てることができます。以下から関連するスコープを選択します。',
	allocationInstructionForCustom: 'カスタムインストラクションは、スコープまたは構成単位毎に割当てることができます。以下のインストラクションの割当てを選択し、関連するスコープまたは構成単位に割当てます。',
	allocateScope: 'スコープへの割当て',
	allocateComponent: '構成単位への割当て',
	pillScopesPlural: 'スコープ',
	pillScopesSingular: 'スコープ',
	pillComponentsPlural: '構成単位',
	pillComponentsSingular: '構成単位',
	selectScopesPlaceholder: 'スコープの選択',
	selectComponentsPlaceholder: '構成単位の選択',
	searchNoResultFoundText: labels.searchNoResultFoundText,
	newCustomInstruction: '新規カスタムインストラクション',
	instructionNameNewCustomInstruction: 'インストラクション名',
	addCustom: 'カスタムの追加',
	custom: 'カスタム',
	required: '必須',
	remove: '削除',
	selectAll: '全て選択',
	unselectAll: '全ての選択を解除',
	lowerPoC: '連絡先',
	editPoCTooltip: '連絡先が無効または連絡先がありません。リンクリクエストを送信するために連絡先を編集します。',
	recomendationType: [{
		id: 1,
		label: '必須 '
	},
	{
		id: 2,
		label: '任意 '
	},
	{
		id: 3,
		label: '該当なし '
	}
	],
	confirmLabel: labels.confirmLabel,
	deleteComponentInstructionalText: '<b>この構成単位をグループ構造から削除しますか？</b><br />構成単位が削除されると、この構成単位へのリンクが削除され文書の授受ができなくなります。 さらに、この構成単位とその勘定科目及びインストラクション間の全ての関連付けが削除されます。',
	noActivitiesAvailable: '利用可能なアクティビティはありません。',
	relatedComponents: '関連する構成単位',
	relatedComponentsSingular: '関連する構成単位',
	relatedComponentsPlural: '関連する構成単位',
	publish: '発行',
	publishModalHeader: '変更を反映',
	publishChangesInstructional: '<b>グループインストラクションサマリーに対する変更を反映しますか？</b><br />前のグループインストラクションのセットは上書きされます。 変更を反映すると、更新されたインストラクションをグループインストラクションサマリーから送信できます。',
	publishManageGroupPermission: 'この操作を実施するには、グループの管理権限が必要です。エンゲージメント管理者に権限を依頼します。',
	lastPublished: '最終発行：',
	publishChangesNotAvailable: 'まだ利用できません',
	noRecordsFound: labels.noRecordsFound,
	deleteInstruction: 'インストラクションの削除',
	deleteInstructionInstructionalText: '<b>インストラクションを削除してよろしいですか？</b><br />この操作は元に戻すことができません。',
	sendInstructionsTitle: 'インストラクションの送付',
	sendInstructionsInstructionalText: '基になるページの「発行」をクリックして、最新のインストラクションが発行されたことを確認します。次に、以下の構成単位のインストラクションをレビューしてください。「送信」を選択して構成単位エンゲージメントにインストラクションを送信してください。',
	instructionsAlreadySent: 'インストラクションの直近バージョンは既に送付済みです。',
	missingDueDates: 'レポーティングフォームの期日がありません',
	createInstructionsModalButton: 'インストラクションの作成',
	createInstructionsModalActionToastMessageStart: '以下のグループリスク評価インストラクションがありません：',
	createInstructionsModalActionToastMessageEnd: ' 構成単位',
	createInstructionsModalDescription: '以下のフル及びスペシフィックスコープの構成単位は割当てられたグループリスク評価インストラクションがありません。<b>作成</b>を選択すると、以下の各構成単位についてグループリスク評価インストラクションが作成されます。',
	createInstructionsModalScope: ' スコープ',
	createInstructionsModalHeader: 'インストラクションの作成',
	createInstructionsModalmodalConfirmBtnLabel: '作成',
	createInstructionsModalmodalCancelBtnLabel: 'キャンセル',
	createInstructionsModalmodalCloseBtnTitletip: '閉じる',
	createInstructionsModalNewGraInstructionDescription: '構成単位に関連する勘定科目のリスク評価が含まれています。 リスク評価をレビューし、エンゲージメントでそれらの勘定科目とリスクが識別されていることを確認してください。 現地で識別された、または構成単位チームが同意しない追加的なリスクは、プライマリーチームと構成単位チームの双方でリスク評価を調整できるように、プライマリーチームとコミュニケーションする必要があります。',
	createInstructionsModalErrorMessage: '次の構成単位に対するグループリスク評価インストラクションの作成に失敗しました：<b>{0}</b>。ページを更新し再試行してください。',
	createInstructionsDuplicatedModalErrorMessage: 'グループリスク評価インストラクションの作成に失敗しました。インストラクション名は重複できません。',
	gaLinkActionTooltip: {
		NotUsingCanvasLabel: 'EY Canvasを使用しない',
		NotUsingCanvas: '<b>送信</b>をクリックすると、この構成単位のプライマリ<br/> グループ タスクが作成されますが、 <br/> インストラクションは送信されません。',
		NotLinkedLabel: 'リンクされていません',
		NotLinked: 'リンクリクエストは<br/> 構成単位チームに送信されていません。インストラクションを送信するには、リンク<br/>リクエストを送信します。',
		Unlinked: 'リンク解除済 '
	},
	viewHistory: '閲覧履歴',
	viewSentInstructionsTitle: '送信されたインストラクションを表示',
	save: labels.saveLabel,
	cancel: labels.cancelLabel,
	viewHistoryInstructionalText: 'インストラクションを選択すると、構成単位チームに送信されたインストラクションの以前のバージョンが表示されます。',
	viewHistorySelectInstruction: 'インストラクションの選択',
	viewHistoryDateSent: '送信日：',
	viewHistoryStatus: 'ステータス： ',
	viewHistoryStatusAccepted: '承諾済',
	viewHistoryStatusPending: '未了',
	viewHistoryStatusRejected: '拒否',
	viewHistoryStatusSystemError: 'JA JP System error',
	viewHistorySelectVersion: 'バージョンの選択',
	noAccountsFound: 'このエンゲージメント及びその他のエンゲージメント内で勘定科目又は開示が見つかりませんでした。<br />勘定科目又は開示を新規作成するか、既存のものを編集するには、{link} を選択してください。',
	generalCommunications: '一般的なコミュニケーション',
	reportingDeliverables: 'デリバラブルズのレポーティング',
	changesPublishedNotSent: '変更は送信されませんでした',
	changesPublishedBrNotSent: '変更<br/>は送信されませんでした',
	changesPublishedNotSentYes: 'はい',
	deleteSubScopeInstructionalTextModal: '選択したサブスコープ<br/>を削除してよろしいですか？',
	deleteSubScopeTitleModal: 'サブスコープの削除',
	riskAssessmentModal: {
		headerText: 'リスク評価',
		modalCloseBtnTitletip: labels.close,
		manageAndDisclosures: '勘定科目と開示の管理リンク',
		next: '次の構成単位',
		back: '前の構成単位 '
	},
	riskAssessment: 'リスク評価',
	preview: 'プレビュー',
	accountsAndDisclosureSummary: '勘定科目及び開示',
	noAccountSnapshotPlaceholder: 'この構成単位に表示する勘定科目データはありません。',
	createOversightProjectButtonLabel: 'EY Canvas Oversightプロジェクトの作成',
	createOversightProjectTitle: 'このプライマリーエンゲージメントでEY Canvas Oversightプロジェクトを作成しますか？',
	createOversightProjectDescription: 'このグループ構造で識別されたリージョナルチームおよび/または構成単位チームのEY Canvasエンゲージメントは、このEY Canvas Oversightプロジェクトの設定の一部として自動的に入力されます。',
	createOversightModalHeader: 'EY Canvas Oversightプロジェクト名',
	createOversightModalDescription: 'EY Canvas Oversightプロジェクトの名前を入力してください。',
	createOversightModalTextLabel: 'プロジェクト名',
	projectRedirectionButtonLabel: 'EY Canvas Oversightプロジェクト',
	projectAssociationTextLabel: 'このエンゲージメントに接続されたEY Canvas Oversightプロジェクトがあります。',
	sendLinkDisableTooltip: 'このエンゲージメントは、グループ監査フローの構成単位を含めてコピーされました。リンクは再送信できません。必要に応じて新しい構成単位を作成し、リンクを送信してください。 ',
	instructionsCannotBeSentUntilPublished: 'JA JP Instructions cannot be sent until they are published.'
};

export const groupInvolvement = {
	NoComponentsAvailables: '構成単位が作成されていません。開始するには、<b>構成単位の管理</b>を選択します。',
	GroupInvolvementToastMsgStart: '以下のグループ関与フォームがありません：',
	GroupInvolvementToastMsgEnd: ' 構成単位',
	CreateGroupInvolvementHeader: 'グループ関与フォームの作成',
	GroupInvolvementInstructionalText: '次の構成単位には、グループ関与フォームが割当てられていません。<br/> &#39;<b>作成</b>&#39; を選択すると、以下の各構成単位に対してグループ関与フォームが作成されます。',
	createGroupInvolvementDocumentErrorMessage: "'次の構成単位のグループ関与文書の作成に失敗しました：<b>{0}</b>. ページを更新し再試行してください。 ",
	createGroupInvolvementDocumentSuccessMessage: 'グループ関与フォームが正常に作成されました。 30秒後にページを更新して、利用可能な文書を表示します。 ',
	involvementTypePlanned: '計画されたInvolvementタイプ',
	significantUpdatesToPlannedInvolvement: '計画されたinvolvementの重要な更新',
	executionComplete: '実施の完了',
	generateGroupInvolvementCommunications: '関与フォームの印刷',
	generateGroupInvolvementInstructionalText: '次の構成単位にはグループ関与フォームが関連付けられています。以下において1つの文書に含めたい構成単位&#39;のグループ関与フォームを選択してください。<br /><br />構成単位を選択し、<b>&#39;作成&#39;</b> を選択すると、以下にリストされている各構成単位のグループ関与文書を含んだ1つのグループ関与文書が作成されます。',
	componentTeams: '構成単位チーム',
	noComponentsSelectedErrorMessage: 'グループ関与コミュニケーションを作成する構成単位の選択',
	documentName: '{taskName} グループ関与パッケージ',
	selectAll: groupStructure.selectAll,
	unselectAll: groupStructure.unselectAll,
	modalConfirmBtnLabel: groupStructure.createInstructionsModalmodalConfirmBtnLabel,
	modalCancelBtnLabel: groupStructure.cancelBtn,
	modalCloseBtnTitletip: groupStructure.modalCloseBtnTitletip
};

export const itPlanning = {
	supportingITColumnsHeaders: {
		applicationTool: {
			name: 'Applications/Tools'
		},
		network: {
			name: 'ネットワーク '
		},
		database: {
			name: 'データベース '
		},
		operatingSystem: {
			name: 'オペレーティングシステム '
		}
	},
	relatedITProcessesColumnsHeaders: {
		relatedITProcess: 'Related IT processes',
		category: 'Category'
	},
	itPlanningPlaceholders: {
		smartEvidenceSourceEntityId: 'この文書は関連付けられたテクノロジーを利用できません',
		smartEvidenceSourceId: '関連オブジェクトはありません。オブジェクトを関連付けて開始します。',
	},
	relatedITProcessesPlaceholders: {
		smartEvidenceSourceEntityId: 'Related IT process not available for this document',
		smartEvidenceSourceId: '関連オブジェクトはありません。オブジェクトを関連付けて開始します。',
		relatedITProcessEmpty: 'No IT process related to the technology'
	},
	noTechnologiesIdentified: 'テクノロジーは識別されていません',
	supportingITEmpty: 'テクノロジーに関連するアプリケーション/ツールはサポートされません',
	supportingITNetworkEmpty: 'テクノロジーに関連するネットワークはサポートされません',
	searchPlaceholder: '検索',
	newTechnology: '新しいテクノロジー',
	noSupportingDatabases: 'テクノロジーに関連するデータベースはサポートされません',
	createEntityFormDocument: '文書の作成',
	noSupportingOperatingSystem: 'JA JP No supporting operating systems related to the technology',
	manageTechnology: 'JA JP Manage technology'
};

export const itRiskFactors = {
	accepted: '承諾済',
	rejected: '差戻済',
	accept: '承諾',
	reject: '差戻',
	rejectionRationale: '差戻し理由',
	rejectionCategory: '差戻しカテゴリー',
	rejectionRationaleRequired: 'JA JP Rejection rationale (required)',
	rejectionCategoryRequired: 'JA JP Rejection category (required)',
	riskName: 'JA JP Risk name',
	smartEvidenceValidations: {
		smartEvidenceSourceEntityId: 'リスク要因は、この文書では利用できません',
		smartEvidenceSourceId: 'JA JP No related object. Relate an object to get started.'
	},
	manageChangePlaceholders: {
		empty: 'JA JP No risk factor associated to the technology',
		emptyAccepted: '全てのリスクが差し戻されました '
	},
	manageOperationsPlaceholders: {
		empty: 'JA JP No risk factor associated to the technology',
		emptyAccepted: 'JA JP All IT risk factors have been rejected'
	},
	manageAccessPlaceholders: {
		empty: 'JA JP No risk factor associated to the technology',
		emptyAccepted: 'JA JP All IT risk factors have been rejected'
	},
	SDLCPlaceholders: {
		empty: 'JA JP No risk factor associated to the technology',
		emptyAccepted: '全てのリスクが差し戻されました '
	},
	manageSecuritySettingsPlaceholders: {
		empty: 'JA JP No risk factor associated to the technology',
		emptyAccepted: 'JA JP All IT risk factors have been rejected'
	}
};

export const rejectionTypeResource = [{
	id: rejectionType.itRiskOther,
	label: 'JA JP ITRisk Other'
},
{
	id: rejectionType.itRiskOption2,
	label: 'JA JP ITRisk - "Option 2"'
},
{
	id: rejectionType.itRiskOption3,
	label: 'JA JP ITRisk - "Option 3"'
}
];

export const sampleList = {
	newSample: '新しいサンプル',
	createSampleModalDescription: "以下にサンプルの詳細を入力し、'<b>{0}</b>'を選択して終了します。別のサンプルを作成するには、'<b>{1}</b>'を選択します。 ",
	saveAndCreateAnother: '保存して別のコンポーネントを作成',
	saveAndClose: '保存して閉じる',
	sampleDescription: 'サンプルの説明 (必須)',
	sampleDate: 'サンプル日 (必須)',
	sampleListId: 'サンプルリストID',
	ok: 'OK',
	addSample: 'サンプルの追加',
	cancel: 'キャンセル',
	saveAndCloseForHeader: '保存して閉じる',
	saveAndCreateAnotherHeader: '保存して別のものを作成',
	required: '必須',
	description: 'サンプルの説明',
	date: '日付',
	attributeStatus: '属性',
	tags: 'タグ',
	open: '開く',
	notApplicableLabel: 'N/A',
	notPresent: '存在しません',
	present: '存在します',
	pagingShowtext: '表示',
	placeHolderMessage: '利用可能なサンプルがありません。{clickHere}をクリックして開始します。',
	noSampleListAvailable: '利用可能なサンプルリストがありません',
	editSample: 'サンプルの編集',
	editSampleDescription: "以下にサンプルの詳細を編集し'<b>{0}</b>'を選択して終了します。 ",
	editSampleSave: '保存',
	sampleCanNotBeCreated: 'この文書のサンプルを作成できません。',
	noRelatedObject: '関連オブジェクトはありません。オブジェクトを関連付けて開始します。',
	noResultsFound: '結果が見つかりません',
};

export const AdditionDocumentationLabels = {
	addAdditionalDocumentation: '追加の文書化の追加',
	editAdditionalDocTitle: '追加の文書化の編集',
	removeAdditionalDocumentation: '追加の文書化の削除',
	cancel: 'キャンセル',
	save: '保存',
	of: '/',
	additionalDocTitlePlaceholder: '追加の文書化（必須）',
	additionalDocTitle: '追加の文書化（必須）',
	remove: '削除',
	enterAdditionalDocTitle: "以下に追加の文書化を入力し、<b>'{0}'</b>を選択して終了します。 ",
	editAdditionalDocDesc: "以下に追加の文書化を編集し、<b>'{0}'</b>を選択して終了します。 ",
	characters: '文字数',
	required: '必須',
	descriptionMaxLengthError: '回答が最大許容値を超えています。',
	attributeIndexLabel: '属性インデックス',
};

export const sampletAttributeConstants = [{
	id: 1,
	label: 'オープン '
},
{
	id: 3,
	label: '存在します '
},
{
	id: 7,
	label: 'コメント付きで存在 '
},
{
	id: 5,
	label: '存在しません '
},
{
	id: 4,
	label: 'N/A '
},
];

export const groupInstructions = {
	ALRAPackageModalTitle: 'ALRAパッケージ名',
	ALRAPackageModalInstructionalText: 'エビデンスに追加したALRAパッケージの名称を入力してください。',
	ALRAPackageModalNameField: '名称の入力',
	ALRAPackageSuccessToastMessage: 'パッケージ作成プロセスが開始されました。完了するまでに最大10分かかる場合があります。',
	ALRAPackageInProgressToastMessage: 'パッケージ作成プロセスが進行中です。完了するまでに最大10分かかる場合があります。',
	delete: labels.delete,
	deleteSectionModalTitle: labels.deleteSection,
	deleteSectionInstructionalText: '<b>セクションを削除してよろしいですか？</b><br />この操作は元に戻すことができません。',
	deleteSectionTooltipText: 'セクションを削除する前に<br />インストラクションを削除する必要があります。',
	modalConfirmBtnLabel: labels.confirmLabel,
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	missing: 'なし',
	sendAllModalTriggerButton: '全て送信',
	sendAllModalTooltipText: '構成単位チームに送信できるインストラクションはありません。',
	publishModalTooltipText: 'グループインストラクションは、送信する前に発行する必要があります。インストラクションが発行されると、変更内容は新しいインストラクションとして保存され、以前のバージョンのインストラクションが上書きされます。これらの新しいインストラクションは、構成単位チームに送信できます。',
	sendAllModalErrorMessage: 'Group instructions for the following Components were not sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk. <br /> <b>{0}</b>',
	sendAllModalHeaderText: '全てのインストラクションを送信',
	sendAllModalConfirmBtnLabel: '送信',
	sendAllModalCancelBtnLabel: 'キャンセル',
	sendAllModalCloseBtnTitletip: '閉じる',
	sendAllModalDescription: ' <b>閉じる</b>を選択すると、次の構成単位チームにインストラクションが送信されます。',
	generateGroupRiskAssessmentCommunications: 'グループALRAの生成',
	bulkALRAPackageName: '{instructionName} 勘定科目レベルのリスク評価パッケージ',
	groupInstructionSummaryReport: 'グループインストラクションサマリーレポート',
	groupInstructionSummaryReportTitletip: 'グループインストラクションの詳細、インストラクション送付履歴、および構成単位/勘定科目のマッピングの変更を表示およびエクスポートします。',
	exportGroupRiskAssessment: 'サマリーを出力',
	reportingDeliverables: groupStructure.reportingDeliverables,
	groupRiskAssessment: 'グループリスク評価 '
};

export const sectionTitles = [{
	id: KnowledgeSectionIds.GeneralCommunications,
	sectionTitle: groupStructure.generalCommunications
},
{
	id: KnowledgeSectionIds.ScopeOfWork,
	sectionTitle: '作業スコープ '
},
{
	id: KnowledgeSectionIds.ReportingForms,
	sectionTitle: groupStructure.reportingDeliverables
},
{
	id: KnowledgeSectionIds.ProceduresPerformedCentrally,
	sectionTitle: '集約的に実施される手続 '
},
{
	id: KnowledgeSectionIds.GroupRiskAssessment,
	sectionTitle: groupInstructions.groupRiskAssessment
},
{
	id: KnowledgeSectionIds.OtherCommunications,
	sectionTitle: 'その他のコミュニケーション '
}
];

export const groupAuditToolbar = {
	search: labels.placeholderForSearch
};

export const AccountType = [{
	id: 1,
	accounttypename: '重要な勘定科目 '
},
{
	id: 2,
	accounttypename: 'リスクが限定的な勘定科目 '
},
{
	id: 3,
	accounttypename: '軽微な勘定科目 '
},
{
	id: 4,
	accounttypename: '他の勘定科目 '
},
{
	id: 5,
	accounttypename: '重要な開示 '
}
];

export const PriorityType = [{
	value: 1,
	label: '低 (Low) '
},
{
	value: 2,
	label: '中（Medium） '
},
{
	value: 3,
	label: '高（High） '
},
{
	value: 4,
	label: 'クリティカル（Critical） '
}
];

export const AccountSummaryAccountType = [{
	id: '0',
	accounttypename: '全ての勘定科目 '
},
{
	id: '1',
	accounttypename: '重要な勘定科目 '
},
{
	id: '2',
	accounttypename: 'リスクが限定的な勘定科目 '
},
{
	id: '3',
	accounttypename: '軽微な勘定科目 '
},
{
	id: '4',
	accounttypename: '勘定科目-その他 '
},
{
	id: '5',
	accounttypename: '重要な開示 '
}
];

export const TaskStatus = [{
	id: 1,
	status: 'オープン '
},
{
	id: 2,
	status: '進行中 '
},
{
	id: 3,
	status: 'レビュー中 '
},
{
	id: 4,
	status: '完了 '
},
{
	id: 5,
	status: '削除済 '
}
];

export const reviewNoteLabels = {
	/*Review Notes*/
	emptyNoteDetailsMessage: 'ノートを選択し詳細を閲覧してください. 複数のレビューノートを選択したい場合にはcontrol又はshiftキーを使用して下さい. 個別のノートについて作業したい場合, リストからそのノートを選択して下さい.',
	documentReviewNotesLabel: '文書ノート',
	addNewReviewNoteButtonText: 'ノートの追加',
	noNotesAssociatedWithDocumentLabel: 'この文書に関連付けられたノートはありません',
	allNotesLabel: 'すべてのノート',
	charactersLabel: '文字',
	myNotesLabel: 'マイノート',
	showClearedLabel: 'クリア済を表示',
	showClosedLabel: 'クローズ済を表示',
	toLabel: '宛先',
	toUserLabel: '宛先',
	ofLabel: 'of',
	textAreaPlaceholder: 'ノートの入力',
	addNewNoteModalClose: 'クローズ',
	addNewNoteModalTitleLabel: '新規ノートの追加',
	editNoteModalTitleLabel: 'ノートの編集',
	deleteIconHoverText: '削除',
	deleteIconModalAcceptText: '削除',
	deleteIconModalConfirmMessage: 'このノートに対する返答を削除しますか？',
	deleteIconModalConfirmMessageParent: '選択されたノートを削除しますか?',
	deleteIconModalTitleLabel: 'ノートの削除',
	deleteReplyIconModalTitle: '返答の削除',
	emptyRepliesMessage: '返答が入力されていません',
	replyInputPlaceholder: 'このノートに対する返答',
	replyText: '回答テキスト',
	editReplyModelTitle: '返答の編集',
	noteDueDateLabel: '期日',
	fromUserLabel: 'From',
	priorityLabel: '優先度',
	dueDateLabel: '期限',
	dueLabel: '期限',
	status: 'ステータス',
	noteModifiedDateLabel: '編集者',
	cancelLabel: 'キャンセル',
	saveLabel: '保存',
	clearedBy: '作成者',
	closedBy: 'クローズ者',
	reopenedBy: '再オープン者',
	reply: '返信',
	editIconHoverTextLabel: '編集',
	required: '必須',
	closeTitle: 'クローズ',
	otherEngagementNotes: '他のエンゲージメントノート',
	closeLabel: '閉じる',
	showMore: 'さらに表示',
	showLess: '表示を減らす',
	showMoreEllipsis: 'さらに表示 ...',
	showLessEllipsis: '表示を減らす...',
	noResultFound: '結果が見つかりません',
	engagementNameLabel: 'エンゲージメント名:',
	drag: 'ドラッグ',
	formMaxLength: '文字数が {number} 字を超えることはできません。',
	voiceNoteButtonLabel: 'ボイスノート',
	stopRecordingButtonLabel: '停止',
	reopen: '再開',
	noNotesFound: 'ノートが見つかりません',
	noNotesFoundInstructional: '以下の入力欄を使用してコメントを残してください。ノートをユーザーに割当て、優先度と期日を指定します。',
	microphoneBlockedMessage: 'ブラウザがマイクにアクセスしてボイスノートを使用できるようにします。すでに許可されている場合は、更新して再試行してください。',
	microphoneBlockedOnVideoMessage: '画面録画で音声を使用できるようにするには、ブラウザがマイクにアクセスできるようにします。既に許可されている場合は、更新して再試行してください。',
	notInMainWindowVoice: 'ドロワー内での音声録音は許可されていません。アクションを実行するには、新しいタブで文書を開いてください。',
	notInMainWindowScreen: 'ドロワー内での画面録画は許可されていません。アクションを実行するには、新しいタブで文書を開いてください。',
	voiceNoteNotAvailable: 'ボイスノートと画面録画は、ドロワービューでは利用できません。これらの機能を使用するには、全画面表示に切り替えてください。',
	playButtonTitle: '再生',
	deleteButtonTitle: '消去',
	pauseButtonTitle: '一時停止',
	screenRecord: '画面録画',
	playbackReview: 'プレイバック・レビュー '
};

export const IndividualAccountAttributeLabels = {
	attributesNotAvailableForDocument: '勘定科目の属性は、この文書では利用できません。',
	noRelatedOnject: '関連オブジェクトはありません。オブジェクトを関連付けて開始します。',
	noAttributesAvailable: '利用可能な属性がありません',
	noRisksAvailable: '利用できるリスクがありません',
	attributeStandardRomms: '標準ROMMsの属性',
	continueButtonTitle: '続行',
	closeButtonTitle: 'キャンセル',
	newAssertionModalPlaceholder: 'この選択により、以前は関連性が識別されていなかった以下のアサーションが、［低(lower)］の固有リスクとして識別されます。続行しますか？',
	assertion: 'アサーション',
	inherentRiskType: '固有リスク',
	assertionModalTitle: '新しいアサーション',
	riskType: '低 '
};

export const entities = [{
	id: 0,
	name: '全て '
},
{
	id: 1,
	name: '文書 '
},
{
	id: 2,
	name: 'リードスケジュール '
},
{
	id: 3,
	name: '勘定科目 '
},
{
	id: 4,
	name: 'SCOT '
},
{
	id: 5,
	name: 'ITプロセス '
},
{
	id: 6,
	name: '監査計画 '
},
{
	id: 7,
	name: 'リスク '
},
{
	id: 8,
	name: 'タスク '
},
{
	id: 9,
	name: '虚偽表示 '
},
{
	id: 10,
	name: '不備 '
},
{
	id: 11,
	name: 'GA構成単位 '
},
{
	id: 12,
	name: 'GA構成単位インストラクション '
},
{
	id: 13,
	name: 'GA構成単位エビデンス '
},
{
	id: 14,
	name: 'GAスコープ '
},
{
	id: 15,
	name: 'GAプライマリーインストラクション '
},
{
	id: 16,
	name: 'GAプライマリー '
},
{
	id: 17,
	name: 'クライアントリクエスト '
},
{
	id: 18,
	name: 'WCGW '
},
{
	id: 19,
	name: '統制 '
},
{
	id: 20,
	name: 'ITアプリケーション '
},
{
	id: 21,
	name: 'Canvasフォーム '
},
{
	id: 22,
	name: 'フォームセクション '
},
{
	id: 23,
	name: 'フォーム本体 '
},
{
	id: 24,
	name: 'アサーション '
},
{
	id: 25,
	name: 'クライアントエンゲージメント '
},
{
	id: 26,
	name: 'クライアントグループ '
},
{
	id: 27,
	name: 'エンゲージメントタグ '
},
{
	id: 28,
	name: 'エンゲージメント '
},
{
	id: 29,
	name: 'フォームヘッダー '
},
{
	id: 30,
	name: 'フォームステータス '
},
{
	id: 31,
	name: 'エンゲージメントユーザー '
},
{
	id: 32,
	name: 'クライアントグループユーザー '
},
{
	id: 33,
	name: 'PSPインデックス '
},
{
	id: 34,
	name: 'ITGC '
},
{
	id: 35,
	name: 'ITリスク '
},
{
	id: 36,
	name: 'オートメーションラインアイテム '
}
];

export const PaceType = [{
	id: 1,
	paceTypename: '低 '
},
{
	id: 2,
	paceTypename: '中 '
},
{
	id: 3,
	paceTypename: '高 '
},
{
	id: 4,
	paceTypename: 'クロースモニタリング '
}
];

export const DocumentHelper = {
	401: '操作を完了できません。ぺージを更新し再試行してください。問題が解消しない場合は、ヘルプデスクに連絡してください。 ',
	413: '文書ファイルが許容サイズ(250mb)を超えているため、アップロードできません。ファイルサイズを小さくして、再試行してください。 ',
	412: '同じ名称の文書がこのエンゲージメント上に既に存在します ',
	414: '名称が最大文字数 (120文字)を超えています ',
	4099: '同じ名前の文書が既に存在します ',
	/*this is a hack as we dont always know why conflict happened.*/
	410: 'この文書は削除済のため開くことができません。',
	411: '空白の文書は認められません。 '
};

export const Errors = {
	/*Doc Helper Custom Messages */
	0: '接続が切断されました。再接続の上再試行してください。問題が解決しない場合は、ヘルプデスクに連絡してください。',
	10: 'EY Canvas Document Helperについて問題が見つかりました。問題の解消方法については<a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">こちら</a>をクリックしてください。',
	101: 'エンゲージメントステータスが無効です。',
	102: '有効なエンゲージメントユーザーが見つかりません。',
	103: 'エンゲージメントユーザーの独立性遵守が確認できません。',
	104: '必要なAzure ADスコープがありません。',
	105: 'エンゲージメント権限の取得中にエラーが発生しました。ページを更新し再試行してください。問題が続く場合は、ヘルプデスクに連絡してください。',
	106: "Unauthorized. Contact your administrator and try again.",
	107: '有効なエンゲージメントユーザーが見つかりません。ページを更新して再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	108: 'エンゲージメントプロファイルが未了です。ランディングページに進みプロファイルを完了してください。',
	303: '同じ名称の文書がアップロード中です',
	403: 'Access to this document is not available.  If this document is shared ensure you have access to the source engagement.  Refresh the page and try again.  If the error persists contact the Help Desk.',
	406: 'ドキュメントを空にすることはできません。',
	412: 'エンゲージメントに同一名称の文書が既に存在します。',
	414: '名称が最大文字数 (120文字)を超えています',
	411: '空白の文書は認められません。',
	500: '接続が切断されました。再接続の上再試行してください。問題が解決しない場合は、ヘルプデスクに連絡してください。',
	600: '今回のオペレーションが完了できません。ページを更新して再試行してください。問題が解決しない場合は、ヘルプデスクにご連絡ください。',
	601: 'スクリーンショットのダウンロード中にエラーが発生しました。ページを更新して、再試行してください。問題が解決しない場合は、ヘルプデスクに連絡してください。 ',
	602: '文書は既にコラボレーションにあります。',
	935: 'ユーザーには、操作を実行するための十分な権限がありません。',
	zip: 'サポートされていないファイルタイプが1つ以上含まれているか、埋め込まれたzipファイルの最大数を超えているため、zipファイルをアップロードできません',
	401000: 'ネットワークの変更が検出されました。継続するにはページを更新してください。',

	/*Accounts*/
	1001: '勘定科目作成のコールに失敗しました。',
	1002: '勘定科目名がありません。',
	1003: 'The selected Account has been deleted. Close this modal to see the updated list.',
	1004: '結果が見つかりません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	1005: 'エンゲージメントIDが無効です。',
	1006: '勘定科目の種類が無効です。',
	1007: '文書種類が無効です。',
	1008: '選択した勘定科目が削除されました。このモーダルを閉じると、更新されたリストが表示されます。',
	1009: 'IDによる勘定科目の取得のコールに失敗しました。',
	1010: 'エンゲージメントIDによる勘定科目の取得のコールに失敗しました。',
	1011: '無効なリクエストにより、勘定科目IDに関するSEMサマリーの取得のコールに失敗しました。',
	1012: 'サマリー種類が無効です。',
	1013: '勘定科目レビューの作成のコールに失敗しました。',
	1014: '勘定科目レビューの削除のコールに失敗しました。',
	1015: '勘定科目レビューの作成リクエストが無効です。',
	1016: '勘定科目レビューIDが無効です。',
	1017: '勘定科目はこのエンゲージメントにないか、削除されています。',
	1018: '勘定科目レビューが別のユーザーによって作成されました。',
	1019: '勘定科目が別のユーザーに削除されました。ページを更新し再試行してください。',
	1020: '更新のためには勘定科目にPSPが必要です',
	1024: '勘定科目名が500文字を超えています。',
	1025: 'リスクが限定的または軽微な勘定科目は想定されていません',
	1026: '勘定科目に重複するアサーションを含めることはできません',
	1027: '無効なアサーションID',
	1037: 'PSPインデックスは重複できません',
	1039: '選択した勘定科目が削除されました。このモーダルを閉じると、更新されたリストが表示されます。',
	1048: '勘定科目の実施タイプが無効です。',
	1053: '勘定科目にはリスク又は見積りが関連付けられており、リスクが限定的な勘定科目又は軽微な勘定科目には設定できません。',
	1054: '削除されるアサーションにリスク又は見積りが関連付けられています',
	1065: '特別な検討を必要とするリスク、不正リスク又は重要な虚偽表示リスク又は見積りに関連付けられたアサーションを変更することはできません。まずこれらの関連付けを削除する必要があります。',
	1070: 'Helixが含まれている場合、試算表IDは空白にできません。',
	1072: 'アクションが完了できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',

	1266: 'エンゲージメントがマルチユーザー編集モードの文書の最大数に達しました。文書を再度チェックインし再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	1267: 'JA JP Document is conflicted. Resolve conflicts and try again.  If the issue persists, contact the Help Desk.',
	1268: 'JA JP Document is already in co-edit mode. end co-edit mode and try again.  If the issue persists, contact the Help Desk.',
	1269: 'JA JP Document is a shared evidence. Unlink and try again.  If the issue persists, contact the Help Desk.',
	1270: 'マルチユーザー編集モードでは、文書バージョンの削除や更新はできません。マルチユーザー編集を終了し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	1271: '文書が共同編集モードになっていないか、共同編集モードの終了中です。エラーが続く場合は、ヘルプデスクに連絡してください。',

	/*Assertions*/
	2001: '無効な作成リクエスト',
	2002: 'アサーション名がありません。',
	2003: 'アサーションがありません。',
	2004: 'アサーションの取得のコールに失敗しました。',
	2005: 'エンゲージメントIDが無効です。',
	2006: 'IDによるアサーションの取得のコールに失敗しました。',
	2007: 'アサーションのWCGWの取得のコールに失敗しました。',

	/*Risks*/
	4001: 'この操作を完了することができません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	4002: 'リスク名称がありません。',
	4003: 'リスクがありません。',
	4004: 'リスクの取得のコールに失敗しました。',
	4005: 'エンゲージメントIDが無効です。',
	4006: 'IDによるリスクの取得のコールに失敗しました。',
	4007: '無効なクエリリクエスト。',
	4008: 'この見積りは利用できなくなりました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	4009: '無効なアップデートリクエスト。',
	4010: '特定のWCGWが既にこのリスクに割当てられています',
	4011: 'WCGWリストをnullにすることはできません。',
	4012: 'リスク種類の取得に失敗しました。',
	4013: '作成リクエストが無効です。',
	4014: '関連するアサーションが有効ではありません。ページを更新の上、再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	4015: 'WCGWが有効ではありません。ページを更新の上、再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	4016: '選択したリスク/見積りが削除されました。このモーダルを閉じると、更新されたリストが表示されます。',
	4017: 'アサーションはリスクに対して有効ではありません。ページを更新の上、再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	4018: '渡されたリスク種類IDが無効です。',
	4019: 'リスク名が無効です。',
	4020: '無効な文書Id。',
	4021: 'リスク名は500文字以上にすることはできません。',
	4023: 'アサーションIdリストは空白にできません。',
	4024: 'リスクが限定的な勘定科目の文書化フォームはエラーのため作成できませんでした。ページを更新し再試行してください。問題が続く場合は、ヘルプデスクに連絡してください。',
	4025: 'アカウントIDが無効です。',
	4026: '無効なアサーションID。',
	4027: 'アサーションリスクモデルを空白にすることはできません',
	4031: 'リスクまたはフォーム本文のオプションが無効です。',
	4035: '特別な検討を必要とするリスクまたは不正リスクの高リスクは編集できません。',
	4036: 'アサーションIDが渡されない場合、ナレッジアサーションIDを空にすることはできません。ナレッジアサーションIDは列挙型にする必要があります',
	4037: 'この勘定科目のナレッジアサーションIDは既に存在します。',
	4038: 'リスクタイプIDが許容されるオプションと一致しません。',
	4062: 'このSCOTは利用できなくなりました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	4063: 'SCOTの関連付けを編集できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	4076: 'JA JP This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	4079: 'JA JP This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*TASK*/
	5001: 'JA JP Get all tasks failed.',
	5002: 'このタスクは、このエンゲージメントでは利用できなくなりました。',
	5003: 'タスクの文書の取得に失敗しました。リクエストパラメーターが無効です。',
	5004: '文書に関連するタスクの取得に失敗しました。リクエストパラメーターが無効です。',
	5005: 'IDによるタスクの取得に失敗しました。',
	5006: 'タスクカテゴリの取得のコールに失敗しました。',
	5007: 'タスクのサブカテゴリの取得のコールに失敗しました。',
	5008: 'タスク説明の取得のコールに失敗しました。',
	5009: 'タスクのクライアントリクエストの取得のコールに失敗しました。',
	5010: 'タスクのクライアントリクエストの保存のコールに失敗しました。',
	5011: '項目の関連付けようとしているタスクが削除または差し戻されました。',
	5012: '関連付けようとしている項目が削除されました。',
	5013: 'アイテムを関連付けようとしているタスクが削除またはリジェクトされました。',
	5014: '関連付けようとしている文書が削除されました。',
	5015: 'タスクエビデンスの取得コールが失敗しました。',
	5016: 'WCGWタスクの取得コールが失敗しました。',
	5017: 'エンゲージメントIDはゼロよりも大きい必要があります。',
	5018: '関連付けを完了できません。ページを更新して再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	5019: 'タスクの説明が空です',
	5020: '選択したタスクが削除またはリジェクトされました。そのため、現時点ではこのアクションを完了できません。',
	5021: 'ソースエンゲージメントIDが存在しません。',
	5022: '注釈の保存に失敗しました。',
	5023: 'エンゲージメントユーザーが見つかりません。',
	5024: 'タスクの削除コールが失敗しました。',
	5025: 'タスクの削除コールが失敗しました。',
	5026: 'タスクリストが空です。',
	5027: 'レビューノートが見つかりません。',
	5028: 'ファイル名は必須です。',
	5029: 'ファイル拡張子は必須です。',
	5030: 'ファイル名に以下を含めることはできません：*/:<>\\?|"',
	5031: '文書名の更新中にエラーが発生しました。',
	5032: '無効な文書ID',
	5033: '操作タイプが見つかりません。',
	5034: 'タスクステータスの変更に失敗しました',
	5035: '実行しようとしているアクションは、現時点では完了できません。後でもう一度やり直してください。このエラーが続く場合には、ヘルプデスクに連絡してください。',
	5036: 'コールで本文をnullまたは空にすることはできません。',
	5037: 'コールでリクエストをnullまたは空にすることはできません。',
	5038: '続行するには、一意のファイル名を入力してください。',
	5039: 'ファイル名は必須です。',
	5040: 'ファイル名は100文字までに制限されています。',
	5041: 'ファイル名に以下を含めることはできません：*/:<>\\?|"',
	5042: '選択されたタスクはリジェクトされています。',
	5043: 'タスクはビルドステップタスクです。',
	5044: 'タスクはマイルストーンタスクです。',
	5045: 'タスクはPSPでもOSPタイプでもありません。',
	5046: '文字数制限を超えています。',
	5047: '文字数制限を超えています。',
	5048: '必須フィールド。',
	5049: '選択した文書をこのタスクから削除することはできません。ページを更新して、再試行してください。問題が解決しない場合には、ヘルプデスクに連絡してください。',
	5050: 'タスクグループIDはゼロまたは無効であってはなりません。',
	5051: 'タスクセクションIDはゼロまたは無効であってはなりません。',
	5052: 'この文書を現在のタスクに追加しようとしたときにエラーが発生しました。コールに失敗しました。',
	5053: '現在のタスクでこの文書を更新しようとしたときにエラーが発生しました。コールに失敗しました。',
	5054: '現在のタスクでこの文書のコピーを追加しようとしたときにエラーが発生しました。コールに失敗しました。',
	5055: '文書の名前を変更しようとしたときにエラーが発生しました。コールに失敗しました。',
	5056: 'ナレッジまたはグループタスクのタスクタイトルを編集できません',
	5057: 'タスクタイプはOSTタイプでなければなりません。',
	5058: 'この文書はシステムに関連付けられているため、タスクから削除できません。',
	5059: '無効なタイムフェーズの値。',
	5060: 'エビデンスへの追加エラー。コールに失敗しました。',
	5061: '他のユーザーが表示された情報を更新しました。ページを更新して、再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	5062: 'リクエストされた文書は、エンゲージメントで選択したEY Atlasのチャネルでは利用できません。将来の掲載に向け、コンテンツ作成者に情報を提供できるように、ヘルプデスクに連絡をお願いします。',
	5063: '無効なパッチ操作。',
	5064: '選択されたタスクは削除または差し戻されました。そのため、現時点ではこのアクションを完了できません。',
	5065: 'タスクソースタイプはアップデートできません。リクエストは無効です。',
	5066: 'ガイダンス取得エラー。コールは失敗しました。',
	5067: 'タスク種類タイプをアップデートできません。リクエストは無効です。',
	5068: 'タスク種類タイプをアップデートできません。リクエストは無効です。',
	5069: 'タスク割当ての削除のコールが失敗しました。',
	5070: '選択された1つ以上のタスクが削除されました。エラーが続く場合は、再試行するか、ヘルプデスクに連絡してください。',
	5071: '割当てをアップデートできません。リクエストは無効です。',
	5072: '作成者が見つかりません。リクエストが無効です。',
	5073: '割当てが見つかりません。',
	5074: 'タスクの割当ての保存に失敗しました。',
	5075: '同じチームメンバーは、作成者以外の1つのタスク割当てのみに割当てることができます。エラーが続く場合は、再試行するか、ヘルプデスクに連絡してください。',
	5076: '選択したチームメンバーは、このエンゲージメントのアクティブメンバーではありません。エラーが続く場合は、再試行するか、ヘルプデスクに連絡してください。',
	5077: '選択した1つ以上のタスクが削除されました。エラーが続く場合は、再試行するか、ヘルプデスクに連絡してください。',
	5078: '選択したタスク割当てが削除されました。エラーが続く場合は、再試行するか、ヘルプデスクに連絡してください。',
	5079: '選択した1つ以上のタスクが削除されました。エラーが続く場合は、再試行するか、ヘルプデスクに連絡してください。',
	5080: '文書バージョンのレコードが存在しません。',
	5081: '現在このタスクに割当てられているユーザーを作成者として再割当てすることはできません。エラーが続く場合は、再試行するか、ヘルプデスクに連絡してください。',
	5082: '更新が失敗しました。文書名は、エンゲージメントにおいて一意である必要があります。このメッセージを削除する場合には、ページを更新してください。',
	5083: 'タスク詳細の文字数制限を超えています。',
	5084: 'タスク文書の作成コールに失敗しました。',
	5085: 'タスク文書の削除コールに失敗しました。',
	5086: 'タスクハンドオフの作成コールに失敗しました。',
	5087: 'タスクパッチのコールに失敗しました。',
	5088: 'このタスクにはハンドオフするエビデンスが含まれている必要があります。再試行するか、ヘルプデスクに連絡してください。',
	5089: 'このタスクに関連するすべてのエビデンス（ペーパープロファイルを除く）は、完了とマークするために少なくとも1人の作成者と査閲者が必要です。エラーが続く場合は、再試行してヘルプデスクに連絡してください。',
	5091: 'ペーパープロファイル名は既にエンゲージメントに存在しています。',
	5092: '名称に以下の文字を含めることはできません：*/:<>\\?|\\',
	5093: 'ペーパープロファイル名が最大長（100文字）を超えています。',
	5111: '選択された勘定科目、アサーション又はリスクが限定的な勘定科目が削除されました。ページを更新して、再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	5116: '文書タイプが無効です。',
	5139: '関連付けを完了できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	5131: '関連付けを完了できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	5146: 'タスクを完了としてマークできません。',
	5156: 'タスクの関連付けを編集できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',

	/*WCGW*/
	6001: 'WCGWの作成コールに失敗しました。',
	6002: 'WCGW名がありません。',
	6003: 'WCGWがありません。',
	6004: 'WCGWの取得コールに失敗しました。',
	6005: '無効なエンゲージメントID。',
	6006: '無効なアサーションID。',
	6007: 'IDによるWCGWの取得コールに失敗しました。',
	6008: '無効なリクエスト。',
	6009: '無効なWCGW ID。',
	6010: 'タスクをWCGWに関連付けることができませんでした。',
	6011: '選択したWCGWが削除されます。',
	6012: 'タスクとWCGWが同じアサーションに関連していません。',
	6013: '選択したタスクは同じエンゲージメントのものではありません。',
	6014: 'タスクをWCGWから関連付けを外すことができませんでした。',
	6015: 'タスクをWCGWに関連付けることができませんでした。',
	6016: 'タスクはリジェクトされ、WCGWに関連付けることはできません。',
	6017: 'タスクはマイルストーンタスクではなく、WCGWに関連付けることはできません。',
	6018: 'タスクはビルドステップタスクではなく、WCGWに関連付けることはできません。',
	6019: 'タスクはPSPまたはOSPではなく、WCGWに関連付けることはできません。',
	6020: 'タスクとWCGWが同じアサーションに関連付けられており、WCGWに関連付けることはできません。',

	/*Engagement*/
	7001: 'IDによる取得ができません。',
	7002: 'ワークスペースIDによるエンゲージメントの取得コールに失敗しました。',
	7003: 'エンゲージメントエンティティの全取得コールに失敗しました。',
	7004: 'IDによるエンゲージメントの取得コールに失敗しました。',
	7005: 'エンゲージメントユーザーの全取得コールに失敗しました。',
	7006: '姓は250文字を超えてはなりません。',
	7007: '無効なユーザータイプエラー。',
	7008: '名は250文字を超えてはなりません。',
	7009: 'ユーザーGUIをnullにすることはできません。',
	7010: '無効なユーザーステータスエラー。',
	7011: 'エンゲージメントユーザーの作成コールに失敗しました。',
	7012: '{0} {1}はすでにアクティブまたは保留中のチームメンバーであるため、インバイトできません。',
	7013: 'イニシャルは3文字を超えてはなりません。',
	7014: '現在、EY Canvasランディングページにアクセスすることができません。再試行し、問題が解決しない場合には、ヘルプデスクに連絡してください。',
	7015: '{0} {1}は次のアクセスグループにインバイトすることができません：{2}はエンゲージメントから削除されました。 更新して再試行してください。',
	7016: '{0} {1}はすでに次のアクセスグループのアクティブなチームメンバーであるため、ユーザーとしてインバイトできません：{2} ',
	7017: '選択したグループにドメインは許可されていません：{0} ',
	7018: 'Eメールアドレスはnullであってはなりません。',
	7019: '名はnullであってはなりません。',
	7020: '姓はnullであってはなりません。',
	7021: 'ユーザーイニシャルはnullであってはなりません。',
	7022: 'ユーザープライマリーオフィスはnullであってはなりません。',
	7023: 'ユーザーログイン名はnullであってはなりません。',
	7024: 'ユーザーEYロールはnullであってはなりません。',
	7025: 'ユーザーのエンゲージメント役割はnullであってはなりません。',
	7026: '操作を完了できません。再試行し、問題が解決しない場合は、ヘルプデスクに連絡してください。',
	7027: '無効なEメールアドレス',
	7028: 'エンゲージメントユーザーのパッチコールに失敗しました。',
	7029: 'エンゲージメントユーザー - 無効なエンゲージメントユーザーステータスID',
	7030: 'エンゲージメントユーザー - 無効なエンゲージメントユーザーロールID',
	7031: '1つ以上のエンゲージメントユーザーIDが見つかりません。',
	7032: 'Eメールは250文字を超えてはなりません。',
	7033: 'リクエストされたユーザーをnullにすることはできません。',
	7034: 'ユニバーサルメッセージプロセッサキューが失敗しました。',
	7035: 'イニシャルは3文字を超えてはなりません。',
	7036: '名は250文字を超えてはなりません。',
	7037: '姓は250文字を超えてはなりません。',
	7038: '外部ユーザーの作成コールに失敗しました。',
	7039: '1人以上のユーザーは、既にアクティブまたは保留中のチームメンバーであるため、インバイトできません。ページを更新して、再試行してください。問題が解決しない場合は、ヘルプデスクに連絡してください。',
	7040: '名は250文字を超えてはなりません。',
	7041: '姓は250文字を超えてはなりません。',
	7042: 'イニシャルは3文字を超えてはなりません。',
	7043: 'GPNは250文字を超えてはなりません。',
	7044: 'GUIは250文字を超えてはなりません。',
	7045: 'IDによる外部ユーザーの取得に失敗しました。',
	7046: 'ユーザーはNullであってはなりません。',
	7047: '現在、変更を保存できません。再試行し、問題が解決しない場合は、ヘルプデスクに連絡してください。',
	7048: '現在、EY CanvasのLanding Pageにはアクセスできないため、編集する必要があります。再試行し、問題が解決しない場合は、ヘルプデスクに連絡してください。',
	7049: 'エンゲージメントユーザーのアップデートのコールに失敗しました。',
	7050: 'メンバーを非アクティブ化することはできません。エンゲージメントには、エンゲージメントを管理する権限を持つメンバーが少なくとも1名必要です。選択内容を更新して、再試行してください。問題が解決しない場合は、ヘルプデスクに連絡してください。',
	7051: 'IDによる内部ユーザーの取得に失敗しました。',
	7052: 'ユーザーが見つかりません。',
	7053: 'IDによる取得ができません。',
	7054: 'エンゲージメントIDによるクイックリンクの取得のコールに失敗しました。',
	7055: '以前のメンバーを追加するための権限がありません。このアクションを実行できる適切な権限を有するエンゲージメントメンバーと協議してください。',
	7056: 'EY Canvasは変更を保存できませんでした。再試行し、問題が解決しない場合は、ヘルプデスクに連絡してください。',
	7057: '新規メンバーを追加するための権限がありません。このアクションを実行できる適切な権限を有するエンゲージメントメンバーと協議してください。',
	7058: 'ユーザーステータスが変更されました。ページを更新して、再試行してください。問題が解決しない場合は、ヘルプデスクに連絡してください。',
	7062: '現時点ではEY Canvas Client Portalにアクセスできず、既存のメンバー情報を更新する必要があります。再試行し、問題が解決しない場合には、ヘルプデスクに連絡してください。',
	7063: '外部メンバーを非アクティブ化することはできません。ページを更新して、再試行してください。問題が解決しない場合は、ヘルプデスクに連絡してください。',
	7064: '無効なパッチ操作。',
	7065: '{0} {1}は1つ以上のアクセスグループでアクティブ化できません。選択したグループにドメインを許可していません：{2}。',
	7066: '{0}は有効なユーザーではありません。',
	7067: '外部ユーザーのPUTコールに失敗しました。',
	7068: '現時点ではEY Canvas Client Portalにアクセスできず、既存のメンバー情報を更新する必要があります。再試行し、問題が解決しない場合には、ヘルプデスクに連絡してください。',
	7069: '選択したアクセスグループはアクティブではありません：{0}。削除して再試行してください。',
	7072: 'エンゲージメントのリンク解除処理中にエラーが発生しました。ページを更新して再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	7074: '変更を保存できません。エンゲージメントには、エンゲージメント管理権限を持ち、独立性を解決したアクティブなメンバーが少なくとも1人必要です。問題が解決しない場合は、ヘルプデスクに連絡してください。',
	7079: '独立性の提出のコールに失敗しました。',
	7080: 'ユーザーIDはログインしたユーザーのものではないため、無効なユーザーIDまたは独立性の提出は許可されていません。',
	7081: '「送信」をクリックする前に、全ての質問を完了してください。Show incomplete （未完了なものを表示する）オプションを使用して未完了の質問をフィルタしてください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	7082: 'リクエストするための独立性の文書が見つかりません。',
	7083: 'SDMサマリーのコールに失敗しました。',
	7084: 'ログインしたユーザーに対して、ユーザーIDが無効であるか、独立性のアクションが許可されていません。',
	7085: '独立性のコメントは4,000文字未満にする必要があります。',
	7086: '独立性のアクションのコールに失敗しました。',
	7087: 'このユーザーのアクセスを承認、拒否または上書きするには、筆頭業務執行社員、業務執行社員、エグゼクティブディレクターの役割でなければなりません。',
	7088: '独立性の提出の変更に失敗しました。後で再試行してください。',
	7098: '無効なPaceタイプ Id.',
	7099: '利用可能な独立性テンプレートがありません。再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	7154: '現在のCanvasフォームにユーザーが見つかりません',
	7155: 'リストアエンゲージメントにプロファイルの提出は許可されていません。',
	7156: 'コンテンツを更新しています。後で再試行してください。長時間問題が継続する場合は、ITヘルプデスクに連絡してください。',
	7158: '文書は利用できません。ページを更新し再試行してください。問題が続く場合は、ヘルプデスクに連絡してください。',

	/*SCOT*/
	8001: 'SCOT作成のコールに失敗しました。',
	8002: 'SCOT名称がありません。',
	8003: 'SCOTがありません。',
	8004: 'SCOTの取得のコールに失敗しました。',
	8005: '無効なエンゲージメントID。',
	8006: '無効なアサーションID。',
	8007: 'IDによるSCOTの取得のコールに失敗しました。',
	8008: '無効なリクエスト。',
	8009: 'JA JP The selected SCOT has been deleted. Close this modal to see the updated list.',
	8010: 'SCOT IDをnullまたは空にすることはできません。',
	8011: 'SCOT IDは0より大きな値にする必要があります。',
	8012: '文書IDが無効です。',
	8013: 'SCOTの更新のコールに失敗しました。',
	8014: 'SCOTの名前をNULLまたは空にすることはできません。',
	8015: 'SCOTの名前は500文字未満にする必要があります。',
	8016: 'SCOT戦略のタイプが無効です。',
	8017: 'SCOTのタイプが無効です。',
	8018: 'SCOT ITアプリケーションが無効です。',
	8019: 'ITアプリケーションが適用されない場合、Scot ITアプリケーションを空にする必要があります。',
	8028: 'JA JP The selected SCOT has been deleted. Close this modal to see the updated list.',

	/*User*/
	10001: 'ログインしているユーザー設定が見つかりません。',
	10002: 'ユーザーの全ての項目の取得要求が達成できていません',
	10003: 'ユーザーのプレゼンスの取得要求が達成できていません',
	10005: 'ユーザーの詳細を取得できません',

	/*Risk Type*/
	11001: '無効な作成リクエスト',
	11002: 'リスク種類の名称が欠けています',
	11003: 'リスク種類が欠けています',
	11004: 'リスク種類の取得要求が達成できていません',
	11005: '無効なエンゲージメントID',

	/*TaskDocuments*/
	80004: '1つ以上のタスクが無効です。',
	80005: '文書IDが無効です。',

	/*Edit Control*/
	83001: '統制の取得に失敗しました。',
	83002: 'IDによる統制の取得に失敗しました。',
	83003: '統制IDがnullまたは空です。',
	83004: '統制IDが無効です。',
	83005: '文書IDが無効です。',
	83006: '統制名がありません。',
	83007: '統制名の長さが無効です。',
	83008: 'リクエストが無効です。',
	83009: '統制の頻度IDが無効です。',
	83010: '統制のタイプIDが無効です。',
	83011: 'ITアプリケーション統制が無効です。',
	83012: '統制のデザインの有効性タイプIDが無効です。',
	83013: 'ITアプリケーションIDsが無効です。',
	83014: '統制のタイプが手作業防止または手作業発見タイプの場合、サービス受託会社タイプのITアプリケーションのみが許可されます。',
	83015: 'IT依存手作業統制のみが手作業IPEテストを選択できます。',
	83016: 'このプロファイルのエンゲージメントでは、低リスクの統制は許可されていません。',
	83017: 'フィルタされたSCOTsを複製します。',
	83018: '統制名が500文字を超えています。',
	83019: 'WCGW IDsが無効です。',
	83020: 'WCGW IDsの複製は許可されていません。',
	83021: 'ITアプリケーションIDsの複製は許可されていません。',
	83022: 'パラメーター {0}が無効です。',
	83023: '現在のページは無効です。',
	83024: 'ページサイズが無効です。',
	83025: '検索文字数は最大100文字としてください。',
	83026: 'IT APPは、IT依存手作業統制又はITアプリケーション統制の統制タイプにのみ関連付けることができます',
	83027: 'フィルタされたWCGWsを複製します。',
	83028: 'ナレッジ統制IDが無効です。',

	112000: 'ソースとターゲット文書が見つかりません。',
	112001: 'リクエストがNull値であるため呼び出しができません。',
	112002: 'リクエストの本文はNull値または空にすることはできません。',
	112003: 'ソースとターゲットの文書IDを等しくすることはできません。',
	112004: 'ソース文書IDはNull値または空にすることはできません。',
	112005: 'ターゲット文書IDはNull値または空にすることはできません。',
	112006: 'ソースエンゲージメントIDはNull値または空にすることはできません。',
	112007: 'ターゲットエンゲージメントIDはNull値または空にすることはできません。',
	112008: 'ソース文書は、指定されたエンゲージメントに対して無効です。',
	112009: 'ターゲット文書は、指定されたエンゲージメントに対して無効です。',
	112010: 'ターゲットエンゲージメントが見つかりません。',
	112011: 'ソースエンゲージメントについてフォームをリンクさせるための権限が不足しています。エンゲージメント管理者に連絡し十分な権限を取得して下さい。',
	112012: 'ターゲットエンゲージメントについてフォームをリンクさせるための権限が不足しています。エンゲージメント管理者に連絡し十分な権限を取得して下さい。',
	112013: 'ソースエンゲージメントのユーザーが無効です。',
	112014: 'ターゲットエンゲージメントのユーザーが無効です。',
	112015: '共有するソース文書タイプが無効です。',
	112016: '共有するターゲット文書タイプが無効です。',
	112017: 'ソース及びターゲットの文書タイプが一致しません。',
	112018: 'ソース及びターゲットの文書ナレッジフォームIDが一致しません。',
	112019: 'ソースや目的の文書についてリンクの共有はすでに存在しています。',
	112020: 'ソースとターゲットのエンゲージメントワークスペースが一致しません。',
	112021: 'ターゲット文書はターゲットにできません。',
	112022: '選択したアクティビティは既に他のアクティビティと共有されているため、共有対象として選択できません。',
	112023: 'ソース文書はターゲットにできません。',
	112024: 'ターゲットの文書IDのエンゲージメントIDが見つかりません。',
	112025: 'ソース文書IDのエンゲージメントIDが見つかりません。',
	112026: 'ソースとターゲットの文書IDのソース文書を等しくすることはできません。',
	112027: 'エンゲージメントがEY Canvas FITイネーブルメントのアクティビティを共有しているため、プロファイルの提出を続けることができません。',
	112028: 'ソースとターゲットのエンゲージメントIDを等しくすることはできません。',
	112029: 'ソースまたはターゲットのエンゲージメントIDと文書IDは、ルートのエンゲージメントIDと文書IDと一致する必要があります。',
	112030: '指定されたエンゲージメントの文書は無効です。',
	112031: '文書IDはNull値または空にすることはできません。',
	112032: 'エンゲージメントIDはNull値または空にすることはできません。',
	112033: 'ターゲット文書IDは一意である必要があります。',
	112034: 'ターゲットまたはソース文書は既に共有されています。',
	112035: '既存のリンクされた回答関連文書には、1つのターゲットのみ存在できます。',

	/*MissingDocument*/
	116001: 'Create form failed.',
	116002: '指定された文書タイプIDのナレッジ文書が見つかりません。',
	116004: '文書の作成に失敗しました。ページを更新ししばらくしてから再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',

	/* Annotation Errors*/
	12001: '無効な要求パラメータ',
	12002: '注釈の作成要求が達成できていません',
	12003: '注釈の取得が達成できていません',
	12004: '無効なエンゲージメントID',
	12005: '収集中のIDはゼロより大きい値として下さい',
	12006: 'ID収集はブランクとできません',
	12007: '無効なタスクID',
	12008: '無効な文書ID。',
	12009: '有効な文書IDまたはタスクIDが必要です。',
	12010: '返信には親が必要です。',
	12011: '無効なステータスID',
	12012: 'ドキュメントタイプは440GLである必要があります。',
	12013: '無効な注釈タイプです。',
	12014: '無効なエンゲージメントユーザーです。',
	12015: 'この文書は別のユーザーによって削除されました。',
	12016: '注釈の更新の呼び出しができませんでした。',
	12017: '回答しようとしているレビューノートは他のチームメンバーによって削除済みです。ページを更新し再試行してください。',
	12018: '注釈変更アクションタイプはNullであってはなりません。',
	12019: 'レビューノートは削除されました。',
	12020: '無効なアクションです。',
	12021: 'ユーザーは注釈の作成者ではありません。,',
	12022: 'オーサリングユーザーが存在しません。',
	12023: '作成者のユーザーがいないかエンゲージメントに含まれていません。,',
	12024: '割り当てられたユーザーが必要です。,',
	12025: '作成者のユーザーがいないかエンゲージメントに含まれていません。,',
	12026: '無効なコメント',
	12027: 'コメントは空白であってはなりません。,',
	12028: '期日は必須です。',
	12029: '有効な優先度が必須です。',
	12030: 'このノートのステータスが変更されました。ノートの編集や返信はできなくなります。更新されたデータを確認して編集を続行するには、ウィンドウを閉じてから再度開いてください。',
	12031: '注釈はトップレベルである必要があります。',
	12032: '注釈はトップレベルであってはなりません。',
	12033: '次の値の少なくとも1つは空にできません：優先度タイプ、期日、ステータス、または割当てられたユーザー。',
	12034: 'コメントが最大文字数（4,000文字）を超えています。',
	12035: '検索文字が最大文字数（500文字）を超えています。',
	12036: 'タスクIDか文書IDを受け入れる必要があり、両方は認められていません。',
	12037: '無効なパッチ操作。',
	12038: '存在しないノートを編集しようとしています。ページを更新して、再試行してください。',
	12039: '他のチームメンバーと同じノートを編集しようとしています。ページを更新して再試行してください。',
	12040: 'アノテーションユーザーの取得のコールに失敗しました。',
	12041: 'アノテーションユーザーの取得のコールに失敗しました。クエリ値が無効です。',
	12042: '文書タイプはアノテーションを作成には無効です。',
	12043: '既に存在しないタスク又は文書に関するノートを追加しようとしています。ページを更新して再試行してください。',
	12044: '既に存在しないノートへの返答を編集しようとしています。ページを更新して、再試行してください。',
	12045: 'レビューノートが見つかりません。',
	12046: '選択したノートは、別のユーザーによってすでに削除されています。',
	12047: 'オープンステータスのノートに対する返答のみ削除できます。ページを更新し再試行してください。',
	12048: '既に存在しないノートのステータスを変更しようとしています。ページを更新して、再試行してください。',
	12049: '他のチームメンバーによって既に削除されているノートへの返答を削除しようとしています。ページを更新して、再試行してください。',
	12050: 'クローズされたノートのみ削除できます。',
	12051: 'コメントタイプの注釈は、有効なHelixドキュメントに対してのみ作成できます。',
	12052: 'お探しのコメントタイプの注釈は削除されています。',
	12060: '参照番号が必要です。0より大きく1000未満である必要があります。 ',
	12061: '参照番号はNuｌｌ値でなければなりません。',
	12062: '注釈はコメントに対してのみ作成できます。',
	12066: 'オープンでないノートに回答しようとしています。',
	12067: '既に別のチームメンバーによって削除されたコメントへの回答を削除しようとしています。ページを更新し再試行してください。',
	12068: '無効か既に存在しない録音へのノートを追加しようとしています。ページを更新し再試行してください。',
	12069: '無効か既に存在しない録音へのノートを編集しようとしています。ページを更新し再試行してください。',
	12070: '財務諸表には1つのコメントのみ追加できます。既存のものを編集してください。',
	12071: '情報の削除に失敗しました。再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',

	/*FlowchartStepControl*/
	123054: 'JA JP Control relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',
	123045: 'JA JP This control is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepWCGW*/
	123022: 'このフローチャートステップは利用できなくなりました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	123023: 'このフローチャートステップは利用できなくなりました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	123055: 'JA JP WCGW relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepITApplicationSO*/
	123056: 'JA JP IT Application / service organization relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the issue persists.',



	/*FlowchartStepDocument*/
	123048: '文書の関連付けは編集できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	123033: 'このフローチャートステップは利用できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	123002: 'この文書は利用できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',

	/*Configuration*/
	13001: '設定が見つかりません。',
	13002: 'API設定の取得のコールに失敗しました。',

	/*Documents*/
	14001: 'リクエストがNull値であるためコールが実行できません。',
	14002: '文書が見つかりません。呼び出しができませんでした。',
	14003: 'エンゲージメントIdはゼロ以上である必要があります。',
	14004: 'エンゲージメントIdはnullもしくは空にできません。',
	14005: '関連するタスクの取得中のエラー。呼び出しに失敗しました。',
	14006: '選択された文書は削除できません。後で再試行してください。問題が解決しない場合は、ヘルプデスクにお問い合わせください。',
	14007: '予期しないエラーが発生しました。',
	14008: 'サインオフ中に予期しないエラーが発生しました。',
	14009: 'サインオフ中にエラーが発生しました。',
	14010: '承認IDはゼロより大きい必要があります。',
	14011: 'サインオフの削除中にエラーが発生しました。',
	14012: '本文は空欄にできません。',
	14013: '選択した文書はリンクを解除できません。後で再試行してください。問題が解決しない場合は、ヘルプデスクに連絡してください。',
	14014: '文書Idによるアカウントの取得に失敗しました。',
	14015: '無効な関連するエンティティです。',
	14016: '文書の承認の取得の呼び出しができませんでした。',
	14017: '全ての文書の発見事項の取得の呼び出しができませんでした。',
	14018: '全ての文書の取得の呼び出しができませんでした。',
	14019: 'サインオフが見つかりません。',
	14020: '無効なアクション値です。',
	14021: '無効な発見事項タイプです。',
	14022: '文書はエンゲージメントに含まれません。',
	14023: '文書変更タイプが有効ではありません。,',
	14024: '全ての文書の取得の呼び出しに失敗しました。パラメーターは有効ではありません。',
	14025: '文書レビューの作成中にエラーが発生しました。APIコールに失敗しました。',
	14026: '文書レビューの削除中にエラーが発生しました。APIコールに失敗しました。',
	14027: 'エンゲージメントIDはNull値または空にすることはできません。',
	14028: 'ユーザーIDが無効です。',
	14029: 'ユーザーはこのアクションをするための認証が行われていません。',
	14030: 'このエンゲージメントには渡されたIDの文書が見つかりません。',
	14031: '文書レビューIDは有効ではありません。',
	14032: '文書レビューが見つかりません。',
	14033: '文書は既に承認されています。',
	14034: 'ワークスペースにある他のエンゲージメントとのリンク解除中であるか、この文書は既にリンク解除されています。ページを更新し再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	14035: 'その文書は指定されたエンゲージメントには共有されていません。',
	14036: 'バージョンナンバーはゼロよりも大きい必要があります。',
	14037: 'オペレーションが完了できません。ページを更新して再試行してください。問題が解決しない場合は、ヘルプデスクにご連絡ください。',
	14038: '文書の変更理由の取得に失敗しました。',
	14039: 'IDによるに文書の変更理由の取得に失敗しました。',
	14040: '変更理由の更新に失敗しました。',
	14041: '無効な変更理由',
	14042: '変更理由が更新できませんでした。',
	14043: '変更理由が作成できませんでした。',
	14044: '変更理由が削除できませんでした。',
	14045: '無効な変更理由Idです。',
	14046: '文書が利用できません。ページを更新して再試行してください。問題が解決しない場合は、ヘルプデスクにご連絡ください。',
	14047: '文書はすでに変更理由が割り当てられています。',
	14048: '文書が競合しています。続行する前に解決してください。',
	14049: '文書がチームまたはユーザーに追加されたかどうか確認が必要な無効なデータ',
	14050: '関連する文書の削除中のエラー',
	14052: '検索テキストが最大文字数（500文字）を超えています。',
	14053: 'リクエストがNull値であるため呼び出しができません。',
	14054: '無効なパッチ操作。',
	14055: '文書の更新履歴の解消のコールに失敗しました。',
	14056: 'メッセージのキュー中にエラーが発生しました。',
	14057: 'エンゲージメントに同じ名前の文書が存在します。',
	14058: '同じ番号の2つ以上のバージョンの文書が見つかりました。詳細についてはヘルプデスクに連絡してください。',
	14059: '選択されたバージョンの文書は見つかりませんでした。ページを更新し再試行してください。エラーが続く場合は、詳細についてヘルプデスクに連絡してください。',
	14060: '操作が完了しませんでした。再試行してください。エラーが続く場合は、詳細についてヘルプデスクに連絡してください。',
	14061: '文書が共有されておらず、文書リンクを解除できませんでした。エラーが続く場合は、詳細についてヘルプデスクに連絡してください。',
	14062: '文書の機密性タイプIDが無効です。',
	14063: 'この文書タイプの機密性タイプは変更できません。',
	14064: 'ユーザーに権限がありません。',
	14065: '無効なカスタム入力',
	14066: '拡張子をアップデートすることに失敗しました。',
	14067: '無効な拡張子',
	14068: '文書の拡張子を取得することに失敗しました。',
	14069: 'この文書は既に他のチームメンバーによりリンク解除されています。ページを更新し、エラーが続く場合は、ヘルプデスクに連絡してください。 ',
	14070: 'JA JP Invalid file type.',
	14071: 'JA JP The selected document version is no longer available. Please close and reopen this window to see the latest set of historical versions for the document.',
	14072: 'ソース文書IDはNull値または空にすることはできません。',
	14073: 'CanvasフォームIDはNull値または空にすることはできません。',
	14074: 'CanvasフォームIDは重複することはできません。',
	14075: 'キャンバスフォームに文書を関連付けることに失敗しました。',
	14076: 'Canvasフォームに関連付けられたソース文書が見つかりません。',
	14077: 'ソース文書が見つかりません。呼び出しに失敗しました。',
	14078: '現在の文書は既にこのCanvasフォームに関連付けられています。',
	14079: 'JA JP This document has been deleted and therefore it cannot be opened.',
	14080: 'JA JP The source approval user id is invalid.',
	14081: 'JA JP The source approval user id should valid GUID and must not be empty GUID.',
	14082: 'JA JP The modify user id is invalid.',
	14083: 'JA JP The modify user id should be a valid GUID and must not be empty GUID.',
	14084: 'JA JP File name cannot include: */:<>\\?|""',
	14085: 'JA JP The document name exceeded maximum length allowed.',
	14086: 'JA JP DocService failed while updating document details.',
	14087: 'JA JP The input is not valid.',
	14088: 'JA JP A input has duplicate document names.',
	14089: 'JA JP The bookmark observation is not valid.',
	14090: 'JA JP Request status has changed. Please refresh the page and try again if required. If the issue persists, contact the help desk.',
	14091: 'JA JP This request has been deleted. Refresh the page to view updated data. If the issue persists, contact the Help Desk.',
	14092: 'JA JP Document not eligible for update. Refresh the page and try again.  If the issue persists, contact the Help Desk.',

	/*SEM*/
	15001: '無効なリクエストによって、勘定科目IDに対するSEMサマリー取得のコールに失敗しました。',
	15002: '無効な勘定科目IDです。',
	15003: '無効なエンゲージメントID',
	15004: '無効なサマリータイプです。',
	15005: '関連する勘定科目が見つかりません。ページを更新して再試行してください。問題が解決しない場合は、ヘルプデスクにお問い合わせください。',

	/*Timephase*/
	16001: 'タイムフェーズの取得ができませんでした。',
	16002: 'エンゲージメントはゼロよりも大きい必要があります。',
	16003: 'タイムフェーズはゼロよりも大きい必要があります。',
	16004: '無効なタスクID値です。',
	16005: '無効なタイムフェーズ値です。',

	/*Validations*/
	17001: 'ビルドステップIDまたは文書タイプIDがありません。',
	17003: 'JA JP The document could not be found. Refresh the page and try again. If the issue persists, contact Help Desk.',

	/*TaskGroupSection*/
	18001: '全てのタスクグループセクション取得の呼び出しができませんでした。',

	/*Assignments*/
	19001: '割当ての作成の呼び出しができませんでした。',
	19002: '割当ての取得の呼び出しができませんでした。',

	/*Client Request*/
	21001: 'クライアントリクエストの関連付けの呼び出しができませんでした。',

	/*Related Components*/
	22001: '関連する構成単位取得の呼び出しができませんでした。',

	/*Component Ceation*/
	22022: '同じ構成単位名がこのエンゲージメント上に既に存在します',
	22024: 'インストラクションを送信しようとしている構成単位は使用できないか、既に削除されています。',
	22027: '期日のないグループインストラクションが見つかりました。',
	22028: '構成単位のスコープインストラクションはまだ発行されていません。',
	22029: '構成単位が送信する新しいインストラクションは発行されていません。',

	22040: 'インストラクションを送信できませんでした。エンゲージメントタイプが正しいことを確認してください。',
	22048: '更新しようとしている構成単位は、エンゲージメントのコピー中に別のエンゲージメントからコピーされています。',

	/*Send Instruction*/
	22049: 'JA JP Group instructions cannot be sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk.',

	/*User Presence*/
	23001: 'ログインしているユーザープレゼンスが見つかりません。',
	23002: 'ユーザープレゼンスの取得のコールに失敗しました。',
	23003: 'ユーザープレゼンス文書の検証に失敗しました。',
	23004: 'ユーザープレゼンス削除のコールに失敗しました。',
	23005: '文書は既に閉じられています。ページを更新して再試行してください。問題が解決しない場合は、ヘルプデスクにご連絡ください。',

	/* Forms */
	24001: 'フォームの取得の呼び出しに失敗しました。',
	24002: '無効なエンゲージメントID',
	24003: 'ドキュメントIDはNull値または空にすることはできません。',
	24005: 'フォームヘッダーが見つかりません。',
	24004: '文書が見つかりません。 ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	24006: 'JA JP Section is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24007: '無効なリクエストパラメーター。',
	24008: 'ヘッダーIDはNull値または空にすることはできません。',
	24009: 'セクションIDはNull値または空にすることはできません。',
	24010: 'フォーム本文の応答の更新操作が失敗しました。',
	24011: 'フォーム本文の回答更新についての無効なリクエストです。',
	24012: 'JA JP Body is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24013: '無効なフォーム本文のオプションIDです。',
	24014: '無効なフォーム本文のタイプIDです。',
	24015: '指定された本文タイプIDのリクエストの本文が無効です。',
	24016: '指定された本文タイプIDの自由テキストが無効です。',
	24017: '最大文字数を超過しています（リッチテキスト形式のタグを含む）。文字列を短くするか、不要な形式を削除し、再試行してください。',
	24018: '本文タイプIDへの返答は許可されていません。',
	24019: '本文IDはNull値または空にすることはできません。',
	24020: '無効なリクエストの本文です。',
	24021: '本文は削除されました。',
	24022: '国はNull値または空にすることはできません',
	24023: '言語はNull値または空にすることはできません',
	24024: 'サブサービスラインはNull値または空にすることはできません',
	24025: 'GAMレイヤーはNull値または空にすることはできません',
	24026: 'ヘッダー作成の呼び出しに失敗しました',
	24027: '無効なヘッダー作成のリクエストです',
	24028: '複製ユニット作成の呼び出しに失敗しました',
	24029: '無効なフォームユニットタイプです',
	24030: 'セクションが既に削除されています。ページを更新し再試行してください。問題が解消しない場合は、ヘルプデスクに連絡してください。',
	24031: '本文がカスタム本文ではありません',
	24032: 'ヘッダー作成リクエストが有効ではありません',
	24033: '提供された文書エンティティIDが無効です。',
	24034: '提供されたエンティティIDが無効です。',
	24035: '関連文書の作成中にエラーが生じました。',
	24036: '関連文書の削除中にエラーが生じました。',
	24037: '関連文書IDはNull又は空にできません。',
	24038: '文書は無効か存在しません。',
	24039: '関連文書が無効または存在しません。',
	24040: 'カスタム本文の作成に失敗しました。',
	24041: '本文作成リクエストが無効です。',
	24042: 'セクション作成リクエストが無効です。',
	24043: 'セクションのIDによる取得が失敗しました。',
	24044: 'セクション作成が失敗しました。',
	24045: '現在のページは無効です。',
	24046: 'ページサイズが無効です。',
	24047: '文書関連オブジェクトIDが無効です。',
	24048: 'オブジェクトはCanvasフォームに既に関連付けられています。',
	24049: 'オブジェクトが見つかりません。',
	24050: '渡されたエンティティUidは無効です。',
	24051: 'エンティティIDを指定する場合には、エンティティUidを指定する必要があり、逆も同様です。',
	24052: 'Canvasフォームスナップショットの作成に失敗しました。',
	24053: 'id呼び出しによるヘッダーの取得に失敗しました。',
	24054: 'id呼び出しによるボディの取得に失敗しました。',
	24055: 'フォームプロファイルの作成でエラーが発生しました。',
	24056: '文書フォームプロファイルは既に存在しています。',
	24057: '文書フォームプロファイルの検証に失敗しました。',
	24058: '文書フォームプロファイルが存在しません。',
	24059: 'セクションはカスタムではありません。',
	24060: '文書フォームプロファイルの検証に失敗しました。PCAOB-IAがTRUEであるならばPCAOB-FSもTRUEである必要があります。',
	24061: '文書フォームプロファイルの検証に失敗しました。PCAOB-IAがFALSEであるならばPCAOB-FSもFALSEである必要があります。',
	24062: '文書フォームプロファイルの検証に失敗しました。「複雑でない」であれば、「PCAOB-FS」/「PCAOB-IA」はFALSEである必要があります。',
	24063: '国Idが無効です。',
	24064: '言語Idが無効です。',
	24065: 'ヘッダーはカスタムではありません。',
	24066: 'フォームセクションは関連するオブジェクトの作成に失敗しました。',
	24067: 'オブジェクトが見つかりません。',
	24068: 'WCGWがSCOTに関連付けられていません。',
	24069: 'パラメーター {0}が無効です。',
	24070: '渡された親エンティティUIdは無効です。',
	24071: 'オブジェクトに関連するフォームセクションは失敗しました。',
	24072: 'セクションが利用できません。ページを更新して、再試行してください。問題が解決しない場合は、ヘルプデスクに連絡してください。',
	24073: 'スナップショットを取得できません。ページを更新して、再試行してください。問題が解決しない場合は、ヘルプデスクに連絡してください。',
	24074: '選択した文書のスナップショットが作成できません。ページを更新して、再試行してください。問題が解決しない場合は、ヘルプデスクに連絡してください。',
	24075: '無効なスナップショットID。',
	24076: 'エンティティIdはNULLまたは空になるべきではありません',
	24077: '渡されたフォームセクションに関連するオブジェクトＩＤが無効です。',
	24078: 'フォームセクションに関連するオブジェクトIDをNULLまたは空にすることはできません。',
	24079: '提供された親エンティティIdが無効です。',
	24080: 'フォームセクションに関連するオブジェクト：親エンティティレコードが見つかりません。',
	24081: 'オブジェクトが見つかりません。',
	24082: 'オブジェクトが見つかりません。',
	24083: 'フォームプロファイルの更新時にエラーが発生しました。',
	24084: '文書フォームプロファイルが存在しません。',
	24085: '言語idは0より大きな値にする必要があります。',
	24086: '国idは0より大きな値にする必要があります。',
	24087: '配信された文書のナレッジが更新できません。このフォームのプロファイルを変更するにはエンゲージメントプロファイルを更新してください。',
	24088: 'コンテンツを編集する権限が不足しています。エンゲージメント管理者と協議して、十分な権限を取得してください。',
	24089: 'カスタムヘッダー名が最大値を超えました（500文字）。名前を調整して、再試行してください。',
	24090: 'カスタムセクション名が最大値を超えました（500文字）。名前を調整して、再試行してください。',
	24091: 'セクションカスタムラベルが最大値を超えました（100文字）。名前を調整して、再試行してください。',
	24092: 'カスタムボディ名が最大値を超えました（500文字）。名前を調整して、再試行してください。',
	24093: 'カスタムセクション名をNULLまたは空にすることはできません。',
	24094: 'ボディ名をNULLまたは空にすることはできません。',
	24096: 'カスタムヘッダー名をNULLまたは空にすることはできません。',
	24097: '現時点では上書きを完了できません。',
	24098: 'ソースまたはターゲットの文書タイプidが無効です。',
	24099: 'ソース及びターゲットの文書ナレッジフォームidが同じではありません。',
	24100: 'ソース文書IDをnullまたは空にすることはできません。',
	24101: 'ターゲット文書IDをnullまたは空にすることはできません。',
	24103: 'ヘッダーが削除されました。ページを更新して再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	24104: 'ヘッダーの編集ができません。',
	24105: 'ボディの編集ができません。',
	24106: '更新アクションが無効です。',
	24107: '文書フォームプロファイルの検証に失敗しました。PCAOB-FSがTRUEであるならば「複雑な企業」もTRUEである必要があります。',
	24108: '文書フォームプロファイルの検証に失敗しました。PCAOB-IAがTRUEであるならばPCAOB-FSもTRUEである必要があります。',
	24110: 'コンテンツの更新が現在進行中です。コンテンツの更新が完了したら、Canvas Formのコンテンツ更新ページでこのフォームのコンテンツを手動で更新してください。',
	24111: 'マッピングとリンク解除の値が同じではありません。',
	24112: 'ソース文書は共有しないでください。',
	24114: 'エビデンスの追加に失敗しました。ページを更新して再試行してください。',
	24115: 'エビデンスの削除に失敗しました。ページを更新して再試行してください。',
	24116: 'プロファイルの提出に失敗しました。ページを更新し再試行してください。',
	24117: 'フォームに不備があります。ページを更新し再試行してください。',
	24118: '文書IDと関連文書IDとは同一にできません',
	24119: 'オブジェクトに関連する本文の編集に失敗しました。',
	24120: 'オブジェクトIDに関連する本文をnullまたは空にすることはできません。',
	24121: 'オブジェクトが見つかりません。',
	24122: '同時実行トークンがありません',
	24124: '無効なターゲット文書が見つかりました。',
	24125: 'ターゲットCanvasフォームが見つかりません。.',
	24126: 'リクエストをnullまたは空にすることはできません。',
	24127: 'EY Helixからのデータインポートが成功しませんでした。EY Helixの設定から データインポートをやり直してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	24155: 'リストアエンゲージメントについてはプロファイル提出は認められていません。',
	24164: 'テンプレートを変更するための適切な権限がありません。',
	24166: 'プロファイルの保存は許可されていません。このプロファイルではフォームを使用できません。',
	24167: '本文タイプは更新できません',
	24168: 'ユーザーIDを変更する場合は、null又は空白にしてはいけません',
	24169: 'ユーザーIDを変更できるのは、リンク済の回答の更新がfalseとされている場合のみです',
	24170: 'リクエスト内のタスクIDが無効です',
	24171: 'リクエスト内の本文 ID が重複しています',
	24172: 'セクション比較の呼び出しに失敗しました',
	24173: '文書IDを空にすることはできません。また、一度に50個を超える文書を作成することはできません',
	24174: 'ルート文書はリクエスト本文文書IDの一部である必要があります',
	24175: 'リクエスト本文のスマートエビデンスのエンティティIDが無効です',
	24176: '本文の文書IDは同じナレッジフォームIDを持つ必要があります',
	24177: '無効なセクションIDです',
	24178: '文書IDと文書IDリストの組み合わせが無効です',
	24179: '文書IDリストは50を超えることはできません',
	24180: '重要性基準値を更新できませんでした',
	24181: '無効な本文フォームID',
	24182: '無効なナレッジフォームIDまたはナレッジ本文フォームID',
	24183: 'このフォームに文書を関連付ける際にエラーが発生しました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	24184: 'このEY Canvasエンゲージメントでは、ユーザーはアクティブではありません。チームの管理からユーザーをインバイトし、再試行してください。問題が続く場合は、ヘルプデスクに連絡してください。',
	24188: 'JA JP One or more records are no longer available. Please refresh the page',

	/*Document Change Types*/
	25001: '文書の変更タイプの取得に失敗しました。',
	25002: '無効なエンゲージメントID',
	25003: '無効なリクエストです。',

	/* Manage team */
	26001: 'クライアントユーザーグループの全取得の呼び出しに失敗しました。',
	26002: 'クライアントグループの作成/ Eメールドメインの呼び出しに失敗しました。',
	26003: 'グループ名はnullにすることはできません。',
	26004: 'Eメールドメインはnullにすることはできません。',
	26005: '{0} Eメールドメインは263文字を超えることができません。',
	26006: '{0} Eメールドメインラベルは263文字を超えることができません。',
	26007: '{0} Eメールドメインの最初の部分は*または英数字を使用することができ、その他の特殊文字は使用できません。',
	26008: '{0} 最初のパートがワイルドカードの場合には、2つ以上のパートが続く必要があります。',
	26009: '{0} 最初のパートは*または英数字を使用することができ、その他ドメインパートは特殊文字は使用できません。',
	26010: 'Eメールドメインは一意である必要があります。',
	26011: '提供されたクライアントアクセスグループは有効ではありません。',
	26012: 'アクセスグループの削除中にエラーが発生しました。このグループまたはこのグループのメンバーにリクエスト又は外部タスクが割り当てられていないことを確認し再試行してください。',
	26013: 'アクセスグループは既に削除されています。ページを更新し最新のデータを取得してください。',
	26014: '少なくとも1つのEメールドメインが必要です。',
	26015: 'EY Canvas Client Portalにアクセスできません。既存メンバーの情報の更新が必要です。再試行し、問題が解決しない場合はヘルプデスクにご連絡ください。',
	26016: 'オペレーションの削除中にエラーが発生しました。',
	26017: 'データオペレーションの取得に失敗しました。',
	26018: '同時実行のため問題が生じました。アクセスグループはアクティブではなくなりました。',
	26019: '保存のオペレーションに失敗しました。',
	26020: 'アクティブユーザーが割り当てられている場合はEメールドメインは削除できません。変更は保存されていません。',

	/* TimePhaseTypes - Milestones */
	27001: 'マイルストーンの内容の取得ができませんでした。',

	/*Client Request Counts*/
	28001: '結果を受信していないクライアントリクエストの情報の呼び出しに失敗しました。',

	/*Content updates Error messages*/
	29001: 'IDによる取得が見つかりませんでした。',
	29002: 'アクションタイプが見つかりません。',
	29003: 'コンテンツIDが見つかりません。',
	29004: 'コンテンツ更新のAPIコールに失敗しました。',
	29005: 'コンテンツを更新しています。後ほど再試行してください。それでも問題が解消しない場合には、ヘルプデスクに連絡してください。',
	29006: '無効なリクエストのパラメーターです。',

	/*IT Process*/
	30001: '全てのITプロセスの取得に失敗しました。',
	30002: '全てのITプロセスの取得に失敗しました - エンゲージメントIDが有効ではありません。',
	30003: 'ITプロセス名を空にすることはできません。',
	30004: 'ITプロセス名は500文字を超えてはなりません。',
	30005: 'この操作を完了できませんでした。ページを更新して再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	30006: 'id呼び出しによるITプロセスの取得に失敗しました。',
	30007: '無効なリクエスト',
	30008: 'ITプロセスが利用できなくなりました。ページを更新して再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	30009: '文書が見つかりません。',
	30010: '削除を完了できません。ページを更新して再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	30012: '削除を完了できません。ページを更新して再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	30017: 'この操作を完了できませんでした。ページを更新して再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	30018: 'この操作を完了できませんでした。ページを更新して再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	30019: 'この操作を完了できませんでした。ページを更新して再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',

	/*Checklist*/
	31001: '全てのチェックリストの取得に失敗しました。',
	31002: 'チェックリストの作成に失敗しました。',
	31003: 'チェックリストパラメーターが無効です。',
	31004: 'エンゲージメントエラーが無効です。',
	31005: 'リクエストパラメーターエラーが無効です。',
	31006: 'IsCheckedリクエストパラメーターエラーが無効です。',

	/*Archive*/
	32001: 'エンゲージメントステータスIDが無効です。',
	32002: 'アーカイブに失敗しました。',
	32003: 'V1 ARCの呼び出しに失敗しています。ページを更新し、バリデーションを解決し、アーカイブ処理を再試行してください。問題が解決しない場合にはヘルプデスクに連絡してください。',
	32004: 'LDCのエンゲージメントステータスの更新に失敗しました。',
	32005: 'エンゲージメントキャッシュの無効化エラー',
	32006: 'コンテンツの更新を行っています。コンテンツの更新が完了するまでエンゲージメントはアーカイブできません。後で再試行し、問題が解決しない場合にはヘルプデスクに連絡してください。',
	32007: 'ArcGUIDがnullまたは空欄です。',
	32008: 'FileGuidがnullまたは空欄です。',
	32009: 'FileStoreHostTcpがnullまたは空欄です。',
	32200: 'エンゲージメントに未解決のバリデーションがあります。ページを更新し、バリデーションを解決し、アーカイブ処理を再試行してください。問題が解決しない場合にはヘルプデスクに連絡してください。',
	32300: 'エンゲージメントに未解決のバリデーションがあります。ページを更新し、バリデーションを解決し、アーカイブ処理を再試行してください。問題が解決しない場合にはヘルプデスクに連絡してください。',

	/*RBAC*/
	33001: 'エンゲージメントIDが見つかりませんでした。',
	33002: 'ユーザーIDが見つかりませんでした。',

	/*Helix Linked Projects*/
	34001: 'リクエストがNull値であるため呼び出しができません。',
	34002: 'エンゲージメントIDはnullまたは空欄ではない必要があります。',
	34003: 'この呼び出しでは本文はNull値又は空欄にできません。',
	34004: 'プロジェクトIDはnullまたは空欄ではない必要があります。',
	34005: 'プロジェクト名はnullまたは空欄ではない必要があります。',
	34006: 'プロジェクトは既にリンクされています。ページを更新し再試行してください。',
	34007: '全てのHelixプロジェクトの呼び出しに失敗しました。',
	34008: 'エンゲージメントIDはゼロより大きい必要があります。',
	34010: '保存を完了できませんでした。 ページを更新して、再試行してください。 問題が解決しない場合は、ヘルプデスクに連絡してください。 ',
	34009: 'プロジェクトIDが変更されました。ページを更新し再試行してください。',
	34011: '呼び出しで通貨タイプをNullにすることはできません。',
	34012: '呼び出しで通貨コードをNullにすることはできません。',
	34013: '呼び出しでビジネスユニットをNullにすることはできません。',
	34014: 'リンクされたEY Helixプロジェクトが変更され、設定を更新できません。',
	34017: 'EY Helixに接続できませんでした。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	34018: '選択された操作を完了できませんでした。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	34019: 'インポートは完了しましたが、データが無効です。ページを更新し再試行してください。',
	34027: 'Helixプロジェクトが進行中です。',
	34036: 'EY Helixに接続できませんでした。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	34039: 'このEY Helixプロジェクトはエンゲージメントのプライマリープロジェクトではなくなりました。ページを更新し再試行してください。',
	34040: 'このアクションを実行するには、<b>EY Helixインポート</b>権限が必要です。アクセス権を取得するには、エンゲージメント管理者に連絡してください。',

	/* PAANS */
	35001: 'EYポリシーの作成、承認、通知サービスが利用できません。関連するEYポリシーの完了を確認できません。関連するポリシーをレビューしていない場合には、ページを更新し再試行してください。問題が解決しない場合には、ヘルプデスクに連絡してください。 ',

	/*Engagement Comments*/
	38001: 'エンゲージメントコメントの作成に失敗しました。',
	38002: '全てのエンゲージメントコメントの取得に失敗しました。',
	38003: 'このアクションは完了できません。ページを更新し、再試行してください。エラーが解消しない場合、ヘルプデスクにコンタクトしてください。',
	38004: 'このアクションは完了できません。ページを更新し、再試行してください。エラーが解消しない場合、ヘルプデスクにコンタクトしてください。',
	38005: 'このアクションは完了できません。ページを更新し、再試行してください。エラーが解消しない場合、ヘルプデスクにコンタクトしてください。',
	38006: 'このアクションは完了できません。ページを更新し、再試行してください。エラーが解消しない場合、ヘルプデスクにコンタクトしてください。',
	38007: 'このアクションは完了できません。ページを更新し、再試行してください。エラーが解消しない場合、ヘルプデスクにコンタクトしてください。',
	38008: 'このアクションは完了できません。ページを更新し、再試行してください。エラーが解消しない場合、ヘルプデスクにコンタクトしてください。',
	38009: 'このアクションは完了できません。ページを更新し、再試行してください。エラーが解消しない場合、ヘルプデスクにコンタクトしてください。',
	38010: '本文をNull値にできません。',
	38011: 'コメントテキストはnull値または空欄にできません。',
	38012: 'エンティティは指定されたエンゲージメントには存在しません。',
	38013: 'エンゲージメントコメントステータスIdは0より大きな値にする必要があります。',
	38014: '親エンゲージメントコメントIdは0より大きな値にする必要があります。',
	38015: '指定された条件に一致するcanvasフォームの本文がありませんでした。',
	38016: '回答しようとしているレビューノートは他のチームメンバーによって削除済みです。ページを更新し再試行してください',
	38017: '提供された親エンゲージメントコメントはクリアされます。',
	38018: '提供された親エンゲージメントコメント自体が返信です。',
	38019: '提供されたコメントテキストは1文字以上4000文字以内にしてください。',
	38020: '提供されたエンティティuidは無効です。',
	38021: '提供された親エンティティuidは無効です。',
	38022: 'エンゲージメントコメントの削除に失敗しました。',
	38023: 'コメントidは空欄にできません。',
	38024: 'アクションは有効なアクションである必要があります。',
	38025: '既に存在しないコメントを削除しようとしています。ページを更新し再試行してください。',
	38026: 'コメントの所有者ではありません。',
	38027: '更新するコメントは見つかりませんでした',
	38028: 'クリア済コメントの更新はできません。',
	38029: 'コメントのテキストを変更できるのは作成者だけです。',
	38030: 'エンゲージメントコメントIdが無効です。',
	38031: 'コメントステータスIdは空欄にできません。',
	38032: 'リクエストされたユーザーとエンゲージメントの間に関係する記録はありませんでした。ページを更新し再試行してください。エラーが解消しない場合には、ヘルプデスクに連絡してください。',
	38033: '更新アクションが無効です。',
	38034: '要求されたコメントIdは既に別のコメントで使用されています。',
	38035: '提供されたコメントIdが無効です。',
	38036: '文書IdはNull値または空欄にできません。',
	38037: 'コメントが割り当てられるユーザーのIdはNull値にできません。',
	38038: 'コメントを開いたり閉じたりするときに、コメントを再割当てしないでください。',
	38039: '回答を削除しようとしているノートは削除済みです。ページを更新し再試行してください',
	38040: '返答には期日は不要です。',
	38041: '返答には優先度は不要です。',
	38042: 'コメントには期日が必要です。',
	38043: 'コメントには優先度が必要です。',
	38044: '存在しないコメントへの返答を編集しようとしています。ページを更新し再試行してください。',
	38045: 'エンゲージメントステータスIDとユーザーまたは優先度に割当てられたコンテンツの両方を編集することはできません。',
	38046: 'エンゲージメントコメントの更新に失敗しました。',
	38047: 'オープンなレビューコメントのみ返信可能です',
	39004: 'JA JP This tag group is no longer available. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*Risk-Estimate*/
	4064: 'このリスクは利用できなくなりました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	4065: 'リスクの関連付けは編集できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	4066: 'この見積りは利用できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',

	/*IT App/SO*/
	81001: '全てのITアプリケーションの取得に失敗しました。',
	81002: '全てのITアプリケーションの取得 - 無効なエンゲージメントid。',
	81003: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81004: '操作を完了できません。ページを更新して再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	81005: '操作を完了できません。ページを更新して再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	81006: '無効な戦略タイプ',
	81007: '無効な文書Id。',
	81008: 'IDによるITアプリケーションの取得に失敗しました。',
	81009: '操作を完了できません。ページを更新して再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	81010: '操作を完了できません。ページを更新して再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	81011: '操作を完了できません。ページを更新して再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	81012: 'ITアプリケーションIDをnullまたは空にすることはできません。',
	81013: '関連付けは編集できません。ページを更新して再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	81014: 'ITアプリケーションITプロセスの削除の呼び出しに失敗しました。',
	81015: 'ITアプリケーションITプロセスIdを空にすることはできません。',
	81016: '無効なITアプリケーションITプロセスId.',
	81017: '関連付けは編集できません。ページを更新して再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	81018: 'ITアプリケーションITプロセスの作成の呼び出しに失敗しました。',
	81019: 'ITアプリケーションの代わりに渡されたサービス受託会社',
	81020: '削除を完了できません。ページを更新して再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	81039: 'JA JP Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81041: 'JA JP Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/* ITControl */
	84001: 'IT統制の取得に失敗しました。',
	84002: 'idによるIT統制の取得に失敗しました。',
	84003: 'IT統制Idがnullまたは空です。',
	84004: '無効なIT統制Id.',
	84005: '文書Idが無効です。',
	84006: 'IT統制名がありません。',
	84007: 'IT統制名の長さが無効です。',
	84008: 'IT統制の頻度Idが無効です。',
	84009: 'IT統制アプローチIdが無効です。',
	84010: 'IT統制のデザインの有効性タイプIdが無効です。',
	84011: 'IT統制テストの値が無効です。',
	84012: 'IT統制運用状況の有効性タイプIdが無効です。',
	84013: 'IT統制の頻度がありません。',
	84014: 'IT統制の削除に失敗しました。',
	84015: '検索文字数は最大100字としてください。',
	84016: '操作を完了できません。ページを更新して再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	84018: '操作を完了できません。ページを更新して再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	84019: 'アップデートを完了できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',

	/*ITRisk*/
	86001: 'ITリスク名はブランクにできません。',
	86002: 'ITリスク名は500文字以下にしてください。',
	86003: 'この操作を完了できませんでした。ページを更新して再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	86004: 'IDによるITリスクの取得に失敗しました。',
	86005: 'この操作を完了できませんでした。ページを更新して再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	86006: '関連付けを完了できません。ページを更新して再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	86007: 'この操作を完了できませんでした。ページを更新して再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	86008: '関連付けを完了できません。ページを更新して再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	86009: 'この操作を完了できませんでした。ページを更新して再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	86010: 'テクノロジーリスクの削除に失敗しました。',

	/*RiskFactorFormHeaders*/
	89001: 'リスク要因の関係が見つかりません。',
	89002: '文書が見つかりません。',
	89003: '文書idがありません。',
	89004: '理由の長さが4000文字を超えています。',
	89005: 'リスクは選択されたリスク要因に関連付けることができませんでした。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	89014: 'リスクidが無効です。',
	89020: 'リスク要因は保存できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',

	/*Materiality*/
	91001: '重要性がありません',
	91002: 'データが保存できません。ページを更新し再試行してください。問題が続く場合は、ヘルプデスクに連絡してください。',
	91003: '重要性の更新リクエストが無効です',
	91004: '年間相当額の入力が不足しています',
	91005: 'IsAnnualizedAmountExpectedToBeReportedをtrueに更新する場合、BasisForecastAmountを指定する必要があります。',
	91006: '見込基礎値は15桁を超えたり、小数点を含めてはいけません',
	91007: 'IsAnnualizedAmountExpectedToBeReportedをtrueに更新する場合、BasisForecastAmountRationaleはnullである必要があります。',
	91008: '基礎となった予測値の説明が短すぎる又は長すぎます',
	91009: '無効な重要性ID',
	91010: '無効な重要性本文タイプID',
	91011: '金額は範囲の上限を超えることはできません',
	91012: '重要性基準値は15桁以上又は小数点以下4桁となることはできません。',

	/*Group Structure - Sub Scopes */
	92013: '少なくとも1つの関連するリージョン/構成単位チームが存在するため、サブスコープを削除できません。',
	92016: 'サブスコープ名はすでに存在します',

	/*Helix Account Mappings */
	94001: '勘定科目のマッピングが保存できませんでした。ページを更新し再試行してください。問題が続く場合は、ヘルプデスクに連絡してください。',
	94004: '選択された1つ以上のEY Canvasの勘定科目は削除済でありマッピングできませんでした。ページを更新し再試行してください。',
	94005: 'プロジェクトを再開できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',

	/*Group Instructions */
	98001: 'グループインストラクションの取得中にエラーが発生しました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	98002: '1つ以上のナレッジインストラクションのセクションIDが無効です。',
	98003: 'グループインストラクションの作成中にエラーが発生しました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	98004: 'インストラクション名は空にできません。',
	98005: 'インストラクション名は500字を超えることはできません。',
	98006: 'インストラクションの説明は空にできません。',
	98007: 'インストラクションの説明は30,000字を超えることはできません。',
	98009: 'インストラクション名は重複できません。',
	98010: '期日を空にすることはできません。',
	98011: 'インストラクションは既に削除されています。',
	98012: 'インストラクションが見つかりません。',
	98013: 'インストラクションIDは0より大きな値にする必要があります。',
	98014: 'グループインストラクションの保存中にエラーが発生しました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	98015: 'インストラクション必須スコープは削除できません',
	98016: 'インストラクションは削除されました。',
	98017: 'グループタスクは既に削除されています。',
	98018: '削除後にグループタスクを編集してください。',
	98019: 'エンティティが見つかりません。',

	98020: 'リスク評価パッケージを生成できません。ページを更新し再試行してください。問題が続く場合は、ヘルプデスクに連絡してください。',

	98021: '文書名を空にすることはできません。',
	98022: '文書名は最大115文字です。',
	98023: '文書名に無効なXML文字を使用することはできません。',
	98024: 'パッケージ作成プロセスが進行中です。完了するまでに最大10分かかる場合があります',
	98025: '構成単位と関連付けられていないインストラクションがあります。構成単位にインストラクションを割当ててから、グループリスク評価コミュニケーションを再度作成してください。',
	98026: '勘定科目と関連付けられていない構成単位があります。勘定科目と構成単位を関連付けてから、グループリスク評価コミュニケーションを再度作成してください。',
	98027: '勘定科目文書の作成されていない勘定科目があります。勘定科目文書を作成し、グループリスク評価コミュニケーションを再度作成してください。',
	98028: 'このCanvasフォームは、有効なタスクに関連付けられていません。',
	98029: '1つ以上の構成単位が参照専用です。',
	98030: '1つ以上の構成単位が、このプライマリチームからのものではありません。',
	98031: '1つ以上のスコープが、このプライマリチームからのものではありません。',
	98032: '文書数が制限を超えました。',
	98033: '必須のスコープはインストラクションから削除できません。',

	/*Estimate */
	115017: '見積りが見つかりません',

	/* Group Audit */
	106003: 'エラーが発生しました。ページを更新し再試行してください。',

	/* TasksOverview */
	117001: '全タスク概要の取得の呼び出しに失敗しました。',
	117002: '全タスク概要の取得リクエストを空にすることはできません。',
	117003: '無効なエンゲージメントID。',
	117004: '無効なタスクカテゴリーの値',
	117005: '無効な表示の値',
	117006: '無効な文書カテゴリー検索の値',
	117007: 'タスクカテゴリーIDの重複。',
	117008: 'タイムフェーズIDの重複。',
	117009: 'タスクIDの重複。',
	117010: '文書IDの重複。',
	117011: '割当先ユーザーIDの重複。',

	/* Multientity */
	114001: '全てのマルチエンティティの取得に失敗しました。',
	114002: 'STエンティティ名を空白にすることはできません。',
	114003: 'STエンティティ名は500文字を超えることはできません。',
	114004: 'ST法人名を空白にすることはできません。',
	114005: 'ST法人名は500文字を超えることはできません。',
	114006: 'マルチエンティティは、MESTエンゲージメントでのみ作成できます。',
	114007: 'マルチエンティティアカウントの作成画面の呼び出しに失敗しました。',
	114008: '選択した STEntity が削除されました。このモーダルを閉じると、更新されたリストが表示されます。',
	114009: 'アカウントIDが無効です。',
	114010: 'STエンティティ略称は100文字を超えることはできません。',
	114011: 'STエンティティのプロファイル送信リクエストが無効です。',
	114012: 'STエンティティのプロファイル送信リクエストの本文が無効です。',
	114013: 'STエンティティのプロファイル送信リクエストの本文には、個別のSTエンティティIDが必要です。',
	114014: 'プロファイルリクエストに関するSTエンティティIDの提出は無効です。',
	114015: 'フォームに不備があります。ページを更新し再試行してください。',
	114016: 'このコンテンツの更新は無効です。',
	114017: 'エンゲージメントにマルチエンティティの個別プロファイル文書がありません。',
	114018: 'STエンティティIDにマルチエンティティの個別のプロファイルがありません。',
	114019: 'マッピングしている1つ以上のエンティティがエンゲージメントに存在しなくなりました。 ページを更新し再試行してください。',
	114020: 'STエンティティ略称を空白にすることはできません。',
	114021: '無効な文書ID。',
	114022: '無効なエンゲージメントID。',
	114023: 'STエンティティ文書レコードは既に存在します。',
	114024: 'STエンティティ文書レコードが存在しません。',
	114025: 'STエンティティ文書レコードはシステムに関連付けられています。',
	114026: '無効なリクエストの本文です。',
	114028: '各エンティティに、1つのプロファイル文書がありません。',
	114035: 'STエンティティと勘定科目の関連付けは既に存在しています。',
	114036: '全てのエンティティの更新をリクエストする場合、少なくとも 1つのプロファイル文書が有効である必要があります。',
	114037: 'STエンティティと勘定科目の関連付けは既に削除されています。',
	114038: 'JA JP Get all MultiEntity layers failed.',
	114039: 'JA JP Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',
	114040: 'JA JP Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',

	/* Sample List */
	121101: '無効なサンプルリストID。',
	121008: '無効なサンプルリストID。',
	121011: 'このサンプルは利用できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	121013: 'このサンプルは、このエンゲージメントでは使用できなくなりました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	121016: 'このサンプルは利用できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	121037: 'このアクションを実行中にエラーが発生しました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	121012: '属性のステータスは更新できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	121025: 'この文書は利用できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	121027: '文書の関連付けは編集できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	121028: '文書の関連付けは編集できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	121014: '属性のステータスは更新できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	121029: 'この属性は利用できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	121021: '属性のステータスは更新できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',

	/*Control Attributes */
	122018: 'アクションを完了できませんでした。ページを更新して再試行してください。問題が解消しない場合には、ヘルプデスクに連絡してください。',
	122021: 'このアクションを実行中にエラーが発生しました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',

	/*Flow chart*/
	123031: '本文フォームIDを複数のフローチャートにリンクすることはできません。',

	1034: 'JA JP This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	1035: 'JA JP This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	/*Group Instructions */
	196033: '割当て項目の無いインストラクションを設定することはできません。少なくとも1つは設定する必要があります。',

	/*Information Security */
	200001: '実行されたアクションはEY情報セキュリティによって禁止されています。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。このアクションはEY Information Securityによって禁止されました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。 ',

	/*Tags */
	40007: 'このタグは利用できなくなりました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
	40029: 'タグの関連付けは編集できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
};

export const roleForMember = [{
	id: 1,
	role: '筆頭業務執行社員',
	roleAbbreviation: 'PIC'
},
{
	id: 2,
	role: '業務執行社員',
	roleAbbreviation: 'EP'
},
{
	id: 3,
	role: 'エグゼクティブディレクター',
	roleAbbreviation: 'ED'
},
{
	id: 4,
	role: 'プリンシパル',
	roleAbbreviation: 'プリンシパル'
},
{
	id: 5,
	role: 'シニアマネージャー',
	roleAbbreviation: 'シニアマネージャー'
},
{
	id: 6,
	role: 'マネージャー',
	roleAbbreviation: 'マネージャー'
},
{
	id: 7,
	role: 'シニア',
	roleAbbreviation: 'シニア'
},
{
	id: 8,
	role: 'スタッフ',
	roleAbbreviation: 'スタッフ'
},
{
	id: 9,
	role: 'インターン',
	roleAbbreviation: 'Intrn'
},
{
	id: 10,
	role: '独立審査担当社員',
	roleAbbreviation: 'EQR'
},
{
	id: 11,
	role: 'その他のパートナー',
	roleAbbreviation: '他のパートナー'
},
{
	id: 12,
	role: 'GDS - スタッフ',
	roleAbbreviation: 'GDSスタッフ'
},
{
	id: 13,
	role: 'アドバイザリー (ITRA,TAS,Human Capitalまたはその他)',
	roleAbbreviation: 'ADV'
},
{
	id: 14,
	role: 'タックス',
	roleAbbreviation: 'Tax'
},
{
	id: 15,
	role: 'エグゼクティブサポートサービス',
	roleAbbreviation: 'ESS'
},
{
	id: 16,
	role: '法務',
	roleAbbreviation: 'GCO'
},
{
	id: 17,
	role: 'AQRレビューアー',
	roleAbbreviation: 'AQR'
},
{
	id: 18,
	role: 'ML 構成単位チーム',
	roleAbbreviation: 'MCT'
},
{
	id: 19,
	role: 'クライアントスーパーバイザー',
	roleAbbreviation: 'C.スーパーバイザー'
},
{
	id: 20,
	role: 'クライアントスタッフ',
	roleAbbreviation: 'C.スタッフ'
},
{
	id: 21,
	role: '内部監査スーパーバイザー',
	roleAbbreviation: 'IAスーパーバイザー'
},
{
	id: 22,
	role: '内部監査スタッフ',
	roleAbbreviation: 'IAスタッフ'
},
{
	id: 23,
	role: '規制当局',
	roleAbbreviation: '規制当局'
},
{
	id: 24,
	role: 'その他 (例：デューデリジェンスレビュー)',
	roleAbbreviation: 'その他'
},
{
	id: 25,
	role: 'オフィス',
	roleAbbreviation: 'オフィス'
},
{
	id: 26,
	role: 'エリア',
	roleAbbreviation: 'Area'
},
{
	id: 27,
	role: 'インダストリー',
	roleAbbreviation: 'IND'
},
{
	id: 28,
	role: '国',
	roleAbbreviation: 'NAT'
},
{
	id: 29,
	role: 'グローバル',
	roleAbbreviation: 'GBL'
},
{
	id: 30,
	role: 'GDS - シニア',
	roleAbbreviation: 'GDSシニア'
},
{
	id: 31,
	role: 'GDS - マネージャー',
	roleAbbreviation: 'GDSマネージャー'
}
];

export const accountConclusionTabs = {
	conclusions: '結論'
};

export const assertions = [{
	id: 1,
	assertionname: '網羅性',
	assertionabbreviation: 'C',
	statementtypeid: 2,
	displayorder: 1
},
{
	id: 2,
	assertionname: '実在性',
	assertionabbreviation: 'E',
	statementtypeid: 2,
	displayorder: 2
},
{
	id: 3,
	assertionname: '評価',
	assertionabbreviation: 'V',
	statementtypeid: 2,
	displayorder: 3
},
{
	id: 4,
	assertionname: '権利と義務',
	assertionabbreviation: 'R&O',
	statementtypeid: 2,
	displayorder: 4
},
{
	id: 5,
	assertionname: '表示及び開示',
	assertionabbreviation: 'P&D',
	statementtypeid: 2,
	displayorder: 5
},
{
	id: 6,
	assertionname: '網羅性',
	assertionabbreviation: 'C',
	statementtypeid: 1,
	displayorder: 6
},
{
	id: 7,
	assertionname: '発生',
	assertionabbreviation: 'O',
	statementtypeid: 1,
	displayorder: 7
},
{
	id: 8,
	assertionname: '測定',
	assertionabbreviation: 'M',
	statementtypeid: 1,
	displayorder: 8
},
{
	id: 9,
	assertionname: '表示及び開示',
	assertionabbreviation: 'P&D',
	statementtypeid: 1,
	displayorder: 9
},
{
	id: 10,
	assertionname: '網羅性',
	assertionabbreviation: 'C',
	statementtypeid: 3,
	displayorder: 10
},
{
	id: 11,
	assertionname: '実在性/発生',
	assertionabbreviation: 'E/O',
	statementtypeid: 3,
	displayorder: 11
},
{
	id: 12,
	assertionname: '測定/評価',
	assertionabbreviation: 'M/V',
	statementtypeid: 3,
	displayorder: 12
},
{
	id: 13,
	assertionname: '権利と義務',
	assertionabbreviation: 'R&O',
	statementtypeid: 3,
	displayorder: 13
},
{
	id: 14,
	assertionname: '表示及び開示',
	assertionabbreviation: 'P&D',
	statementtypeid: 3,
	displayorder: 14
}
];

export const documentChangeTypesOptions = [{
	value: 1,
	label: '事務的ではない変更'
},
{
	value: 2,
	label: '変更履歴の承諾'
},
{
	value: 3,
	label: '既に存在する監査証拠へのクロスリファレンスの追加'
},
{
	value: 4,
	label: 'FAXまたはEメールで以前に受領した確認回答原文の追加'
},
{
	value: 5,
	label: '形式的な編集'
},
{
	value: 6,
	label: 'フォームAPとSubstantial role評価の完了'
},
{
	value: 7,
	label: '差し替えによる文書の削除'
},
{
	value: 8,
	label: 'マネジメントレターの作成'
},
{
	value: 9,
	label: 'アーカイブプロセスに関する完了チェックリストへのサインオフ'
},
{
	value: 10,
	label: '最終文書との照合及びクロスリファレンス',
},
{
	value: 12,
	label: 'MESTのみ：EY Canvas登録日以外の決算日を有する企業に係るエビデンスの修正'
},
{
	value: 11,
	label: 'ローカルタイムゾーンに修正された場合は、監査報告書日以前の変更になります(Americasのみ)',
}
];

export const KnowledgeFormProfileAnswer = [{
	value: 1,
	label: '複雑',
	display: true
},
{
	value: 2,
	label: '複雑でない',
	display: true
},
{
	value: 3,
	label: '上場',
	display: true
},
{
	value: 4,
	label: '非上場',
	display: false
},
{
	value: 5,
	label: 'PCAOB - IA',
	display: true
},
{
	value: 6,
	label: 'PCAOB - FS',
	display: false
},
{
	value: 7,
	label: 'PCAOB - FS',
	display: true
},
{
	value: 8,
	label: 'Non-PCAOB-FS',
	display: false
},
{
	value: 9,
	label: 'グループ監査',
	display: true
},
{
	value: 10,
	label: 'グループ監査以外',
	display: false
},
{
	value: 11,
	label: 'Digital',
	display: true
},
{
	value: 12,
	label: 'Core',
	display: true
}
];

export const strategyType = [{
	StrategyTypeId: 1,
	StrategyTypeName: '統制'
},
{
	StrategyTypeId: 2,
	StrategyTypeName: '実証'
}
];

export const controlTypeName = {
	1: 'アプリケーション',
	2: 'IT依存手作業統制',
	3: '手作業防止',
	4: '手作業発見'
};

export const inCompleteList = [{
	value: 1,
	label: '未完了',
	title: '未完了'
}];

export const scotTypes = [{
	value: 1,
	label: '経常',
	title: '経常',
	isDisabled: false
},
{
	value: 2,
	label: '非経常',
	title: '非経常',
	isDisabled: false
},
{
	value: 3,
	label: '見積',
	title: '見積',
	isDisabled: false
}
];

export const scotTypesNew = [{
	value: 1,
	label: '経常',
	title: '経常',
	isDisabled: false
},
{
	value: 2,
	label: '非経常',
	title: '非経常',
	isDisabled: false
},
{
	value: 3,
	label: '見積',
	title: '見積',
	isDisabled: false
},
{
	value: 4,
	label: 'FSCP',
	title: 'FSCP',
	isDisabled: false
}
];

export const estimationTypes = [{
	value: 1,
	label: 'いいえ',
	title: 'いいえ',
	isDisabled: false
},
{
	value: 2,
	label: 'はい',
	title: 'はい',
	isDisabled: false
}
];

/* IT Control */
export const itApproachType = [{
	ITApproachTypeId: 1,
	ITApproachTypeName: '統制'
},
{
	ITApproachTypeId: 2,
	ITApproachTypeName: '実証'
}
];

/*Controls */
export const controlFrequencyType = [{
	value: 1,
	label: '毎日何度も',
	title: '毎日何度も'
},
{
	value: 2,
	label: '日次',
	title: '日次'
},
{
	value: 3,
	label: '週次',
	title: '週次'
},
{
	value: 4,
	label: '月次',
	title: '月次'
},
{
	value: 5,
	label: '四半期毎',
	title: '四半期毎'
},
{
	value: 6,
	label: '年次',
	title: '年次'
},
{
	value: 8,
	label: 'その他',
	title: 'その他'
}
];

export const controlTypes = [{
	value: 1,
	label: 'ITアプリケーション統制'
},
{
	value: 2,
	label: 'IT依存手作業統制'
},
{
	value: 3,
	label: '手作業防止'
},
{
	value: 4,
	label: '手作業発見'
}
];

export const strategyTypeCheck = {
	1: '統制',
	2: '実証'
};

export const designEffectivenessType = [{
	DesignEffectivenessTypeId: 1,
	DesignEffectivenessTypeName: '有効'
},
{
	DesignEffectivenessTypeId: 2,
	DesignEffectivenessTypeName: '非有効'
}
];

export const controlEffectivenessType = [{
	ControlEffectivenessTypeId: 1,
	ControlEffectivenessTypeName: '有効'
},
{
	ControlEffectivenessTypeId: 2,
	ControlEffectivenessTypeName: '非有効'
}
];

export const testing = [{
	testingId: 1,
	testingDescription: 'はい'
},
{
	testingId: 2,
	testingDescription: 'いいえ'
}
];

export const controlType = [{
	value: 1,
	label: 'ITアプリケーション統制',
	title: 'ITアプリケーション統制'
},
{
	value: 2,
	label: 'IT依存手作業統制',
	title: 'IT依存手作業統制'
},
{
	value: 3,
	label: '手作業防止',
	title: '手作業防止'
},
{
	value: 4,
	label: '手作業発見',
	title: '手作業発見'
}
];

export const aggregateITEvaluationType = [{
	value: 1,
	label: 'サポートする',
	title: 'サポートする'
},
{
	value: 2,
	label: 'サポートしない',
	title: 'サポートしない'
},
{
	value: 3,
	label: 'FS & ICFRをサポートする',
	title: 'FS & ICFRをサポートする'
},
{
	value: 4,
	label: 'FSのみサポートする',
	title: 'FSのみサポートする'
}
];

export const KnowledgeFormProfileQuestion = [{
	value: 1,
	label: '複雑'
},
{
	value: 2,
	label: '上場'
},
{
	value: 3,
	label: 'PCAOB - IA'
},
{
	value: 4,
	label: 'PCAOB - FS'
},
{
	value: 5,
	label: 'ロケーション'
},
{
	value: 6,
	label: '言語'
},
{
	value: 7,
	label: 'グループ監査'
},
{
	value: 8,
	label: 'JA JP Digital'
}
];

export const KnowledgeLanguage = [{
	value: 1,
	label: '英語'
},
{
	value: 2,
	label: 'スペイン語（ラテンアメリカ）'
},
{
	value: 3,
	label: 'フランス語（カナダ）'
},
{
	value: 4,
	label: 'オランダ語'
},
{
	value: 5,
	label: 'クロアチア語'
},
{
	value: 6,
	label: 'チェコ語'
},
{
	value: 7,
	label: 'デンマーク語'
},
{
	value: 8,
	label: 'フィンランド語'
},
{
	value: 9,
	label: 'ドイツ語（ドイツ、オーストリア）',
},
{
	value: 10,
	label: 'ハンガリー語'
},
{
	value: 11,
	label: 'イタリア語'
},
{
	value: 12,
	label: '日本語（日本）'
},
{
	value: 13,
	label: 'ノルウェー語（ノルウェー）'
},
{
	value: 14,
	label: 'ポーランド語'
},
{
	value: 15,
	label: 'スロバキア'
},
{
	value: 16,
	label: 'スロベニア'
},
{
	value: 17,
	label: 'スウェーデン'
},
{
	value: 18,
	label: 'アラビア語'
},
{
	value: 19,
	label: '簡体字中国語（中国）'
},
{
	value: 20,
	label: '繁体字中国語（台湾）'
},
{
	value: 21,
	label: 'ギリシア語'
},
{
	value: 22,
	label: 'ヘブライ語（イスラエル）'
},
{
	value: 23,
	label: 'インドネシア語'
},
{
	value: 24,
	label: '韓国語（韓国）'
},
{
	value: 25,
	label: 'ポルトガル語（ブラジル）'
},
{
	value: 26,
	label: 'ルーマニア語'
},
{
	value: 27,
	label: 'ロシア語（ロシア）'
},
{
	value: 28,
	label: 'タイ語'
},
{
	value: 29,
	label: 'トルコ語'
},
{
	value: 30,
	label: 'ベトナム語'
},
{
	value: 31,
	label: 'PCAOB - 英語'
},
{
	value: 32,
	label: 'PCAOB - スペイン語 (ラテンアメリカ)'
},
{
	value: 33,
	label: 'PCAOB - フランス語 (カナダ)'
},
{
	value: 34,
	label: 'PCAOB - オランダ語'
},
{
	value: 35,
	label: 'PCAOB - クロアチア語'
},
{
	value: 36,
	label: 'PCAOB - チェコ語'
},
{
	value: 37,
	label: 'PCAOB - デンマーク語'
},
{
	value: 38,
	label: 'PCAOB - フィンランド語'
},
{
	value: 39,
	label: 'PCAOB - ドイツ語 (ドイツ, オーストリア)',
},
{
	value: 40,
	label: 'PCAOB - ハンガリー語'
},
{
	value: 41,
	label: 'PCAOB - イタリア語'
},
{
	value: 42,
	label: 'PCAOB - 日本語 (日本)'
},
{
	value: 43,
	label: 'PCAOB - ノルウェー語 (ノルウェー)'
},
{
	value: 44,
	label: 'PCAOB - ポーランド語'
},
{
	value: 45,
	label: 'PCAOB - スロバキア語'
},
{
	value: 46,
	label: 'PCAOB - スロベニア語'
},
{
	value: 47,
	label: 'PCAOB - スウェーデン'
},
{
	value: 48,
	label: 'PCAOB - アラビア語'
},
{
	value: 49,
	label: 'PCAOB - 簡体字中国語 (中国)'
},
{
	value: 50,
	label: 'PCAOB - 繁体字中国語 (台湾)'
},
{
	value: 51,
	label: 'PCAOB - ギリシア語'
},
{
	value: 52,
	label: 'PCAOB - ヘブライ語 (イスラエル)'
},
{
	value: 53,
	label: 'PCAOB - インドネシア語'
},
{
	value: 54,
	label: 'PCAOB - 韓国語 (大韓民国)'
},
{
	value: 55,
	label: 'PCAOB - ポルトガル語 (ブラジル)'
},
{
	value: 56,
	label: 'PCAOB - ルーマニア語'
},
{
	value: 57,
	label: 'PCAOB - ロシア語 (ロシア）'
},
{
	value: 58,
	label: 'PCAOB - タイ語'
},
{
	value: 59,
	label: 'PCAOB - トルコ語'
},
{
	value: 60,
	label: 'PCAOB - ベトナム語'
}
];

export const KnowledgeCountry = [{
	value: 1,
	label: 'マヨット'
},
{
	value: 2,
	label: '英領ヴァージン諸島'
},
{
	value: 3,
	label: 'スペイン'
},
{
	value: 4,
	label: 'ベリーズ'
},
{
	value: 5,
	label: 'ペルー'
},

{
	value: 6,
	label: 'スロバキア'
},
{
	value: 7,
	label: 'ベネズエラ'
},
{
	value: 8,
	label: 'ノルウェー'
},
{
	value: 9,
	label: 'フォークランド諸島（マルビナス）'
},
{
	value: 10,
	label: 'モザンビーク'
},

{
	value: 11,
	label: '中華人民共和国'
},
{
	value: 12,
	label: 'スーダン'
},
{
	value: 13,
	label: 'イスラエル'
},
{
	value: 14,
	label: 'ベルギー'
},
{
	value: 15,
	label: 'サウジアラビア'
},

{
	value: 16,
	label: 'ジブラルタル'
},
{
	value: 17,
	label: 'グアム'
},
{
	value: 18,
	label: 'ノーフォーク島'
},
{
	value: 19,
	label: 'ザンビア'
},
{
	value: 20,
	label: 'レユニオン'
},

{
	value: 21,
	label: 'アゼルバイジャン'
},
{
	value: 22,
	label: 'セントヘレナ'
},
{
	value: 23,
	label: 'イラン'
},
{
	value: 24,
	label: 'モナコ公国'
},
{
	value: 25,
	label: 'サンピエール島及びミクロン島'
},

{
	value: 26,
	label: 'ニュージーランド'
},
{
	value: 27,
	label: 'クック諸島'
},
{
	value: 28,
	label: 'セントルシア'
},
{
	value: 29,
	label: 'ジンバブエ'
},
{
	value: 30,
	label: 'イラク'
},

{
	value: 31,
	label: 'トンガ'
},
{
	value: 32,
	label: '米領サモア'
},
{
	value: 33,
	label: 'モルディブ'
},
{
	value: 34,
	label: 'モロッコ'
},
{
	value: 35,
	label: '国際監査基準（ISA）'
},

{
	value: 36,
	label: 'アルバニア'
},
{
	value: 37,
	label: 'アフガニスタン'
},
{
	value: 38,
	label: 'ガンビア'
},
{
	value: 39,
	label: 'ブルキナファソ'
},
{
	value: 40,
	label: 'トケラウ'
},

{
	value: 41,
	label: 'リビア'
},
{
	value: 42,
	label: 'カナダ'
},
{
	value: 43,
	label: 'アラブ首長国連邦'
},
{
	value: 44,
	label: '朝鮮民主主義人民共和国'
},
{
	value: 45,
	label: 'モントセラト'
},

{
	value: 46,
	label: 'グリーンランド'
},
{
	value: 47,
	label: 'ルワンダ'
},
{
	value: 48,
	label: 'フィジー'
},
{
	value: 49,
	label: 'ジブチ'
},
{
	value: 50,
	label: 'ボツワナ'
},

{
	value: 51,
	label: 'クウェート'
},
{
	value: 52,
	label: 'マダガスカル'
},
{
	value: 53,
	label: 'マン島'
},
{
	value: 54,
	label: 'ハンガリー'
},
{
	value: 55,
	label: 'ナミビア'
},

{
	value: 56,
	label: 'マルタ'
},
{
	value: 57,
	label: 'ジャージー'
},
{
	value: 58,
	label: 'タイ'
},
{
	value: 59,
	label: 'セントクリストファー・ネイビス'
},
{
	value: 60,
	label: 'ブータン'
},

{
	value: 61,
	label: 'パナマ'
},
{
	value: 62,
	label: 'ソマリア'
},
{
	value: 63,
	label: 'バーレーン'
},
{
	value: 64,
	label: 'ボスニア・ヘルツェゴビナ'
},
{
	value: 65,
	label: 'フランス'
},

{
	value: 66,
	label: '大韓民国'
},
{
	value: 67,
	label: 'アイスランド'
},
{
	value: 68,
	label: 'ポルトガル'
},
{
	value: 69,
	label: 'チュニジア'
},
{
	value: 70,
	label: 'ガーナ'
},

{
	value: 71,
	label: 'カメルーン'
},
{
	value: 72,
	label: 'ギリシャ'
},
{
	value: 73,
	label: '仏領南方・南極地域'
},
{
	value: 74,
	label: 'ハード島及びマクドナルド諸島'
},
{
	value: 75,
	label: 'アンドラ'
},

{
	value: 76,
	label: 'ルクセンブルク'
},
{
	value: 77,
	label: 'サモア'
},
{
	value: 78,
	label: 'アンギラ'
},
{
	value: 79,
	label: 'オランダ'
},
{
	value: 80,
	label: 'ギニアビサウ'
},

{
	value: 81,
	label: 'ニカラグア'
},
{
	value: 82,
	label: 'パラグアイ'
},
{
	value: 83,
	label: 'アンティグア・バーブーダ'
},
{
	value: 84,
	label: '国際財務報告基準（IFRS）'
},
{
	value: 85,
	label: 'ニジェール'
},

{
	value: 86,
	label: 'エジプト'
},
{
	value: 87,
	label: 'バチカン市国'
},
{
	value: 88,
	label: 'ラトビア'
},
{
	value: 89,
	label: 'キプロス'
},
{
	value: 90,
	label: '合衆国領有小離島'
},

{
	value: 91,
	label: 'ロシア連邦'
},
{
	value: 92,
	label: 'セントビンセント及びグレナディーン諸島'
},
{
	value: 93,
	label: 'ガーンジー'
},
{
	value: 94,
	label: 'ブルンジ'
},
{
	value: 95,
	label: 'キューバ'
},

{
	value: 96,
	label: '赤道ギニア'
},
{
	value: 97,
	label: '英領インド洋地域'
},
{
	value: 98,
	label: 'スウェーデン'
},
{
	value: 99,
	label: 'ウガンダ'
},
{
	value: 100,
	label: 'マケドニア、旧ユーゴスラビア共和国'
},

{
	value: 101,
	label: 'スワジランド'
},
{
	value: 102,
	label: 'エルサルバドル'
},
{
	value: 103,
	label: 'キルギスタン'
},
{
	value: 104,
	label: 'アイルランド'
},
{
	value: 105,
	label: 'カザフスタン'
},

{
	value: 106,
	label: 'ホンジュラス'
},
{
	value: 107,
	label: 'ウルグアイ'
},
{
	value: 108,
	label: 'ジョージア'
},
{
	value: 109,
	label: 'トリニダード・トバゴ'
},
{
	value: 110,
	label: 'パレスチナ自治政府'
},

{
	value: 111,
	label: 'マルティニーク'
},
{
	value: 112,
	label: 'オーランド諸島'
},
{
	value: 113,
	label: 'フランス領ポリネシア'
},
{
	value: 114,
	label: 'コートジボワール'
},
{
	value: 115,
	label: 'モンテネグロ'
},

{
	value: 116,
	label: '南アフリカ'
},
{
	value: 117,
	label: 'サウスジョージア・サウスサンドウィッチ諸島'
},
{
	value: 118,
	label: 'イエメン'
},
{
	value: 119,
	label: '中華人民共和国香港特別行政区'
},
{
	value: 120,
	label: 'ケニア'
},

{
	value: 121,
	label: 'チャド'
},
{
	value: 122,
	label: 'コロンビア'
},
{
	value: 123,
	label: 'コスタリカ'
},
{
	value: 124,
	label: 'アンゴラ'
},
{
	value: 125,
	label: 'リトアニア'
},

{
	value: 126,
	label: 'シリア'
},
{
	value: 127,
	label: 'マレーシア'
},
{
	value: 128,
	label: 'シエラレオネ'
},
{
	value: 129,
	label: 'セルビア'
},
{
	value: 130,
	label: 'ポーランド'
},

{
	value: 131,
	label: 'スリナム'
},
{
	value: 132,
	label: 'ハイチ'
},
{
	value: 133,
	label: 'ナウル'
},
{
	value: 134,
	label: 'サントメ・プリンシペ'
},
{
	value: 135,
	label: 'スヴァールバル諸島・ヤンマイエン島'
},

{
	value: 136,
	label: 'シンガポール'
},
{
	value: 137,
	label: 'モルドバ'
},
{
	value: 138,
	label: '台湾'
},
{
	value: 139,
	label: 'セネガル'
},
{
	value: 140,
	label: 'ガボン'
},

{
	value: 141,
	label: 'メキシコ'
},
{
	value: 142,
	label: 'セーシェル'
},
{
	value: 143,
	label: 'ミクロネシア連邦'
},
{
	value: 144,
	label: 'アルジェリア'
},
{
	value: 145,
	label: 'イタリア'
},

{
	value: 146,
	label: 'サンマリノ'
},
{
	value: 147,
	label: 'リベリア'
},
{
	value: 148,
	label: 'ブラジル'
},
{
	value: 149,
	label: 'クロアチア'
},
{
	value: 150,
	label: 'フェロー諸島'
},

{
	value: 151,
	label: 'パラオ'
},
{
	value: 152,
	label: 'フィンランド'
},
{
	value: 153,
	label: 'フィリピン'
},
{
	value: 154,
	label: 'ジャマイカ'
},
{
	value: 155,
	label: 'フランス領ギアナ'
},

{
	value: 156,
	label: 'カーボベルデ'
},
{
	value: 157,
	label: 'ミャンマー'
},
{
	value: 158,
	label: 'レソト'
},
{
	value: 159,
	label: '米領ヴァージン諸島'
},
{
	value: 160,
	label: 'ケイマン諸島'
},

{
	value: 161,
	label: 'ニウエ'
},
{
	value: 162,
	label: 'トーゴ'
},
{
	value: 163,
	label: 'ベラルーシ'
},
{
	value: 164,
	label: 'ドミニカ'
},
{
	value: 165,
	label: 'インドネシア'
},

{
	value: 166,
	label: 'ウズベキスタン'
},
{
	value: 167,
	label: 'ナイジェリア'
},
{
	value: 168,
	label: 'ウォリス・フツナ'
},
{
	value: 169,
	label: 'バルバドス'
},
{
	value: 170,
	label: 'スリランカ'
},

{
	value: 171,
	label: 'イギリス'
},
{
	value: 172,
	label: 'エクアドル'
},
{
	value: 173,
	label: 'グアドループ'
},
{
	value: 174,
	label: 'ラオス'
},
{
	value: 175,
	label: 'ヨルダン'
},

{
	value: 176,
	label: 'ソロモン諸島'
},
{
	value: 177,
	label: '東ティモール'
},
{
	value: 178,
	label: 'レバノン'
},
{
	value: 179,
	label: '中央アフリカ共和国'
},
{
	value: 180,
	label: 'インド'
},

{
	value: 181,
	label: 'クリスマス島'
},
{
	value: 182,
	label: 'バヌアツ'
},
{
	value: 183,
	label: 'ブルネイ'
},
{
	value: 184,
	label: 'バングラデシュ'
},
{
	value: 185,
	label: '南極大陸'
},

{
	value: 186,
	label: 'ボリビア'
},
{
	value: 187,
	label: 'トルコ'
},
{
	value: 188,
	label: 'バハマ'
},
{
	value: 189,
	label: 'コモロ'
},
{
	value: 190,
	label: '西サハラ'
},

{
	value: 191,
	label: 'チェコ共和国'
},
{
	value: 192,
	label: 'ウクライナ'
},
{
	value: 193,
	label: 'エストニア'
},
{
	value: 194,
	label: 'ブルガリア'
},
{
	value: 195,
	label: 'モーリタニア'
},

{
	value: 196,
	label: 'コンゴ民主共和国'
},
{
	value: 197,
	label: 'リヒテンシュタイン'
},
{
	value: 198,
	label: 'ピトケアン'
},
{
	value: 199,
	label: 'デンマーク'
},
{
	value: 200,
	label: 'マーシャル諸島'
},

{
	value: 201,
	label: '日本'
},
{
	value: 202,
	label: 'オーストリア'
},
{
	value: 203,
	label: 'オマーン'
},
{
	value: 204,
	label: 'モンゴル'
},
{
	value: 205,
	label: 'タジキスタン'
},

{
	value: 206,
	label: 'スイス'
},
{
	value: 207,
	label: 'グアテマラ'
},
{
	value: 208,
	label: 'エリトリア'
},
{
	value: 209,
	label: 'ネパール'
},
{
	value: 210,
	label: 'マリ'
},

{
	value: 211,
	label: 'スロベニア'
},
{
	value: 212,
	label: '北マリアナ諸島'
},
{
	value: 213,
	label: '(該当無し)'
},
{
	value: 214,
	label: 'アルバ'
},
{
	value: 215,
	label: 'コンゴ'
},

{
	value: 216,
	label: 'カタール'
},
{
	value: 217,
	label: 'ギニア'
},
{
	value: 218,
	label: 'アメリカ合衆国'
},
{
	value: 219,
	label: 'エチオピア'
},
{
	value: 220,
	label: 'その他'
},

{
	value: 221,
	label: 'ガイアナ'
},
{
	value: 222,
	label: 'ドイツ'
},
{
	value: 223,
	label: 'バミューダ'
},
{
	value: 224,
	label: 'タークス・カイコス諸島'
},
{
	value: 225,
	label: 'オーストラリア'
},

{
	value: 226,
	label: 'キリバス'
},
{
	value: 227,
	label: 'プエルトリコ'
},
{
	value: 228,
	label: 'パキスタン'
},
{
	value: 229,
	label: 'モーリシャス'
},
{
	value: 230,
	label: 'マラウイ'
},

{
	value: 231,
	label: 'トルクメニスタン'
},
{
	value: 232,
	label: 'カンボジア'
},
{
	value: 233,
	label: 'チリ'
},
{
	value: 234,
	label: 'ニューカレドニア'
},
{
	value: 235,
	label: 'パプアニューギニア'
},

{
	value: 236,
	label: 'ブーベ島'
},
{
	value: 237,
	label: 'ツバル'
},
{
	value: 238,
	label: 'キュラソー'
},
{
	value: 239,
	label: 'ドミニカ共和国'
},
{
	value: 240,
	label: 'ベトナム'
},

{
	value: 241,
	label: 'ココス (キーリング) 諸島'
},
{
	value: 242,
	label: 'グレナダ'
},
{
	value: 243,
	label: 'タンザニア'
},
{
	value: 244,
	label: 'アルゼンチン'
},
{
	value: 245,
	label: '中華人民共和国マカオ特別行政区'
},

{
	value: 246,
	label: 'ベナン'
},
{
	value: 247,
	label: 'ルーマニア'
},
{
	value: 248,
	label: 'アルメニア'
},
{
	value: 249,
	label: 'グローバル'
},
{
	value: 250,
	label: '中小企業向けIFRS'
},

{
	value: 251,
	label: 'US GAAP'
},
{
	value: 252,
	label: '中小企業向けのAICPA財務報告フレームワーク'
},
{
	value: 253,
	label: '南スーダン'
}
];

export const pagingSvgHoverText = {
	first: '最初のページ',
	previous: '前のページ',
	next: '次のページ',
	last: '最後のページ'
};

export const priorityTypesForDropdown = [{
	value: 1,
	label: '低 (Low)',
	className: 'Low'
},
{
	value: 2,
	label: 'Medium (中)',
	className: 'Medium'
},
{
	value: 3,
	label: 'High (高)',
	className: 'High'
},
{
	value: 4,
	label: 'クリティカル（Critical)',
	className: 'Critical'
}
];

export const reviewNoteFilterTypes = [{
	value: 0,
	label: '全て'
},
{
	value: 1,
	label: 'オープン'
},
{
	value: 2,
	label: 'クリア済'
},
{
	value: 3,
	label: 'クローズ済'
}
];

export const reviewStatus = [{
	id: 1,
	name: 'オープン'
},
{
	id: 2,
	name: 'クリア済'
},
{
	id: 3,
	name: 'クローズ済'
}
];

export const reviewNoteOpenStatusOption = [{
	value: 2,
	label: 'クリア'
},
{
	value: 3,
	label: 'クローズ'
}
];

export const reviewNoteClearedStatusOption = [{
	value: 1,
	label: '再オープン'
},
{
	value: 3,
	label: 'クローズ'
}
];

export const reviewNoteBulkClearedStatusOption = [{
	value: 1,
	label: '再オープン'
},
{
	value: 2,
	label: 'クリア'
},
{
	value: 3,
	label: 'クローズ'
}
];

export const reviewNoteClosedStatusOption = [{
	value: 1,
	label: '再オープン'
},
{
	value: 4,
	label: '消去'
}
];

export const taskTypeBadge = {
	1: 'OST',
	2: 'PST',
	3: 'WT',
	4: 'TOC',
	5: 'OSP',
	6: 'PSP',
	7: 'RT',
	8: 'GT',
	9: 'PIC',
	10: 'EQR',
	11: 'PIC/EQR',
	22: 'ACT',
	23: 'UDP'
};

export const riskTypes = [{
	id: 1,
	name: '特別な検討を必要とするリスク ',
	abbrev: 'SR',
	label: '特検',
	title: '特別な検討を必要とするリスク'
},
{
	id: 2,
	name: '不正リスク',
	abbrev: 'FR',
	label: '不正',
	title: '不正リスク'
},
{
	id: 3,
	name: '重要な虚偽表示リスク',
	abbrev: 'R',
	label: '重要な虚偽表示リスク',
	title: '重要な虚偽表示リスク'
},
{
	id: 4,
	name: '極めて低いリスクの見積項目',
	abbrev: 'VLRS',
	label: '極めて低いリスクの見積項目',
	title: '極めて低いリスクの見積項目'
},
{
	id: 5,
	name: '低リスク見積項目',
	abbrev: 'LRE',
	label: '低リスク見積項目',
	title: '低リスク見積項目'
},
{
	id: 6,
	name: '高リスク見積項目',
	abbrev: 'HRE',
	label: '高リスク見積項目',
	title: '高リスク見積項目'
},
{
	id: 7,
	name: '見積り - 未選択',
	abbrev: 'ENS',
	label: '見積り - 未選択',
	title: '見積り - 未選択'
}

];

export const relatedRisksDropdownRiskTypes = [{
	id: 1,
	name: '特別な検討を必要とするリスク',
	abbrev: 'SR',
	label: '特検',
	title: '特別な検討を必要とするリスク'
},
{
	id: 2,
	name: '不正リスク',
	abbrev: 'FR',
	label: '不正',
	title: '不正リスク'
},
{
	id: 3,
	name: '重要な虚偽表示リスク',
	abbrev: 'R',
	label: '重要な虚偽表示リスク',
	title: '重要な虚偽表示リスク'
}
];

export const estimateTypes = [{
	id: 4,
	name: '極めて低いリスクの見積項目',
	abbrev: 'VLRE',
	label: '極めて低い',
	title: '極めて低いリスクの見積項目'
},
{
	id: 5,
	name: '低リスク見積項目',
	abbrev: 'LRE',
	label: '低',
	title: '低リスク見積項目'
},
{
	id: 6,
	name: '高リスク見積項目',
	abbrev: 'HRE',
	label: '高',
	title: '高リスク見積項目'
},
{
	id: 7,
	name: '見積り - 選択されていません',
	abbrev: 'NA',
	label: '選択されていません',
	title: '見積り - 選択されていません'
}
];

export const statementTypes = [{
	id: 1,
	name: '損益計算書'
},
{
	id: 2,
	name: '貸借対照表'
},
{
	id: 3,
	name: '両方'
}
];

export const RbacErrors = {
	106: 'コンテンツを更新するための権限がありません。エンゲージメント管理者と連携して、十分な権限を取得してください。'
};

export const HelixProjectValidationErrors = {
	800: 'EY Helixへアクセスされた履歴がありません。こちらへ移動してください',
	801: 'リンクされたプロジェクトへチームメンバーとしてアクセスがありません。EY Helixプロジェクトの管理者へ連絡してください。',
	901: '選択されたEY Helixプロジェクトはありません。EY Helixプロジェクトをクリックし新たなプロジェクトをリンクしてください。',
	902: '選択されたEy Helixプロジェクトは削除対象として指定されています。EY Helixプロジェクトをクリックし新たなプロジェクトをリンクしてください。',
	903: '選択されたEy Helixプロジェクトは保管のため指定されています。EY Helixプロジェクトをクリックし新たなプロジェクトをリンクしてください。',
	926: '選択されたアナライザーはありません。ページを更新し再試行してください。問題が続く場合は、ヘルプデスクに連絡してください。',
	927: '分析がリンクされたプロジェクトにおいて利用できません。EY Helixに移動しデータ及び分析プロセスのステップを完了したうえで続行してください。',
	928: 'EY Helixのアナライザーが無効もしくはありません。ページを更新し再試行してください。問題が続く場合は、ヘルプデスクに連絡してください。',
	929: 'リンクされたEY Helixプロジェクトでエラーが発生しました。データのインポートができません。'
};

export const EngagementProfileRequirementErrors = {
	108: 'エンゲージメントプロファイルが未完了です。'
};

export const IndependenceRequirementErrors = {
	103: 'エンゲージメントユーザーの独立性の確認ができません。'
};

export const strategyTypes = [{
	id: 3,
	name: 'スコープ内'
},
{
	id: 4,
	name: 'スコープ外'
}
];

export const itAppTypes = [{
	value: 0,
	label: 'ＩＴアプリケーション'
},
{
	value: 1,
	label: 'サービス受託会社'
}
];

export const confidentialityLevels = {
	[confidentialityTypes.DEFAULT]: 'デフォルト',
	[confidentialityTypes.LOW]: '低',
	[confidentialityTypes.MODERATE]: '中',
	[confidentialityTypes.HIGH]: '高'

	// This has been disabled for release 2.5, uncomment if required
	// [confidentialityTypes.CONFIDENTIAL]: '機密'
};

export const formBodyOptionRiskTypes = [{
	id: 1,
	label: '特別な検討を必要とするリスク'
},
{
	id: 2,
	label: '不正リスク'
},
{
	id: 3,
	label: '重要な虚偽表示リスク'
}
];

export const formViewTypes = [{
	value: 0,
	label: 'フォーム'
},
{
	value: 1,
	label: '変更'
},
{
	value: 2,
	label: '詳細'
}
];

export const railFilterValidations = [{
	value: 0,
	label: '全て'
},
{
	value: 1,
	label: '回答未了あり'
},
{
	value: 2,
	label: '未解決のコメント'
}
];

export const aresRiskTypes = [{
	id: 1,
	name: '特別な検討を必要とするリスク'
},
{
	id: 2,
	name: '不正リスク'
},
{
	id: 3,
	name: '重要な虚偽表示リスク'
},
{
	id: 4,
	name: '極めて低いリスクの見積項目'
},
{
	id: 5,
	name: '低リスク見積項目'
},
{
	id: 6,
	name: '高リスク見積項目'
},
{
	id: 7,
	name: '見積り - 選択されていません'
}
];

export const materialityTypes = [{
	value: 1,
	label: '税引前利益'
},
{
	value: 2,
	label: 'EBIT (Earnings before interest and taxes)'
},
{
	value: 3,
	label: 'EBITDA (Earnings before interest, taxes, depreciation, and amortization)',
},
{
	value: 4,
	label: '売上総利益'
},
{
	value: 5,
	label: '収益'
},
{
	value: 6,
	label: '営業費用'
},
{
	value: 7,
	label: '純資産'
},
{
	value: 8,
	label: '資産'
},
{
	value: 9,
	label: '事業活動を基礎とした指標(その他)'
},
{
	value: 10,
	label: '税引前損失'
},
{
	value: 11,
	label: '資本を基礎とした指標(その他)'
},
{
	value: 12,
	label: '利益を基礎とした指標(その他)'
}
];

export const helixCurrencyType = {
	[currencyType.Functional]: '機能',
	[currencyType.Reporting]: '報告'
};

export const controlRiskType = [{
	id: 1,
	name: '依拠する'
},
{
	id: 2,
	name: '依拠しない'
},
{
	id: 3,
	name: '経営管理者による手続 (MP)のテスト'
}
];

export const inherentRiskType = [{
	id: 1,
	name: '高 (Higher)'
},
{
	id: 2,
	name: '低 (Lower)'
},
{
	id: 3,
	name: '極めて低い'
}
];

export const AlraInherentRiskType = [{
	id: 3,
	name: '関連なし'
},
{
	id: 2,
	name: '低 (Lower)'
},
{
	id: 1,
	name: '高 (Higher)'
}
];

export const scotInherentRiskType = [{
	id: 1,
	name: '高'
},
{
	id: 2,
	name: '低'
},
{
	id: 3,
	name: '非経常SCOT'
}
];

export const CRAStrings = {
	Minimal: '最低 (Minimal)',
	Low: '低 (Low)',
	'Low +SC': "低 (Low) + SC",
	Moderate: '中 (Moderate)',
	High: '高 (High)',
	'High +SC': "高 (High) + SC"
};

export const priorityType = [{
	id: 1,
	name: '低 (Low)',
	className: 'Low',
	label: 'L'
},
{
	id: 2,
	name: 'Medium (中)',
	className: 'Medium',
	label: 'M'
},
{
	id: 3,
	name: 'High (高)',
	className: 'High',
	label: 'H'
},
{
	id: 4,
	name: 'クリティカル（Critical）',
	className: 'Critical',
	label: 'C'
}
];

export const kendoLabels = {
	addComment: 'コメントの追加',
	addColumnBefore: '左に列を挿入',
	addColumnAfter: '右に列を挿入',
	addInlineComment: 'インラインコメントの追加',
	addRowAbove: '上に行を追加',
	addRowBelow: '下に列を追加',
	alignLeft: '左揃え',
	alignRight: '右揃え',
	alignCenter: '中央揃え',
	alignFull: '両端揃え',
	backgroundColor: '背景色',
	bulletList: '番号なしリストの挿入',
	bold: '太字',
	backColor: 'ハイライト',
	createLink: 'ハイパーリンクの挿入',
	createTable: 'テーブルの作成',
	cleanFormatting: '書式を初期化',
	deleteRow: '行を削除',
	deleteColumn: '列を削除',
	deleteTable: '表の削除',
	fontSizeInherit: 'フォントサイズ',
	foreColor: 'フォントの色',
	format: 'フォーマット',
	fontSize: 'フォントサイズ',
	hyperlink: 'リンクを挿入',
	italic: '斜体',
	indent: 'インデント',
	insertTableHint: '{0} × {1} の表を作成',
	huge: '特大',
	'hyperlink-dialog-content-address': "Webアドレス ",
	'hyperlink-dialog-title': "ハイパーリンクの挿入",
	'hyperlink-dialog-content-title': "タイトル",
	'hyperlink-dialog-content-newwindow': "新しいウィンドウでリンクを開く",
	'hyperlink-dialog-cancel': "中止",
	'hyperlink-dialog-insert': "挿入",
	large: '大',
	noDataPlaceholder: '文字の入力',
	normal: '標準',
	orderedList: '番号付きリストの挿入',
	outdent: 'インデントを戻す',
	paragraphSize: '段落サイズ',
	print: '印刷',
	pdf: 'pdfに出力',
	redo: 'やり直し',
	removeFormatting: '書式の削除',
	strikethrough: '取り消し線',
	small: '小',
	subscript: '下付き',
	superscript: '上付き',
	underline: '下線',
	undo: '元に戻す',
	unlink: 'リンク解除'
};

export const kendoFormatOptions = [{
	text: '段落',
	value: 'p'
},
{
	text: '見出し1',
	value: 'h1'
},
{
	text: '見出し2',
	value: 'h2'
},
{
	text: '見出し3',
	value: 'h3'
},
{
	text: '見出し4',
	value: 'h4'
},
{
	text: '見出し5',
	value: 'h5'
},
{
	text: '見出し6',
	value: 'h6'
}
];

export const kendoFontSize = [{
	text: '8',
	value: '8px'
},
{
	text: '9',
	value: '9px'
},
{
	text: '10',
	value: '10px'
},
{
	text: '11',
	value: '11px'
},
{
	text: '12',
	value: '12px'
},
{
	text: '14',
	value: '14px'
},
{
	text: '16',
	value: '16px'
},
{
	text: '18',
	value: '18px'
},
{
	text: '20',
	value: '20px'
},
{
	text: '22',
	value: '22px'
},
{
	text: '24',
	value: '24px'
},
{
	text: '26',
	value: '26px'
},
{
	text: '28',
	value: '28px'
},
{
	text: '36',
	value: '36px'
},
{
	text: '48',
	value: '48px'
},
{
	text: '72',
	value: '72px'
}
];

export const ItFlowValidationLabels = {
	ITAppWithoutAtLeastOneRelatedITProcess: '関連しないITアプリケーション',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: '関連しないITGC',
	ITSPHasNorelatedITRisk: '関連しないITSP',
	ITProcessHasNoRelatedITApplication: '関連しないITプロセス',
	ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication: 'ITリスクが存在しません',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: 'ITGCが存在しません、または何もマークされていません',
	ITDMorITACWithNoRelatedITApplication: 'ＩＴアプリケーションのないアプリケーション/IT依存手作業統制',
	ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed: '削除されるITSP',
	ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed: '削除されるITGC',
	ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed: '削除されるITリスク',
	ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed: '無効なテスト戦略のITGC',
	ITGCWithoutASelectedDesignEffectiveness: 'ITGCデザインの評価がありません',
	SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero: '関連しないSCOT',
	AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed: '統制テストに対する一貫性のない回答',
	SCOTWithHasNoITApplicationHasITDMOrAppControls: 'SCOTにITアプリケーションがありません'
};

export const ISA315ITFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: ItFlowValidationLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: ItFlowValidationLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: ItFlowValidationLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: ItFlowValidationLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: ItFlowValidationLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed,
	label: ItFlowValidationLabels.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: ItFlowValidationLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
}
];

/* Notes modal labels */

export const reviewNoteModalLabels = {
	/*Review Notes*/
	engagement: 'エンゲージメント',
	emptyReplyErrorMsg: 'テキストを追加し続行',
	lengthReplyErrorMsg: '返答は4,000字を超えることはできません。',
	documentLabel: '文書',
	task: 'タスク',
	allEngagementFilterLabel: '他の全てのエンゲージメント',
	otherEngagementComments: '他のエンゲージメントノート',
	notesModalInstructionalText: '以下で選択した {0}のノートを表示して返答する',
	commentThread: 'ノートスレッド',
	singleNoteInstructionalText: '以下で選択したノートを表示して返答する',
	emptyNoteDetailsMessage: 'ノートを選択して詳細を表示します。一括操作を行うには、CtrlキーまたはShiftキーを使用して複数のノートを選択します。個別のノートで作業する場合は、リストからそのノートを選択します。',
	documentReviewNotesLabel: '文書ノート',
	addNewReviewNoteButtonText: 'ノートの追加',
	noNotesAssociatedWithDocumentLabel: '以下の入力欄を使用してコメントを残してください。ノートをユーザーに割当て、優先度と期日を指定します。',
	noNotesFound: 'ノートが見つかりません',
	noNotesAssociatedWithTaskLabel: 'タスクに関連付けられた {0} ノートがありません。',
	allNotesLabel: '全てのノート',
	charactersLabel: '文字',
	myNotesLabel: '私のノート',
	showClearedLabel: 'クリア済を表示',
	showClosedLabel: 'クローズ済を表示',
	assignedToLabel: '割当て先',

	ofLabel: 'of',
	enterNoteText: 'ノートの入力',
	addNewNoteModalClose: 'クローズ',
	addNewNoteModalTitleLabel: '新しいノートの追加',
	editNoteModalTitleLabel: 'ノートの編集',
	deleteIconHoverText: '削除',
	deleteIconModalAcceptText: '削除',
	deleteIconModalConfirmMessage: 'このノートに対する返答を削除してよろしいですか？',
	deleteIconModalConfirmMessageParent: '選択したノートを削除してよろしいですか？',
	deleteIconModalTitleLabel: 'ノートの削除',
	deleteReplyIconModalTitle: '返信の削除',
	emptyRepliesMessage: '返信未了',
	replyInputPlaceholder: 'このノートへの返答',
	replyInputPlaceholderEdit: 'ノート又はボイスノートを使用した返信の編集',
	noteInputPlaceholderEdit: 'ノート又はボイスノートを使用した編集',
	replyText: 'テキストを返信',
	editReplyModelTitle: '返信を編集',
	editReplyPlaceholder: '返信を入力',
	noteDueDateLabel: '期限',

	priorityLabel: '優先度',
	dueDateLabel: '期日',
	dueLabel: '期限',
	status: 'ステータス',
	noteModifiedDateLabel: '編集済',
	cancelLabel: 'キャンセル',
	saveLabel: '保存',
	clearedBy: 'クリア者',
	closedBy: 'クローズ者',
	reopenedBy: '再オープン者',
	reply: '返信',
	editIconHoverTextLabel: '編集',
	required: '必須',
	closeTitle: 'クローズ',
	otherEngagementNotes: '他のエンゲージメントノート',
	closeLabel: 'クローズ',
	showMore: 'さらに表示',
	showLess: '表示を減らす',
	showMoreEllipsis: 'さらに表示...',
	showLessEllipsis: '表示を減らす...',
	noResultFound: '結果が見つかりません',
	engagementNameLabel: 'エンゲージメント名:',
	taskReviewNotesLabel: 'タスクノート',
	fromUserLabel: 'From',
	toUserLabel: 'To',
	view: '表示',
	dueDateRequiredTextError: '期日は必須です'
};

export const notesFilterLabels = [{
	id: notesFilter.allNotes,
	label: '全てのノート',
	value: notesFilter.allNotes
},
{
	id: notesFilter.myNotes,
	label: '私のノート',
	value: notesFilter.myNotes
},
{
	id: notesFilter.authoredByMeNotes,
	label: '私に割当られたもの',
	value: notesFilter.authoredByMeNotes
}
];

export const reviewerAssignments = {
	taskLayoutHeaderAssignments: '割当て',
	manageAssigmentsStep2: 'タスクの割当ての編集',
	editAssignment: '割当ての編集',
	deleteAssignment: '割当ての削除',
	manageAssigmentsStep3: '割当ての完了',
	taskAssigmentStatusHeader: '割当てステータス',
	taskAssignmentName: '割当て',
	dueDateAssigment: '期限',
	editDueDate: '任意：業務終了日前の編集日付',
	teamMemberAssigmentLabel: 'チームメンバー',
	currentAssigmentLabel: '当年度',
	handOffToAssigmentLabel: 'ハンドオフ先:',
	priorToEndDateLabel: '終了日の前日',
	noTimePhaseAssigmentLabel: '割当て未了のタイムフェーズ',
	closedByAssigmentLabel: 'クローズ者',
	onAssingmentLabel: 'on',
	preparerAssigmentOpenTitleTip: 'このタスクをハンドオフしてタスクの割当てをクローズする',
	reviewerAssigmentOpenTitleTip: 'タスクの割当てをクローズ済としてマークする',
	reviewerAssigmentClosedTitleTip: 'タスクの割当てをオープンとしてマークする',
	AssigmentLabel: 'このタスクをハンドオフして、タスクの割当てを閉じます',
	timePhaseName: 'タイムフェーズ:',
	timePhaseEndDate: '終了日: ',
	AssignmentType: [{
		id: 1,
		displayName: '作成者'
	},
	{
		id: 2,
		displayName: '詳細レビューアー'
	},
	{
		id: 3,
		displayName: '一般レビューアー'
	},
	{
		id: 4,
		displayName: 'パートナー'
	},
	{
		id: 5,
		displayName: 'EQR'
	},
	{
		id: 6,
		displayName: 'その他'
	}
	],
	AssignmentStatus: [{
		id: 1,
		displayName: 'オープン'
	},
	{
		id: 2,
		displayName: '進捗中'
	},
	{
		id: 3,
		displayName: 'クローズ済'
	},
	{
		id: 4,
		displayName: '割当て未了'
	}
	],
	assignmentTableColumnHeader: '割当て',
	teamMemberTableColumnHeader: 'チームメンバー',
	dueDaysTableColumnHeader: '期限',
	daysPriorToEndDate: '最終日までの日数',
	handoffButton: 'ハンドオフ'
};
/* Notes modal labels */

export const handOffModal = {
	title: 'ハンドオフ',
	description: '次のチームメンバーに本タスクをハンドオフしてください。エビデンスファイルにサインオフするには、以下のオプションから選択してください。',
	dropdownLabel: 'ハンドオフ先',
	closeTitle: 'キャンセル',
	confirmButton: 'ハンドオフ',
	evidence: 'エビデンス',
	evidenceSignOffTitle: '全てにサインオフ',
	existingSignOffs: '既存のサインオフ',
	noDocumentsAvailable: '利用可能な文書がありません'
};

// manage scot modal labels
export const manageSCOTModal = {
	title: 'SCOTsの管理',
	description: '既存のSCOTsを作成、編集、または削除できます。保存すると変更が適用されます。',
	addSCOTLink: 'SCOTの追加'
};

export const deleteSCOTModal = {
	title: 'SCOTの削除',
	description: '次のSCOT(s)は削除されます。この操作は元に戻せません。'
};

export const manageITAppModalLabels = {
	title: 'ITアプリケーションの管理',
	description: '新しいITアプリケーションを作成するか、以下の既存のITアプリケーションを編集または削除します。',
	inputNameTitle: 'ITアプリケーション名',
	deleteConfirmMessage: 'ITアプリケーション <b>{0}</b>を削除してよろしいですか? この操作は元に戻せません。',
	addSuccessMessage: "ITアプリケーション '{0}'は正常に作成されました",
	editSuccessMessage: "編集内容はITアプリケーション '{0}'に正常に保存されました",
	deleteSuccessMessage: "'{0}' は正常に削除されました"
};

export const manageSOModalLabels = {
	title: 'サービス受託会社の管理',
	description: '新しいサービス受託会社を作成するか、以下の既存のサービス受託会社を編集または削除します。',
	inputNameTitle: 'サービス受託会社名',
	deleteConfirmMessage: 'サービス受託会社<b>{0}</b>を削除してよろしいですか？この操作は元に戻すことはできません。',
	addSuccessMessage: "サービス受託会社'{0}'の作成に成功しました。",
	editSuccessMessage: "サービス受託会社'{0}'の編集に成功しました",
	deleteSuccessMessage: "'{0}' は正常に削除されました",
    addServiceOrganization: 'サービス受託会社の追加',
	editServiceOrganization: 'サービス受託会社の編集',
	deleteServiceOrganization: 'サービス受託会社の削除'
};

export const customNameModal = {
	title: '仕訳ソース名のサフィックス',
	closeTitle: 'キャンセル',
	save: '保存',
	suffixRequired: 'サフィックスは必須です！',
	suffix: 'サフィックス',
	addSuffix: 'サフィックスの追加',
	editSuffix: 'サフィックスの編集'
};

export const GuidedWorkFlowLabels = {
	RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM: '関連付けられていない事象及び状況/重要な虚偽表示リスク',
	RisksUnrelatedToAnAssertionForGuidedWorkflow: '関連付けられていないリスク',
	IncompleteMeasurementBasisForecastAmount: '算定基礎 - 未了',
	IncompleteMeasurementBasisForecastAmountRationale: '算定基礎の根拠が必要',
	IncompleteMeasurementBasisAdjustedAmount: '調整額 - 未了',
	IncompletePlanningMateriality: 'PM - 未了',
	PlanningMaterialityGreaterThanMaximumAmount: 'PMが過大',
	IncompletePlanningMaterialityRationale: 'PMの根拠が必要',
	IncompleteTolerableError: 'TE - 未了',
	TENotWithinRangeOfAllowedValues: '無効なTEパーセンテージ',
	IncompleteTolerableErrorRationale: 'TEの根拠が必要',
	IncompleteSAD: 'SAD - 未了',
	SADGreaterThanMaximum: 'SADが過大',
	IncompleteSADRationale: 'SADの根拠が必要',
	IncompletePACESelection: 'PACE - 未了',
	AccountWithoutIndividualRiskAssessmentForm: '勘定科目{0} に文書がありません',
	EstimateWithoutIndividualEstimateForm: '見積り{0} に文書がありません',
	AccountWithoutIndividualAnalyticForm: '勘定科目{0} に文書がありません',
	MultiEntityWithoutIndividualProfileForm: 'マルチエンティティの個別プロファイル文書のないエンティティ',
	AccountAccountTypeIDDoesNotMatchAction: '一貫性のない勘定科目指定の選択',
	AccountHasEstimateDoesNotMatchAction: '不整合な会計上の見積りの選択',
	AccountFormOptionHasRelatedRisksNotAssociatedToAccount: '勘定科目と関連付けがされていないリスク',
	AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk: 'リスクと関連付けがされていない固有リスク「高」のアサーション',
	AccountMissingSubstantiveProcedure: '実証手続の関連付けがされていない勘定科目/エンティティ',
	MultiEntityNotRelatedToALLPSTACTForRelatedAccount: 'コンテンツの更新が必要な勘定科目/エンティティ',
	ComponentWithoutGroupInvolvementForm: '構成単位チームの作業に対する関与フォームのない構成単位 (参照専用の構成単位を除く)',
	ComponentWithoutRelatedGroupAssessmentInstruction: 'グループリスク評価インストラクションのない構成単位',
	IncompleteAssertionRiskLevel: '不完全なアサーションのリスクレベル',
	EstimateAccountWithoutEsimatePSPIndex: '見積りPSPインデックスなしの見積勘定科目',
	AccountExecutedWithoutRelatedComponent: 'グループ - 関連するフル/スペシフィックの構成単位がない勘定科目 (他のエンゲージメントで実施済)',
	MultiEntityAccountWithoutRelatedToAnyMultiEntity: '関連するエンティティのない勘定科目',
	ChangeNotSubmittedMultiEntityFullProfile: '変更は送信されませんでした',
	ChangeNotSubmittedMultiEntityIndividualDocument: '変更は送信されませんでした',
	AccountTypeWithMissingInformation: '情報が不足している勘定科目',
	DocumentUploadMissingRequiredPICEQRSignOffs: 'サインオフのないエビデンス',
	DocumentUploadMissingRequiredPICEQRSignOffRequirements: 'サインオフ要件がないエビデンス',
	DocumentUploadMissingPreparerOrReviewerSignOffs: '文書のアップロード - 作成者又はレビューアーのサインオフがありません',
	ITAppWithoutAtLeastOneRelatedITProcess: '関連しないITアプリケーション',
	ITProcessHasNoRelatedITApplication: '関連しないITプロセス',
	ITGCWithoutASelectedDesignEffectiveness: 'ITGCデザインの評価がありません',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: '関連しないITGC',
	ITSPHasNorelatedITRisk: '関連しないITSP',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: 'ITGCが存在しません、または何もマークされていません',
	EstimateWithoutAccountRelated: '勘定科目が関連付けられてない見積り',
	EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount: '見積りではない勘定科目に関連するリスク「高」または「低」の見積り',
	RiskEstimateRiskTypeIDDoesNotMatchAction: '見積りカテゴリの回答が「見積りの編集」の指定と一致していません',
	LowerorHigherRiskEstimateWithoutEstimateSCOT: '有効なSCOTのない見積り',
	EstimateWithoutIndividualEstimateDocument: '個別の文書がない見積り',
	EstimateAccountWithoutHigherOrLowerRiskEstimate: '有効な見積りのない勘定科目',
	EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary: 'リスク「高」または「低」の見積りと関連付けられていない見積勘定科目',
	EstimateScotWithoutHigherOrLowerRiskEstimate: 'リスク「高」または「低」の見積りと関連付けられていない見積SCOT',
	HigherRiskEstimateWithoutRisk: '関連する有効なリスクのない高リスク見積項目',
	PICEQRSignOffRequirements: 'PIC or EQR Signoff requirement does not match response',
	AdjustmentsWithoutAnyEvidence: 'エビデンスのない調整',
	AdjustmentsThatDoNotNet: '正味ではない調整',
	DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration: 'コラボレーションのためにチェックアウトされたか、またはチェックアウト中の文書',
	NonEngagementWideTasksMissingEvidence: 'エビデンスがないエンゲージメント非横断タスク',
	EstimatesMustBeMarkedHigherRisk: '高リスクではない見積りに関連する特別な検討を必要とするリスク/不正リスク',
	SCOTEstimateNoRelatedWalkthroughForm: 'ウォークスルーがないSCOT/見積り',
	SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2: '関連する勘定科目がないSCOT',
	AccountSignificantDisclosureWithNoRelatedSCOTV2: 'Account - Significant Account / Significant Disclosure that is not a CT only Account with no related SCOT or an Estimate when it is an Estimate Account',
	ITApplicationWithoutITAppRiskAssessmentIndividualDocument: 'Technology risk assessment missing document',
	ITApplicationWithoutITAppPlanningIndividualDocument: 'Technology missing document',
	FormContentWithoutHeader: 'Form Content without Header',
	RisksWithoutAnyRelatedAssertions: 'There are risks that have not been related to at least one assertion',
	AssertionsWithIncompleteCRA: 'There are assertions missing an inherent and/or control risk assessment',
	LimitedRiskOrInsignificantAccountMissingRationale: 'All limited risk and insignificant accounts shall have rationale provided',
	ITProcessWithoutWalkthroughDocument: 'ITProcess without IT process - Walkthrough - Individual',
	ITProcessIsUncategorized: 'IT Process - ITProcessTypeID is Uncategorized',
	ITProcessWithNoRelatedITApplication: 'ITProcess - ITProcess with no related IT Application'
};

export const GuidedWorkFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.RiskEstimateRiskTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.RiskEstimateRiskTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.FormContentWithoutHeader,
	label: GuidedWorkFlowLabels.FormContentWithoutHeader
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary
},
{
	validationId: validationTypes.EstimateScotWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateScotWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.HigherRiskEstimateWithoutRisk,
	label: GuidedWorkFlowLabels.HigherRiskEstimateWithoutRisk
},
{
	validationId: validationTypes.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM,
	label: GuidedWorkFlowLabels.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.RisksUnrelatedToAnAssertionForGuidedWorkflow,
	label: GuidedWorkFlowLabels.RisksUnrelatedToAnAssertionForGuidedWorkflow
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmount
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmountRationale,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmountRationale
},
{
	validationId: validationTypes.IncompleteMeasurementBasisAdjustedAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisAdjustedAmount
},
{
	validationId: validationTypes.IncompletePlanningMateriality,
	label: GuidedWorkFlowLabels.IncompletePlanningMateriality
},
{
	validationId: validationTypes.PlanningMaterialityGreaterThanMaximumAmount,
	label: GuidedWorkFlowLabels.PlanningMaterialityGreaterThanMaximumAmount
},
{
	validationId: validationTypes.IncompletePlanningMaterialityRationale,
	label: GuidedWorkFlowLabels.IncompletePlanningMaterialityRationale
},
{
	validationId: validationTypes.IncompleteTolerableError,
	label: GuidedWorkFlowLabels.IncompleteTolerableError
},
{
	validationId: validationTypes.TENotWithinRangeOfAllowedValues,
	label: GuidedWorkFlowLabels.TENotWithinRangeOfAllowedValues
},
{
	validationId: validationTypes.IncompleteTolerableErrorRationale,
	label: GuidedWorkFlowLabels.IncompleteTolerableErrorRationale
},
{
	validationId: validationTypes.IncompleteSAD,
	label: GuidedWorkFlowLabels.IncompleteSAD
},
{
	validationId: validationTypes.SADGreaterThanMaximum,
	label: GuidedWorkFlowLabels.SADGreaterThanMaximum
},
{
	validationId: validationTypes.IncompleteSADRationale,
	label: GuidedWorkFlowLabels.IncompleteSADRationale
},
{
	validationId: validationTypes.IncompletePACESelection,
	label: GuidedWorkFlowLabels.IncompletePACESelection
},
{
	validationId: validationTypes.AccountWithoutIndividualRiskAssessmentForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualRiskAssessmentForm
},
{
	validationId: validationTypes.EstimateWithoutIndividualEstimateForm,
	label: GuidedWorkFlowLabels.EstimateWithoutIndividualEstimateForm
},
{
	validationId: validationTypes.AccountWithoutIndividualAnalyticForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualAnalyticForm
},
{
	validationId: validationTypes.MultiEntityWithoutIndividualProfileForm,
	label: GuidedWorkFlowLabels.MultiEntityWithoutIndividualProfileForm
},
{
	validationId: validationTypes.AccountAccountTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountAccountTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.AccountHasEstimateDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountHasEstimateDoesNotMatchAction
},
{
	validationId: validationTypes.AccountFormOptionHasRelatedRisksNotAssociatedToAccount,
	label: GuidedWorkFlowLabels.AccountFormOptionHasRelatedRisksNotAssociatedToAccount
},
{
	validationId: validationTypes.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk,
	label: GuidedWorkFlowLabels.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk
},
{
	validationId: validationTypes.MultiEntityNotRelatedToALLPSTACTForRelatedAccount,
	label: GuidedWorkFlowLabels.MultiEntityNotRelatedToALLPSTACTForRelatedAccount
},
{
	validationId: validationTypes.AccountMissingSubstantiveProcedure,
	label: GuidedWorkFlowLabels.AccountMissingSubstantiveProcedure
},
{
	validationId: validationTypes.ComponentWithoutGroupInvolvementForm,
	label: GuidedWorkFlowLabels.ComponentWithoutGroupInvolvementForm
},
{
	validationId: validationTypes.ComponentWithoutRelatedGroupAssessmentInstruction,
	label: GuidedWorkFlowLabels.ComponentWithoutRelatedGroupAssessmentInstruction
},
{
	validationId: validationTypes.EstimateAccountWithoutEstimatePSPIndex,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutEsimatePSPIndex
},
{
	validationId: validationTypes.AssertionInherentRiskWithoutRelatedHigherRisk,
	label: GuidedWorkFlowLabels.IncompleteAssertionRiskLevel
},
{
	validationId: validationTypes.AccountGroupWithoutAComponent,
	label: GuidedWorkFlowLabels.AccountExecutedWithoutRelatedComponent
},
{
	validationId: validationTypes.MultiEntityAccountWithoutRelatedToAnyMultiEntity,
	label: GuidedWorkFlowLabels.MultiEntityAccountWithoutRelatedToAnyMultiEntity
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityFullProfile,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityFullProfile
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityIndividualDocument,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityIndividualDocument
},
{
	validationId: validationTypes.AccountWithMissingValues,
	label: GuidedWorkFlowLabels.AccountTypeWithMissingInformation
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffs
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffRequirements
},
{
	validationId: validationTypes.DocumentUploadMissingPreparerOrReviewerSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingPreparerOrReviewerSignOffs
},
{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: GuidedWorkFlowLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: GuidedWorkFlowLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: GuidedWorkFlowLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: GuidedWorkFlowLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: GuidedWorkFlowLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
},
{
	validationId: validationTypes.EstimateWithoutAccountRelated,
	label: GuidedWorkFlowLabels.EstimateWithoutAccountRelated
},
{
	validationId: validationTypes.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount,
	label: GuidedWorkFlowLabels.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount
},
{
	validationId: validationTypes.LowerorHigherRiskEstimateWithoutEstimateSCOT,
	label: GuidedWorkFlowLabels.LowerorHigherRiskEstimateWithoutEstimateSCOT
},
{
	validationId: validationTypes.PICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.PICEQRSignOffRequirements
},
{
	validationId: validationTypes.EstimatesMustBeMarkedHigherRisk,
	label: GuidedWorkFlowLabels.EstimatesMustBeMarkedHigherRisk
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.NonEngagementWideTasksMissingEvidence,
	label: GuidedWorkFlowLabels.NonEngagementWideTasksMissingEvidence
},
{
	validationId: validationTypes.AdjustmentsWithoutAnyEvidence,
	label: GuidedWorkFlowLabels.AdjustmentsWithoutAnyEvidence
},
{
	validationId: validationTypes.AdjustmentsThatDoNotNet,
	label: GuidedWorkFlowLabels.AdjustmentsThatDoNotNet
},
{
	validationId: validationTypes.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration,
	label: GuidedWorkFlowLabels.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration
},
{
	validationId: validationTypes.ITApplicationWithoutITAppRiskAssessmentIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppRiskAssessmentIndividualDocument
},
{
	validationId: validationTypes.SCOTEstimateNoRelatedWalkthroughForm,
	label: GuidedWorkFlowLabels.SCOTEstimateNoRelatedWalkthroughForm
},
{
	validationId: validationTypes.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2,
	label: GuidedWorkFlowLabels.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2
},
{
	validationId: validationTypes.AccountSignificantDisclosureWithNoRelatedSCOTV2,
	label: GuidedWorkFlowLabels.AccountSignificantDisclosureWithNoRelatedSCOTV2
},
{
	validationId: validationTypes.ITApplicationWithoutITAppPlanningIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppPlanningIndividualDocument
},
{
	validationId: validationTypes.RisksWithoutAnyRelatedAssertions,
	label: GuidedWorkFlowLabels.RisksWithoutAnyRelatedAssertions
},
{
	validationId: validationTypes.AssertionsWithIncompleteCRA,
	label: GuidedWorkFlowLabels.AssertionsWithIncompleteCRA
},
{
	validationId: validationTypes.LimitedRiskOrInsignificantAccountMissingRationale,
	label: GuidedWorkFlowLabels.LimitedRiskOrInsignificantAccountMissingRationale
},
{
	validationId: validationTypes.ITProcessWithoutWalkthroughDocument,
	label: GuidedWorkFlowLabels.ITProcessWithoutWalkthroughDocument
},
{
	validationId: validationTypes.ITProcessIsUncategorized,
	label: GuidedWorkFlowLabels.ITProcessIsUncategorized
},
{
	validationId: validationTypes.ITProcessWithNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessWithNoRelatedITApplication
}

];

// Label overrides (redefine here labels / objects that apply for a different part of the application)
export const resourceOverrides = {
	['Ares']: {
		labels: {
			notAROMM: '重要な虚偽表示リスクではない',
			fraudRisk: '不正リスク',
			significantRisk: '特別な検討を必要とするリスク',
			identifiedRiskFactors: '識別された事象/状況、重要な虚偽表示リスク、特別な検討を必要とするリスク、及び不正リスク',
			countUnassociatedRisk: '事象又は状況が関連付けられていないか、「重要な虚偽表示リスクではない」とマークされていません。'
		},
		riskTypes: [{
			id: 1,
			name: '特別な検討を必要とするリスク',
			abbrev: 'S',
			label: '特検',
			title: '特別な検討を必要とするリスク'
		},
		{
			id: 2,
			name: '不正リスク',
			abbrev: 'F',
			label: '不正',
			title: '不正リスク'
		},
		{
			id: 3,
			name: '重要な虚偽表示リスク',
			abbrev: 'R',
			label: '重要な虚偽表示リスク',
			title: '重要な虚偽表示リスク'
		}
		]
	}
};

export const jeSourceTypes = [{
	value: 1,
	label: 'システム出力'
},
{
	value: 2,
	label: 'マニュアル'
},
{
	value: 3,
	label: '両方'
}
];

export const hasJournalEntriesOption = [{
	value: 1,
	label: 'はい'
},
{
	value: 2,
	label: 'いいえ'
}
];

export const filterReviewNoteStatus = [{
	value: 1,
	label: 'オープン'
},
{
	value: 2,
	label: 'クリア済'
},
{
	value: 3,
	label: 'クローズ済'
}
];

export const EntitiesLabels = {
	close: '閉じる',
	cancel: 'キャンセル',
	repNoRecordMessage: '結果が見つかりません',
	edit: '編集',
	delete: '削除',
	actions: 'アクション',
	show: '表示',
	first: '最初のページ',
	last: '最後のページ',
	prev: '前のページ',
	next: '次のページ',
	search: '検索',
	primary: 'Primary',
	knowledgeRiskLabel: 'JA JP Risks from knowledge cannot be edited or deleted',


	[Entity.Account]: {
		manageEntity: '勘定科目及び開示の管理',
		searchEntity: '勘定科目の検索',
		createEntity: '新しい勘定科目',
		entityName: '勘定科目',
		entityNameCaps: '勘定科目',
		entityNamePlural: '勘定科目',
		placeholderText: '新しく勘定科目及び開示を作成するか、以下より既存の勘定科目及び開示を編集又は削除してください。',
		deleteConfirmLabel: 'この勘定科目を削除してよろしいですか？全ての既存の関連付けが削除されます。この操作は元に戻せません。'
	},
	[Entity.Estimate]: {
		manageEntity: '見積りの管理',
		searchEntity: '見積りの検索',
		createEntity: '新しい見積り',
		entityName: '見積り',
		entityNameCaps: '見積り',
		entityNamePlural: '見積り',
		placeholderText: '新しい見積りの作成、又は既存の見積りの編集と削除は以下から行えます。',
		deleteConfirmLabel: 'この見積りを削除してよろしいですか？全ての既存の関連付けが削除されます。この操作は元に戻せません。'
	},
	[Entity.Risk]: {
		manageEntity: 'リスクの管理',
		searchEntity: 'リスクの検索',
		createEntity: '新しいリスク',
		entityName: 'リスク',
		entityNameCaps: 'リスク',
		entityNamePlural: 'リスク',
		placeholderText: '新しいリスクの作成、又は既存のリスクの編集と削除は以下から行えます。',
		deleteConfirmLabel: 'このリスクを削除してよろしいですか？全ての既存の関連付けが削除されます。この操作は元に戻せません。'
	},
	[Entity.STEntity]: {
		manageEntity: 'エンティティの管理',
		searchEntity: 'エンティティの検索',
		createEntity: '新しいエンティティ',
		entityName: 'エンティティ',
		entityNameCaps: 'エンティティ',
		entityNamePlural: 'エンティティ',
		placeholderText: '新しいエンティティを作成するか、以下の既存のエンティティを編集または削除します。',
		deleteEntity: 'エンティティの削除',
		deleteConfirmLabel: 'このエンティティを削除してよろしいですか？全ての既存の関連付けが削除されます。この操作は元に戻せません。'
	},
	[Entity.Control]: {
		manageEntity: '統制の管理',
		searchEntity: '統制の検索',
		createEntity: '新しい統制',
		entityName: '統制',
		entityNameCaps: '統制',
		entityNamePlural: '統制',
		placeholderText: '新しい統制を作成するか、以下の既存の統制を編集又は削除します。',
		deleteEntity: '統制の削除',
		deleteConfirmLabel: 'この統制を削除してよろしいですか？全ての既存の関連付けが削除されます。この操作は元に戻せません。'
	},
	[Entity.ITProcess]: {
		manageEntity: 'ITプロセスの管理',
		searchEntity: 'ITプロセスの検索',
		createEntity: '新しいITプロセス',
		entityName: 'ITプロセス',
		entityNameCaps: 'ITプロセス',
		entityNamePlural: 'ITプロセス',
		placeholderText: '新しいITプロセスを作成するか、以下の既存のITプロセスを編集又は削除します。',
		deleteEntity: 'ITプロセスの削除',
		deleteConfirmLabel: 'このITプロセスを削除してよろしいですか？全ての既存の関連付けが削除されます。この操作は元に戻せません。'
	},
	[Entity.ITRisk]: {
		manageEntity: 'テクノロジーリスクの管理',
		searchEntity: 'テクノロジーリスクの検索',
		createEntity: '新しいテクノロジーリスク',
		entityName: 'テクノロジーリスク',
		entityNameCaps: 'テクノロジーリスク',
		entityNamePlural: 'テクノロジーリスク',
		placeholderText: '新しいテクノロジーリスクの作成、又は既存のテクノロジーリスクの編集と削除は以下から行えます。',
		deleteEntity: 'テクノロジーリスクの削除',
		deleteConfirmLabel: 'このテクノロジーリスクを削除してよろしいですか？全ての関連付けが解除されます。この操作は元に戻せません。'
	},
	[Entity.ITControl]: {
		ITGC: {
			manageEntity: 'ITGCsの管理',
			searchEntity: 'ITGCsの検索',
			createEntity: '新しいITGC',
			editEntity: 'ITGCの編集',
			viewEntity: 'ITGCを表示',
			entityName: 'ITGC',
			entityNameCaps: 'ITGC',
			entityNamePlural: 'ITGCs',
			placeholderText: '新しいITGCを作成するか、以下の既存のITGCsを編集又は削除してください。',
			deleteEntity: 'ITGCの削除 ',
			close: '閉じる',
			cancel: 'キャンセル',
			processIdRequired: 'ITプロセスは必須です',
			save: '保存',
			confirm: '確認',
			iTProcesslabel: 'ITプロセス',
			saveAndCloseLabel: '保存して閉じる',
			saveAndCreateLabel: '保存して別のコンポーネントを作成',
			deleteConfirmLabel: 'このITGCを削除してよろしいですか？全ての既存の関連付けが削除されます。この操作は元に戻せません。',
			operationEffectiveness: '運用状況の有効性',
			itDesignEffectivenessHeader: '整備状況の有効性',
			itTestingColumnHeader: 'テスト',
			testingTitle: 'テスト',
			frequency: '頻度',
			controlOpertaingEffectiveness: '運用状況の有効性',
			designEffectiveness: '整備状況の有効性',
			frequencyITGC: '頻度の選択',
			nameITGC: 'ITGC名（必須）',
			itspNameRequired: 'ITSP名（必須）',
			noResultsFound: '結果が見つかりません',
			selectITRisk: 'テクノロジーリスクの選択（必須）',
			itRiskRequired: 'テクノロジーリスク（必須）',
			itRiskName: 'テクノロジーリスク',
			inputInvaildCharacters: '以下の文字列は、入力に使用できません：*/:<>\\?|"',
			itControlNameRequired: "'ITGC名は必須です",
			itgcTaskDescription: 'ITGCに関する設計したテストを実施して、依拠する期間全体におけるITGCの運用状況の有効性に関する十分かつ適切な監査証拠を入手してください。',
			selectITProcess: 'ITプロセスの選択（必須）',
			itProcessRequired: 'ITプロセス（必須）',
			riskNameRequired: 'テクノロジーリスクは必須です',
			addModalDescription: 'ITGCの説明を入力してください。',
			editModalDescription: 'ITGC及びその関連する属性を編集してください。',
			controlDesignEffectiveness: {
				[0]: {
					description: '未選択'
				},
				[1]: {
					description: '有効'
				},
				[2]: {
					description: '非有効'
				}
			},
			controlTesting: {
				[0]: {
					description: '未選択'
				},
				[1]: {
					description: 'はい'
				},
				[2]: {
					description: 'いいえ'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: '未選択'
				},
				[1]: {
					description: '有効'
				},
				[2]: {
					description: '非有効'
				}
			}
		},
		ITSP: {
			manageEntity: 'ITSPsの管理',
			searchEntity: 'ITSPsの検索',
			createEntity: '新しいITSP',
			editEntity: 'ITSPの編集',
			viewEntity: 'ITSPを表示',
			inputInvaildCharacters: '以下の文字列は、入力に使用できません：*/:<>\\?|"',
			addModalDescription: 'ITSPの説明を入力してください。',
			editModalDescription: 'ITSP及びその関連する属性を編集してください。',
			entityName: 'ITSP',
			selectITProcess: 'ITプロセスの選択（必須）',
			entityNameCaps: 'ITSP',
			processIdRequired: 'ITプロセスは必須です',
			entityNamePlural: 'ITSPs',
			itspRequired: 'ITSP名は必須です',
			close: '閉じる',
			cancel: 'キャンセル',
			iTProcesslabel: 'ITプロセス',
			save: '保存',
			confirm: '確認',
			saveAndCloseLabel: '保存して閉じる',
			riskNameRequired: 'テクノロジーリスクは必須です',
			saveAndCreateLabel: '保存して別のコンポーネントを作成',
			placeholderText: '新しいITSPを作成するか、以下の既存のITSPsを編集又は削除してください。',
			deleteEntity: 'ITGCの削除 ',
			deleteConfirmLabel: 'このITSPを削除してよろしいですか？全ての既存の関連付けが削除されます。この操作は元に戻せません。',
			itspTaskDescription: '依拠対象期間の全体にわたってテクノロジーリスクに有効に対処しているという十分かつ適切な監査証拠を得るため、このタスクの説明をカスタマイズして、IT実証手続の種類、実施時期及び実施範囲を立案します。<br />IT実証手続が期中に実施される場合、期中手続の対象期間から期末までの期間にわたってテクノロジーリスクに対処していることを確認するための追加的な監査証拠を入手するための手続を立案し、実施します。<br />IT実証手続の結果について結論付けます。',
			operationEffectiveness: '運用状況の有効性',
			itDesignEffectivenessHeader: '整備状況の有効性',
			itTestingColumnHeader: 'テスト',
			testingTitle: 'テスト',
			frequency: '頻度',
			controlOpertaingEffectiveness: '運用状況の有効性',
			designEffectiveness: '整備状況の有効性',
			frequencyITGC: '頻度の選択',
			nameITGC: 'ITGC名（必須）',
			itspNameRequired: 'ITSP名（必須）',
			noResultsFound: '結果が見つかりません',
			selectITRisk: 'テクノロジーリスクの選択（必須）',
			itRiskRequired: 'テクノロジーリスク（必須）',
			itRiskName: 'テクノロジーリスク',
			itProcessRequired: 'ITプロセス（必須）',
			controlDesignEffectiveness: {
				[0]: {
					description: '未選択'
				},
				[1]: {
					description: '有効'
				},
				[2]: {
					description: '非有効'
				}
			},
			controlTesting: {
				[0]: {
					description: '未選択'
				},
				[1]: {
					description: 'はい'
				},
				[2]: {
					description: 'いいえ'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: '未選択'
				},
				[1]: {
					description: '有効'
				},
				[2]: {
					description: '非有効'
				}
			},
		}
	},
	[Entity.ITSOApplication]: {
		manageEntity: 'ITアプリケーション,の管理',
		searchEntity: 'ITアプリケーションの検索',
		createEntity: '新しいITアプリケーション',
		entityName: 'ITアプリケーション',
		entityNameCaps: 'ITアプリケーション',
		entityNamePlural: 'ITアプリケーション',
		placeholderText: '新しいITアプリケーションを作成するか、以下の既存のITアプリケーションを編集又は削除します。',
		deleteEntity: 'ITアプリケーションを削除',
		deleteConfirmLabel: 'このITアプリケーションを削除してよろしいですか？全ての既存の関連付けが削除されます。この操作は元に戻せません。'
	},
	[Entity.SCOT]: {
		manageEntity: 'SCOTsの管理',
		searchEntity: 'SCOTsの検索',
		createEntity: '新しいSCOT',
		entityName: 'SCOT',
		entityNameCaps: 'SCOTs',
		entityNamePlural: 'SCOTs',
		placeholderText: '新しいSCOTsを作成するか、以下の既存のSCOTsを編集または削除します。',
		deleteEntity: 'SCOTの削除',
		deleteConfirmLabel: 'このSCOTを削除してよろしいですか？全ての既存の関連付けが削除されます。この操作は元に戻せません。'
	},
	[Entity.SampleItem]: {
		manageEntity: 'Manage sample tags',
		searchEntity: 'タグの検索',
		createEntity: '新しいタグ',
		createManageTagEntity: 'タググループの管理',
		entityName: 'サンプルタグ',
		entityNamePlural: 'タグ',
		entityNameForTagGroupPlural: 'タググループ',
		placeholderText: "新しいタグを作成するか、以下の既存のタグを編集または削除します。新しいタググループを作成する必要がある場合は、<b>'タググループの管理'</b>をクリックしてください",
		deleteEntity: 'サンプルタグの削除',
		deleteConfirmLabel: '選択したタグを削除してよろしいですか？関連する全てのサンプルから削除されます。この操作は元に戻せません。'
	},
	[Entity.SampleTagGroups]: {
		manageEntity: 'サンプルタググループの管理',
		searchEntity: 'JA JP Search tag groups',
		createEntity: '新しいタググループ',
		entityName: 'JA JP sample tag group',
		entityNameCaps: 'タググループ',
		entityNamePlural: 'タググループ',
		placeholderText: '新しいタググループを作成するか、以下の既存のサンプルタググループを編集または削除します。',
		deleteConfirmLabel: '選択したタグを削除してよろしいですか？関連する全てのサンプルから削除されます。この操作は元に戻せません。'
	},
};

export const inherentRiskFactorTypes = [{
	id: 1,
	label: '複雑性',
	displayOrder: 1
},
{
	id: 2,
	label: '主観性 不確実性',
	displayOrder: 2
},
{
	id: 3,
	label: '不正又は誤謬',
	displayOrder: 3
},
{
	id: 4,
	label: '変更',
	displayOrder: 4
},
{
	id: 5,
	label: '勘定科目の種類',
	displayOrder: 5
},
{
	id: 6,
	label: '関連当事者',
	displayOrder: 6
}
];

export const executionType = [{
	id: 1,
	label: 'PT',
	toolTip: 'このエンゲージメントの手続[PT-only]',
	value: 'このエンゲージメント[PT-only]'
},
{
	id: 2,
	label: 'CT',
	toolTip: '他のエンゲージメントの手続[CT-only]',
	value: '他のエンゲージメント[CT-only]'
},
{
	id: 3,
	label: 'PT/CT',
	toolTip: 'このエンゲージメントと他のエンゲージメントの手続[PT/CT]',
	value: 'このエンゲージメントと他のエンゲージメント[PT/CT]'
}
];

export const createEditAccountModalLabels = {
	createModalDescription: "以下に新しい勘定科目の詳細を入力し '<b>{0}</b>' を選択して終了してください。他の勘定科目を作成する場合は '<b>{1}</b>' を選択してください。",
	editModalDescription: "以下に勘定科目の詳細を編集し  '<b>{0}</b>' を選択して終了してください。",
	close: '閉じる',
	cancel: 'キャンセル',
	createAccount: '新しい勘定科目',
	editAccount: '勘定科目の編集',
	newSignificantDisclosure: '新しい重要な開示',
	save: '保存',
	confirm: '確認',
	saveAndCloseLabel: '保存して閉じる',
	saveAndCreateLabel: '保存して別のコンポーネントを作成',
	accountNameLabel: '勘定科目名（必須）',
	accountDesignationLabel: '指定',
	accountExecutionTypeLabel: 'この勘定科目の手続はどの Canvas エンゲージメントで実施され、文書化されていますか？',
	accountEstimateLabel: 'この勘定科目は見積りの影響を受けますか？',
	yes: 'はい',
	no: 'いいえ',
	accountStatementTypeLabel: '文書種類',
	pspIndexDropdownLabel: 'PSPインデックス（必須　最大5つ）',
	removePSPIndexLabel: 'PSPインデックスの削除',
	assertionsLabel: '関連するアサーションの選択',
	accountTypeOptions: AccountType,
	assertionOptions: assertions,
	executionTypeOptions: executionType,
	statementTypeOptions: statementTypes,
	noOptionsMessage: '結果が見つかりません',
	accountNameErrorMsg: '必須',
	pspIndexErrorMsg: '必須',
	assertionWarningMessage: "特別な検討を必要とするリスク、不正リスク又は重要な虚偽表示リスク、見積項目に関連付けられたアサーションを変更することはできません。まずこれらの関連付けを解除する必要があります。',",
	confirmChanges: '変更を確認',
	executionTypeWarningMessage: 'この勘定科目に保存しようとしている変更は既存のアサーションとPSPに影響し、リンクは解除されます。続行してもよろしいですか？この操作は元に戻せません。',
	contentUpdateToastMessage: '{0} のコンテンツ更新が利用可能です。コンテンツ更新ページからコンテンツ更新を開始してください。',
	assertionsRequired: '少なくとも１つのアサーションを選択する必要があります',
	pspIndexDisabledLabel: '最大 5 つの PSP インデックスを選択してください。続行するには、1 つ以上のオプションの選択を解除してください。'
};

export const createEditRiskModalLabels = {
	createModalDescription: "以下に新しいリスクの詳細を入力し、'<b>{0}</b>' を選択して終了します。別のリスクを作成するには、'<b>{1}</b>' を選択します。",
	editModalDescription: "以下のリスクの詳細を編集し、'<b>{0}</b>' を選択して終了します。",
	close: '閉じる',
	cancel: 'キャンセル',
	createRisk: '新しいリスク',
	editRisk: 'リスクの編集',
	riskType: 'リスクタイプ(必須)',
	riskTypeOptions: [{
		value: 1,
		label: '特別な検討を必要とするリスク'
	},
	{
		value: 2,
		label: '不正リスク'
	},
	{
		value: 3,
		label: '重要な虚偽表示リスク'
	}
	],
	save: '保存',
	saveAndCloseLabel: '保存して閉じる',
	saveAndCreateLabel: '保存して別のものを作成',
	riskNameLabel: 'リスク名（必須）',
	relatedAccountsAssertionsLabel: '関連する勘定科目とアサーション（任意）',
	relateAccounts: '勘定科目の関連付け',
	assertionsLabel: '関連するアサーションの選択',
	riskNameErrorMsg: '必須',
	riskTypeErrorMsg: '必須',
	assertionOptions: assertions,
	removeAccountLabel: '勘定科目の削除',
	required: '必須',
	assertionsRequired: '少なくとも１つのアサーションを選択する必要があります'
};

export const CreateEditMestLabels = {
	createModalTitle: '新しいエンティティ',
	createModalDescription: "以下に新しいエンティティの詳細を入力して'<b>{0}</b>' を選択して終了してください。別のエンティティを作成するには、'<b>{1}</b>' を選択してください。",
	close: '閉じる',
	cancel: 'キャンセル',
	save: '保存',
	confirm: 'Confirm',
	primary: 'Primary',
	saveAndCloseLabel: '保存して閉じる',
	saveAndCreateLabel: '保存して別のものを作成',
	entityNameLabel: 'エンティティ名（必須）',
	entityStandardIndexLabel: 'エンティティ標準インデックス（必須）',
	entityDescriptionLabel: 'エンティティの説明',
	entityNameErrorMsg: '必須',
	entityStandardIndexErrorMsg: '必須',
	editModalTitle: 'エンティティの編集',
	editModalDescription: "以下にエンティティの詳細を編集し  '<b>{0}</b>' を選択して終了してください。",
	primaryEntitySelectionLabel: 'JA JP Select as the primary entity',
	primaryEntitySelectionMsg: "JA JP Only one entity in the engagement can be selected as the primary entity, which will be the determinant for the content delivered to the engagement. An entity will need to be selected as the primary to be able to submit the engagement profile. \'Update content\' permission is required to make or edit the primary entity selection.",
	primaryEntityDisableSelectionLabel: "JA JP To change the primary entity designation, select from the \'Edit\' of the entity you wish to designate as primary",
	noAccessLabel: 'JA JP Unauthorized. Contact your administrator and try again.',
	primaryEntityConfirmationLabel: 'JA JP Primary entity confirmation',
	primaryEntityConfirmationDisplay: 'JA JP {0} is currently selected as the primary entity. Are you sure you want to change the primary entity?',
	profileV2ChangeNotSubmittedBannerMessage: 'JA JP Changes have been made to the profile that will result in content updates. Submit the profile to receive the new content or revert the answers to the previous state.',
};

export const CreateEditITProcessLabels = {
	close: '閉じる',
	cancel: 'キャンセル',
	yes: 'はい',
	no: 'いいえ',
	delete: '削除',
	save: '保存',
	saveAndCloseLabel: '保存して閉じる',
	saveAndCreateLabel: '保存して別のコンポーネントを作成',
	newITProcessLabel: '新しいITプロセス',
	editITProcessLabel: 'ITプロセスの編集',
	viewITProcessLabel: 'ITプロセスを表示',
	addDescriptionLabel: "以下に新しいITプロセスの詳細を入力し、終了するには '<b>{0}</b>' を選択してください。別のITプロセスを作成するには '<b>{1}</b>' を選択してください。",
	editDescriptionLabel: "以下のITプロセスの詳細を編集し、終了するには '<b>{0}</b>' を選択してください。",
	iTProcessNameLabel: 'ITプロセス名(必須)',
	confirm: '確認',
	confirmChanges: '確認',
	iTProcessNameErrorMsg: '必須',
	inputInvaildCharacters: '以下の文字列は、入力に使用できません：*/:<>\\?|"',
	remove: '削除'
};

export const CreateEditITRiskLabels = {
	close: '閉じる',
	cancel: 'キャンセル',
	yes: 'はい',
	no: 'いいえ',
	delete: '削除',
	save: '保存',
	saveAndCloseLabel: '保存して閉じる',
	saveAndCreateLabel: '保存して別のものを作成',
	newITRiskLabel: '新しいテクノロジーリスク',
	editITRiskLabel: 'テクノロジーリスクの編集',
	itRiskNameLabel: 'テクノロジーリスク（必須）',
	confirm: '確認',
	confirmChanges: '確認',
	itRiskNameErrorMsg: '必須',
	itProcessNotSelectedErrorMsg: '必須',
	hasNoITGCLabel: 'テクノロジーリスクに対応するITGCsが存在しません',
	editModalDescription: 'テクノロジーリスクの説明を編集します。',
	createModalDescription: 'テクノロジーリスクの説明を入力します。',
	selectITProcess: 'ITプロセスの選択（必須）',
	noITProcessAvailable: 'ITプロセスが作成されていません',
	relatedITProcessLabel: '関連付けられているITプロセス',
	inputInvaildCharacters: '以下の文字列は、入力に使用できません：*/:<>\\?|"',
	remove: '削除'
};

export const CreateEditEstimateLabels = {
	createModalDescription: "以下の新しい見積りの詳細を入力し、'<b>{0}</b>'を選択して終了します。別の見積りを作成するには、'<b>{1}</b>'を選択します。",
	editModalDescription: "以下の見積りの詳細を編集し、'<b>{0}</b>'を選択して終了します。",
	close: '閉じる',
	cancel: 'キャンセル',
	save: '保存',
	saveAndCloseLabel: '保存して閉じる',
	saveAndCreateLabel: '保存して別のものを作成',
	createModalTitle: '新しい見積り',
	editEstimateLabel: '見積りの編集',
	estimateNameLabel: '見積り名（編集）',
	analysisPeriodBalance: '分析日付残高（必須）',
	analysisPeriodDate: '分析日付（必須）',
	comparativePeriodBalance: '比較日付残高（必須）',
	comparativePeriodDate: '比較日付（必須）',
	estimateCategory: '見積りのカテゴリー（必須）',
	confirm: '確認',
	estimateNameErrorMsg: '必須',
	analysisPeriodBalanceErrorMsg: '必須',
	analysisPeriodDateErrorMsg: '必須',
	comparativePeriodBalanceErrorMsg: '必須',
	comparativePeriodDateErrorMsg: '必須',
	estimateCategoryErrorMsg: '必須',
	remove: '削除',
	balanceNOTApplicable: '残高は適用されません',
	wtDetailPrefixForEstimate: '<p>JA JP Complete the estimate walkthrough task.</p>',

	riskLevelOptions: [{
		value: 4,
		label: '極めて低いリスク'
	},
	{
		value: 5,
		label: '低リスク'
	},
	{
		value: 6,
		label: '高リスク'
	},
	{
		value: 7,
		label: '選択されていません'
	}
	]
};

export const CreateEditControlLabels = {
	createModalTitle: '新しい統制',
	editModalTitle: '統制の編集',
	viewModalTitle: '統制の表示',
	close: '閉じる',
	cancel: 'キャンセル',
	save: '保存',
	saveAndCloseLabel: '保存して閉じる',
	saveAndCreateLabel: '保存して別のものを作成',
	controlNameLabel: '統制名 (必須)',
	frequency: '頻度',
	controlType: '統制タイプ',
	frequencyTypeOptions: controlFrequencyType,
	controlTypeOptions: controlTypes,
	designEffectiveness: 'デザインの有効性',
	operatingEffectiveness: '運用状況の有効性',
	testingLabel: 'テストする',
	lowerRiskLabel: 'Is control lower risk?',
	effective: '有効',
	ineffective: '非有効',
	yes: 'はい',
	no: 'いいえ',
	required: '必須',
	remove: '削除',
	noOptionsMessage: '結果が見つかりません',
	disabledTabTooltipMessage: 'ITアプリケーションを関連付けるには、「統制タイプ」で「ITアプリケーション統制」又は「IT依存手作業統制」を選択します',
	itAppLabels: {
		tabLabel: 'IT apps',
		dropdownLabel: '関連するITアプリケーション',
		noRelatedItems: '関連するITアプリケーションがありません',
		itApplications: 'ITアプリケーション'
	},
	soLabels: {
		tabLabel: 'サービス受託会社',
		dropdownLabel: 'サービス受託会社の関連付け',
		noRelatedItems: '関連するサービス受託会社がありません',
		serviceOrganizations: 'サービス受託会社'
	},
	controlNameErrorMsg: '必須',
	createModalDescriptionLabel: "以下に新しい統制の詳細を入力し、終了するには '<b>{0}</b>' を選択してください。別の統制を作成するには '<b>{1}</b>' を選択してください。",
	editModalDescriptionLabel: "以下の統制の詳細を編集し、終了するには '<b>{0}</b>' を選択してください。",
	viewModalDescriptionLabel: '統制並びに関連するITアプリケーション及びサービス受託会社を表示する。',
	wcgwLabel: 'WCGW'
};

export const ITApplicationTypeLabels = [{
	value: 1,
	label: 'アプリケーション/ツール'
},
{
	value: 2,
	label: 'データベース'
},
{
	value: 3,
	label: 'オペレーティングシステム'
},
{
	value: 4,
	label: 'ネットワーク'
},
{
	value: 6,
	label: '未分類'
}
];

export const CreateEditITApplicationLabels = {
	close: '閉じる',
	cancel: 'キャンセル',
	yes: 'はい',
	no: 'いいえ',
	delete: '削除',
	save: '保存',
	saveAndCloseLabel: '保存して閉じる',
	saveAndCreateLabel: '保存して別のものを作成',
	newITApplicationLabel: '新しいITアプリケーション',
	editITApplicationLabel: 'IT アプリケーションの編集',
	iTApplicationNameLabel: 'ITアプリケーション名',
	confirm: '確認',
	confirmChanges: '変更を確認',
	noOptionsMessage: '結果が見つかりません。',
	iTAppNameErrorMsg: '必須',
	controls: '統制',
	substantive: '実証',
	remove: '削除',
	iTApplicationStrategyLabel: 'ITアプリケーション戦略',
	SCOTsLabel: 'SCOT名',
	StrategyLabel: '戦略',
	ControlsLabel: '統制',
	ControlTypeLabel: 'タイプ',
	addDescriptionLabel: "以下に新しいITアプリケーションの詳細を入力し'<b>{0}</b>'を選択して終了してください。他のITアプリケーションを作成する場合は'<b>{1}</b>'を選択してください。.",
	editDescriptionLabel: "以下のITアプリケーションの詳細を編集し、終了するには'<b>{0}</b>' を選択してください。",
	scotErrorMessage: '関連する統制があるため、SCOTはITアプリケーションと関連付けが解除されていない可能性があります。',
	SCOTsLabels: {
		tabLabel: 'SCOTs',
		dropdownLabel: 'SCOTsの関連付け',
		noRelatedItems: '関連するSCOTsなし'
	},
	ControlsLabels: {
		tabLabel: '統制',
		dropdownLabel: '統制の関連付け',
		noRelatedItems: '関連する統制がありません'
	},
	strategyType: {
		1: '統制',
		2: '実証',
		3: '依拠する',
		4: '依拠しない'
	},
	controlType: {
		1: 'ITアプリケーション統制',
		2: 'IT依存手作業統制',
		3: '手作業防止',
		4: '手作業発見'
	},
	technologyTypeOptions: ITApplicationTypeLabels,
	technologyType: 'JA JP Select technology type'
};

export const CreateEditSCOTLabels = {
	createModalTitle: '新しいSCOT',
	editModalTitle: 'SCOTの編集',
	viewModalTitle: 'SCOTの表示',
	createModalDescription: "以下に新しいSCOTの詳細を入力し、終了するには '<b>{0}</b>' を選択してください。別のSCOTを作成するには '<b>{1}</b>' を選択してください。",
	editModalDescription: "以下のSCOTの詳細を編集し、終了するには '<b>{0}</b>' を選択してください。",
	close: '閉じる',
	cancel: 'キャンセル',
	save: '保存',
	saveAndCloseLabel: '保存して閉じる',
	saveAndCreateLabel: '保存して別のものを作成',
	scotNameLabel: 'SCOT名（必須）',
	scotStrategyLabel: 'SCOT戦略',
	scotTypeLabel: 'SCOTタイプ',
	hasEstimateLabel: 'このSCOTは見積りの影響を受けますか？',
	itAPPUsedLabel: 'ITアプリケーションは使用されていますか？',
	routine: '経常',
	nonRoutine: '非経常',
	controls: '統制',
	substantive: '実証',
	yes: 'はい',
	scotNameErrorMsg: '必須',
	remove: '削除',
	noOptionsMessage: '結果が見つかりません。',
	disabledTabTooltipMessage: 'ITアプリケーションを関連付けるには、「ITアプリケーションを使用していますか？」を選択します。',
	itAppLabels: {
		itApplications: '関連するITアプリケーション',
		tabLabel: 'IT Apps',
		dropdownLabel: 'ITアプリケーションの関連付け',
		noRelatedItems: '関連するITアプリケーションがありません'
	},
	soLabels: {
		serviceOrganizations: '関連するサービス受託会社',
		tabLabel: 'サービス受託会社',
		dropdownLabel: 'サービス受託会社の関連付け',
		noRelatedItems: '関連するサービス受託会社がありません'
	},
	wtDetailPrefix: '<p>全ての経常及び非経常SCOTs及び重要な開示プロセスについて、ウォークスルー手続を実施することを通して毎期理解していることを確認します。さらに、PCAOB監査では、見積SCOTsのウォークスルー手続を実施します。<br/>統制依拠戦略を採用する全てのSCOTsに対して、及び特別な検討を必要とするリスクに対応する統制については、関連する統制が適切にデザイン及び適用されていることを確認します。統制依拠戦略を採用するという決定が依然として適切であることを確認します。<br/><br/>監査調書にSCOTの運用を正確に記述しており、ITの利用から生じるリスクや関連する統制(該当する場合)を含む適切なWCGWを全て識別していると結論付けています。<br/><br/> 実証単独戦略を使用する場合の見積SCOTsについては、実証手続に基づいて、見積SCOTの理解が適切かどうかを判断します。</p>',
};

export const ViewSampleItemLabels = {
	previous: '前',
	next: '次',
	sampleDateLabel: 'サンプルの日付',
	attributesHeader: '属性',
	statusHeader: 'ステータス',
	noAttributesLabel: '利用可能な属性はありません。',
	present: '存在する',
	presentWithComments: 'コメント付きで存在',
	notPresent: '存在しません',
	notApplicatable: '該当なし',
	naLabel: 'N/A',
	additionDocumentation: '追加の文書化',
	deleteSampleHeader: 'サンプルの削除',
	deleteSmapleDescription: '選択したサンプルを削除してよろしいですか？この操作は元に戻すことができません。',
	deleteSamplePreText: 'サンプルの説明',
	relateTagModalTitle: 'サンプルにタグを関連付ける',
	relateTagModalDescription: "1つ以上のタグをサンプルに関連付けます。<b>'タグの管理'</b>をクリックして、新しいタグを追加します。タグの関連付けはアーカイブされませんが、タグ自体はアーカイブされるため、ロールフォワードで使用できます。",
	relateTagTableHeader: 'タグ名称',
	relateTagTableSubHeader: 'タググループ',
	tagsCounter: '{0}タグ',
	tagCounter: '{0}タグ',
	relateTagGroupLabel: 'タググループ',
	relateTagSearchPlaceholder: '検索',
	relateTagClearSearch: 'クリア',
	relateTagShowSelectedOnly: '関連付けのあるもののみを表示',
	manageTagsLabel: 'タグを編集',
	addTag: 'タグの追加',
	supportingDocumentationTitle: 'サポートする文書化',
	dropdownAll: 'すべて',
	noResultsLabel: '結果が見つかりません。',
	noDataLabel: 'データがありません',
	attributeStatusModalTitle: 'Mark all as present',
	attributeStatusModalCancelButton: 'キャンセル',
	attributeStatusModalConfirmButton: '保存',
	attributeStatusModalDescription: '属性を「存在する」としてマークしてもよろしいですか？ステータスが選択されていない属性のみが「存在する」としてマークされます。',
	attributeModalDeleteErrorMessage: '属性のステータスは更新できません。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。',
};

export const ShortRiskTypeForAres = {
	1: 'SR',
	2: '不正',
	3: '固有',
	4: '極めて低いリスク',
	5: '低リスク',
	6: '高リスク',
	7: '選択されていません'
};

export const RelateEstimateToAssertionLabels = {
	relateAccountsAndAssertions: '関連する勘定科目及びアサーション',
	relateAccountsToEstimate: '勘定科目を見積りに関連付ける',
	accounts: '勘定科目',
	designation: '指定',
	relatedAssertions: '関連するアサーション',
	accountNameField: '勘定科目名',
	accountTypeIdField: '勘定科目タイプID',
	assertionsField: 'アサーション',
	executionTypeIdField: '実施タイプID',
	notSelected: '選択されていません',
	pathField: 'パス',
	noAccountsAvailable: '使用可能な勘定科目がありません',
	noRelatedAccounts: '見積りに関連付けられた勘定科目がありません。',
	accountId: '勘定科目ID',
	remove: '削除'
};

//Send instructions switcher
export const sendIntructionsSwitcherLabels = {

	[sendInstructionsSwitcherIds.groupInstructions]: 'グループインストラクション',
	[sendInstructionsSwitcherIds.groupRiskAssessment]: 'グループリスク評価'
};

export const RelateEstimateToAccountLabels = {
	relatEstimatesToAccount: '見積りを勘定科目に関連付ける',
	showOnlyRelatedEstimates: '関連する見積りのみ表示',
	noEstimatesResult: labels.noResultsFound,
	noEstimatesLabel: 'エンゲージメントに作成された見積りがありません。',
	estimateNameHeader: '見積り名',
	relatedEstimateCounter: '{0} 見積り',
	relatedEstimatesCounter: '{0} 見積り',
	relatedAccount: '勘定科目/開示',
	close: labels.close
};

export const RelateAccountsToEstimateLabels = {
	relateAccountsToEstimate: '勘定科目を見積りに関連付ける',
	showOnlyRelatedAccounts: '関連する勘定科目のみを表示',
	noAccountsResult: labels.noResultsFound,
	noAccountsLabel: 'エンゲージメントに勘定科目が作成されていません',
	accountNameHeader: '勘定科目名',
	relatedAccountCounter: '{0}勘定科目',
	relatedAccountsCounter: '{0}勘定科目',
	relatedEstimate: labels.estimate,
	close: labels.close
};

export const SupportingDocumentationLabels = {
	evidence: 'エビデンス',
	priorPeriod: '前期',
	temporaryFiles: '一時ファイル',
	externalDocuments: '外部文書',
	addEvidenceBtn: 'エビデンスの追加',
	addTemporaryFilesBtn: '一時ファイルの追加',
	notes: 'ノート',
	signOffs: 'サインオフ',
	name: '名前',
	supportingDocumentationTitle: 'サポートする文書化',
	temporaryFilesEmptyPlaceholder1: '関連する一時文書がありません。',
	temporaryFilesEmptyPlaceholder2: '{addTemporaryFiles}をクリックして一時文書を関連付けます。',
	evidencePlaceholderLine1: '関連するエビデンスがありません。',
	evidencePlaceholderLine2: '{addEvidenceBtn} をクリックしてエビデンスを関連付けます。',
	removeFromSample: 'サンプルから削除',
	unlink: 'リンク解除',
	retailControlEvidenceLabel: 'この特定のサンプルアイテムの属性のテストをサポートする統制エビデンスを保持していましたか？',
	removeEvidenceModalTitle: 'サンプルからエビデンスを削除する',
	removeEvidenceModalDesc: 'このサンプルから全てのエビデンスを削除してよろしいですか？',
	removeEvidenceErrorMessage: 'これらの文書は、このサンプルでは利用できなくなりました。ページを更新し再試行してください。エラーが続く場合は、ヘルプデスクに連絡してください。'
}

export const deleteSampleItemAttributeModal = {
	modalDescription: 'この属性の選択を削除してよろしいですか？追加の文書化は削除されます。',
	modalTitle: '選択の削除',
	modalConfirmButton: '削除',
	modalCancelButton: 'キャンセル',
	additionalDocumentationLabel: '追加の文書化'
}
export const accountsFilterLabels = [{
	id: accountsFilter.allAccounts,
	label: '全ての勘定科目',
	value: accountsFilter.allAccounts
},
{
	id: accountsFilter.accountsWithRelatedEstimates,
	label: '勘定科目及び関連する見積り',
	value: accountsFilter.accountsWithRelatedEstimates
},
{
	id: accountsFilter.accountsWithoutRelatedEstimates,
	label: '関連する見積りのない勘定科目',
	value: accountsFilter.accountsWithoutRelatedEstimates
}
];

export const changeSampleItemAttributeModal = {
	modalDescription: 'この属性の選択を変更してよろしいですか？追加の文書化は削除されます。',
	modalTitle: '選択の変更',
	modalConfirmButton: '変更',
	modalCancelButton: 'キャンセル'
}
export const scotsFilterLabels = [{
	id: scotsFilter.allScots,
	label: '全てのSCOTs',
	value: scotsFilter.allScots
},
{
	id: scotsFilter.scotsWithRelatedEstimates,
	label: 'SCOTs及び関連する見積り',
	value: scotsFilter.scotsWithRelatedEstimates
},
{
	id: scotsFilter.scotsWithoutRelatedEstimates,
	label: '関連する見積りのないSCOTs',
	value: scotsFilter.scotsWithoutRelatedEstimates
}
];

export const CreateEditTagGroupLabels = {
	createModalTitle: '新しいサンプルタググループ',
	editModalTitle: 'サンプルタググループの編集',
	createModalDescription: "以下にタググループの詳細を入力し、終了するには <b>'保存して閉じる'</b> を選択してください。別のタググループを作成するには <b>'保存して別のものを作成> を選択してください。",
	editModalDescription: "以下のタググループの詳細を編集し、終了するには '<b>{0}</b>' を選択してください。",
	close: '閉じる',
	cancel: 'キャンセル',
	save: '保存',
	saveAndCloseLabel: '保存して閉じる',
	saveAndCreateLabel: '保存して別のものを作成',
	tagGroupNameLabel: 'タググループ名称 （必須）',
	required: '必須'
};

export const CreateEditTagLabels = {
	createModalTitle: '新しいサンプルタグ',
	editModalTitle: 'Edit sample tag',
	createModalDescription: "以下にタグの詳細を入力し、終了するには <b>'保存して閉じる'</b> を選択してください。別のタグを作成するには <b>'保存して別のものを作成> を選択してください。",
	editModalDescription: `Edit the tag details below and select '<b>{0}</b>' to finish.`,
	tagNameLabel: 'タグ名称（必須）',
	tagGroupNameLabel: 'タググループ名称 （必須）',
	tagColorLabel: '色（必須）',
	saveAndCloseLabel: '保存して閉じる',
	saveAndCreateLabel: '保存して別のものを作成',
	cancelLabel: 'キャンセル',
	required: '必須',
	save: '保存',
	noresultsLabel: '利用可能なタググループがありません',
	tagColors: [{
		text: '赤',
		color: '赤'
	},
	{
		text: 'オレンジ',
		color: 'オレンジ'
	},
	{
		text: '青緑',
		color: '青緑'
	},
	{
		text: '青',
		color: '青'
	},
	{
		text: '紫',
		color: '紫'
	},
	{
		text: '緑',
		color: '緑'
	}
	]
};

export const ITProcessFlowLabels = {
	itProcess: 'ITプロセス',
	technology: 'テクノロジー',
	technologies: 'テクノロジー',
	technologyName: 'テクノロジー名',
	supportingTechnologyName: 'サポートするテクノロジー名',
	technologyType: 'テクノロジータイプ',
	showOnlyRelated: '関連付けのあるもののみを表示',
	technologiesCounter: '{0} テクノロジー',
	technologyCounter: '{0} テクノロジー',
	supportingTechnologyLabel: 'サポートするテクノロジー',
	relatedITAppNoDataPlaceholder: 'ITプロセスと関連付けられたテクノロジーなし',
	relateTechnology: 'テクノロジーの関連付け',
	supportingITPAppNoDataPlaceholder: 'ITプロセスと関連付けられたテクノロジーなし',
	itRiskHeader: 'ITリスク',
	itgcHeader: 'ITGCs',
	itspHeader: 'ITSPs',
	noDataPlaceholderITGC: '少なくとも1つのITGCを特定するか、ITリスクに対応するITGCが存在しないと指定する必要があります。{createNewITGC}、{relateITGC}を作成するか、ITリスクに対応するITGCが存在しないことを{noITGC}で示してください。',
	noDataPlaceholderITSP: 'ITGCsが非有効と評価した場合、又はITリスクに対応するITGCsが存在しないと判断した場合に、非有効のITGCに関連するITプロセス内のITリスクが悪用されていないという合理的な保証を得るために、IT実証テスト手続(ITSPs) を実施できる場合があります。その場合、{createNewITSP}又は{relateITSP}を作成します。',
	noRecordsFound: 'このITプロセスについて識別されたITリスクなし',
	noITGCPlaceholder: 'ITリスクに対応するITGCsが存在しません。',
	relateSupportingTechnology: 'サポートするテクノロジーの関連付け',
	relatedTechnologiesNotAvailable: 'この文書は関連付けられたテクノロジーでは利用できません',
	supportingTechNotAvailable: 'この文書はサポートするテクノロジーでは利用できません',
	relatedITRisksNotAvailable: 'この文書は関連するITリスクでは利用できません',
	relateITGC: 'Relate ITGCs',
	itRisk: 'ITリスク',
	itgcCounter: '{0} ITGC',
	itgcsCounter: '{0} ITGCs',
	itgcName: 'ITGC名',

};

export const ITRisksFlowLabels = {
	itRisk: 'ITリスク',
	relatedITRiskNoDataPlaceholder: 'ITプロセスと関連付けられたITリスクなし',
	newITRisk: '新規ITリスク',
	relatedITRisksNotAvailable: 'この文書は関連するITリスクでは利用できません',
	deleteConfirmLabel: '選択したITリスクを削除してよろしいですか？この操作は元に戻せません。',
	deleteITRisk: 'ITリスクの削除',
	CreateITFlowITRiskLabels: {
		close: '閉じる',
		cancel: 'キャンセル',
		yes: 'はい',
		no: 'いいえ',
		delete: '削除',
		save: '保存',
		saveAndCloseLabel: '保存して閉じる',
		saveAndCreateLabel: '保存して別のものを作成',
		newITRiskLabel: '新規ITリスク',
		itRiskNameLabel: 'ITリスク名（必須）',
		itRiskCharactersLabel: '文字',
		itRiskOfLabel: '／',
		confirm: '確認',
		confirmChanges: '確認',
		itRiskNameErrorMsg: '必須',
		itProcessNotSelectedErrorMsg: '必須',
		hasNoITGCLabel: 'ITリスクに対応するITGCsが存在しません',
		createModalDescription: "以下にITリスクの詳細を入力し、終了するには '<b>保存して閉じる</b>'を選択してください。別のITリスクを作成するには、'<b>保存して別のものを作成</b>'を選択してください。",
		relatedITProcessLabel: 'ITプロセス',
		inputInvaildCharacters: '入力には、「: */:<>\\?|"」の文字列を含めることはできません',
		remove: 'JA JP Remove',
		editModalDescription: "以下のITリスクの詳細を編集し'<b>保存</b>'を選択して終了します。",
		editITRiskLabel: 'JA JP Edit IT risk'
	}
};

export const ITProcessListingLabels = {
	all: '全て',
	manageChange: '変更管理',
	manageAccess: 'アクセス管理',
	manageSecuritySettings: 'セキュリティ設定管理',
	itOperations: 'IT運用',
	systemImplementation: 'システム導入',
	category: 'カテゴリー',
	uncategorized: '未分類',
	technologies: 'テクノロジー',
};

export const ITProcessQuickFilterOptions = {
	0: ITProcessListingLabels.all,
	1: ITProcessListingLabels.manageChange,
	2: ITProcessListingLabels.manageAccess,
	3: ITProcessListingLabels.manageSecuritySettings,
	4: ITProcessListingLabels.itOperations,
	5: ITProcessListingLabels.systemImplementation
}

// Relate Technology Modal
export const RelateTechnologyModalLabels = {
	relateTechnology: 'テクノロジーの関連付け',
	relatedTechnologiesDescription: 'テクノロジー名',
	supportingTechnologyName: 'サポートするテクノロジー名',
	technology: 'JA JP {0} technology',
	technologies: 'JA JP {0} technologies'
};

export const AccountStandardROMMListingLabels = {
	accountRisksNotAvailableForDocument: 'この文書では、勘定科目リスクは利用できません。',
	noRelatedObject: '関連オブジェクトはありません。オブジェクトを関連付けて開始します。',
	noResultsFound: '利用できるリスクがありません。',
	acceptedText: '承諾済',
	rejectedText: '差戻済',
	allRisksRejected: '全てのリスクが差戻されました',
	relevantAssertions: '関連するアサーション',
	rejectLabel: '差戻',
	acceptLabel: '承諾',
	rejectionRationaleLabel: '差戻し理由',
	rejectionCategoryText: 'カテゴリーの差戻し',
	editRejectionRationaleText: '差戻し理由の編集',
	rejectionRationalePlaceholder: "Are you sure you want to reject the selected risk? Enter the details below and select <strong>\'Reject\'</strong>.",
	cancel: 'JA JP Cancel',
	rejectionRationaleTextAreaPlaceholder: 'JA JP Rationale (required)',
	rejectionCategoryDropdownPlaceholder: 'JA JP Rejection category (required)',
	required: 'JA JP Required',
	preRejectedText: 'JA JP Pre-rejected',
	additionalContextLabel: 'JA JP Additional context',
	additionalContextwhyRiskShouldBeRejected: 'JA JP Click {0} to add additional context specific to this client for why this risk should be rejected.',
	hereLink: 'JA JP here',
	editAdditionalContextText: 'JA JP Edit additional context'
}

export const RejectionCategory = [{
	id: 1,
	label: '発生可能性 - 発生する合理的な可能性がない（統制を考慮しない）'
},
{
	id: 2,
	label: '影響の度合い - 潜在的な虚偽表示は軽微である'
},
{
	id: 3,
	label: 'このエンゲージメントにおいてリスクは重要ではない'
},
{
	id: 4,
	label: 'ROMMは別のエンゲージメントで対応されている（グループ監査のみ）'
},
]

export const formLabels = {
	required: labels.required,
	maxLength: labels.maxLength
};

export const paginationLabels = {
	show: labels.pagingShowtext,
	first: '最初のページ',
	last: '最後のページ',
	prev: '前のページ',
	next: '次のページ'
};
