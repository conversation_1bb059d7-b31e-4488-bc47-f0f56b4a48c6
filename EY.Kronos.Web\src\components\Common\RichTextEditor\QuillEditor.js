import React, {forwardRef, useEffect, useRef, useState} from 'react';
import styled from 'styled-components';
import Quill from 'quill';
import QuillEditorHeader from './QuillEditorHeader';
import { adjustCommentBorders } from './utils/InlineCommentUtils';

const QuillEditor = forwardRef((props, ref) => {
	const {
		editorId,
		value,
		toolbarId,
		toolbar,
		customModules,
		onEditorCreated,
		onSelectionChange,
		onChange,
		placeholder,
		addCustomScrollbar,
		charactersLabel,
		characterCountPrefix,
		label,
		maxCharCount,
		ofLabel,
		optional,
		optionalText,
		showCharacterCount,
		showMaxCharacterCount,
		numberFormat,
		defaultFontSize = 14
	} = props;

	const [isEditorInitialized, setIsEditorInitialized] = useState(false);

	const domObserverRef = useRef();

	// const enforceMaxCharacterLimit = useCallback(
	// 	(delta, oldDelta, source) => {
	// 		const quillRef = ref.current;
	// 		const maxChar = maxCharCount + 1; // This is to account the fact that Quill adds an extra new-line character at the end of its content

	// 		if (!isNullOrUndefined(maxCharCount) && quillRef.getLength() > maxChar) {
	// 			// Get the cursor's current position in the editor
	// 			const selection = quillRef.getSelection();

	// 			quillRef.setContents(oldDelta, 'silent');

	// 			// Move the cursor back to the position it was before calling setContents
	// 			if (selection && selection.index <= quillRef.getLength()) {
	// 				quillRef.setSelection(selection.index - 1);
	// 			}
	// 		}
	// 	},
	// 	[maxCharCount]
	// );

	// Callback function to execute when mutations are observed
	const fixTooltipPosition = (mutationList, observer) => {
		for (const mutation of mutationList.filter(
			(m) =>
				m.type === 'attributes' &&
				m.target.classList.contains('ql-tooltip') &&
				!m.target.classList.contains('ql-hidden') &&
				m.oldValue !== m.target.className
		)) {
			const quill = ref.current;
			const tooltip = mutation.target;
			// const quillBounds = quill.getBounds(quill.selection.savedRange.index);
			const quillBounds = quill.root.getBoundingClientRect();

			const cursorBounds = quill.getBounds(quill.selection.savedRange.index);

			const tooltipBounds = tooltip.getBoundingClientRect();

			// Calculate the new position for the tooltip
			let newLeft = cursorBounds.left;
			const newRight = newLeft + tooltipBounds.width;

			// Check if the new tooltip position is within the editor bounds
			if (newRight > quillBounds.right) {
				// If the tooltip falls outside the of the editor's right edge, align the tooltip's right edge withe the cursor's left edge
				newLeft = cursorBounds.left - tooltipBounds.width;
			}

			// Set the new position
			tooltip.style.left = `${newLeft}px`;
		}
	};

	useEffect(() => {
		// Import FilteredInlineBlot but don't register it here
		// It will be imported and registered in index.js

		let modules = {
			...customModules,
			history: {
				delay: 0,
				maxStack: 100,
				userOnly: false
			},
			table: true
		};

		if (toolbar) {
			modules.toolbar = {
				...modules.toolbar,
				container: `#${toolbarId}`
			};
		} else {
			modules.toolbar = undefined;
		}

		const quill = new Quill(`#${editorId}`, {
			theme: 'snow',
			modules,
			placeholder: placeholder
		});

		// Register DOM observer to fix tooltip position
		domObserverRef.current = new MutationObserver(fixTooltipPosition);

		// Inner tooltip DOM element
		const tooltipElement = quill.container.querySelector('.ql-tooltip');

		domObserverRef.current.observe(tooltipElement, {
			subtree: true,
			attributes: true,
			attributeFilter: ['class'],
			attributeOldValue: true
		});

		if (ref) {
			ref.current = quill;
		}

		if (value) {
			// Using dangerouslyPasteHTML instead of innerHTML
			// to properly insert initial HTML content without polluting the history.
			// This ensures undo doesn't remove the initial content.
			quill.clipboard.dangerouslyPasteHTML(value, 'silent');
			quill.history.clear();
		} else {
			quill.focus();
		}

		if (addCustomScrollbar) {
			const elements = document.getElementById(editorId).getElementsByClassName('ql-editor');

			if (elements.length > 0) {
				const element = elements[0];
				element.classList.add('customScrollbar');
			}
		}

		quill.on('text-change', (delta, oldDelta, source) => {
			if (source === 'user') {
				adjustCommentBorders(quill, { oldDelta, source, onChange });
			} else {
				// For non-user changes, call onChange immediately.
				const newContent = quill.root.innerHTML;
				onChange && onChange(newContent, oldDelta, source, quill);
			}
		});

		// adding a new event for text selection changes
		quill.on('selection-change', (range, oldRange, source) => {
			// emit up
			onSelectionChange?.(range, quill);
		});

		onEditorCreated && onEditorCreated(quill);

		setIsEditorInitialized(true);

		return () => {
			// Cleanup the observer
			if (domObserverRef.current) {
				domObserverRef.current.disconnect();
			}
			// Cleanup the quill instance
			if (quill) {
				quill.off('text-change');
				quill.off('selection-change');
			}
		};
	}, []);

	return (
		<StyledQuillEditor className="StyledQuillEditor motif-rich-text-editor" defaultFontSize={defaultFontSize}>
			{isEditorInitialized && (
				<QuillEditorHeader
					editorRef={ref}
					charactersLabel={charactersLabel}
					characterCountPrefix={characterCountPrefix}
					label={label}
					maxCharCount={maxCharCount}
					ofLabel={ofLabel}
					optional={optional}
					optionalText={optionalText}
					showCharacterCount={showCharacterCount}
					showMaxCharacterCount={showMaxCharacterCount}
					numberFormat={numberFormat}
				/>
			)}
			{toolbar && <section id={toolbarId}>{toolbar}</section>}
			<section id={editorId} />
		</StyledQuillEditor>
	);
});

QuillEditor.displayName = 'QuillEditor';

const StyledQuillEditor = styled.section`
	.ql-container {
		font-size: ${(props) => `var(--px-${props.defaultFontSize})`};
	}
	.ql-editor {
		min-height: var(--px-280) !important;
		max-height: var(--px-560) !important;
		color: var(--neutrals-900) !important;
		outline: none !important;
		.highlight-teal {
			font-size: inherit;
			line-height: var(--px-30) !important;

			/* Only apply borders when we have data-for attribute */
			&[data-for] {
				border-top: 0 !important;
				border-right: var(--px-1) solid var(--neutrals-700) !important;
				border-bottom: 0 !important;
				border-left: var(--px-1) solid var(--neutrals-700) !important;

				& + .highlight-teal[data-for] {
					margin-left: calc(var(--px-1) * -1);
				}
			}

			/* This class is added by the text-change handler to spans created by Quill during formatting. */
			&.no-lr-border {
				border-left: 0 !important;
				border-right: 0 !important;
				margin-left: 0 !important;
				padding: var(--px-2) 0 !important;
			}
			&.no-left-border {
				border-left: 0 !important;
				padding-left: 0 !important;
			}
			&.no-right-border {
				border-right: 0 !important;
				padding-right: 0 !important;
			}
		}
		strong {
			font-weight: 700;
		}
	}
	.ql-snow {
		&.ql-toolbar {
			border-radius: var(--px-8) var(--px-8) 0 0 !important;
			.ql-formats {
				margin-right: var(--px-5) !important;
				&:first-child {
					gap: var(--px-5) !important;
				}
				&:last-child {
					margin-right: 0 !important;
				}
			}
			.tb-custom-option {
				.motif-icon {
					svg {
						color: var(--neutrals-700);
					}
				}
			}
			.ql-color-picker {
				.ql-picker-label {
					display: inline-flex;
				}
				.ql-picker-options {
					width: var(--px-124);
					@media screen and (max-width: 1366px) {
						width: var(--px-152);
					}
				}
			}
		}
		&.ql-container {
			border-radius: 0 !important;
			.ql-editor {
				table {
					td {
						border: var(--px-1) solid var(--neutrals-900);
					}
				}
			}
		}
	}
`;

export default QuillEditor;
