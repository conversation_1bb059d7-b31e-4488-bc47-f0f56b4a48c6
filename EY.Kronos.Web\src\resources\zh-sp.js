/* eslint-disable prettier/prettier,max-len */

import {
	Entity,
	GALinkStatus,
	confidentialityTypes,
	currencyType,
	gaRegion,
	gaRoleTypes,
	gaScopeType,
	notesFilter,
	pointOfContactTypes,
	validationTypes,
	KnowledgeSectionIds,
	sendInstructionsSwitcherIds,
	accountsFilter,
	scotsFilter,
	rejectionType
} from '../util/uiconstants';

/**
 * Created by calhosh on 4/14/2017.
 * ZH SP resource file
 */
export const labels = {
	addEvidenceBtn: '添加证据',
	multipleDocuments: '多个文档',
	invalidEngagementId: '项目ID无效。刷新页面，然后重试。如果错误仍然存在，请与服务台联系。',
	newComponent: '新组成部分',
	workingOffline: '离线工作',
	syncInProgress: '同步进行中',
	prepareOffline: '编制离线数据',
	connectionAvailable: '可用的连接',
	training: '训练',
	clickForOptions: '点击查看更多选项',
	navReviewNotes: '复核注释',
	navHeaderManageTeam: '管理项目组',
	navManageGroup: '管理集团',
	manageObjects: '管理对象',
	navCRASummary: 'CRA汇总表',
	navAuditPlan: '审计计划',
	navWorkPlan: '工作计划',
	navSEM: '实质性评估矩阵',
	navFindings: '发现',
	navContentUpdates: '内容更新',
	navCanvasFormUpdates: 'Canvas表格更新',
	navCopyHub: '复制中心',
	navCopyHubNew: '复制中心-新建副本',
	navArchiveChecklist: '归档检查表',
	navExportHub: '出口中心',
	navReporting: '报告',
	navHelp: '通用帮助',
	validationNavHelp: '验证帮助',
	leaveUsFeedback: '留下反馈',
	navDashboard: '仪表盘',
	tasksPage: '任务',
	documentsPage: '文档',
	collaborationPage: '合作',
	automationPage: '自动化',
	documentHelperConnectionIssue: 'EY Canvas文档助手已检测到问题。点击<a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">此处</a>获得解决问题的指引。',
	noContentAvailable: '无可用内容',
	noSectionsAvailable: '无可用章节',
	noInformationAvailable: '无可用信息',
	collapse: '折叠',
	expand: '展开',
	duplicate: '复制',
	duplicateSection: '复制部分',
	duplicateSectionHeader: '是否确认要复制选定的章节?',
	deleteSection: '删除章节',
	deleteSectionHeader: '是否确认删除所选章节？',
	deleteHeader: '删除标题栏',
	deleteHeaderTitle: '是否确定删除选定的标题栏?',
	confirmLabel: '确定',
	custom: '自定义',
	selectHeader: '选择页眉',
	selectSection: '选定章节',
	noResultsFound: '未找到结果',
	scot: '重大交易类别',
	scotTypes: '重大交易类别',
	frequency: '频率',
	SelectFrequency: '选择频率',
	SelectControlType: '选择控制类型',
	itBadge: 'IT',
	soBadge: 'SO',
	noRecordsAvailable: '无可用记录',
	noIncompleteResponseSummaryView: '无不完整回复',
	noUnresolvedCommentsSummaryView: '无未解决注释',
	edit: '编辑',
	editForm: '编辑表格',
	editControl: '编辑控制',
	delete: '删除',
	remove: '移除',
	noBodies: '无可用正文',
	relateDocuments: '关联文档',
	relatedDocuments: '相关文件',
	deleteBody: '删除主要内容',
	bodyDescription: '是否确定删除选定的主要内容?',
	description: '描述',
	maxLengthForEditResponse: '正文文本超出允许的最大长度',
	maxLengthForEditResponseWithCount: '回复中包含{#}个字符，超过了{##}个字符上限。请通过减少文字或格式化来调整回答并重试。如果问题仍然存在，请联系帮助台。',
	saveResponse: '如放弃修改，则对回答文本的任何编辑都将不被保存。已关闭或已删除的批注将会保持高亮状态。请确认是否放弃全部修改。',
	discardChangesModalText: '放弃更改',
	seeBodyDescriptionText: '参见描述',
	hideBodyDescriptionText: '隐藏描述',
	showBodyDescriptionText: '显示描述',
	okLabel: '好的',
	addButtonLabel: '添加',
	addEvidence: '添加证据',
	addTemporaryFiles: '添加临时文件',
	notemporaryDocs: '无可用临时文件',
	relateFiles: '关联文件',
	uploadFiles: '上传文件',
	cancelButtonLabel: '取消',
	clickEditResponseToContinue: '点击编辑按钮以继续',
	editResponse: '编辑回复',
	save: '保存',
	numericValuePlaceholder: '输入金额',
	saveLabel: '保存',
	cancelLabel: '取消',
	closeLabel: '关闭',
	editText: '编辑',
	select: '选择',
	selectScot: '选择重大交易类别',
	clearHoverText: '清空',
	optional: '（可选）',
	nodocumentsAdded: '无可用的文档',
	errorBanner: '{0} 错误',
	NetworkErrorMessage: '应用网络出现错误。刷新页面，稍后再试。如果错误仍然存在，请与帮助台联系。',
	of: '的',
	characters: '字符',
	show: '显示: ',
	views: '查看： ',
	primaryRelated: '主要相关Canvas表格',
	secondaryRelated: '次要相关Canvas表格',
	singleLineValuePlaceholder: '输入文本',
	paceInputPlaceholder: 'PACE ID',
	multiLineValuePlaceholder: '输入文本',
	riskFactorDescribe: '描述',
	riskFactorLabel: '相关事件和情况/错报风险',
	riskFactorEmptyWarning: '缺少事件和情况/错报风险描述',
	riskFactorNoDescription: '创建新的或选择现有的相关事件和情况/错报风险',
	fraudRiskTagMessage: '此相关事件和情况/错报风险始终会导致舞弊风险，或应被指定为非重大错报风险（如适用）',
	significantRiskTagMessage: '此相关事件和情况/错报风险始终会导致特别风险、舞弊风险，或应被指定为非重大错报风险（如适用）',
	on: '开启',
	sliderDeSelectMessage: '拖动圆形以分配一个值',
	yearPlaceholder: '年年年年',
	dayPlaceholder: '日日',
	monthPlaceholder: '月月',
	amLabel: '上午',
	pmLabel: '下午',
	relatedEntities: '相关实体',
	eyServiceGateway: 'EY Service Gateway',
	eyAutomation: 'EY Automation',
	eyserviceGatewayAutomation: 'EY Service Gateway & Automation',
	creating: 'Creating...',
	cannotCreateUdp: 'Cannot create UDP. Time Phase cannot be empty',

	// 440GL
	rrdReminderTitle: '复核及批准总结表提示',
	rrdReminderMessage: '报告发布日期为｛rrdDescription｝。请勿忘签署复核及批准总结表｛rrdPendingDays｝。',
	rrdReminderPendingDays: '在{0}天内',

	//Create or Associate risks
	createAssociateRisksLabel: '创建新风险或关联风险',
	relatedRiskIsMissingWarning: '相关风险未填写',
	associateRiskDescription: '选择一个或多个风险，或创建一个新风险，以关联至问题的回复。',
	createNewRiskLabel: '创建新风险',
	noRiskIdentifiedLabel: '未识别到风险',

	// GuidanceModal
	eyAtlasLink: 'EY Atlas',
	guidanceHeaderMessage: '本模块包括',
	guidanceModalHeader: '指引',
	guidanceModalLabelText: '分录',
	guidanceFooter: '可获取更多信息。',
	guidanceSeveralEntriesText: '这些分录有： ',
	guidanceVisitText: '访问',
	guidanceClickText: '点击',
	guidanceHereText: '此处，',
	guidanceFooterText: '可获取更多信息。',
	analyticsInconsistencies: '在查看分析工具时，考虑是否存在与我们预期不一致的变动或活动。这些情况可能表明存在新的风险、单独的交易类别、重大交易类别的变动或管理层越权风险。',
	analyticsInconsistenciesOSJE: '记账分录对方科目描述了所选被分析账户的复式记账。我们寻找新的或异常的账户配对。',
	analyticsInconsistenciesActivityBySource: '来源活动描述了所选账户的月度活动总额和相关来源。我们关注异常活动或来源或来源活动量的变动。',
	analyticsInconsistenciesPreparerAnalysis: '编制人分析汇总了编制人为所选账户记录的各期间的活动总额。我们关注编制人的变动或通常不是由其编制的账户活动。',
	analyticsInconsistenciesAccountMetrics: '账户指标汇总了有助于完成账户指定的账户关键信息。',
	analyticsLoadingIsInProgress: '仍在加载所请求的分析工具。加载完成后即可访问该标签页。',

	aboutDescriptioin: '编辑GAM的层面、标准或语言。相关编辑会触发内容更新。',
	aboutContentDescription: 'Edit the content layers, standards, language, or content driving entity. Edits will trigger a content update.',
	about: '关于',
	formType: '表格类型',
	gamLayer: 'GAM 层面',
	contentLayer: '内容层级',
	standard: '准则',
	language: '语言',
	createdBy: '创建人',
	createdOn: '创建于',
	contentLastUpdatedBy: '内容上次更新者',
	contentLastUpdatedOn: '上一次内容更新发生于',
	notModified: '未修改',

	rolesInsufficientTooltip: '角色没有内容编辑权限。向项目管理员获取足够权限。',
	knowledgeFormToolTip: "Knowledge delivered documents cannot be updated. Update the Engagement Profile to change this form's profile.",
	selectTeamMember: '姓名或邮箱',

	// SeeMore component
	showMore: '显示更多',
	showLess: '显示较少',
	showMoreEllipsis: '显示更多...',
	showLessEllipsis: '显示较少...',

	relatedITapplicationSOs: '相关IT应用程序/SO',
	aggregateITevaluations: '汇总IT评估',
	lowerRisk: '较低风险',
	controlLowerRisk: '控制风险较低',
	relatedITApplication: '相关的IT应用程序',
	relatedITSO: '相关的SO',
	noITApplicationUsed: '未使用IT应用程序',

	notSel: '未选中',
	internal: '内部',
	external: '外部',
	notSelected: '未选择',
	noOptionSelected: '未选中',
	tod: 'TOD',
	sap: 'SAP',
	int: 'INT',
	ext: 'EXT',
	toc: 'TOC',

	placeholderForSearch: '搜索',
	source: '来源',
	nature: '性质',
	testOfDetail: '详细测试',
	testOfControl: '控制测试',
	substantiveAnalyticalProcedure: '实质性分析程序',
	expandScot: '1.展开SCOT',
	selectWCGWToDisplayTheResponsiveTask: '2.选择重大错报风险，显示对应的任务。',
	tasksWithNoRelatedWCGW: '无重大错报风险的任务',
	noTasksAvailable: '无可用的任务',
	noWCGWAvailableForTask: '无可用的ROMM',
	noSubstantiveTasksAvailable: '无相关实质性任务',
	selectAssertionToRelateWCGW: '选择认定，将风险与WCGW相关联',
	significantAccounts: '重大账户',
	riskName: '风险： ',
	accountName: '账户： ',
	control: '控制',
	controls: '控制',
	noScotsFound: '无相关的SCOT',
	relatedwcgw: '相关的WCGW',
	relatedRisks: '相关的风险： ',
	boltIconTitle: '相关的风险',
	relatedITApp: '关联的IT应用程序/SO',
	instructions: '说明： ',
	expandRisk: '1.扩大风险',
	selectAssertion: '2.选择一项认定',
	identifyRelatedWCGW: '3.识别与风险有关的WCGW。',
	clickAccount: '1.点击一个账户',
	selectWCGW: '2.选择WCGW',
	identifyRelatedTask: '3.识别应对WCGW的任务。',
	information: '信息',
	requiredAssertions: '所需认定',
	wcgwWithoutTasks: '无任务的WCGW',
	rommAssociatedWNotRelyAssertion: 'ROMM与具有不依赖控制风险的认定或具有较高固有风险的认定相关联（如果账户涉及估计）',
	hasRiskAssociated: '相关风险',
	clearSelections: '全部取消选择',
	romm: 'ROMM',
	riskOfMaterialMisstatementsWithoutRelatedTask: '没有相关任务的重大错报风险',
	selectOneOrMoreTasksToSeeTheRelatedROMM: '选择一项或多项任务以查看相关的ROMM',
	invalidRelatedEntity: '未发现相关账户。刷新页面，然后重试。如果问题仍然存在，请联系帮助台。',
	noResultsAvailable: '未发现结果',
	riskOfMaterialMisstatement: '重大错报风险',
	AccountConclusion: '账户结论',
	CanvasForm: 'Canvas表格',
	IndependenceForm: '独立性表格',
	Profile: '配置文件',
	AccountDetails: '详情',
	Conclusions: '结论',
	accountDetailsTab: '详情',
	conclusionsTab: '结论',
	formsNoContentText: '无可用的内容。',
	formsDocumentNoRelatedObjects: '没有与文档关联的对象',
	formsNoRelatedObjects: '没有关联的对象',
	formsBodyHeaderControl: '控制',
	formsBodyDesignEffectiveness: '设计有效性',
	formsScotsAndWcgws: '重大交易类别和可能出错项',
	wcgWAndRelatedControls: '可能出错项和相关控制',
	controlAndRelatedItSO: '控制和相关IT应用程序/SO',
	type: '类型',
	designEffectiveness: '设计有效性',
	approch: '方法',
	controlOpertaingEffectiveness: '运行有效性',
	iTAppSO: 'IT应用程序/SO',
	iTProcess: 'IT流程',
	iTControl: 'IT控制',
	iTRisk: 'IT风险',
	aggregateITEvaluation: '综合IT评估',
	relatedCanvasForm: '相关Canvas表格',
	relatedSections: '相关章节',
	validations: '验证',
	profileV2Validation: '更改未提交',
	profileV2ValidationModalDescription: '已进行的更改可能导致内容更新，但尚未提交。如果旨在进行更改，请关闭此模态并提交新的配置应答。如果并非旨在进行更改，请复核修订视图并手动将应答恢复至之前选项。',
	profileV2ValidationCount: '1',
	itProcessWithoutRelatedTechnology: 'IT process without related technology',
	reviewNote: '复核注释',
	editAssociations: '编辑关联',
	editAssociationsLower: '编辑关联',
	riskWCGW: '风险：WCGW关系',
	wcgwTask: 'WCGW：任务关系',
	noAssertionFound: '无已关联的认定。请点击{here}关联认定',
	limitedRiskAccountIdentifier: '有限风险账户',
	insignificantAccountIdentifier: '不重大账户',
	noWCGWFound: '没有与此风险相关的WCGW。单击“编辑关联”以关联一个或多个WCGW。',
	noRelatedWCGWs: '无相关的WCGW',
	noWCGWAvailable: '没有适用于所选认定的ROMM',
	expandCollapse: '单击此处展开/折叠',
	requiredAssertionsInfo: '仅显示控制风险评估为不依赖的认定以及与较高风险估计有关的认定',
	wCGWwithoutTasksInfo: '仅显示与控制风险评估为不依赖的认定相关的WCGW，以及与较高风险估计相关的认定的WCGW，这些认定没有任何相关的实质性任务',
	noBuildStepsAvailable: '无可供列报的构建步骤。',
	risk: '风险',
	wcgw: 'WCGW',
	riskWcgw: '风险：WCGW',
	wcgwTasks: 'WCGW：任务',
	riskWcgwLabel: '将风险与WCGW相关联',
	wcgwTasksLabel: '将WCGW与任务相关联',
	noRiskTypes: '未发现风险类型',
	saveRisk: '保存',
	noRisksFound: '未发现风险',
	haveBeenIdentified: '已识别',
	noAccountsFound: '未发现记录',
	noResponseAvailable: '无可用回复',
	noDocumentsAvailable: ' 无可用文档',
	noValue: '没有数值',
	showValidation: '验证',
	noAccountsIdentified: '尚未识别账户。',
	noAssertionsIdentified: '尚未识别认定',
	noWcgwsIdentified: '尚未识别WCGW',
	pastingImagesNotAllowed: '不允许粘帖图片。如果需要图片，请上传图片作为证据并索引。',
	incompleteResponse: '不完整的回复',
	unresolvedComments: '未解决的批注',
	inconsistentForms: '不一致的表格',
	limitedRiskAccount: '有限风险账户',
	inherentRiskAssessment: '固有风险评估',
	task: '任务',
	selected: '已选定',
	displaytoc: '显示TOC',
	workingoffline: '离线工作',
	syncinprogress: '同步进行中',
	prepareoffline: '编制离线数据',
	connectionavilable: '可用的连接',
	softwareUpdate: '软件更新',
	updateLater: '稍后更新',
	updateNow: '立即更新',
	updateMsg: '软件更新可用于EY Canvas。选择立即更新，下载并安装更新。该页面将被刷新。',
	searchPlaceholder: '搜索',
	filter: '筛选',
	leftNavSearchPlaceholder: '搜索标题和小节',
	back: '返回',
	updateAvailable: '可用更新',
	contentUpdateAvailableTooltip: '内容更新可用。点击此处前往“Canvas表格更新”界面以启动更新。',
	moreMenu: '更多菜单',
	signoffPreparer: '编制人签字',
	signoffReviewer: '复核人签字',
	pagingShowtext: '显示',
	searchDocuments: '搜索文档',
	noRelatedDocuments: '没有关联的文档。',
	noRelatedObjects: '无相关对象',
	documentName: '文档名称',
	formDetails: '表格详细信息',
	questionsAndResponses: '问题和回答',
	details: '详细信息',
	trackChanges: '跟踪修订',
	goToTrackChanges: '进入追踪修订',
	attributes: '属性',
	relatedActions: '关联的行动',
	createCustom: '自定义创建',
	createCustomButtonLabel: 'Create custom header, section, or body',
	overwriteForm: '覆盖表格',
	decimalNaN: 'NaN - 不是一个数字',
	noRelatedObjectsApplicable: '无须将对象与此Canvas表格关联。',
	objects: '对象',
	objectName: '对象名称',
	addCustomDescription: '选择要添加到此Canvas表格的内容类型，输入详细信息，然后点击保存',
	headerTitle: '标题',
	sectionTitle: '章节名称（必填）',
	aresSectionTitle: '章节标题',
	customLabel: '自定义标签（可选）',
	customBodyDescription: '主要内容描述',
	header: '标题',
	section: '部分',
	body: '主要内容',
	requiredWCGW: '必填',
	headerTitleRequired: '标题为必填项。',
	bodyDescriptionRequired: '必须有正文描述。',
	bodySectionRequired: '必须有章节。',
	bodyHeaderRequired: '必须有标题。',
	sectionTitleRequired: '必须提供章节名称。',
	headerRequiredMessage: '必须有标题。',
	enterDecimalAmount: '输入小数点后数额',
	enterPercentage: '输入百分比',
	completeRiskFactorAssessment: '完成对已识别事件和情况的评估。',
	noScotsEstimatesIdentified: '未识别重大交易类别或估计',
	// Track changes
	trackChangesResponseLabel: '追踪修订版回复',
	trackChangesVersionLabel: '追踪修订版本',
	noResponseIdentified: '未发现回复',

	// Compare responses
	compareResponsesLabel: '比较应对措施',
	compareResponsesTitle: '比较实体应对措施',
	compareResponseNoDataPlaceholder: '无可用数据，因为项目同类型文档只有一个。',
	labelFor: '用于',
	questions: '问题',
	answers: '答案',
	countOfResponses: '回复数量',
	openNotes: '未解决注释',
	clearedNotes: '已清除注释',
	click: '点击',
	clickToViewAnswer: '查看答案',
	clickToViewQuestionAnswer: '查看问题和答案',
	selectDocuments: '选择文档',
	selectedDocumentsCount: '已选择{0}个文档',
	selectedDocumentCount: '已选择{0}个文档',
	associatedDocuments: '关联文档',
	noAnswerProvided: '未提供回答',

	// Workspace Engagement
	thisEngagement: '此项目',
	documentLocation: '文档位置',
	otherEngagementsInWorkspace: '工作区中的其他项目',
	added: '已添加',
	documentIneligibleForSharingMessage: '不能共享机密文件。',
	fitDocumentCannotbeSelected: 'FIT文档无法在项目之间共享。',

	//Helix Configuration
	helixConfigurationTitle: '整合EY Helix数据',
	helixConfigurationPageDescription: '验证链接的EY Helix项目并将数据导入EY Canvas。如果您在导入数据后更改了以下EY Helix设置或EY Helix数据，则必须重新导入数据进行更新。',
	linkedEYHelixProjects: '已关联EY Helix项目： ',
	client: '客户： ',
	engagement: '项目： ',
	analysisDate: '分析日期： ',
	eyHelixProjects: 'EY Helix项目',
	noPrimaryEYHelixproject: '没有发现主要EY Helix项目。',
	here: '此处',
	identifyEyHelixProjects: '找到EY Helix项目并开始工作流。',
	eyHelix: 'EY Helix',
	primary: '主要',
	helixSettingsDescription: '点击编辑，选择加载EY Helix分析工具时将应用的设置。',
	editButton: '编辑',
	helixSettingsModalTitle: 'EY Helix设置',
	currencyType: '货币类型',
	currencyTypeError: '无法在EY Helix检索货币类型。请确认EY Helix中数据设置正确，然后重试。',
	shortNumberFormat: '少位数格式',
	shortNumberFormatFooter: 'EY Helix表中显示的数值采用四舍五入。',
	eyHelixAnalyzerFilterMetadataError: '无法连接至EY Helix。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	functional: '功能',
	reporting: '报告',
	currencyCode: '货币代码',
	businessUnit: '业务单元',
	roundingNumberFormat: '四舍五入格式',
	eyHelixProjectChangedLine1: '上次保存EY Helix设置后，关联的EY Helix项目发生变化。',
	eyHelixProjectChangedLine2: '从EY Helix导入或重新导入数据前，请先点击编辑以更新设置。',
	helixSettingsTimeline: 'EY Helix设置',
	helixMapEntitiesToBU: '将实体和业务单元匹配',
	helixNumberOfMapEntities: '已匹配业务单元数量',
	importEYHelixDataTimeline: '导入EY Helix数据',
	mapAccountsHelixTimeline: '匹配账户',
	setEYHelixSettings: '在下方编辑EY Helix设置。保存并导入数据后，选定的比较日期1和比较日期2日期将用作OAR活动中与指定分析日期的比较。如想仅与一个比较日期进行比较，请在比较2日期选择中选择“无”。',
	eyHelixDataHasChangedLine1: '上次保存设置后，数据发生变化。在下方重新选择数据然后点击',
	eyHelixDataHasChangedLine2: '更新EY Helix设置。',
	all: '全部',
	multiple: '多个',
	notApplicableAbbreviation: '不适用',
	importEyHelixData: '导入EY Helix数据',
	editScreenshot: '编辑',
	deleteNote: '删除',
	removeAnnotation: '删除注解',
	addAnnotation: '添加注解',
	addingBoundingBox: '选择截图的某一区域添加注解，确认注解，然后打勾确认。',
	cancelBoundingBox: '取消',
	deleteScreenshot: '删除截图',
	openInFullscreen: '全屏模式打开',
	helixURLErrorMessage1: '匹配的EY Helix项目的配置不完整。',
	helixURLErrorMessage2: '请转到{0}页进行更新。',
	helixIsNotEnabledMessage: '您的项目无法使用EY Helix。',
	helixSetup: 'EY Helix设置',
	openAnalysisInHelix: '打开EY Helix中的分析',
	helixInvaliddate: '无效日期。选择分析日期之前的日期。',
	helixcomparativedateoptional: 'EY Helix比较日期2（可选）',
	helixpriorperioddate: 'EY Helix比较日期1',
	helixanalysisperiod: 'EY Helix分析日期',
	helixfiscalDropDownLabel: '期间{0} - {1}',

	helixSettingsEditButtonTitle: '编辑',
	helixImportDataEditButtonTitle: '导入',
	helixImportInsufficientPermissionsMessage: '权限不足，无法导入Helix数据。请与您的项目管理员联系，请求获取Helix数据导入权限。',
	helixImportNotAllowed: '项目配置文档不允许导入Helix数据',
	helixDeleteImportInsufficientPermissionsMessage: '您的权限不足，无法删除EY Helix数据导入。请联系您的项目管理员，请求提供EY Helix数据的删除权限。',

	EYAccountMappingStepLabel: '管理账户匹配',

	EYAccountMappingOptional: '可选',
	EYAccountMappingStepTitleSettingsCompleted: '执行安永账户匹配',
	EYAccountMappingStepTitleSettingsIncomplete: '完成EY Helix设置，以访问EY Helix匹配模块',
	EYAccountMappingInstructions: '将客户账户匹配到安永账户并处理变更。处理完成后，导入以下数据。',
	manageAccountMappingButtonLabel: '管理账户匹配',

	//EY Helix Setup Card
	EYHelixSetupTitle: 'EY Helix',
	EYHelixSetupSubTitle: '配置数据并将其导入EY Canvas',
	LastImported: '上次已导入',
	EYHelixSettings: 'EY Helix连接',
	NoEYHelixProjectLinkedLabel1: '此项目未与主Helix项目关联。请访问',
	NoEYHelixProjectLinkedLabel2: '该页面，以创建链接。',
	NoEYHelixProjectLinkedHperlink: 'EY Helix项目',
	NoDataImportedLabel: '尚未导入EY Helix数据。点击EY Helix设置，以启动流程。',
	noHelixConnections: '点击\“EY Helix连接\”以创建连接。',
	helixConnectionExists: '存在连接',
	helixConnectionsExist: '存在连接',
	helixTrialBalanceImported: '已导入范围和策略试算表',
	helixTrialBalancesImported: '已导入范围和策略试算表',
	helixNoTrialBalanceImported: '点击\“EY Helix连接\”以导入范围和策略试算表。',

	// EY Helix - Map Accounts
	mapAccountsHelixCanvas: '点击“编辑”，将EY Canvas账户与从EY Helix 导入的账户进行匹配。',
	mapAccountsHelixCanvasSubtitle: '拖放EY Helix账户，与EY Canvas账户逐一匹配。点击“管理账户”，创建或编辑EY Canvas账户。',
	mapAccountsHelixHeaderLabel: 'EY Helix账户',
	mapAccountsCanvasHeaderLabel: 'EY Canvas账户',
	mapAccountsConnectedLabel: '已关联账户',
	mapAccountsShowMappedLabel: '显示已匹配账户',
	mapAccountsHideMappedLabel: '隐藏匹配账户',
	mapAccountsManageLabel: '管理账户',
	mapAccountsAndDisclosuresManageLabel: '管理账户和披露',
	mapAccountsNoCanvasAccounts: '尚未识别账户。',
	mapAccountsNoCanvasAccountsClick: '点击',
	mapAccountsNoCanvasAccountGetStarted: '然后开始。',
	mapAccountsRemoveAccount: '删除',
	mapAccountsReImportHelixAccounts: '从EY Helix导入数据失败。请重新导入并重试。',
	mapAccountsReImportHelixAccountsHelpDesk: '如果问题仍然存在，请联系帮助台',
	mapAccountsNoHelixAccountHasBeenImported: '没有已导入的EY Helix 账户。',
	mapAccountsNoHelixAccountHasBeenImportedCheckData: '检查EY Helix 数据并重试。',

	//Helix Analyzer
	accountNotRelatedToDocumentOnPhaseTwo: '无与此文档相关的账户',

	//PM TE SAD Widget
	materialityWidgetLabel: 'PM/TE/SAD',

	// TE labels
	planningmateriality: '计划重要性水平（PM）',
	requiredTePercentage: '必填TE',
	suggestedtepercentage: '建议的可容忍误差（TE）',
	currentperiodte: '当期TE',
	priorperiodte: '上期TE',
	pmPriorPeriod: '上期PM',
	tepercentage: 'TE百分比',
	teamount: 'TE金额',
	teLabel: '可容忍误差',
	sugestedSAD: '建议SAD百分比： ',
	priorSAD: '上期SAD',
	currentPeriodSAD: '当期SAD',
	sadPercentage: 'SAD百分比',
	sadAmount: 'SAD金额',
	rationaleLabel: '逻辑依据',
	suggestedTEPercentageInfo: '如果根据项目特定因素选择了不同的百分比，系统将提示您在下面记录这些因素。',
	rationaleTEDescription: '考虑到上面选择的属性，输入TE占PM百分比的逻辑依据。',
	teAmmountInvalid: '输入有效金额或选择50%或75%',
	highRiskTEAmmountInvalid: '输入有效金额或选择50%',
	highRiskTERequired: '根据对上述考虑事项的回应，确定的TE百分比是必填的且不可更改。',

	// EY Helix Map Entities to Business Units Modal
	mapEntitiesModalTitle: '管理实体',
	mapEntitiesModalLeyendDescription: '管理以下EY Helix项目中的业务单元和EY Canvas实体之间的匹配。',
	mapEntitiesModalLeyendNote: '注：业务单元可以关联到一个或多个安永Canvas实体。保存并导入数据后，与EY Helix中的业务单元关联的数据将显示为匹配到相关EY Canvas实体。',
	mapEntitiesModalEntityCodeLabel: '实体代码',
	mapEntitiesModalEmptyEntitiesList: '尚未创建实体。',
	mapEntitiesRelatedBusinessUnitDropdownPlaceholder: '相关业务单元',
	mapEntitiesSelectedBusinessUnitsCount: '已选择 {0} 业务单元',

	//AdjustedBasis
	enterAmount: '输入金额',
	basisAmount: '基础金额',
	lowEndOfRange: '最低值',
	highEndOfRange: '最高值',
	suggestedRange: 'Based on the above factors, use of a percentage towards the {0} of the range {1} to {2} may be appropriate. If an amount is selected outside of this range based on engagement specific factors, you will be prompted to document those factors below.',
	suggestedRangeLowPMBracket: '根据上述因素，百分比应设置为百分之{0}。如果根据项目特定因素，选择了此百分比以外的某个金额，则将提示您记录这些因素。',
	middle: '中间区间',
	lowerEnd: '较低区间',
	higherEnd: '较高区间',
	lowEnd: '较低区间',
	priorPeriodPm: '上期PM： ',
	suggestedRangeSummary: '建议的范围',
	loadingMateriality: '重要性水平载入中......',
	pmBasisPercentage: 'PM基础百分比',
	pmAmount: 'PM金额',
	currentPmAmount: '本期计划重大性水平金额',
	pmAmountPlaceholder: '输入PM金额',
	currentPeriodPm: '当期PM： ',
	enterRationale: '输入原因',
	rationaleDescription: '将确定计划重要性水平（PM）基准百分比的原因输入到上述选定属性的考虑事项中',
	pmValidationMessage: '计划重要性水平不能超过范围的上限',
	sadValidationMessage: '名义金额不能超过范围的上限。',
	sadRationaleDiscription: '考虑到上面选择的属性，输入设定特定SAD百分比的理由。',
	nopriorperiodDocs: '没有可用的前期文件',
	addPriorPeriodEvidence: '添加以前期间的证据',
	addToEvidenceLabel: '添加至证据',
	moveToEvidenceLabel: '移动到证据',
	addToEvidenceModalDescription: '为已选定文档创建新名称或保留现有名称。',
	GoToSource: '查看来源',
	//ITRiskITControls
	createNewITGC: '新的IT一般控制',
	relateITGC: 'Relate ITGCs',
	createNewITSP: '新IT实质性程序',
	relateITSP: '关联IT实质性程序',
	noITGC: '无IT一般控制',
	itRiskForITGCITSP: 'IT风险名称（必填）',
	createITGCModalDescription: '在下方输入IT一般控制详情，然后点击“<b>{0}</b>”完成。如想另外创建IT一般控制，请点击“<b>{1}</b>”。',
	createITSPModalDescription: '在下方输入IT实质性程序详情，然后点击“<b>{0}</b>”完成。如想另外创建IT实质性程序，请点击“<b>{1}</b>”。',
	controlDesignEffectiveness: {
		[0]: {
			description: '未选中'
		},
		[1]: {
			description: '有效的'
		},
		[2]: {
			description: '无效的'
		}
	},
	controlOperationEffectiveness: {
		[0]: {
			description: '未选中'
		},
		[1]: {
			description: '有效的'
		},
		[2]: {
			description: '无效的'
		}
	},
	controlTesting: {
		[0]: {
			description: '未选中'
		},
		[1]: {
			description: '是'
		},
		[2]: {
			description: '否'
		}
	},
	itAppTypes: {
		[0]: {
			label: 'IT应用程序'
		},
		[1]: {
			label: 'SO'
		}
	},
	controlType: {
		[0]: {
			controlTypeName: '',
			shortName: '未选择'
		},
		[1]: {
			controlTypeName: 'IT应用程序控制',
			shortName: '应用程序'
		},
		[2]: {
			controlTypeName: '依赖IT的手动控制',
			shortName: 'ITDM'
		},
		[3]: {
			controlTypeName: '手动预防',
			shortName: '手动预防'
		},
		[4]: {
			controlTypeName: '手动发现',
			shortName: '手动发现'
		}
	},
	controlTypeEnumLabel: {
		[0]: {
			controlTypeName: '未选择'
		},
		[1]: {
			controlTypeName: 'IT应用程序控制'
		},
		[2]: {
			controlTypeName: '依赖IT的手动控制'
		},
		[3]: {
			controlTypeName: '手动预防'
		},
		[4]: {
			controlTypeName: '手动检查'
		}
	},
	controlFrequencyType: {
		[0]: {
			controlFrequencyTypeName: '未选择'
		},
		[1]: {
			controlFrequencyTypeName: '每天多次'
		},
		[2]: {
			controlFrequencyTypeName: '每天'
		},
		[3]: {
			controlFrequencyTypeName: '每周'
		},
		[4]: {
			controlFrequencyTypeName: '每月'
		},
		[5]: {
			controlFrequencyTypeName: '每季度'
		},
		[6]: {
			controlFrequencyTypeName: '每年'
		},
		[7]: {
			controlFrequencyTypeName: 'IT应用程序控制'
		},
		[8]: {
			controlFrequencyTypeName: '其他'
		}
	},
	strategyType: {
		[0]: {
			strategyTypeName: '未选择'
		},
		[1]: {
			strategyTypeName: '控制'
		},
		[2]: {
			strategyTypeName: '实质性'
		},
		[3]: {
			strategyTypeName: '依赖'
		},
		[4]: {
			strategyTypeName: '不依赖'
		}
	},
	aggregateITEvaluationType: {
		[0]: {
			aggregateITEvaluationTypeName: '未选中'
		},
		[1]: {
			aggregateITEvaluationTypeName: '支持'
		},
		[2]: {
			aggregateITEvaluationTypeName: '不支持'
		},
		[3]: {
			aggregateITEvaluationTypeName: 'FS和ICFR支持'
		},
		[4]: {
			aggregateITEvaluationTypeName: 'FS仅支持'
		}
	},

	sampleItemFilterLabels: {
		filterTypeOfTags: '标记',
		noFiltersAvailable: '没有可用的筛选条件',
		filterToolTip: '筛选',
		clearAll: '全部清除',
		showMore: '更多',
		filters: '筛选条件',
		noResults: '未找到结果',
	},

	stratergyTypeLabels: {
		[0]: {
			label: '未选中'
		},
		[1]: {
			label: '范围内'
		},
		[2]: {
			label: '范围外'
		}
	},
	noChangeReasonCommentAvailable: '从文件齿轮选项中点击编辑原因，以输入变更原因。',
	changeReasonModalTitle: '编辑更改原因',
	changeReasonModalText: '选择报告日期后的文档变更原因。如果进行了多个管理更改，请从下面的下拉列表中选择最重要的更改。如果同时进行了管理更改和非管理更改，请在下面选择“非管理”。',
	changeReasonUploadModalTitle: '上传文档的原因',
	changeReasonUploadModalText: '选择报告日期后的文档变更原因。如果进行了多个管理更改，请从下面的下拉列表中选择最重要的更改。如果同时进行了管理更改和非管理更改，请在下面选择“非管理”。',
	changeReasonModalComboPlaceholder: '选定',
	changeReasonModalAnnotationText: '记录遇到的情况和添加信息的原因，所执行的新的或额外的审计程序、取得的证据和得出的结论，以及对我们的审计报告的影响。',
	changeReasonUploadModalAnnotationText: '记录遇到的情况和添加信息的原因，所执行的新的或额外的审计程序、取得的证据和得出的结论，以及对我们的审计报告的影响。',
	changeReasonModalAnnotationPlaceHolder: '输入更改原因',
	changeReasonModalChangeReasonRequired: '保存更改原因',
	reasonColumnTitle: '原因',
	shared: '已共享',
	shareStatusOwned: '属于该项目。',
	shareStatusShared: '已共享至该项目。',
	lastModifiedBy: '最后修改人',
	fileSize: ' |{1}KB',
	openedLabelText: '已开启',
	currentlyBeingModifiedBy: '当前修改人',
	OpenGuidedWorkflowDocument: '通过EY Canvas FIT促进工具打开此文档。',
	submitProfile: '提交配置文件',
	submitProfileFit: '提交配置',
	contentUpdateUnAuthorizedTooltipMessage: '权限不足，无法执行内容更新。请与你的项目管理员联系，并请求权限以启动内容更新。',
	submitProfileValidationErrorMessage: '仅在已回答所有问题时方可提交配置文件。筛选未完成的问题，完成后并再次提交。如仍有问题，请联系帮助台。',
	pickADate: '选择日期',

	/* Sign Offs */
	preparerHoverText: '作为编制人签字',
	reviewerHoverText: '作为复核人签字',
	preparerTitle: '编制人签字',
	reviewerTitle: '复核人签字',
	deleteHoverText: '删除签名',
	preparer: '编制人',
	reviewer: '复核人',
	preparerLabel: 'P',
	reviewerLabel: 'R',
	noSignOffsAvailable: '无可用签名',
	none: '无',
	partnerInChargeLabel: 'PIC',
	eqrLabel: 'EQR',
	documentSignoffRequiredLabel: '需要以下人员签字： ',

	relatedDocumentsTitle: '相关文件',
	relatedTasksCount: '{0}关联任务',
	relatedTasksTitle: '关联任务',
	relateTemporaryFiles: '关联临时文件',
	bodyRelatedDocumentsTitle: '与正文关联文档',
	relatedObjectsTitle: '相关对象',
	relateDocumentsTitle: '管理相关文件',
	relateDocumentsToBodyTitle: '添加证据',
	relateDocumentsDesc: '选择与Canvas表格关联的文件',
	relateDocumentsToBodyDesc: '关联此工作区中此项目中的文档或另一项目中的文档',
	relateDocumentsToTheBody: 'Relate a document from this engagement.',
	priorPeriodEvidencesToTheBody: '上一期间与正文关联的证据',
	relatedDocunentEngdisabed: '文档不与本项目共享。',
	showOnlyRelatedDocuments: '仅显示相关文件',
	manageDocuments: '管理文件',
	documentCount: '{0}文件',
	documentsCount: '{0}文件',
	relateDocumentsSearchPlaceholder: '搜索文件',
	overwriteFormDesc: '选择一个表格以用当前Canvas表格中的数据覆盖回复。请注意，当前Canvas表格将被移动至临时文件。',
	searchFormPlaceholder: '搜索表格',
	overwriteLabel: '覆盖',
	confirmOverwriteLabel: '确认覆盖',
	confirmOverwriteDesc: '是否确定要将“{0}”表格中的内容复制到 “{1}”表格? “{2}”表格中的回复将被覆盖，但相关联的证据和对象不会被覆盖。请在完成覆盖后将适用的相关证据和对象重新关联至“{3}”表格。“{4}”表格将保留目前的文档配置/GAM层级。因此，如果“{4}”表格的文档配置与“{5}”表格不同，请复核并清除复制的内容。\n在覆盖进程中，不得关闭浏览器或离开页面。完成覆盖后，“{6}”表格将被移至临时文件夹，页面将自动跳转至“{7}”表格。此操作无法撤销。',
	formSelectionRequired: '选择要覆盖的表格。',

	open: '打开',
	startCoeditMode: '开始多用户编辑',
	endCoeditMode: '结束多用户编辑',
	openReadOnly: '打开只读文件',
	copyLink: '复制链接',
	rename: '重命名',
	viewHistory: '查看历史',
	documentOpenModelLabel: '目前文件正在被修改',
	modelUserOpenedTheDocumentText: '该用户打开了文件',
	modelDocumentOpenedText: '该文件目前正在被XX修改',
	modelOpenedDocumentConflictText: '打开文件会发生冲突，因此我们建议作为只读文件打开。如果您想要作为本文件的编辑人，那么',
	clickHereEnabledText: '请点击此处。',
	documentOptions: '文档选项',
	accountDetails: '账户详细信息',

	// DAAS labels
	coEditModeIsEnding: '多用户编辑即将结束',
	coEditMode: '多用户编辑',
	checkInInProgressMessage: '正在签入文档。签入文档可能需要20分钟的时间。请刷新以获取最新进度。',
	checkInInErrorLabel: '文档签入失败',
	checkOutInProgressMessage: '正在签出文档。签出文档可能需要20分钟的时间。请刷新以获取更新',
	checkOutInProgressLabel: '正在签出文档。',
	checkInInProgressLabel: '正在签入文档',
	checkOutInErrorLabel: '文档签出失败。',
	daasErrorMessage: '此时无法完成操作。请刷新页面并重试。如果问题仍然存在，请联系帮助台',
	coEditModeIsStarting: '正在启动多用户编辑',
	daasOpenDocumentWarning: 'Multi-user editing may have been ended by another user. Refresh the page and try again.',
	beingEditedInCoeditMode: 'Being edited in multi-user edit. Edit started by {0}',
	beingEditedInCoeditModeOn: 'on {0}.',
	beingEditedInCoeditModeError: 'Being edited in multi-user edit',
	coEditModeAutomaticallyEnds: '文档处于多用户编辑模式，该模式将在{0}天后自动结束。',
	coEditModeAutomaticallyEndsToday: '文档当前处于多用户编辑模式，该模式将于今天结束。',
	daasStartCollaborationModeWarning: 'Collaboration mode may have been started by another user. Refresh the page and try again.',
	documentCurrentlyBeingModifiedTitle: 'Document currently being modified',
	documentCurrentlyBeingModifiedHeader: 'This document is currently being modified by {0}. This user opened the document',
	documentCurrentlyBeingModifiedBody: 'Starting multi-user edit mode could cause conflicts, so we recommend discussing with {0} before proceeding. Select {1} to start the multi-user edit mode or {2} to return without starting multi-user edit mode.',
	documentEndMultiUserEditingTitle: '​结束多用户编辑',
	documentEndMultiUserEditingHeader: 'Warning: Other users may be actively editing this document.  This can be checked by opening the document and seeing if other users are currently in the file. Please confirm that all changes are complete before ending multi-user mode. File changes in multi-user mode may take up to 1 minute to be processed. Therefore, please wait at least 1 minute after exiting the file before ending multi-user mode.',
	documentEndMultiUserEditingBody: 'Select {0} to end the multi-user edit mode or {1} to return without ending multi-user edit mode.',
	startMultiuserEditing: 'Start',

	/* Engagement Comments */
	clear: '清除',
	close: '关闭',
	reOpen: '重新打开',
	reply: '添加回复',
	replyLabel: '回复',
	unselectComment: '取消选择批注',
	commentText: '输入文本',
	replyText: '回复文本',
	openStatus: '打开',
	clearedStatus: '已清除',
	closedStatus: '已关闭',
	chartCommentsTitle: '复核注释',
	showComments: '显示批注',
	noRecordsFound: '未找到记录',
	noCommentsFound: '使用以下输入信息作出批注。将批注分配给用户，并指定优先级别和到期日。',
	newComment: '添加批注',
	addNoteTitle: '添加注释',
	editComment: '编辑批注',
	newReply: '添加回复',
	editReply: '编辑回复',
	commentTextRequired: '必须有批注文本',
	replyTextRequired: '回复文本是必需的',
	myComments: '我的批注',
	assignTo: '分配至',
	theCommentMustBeAssigned: '需要提供被分配人',
	priorityRequired: '优先级别为必填项',
	dueDateRequired: '到期日为必填项',
	assignedTo: '分配至',
	allComments: '所有批注',
	assignedToMe: '分配给我',
	unassigned: '未分配',
	draggableCommentsPlaceholder: '输入文本以增加一项新批注',
	draggableNotesPlaceholder: '输入文本，以添加新注释',
	enterReply: '输入回复',
	dueDate: '到期日',
	commentsAmmount: '{count} 批注',
	singleCommentAmmount: '{count} 批注',
	eyInternal: '安永',
	noneAvailable: '均不可用',

	navHelixProjects: 'EY Helix关联',

	evidence: '证据',
	priorPeriod: '上一期间',
	temporaryFiles: '临时文件',
	priorPeriodEvidence: '上一期间',
	closed: '所有工作分配已关闭',

	/*Delete*/
	deleteFileTitle: '删除文档',
	deleteFileCloseBtnTitle: '取消',
	deleteFileConfirmBtnTitle: '删除',
	deleteFileCloseTitle: '关闭',
	deleteFileModalMessage: '是否确定要删除选定的文档?',
	/*Rename*/
	editCanvasformObjects: '编辑属性并点击<b>保存</b>。',
	renameFileModalMessage: '重命名文档并点击保存。',
	renameScreenshotModalMessage: '重新命名截图并单击确认。',

	renameFileTitle: '重命名文档',
	fileNameRequired: '文件夹名为必填项',
	invalidCharacters: '文件夹名不能包含： */:<>\\?|"',
	existingFileName: '文件夹名已存在。刷新页面或重命名文件夹，删除此消息。',
	maxLengthExceeded: '文件名不得超过115个字符。',

	STEntityProfileBannerMessage: '对一个或多个实体配置文档所做的更改将导致内容更新。返回配置文档实体页面，然后点击“导入内容”以接收适用于实体配置文档的新内容，或将回答恢复到以前的状态。',
	independenceValidationForOwnForm: '已修改关于独立性的回答，但尚未提交。如果修改为有意去做，确保将修改后的回答提交。如果修改不是有意去做，则请复核修改页面，并手动地将回答内容恢复为之前的选择。',
	independenceValidationForOthersForm: '已修改关于独立性的回答，但项目组成员尚未提交。确保项目组成员有意去修改时，进行了复核后按修改后的内容提交。',
	insufficientRightsForIndependenceSubmission: '权限不足，无法编辑内容。请联系您的项目管理员，申请编辑内容的权限。',
	submitIndependenceProfileV2Message: '请复核配置文档，确认回复准确无误。如果是，请签字确认并继续进行项目。',
	submitIndependenceProfileV2EditMessage: '没有对配置文档做出任何会导致项目内容发生变化的变更。在必要情况下，使用“项目内容更新”页面更新内容。',
	insufficientRightsForProfileV2Submission: '权限不足，无法编辑配置文档。请与您的项目管理员联系，并请求权限以编辑配置文档。',
	returnToDashboard: '返回展示板',
	returnToDashboardFit: '返回看板',
	profileV2ChangeNotSubmittedBannerMessage: 'Changes have been made to the Profile that will result in content updates. Submit the Profile to receive the new content or revert the answers to the previous state.',
	independenceChangeNotSubmittedBannerMessage: '对独立性表格进行了修改后，需将其重新提交。提交独立性表格，或停用该用户以清除系统对该项操作的验证。',
	multiEntityIndividualProfileBannerMessage: '权限不足，无法编辑配置文档。请联系项目管理员，请求获得编辑配置文档的权限。',
	scotStrategy: '重大交易类别策略',
	wcgwStrategy: '可能出错项策略',
	itProcessStrategy: 'IT流程策略',

	/*Edit Wcgw*/
	editWcgw: '编辑可能出错项',
	viewWcgw: '查看可能出错项',
	editScot: '编辑重大交易类别',
	viewScot: '查看重大交易类别',
	showIncomplete: '显示不完整',
	forms: '表格',
	form: '表格',
	comments: '注释',
	changes: '变动',
	editHeader: '编辑标题',
	editSection: '编辑章节',
	editBody: '编辑正文',
	editSectionDescription: '编辑该章节的详细信息，然后点击“保存”。',
	editHeaderDescription: '编辑标题的详细信息，然后点击“保存”。',
	editBodyDescription: '编辑主体内容的详细信息，然后点击“保存”。',
	manageObject: '管理对象',
	relatedObjects: '相关对象',

	/* Manage body objects */
	bro_manage_WCGWTask_title: '相关可能出错项',
	bro_manage_WCGWTask_instructions: '管理适用的可能出错项',
	bro_manage_WCGWTask_noDataLabel: '未找到结果',

	/*Add/Edit ITGC*/
	addITGC: '添加ITGC',
	addNewITGC: '添加新的IT一般控制',
	addExistingITGC: '添加现有IT一般控制',
	addITGCDescription: '输入ITGC描述。',
	itControlNameRequired: 'ITGC名称为必填项',
	frequencyRequired: '频率为必填项',
	frequencyITGC: '选择频率',
	nameITGC: 'ITGC名称（必填）',
	iTProcesslabel: 'IT流程',
	editITGC: '编辑IT一般控制',
	editITSP: '编辑IT实质性测试程序',
	editITGCDescription: '编辑IT一般控制及其关联属性',
	editITSPDescription: '编辑IT实质性测试程序及其关联属性',
	viewITGC: '查看IT一般控制',
	viewITSP: '查看IT实质性测试程序',
	itgcTaskDescription: '执行我们设计的IT一般控制测试，以获得充分、适当的审计证据，证明其在整个依赖期间的运行有效性。',
	/**
	 * Add Edit ITGC
	 */
	addITSPDescription: '输入ITSP描述。',
	selectITRisk: '选择IT风险（必选）',
	itRiskRequired: 'IT风险（必选）',
	itspNameRequired: 'ITSP名称（必填）',
	itspTaskDescription: '自定义此任务描述，以设计IT实质性程序的性质、时间和范围，从而获得充分、适当的审计证据，证明IT风险在整个依赖期内得到有效应对。<br />当在中期日执行IT实质性程序时，设计和执行程序以获得额外证据，证明IT风险在我们的中期程序至期末期间得到应对。<br />我们根据IT实质性程序的结果得出结论。',
	itspRequired: 'ITSP名称为必填',
	selectTestingStrategy: '设计控制测试的性质、时间和范围，以获得充分、适当的审计证据，证明控制在整个依赖期内按照设计有效运行，以预防或发现并更正认定层面的重大错报。<br />通过评估我们的控制测试结果，包括我们扩大样本量和执行补偿性控制测试时的结果，就控制运行有效性得出结论。',
	itControlNameTest: '测试{0}',

	/*Edit ITControl*/
	editITControl: '编辑IT一般控制（ITGC）/IT实质性程序（ITSP）',
	viewITControl: '查看ITGC/ITSP',

	/*Add/Edit ITRisk*/
	editITRisk: '编辑IT风险',
	editITRiskDescription: '编辑IT风险',
	viewITRisk: '查看IT风险',
	addITRisk: '添加IT风险',
	addITRiskDescription: '输入IT风险描述',
	selectITProcess: '选择IT流程（必填）',
	itRiskName: 'IT风险',
	itRiskNameRequired: 'IT风险（必填）',
	riskNameRequired: 'IT风险为必填项',
	processIdRequired: 'IT流程为必填项',
	itProcessRequired: 'IT流程（必填）',
	hasNoITGC: '没有应对该IT风险的ITGC',

	/*Edit Risk*/
	editRisk: '编辑风险',
	viewRisk: '查看风险',

	/*Edit Control*/
	editControl: '编辑控制',
	viewControl: '查看控制',
	scotRelatedControls: '相关的控制',
	applicationControl: '应用程序控制',
	iTDependentManualControl: '依赖IT的手动控制',
	noAapplicationControlAvailable: '无应用程序控制',
	noITDependentManualControlAvailable: '无ITDM控制',
	isIPEManuallyTested: '此ITDM控制的自动化部分仅用于经过实质性测试的系统生成报告。',

	/*Edit ITProcess*/
	editITSOProcess: '编辑IT/SO流程',
	viewITSOProcess: '查看IT/SO流程',

	/*Edit ITApplication*/
	viewITAppSO: '查看IT应用程序/SO',
	editITAppSO: '编辑IT应用程序/SO',
	strategy: '策略',
	nameRequired: '名称为必填',
	name: '姓名',

	/*Snap shot*/
	currentVersion: '当前版本',
	compareVersion: '选择一个版本进行比较',
	snapshotVersionNotAvailable: '无可以进行比较的版本',
	snapshots: '截图',
	sharedFormWarning: "这是共享Canvas表格。对象和证据在原项目中，因此删除链接后，无法添加至该项目。查看<a style='color：#467cbe' href='https://live.atlas.ey.com/#library/C_38129691' target='_blank'>此处的促进工具</a> ，获取更多详细信息。 ",
	fullView: '全页面视图',
	defaultView: '默认视图',
	print: '打印',
	version: '版本',
	navigationUnavailable: '在追踪修订和属性视图下无法使用导航。查看“问题和回复”，以重新启用导航。',
	snapshotUpdate: '已更新',
	snapshotNew: '新建',
	snapshotRemoved: '已移除',
	snapshotRollforward: '已在后推时创建',
	snapshotRestore: '已在恢复时创建',
	snapshotCopy: '已在复制时创建',

	/*Special Body*/
	priorPeriodAmount: '之前期间金额',

	// Helix special body:
	helixScreenshotListLoading: '正在加载截图...',
	helixScreenshotLoading: '正在加载截图图像...',
	helixScreenshotDeleting: '正在删除截图...',
	helixNotesLoading: '正在加载标记...',
	helixNotesBoundingBoxShow: '显示注释',
	helixNotesBoundingBoxHide: '隐藏注释',
	helixNoteReferenceNumber: '#',
	helixNoteReferenceNumberPlaceholder: '输入索引号',
	helixNoteText: '注释',
	helixNoteTextPlaceholder: '输入标记文本',
	helixNoteAnnotate: '注释',
	helixNoteAnnotateMessage: '在截图上选择一块区域添加注解，确认注解，然后点击复选标记保存注解。',
	helixRemoveAnnotation: '删除注释',

	/* User lookup body */
	userLookupInstructionalText: '输入姓名或电子邮件，然后按回车键查看结果。',
	userLookupShortInstructionalText: '请输入姓名或邮箱，然后点击回车',

	/*Guidance*/
	guidance: '指引',
	noIncompleteBodies: '选择导航菜单中的标题或章节，查看内容。',
	noUnresolvedComments: '选择导航菜单中的标题或章节，查看内容。',
	addComment: '添加批注',

	/*Independence*/
	otherFormIndependenceMessage: '此独立性表格的内容已更新，且用户自更新后尚未重新登录。因此某些回复可能不完整。已保留之前独立性状态，供参考。',
	override: '覆盖',
	grantAccess: '授权访问',
	denyAccess: '拒绝访问',
	overrideSmall: '覆盖',
	grantAccessSmall: '授权访问',
	denyAccessSmall: '拒绝访问',
	status: '状态',
	undefined: '未定义',
	incomplete: '未完成',
	noMattersIdentified: '未识别事项',
	matterIdentifiedPendingAction: '已识别事项——待采取行动',
	matterResolvedDeniedAccess: '已解决事项——拒绝访问',
	matterResolvedGrantedAccess: '已解决事项——授权访问',
	notApplicable: '不适用',
	restored: '已恢复',
	overridden: '已覆盖',
	priorNoMattersIdentified: '之前——未识别事项',
	priorMatterIdentifiedPendingAction: '之前——已识别事项——待采取行动',
	priorMatterResolvedGrantedAccess: '之前——已解决事项——授权访问',
	priorMatterResolvedDeniedAccess: '之前——已解决事项——拒绝访问',
	byOn: '由{0}于',
	byLabel: '通过',
	onLabel: '在',
	modifiedBy: '修改人',
	reason: '原因',
	submit: '提交',
	submitTemplate: '提交模板',
	independenceHoverText: '您必须为审计负责合伙人、项目合伙人或执行总监，才可向此用户授权访问权限、拒绝向其授权访问权限或覆盖其访问权限。',
	enterRationaleText: '输入理由',
	enterRationalePlaceholderText: '输入理由文本',
	requiredRationaleText: '原因（必填）',
	rationaleTextRequired: '原因为必填项',

	sharedExternalWarning: '此表格已通过Canvas客户端共享，外部项目组成员有权访问此表格。只能输入应与外部项目组成员共享的回复和批注。',
	independenceViewTemplateMessage: "在对各项目组成员进行个人独立性询问时，可将本表格当作模板使用。<br /> 在完成独立性询问时，有若干问题与适用于被审计实体的独立性要求有关，每个项目组成员都必须对此进行回答。针对这些问题选择合适的回答。回答将同步到每个项目组成员的个人独立性询问中。如果项目组成员在再次参与项目时选择了不同的回答，则必须再次确认其独立性。如果项目组成员不再次参与项目，则会保留其以前的独立性状态和回答。<br /> 只有经授权的用户才能对独立性模板进行更改。请联系项目管理员。任何更改都必须提交，即使这些更改已在存档前被手动撤消。",

	/**
	 * FORM OBJECTS: SCOT-WCGW-CONTROL
	 */
	fo_instructionalText: '选择Canvas表格正在记录的对象',
	fsro_instructionalText: '管理与本章节相关的对象',
	relObj_title_risk: '风险',
	relObj_title_riskType: '风险类型',
	fo_showOnlyRelated: '仅显示相关对象',
	scotsCount: '{0}重大交易类别',
	wcgwsCount: '{0}可能出错项',
	itsoCount: '{0}IT应用程序/服务机构',
	controlsCount: '{0}控制',
	itControlsCount: '{0}IT控制',
	itGcCount: '{0}ITGC',
	itSpCount: '{0}ITSP',
	itProcessesCount: '{0}IT流程',
	risksCount: '{0}风险',
	accountsCount: '{0}账户',

	stEntitiesCount: '{0}实体',

	componentsCount: ' {0} 组成部分',
	view: '查看',
	searchByScotName: '按重大交易类别名称搜索',
	searchByWcgwName: '按可能出错项名称搜索',
	searchByITSOAppName: '按IT/SO应用程序名称搜索',
	searchByControlName: '按控制名称搜索',
	searchByItControlName: '按IT控制名称搜索',
	searchByItProcessName: '按IT流程名称搜索',
	searchByRiskName: '按风险名称搜索',
	searchByAccountName: '按账户名称搜索',
	searchBySTEntityName: '按实体名称搜索',
	searchByEstimateName: '按估计名称搜索',
	searchByComponentName: '按组成部分名称搜索',
	noScotsAvailable: '本项目中没有可用的重大交易类别。',
	noRisksAvailable: '本项目中不存在风险。',
	noControlsAvailable: '本项目中没有可用的控制。',
	noItControlsAvailable: '本项目中没有可用的IT控制。',
	noItProcessesAvailable: '本项目中没有可用的IT流程。',
	noItApplicationsAvailable: '本项目中没有可用的IT应用程序。',
	noAccountsAvailableLabel: '本项目中无可用的账户。',
	noObjectsRelatedToForm: '无此Canvas表格相关对象',
	noDocumentControlsAvailable: '没有与此文档关联的控制。',
	noDocumentScotsAvailable: '没有与此文档关联的重大交易类别。',
	noSTEntitiesAvailable: '本项目中没有可用实体。',
	noComponentsAvailable: '此项目中没有可用的组成部分。',
	editObjectDescription: '编辑对象与此表格的关联',
	editObjectsLabel: '编辑对象',
	noITGCsOrITSPsHaveBeenIdentified: '没有识别到IT一般控制或IT实质性程序',
	noItProcessIdentified: '尚未识别出IT流程',
	noControlsIdentified: '没有识别到控制',
	noRelatedRisksIdentified: '没有识别到特别风险或舞弊风险',
	noItApplicationsIdentified: '没有识别到IT应用程序',
	noSCOTIdentified: '没有识别到重大交易类别',
	noWCGWIdentified: '没有识别到可能出错项',
	maxLimitLabel: '已选择最多数量的对象。',
	minLimitLabel: '已选择最少数量的对象。',

	relatedITAppsTitle: 'IT流程和相关IT应用程序',
	relatedWCGWTasksTitle: '可能出错项和相关任务',
	noRelatedTasks: '无相关任务',
	noRelatedWcgw: '无相关可能出错项',
	noRelatedControls: '无相关控制',
	controlRelatedRisksTitle: '控制和相关风险',
	sCOTRelatedRisksTitle: '重大交易类别和相关风险',
	scotRelatedItApp: '与重大交易类别相关的IT应用程序',
	relatedItApps: '相关IT应用程序',
	relatedRisksTitle: '相关的风险',
	relatedItRisksItProcessesTitle: 'ITCG和相关IT流程与IT风险',
	testingTitle: '测试',
	strategyTitle: '策略',
	yes: '是',
	no: '否',
	noRelatedRisks: '无相关特别风险或舞弊风险',
	closeAllComments: '关闭所有批注',
	closeComments: '关闭批注',
	closeCommentsDescription: '将关闭所有未开始和已清除的注释。是否确认关闭此{0}的所有批注？',
	addCanvasFormDigital: '数字化',
	addCanvasFormCore: '核心',
	addCanvasFormNonComplex: '非复杂',
	addCanvasFormComplex: '复杂',
	addCanvasFormListed: '已上市',
	addCanvasFormGroupAudit: '集团审计',
	addCanvasFormPCAOBFS: 'PCAOB - FS',
	addCanvasFormPCAOBIA: 'PCAOB - IA',
	addCanvasFormStandards: '准则',
	addCanvasFormLanguage: '语言',
	addCanvasFormNoResultFound: '未发现结果',
	addCanvasFormStandardsNotSelectedMessage: '准则是必填字段',
	addCanvasFormLanguageNotSelectedMessage: '语言是必填字段',

	/* Confidentiality */
	confidentialityPlaceholder: '请选择保密性',
	confidentiality: '机密性',
	confidentialityTitle: '文档机密性',
	confidentialityText: '设置打开此文档所需的访问权限等级。项目管理员在管理小组页面设置访问权限等级。如果已设置此文档的机密性，只有能够打开此文档的人员才可以进行更改。',
	confidentialityNotOpenable: '您的项目权限不足，无法打开文档。访问权限等级由管理小组的项目管理员设置。',
	confidentialityTargetNotOpenable: '只能从来源项目打开机密性文档。',
	backToCCP: '返回至EY Canvas客户端',
	guidanceMessageBackToCCP: '填写本表格后，返回至EY Canvas客户端并向安永提交该请求。',
	noProfileInformationFound: '未找到配置信息。刷新页面，然后重试。如错问题然存在，请联系帮助台。',
	confirmUpdate: '确认更新',
	keepVersion: '保留此版本',
	conflictDescription: '{0}打开此文本{1}后对其进行了编辑。选择应保留的版本。',
	currentConflictVersion: '当前版本',
	serverConflictVersion: '服务器版本',
	conflictShowChanges: '显示跟踪修订',
	sectionViewTrackChangesDropdownPlaceholder: '选择版本',
	verifyingIndependence: '正在检查独立性状态，请稍等。',
	creatingIndependenceForm: '创建独立性表格。',
	meCallFailed: '检索用户信息失败。请刷新页面，然后重试。如错问题然存在，请联系帮助台。',
	getUserByIdFailed: '检索用户独立性状态失败。请刷新页面，然后重试。如问题仍然存在，请联系帮助台。',
	independenceFormCreationFailed: '创建用户独立性表格失败。请刷新页面，然后重试。如问题仍然存在，请联系帮助台。',
	gettingProfile: '正在获取配置信息，请稍候。',
	invalidDocumentId: '文档ID无效。刷新页面，然后重试。如错误仍然存在，请联系帮助台。',
	returnToEditMode: '返回编辑模式',
	saveAndCloseButtonTitle: '保存并关闭',
	formCreationFailed: '创建表格失败。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',

	/*Sign-off requirements*/
	signOffRequirements: '签字要求',
	signoffRequirementsModalTitle: '签字要求',
	signoffRequirementsModalDescription1: '调整此文档中以下管理人员签字要求。',
	signoffRequirementsModalDescription2: '由于EY Canvas要求，某些签名要求无法调整。',
	signoffRequirementsModalSaveLabel: '保存',
	signoffRequirementsModalCancelLabel: '取消',
	signoffRequirementsModalCloseLabel: '关闭',
	signoffRequirementsModalPICLabel: 'PIC',
	signoffRequirementsModalEQRLabel: 'EQR',

	/*<Ares>*/
	/* View changes */
	viewChanges: '查看变动',
	viewChangesModalTitle: '查看变动',
	documentModificationAlert: '此活动最后一次修改人',
	dismiss: '驳回',

	/*Task List*/
	aresPageTitle: 'EY Canvas FIT促进工具',
	aresPageSubtitle: '提供有关您的审计的所需信息，完成以下步骤。',
	summary: '汇总',
	aresNoDocumentFound: '选定活动没有其他可用信息',
	taskSubTitleNoValue: '无可用描述',
	mainActivities: '主要活动',
	unmarkComplete: '取消已完成标记',
	markCompleteTitleTip: '标记完成',
	disableMarkCompleteTitleTip: '确保所有相关文档均由至少一名编制人和一名复核人签署，以将此活动标记为已完成',
	/*Activity Summary*/
	activitySummary: '活动汇总',
	selectedAnswers: '已选答案',
	allAnswers: '所有答案',
	incompleteResponses: '回复不完整',
	previous: '上一步',
	next: '下一步',
	viewAsLabel: '视为',
	rolePreparerLabel: '编制人',
	roleDetailedReviewerLabel: '详细复核人',
	roleGeneralReviewerLabel: '一般复核人',
	roleEQRLabel: 'EQR',
	/*Simple Helix*/
	helixPlaceholder: 'Helix截图需要指引类型7的指引路径。',
	noNotesAvailable: '未创建标记',
	addScreenshot: '添加截图',
	replaceScreenshot: '替换截图',
	replaceFrameDescription: '“查看以下分析并点击替换，以替换当前截图。”`',
	addNote: '添加标记',
	notes: '标记',
	noScreenshotsAvailable: '点击{viewDataAnalytic}开始',
	viewDataAnalytic: '查看数据分析',
	/* Delete modal Helix screenshot*/
	modalTitle: '删除截图',
	sureDeleteBeforeName: '是否删除截图？',
	sureDeleteAfterName: '删除屏幕截图时，所有相关标记也会被删除，且此操作无法撤销。',

	/*uploadDocument body type */
	relateExistingDocuments: '关联现有文档',
	fromEngagementOr: '从此/其他项目或',
	browse: '浏览',
	toUpload: '上传',
	signoffs: '签署',
	addDocument: '添加文档',
	uploadDocument: '上传文档',
	relateDocument: '关联现有文档',
	generateAccountRiskAssessmentPackage: '生成集团ALRA',
	relateDocumentsToBodyAresTitle: '关联文档',
	discardLabel: '放弃',
	uploadDocumentLabel: '上传文档',
	confirm: '确定',
	duplicateDocumentHeader: '该项目中已经存在一个或多个同名文档（作为证据文件或临时文件）。',
	duplicateDocumentInstruction: '选择“覆盖”以上传文档并替换现有文档，或者选择“放弃”以取消上传。如现有文件为“证据”，则该文档将被上传至“证据”。如现有文件为“临时文件”，则该文档将被上传至“临时文件”。',
	maxUploadFilesError: '系统最多可同时上传10个文档',
	/*</Ares>*/
	noTaskRelatedToThisDocument: '没有与此文档关联的任务',
	uncheckTrackChangesToSave: 'Unselect the track changes option to save',
	reviewRoleCloseCommentsTitle: '未解决的注释',
	reviewRoleCloseCommentsDesc: '有需要处理的未解决注释。利用筛选功能快速找出未解决注释。',

	/*Document Upload - PIC/EQR Required Body type*/
	requirementDetails: '要求详细信息',

	//Risk Factors
	riskFactor: '相关事件和情况/错报风险',
	manageRisk: '管理风险',
	noRiskFactors: '未识别到相关事件和情况/错报风险',
	relateRiskFactorsToRisks: '确定事件和情况的重大性',
	riskType: '风险类型',
	relateToRisk: '关联至风险',
	noRisksIdentified: '未识别到风险',
	notDefined: '未定义',
	selectValidRiskType: '请选择有效风险类型',
	newRisk: '添加新风险',
	notAROMM: '非重大错报风险',
	describeRationale: '说明理由',
	noRisksIdentifiedForTheSpecificRiskType: '尚未识别{0}。',
	addAnAccount: '关联其他账户',
	selectAnAccount: '选择账户',
	noAccountsHaveBeenIdentified: '尚未识别账户',
	accountSelected: '账户',
	statementType: '报表类型',
	selectAssertions: '选择一个或多个认定',
	noAssertionsIdentifiedForAccount: '尚未识别该账户的认定',
	relatedAssertions: '相关认定',
	editAccount: '编辑账户与披露',
	addNewDescription: '添加新描述',
	editRiskFactorDescription: '编辑描述',
	enterRiskFactorDescription: '输入相关事件和情况/错报风险描述',
	riskFactorDescriptionRequired: '相关事件和情况/错报风险描述为必填',
	riskFactorDescription: '相关事件和情况/错报风险描述',
	createNewAccount: '创建新账户',
	createAccountLabel: '成功创建账户{0}',
	updateAccountLabel: '将编辑成功保存至账户{0}',
	deleteAccountLabel: ' 已成功删除',
	significanceLabel: '重大',
	provideRationale: '请提供理由，以便保存您的选择',
	clearRiskSignificance: '清除风险重大程度与描述',
	clearSignificance: '明确重要性和描述',

	// Account Summary
	unavailable: '不可用',
	notAvailable: '不可用',
	fraudRisk: '舞弊风险',
	significantRisk: '特别风险',
	significantRiskIndicator: 'SR',
	fraudRiskIndicator: 'FR',
	inherentRisk: '重大错报风险',
	inherentRiskIndicator: '重大错报风险',
	prioryearbalance: '上期余额： ',
	accounts: '账户',
	accountsOther: '账户—其他',
	accountsSignDis: '重大披露',
	xMateriality: '计划重要性水平的倍数',
	xTEMateriality: 'x TE',
	estimateAssociated: '关联估计',
	designation: '指定',
	noAccountshasbeenIdentified: '没有识别到账户。',
	noAccountsOtherhasbeenIdentified: '未识别出其他账户。',
	noAccountsSigfhasbeenIdentified: '未识别出重大披露。',
	addOtherAccounts: '添加账户—其他',
	addSignificantDisclosure: '添加重大披露',
	pydesignation: '前期指定： ',
	notapplicable: '不适用',
	noApplicable: '不适用',
	changeDesignationMessage: '你将改变账户指定',
	changeDesignationTitle: '改变账户指定',
	continue: '继续',
	currentYearBalance: '当期',
	currentPeriodBalance: '本期',
	priorYear: '上年度',
	priorYearDesignation: '上期指定',
	priorYearEstimation: '上期估计',
	priorPeriodChange: '变动百分比',
	analytics: '分析工具',
	notROMMHeader: '非重大错报风险',
	manageEyCanvasAccounts: '管理EY Canvas账户',
	manageAccountMapping: '管理账户匹配',
	manageAccountMappingCloseButton: '使用页面最下方的按钮关闭。',
	manageAccountMappingToasterMessage: '无法连接至EY Helix。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	inherentRiskTypeChangeMessage: '将账户认定从相关更改为不相关后，Canvas中的某些关联（包括可能出错项与认定的关联）将被删除，主要实质性程序将被降级为其他实质性程序。如果要继续下一步，请点击“确认”。如点击“取消”则将返回且不做出更改。',

	analyticsIconDisabled: '您的项目无法使用EY Helix。',
	//Estimate
	estimatesTitle: '估计',
	relatedAccount: '相关账户',
	relatedAccounts: '关联账户',
	currentBalance: '当期余额',
	priorBalance: '上期余额',
	unrelateEstimates: '不相关估计账户',
	manageEstimates: '管理估计',
	noEstimatesCreated: '未创建估计。',
	createEstimates: '创建估计',
	estimateStarted: '以开始',
	createEstimateDocument: '创建估计文档记录',
	noEstimatesFound: '尚未识别出估计',
	relateEstimateLink: '关联估计',

	//Journal Source
	noJournalEntrySourcesAreAvailable: '无可用记账分录来源。',
	jeSourceName: '记账分录来源',
	jeSourceNameTooltip: '记账分录来源',
	changeInUse: '使用变动',
	grossValue: '相关交易总金额',
	relatedTransactions: '相关交易数量',
	relevantTransactions: '与重大交易类别相关',
	expandAll: '展开全部',
	collapseAll: '收起全部',
	descriptionLabel: '提供此来源目的的简要说明',
	jeSourceTypesLabel: '记账分录是通过该来源生成的、系统生成的还是手动记录的？',
	journalEntries: '此来源是否用于记录非标准记账分录（即非经常性、异常交易或调整）？',
	identifySCOTsLabel: '识别与记账分录来源相关的重大交易类别（选择所有适用项）',
	systemGeneratedLabel: '系统生成的',
	manualLabel: '手工的',
	bothLabel: '两者都是',
	accountEstimateLabel: '估计',
	addSCOTLabel: '添加重大交易类别',
	newSCOTLabel: '建立重大交易类别',
	addSCOTModalDescription: '可以创建重大交易类别。变动在保存后即生效。',
	scotnameRequired: '重大交易类别名称为必填项。',
	scotCategoryRequired: '重大交易类别类型为必填项。',
	estimateRequired: '估计为必填项。',

	jeSourcesLabel: '记账分录来源',
	jeSourcesToSCOTs: '记账分录来源至重大交易类别',
	scotsToSignificantAccounts: '重大交易类别至重大账户',

	//Modal common labels.
	modalCloseTitle: '关闭',
	modalConfirmButton: '保存变更',
	modalCancelTitle: '取消',
	modalSave: '保存',
	modalSaveAndClose: '保存并关闭',
	modalSaveAndAdd: '保存并添加',
	modalSaveAndCreateAnother: '保存并新建',

	//Add & Manage Risks
	addNewRiskModalTitle: '添加新风险',
	manageRisksListModalTitle: '管理风险',
	riskInfoMessage: '变动在保存后即生效。',
	risksListInstructionalText: '你可以编辑和删除已有标签。如需要可添加新标签。',
	risksListEmptyArray: '无可用风险。添加新风险以开始。',
	addNewRiskButtonLabel: '添加新风险',
	labelRiskName: '风险名称',
	riskDescriptionLabel: '风险描述',
	selectRiskType: '风险类型',
	requiredRiskName: '风险名称为必填项',
	requiredRiskType: '风险类型为必填项',
	deleteRiskTrashLabel: '删除风险',
	undoDeleteRiskTrashLabel: '撤销删除',
	notARommLabel: '无重大错报风险',
	identifiedRiskFactors: '已识别的事件/情况/错报风险、重大错报风险、特别风险和舞弊风险。',
	noneIdentified: '没有已识别的风险因素',
	countUnassociatedRisk: '事件/情况/错报风险不相关/未标记为“非重大错报风险/重大错报风险”。',

	// Bar Chart / Account Summary
	accountsTotal: '共计{0}账户',
	accountSummary: '账户汇总',
	allAccounts: '所有账户',
	significantAccountsBarChart: '重大账户',
	limitedAccounts: '有限风险账户',
	insignificantAccounts: '非重大账户',
	noAccountsHasBeenIdentifiedBarChart: '没有识别到{0}。',
	selectedTotalAccountsCounter: '{0}/{1} 账户',
	identifyInsignificantAccounts: '识别非重大账户',
	identifySignificantAccounts: '识别重大账户',
	identifyLimitedAccounts: '识别有限风险账户',
	preInsigniAccounts: '不低于当期可容忍误差的前期非重大账户。',
	nonDesignatedInsignificant: '不可将该账户指定为非重大。点击指定下拉列表更改此账户的指定。',
	tolerableError: '可容忍误差不可用。更新重要性水平并重试。',
	documentContainerLabel: '文档',
	clickcreateformtogenerate: '点击 {0} ，生成有限风险账户文档记录。',
	createform: '创建表格',
	createLimitedRiskAccountDocumentation: '创建有限风险账户文档记录',
	limitedAccountDocumentName: '有限风险账户文档记录',

	//Modal Confirm Switch account
	modalSwitchTitle: '未保存变动',
	modalConfirmSwitch: '确认',
	modalConfirmSwitchDescription: '变动未保存，如决定继续，将丢失这些变动。是否想继续？',

	//Modal Edit Account
	manageAccountsModalTitle: '管理EY Canvas账户',
	editAccountLinkLabel: '编辑账户',
	editAccountInstructionalText: '您可以编辑或删除现有EY Canvas账户，也可以创建新账户。变动在保存后即生效。',
	selectAnAccountLabel: '选择账户',
	accountNameLabel: '账户名称',
	accountLabel: '账户',
	accountDesignationLabel: '账户制定',
	accountStatementTypeLabel: '报表类型',
	accountRelatedAssertionsLabel: '相关认定',
	accountHasEstimateLabel: '账户是否受估计影响',
	requiredAccountName: '账户名称为必填项',
	requiredAccountDesignation: '账户指定为必填项',
	requiredStatementType: '报表类型为必填项',
	requiredRelatedAssertions: '选择一项认定',
	pspIndexDropdownLabel: '选择主要实质性程序索引',
	removePSPIndexLabel: '删除主要实质性程序索引',
	addPSPIndexLink: '添加主要实质性程序索引',
	pspIsRequired: '主要实质性程序索引为必填',

	//Delete account modal
	deleteAccount: '删除账户',
	deleteAccountModalMessage: '是否确定删除选定的账户?',
	cannotBeUndone: '此操作无法撤销。',
	guidedWorkflow: 'EY Canvas FIT促进工具',
	scotSummary: 'SCOT汇总表',
	scopeAndStrategy: '范围和策略',
	ToggleSwitch: {
		Inquire: '询问',
		Completed: '已完成',
		isOn: '是',
		isOff: '否'
	},
	navExecution: '执行',
	navCanvasEconomics: 'EY Canvas Economics',
	navOversight: 'EY Canvas监督',
	navConclusion: '结论',
	navTeamMemberIndependence: '项目组成员独立性',
	navGroupAudit: '集团管理层',
	navGroupActivityFeed: '分组活动信息',
	navPrimaryStatus: '主审小组状态',
	navComponentStatus: '组成部分项目小组状态',
	navGroupStatus: '集团状态',
	navEngagementManagement: '项目管理',
	navProfile: '配置文件',
	navItSummary: 'IT汇总',
	nav440GL: '报告日期后改变。',
	navGroupStructureSummary: '集团结构',
	navGroupInstructionSummary: '集团指引',
	navGroupInvolvement: '集团参与',
	navNotApplicable: '不适用',
	cropScreenshot: '裁剪截图',
	cropScreenshotModalDescription: '裁剪屏幕截图，仅保留相关部分。裁剪将删除现有注释，标记将保留，注释可重新添加。裁剪无法撤销。',
	crop: '裁剪',
	replace: '替换',
	nameTheScreenshot: '截图名称',
	nameLabel: '名称',
	takeScreenshot: '添加截图',
	measurementBasis: '计量基础',
	MeasurementBasisMessage: '根据相关EY Helix数据匹配，所选计量基础似乎不处于预期位置（例如，税前收入处于借方位置）。请考虑以下方面： ',
	measurementBasisProjectMappedCorrectly: 'EY Helix项目中的数据是否匹配正确，',
	measurementBasisAppropriateValue: '是否应修改计量基础，或',
	measurementBasisAdjustValue: '调整以下计量基础值是否可能适当',
	basisValueFromHelix: '试算表值',
	rationaleDeterminationLabel: '金额的确定理由',
	loginInstructions: '登录并按照说明设立账户。',
	gotoText: '前往，',
	asOfDate: '截止日期',
	annualizedBasisValue: '年化基础值',
	basisValue: '基础值',
	isAnnualizedAmountRepresentative: '年化基础值是否代表期末预计报告的金额？',
	isAnnualizedAmountRepresentativeForAssetsOrEquity: '该基础值是否代表预计审计期末报告的金额？',

	enterExpectedFinancialPerioadAmount: '输入审计期末的预期金额',
	enterRationaleAmountDetermined: '输入金额的确定理由',
	printNotAvailable: '{0}没有内容，因此不显示任何信息',
	canvasFormPrintNotAvailable: 'Canvas表格打印功能目前不可用。请重试，如仍有错误请联系帮助台。',
	saving: '保存中……',
	removing: '移除中',
	activitySummaryTitle: '活动汇总',
	currentLabel: '当期',
	PMLabel: '计划重要性水平',
	planningMaterialityLabel: '计划重要性水平（PM）',
	TELabel: '可容忍误差',
	tolerableErrorLabel: '可容忍误差',
	SADLabel: 'SAD',
	SADNominalAmountLabel: 'SAD名义金额',
	PriorLabel: '前期',
	editRichText: '编辑文本',
	noTypeTwoResponseAvailable: '无可用回答。<br />点击{clickHere}进行回答。',
	clickHere: '此处',
	helixNavigationTitle: '前往EY Helix配置页关联或配置EY Helix项目',
	helixNavigationLink: '前往EY Helix',
	measurementBasisValue: '计量基础值',
	inlineSaveUnsavedChanges: '有未保存的更改，是否继续?',
	rationaleIncomplete: '理由不完整',
	projectMismatchDisplayMessageOnDataImport: '您的EY Helix主要项目已更改。请确认EY Helix设置并从新项目导入数据。',
	importSuccess: 'EY Helix数据成功导入。',
	importHelix: '点击“导入”，从EY Helix导入总账数据。',
	importLabel: '导入',
	reImportLabel: '重新导入',
	lastImportedBy: '上次导入人',
	lastImportedOn: '上次导入日期',
	dataImportedFromProjec: '从项目导入数据',
	reImportConfirmationTitle: '重新从EY Helix导入数据',
	reImportConfirmationMessagePart1: '从EY Helix重新导入数据将更改现有数据或在相关活动内添加新数据。此操作无法撤销。',
	reImportConfirmationMessagePart2: '有关重新导入数据的流程如何影响EY Canvas FIT促进工具的更多信息和概述，请参阅EY Atlas。',
	defineRisksTitle: '定义风险',
	assessAndDefineRisksTitle: '评估和定义风险',
	identifiedRiskFactorsTitle: '识别的事件/情况和相关风险： ',
	descriptionIncompleteLabel: '描述不完整',
	noRiskHasBeenRelatedMsg: '无已关联的风险。',
	rationaleIncompleteMsg: '理由不完整',
	loading: '正在加载...',
	importInProgressLabel: '正在导入。这可能需要几分钟的时间。请刷新页面，以查看更新后的状态。',
	importInProgressMessage: '正在导入Helix数据。刷新页面，以查看导入状态。',
	importHelixProjectError: '从EY Helix导入数据时出错。检查EY Helix项目的状态是否显示为分析工具可用。请刷新页面，然后再次点击导入或重新导入。如果问题仍然存在，请联系帮助台。',
	importDeletionConfirmationMsg: '是否确定要删除EY Helix导入数据？若删除导入数据，则将会删除相关活动中的现有数据，此操作无法撤销。',
	deletePreviousImport: '删除EY Helix导入数据',
	//Assess Risks - Summary Page
	assessRisksAccordionLabel: '相关风险',
	assessRisksNoItemsFound: '尚未识别风险',
	assessRiskAccountsAndRelatedAssertions: '账户和相关认定',
	assessRiskNoAccountsLinked: '无相关账户',
	accountRiskAssessmentSummary: '账户与披露',
	// Flow chart
	flowchartTitle: '流程图',
	launchFlowchart: '启动流程图',
	clickherelink: '单击此处',
	orderToCashPlacement: '订单到现金发售',
	orderToCashPlacementMessage: '项目部与新客户商议协议，与现有客户商议续约和/或修改合同。合同包含以下详细信息：定价、期限和质保',
	saveFlowChart: '保存',
	newstep: '新步骤',
	zoomIn: '放大',
	zoomOut: '缩小',
	resetZoom: '重置缩放',
	toogleInteractivity: '切换互动',
	fitView: '调整视图',
	numberOfSteps: '步骤数量',
	flowchartSuccessfullyCreated: '已成功创建流程图。',
	flowchartLinkedEvidenceMessage: '此流程图已在另一个项目中创建。当取消证据链接时，将删除对此项目流程图的访问。',
	flowchartSmartEvidenceSourceIdNullMessage: '无可用重大交易类别。',
	noTaskDocumentAvailableFlowchart: '此文档是临时文件。请将任务关联为访问流程图详细資訊的证据',
	// Control Attributes
	controlAttributes: '控制属性',
	noControlAttributes: '无可用控制',
	flowchartStepMoremenu: '更多菜单',
	createControlAttributes: '无可用控制属性。<br/>点击 {clickHere}创建新的控制属性。',
	createNewControlAttribute: '新建属性',
	editControlAttribute: '编辑属性',
	createControlAtttributeInstructions: '在下方输入属性详请，然后选择<b>\“保存并关闭\”</b>完成。新建属性时，选择<b>\“保存并新建\”。</b>属性将按属性索引排序。',
	editControlAttributeInstructions: '编辑下方属性详情，然后点击<b>\“保存\”完成。属性将按属性索引排序。',
	editAttributeButtonLabel: '编辑属性',
	deleteAttributeButtonLabel: '删除属性',
	deleteControlAttributeInstructions: '是否确认删除所选属性？此操作无法撤销。',
	// Control Attributes Form
	requiredAttributeIndexLabel: '属性索引（必填）',
	requiredAttributeDescriptionLabel: '属性描述（必填）',
	errorMessageAttributeIndexRequired: '必填',
	errorMessageAttributeDescriptionRequired: '必填',
	errorMessageAttributeDescriptionMaxLength: '回答包含{#}个字符，超过了{##}个字符的上限。请通过减少文本或调整格式的方式调整描述，然后重试。如问题仍然存在，请联系帮助台。',
	errorMessageAttributeTestingTypesRequired: '必填',
	proceduresLabel: '待执行程序',
	modalRequiredProceduresLabel: '待执行程序（必填）',
	attributeTestingTypesLabel: {
		inquiry: '询问',
		observation: '观察',
		inspection: '检查',
		reperformance: '重新执行/重新计算'
	},

	/*CRA Badge*/
	ir: 'IR',
	cr: 'CR',
	cra: 'CRA',
	incompleteCra: '综合风险评估不完整',
	incomplete: '未完成',

	//Progess bar labels
	savingProgress: '保存中……',
	discardChangesLabel: '放弃更改',
	removeFromBody: '从正文删除',
	uploading: '上传中......',
	uploadComplete: '上传完成',
	downloadComplete: '下载完成',
	processing: '处理中......',

	/* ISA BODIES */
	/* Common */
	deleteEntityConfirmation: '是否确认删除<b>{0}</b>？此操作不可撤销。',
	/* ITAPP-SCOT */
	searchScot: '搜索重大交易类别',
	addITApp: '添加IT应用程序',
	relatedItApp: '相关的IT应用程序',
	itApplicationHeader: 'IT应用程序',
	scotNoDataPlaceholder: '无可用信息',
	noScotsOrControlsPlaceholder: '无相关重大交易类别或控制； {noScotsOrControlsPlaceholderEditAssoc}。',
	noScotsOrControlsPlaceholderEditAssoc: '编辑关联',
	noScotsOrControlsPlaceholderTarget: '无相关的重大交易类别或控制。',
	scotHeader: '重大交易类别',
	controlsHeader: '控制',
	controlsApplicationHeader: '应用程序',
	controlsITDMHeader: '依赖IT的手动控制',
	itAppScotNoDataPlaceholderLabel: '未添加任何IT应用程序。',
	itAppScotNoDataPlaceholder: '尚未添加任何IT应用程序。<br/>点击{itAppScotNoDataPlaceholderAddItApp}开始',
	itAppScotNoDataPlaceholderAddItApp: '添加IT应用程序',
	editItAppOption: '编辑IT应用程序',
	removeItAppOption: '删除IT应用程序',
	viewItAppOption: '查看IT应用程序',
	editScotsISA: '编辑重大交易类别',
	viewScotISA: '查看重大交易类别',
	viewControlISA: '查看控制',
	scotAndRelatedControls: '重大交易类别和控制',
	otherControls: '其他控制',
	controlTypeLabel: '控制类型',

	/*SCOT-ITAPP*/
	addOrRelateItAppPlaceholder: '{identifyRelatedItApps} 或记录 {documentScotHasNoItApps}。',
	identifyRelatedItApps: '识别相关IT应用程序',
	documentScotHasNoItApps: '重大交易类别没有关联IT应用程序',
	correctScotDocumentationPlaceholder: '已将重大交易类别指定为不受IT应用程序支持。应用程序和/或ITDM控制已关联到此{correctScotDocumentation}重大交易类别。',
	correctScotDocumentation: '重新查看没有IT应用程序支持此重大交易类别的指定',
	controlsWithoutRelatedItApps: '控制不具备相关IT应用程序',
	controlsWithRelatedItApps: '具备相关IT应用程序的控制',

	/*AddEditITApplication */
	saveAndCreateNewButtonTitle: '保存和创建新任务',
	instructionalMessage: '添加IT应用程序并选择相关重大交易类别。将相关控制关联至IT应用程序（如适当）。',
	ITApplicationNamePlaceholder: 'IT应用程序名称（必填）',
	scotDropdownPlaceholderText: '选择将与该IT应用程序关联的重大交易类别。',
	selectScotPlaceholderText: '选择将与该IT应用程序关联的重大交易类别。',
	selectControlPlaceholderText: '选择将与该IT应用程序关联的控制。',
	noRelatedScotsPlaceholderText: '无相关的重大交易类别',
	noRelatedControlsPlaceholderText: '无相关的控制',
	CreateSOModelTitle: '添加服务机构',
	CreateSOInstructionalMessage: '在下方输入新的服务机构详情，然后点击“<b>{0}</b>”完成。如想创建其他服务机构，请选择“<b>{1}</b>”。“，//”创建新的服务机构，并关联与之相关的重大交易类别和控制。',
	saveAndCloseLabel: '保存并关闭',
	saveAndCreateLabel: '保存并另外创建',
	SONamePlaceholder: '服务机构名称（必填）',
	SOSelectScotPlaceholderText: '选择该服务机构的相关重大交易类别。',
	SOSelectControlPlaceholderText: '选择该服务机构的相关控制。',
	CreatedSOLabel: '添加的SO',
	createdITAppLabel: '已添加IT应用程序',
	searchNoResultFoundText: '未发现结果',
	searchNoResultsFoundText: '未找到结果',
	iTApplicationNameRequired: 'IT应用程序名称为必填项',
	soNameRequired: '服务机构名称为必填项',
	editITAppDesctiptionLabel: '编辑IT应用程序和关联重大交易类别以及控制',
	editSODescriptionLabel: '请编辑下面的服务机构详细信息，然后点击<b>“保存”</b>完成。',
	viewITApplication: '查看IT应用程序',
	itApplicationName: 'IT应用程序名称',
	serviceOrganizationName: '服务机构名称',
	newItApplication: '新IT应用程序',

	/*Add/Edit ITProcess*/
	itProcessName: 'IT流程名称',
	addItProcessDescription: '创建IT流程',
	addItProcess: '添加IT流程',
	itProcessNameRequired: 'IT流程名称为必填项',
	editItProcess: '编辑IT流程',
	editItProcessDescription: '编辑IT流程',
	viewItProcess: '查看IT流程',
	taskTitle: '了解并记录IT流程：{0}',
	taskDescription: '记录我们对IT流程的了解。附上相关表格作为证据，以支持我们对IT流程的了解。<br />当IT一般控制出现在“任务属性”部分时，执行穿行测试程序以确认我们对IT一般控制的了解，并评估控制的设计和实施。附上所执行程序的证据。',
	newItProcess: '新IT流程',
	noITProcessesFound: '没有识别出IT流程',

	/* IT Process - Task */
	itProcessSearchPlaceholder: '搜索IT流程',
	itProcessHeader: 'IT流程',
	itProcessTasksHeader: '任务',
	itProcessAddUPD: '添加UDP',
	itProcessEditItProcess: '编辑IT流程',
	itProcessRelateUDP: '关联UDP',
	itProcessViewProcess: '查看IT流程',
	itProcessNoDataPlaceholder: '尚未添加任何IT流程。<br />点击{addItProcess}开始。',
	itProcessSourceInstructionalText: '至少一个UDP必须与IT流程关联。{itProcessSourceInstructionalTextCreateUdp}或{itProcessSourceInstructionalTextRelateUdp}',
	itProcessSourceInstructionalTextCreateUdp: '创建新的用户数据报协议',
	itProcessSourceInstructionalTextRelateUdp: '关联现有用户数据报协议。',
	itProcessTargetInstructionalText: '无与该IT流程相关的UDP。',

	/* IT APP IT PROCESSES RELATION*/
	itApplicationHeaderRelate: 'IT应用程序',
	itProcessesHeaderRelate: 'IT流程',
	itAppNoDataPlaceHolderLabel: '未识别出任何IT应用程序。',
	itAppNoDataPlaceHolder: '尚未识别出任何IT应用程序。<br />{identifyItApp}',
	identifyItApp: '识别IT应用程序。',
	itProcessNoDataPlaceHolderRelationLabel: '未识别出任何IT流程。',
	itProcessNoDataPlaceHolderRelation: '尚未识别出IT流程。 <br /> {identifyItProcess}',
	identifyItProcess: '识别IT流程。',
	editItApp: '编辑IT应用程序',
	deleteItApp: '删除IT应用程序',
	drag: '将IT流程拖至相关IT应用程序',
	// editItProcess: 'Edit IT process',
	deleteItProcess: '删除IT流程',
	unassociatedProcess: '{0}无关的流程',
	unassociatedItApplications: '{0}无关的IT应用程序',
	showOnlyUnrelated: '仅显示非相关-{0}',
	searchItProcess: '搜索IT流程',
	searchItApplication: '搜索IT应用程序',
	itProcessesLabel: 'IT流程',
	itApplicationsLabel: 'IT应用程序',
	showAllItAplications: '显示所有IT应用程序',
	showAllItProcesses: '显示所有IT流程',
	relatedToLabel: "所有与<span class='parent-entity-object-name'>{parent}</span>相关的<span class='child-entity-count'>{count}</span> <span class='child-entity-name'>{child}</span>列表 ",

	/* IT Process > IT Risk */
	itProcessItRiskNoDataPlaceholder: '尚未识别出任何IT流程。<br/>{identifyItProcess}',
	itProcessItRiskNoDataPlaceholderTarget: '未识别出任何IT流程。',
	itApplication: 'IT应用程序',
	itGC: 'ITGC',
	addItRiskBtnTitle: '添加IT风险',
	itProcessItRiskUnrelatedITGC: '无关联的ITGC',
	itProcessItRiskUnrelatedITGCUppercase: '未关联IT一般控制',
	itProcessItRiskNoRisksNoControlsPlaceholder: '由于与此IT流程关联的IT应用程序没有经过有效设计控制的相关应用程序和ITDM控制，因此不要求为该IT流程添加IT风险或ITGC。',
	itProcessItRiskNoRisksControlsPlaceholder: '根据已识别出的应用程序和依赖IT的手工控制，对该IT流程进行{itRiskIdentify}。',
	itRiskIdentify: '应识别IT风险',
	itProcessItRiskItProcessContentTitle: 'IT风险和ITGC',
	itProcessItRiskItRiskNoItgcRequiredPlaceholder: '没有应对该IT风险的ITGC。',
	itProcessItRiskItRiskItgcRequiredPlaceholder: '所有已识别的风险必须至少对应一个已识别的ITGC，或IT风险没有ITGC的指定。<br/>识别应对IT风险的{newITGC}或{existingITGC}，或指出存在应对该IT风险的{noItRisksIdentified}。',
	noItRisksIdentified: '无ITGC',
	newITGC: '新的IT一般控制',
	existingITGC: '现有IT一般控制',
	unrelatedItGCModalMessage: '删除不再需要的未关联IT一般控制。',
	unrelatedItGCModalNoDataPlaceholder: '无未关联IT一般控制',
	removeItRisk: '删除IT风险',
	deleteItRiskConfirmation: '是否确认删除该风险因素<b>{0}</b>？此操作将删除IT风险且不可撤销。',
	relateItGcTitle: '关联ITGC',
	relateItGcEntityTitle: 'IT风险',
	relateItGcDescription: '选择与该IT风险相关的ITGC。',
	relateItGcSearchPlaceholder: '搜索ITGC',
	relateItGcShowSelectedOnlyText: '仅显示相关ITGC',
	relateItGcNoDataPlaceholder: '无可用ITGC。创建一个新的ITGC以继续。',
	relateITSPTitle: '关联ITSP',
	relateITSPDescription: '选择与该IT风险相关ITSP。',
	relateITSPSearchPlaceholder: '按ITSP名称搜索',
	relateITSPShowSelectedOnlyText: '仅显示相关ITSP',
	relateITSPNoDataPlaceholder: '无可用ITSP。创建一个新的ITSP以继续。',

	/* IT Process Task Relationship */
	relateUDP: '关联UDP',
	relateUDPDescription: '选择与该IT流程相关的UDP任务。',
	relateUDPListHeaderItemName: '任务名称',
	relateUDPSearchPlaceholder: '按任务名称搜索',
	relateUDPNoResultsFoundPlaceholder: '未发现结果',
	relateUDPCountLabel: '{0}任务',
	relateUDPClose: '关闭',
	relateUDPShowOnlyRelatedTasks: '仅显示相关任务',
	relateUDPNoDataFoundPlaceholder: '无可用任务',
	relateUDPNoDataPlaceHolder: '尚未识别到IT流程',

	/* ITGC test strategy */
	itProcessItRiskItGcWithoutDesignEffectiveness: '不具有设计有效性的ITGC',
	searchItGC: '搜索IT流程',
	itGCNoDataPlaceHolder: '尚未识别出任何IT流程',
	addItRisks: '添加IT风险',
	itDMHeader: 'ITDM',
	itAppHeader: 'IT应用程序',
	itTestHeader: '测试',
	itTestingHeader: '测试',
	itgcHeader: 'ITGCs',
	controlsSelectedHeader: '为测试而选取的控制',
	iTRisksAndITGCs: 'IT风险和ITGC',
	NoITGCForITRiskPlaceholder: 'IT环境中没有应对此IT风险的IT一般控制',
	ITGCsNotIdentifiedRiskNoITGCs: '未识别到应对此风险的IT一般控制。{identifyAnITGC}或指定为{itRiskHasNoITGCs}。',
	identifyAnITGC: '识别IT一般控制',
	itRiskHasNoITGCs: 'IT风险没有IT一般控制',

	/**
	 * IT SO > SCOT
	 */
	searchItSO: '搜索服务机构',
	addItSOBtnTitle: '添加服务机构',
	itSoNoDataPlaceHolder: '未发现服务机构。<br/><a>{identityAnso}<a/>',
	noItSoDataPlaceHolder: '尚未识别出服务机构。',
	identifyAnSo: '识别服务机构',
	soHeader: '服务机构',
	editSO: '编辑服务机构',
	deleteSO: '删除服务机构',
	viewSO: '查看服务机构',
	controlRelatedToSO: '与SO相关的控制',

	/**
	 * Manage IT SP
	 */
	addITSP: '添加ITSP',
	searchPlaceholderManageITSP: '搜索IT流程',
	noManageITSPDataPlaceholder: '尚未识别出IT流程',
	itRiskColumnHeader: 'IT风险',
	itDesignEffectivenessHeader: '设计有效性',
	itTestingColumnHeader: '测试',
	itGCColumnHeader: 'ITGC',
	itSPColumnHeader: 'ITSP',
	searchClearButtonTitle: '清空',
	itProcessItRiskUnrelatedITSP: ' 无关联的ITSP',
	manageITSPUnrelatedITSPUppercase: '非关联ITSP',
	unrelatedITSPModalMessage: '删除不再需要的非关联ITSP。',
	unrelatedITSPModalNoDataPlaceholder: '没有非关联ITSP',
	noITGCPlaceholderMessageFragment1: '所有已识别风险须至少对应一个已识别ITGC，或得到IT风险无对应的ITGC的指定: ',
	noITGCPlaceholderMessageFragment2: '识别与现有ITGC关联的新ITGC或者指明该IT风险没有相应的ITGC。',
	noITGCPlaceholderMessageFragment3: '新的ITGC',
	noITGCPlaceholderMessageFragment4: '或',
	noITGCPlaceholderMessageFragment5: '现有ITGC',
	noITGCPlaceholderMessageFragment6: '可应对IT风险，或表明',
	noITGCPlaceholderMessageFragment7: '不存在应对IT风险的',
	noITGCPlaceholderMessageFragment8: 'ITGC',
	addNewITSP: '添加新ITSP',
	addExistingITSP: '添加现有ITSP',
	noITSPPlaceholderMessageFragment1: '如果我们将ITGC评估为无效，或确定不存在应对IT风险的ITGC，我们执行IT实质性测试程序，以合理保证无效ITGC的相关IT流程内的风险未被利用。',
	noITSPPlaceholderMessageFragment2: '识别新ITSP',
	noITSPPlaceholderMessageFragment3: '或者',
	noITSPPlaceholderMessageFragment4: '与现有ITSP关联',
	noITSPPlaceholderMessageFragment5: '。',
	noITGCsExitForITRisk: '没有应对IT风险的IT一般控制。',
	noITSPExitForITRisk: '没有识别到针对IT风险的ITSP。',
	manageITSPItemExpansionMessage: 'IT风险',
	noITGCExists: '没有应对该IT风险的ITGC。',
	iTGCName: 'IT一般控制名称',
	itSPName: 'ITSP名称',
	operationEffectiveness: '运行有效性',
	savingLabel: '保存中',
	deletingLabel: '删除中',
	removingLabel: '移除中',
	itFlowModalDescription: '前往{itSummaryLink}编辑/删除不再适用项目的对象。',
	itSummaryLink: 'IT汇总界面',
	manageITSPYes: '是',
	manageITSPNo: '否',

	understandITProcess: '了解IT流程',
	activity: '活动',
	unsavedPageChangesMessage: '你有未保存变动，如选择继续，将丢失这些变动。是否确认离开此页？',
	unsavedChangesTitle: '未保存变动',
	unsavedChangesLeave: '离开此页',
	unsavedChangesStay: '留在此页',

	notificationDownErrorMessage: '通知功能暂不可用。请刷新页面并重试。如果此消息仍然存在，请联系帮助台。',
	notificationUpbutSomeLoadingErrorMessage: '出现技术错误，通知功能无法运行。请刷新页面并重试。',
	markCompleteError: '页面中显示的所有文档均需要至少一名编制人和一名复核人签字。',
	markCompleteDescription: '所有文档必须由至少一名编制人和一名复核人签字，方可将活动标记为已完成。',
	lessthan: '少于',
	openingFitGuidedWorkflowFormError: '无法打开EY Canvas FIT促进工具表格',
	timeTrackerErrorFallBackMessage: '时间跟踪功能暂时无法使用。请刷新页面并重试。如果此消息仍然存在，请联系帮助台。',
	timeTrackerLoadingFallbackMessage: '时间跟踪功能暂时无法使用。该功能近期将可使用。',
	priorPeriodRelateDocument: '关联上期证据',
	selectedValue: '已选值',
	serviceGateway: '服务网关',
	docNameRequired: '名称不得为空',
	docInvalidCharacters: '名称中不能包含： */:<>\\?|"',
	invalidComment: '无法添加批注。如果选择了列表中的多个项目，请只选择一个项目，然后重试。如果问题仍然存在，请刷新页面并重试或联系帮助台。',
	inputInvaildCharacters: '输入内容不可含有下列字符：*/:<>\\?|"',

	// FIT Navigation panel
	relatedActivities: '相关活动',
	backToRelatedActivities: '返回相关活动',
	backToMainActivities: '返回主要活动',
	formOptions: '表格选项',

	// FIT Sharing
	shareActivity: '共享活动',
	shareLabel: '共享',
	shareInProgress: '共享中',
	manageSharing: '共享此活动需要“管理EY Canvas FIT促进工具的共享”用户权限。前往“管理小组”页面以管理权限或联系项目组的其他成员',
	dropdownPlaceholderSA: '选择要共享的项目',
	fitSharingModalInfo: '将此活动与同一项目或同一工作区中另一项目的一个或多个活动共享。如果所选活动尚未共享，则下方所选活动中的回答将被覆盖。如果活动已共享，则只能选择一个活动，并且此活动中的回答将被覆盖。',
	lastModifiedDate: '最后修改日期： ',
	noActivityToShare: '无可共享活动',
	activityNotShared: ' {0} 未共享',
	activityShareSuccessfull: ' {0} 已成功共享',
	sharedWithAnotherFITActivity: '此活动已共享至其他活动',
	sharedActivityWithAnotherCanvas: '与另一个EY Canvas FIT促进工具共享活动',
	shareActivityModalTitle: '共享活动',
	showRelationshipsTitle: '显示关系',
	shareActivityEngagement: '项目',
	shareActivityRelationshipsModalTitle: '共享活动关系',
	shareActivityWorkspaceHeading: '此活动与同一工作区内的下列项目和相关活动共享。',
	shareModalOkTitle: '共享',
	shareModalContinueLabel: '继续',
	selectedActivityInfoLabel: '所选项目最后修改日期： ',
	noSharedActivityInfoLabel: '此项目没有同类型的其他文档可以共享。',
	alreadyHasSharedActivityInfoLabel: '所选活动已共享至其他活动。若共享当前活动，则所选活动的回复将同步到当前活动。',
	selectActivityResponsesForSharingLabel: '选择使用哪个文档的回复去替换另一个文档的回复： ',
	selectActivityResponsesForCurrentRadioLabel: '将当前文档的回复共享至上方所选文档',
	selectActivityResponsesForSelectedRadioLabel: '将上方所选文档的回复共享至当前文档',
	selectActivityResponsesWarningEarlierTimeLabel: "The current activity was modified at an earlier time compared to the selected engagement's activity. Please consider this before confirming the sharing option's below the table.",
	selectActivityResponsesWarningModifiedMoreRecentlyLabel: '与所选文档的活动相比，当前活动的修改时间更近。请在确认上方共享选择前考虑这一点。',
	selectActivityUnsuccessfulMessage: '无法共享。请重试。如果问题仍然存在，请联系安永帮助台。',
	otherEngagemntDropdownlabel: '工作区中的其他项目： ',
	documentSearchPlaceholder: '搜索文档',
	showOnlySelected: '仅显示选定项',

	//FIT Copy
	copyLabel: '复制',
	copyActivity: '复制活动',
	copyInProgress: '复制中',
	fitCopyModalInfo: '将此活动的回复复制到同一项目或同一工作区中另一项目的一个或多个活动。',
	dropdownPlaceholderCA: '选择要复制到的项目',
	noCopyActivityInfoLabel: '本项目没有同一类型的文档可复制。',
	copyActivityHoverLabel: '此活动已与其他活动共享，无法复制',
	copyActivityWarningEarlierTimeLabel: '与所选项目的活动相比，当前活动在较早前被修改。请在确认复制选项之前考虑这一点。',

	//Unlink
	unlinkModalTitle: '删除活动链接',
	unlinkModalDescription: '是否确定删除选定活动的链接?',
	unlinkLabel: '删除链接',
	insufficientPermissionsLabel: '权限不足',
	unlinkFailMessage: '链接删除失败。请刷新并重试。如果问题仍然存在，请联系安永帮助台。',
	unlinkSuccessfulMessage: '成功删除链接',
	unlinkInProgressLabel: '删除链接中',
	unlinkError: '删除链接错误',
	unlinkInProgressInfo: '正在取消链接。这可能需要15分钟完成。取消链接完成后，需要关闭并重新打开此表格。',

	/** Manage scot modal labels */
	scotName: '重大交易类别名称',
	scotCategory: '重大交易类别的类别',
	estimate: '估计',
	noScotsAvailablePlaceHolder: '无可用重大交易类别。添加新重大交易类别以开始',
	addScotDisableTitle: '若要添加新重大交易类别，请填写重大交易类别所有详细信息',
	deleteScotTrashLabel: '删除重大交易类别',
	undoDeleteScotTrashLabel: '撤销删除',
	scotNameValidationMessage: '重大交易类别名称为必填项',
	scotCategoryValidationMessage: '重大交易类别的类别为必填项',
	scotWTTaskDescription: '<p>对于所有常规和非常规重大交易类别以及重大披露流程，我们通过执行穿行测试程序来确认我们对每个期间的了解。此外，对于PCAOB审计，我们对估计重大交易类别执行穿行测试程序。<br/>对于当我们采取控制依赖策略时的所有重大交易类别以及应对特别风险的控制，我们确认相关控制得到适当设计和实施。我们确认，我们采取控制依赖策略的决策仍然适当。<br/><br/>我们得出结论认为，我们的文档记录准确描述了重大交易类别的运行，并且我们已经识别出了所有适当的可能出错项，包括使用IT产生的风险和相关控制（如适用）。<br/><br/>对于我们使用唯实质性策略时的估计重大交易类别，我们根据我们的实质性程序确定对估计重大交易类别的了解是否适当。</p>',

	relate: '关联',
	unrelate: '取消关联',
	related: '已关联',
	relatedSCOTs: '已关联重大交易类别',
	thereAreNoSCOTsIdentified: '没有识别出重大交易类别',
	selectSCOTsToBeRelated: '选择将关联的重大交易类别',

	//OAR Tables
	OARBalanceSheet: '资产负债表',
	OARIncomeStatement: '利润表',
	OARCurrentPeriod: '分析日期',
	OARAmountChangeFrom: '更改前',
	OARPercentageChangeFrom: '变动前百分比',
	OARNoDataAvailable: '无可用数据。复核第{0}页并导入数据以继续。',
	OARAnnotationLabel: '点击以复核出现非预期变化或预期变化未发生的原因',
	OARAnnotationSelectedIcon: '记录发生预期外变化或预期变化未发生的原因',
	OARAnnotationModalTitle: '注解',
	OARAnnotationModalPlaceholder: '记录异常项、非预期变化或预期变化未发生的情况。',
	OARWithAnnotationLabel: '非预期变化的文档记录',
	OARAnnotation: '注解',
	OARAccTypeWithAnnotationCountLabel: '账户类型内的{0}个注解',
	OARSubAccTypeWithAnnotationCountLabel: '账户子类型内的{0}个注解',
	OARColumnA: 'A',
	OARColumnB: 'B',
	OARColumnC: 'C',
	OARComparative1Period: '比较日期1',
	OARComparative2Period: '比较日期2',
	OARExpand: '展开账户类别',
	OARCollapse: '收起账户类别',
	OARHelixNavigationLink: '登录EY Helix获取额外信息。',
	OARPrintNoDataAvailable: '无可用数据',
	OARAdjustedBalance: '调整后余额',
	OARLegendLabel: '带*的值表示包含调整。前往“调整”模块以了解更多详细資訊。',
	OARAccountType: '账户类型',
	astrixLabel: '*',

	//OAR Helix integration
	helixIntegrationModalDescription: '此文本待定义',
	OSJETabText: '记账分录对方科目',
	activityAnalysisTabText: '活动分析',
	preparerAnalysisTabText: '编制人分析',
	accountMetricsTabText: '账户指标',
	noAnalyticsData: '没有可用于显示的分析工具',

	printActivitiesTitle: '打印活动',
	printActivitiesModalInfo: '请选择要包含的活动。',
	printActivitiesModalConfirmButton: '编制PDF',
	printActivitiesDropdownLabel: 'FIT活动',
	printActivitiesAll: '全部',
	oarSetupText: '前往第{0}页以链接或配置EY Helix项目',
	helixNotAvailable: '您的项目不可使用EY Helix。',
	dragDropUploadPlaceholder: '拖放一个或多个文档，或点击<span>{addDocument}</span>。',

	noTaskAssociatedToastMessage: 'Canvas表格在临时文件中，因此所添加文档也已添加到临时文件中',

	// chart labels.
	assets: '资产',
	liabilities: '负债',
	equity: '权益',
	revenues: '收入',
	expenses: '费用',
	noAccountsAvailable: '没有可添加的账户',

	// ALRA
	ALRAFilterByAccount: '按账户筛选',
	ALRANoRecords: '未找到结果',
	ALRAAssertions: '认定',
	ALRAInherent: '固有风险因素',
	ALRAHigher: '较高风险因素',
	ALRAAccountDisclosure: '账户/披露',
	ALRAType: '类型',
	ALRAName: '名称',
	ALRARisks: '风险',
	ALRAC: 'C',
	ALRAEO: 'E/O',
	ALRAMV: 'M/V',
	ALRARO: 'R&O',
	ALRAPD: 'P&D',
	ALRAR: 'R',
	ALRANoRisksAssociated: '没有与此账户相关的风险',
	ALRAAccountsDisclosureName: '账户/披露名称',
	ALRAHigherRisk: '较高风险',
	ALRAHigherInherentRisk: '较高固有风险',
	ALRAHigherRiskCode: 'H',
	ALRALowerRisk: '较低风险',
	ALRALowerInherentRisk: '较低固有风险',
	ALRALowerRiskCode: 'L',
	ALRALimitedRiskAccount: '该账户已被识别为有限风险账户',
	ALRAInsignificantRiskAccount: '该账户已被识别为不重大账户',
	ALRADesignations: '指定',
	ALRABalances: '余额',
	ALRADesignation: '指定',
	ALRAAnalysisPeriod: '分析日期',
	ALRAxTE: '可容忍误差的倍数',
	ALRAPercentageChangeFrom: '变动百分比（%）',
	ALRAPriorPeriodDesignation: '上一期间的指定',
	ALRAPriorPeriodEstimate: '上一期间的估计',
	ALRAComparativePeriod1: '比较日期1',
	ALRAComparativePeriod2: '比较日期2',
	ALRASelectUpToThreeOptions: '最多选择3项',
	ALRASelectUpToTwoOptions: '最多选择2个选项',
	ALRAValidations: '验证',
	ALRANoSignOffs: '无签字',
	ALRAIncompleteInherentRisk: '固有风险不完整',
	ALRARelatedDocuments: '关联的文档',
	ALRAGreaterExtent: '较大范围',
	ALRALesserExtent: '较小范围',
	ALRARiskRelatedToAssertion: '风险已关联',
	ALRAContributesToHigherInherentRisk: '风险已关联，构成较高固有风险',

	// Assess inherent risk
	HigherRiskAssertionWithoutRisksThatContributesToTheHigherInherentRisk: '认定在没有至少一种促成较高固有风险的风险的情况下，被确认为具有较高固有风险。请关联风险，并指出是哪些风险导致认定具有较高固有风险。',

	//MEST - Multi-entity account Execution Type selection listing
	account: '账户',
	taskByEntity: '各实体的任务',
	bodyInformation: '若要保存任何更改，您须点击下方的导入内容。',

	/*user search component*/
	seachInputRequired: '搜索输入项为必填',
	nameOrEmail: '姓名或邮箱',
	emailForExternal: '邮箱',
	noRecord: '未找到结果',
	userSearchPlaceholder: '请输入姓名或邮箱，然后按回车键查看结果。',
	userSearchPlaceholderForExternal: '请输入邮箱，然后按回车键查看结果。',
	clearAllValues: '清除所有值',
	inValidEmail: '请输入有效的邮箱',

	//reactive frame
	maxTabsLocked: '已达到允许的最大标签页数量。请取消固定一个标签页并关闭，以打开一个新标签页。',
	openInNewTab: '在新标签页中打开',
	unPinTab: '取消固定标签页',
	pinTab: '固定标签页',
	closeDrawer: '关闭抽屉菜单',
	minimize: '最小化',

	accountHeader: '账户',
	sCOTSummaryAccountNoDataLabel: '每个重大交易类别必须至少与一个重大账户或揭露相关联。请选择一项现有重大账户或揭露关联到此重大交易类别',
	sCOTSummaryNoDataLabel: '尚未创建重大交易类别',
	scotSearchNoResultsFound: 'No results found',
	scotSummary225TabsName: {
		[0]: {
			label: '按账户显示'
		},
		[1]: {
			label: '按重大交易类别显示'
		}
	},

	// Display Account Balances
	currentPeriodAccountBalance: '当期账户余额： ',
	priorPeriodAccountBalance: '上期账户余额： ',

	ALRANoResults: '未找到结果。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	associatedRomsCount: '关联风险总数：{0}',
	alraMessage: '账户指定回复与“编辑账户和披露”中的指定不一致',
	estimateCategoryResponseNotAlignedToDesignation: '回答的估计类别与“编辑估计”中的指定不一致',


	// Analytics Overview
	analyticsOverviewTitle: '分析工具概述',
	noSignificantAccountRecords: '未创建重大账户。',
	noSignificantAccountMapped: '没有對應到所选实体的重大账户。',
	noLimitedAccountMapped: '没有對應到所选实体的有限风险账户。',
	openAnalyticDocumentation: '打开分析工具文档记录',
	openLimitedRiskAccountDocumentation: '打开有限风险账户文档记录',
	associatedSCOTs: '关联的重大交易类别： ',
	analysisPeriodLabel: '分析日期{0}',
	analysisPeriodChangeLabel: '变动% {0}',
	xTELabel: 'xTE',
	risksLabel: '风险',
	comparativePeriod1: '比较日期1{0}',
	analysisPeriodTitle: '分析日期',
	analysisPeriodChangeTitle: '相较于…的变动比例（%）',
	comparativePeriodTitle: '比较日期1',
	noAccountAvailable: '无可用账户',

	// Estimates
	titleEstimateCategory: '估计类别',
	titleRisks: '风险',

	voiceNoteNotAvailable: '抽屉菜单视图下无法使用语音注释和录屏。请切换到全屏视图以使用这些功能。',

	financialStatementType: {
		[1]: {
			label: '资产'
		},
		[2]: {
			label: '流动资产'
		},
		[3]: {
			label: '非流动资产'
		},
		[4]: {
			label: '负债'
		},
		[5]: {
			label: '流动负债'
		},
		[6]: {
			label: '非流动负债'
		},
		[7]: {
			label: '权益'
		},
		[8]: {
			label: '收入'
		},
		[9]: {
			label: '费用'
		},
		[10]: {
			label: '营业外收入/（费用）'
		},
		[11]: {
			label: '其他综合收益'
		},
		[12]: {
			label: '其他'
		},
		[13]: {
			label: '账户类型'
		},
		[14]: {
			label: '账户子类型'
		},
		[15]: {
			label: '账户类别'
		},
		[16]: {
			label: '账户子类别'
		},
		[17]: {
			label: '净（利润）/亏损'
		}
	},
	accountTypes: {
		[1]: {
			label: '重大账户'
		},
		[2]: {
			label: '有限风险账户'
		},
		[3]: {
			label: '不重大账户'
		},
		[4]: {
			label: '其他账户'
		},
		[5]: {
			label: '重大披露'
		}
	},
	noClientDataAvailable: '无可用数据',

	analysisPeriod: '分析日期',
	comparativePeriod: '比较日期',
	perchangeLabel: '变动百分比',

	entityCreateAccountLabel: '创建账户与披露',
	insignificantAccount: '非重大账户',
	noAccountRecords: '尚未识别到账户',
	noAccountsForEntity: '未将任何账户或披露映射到选定实体。',
	noLimitedRiskAccountRecords: '无可用有限风险账户。',
	createAccount: '创建账户',
	createDocument: '创建文档',
	noAccountResults: '未识别出账户。',
	createGroupInvolvementDocument: '创建参与表格',
	chooseVersionsToCompare: '选择版本进行比较',
	noTrackChangesOption: '无修订版本可用',
	trackChangesDefaultMessage: '从“选择版本进行比较”下拉菜单中选择版本，继续下一步。',
	whichRiskContributeToHigherRisk: '哪些风险促成了较高风险认定？',

	//multi-entity Entity List
	createMultiEntity: '新实体',
	editMultiEntity: '创建实体',
	noEntitiesAvailableCreateNewLink: '点击此处',
	noEntitiesAvailable: '未创建实体。{noEntitiesAvailableCreateNewLink}以开始',
	noEntitiesFound: '未找到结果',
	createMultiEntityProfile: '创建实体配置文档',

	createEntity: '创建实体',
	includeEntities: '多实体列表中必须至少包含一个实体。｛createEntity｝以开始。',
	//multi-entity table
	multiEntityCode: '实体标准索引',
	multiEntityName: '实体名称',
	multiEntityGroup: '实体分组',
	multiEntityActions: '操作',
	relateMultiEntityUngrouped: '未分组',
	selectAll: '选择全部',
	entitiesSelected: '选定的实体',
	entitySelected: '选定的实体',
	meNoEntitiesAvailable: '无可用实体',
	meSwitchEntities: '更改实体',
	meSelectEntity: '选择实体',
	allEntities: '所有实体',
	noEntitiesIdentified: '未识别到实体',
	contentDeliveryInProcessMessage: '正在交付内容。交付该内容需要10分钟。',
	importContent: '导入内容',
	profileSubmit: '提交配置文档',
	importPSPs: '导入主要实质性程序',
	contentUpdateInsufficienRolesLabel: '角色权限不足，无法更新内容。请联系项目管理员以获得足够的权限。',
	// MEST Switcher
	meEntitySwitcher: '切换实体',
	//Error Boundary
	errorBoundaryMessage: '出现错误。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	sectionName: '章节名称',
	maxLength: '文本不能超过 {number}个字符。',
	required: '必填',
	yearAgo: '年前',
	yearsAgo: '年前',
	monthAgo: '月前',
	monthsAgo: '月前',
	weekAgo: '周前',
	weeksAgo: '周前',
	daysAgo: '天前',
	dayAgo: '天前',
	today: '今日',
	todayLowercase: '今天',
	yesterday: '昨日',
	approaching: '处理中',

	associatedToInherentRiskFactor: '已关联至固有风险因素',

	createMissingDocument: '创建缺失文档',
	createMissingDocumentBody: '点击“确认”为当前存在文档缺失的相关项目创建文档。',
	documentCreationSuccessMsg: '文档创建中。请刷新页面进行更新。',

	noRisksRelatedToAssertion: '无此账户认定相关风险。',

	noAssertionsRelatedToAccount: '无与此账户相关的认定',

	sharing: '共享',

	risksUnrelatedToAccountAssertion: '与账户认定无关的风险',
	cantCompleteTask: '与相关任务关联的文档中存在签字缺失。打开任务，将存在缺失的文档完成，然后重新点击标记完成。',
	cantCompleteTasksTitle: '无法标记完成',
	ok: '确认',
	documentsEngagementShareLabel: '选择项目中要共享的文档',
	documentsEngagementCopyLabel: '选择项目中要复制的文档',
	lastModifiedon: '最后修改日期',
	newAccountAndDisclosure: '新账户和披露',
	newAccountORDisclosure: '新账户或披露',

	externalDocuments: '外部文档',
	noExternalDocumentsAvailable: '无可用外部文档',
	addExternalDocuments: '添加外部文档',
	relateExternalDocuments: '关联外部文档',

	helixNotMappedToAccount: 'EY Helix数据未匹配到此账户。请更新匹配并重新导入数据以继续。',
	trackChangesNotAvailableForSpecialBodyDisplayMessage: '追踪修订功能不适用于以下回复。',
	noDocumentRelatedObjectsApplicable: '对象不需要与此指导工作流表格关联',
	helixViewerLoader: '正在加载Helix Viewer……',
	trackChangesViewDefaultMessage: '某些回复没有追踪修订功能。这一点会通过相关活动详细信息中的以下消息指出：“追踪修订功能不适用于以下回复。”因此，虽然下方没有显示变更通知，但并不表明没有发生变更。',

	//Relate task modal
	relateTasksTitle: '关联任务',
	taskLocationLabel: '任务位置',
	relateTaskInstructionalText: '请添加或删除应与文档关联的任务。如果文档是证据，则将其从最后一个任务中删除会将其移动到临时文件中。',
	noResultFound: '未找到结果。',
	relatedTaskCounter: '{0}任务',
	relatedTasksCounter: '{0}任务',
	onlyShowRelatedTasks: '仅显示关联任务',
	relateTaskName: '任务名称',
	relateTaskType: '类型',

	/*Relate Entities*/
	relateEntitiesTitle: '关联实体',
	relateEntitiesSearchPlaceholder: '输入以按名称搜索',
	relateEntitiesName: '实体名称',
	relateEntitiesIndex: '实体标准索引',
	relatedEntitiesCounter: '{0} 个实体',
	relatedEntityCounter: '{0} 个实体',
	onlyShowRelatedEntities: '仅显示相关实体',
	entity: '实体',

	step01: '步骤01',
	step02: '步骤02',
	shareActivityStep1Description: '选择项目和文档',
	shareActivityStep2Description: '选择应用哪个文档回复替换其他回复',
	documentsShareLabel: '将下面所选文档的回复共享给其余文档。',
	selectedActivity: '所选活动',
	sharedHoverLabel: '此活动已与其他活动共享。共享此活动会将此活动的回复同步到所有共享活动',
	noAssertionsRelatedLabel: '没有关联认定。',

	// Bulk mark complete:
	bulkMarkCompleteInstructionalText: '以下是与此表格关联的所有任务。若要将选定任务标记完成，所有文档必须至少有一名编制人和一名复核人签字。',
	bulkMarkCompleteEngagementColumn: '项目',
	bulkMarkCompleteDocumentsMissingSignOffs: '缺少签字。点击 {bulkMarkCompleteMissingSignOffsetClickableText}进行签字。',
	bulkMarkCompleteMissingSignOffsClickableText: '此处',
	bulkMarkCompleteNoAccessToEngagement: '您无权访问此任务所在的项目',
	bulkMarkCompleteInProgressMessage: '正在处理。最长可能需要十分钟时间。请刷新以获取更新',
	bulkMarkCompleteRelatedDocumentsModalTitle: '文档签字',
	bulkMarkCompleteFilterUnreadyTasks: '仅显示缺少文档签字的任务。',
	bulkMarkCompleteNotAllowedModalTitle: '无法标记完成',
	bulkMarkCompleteNotAllowedModalDescription: '您需要至少选择一个任务标记完成',
	bulkMarkCompleteRelatedDocumentsModalDescription: '为将所选任务标记完成，所有文档必须至少有一名编制人和一名复核人签字。',
	bulkMarkCompleteRelatedDocumentsModalRefreshSignoffs: '请刷新签名和注释',
	selectedTaskCounter: '({0})选定任务',
	selectedTasksCounter: '({0})选定任务',

	// Mark complete (old):
	markCompleteNotAllowedModalDescription: '与相关任务关联的文档中缺少签字。打开任务，将缺失的文档完成，然后重新点击标记完成。',
	markCompleteInstructionalText: '所有文件必须由至少一名编制人和一名复核人签字，以将活动标记为已完成',

	// Adobe Analytics
	aaCookieConsentTitle: '欢迎访问',
	aaCookieContentPrompt: '您是否允许使用Cookie？',
	aaCookieConsentExplanation: '<p>除了运营本网站所需的cookie外，我们还使用以下类型的Cookie来改善您的用户体验和我们的服务：<strong>功能cookie</strong>，提升用户体验（如记住设置），<strong>性能cookie</strong>，监测网站性能，改善用户体验，<strong>广告/定向cookie</strong>，由第三方设置，我们与该第三方共同执行广告活动，为您提供与您相关的广告信息。</p> <p>您可以查阅我们的<a target=“_blank”href=“https://www.ey.com/en_us/cookie-policy“>cookie政策</a>了解更多信息。</p>',
	aaCookieConsentExplanationWithDoNotTrack: '<p>除了运营本网站所需的Cookie外，我们还使用以下类型的cookie来改善您的用户体验和我们的服务：<strong>功能cookie</strong>，提升用户体验（如记住设置），<strong>性能cookie</strong>，监测网站性能，改善用户体验，<strong>广告/定向cookie</strong>，由第三方设置，我们与该第三方共同执行广告活动，为您提供与您相关的广告信息。</p><p>我们已检测到您的浏览器开启了“请勿追踪”的设置；因此，广告/定向cookie将自动禁用。</p><p>您可以查阅我们的<a target=“_blank”href=“https://www.ey.com/en_us/cookie-policy“>cookie政策</a>了解更多信息。</p>',
	aaCookieConsentDeclineOptionalAction: '我拒绝可选cookie',
	aaCookieConsentAcceptAllAction: '我接受所有cookie',
	aaCookieConsentCustomizeAction: '自定义cookie',
	aaCookieConsentCustomizeURL: 'https://www.ey.com/en_us/cookie-settings',

	// Cookie Settings
	cookieSettings: {
		title: 'cookie设置',
		explanation: '请您在ey.com和“我的安永”平台上提供cookie使用许可。请选择下面列出的一种或多种cookie类型，然后点击保存。有关cookie类型及其用途的详细信息，请参阅下面的列表。',
		emptyCookieListNotice: '此应用中未使用此类别的Cookie',
		nameTableHeader: 'cookie名称',
		providerTableHeader: 'cookie提供商',
		purposeTableHeader: 'cookie用途',
		typeTableHeader: 'cookie类型',
		durationTableHeader: 'cookie有效期',
		formSubmit: '保存我的选择',
		requiredCookieListTitle: '必需Cookie',
		functionalCookieListTitle: '功能Cookie',
		functionalCookieAcceptance: '我接受以下功能cookie',
		functionalCookieExplanation: '功能cookie，可以帮助我们增强您的用户体验（例如，通过记住您选择的任何设置）。',
		performanceCookieListTitle: '性能cookie',
		performanceCookieAcceptance: '我接受以下性能cookie',
		performanceCookieExplanation: '性能Cookie，可以帮助我们监测网站的性能并改善您的用户体验。在使用性能cookie时，我们不存储任何个人数据，仅使用通过这些cookie以汇总和匿名形式收集的信息。',
		advertisingCookieListTitle: '定向cookie',
		advertisingCookieAcceptance: '我接受以下广告/定向cookie',
		advertisingCookieExplanation: '广告/定向cookie，可以帮助我们跟踪用户活动和会话，以便提供更个性化的服务。（在广告cookie的情况下）cookie由第三方设置，我们与该第三方共同执行广告活动，为您提供与您相关的广告信息。',
		doNotTrackNotice: '我们已检测到您的浏览器开启了“请勿追踪”的设置；因此，广告/定向cookie将自动禁用。',
	},
	accountFormsMissing: ' {0} 账户缺少账户表格',
	createAccountForms: '创建账户表格',
	createAccountFormsDescription: '点击“确认”为当前存在缺失文档的相关项目创建文档。',
	createMissingDocuments: '相关账户当前缺少文档',
	accountDocumentsCreated: '内容交付中。交付该内容需要10分钟。',

	evidenceMissingPICSignoffs: '缺少项目负责合伙人签字的证据',
	evidenceMissingEQRSignoffs: '缺少项目质量复核人签字的证据',
	evidenceMissingPICEQRSignoffs: '缺少项目负责合伙人和/或项目质量复核人签字的证据',
	evidenceMissingPICSignoffRequirements: '缺少项目负责合伙人签字要求的证据',
	evidenceMissingEQRSignoffRequirements: '缺少项目质量复核人签字要求的证据',
	evidenceMissingPICEQRSignoffRequirements: '缺少项目负责合伙人和/或项目质量复核人签字要求的证据',
	evidenceMissingSignoffs: '证据无签字',

	// Bulk task relate
	bulkTaskRelateFailureMessage: '某些选定的文档无法与选定的任务关联。',
	/*endoflabels*/
	evidenceMissingPreparerOrReviwerSignoffs: '文档上传——缺少编制人或复核人签字',

	manageITProcess: '管理IT流程',
	manageITRisk: '管理技术风险',
	manageITControl: '管理IT控制',
	manageITSP: '管理IT实质性程序',
	manageITApp: '管理IT应用程序',
	manageSCOT: '管理重大交易类别',
	addAresCustomDescription: '选择要添加到此指导性工作流表格的内容类型，输入详细信息，然后点击保存。',

	documentImportSuccess: '已成功创建 {0} 。可能需要十分钟完成交付。',
	documentImportFailure: '文档创建失败。请刷新或稍后再试。如果问题仍然存在，请联系帮助台。',
	formNotAvailable: '未找到匹配的Canvas表格。',
	selectTask: '选择与指引相关的任务',
	canvas: 'Canvas',
	selectEngagement: '选择项目',

	//Modal Manage sub-scope
	manageSubScopeTitle: '管理子范围',
	manageSubScopeDescription: '创建新的子范围，或编辑或删除下方现有子范围。',
	addSubScope: '添加子范围',
	subScopeName: '子范围名称',
	knowledgeScope: '知识范围',
	subScopeAlreadyExist: '子范围名称已存在',
	subScopes: '子范围',
	notAvailableSubScopes: '无可用子范围。',
	SubScopeNameValidation: '子范围名称长度超过255个字符。',

	//CRA Summary
	manageAccount: '管理账户',
	newAccount: '新账户',

	noRelatedObjectITProcessFlow: '无关联对象。关联一个对象以开始。',

	//Add New Flow Chart Steps
	flowChartNewSteps: {
		newStepTitle: '新步骤',
		placeholderText_1: '在下方输入步骤详细信息并点击',
		placeholderText_2: ' “保存并关闭”',
		placeholderText_3: ' 完成。新建步骤时，点击',
		placeholderText_4: ' “保存并新建”。',
		columnLabel: '列（必填）',
		counterOf: '的',
		counterChar: '字符',
		stepNameLabel: '步骤名称（必填）',
		errorMsgStepNameRequired: '步骤名字为必填项',
		stepDescLabel: '步骤描述（必填）',
		stepDescPlaceholder: '输入步骤描述',
		errorMsgStepDescRequired: '步骤描述为必填项。',
		required: '必填',
		errorMsgStepDescExceedMaxLength: '步骤描述超过了字符上限',
		buttonCancel: '取消',
		buttonSaveAndClose: '保存并关闭',
		buttonSaveAndCreateAnother: '保存并新建',
		errorMsgColumnRequired: '列是必填项',
		headerNameForWCGW: '可能出错项名称',
		headerNameForControl: '控制名称',
		headerNameForITApp: 'IT应用程序名称',
		headerNameForServiceOrganisation: '服务机构名称',
		relateLabelForWCGW: '关联可能出错项',
		relateLabelForControl: '关联控制',
		relateLabelForITApp: '关联IT应用程序',
		relateLabelForServiceOrganisation: '关联服务机构',
		designEffectiveness: '设计有效性',
		testing: '测试',
		lowerRisk: '较低风险',
		wcgwNoRowsMessage: '没有关联可能出错项。点击{0}开始。',
		controlNoRowsMessage: '没有关联控制。点击{0}开始。',
		itAppNoRowsMessage: '没有关联IT应用程序。点击{0}开始。',
		serviceOrganisationNoRowsMessage: '未关联服务机构。单击 {0} 开始',
		wgcwTabLabel: '可能出错项',
		controlsTabLabel: '控制',
		itAppsTabLabel: 'IT应用程序',
		serviceOrganisationTabLabel: '服务机构',
		connectionSuccessMessage: '已成功创建连接。',
		connectionFailedMessage: '无法建立连接。请重试。',
		selfConnectFailMessage: '来源和目标不能相同。',
		connectionDuplicateMessage: '连接已存在。',
		connectionDeleteSuccessMessage: '已成功删除连接。',
		connectionDeleteFailMessage: '无法删除连接。请重试。',
		editStepFailMessage: '无法编辑该步骤。请重试。',
		flowchartStepGetByIdFailMessage: '此步骤无效，请刷新并重试。',
		flowchartStepGetByIdFailureMessage: '此流程图步骤不再可用。请刷新页面并重试。如仍有错误，请联系帮助台。',
		newStepFailureMessage: '无法创建新步骤。请重试。',
		deleteConnector: '删除连接工具',
		edgeConnectorOptions: '连接器选项',
		edgeStartPoint: '起点',
		edgeEndPoint: 'End Point',
		relateDocumentToFlowchartStepError: '此时无法完成操作。请更新页面并重试。如果问题仍然存在，请联系帮助台。',
		relateDocumentsOrObjects: '关联文档或对象',
		thisstep: '到这一步'
	},

	flowChartWCGW: {
		wcgwsCounter: '{0} 个可能出错项',
		wcgwCounter: '{0} 个可能出错项',
		headerName: '关联可能出错项',
		showOnlyRelatedText: '仅显示相关项目',
		noResultsFound: '未找到结果'
	},

	flowchartITAPPSO: {
		showOnlyRelatedText: '仅显示相关项目',
		noResultsFound: '未找到结果'
	},

	flowChartITApplication: {
		itApplicationsCounter: '{0} 个IT应用程序',
		itApplicationCounter: '{0} 个IT应用程序',
		headerName: '关联IT应用程序',
		columnName: 'IT应用程序名称',
		noDataFound: '未找到IT应用程序'
	},

	flowChartITSO: {
		itSOsCounter: '{0} 个服务机构',
		itSOCounter: '{0} 个服务机构',
		headerName: '关联服务机构',
		columnName: '服务机构名称',
		noDataFound: '未找到服务机构'
	},

	flowChartControl: {
		controlsCounter: ' {0} 关联控制',
		headerName: '关联控制',
		showOnlyRelatedText: '仅显示相关项目',
		noResultsFound: '未找到结果',
		noWCGWs: '未创建任何控制'
	},

	relateSCOT: {
		header: '关联重大交易类别',
		estimate: '估计',
		scotsCounter: '{0} 个重大交易类别',
		scotCounter: '{0} 个重大交易类别',
		headerName: '重大交易类别名称',
		showOnlyRelated: '仅显示相关项目',
		noResultsFound: '未找到结果',
		noScotCreated: '此项目中未创建重大交易类别（SCOT）'
	},

	relatedStepObjects: {
		relatedWCGWs: '已关联的可能出错项',
		relatedControls: '已关联的控制',
		relatedDocuments: '已关联的证据',
		relatedITApplications: '已关联的IT应用程序',
		relatedSOs: '已关联的服务机构'
	},

	flowchartEditSteps: {
		nextStep: '下一步',
		previousStep: '上一步',
		editStepTitle: '编辑步骤',
		editPlaceholderText_1: '编辑下方步骤详细信息和相关对象。点击',
		editPlaceholderText_2: "'保存并关闭' ",
		editPlaceholderText_3: '保存并返回流程图。通过下方选项进入其他步骤将保存您的更新。 ',
		draftEditStepFailMessage: '无法创建流程图步骤。请刷新页面并重试。如错误仍然存在，请联系帮助台。',
	},

	flowChartStepmoreMenu: {
		edit: '编辑',
		delete: '删除'
	},

	relateEstimate: {
		scot: '重大交易类别',
		strategy: '重大交易类别策略',
		type: '类型',
		noSCOT: '每个估计必须至少与一个重大交易类别相关。点击',
		noSCOTmsg: ' 然后开始。',
		estimate: '估计',
		routine: '常规',
		nonRoutine: '非常规',
		notSelected: '未选择',
		relateSCOTs: '关联重大交易类别',
		remove: '移除',
		noEstimate: '无可用估计'
	},

	flowChartStepIcons: {
		wcgws: '可能出错项',
		controls: '控制',
		iTApps: 'IT应用程序',
		serviceOrganisations: '服务机构'
	},

	flowChartStepIcon: {
		wcgw: 'WCGW',
		control: '控制',
		iTApp: 'IT应用程序',
		serviceOrganisation: '服务机构',
		evidence: '证据'
	},

	flowChartErrorMessage: {
		stepOutsideOfTheColumns: 'Steps cannot be placed outside of the flowchart area',
		stepBetweenTheColumns: 'Steps cannot be placed between the columns',
		stepOnTopOrTooCloseToAnotherStep: 'Steps cannot be placed on top of the other steps'
	},

	//Delete Flow Chart Steps
	flowChartStepsDelete: {
		deletestep: '删除步骤',
		deleteStepModalMessage: '是否确认删除此步骤？与已删除步骤相关的所有可能出错项、控制、IT应用程序、服务机构和证据将不再关联。',
		cannotBeUndone: '新签或续签的客户合同',
		deleteStepFailMessage: '未删除该步骤。请重试',
		deleteDraftStepErrorMessage: '没有删除草稿中创建的步骤。若要删除此步骤，请选择该步骤并再次执行删除操作。',
	},
	notEntered: '未输入',
	estimateCategory: '估计类别',
	noResultsFoundWithPeriod: '未找到结果',
	noEstimateAvailable: '无可用估计',
	noRelatedObject: '无已关联的对象。',
	relateAnObject: '关联对象',
	copyrightMessage: '版权©<year>版权所有',
	leadsheet: '引导表',
	controlName: '控制名称',
	noControlAvailable: '无可用控制',
	independenceError: '在提交独立性之前，必须将所有未完成的回答填写完毕。',
	riskTypeNotAssociated: '新添加的风险与允许的风险类型不匹配，因此不会显示在下方。添加允许类型的风险或从下方的列表中选择',
	accountsAndRelatedEstimates: '账户和相关估计',
	noEstimatesAssociated: '无相关估计',
	noAssertionsAvailable: '无可用认定',
	noAccountsOrDisclosuresAvailable: '无可用账户或披露',

	relateEstimateToRisk: {
		riskType: '风险类型',
		risk: '风险',
		hasestimate: "Has estimate?",
		accounts: '账户',
		isItRelevant: '是否相关？',
		assertions: '认定',
		invalidRiskParentRiskErrMsg: '未找到记录。请刷新页面以继续。',
		noEstimate: '无可用估计',
		invalidRelateRiskOrEstimateRelationErrMsg: '该对象已关联。请刷新页面以继续下一步。',
		invalidUnRelateRiskOrEstimateRelationErrMsg: '该对象已不相关。请刷新页面以继续下一步。'
	},

	savingChanges: '保存变更',
	showEstimateAccountsWithoutEstimates: '显示没有估计的估计账户',
	showEstimateSCOTsWithoutEstimates: '显示没有估计的估计重大交易类别',
	manageSCOTs: '管理重大交易类别',
	sCOTsAndRelatedEstimates: '重大交易类别和相关估计',
	relateEstimateToRiskNoDataMessage: '无可用记录，请将至少一个账户和认定与相关风险关联（如适用）',
	maps: '示图',
	mapsUpbutSomeLoadingErrorMessage: '出现技术错误，對應功能无法运行。请更新页面并重试。',
	mapsDownErrorMessage: '對應功能暂时无法使用。请更新页面并重试。如果此消息仍然存在，请联系帮助台。',
	financialStatements: '财务报表',
	serviceGatewayAutomation: '服务网关和自动化',
	priorPeriodCategory: '上期类别',
	relatedAccountWithColon: '关联账户： ',
	noRelatedAccount: '无关联账户',
	noRetionaleAvailable: '没有可用的理由',
	leftNavIconApprovals: '批准',
	editDuplicateSectionHeader: '编辑章节的详细信息，然后点击保存。',

	relatedEvidences: '关联证据',
	relatedEvidencesInstruction: '关联此项目的证据。',
	relatedTemporaryFilesInstruction: '关联此项目的临时文档。',
	noDataLabel: '未找到数据',
	editDuplicateSection: '编辑章节',
	showOnlyRelated: '仅显示已关联',
	aiChatbot: 'EYQ Assurance Knowledge',
	StEntityNoRecords: '未将任何账户或披露映射到选定实体。',
	versionLabel: '版本',
	relatedEstimates: '相关估计',
	viewEvidenceRelatedToBody: 'View evidence related to the body',
	selectHeaderFromRail: 'Select a header from left navigation pane to proceed',
	manageITProcesses: '管理IT流程',
	rationaleForLR: '有限风险账户的理由',
	rationaleForInsignificant: '不重大账户的理由',
	rationalIsMissing: '未提供理由。',
	craSummaryText1: '每个重大账户或揭露必须至少有一个相关认定。点击',
	scotDetails223: {
		relatedAccounts: '关联账户',
		scotType: '类型',
		manageScot: '管理重大交易类别',
		editScot: '编辑重大交易类别',
		scotNotAvailableMessage: '重大交易类别不適用于此文档',
		relatedScotNotAvailableMessage: '没有关联的重大交易类别。关联属性页面的一个重大交易类别以开始',
		risksDocumented: '此穿行测试中记录的风险',
		risksAvailableHeader: '是',
		risksNotAvailableHeader: '否',
		viewRelatedRisks: '查看关联风险',
		noRelatedAccountsMessage: '没有关联账户'
	},

	scotDetails226: {
		noscotsidentified: 'No SCOTs have been identified'
	},

	scotDetails224: {
		riskRelatedWalkthrough: '此穿行测试中关联的风险',
		relatedToWTDocuments: 'Related to other WT documents',
		riskNotRelatedWalkthrough: '此穿行测试中未关联的风险”，',
		substantiveNotSufficient: '实质性不充分',
		journalEntry: '记账分录',
		noDirectRiskSourcesAreAvailable: '无关联的风险',
		scotNotAvailableMessage: '重大交易类别不適用于此文档',
		relatedScotNotAvailableMessage: '没有关联的重大交易类别。关联属性页面的一个重大交易类别以开始',
		relatedDocuments: 'Related documents',
		risk: "Risk:",
		riskSpecialCircumstances: 'Risk special circumstances',
		relateInstructionText: "This risk has been identified in another SCOT.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		unrelateInstructionText: "This risk has been identified in the critical path of another walkthrough.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		concurrencyErrorMessage: "This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.",
	},
	ipe: '实体生成的資訊（IPE）',
	scotSummary198: {
		noAccountsDisclosureCreated: '没有创建重大账户或揭露。',
		noScotEstimateIdentified: '没有识别到重大交易类别或估计。',
		noScotIdentified: '没有识别到重大交易类别',
		scots: '重大交易类别',
		estimates: '估计',
		errorMessage: '无法完成此操作。请更新页面并重试。如错误仍然存在，请联系帮助台。',
		noResultsFound: '未发现结果',
		searchAccounts: '搜尋账户',
		searchScotEstimates: '搜尋重大交易类别/估计',
		accounts: '账户',
		scotsOrEstimate: '重大交易类别/估计',
		accountNotRelatedToScotValidation: '每个重大账户或揭露必须至少与一个重大交易类别相关联。选中复选框，将相关重大交易类别与该重大账户或重大揭露相关联。',
		scotNotRelatedToAccountValidation: '每个重大交易类别必须至少与一个重大账户或重大揭露相关联。选中复选框，将此重大交易类别与相关的重大账户或重大揭露相关联。',
		showValidations: '显示验证',
	},
	scotSummary225: {
		relatedScots: '已关联重大交易类别',
		relatedAcconts: '关联的账户',
		scotListHeader: '重大交易类别和估计',
		noScotsMessage: '每个重大账户或揭露必须至少与一个重大交易类别相关联。请选择一个现有重大交易类别与此重大账户或重大揭露相关联',
		noAccountsMessage: 'No significant accounts or disclosures have been created.',
		noAccountsAvailableOnSearch: 'No results found',
		relateAccounts: 'Relate accounts and disclosures',
		noAccountsCreated: 'No accounts have been created',
		noScotsCreated: 'No SCOTs have been created',
		relateScots: 'Relate SCOTs',
	},
	bodyUnavailableInCCP: '此内容无法通过EY Canvas客户端门户获得。',
	pyBalance: '上年余额',
	cyBalance: '本年余额',
	designationNotDefined: '未定义指定事项',
	controlRiskAssessment: '控制风险评估',
	first: '首页',
	noImportedTrialBalance: '没有导入的试算表。',
	placeHolderMessageWhenHelixMappingIsTrue: '点击{0}关联新分析工具。',
	documentPrintSuccess: 'Document print in progress. It may take up to ten minutes. Once completed, the print will be added to the temporary files.',
	documentPrintError: '文档打印失败。请更新或稍后再试。如果问题仍然存在，请联系帮助台。',
	backToEvidenceWarningMessage: 'This action could not be completed. Please refresh and try again. If issue persists, please contact the Help Desk.',
	rationaleMissingForLR: 'Each limited risk account shall have a rationale provided',
	rationaleMissingForIR: 'Each insignificant account shall have a rationale provided',
	craSummaryText2: ' Each account shall have designation determined. Click',
	contentDrivingEntity: 'Content driving entity',
	contentDrivingEntityPlaceholder: 'Content driving entity has not been selected',
	rationaleForPlaceholder: 'Provide rationale for this account designation',
	contentDrivingEntityRequired: 'Content driving entity (required)',
	refreshContentLayers: 'Refresh content layers',
	noAccessLabel: 'Unauthorized. Contact your administrator and try again.',
	copyForHelpDeskDetails: 'Copy for Help Desk details',
	copyForHelpDeskDetailsSuccess: 'Details copied to clipboard',

	//toast activity as guest user
	sharedGuidedworkflowEvidenceWarning: 'This is a shared Guided Workflow Activity. The objects and evidence exist in the original engagement and will not be added to this engagement upon unlink. See <a style="color: #467cbe" href="https://live.atlas.ey.com/#library/104?pref=20058/9/5" target="_blank">enablement here</a> for further details.',
	sharedGuidedworkflowResponseWarning: "This is a shared Guided Workflow Activity. The responses are being shared with other activities in the same workspace. View relationships by accessing the'Show relationships' menu item from this activity's summary section."
};

export const groupStructure = {
	createComponent: '新组成部分',
	deleteComponent: '删除组成部分',
	manageComponents: '管理组成部分',
	emptyComponents: '未创建组成部分。创建{newComponent}，然后开始。',
	scope: '范围',
	role: '角色',
	pointOfContact: '联系人',
	linkRequest: '关联请求',
	instructions: '指引',
	instructionsSent: '指引已发送',
	status: '状态',
	createComponentInstructionalText: "在下方输入组成部分详情，然后点击<b>\’保存并关闭\'</b>结束。如想另外创建组成部分，请选择<b>’保存并新建’。 ",
	componentName: '组成部分名称',
	region: '地区',
	notUsingCanvas: '不使用EY Canvas',
	referenceOnly: '仅供参考',
	saveAndCreateAnother: '保存并创建另一个组成部分',
	dueDate: '到期日',
	components: '组成部分',
	allocation: '分配',
	documents: 'Evidence',
	discussions: '讨论',
	EDAP: '项目管理人员讨论及批准要点',
	siteVisits: '现场走访',
	reviewWorkComponent: '已执行复核工作',
	other: '其他',
	significantUpdates: '重大更新',
	executionComplete: '执行已完成',
	gaRoleTypesLabel: [{
		id: 1,
		displayName: '主审小组'
	},
	{
		id: 2,
		displayName: '地区'
	},
	{
		id: 3,
		displayName: '组成部分'
	}
	],
	gaLinkStatusLabel: [{
		id: GALinkStatus.NotSent,
		displayName: '发送'
	},
	{
		id: GALinkStatus.Sent,
		displayName: '已发送/未接受'
	},
	{
		id: GALinkStatus.ComponentNotUsingCanvas,
		displayName: '不使用EY Canvas'
	},
	{
		id: GALinkStatus.ReferenceOnly,
		displayName: '仅供参考'
	},
	{
		id: GALinkStatus.Accepted,
		displayName: '已接受'
	},
	{
		id: GALinkStatus.Rejected,
		displayName: '已拒绝'
	},
	{
		id: GALinkStatus.Unlinked,
		displayName: '未关联'
	},
	{
		id: GALinkStatus.Pending,
		displayName: '已发送/未接受'
	}
	],
	notAvailable: '不可用',
	search: labels.searchPlaceholder,
	noResultsFound: '未找到结果。',
	noComponentsFound: '未找到组成部分。',
	contentSwitcher: [{
		id: gaRoleTypes.primary,
		displayName: '主审小组'
	},
	{
		id: gaRoleTypes.component,
		displayName: '组成部分'
	},
	{
		id: gaRoleTypes.regional,
		displayName: '地区'
	}
	],
	gaRegionTypesLabel: {
		id: gaRegion.notApplicable,
		displayName: '不适用'
	},
	//TODO: To be removed
	pointOfContactValues: [{
		id: pointOfContactTypes.EYcontact,
		displayName: '安永联系人'
	},
	{
		id: pointOfContactTypes.externalContact,
		displayName: '外部联系人'
	}
	],
	saveAndClose: labels.modalSaveAndClose,
	cancelBtn: labels.modalCancelTitle,
	gaScopesValues: [{
		id: gaScopeType.full,
		displayName: '全面'
	},
	{
		id: gaScopeType.specific,
		displayName: '特定'
	},
	{
		id: gaScopeType.specifiedAuditProcedures,
		displayName: '规定程序'
	},
	{
		id: gaScopeType.review,
		displayName: '审阅'
	}
	],
	edit: labels.edit,
	delete: labels.delete,
	tooltipIcon: '如果组成部分不使用EY Canvas接收集团审计指引并提交跨办事处交付成果，请点击此选项。',
	tooltipReferenceIcon:
		'被指定为<b>仅供参考</b>的组成部分仅用于建立组织结构。这些组成部分项目不会接收关联请求、指引或任务，此主审小组的项目也不会接收集团任务。',
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	modalConfirmBtnLabel: labels.confirmLabel,
	clear: '清空',
	clearUpper: labels.clear,
	nameOrEmail: '输入安永联系人电子邮箱',
	editComponent: '编辑组成部分',
	editComponentInstructionalText: "在下方输入组成部分详情，然后点击<b>\’保存\'</b>结束。 ",
	linkAlreadyAcceptedInfo: '由于关联请求已发送至组成部分项目小组，因此只能编辑邮件信息。',
	sendAll: '发送所有',
	send: '发送',
	resend: '重新发送',
	scopeAndStrategy: '范围和策略',
	execution: '执行',
	conclusion: '结论',
	reportingForms: '汇报表格',
	manageGroupPermission: '您没有<b>管理分组</b>权限，无法执行此操作。请求从项目管理员处获取<b>管理分组</b>权限。',
	manageComponentModalDesc: '创建新组成部分或编辑并删除以下已有组成部分。',
	editLinkInfo: '由于关联请求已发送至组成部分项目小组，因此只能编辑邮件信息。',
	invalidPointOfContact: '需要联系人来发送关联请求。编辑组成部分，添加联系人。',
	manageComponentModalActions: '操作',
	manageComponentModalComponents: '组成部分',
	manageComponentModalDelete: '删除',
	noThereAtLeastOneComponentToSendAll: '没有组成部分处于可发送关联请求的状态。组成部分必须为<b>发送</b>或<b>重新发送</b>的状态才能发送关联请求。',
	showKnowledgeDescription: '显示知识库的标题和描述',
	hideKnowledgeDescription: '隐藏知识库的标题和描述',
	instructionName: '输入指引名称',
	instructionDescriptionPlaceholder: '输入指引描述',
	selectDueDate: '截止日期（必填）',
	show: '显示',
	allocationHeader: '分配',
	allocationInstructionForKnowledge: '知识库指引只可以按照范围进行分配。选择以下相关范围。',
	allocationInstructionForCustom: '定制指引只可以按照范围或组成部分进行分配。选择以下指引分配，然后分配至相关范围或组成部分。',
	allocateScope: '分配至范围',
	allocateComponent: '分配至组成部分',
	pillScopesPlural: '范围',
	pillScopesSingular: '范围',
	pillComponentsPlural: '组成部分',
	pillComponentsSingular: '组成部分',
	selectScopesPlaceholder: '选择范围',
	selectComponentsPlaceholder: '选择组成部分',
	searchNoResultFoundText: labels.searchNoResultFoundText,
	newCustomInstruction: '新的自定义指引',
	instructionNameNewCustomInstruction: '指引名称',
	addCustom: '添加自定义',
	custom: '自定义',
	required: '必填',
	remove: '移除',
	selectAll: '选择全部',
	unselectAll: '取消选择全部',
	lowerPoC: '联系人',
	editPoCTooltip: '无效联系人或无联系人。编辑联系人以发送关联请求。',
	recomendationType: [{
		id: 1,
		label: '必填'
	},
	{
		id: 2,
		label: '选填'
	},
	{
		id: 3,
		label: '不适用'
	}
	],
	confirmLabel: labels.confirmLabel,
	deleteComponentInstructionalText: '<b>是否要从集团架构中删除此组成部分？</b><br />该组成部分被删除后，指向该组成部分的链接将被删除，项目之间将无法再交换文档记录。此外，该组成部分与其账户和指引之间的所有关联都将被删除。',
	noActivitiesAvailable: '无可用活动',
	relatedComponents: '相关组成部分',
	relatedComponentsSingular: '相关组成部分',
	relatedComponentsPlural: '相关组成部分',
	publish: '发布',
	publishModalHeader: '发布变更',
	publishChangesInstructional: '<b>是否要发布集团指引概述变更？</b><br />上一套集团指引将被覆盖。发布变更后，可通过集团指引概述发布更新指引。',
	publishManageGroupPermission: '您必须拥有“管理集团”权限才能执行该操作。请向项目管理员申请权限。',
	lastPublished: '上次发布时间： ',
	publishChangesNotAvailable: '尚不可用',
	noRecordsFound: labels.noRecordsFound,
	deleteInstruction: '删除指引',
	deleteInstructionInstructionalText: '<b>是否要删除该指引？</b><br />此操作无法撤销。',
	sendInstructionsTitle: '发送指引',
	sendInstructionsInstructionalText: '点击相关页面上的“发布”，确保已发布最新的指引。然后，查看以下组成部分的指引，并点击“发送”将指引发送至组成部分项目。',
	instructionsAlreadySent: '已发送最新版本的指引。',
	missingDueDates: '报表到期日缺失。',
	createInstructionsModalButton: '创建指引',
	createInstructionsModalActionToastMessageStart: '缺少给组成部分的集团风险评估指引。',
	createInstructionsModalActionToastMessageEnd: ' 。',
	createInstructionsModalDescription: '以下“全面”和“特定”范围的组成部分未分配到集团风险评估指引。选择<b>创建</b>，将为下列每个组成部分创建集团风险评估指引。',
	createInstructionsModalScope: '范围',
	createInstructionsModalHeader: '创建指引',
	createInstructionsModalmodalConfirmBtnLabel: '创建',
	createInstructionsModalmodalCancelBtnLabel: '取消',
	createInstructionsModalmodalCloseBtnTitletip: '关闭',
	createInstructionsModalNewGraInstructionDescription: '已随附与您的组成部分相关的账户的风险评估。复核风险评估，确保您的项目已识别出这些账户和风险。应与主审小组沟通本地识别出的或组成部分项目小组有不同意见的任何其他风险，以便主审小组和组成部分项目小组可以相应地调整风险评估。',
	createInstructionsModalErrorMessage: '以下组成部分的集团风险评估指引创建失败：<b>{0}</b>。请刷新页面并重试。',
	createInstructionsDuplicatedModalErrorMessage: '集团风险评估指引创建失败。指引名称不能重复。',
	gaLinkActionTooltip: {
		NotUsingCanvasLabel: '不使用EY Canvas',
		NotUsingCanvas: '如果点击<b>发送</b>，则会创建<br/>此组成部分的主要集团任务<br/>，但不会发送指引。',
		NotLinkedLabel: '未关联',
		NotLinked: '关联请求未发送至<br/>组成部分项目小组。发送关联<br/>请求以发送指引。',
		Unlinked: '未关联'
	},
	viewHistory: '查看历史',
	viewSentInstructionsTitle: '查看已发送指引',
	save: labels.saveLabel,
	cancel: labels.cancelLabel,
	viewHistoryInstructionalText: '选择指引，查看发送给组成部分项目小组的指引的早期版本。',
	viewHistorySelectInstruction: '选择指引',
	viewHistoryDateSent: '发送日期： ',
	viewHistoryStatus: '状态： ',
	viewHistoryStatusAccepted: '已接受',
	viewHistoryStatusPending: '待处理',
	viewHistoryStatusRejected: '已拒绝',
	viewHistoryStatusSystemError: 'System error',
	viewHistorySelectVersion: '选择版本',
	noAccountsFound: '此项目或其他项目中未发现账户或披露。<br />点击{link}创建新的账户或披露，或编辑现有的账户或披露。',
	generalCommunications: '一般沟通事项',
	reportingDeliverables: '报告交付成果',
	changesPublishedNotSent: '未发送变更',
	changesPublishedBrNotSent: '未发送变更<br/>',
	changesPublishedNotSentYes: '是',
	deleteSubScopeInstructionalTextModal: '是否确定删除<br/>选定的子范围?',
	deleteSubScopeTitleModal: '删除子范围',
	riskAssessmentModal: {
		headerText: '风险评估',
		modalCloseBtnTitletip: labels.close,
		manageAndDisclosures: '管理账户和披露链接',
		next: '下一个组成部分',
		back: '上一个组成部分'
	},
	riskAssessment: '风险评估',
	preview: '预览',
	accountsAndDisclosureSummary: '账户和披露',
	noAccountSnapshotPlaceholder: '没有可为此组成部分显示的账户数据。',
	createOversightProjectButtonLabel: '创建EY Canvas Oversight项目',
	createOversightProjectTitle: '是否希望使用此主要项目创建EY Canvas Oversight项目？',
	createOversightProjectDescription: '在该集团结构中识别的地区和/或组成部分EY Canvas项目将作为EY Canvas Oversight项目设置的一部分自动填充。',
	createOversightModalHeader: 'EY Canvas Oversight项目名称',
	createOversightModalDescription: '输入EY Canvas Oversight项目的名称。',
	createOversightModalTextLabel: '项目名称',
	projectRedirectionButtonLabel: 'EY Canvas Oversight项目',
	projectAssociationTextLabel: '存在与此项目关联的EY Canvas Oversight项目',
	sendLinkDisableTooltip: '此项目已复制，包括集团审计流程中的组成部分。链接不能重新发送。如需要，请创建新组成部分并发送链接。 ',
	instructionsCannotBeSentUntilPublished: 'Instructions cannot be sent until they are published.'
};

export const groupInvolvement = {
	NoComponentsAvailables: '未创建任何组成部分。<b>管理组成部分</b>以开始。',
	GroupInvolvementToastMsgStart: '缺少给组成部分的集团参与表格。',
	GroupInvolvementToastMsgEnd: ' 。',
	CreateGroupInvolvementHeader: '创建参与表格',
	GroupInvolvementInstructionalText: '以下组成部分未分配集团参与表格。<br/>点击&#39;<b>创建</b>&#39;将为下列每个组成部分创建集团参与表格。',
	createGroupInvolvementDocumentErrorMessage: '以下组成部分的集团参与文档创建失败：<b>{0}</b>。请刷新页面并重试。',
	createGroupInvolvementDocumentSuccessMessage: '已成功创建集团参与表格。请在30秒内刷新页面以查看可用文档。',
	involvementTypePlanned: '计划参与类型',
	significantUpdatesToPlannedInvolvement: '计划参与的重大更新',
	executionComplete: '执行已完成',
	generateGroupInvolvementCommunications: '打印参与表格',
	generateGroupInvolvementInstructionalText: '以下组成部分具有与之相关的集团参与表格。请选择将哪个组成部分&#39;集团参与表格纳入以下文件。<br /><br />选择组成部分后，点击<b>&#39;创建&#39;</b>将为下列每个组成部分&#39;集团参与文档创建一份集团参与文档。',
	componentTeams: '组成部分项目小组',
	noComponentsSelectedErrorMessage: '请选择组成部分以创建集团参与沟通。',
	documentName: '{taskName}集团参与资料包',
	selectAll: groupStructure.selectAll,
	unselectAll: groupStructure.unselectAll,
	modalConfirmBtnLabel: groupStructure.createInstructionsModalmodalConfirmBtnLabel,
	modalCancelBtnLabel: groupStructure.cancelBtn,
	modalCloseBtnTitletip: groupStructure.modalCloseBtnTitletip
};

export const itPlanning = {
	supportingITColumnsHeaders: {
		applicationTool: {
			name: 'Applications/Tools'
		},
		network: {
			name: '网络'
		},
		database: {
			name: '数据库'
		},
		operatingSystem: {
			name: '操作系统'
		}
	},
	relatedITProcessesColumnsHeaders: {
		relatedITProcess: 'Related IT processes',
		category: 'Category'
	},
	itPlanningPlaceholders: {
		smartEvidenceSourceEntityId: '关联风险不可用于于此文档',
		smartEvidenceSourceId: '无关联对象。请关联一个对象以开始。',
	},
	relatedITProcessesPlaceholders: {
		smartEvidenceSourceEntityId: 'Related IT process not available for this document',
		smartEvidenceSourceId: '无关联对象。请关联一个对象以开始。',
		relatedITProcessEmpty: 'No IT process related to the technology'
	},
	noTechnologiesIdentified: '没有识别到技术',
	supportingITEmpty: '没有与技术关联的支持性应用程序/工具',
	supportingITNetworkEmpty: '没有与技术关联的支援性网络',
	searchPlaceholder: '搜索',
	newTechnology: '新技术',
	noSupportingDatabases: '没有与技术关联的支持性数据库',
	createEntityFormDocument: '创建文档',
	noSupportingOperatingSystem: 'No supporting operating systems related to the technology',
	manageTechnology: 'Manage technology'
};

export const itRiskFactors = {
	accepted: '已接受',
	rejected: '已拒绝',
	accept: '接受',
	reject: '拒绝',
	rejectionRationale: '拒收理由',
	rejectionCategory: '拒绝类别',
	rejectionRationaleRequired: 'Rejection rationale (required)',
	rejectionCategoryRequired: 'Rejection category (required)',
	riskName: 'Risk name',
	smartEvidenceValidations: {
		smartEvidenceSourceEntityId: '风险因素不可用于此文档',
		smartEvidenceSourceId: 'No related object. Relate an object to get started.'
	},
	manageChangePlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: '已拒绝所有风险'
	},
	manageOperationsPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	},
	manageAccessPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	},
	SDLCPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: '已拒绝所有风险'
	},
	manageSecuritySettingsPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	}
};

export const rejectionTypeResource = [{
	id: rejectionType.itRiskOther,
	label: 'ITRisk Other'
},
{
	id: rejectionType.itRiskOption2,
	label: 'ITRisk - "Option 2"'
},
{
	id: rejectionType.itRiskOption3,
	label: 'ITRisk - "Option 3"'
}
];

export const sampleList = {
	newSample: '新样本',
	createSampleModalDescription: '在下方输入样本详情，然后点击“<b>{0}</b>”完成。新建样本时，请选择“<b>{1}</b>”。',
	saveAndCreateAnother: '保存并创建另一个组成部分',
	saveAndClose: '保存并关闭',
	sampleDescription: '样本描述（必填）',
	sampleDate: '抽样日期（必填）',
	sampleListId: '样本列表ID',
	ok: '确认',
	addSample: '添加样本',
	cancel: '取消',
	saveAndCloseForHeader: '保存并关闭',
	saveAndCreateAnotherHeader: '保存并新建',
	required: '必填',
	description: '样本描述',
	date: '日期',
	attributeStatus: '属性',
	tags: '标签',
	open: '打开',
	notApplicableLabel: '不适用',
	notPresent: '不存在',
	present: '存在',
	pagingShowtext: '显示',
	placeHolderMessage: '无可用样本。点击{clickHere}开始。',
	noSampleListAvailable: '没有可用的样本列表',
	editSample: '编辑样本',
	editSampleDescription: '编辑下方样本详情，然后点击“<b>{0}</b>”完成。',
	editSampleSave: '保存',
	sampleCanNotBeCreated: '无法为此文档创建样本。',
	noRelatedObject: '无相关对象。关联对象以开始。',
	noResultsFound: '未找到结果'
};

export const AdditionDocumentationLabels = {
	addAdditionalDocumentation: '添加其他文档',
	editAdditionalDocTitle: '编辑其他文档记录',
	removeAdditionalDocumentation: '删除其他文档',
	cancel: '取消',
	save: '保存',
	of: '的',
	additionalDocTitlePlaceholder: '其他文档（必填）',
	additionalDocTitle: '其他文档（必填）',
	remove: '移除',
	enterAdditionalDocTitle: "在下面输入其他文档，然后选<b>’{0}'</b>完成。 ",
	editAdditionalDocDesc: "编辑下方文档记录，然后点击<b>'{0}'</b> 结束。 ",
	characters: '字符',
	required: '必填',
	descriptionMaxLengthError: '回复长度超过了允许的上限。',
	attributeIndexLabel: '属性索引'
};

export const sampletAttributeConstants = [{
	id: 1,
	label: '未完成'
},
{
	id: 3,
	label: '存在'
},
{
	id: 7,
	label: '存在且随附意见'
},
{
	id: 5,
	label: '不存在'
},
{
	id: 4,
	label: '不适用'
},
];

export const groupInstructions = {
	ALRAPackageModalTitle: 'ALRA数据包名称',
	ALRAPackageModalInstructionalText: '输入您要添加到证据中的ALRA程序数据包的名称。',
	ALRAPackageModalNameField: '输入名称',
	ALRAPackageSuccessToastMessage: '数据包创建流程已启动。完成该步骤可能需要十分钟。',
	ALRAPackageInProgressToastMessage: '数据包创建流程正在运行，完成该步骤可能需要十分钟。',
	delete: labels.delete,
	deleteSectionModalTitle: labels.deleteSection,
	deleteSectionInstructionalText: '<B>是否要删除这部分？</b><br />此操作无法撤消。',
	deleteSectionTooltipText: '必须先删除指引<br />，然后才能删除这部分。',
	modalConfirmBtnLabel: labels.confirmLabel,
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	missing: '缺失',
	sendAllModalTriggerButton: '发送所有',
	sendAllModalTooltipText: '没有可发送给组成部分项目小组的指引。',
	publishModalTooltipText: '集团指引需在发送前发布。发布指引时，所做的任何变更都将保存到新指引中，覆盖以前版本的指引。然后，可以将这些新指引发送给组成部分项目小组。',
	sendAllModalErrorMessage: 'Group instructions for the following Components were not sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk. <br /> <b>{0}</b>',
	sendAllModalHeaderText: '发送所有指引',
	sendAllModalConfirmBtnLabel: '发送',
	sendAllModalCancelBtnLabel: '取消',
	sendAllModalCloseBtnTitletip: '关闭',
	sendAllModalDescription: '点击<b>发送</b>将向以下组成部分项目小组发送指引。',
	generateGroupRiskAssessmentCommunications: '生成集团ALRA',
	bulkALRAPackageName: '{instructionName}账户层级风险评估数据包',
	groupInstructionSummaryReport: '集团指引汇总报告',
	groupInstructionSummaryReportTitletip: '查看并导出集团指引详细信息、指引历史记录以及对组成部分/账户匹配的更改。',
	exportGroupRiskAssessment: '导出汇总',
	reportingDeliverables: groupStructure.reportingDeliverables,
	groupRiskAssessment: '集团风险评估'
};

export const sectionTitles = [{
	id: KnowledgeSectionIds.GeneralCommunications,
	sectionTitle: groupStructure.generalCommunications
},
{
	id: KnowledgeSectionIds.ScopeOfWork,
	sectionTitle: '工作范围'
},
{
	id: KnowledgeSectionIds.ReportingForms,
	sectionTitle: groupStructure.reportingDeliverables
},
{
	id: KnowledgeSectionIds.ProceduresPerformedCentrally,
	sectionTitle: '集中执行的程序'
},
{
	id: KnowledgeSectionIds.GroupRiskAssessment,
	sectionTitle: groupInstructions.groupRiskAssessment
},
{
	id: KnowledgeSectionIds.OtherCommunications,
	sectionTitle: '其他沟通事项'
}
];

export const groupAuditToolbar = {
	search: labels.placeholderForSearch
};

export const AccountType = [{
	id: 1,
	accounttypename: '重大账户'
},
{
	id: 2,
	accounttypename: '有限风险账户'
},
{
	id: 3,
	accounttypename: '非重大账户'
},
{
	id: 4,
	accounttypename: '其他账户'
},
{
	id: 5,
	accounttypename: '重大披露'
}
];

export const PriorityType = [{
	value: 1,
	label: '低'
},
{
	value: 2,
	label: '中等'
},
{
	value: 3,
	label: '高'
},
{
	value: 4,
	label: '关键'
}
];

export const AccountSummaryAccountType = [{
	id: '0',
	accounttypename: '所有账户'
},
{
	id: '1',
	accounttypename: '重大账户'
},
{
	id: '2',
	accounttypename: '有限风险账户'
},
{
	id: '3',
	accounttypename: '非重大账户'
},
{
	id: '4',
	accounttypename: '账户—其他'
},
{
	id: '5',
	accounttypename: '重大披露'
}
];

export const TaskStatus = [{
	id: 1,
	status: '打开'
},
{
	id: 2,
	status: '进行中'
},
{
	id: 3,
	status: '复核中'
},
{
	id: 4,
	status: '已完成'
},
{
	id: 5,
	status: '已删除'
}
];

export const reviewNoteLabels = {
	/*Review Notes*/
	emptyNoteDetailsMessage: '选择一个注释，查看详情。要启用批量控制，请使用控制键或shift键并选择多个复核注释。如果要处理单个注释，请从列表中选择该注释。',
	documentReviewNotesLabel: '文件注释',
	addNewReviewNoteButtonText: '添加注释',
	noNotesAssociatedWithDocumentLabel: '没有与该本文件相关的注释',
	allNotesLabel: '全部注释',
	charactersLabel: '字符',
	myNotesLabel: '我的注释',
	showClearedLabel: '显示已清除项',
	showClosedLabel: '显示已关闭项',
	toLabel: '至',
	toUserLabel: '至',
	ofLabel: '的',
	textAreaPlaceholder: '输入注释',
	addNewNoteModalClose: '关闭',
	addNewNoteModalTitleLabel: '添加新注释',
	editNoteModalTitleLabel: '编辑注释',
	deleteIconHoverText: '删除',
	deleteIconModalAcceptText: '删除',
	deleteIconModalConfirmMessage: '是否确定删除对此注释的回复?',
	deleteIconModalConfirmMessageParent: '是否确定删除选定的注释?',
	deleteIconModalTitleLabel: '删除注释',
	deleteReplyIconModalTitle: '删除回复',
	emptyRepliesMessage: '尚未有回复',
	replyInputPlaceholder: '回复该注释',
	replyText: '回复文本',
	editReplyModelTitle: '编辑回复',
	noteDueDateLabel: '到期： ',
	fromUserLabel: '从',
	priorityLabel: '优先级',
	dueDateLabel: '到期日',
	dueLabel: '到期',
	status: '状态',
	noteModifiedDateLabel: '修改： ',
	cancelLabel: '取消',
	saveLabel: '保存',
	clearedBy: '被xxx清除',
	closedBy: '被xxx关闭',
	reopenedBy: '被xxx重新打开',
	reply: '回复',
	editIconHoverTextLabel: '编辑',
	required: '必填',
	closeTitle: '关闭',
	otherEngagementNotes: '其他项目注释',
	closeLabel: '关闭',
	showMore: '显示更多',
	showLess: '显示较少',
	showMoreEllipsis: '显示更多...',
	showLessEllipsis: '显示较少...',
	noResultFound: '未找到结果',
	engagementNameLabel: '项目名称： ',
	drag: '拖放',
	formMaxLength: '文本不得超过{number}个字符。',
	voiceNoteButtonLabel: '语音注释',
	stopRecordingButtonLabel: '停止',
	reopen: '重新打开',
	noNotesFound: '未找到注释',
	noNotesFoundInstructional: '使用以下输入信息添加注释。将注释分配给一位用户，并说明优先等级和到期日。',
	microphoneBlockedMessage: '允许浏览器访问您的麦克风以使用语音注释。如已允许，请刷新并重试。',
	microphoneBlockedOnVideoMessage: '允许浏览器访问麦克风，以在录屏时录制语音。如已允许，请刷新并重试。',
	notInMainWindowVoice: '抽屉菜单内不允许录音，请在新的标签页中打开文档以执行操作。',
	notInMainWindowScreen: '抽屉菜单内不允许录屏，请在新的标签页中打开文档以执行操作。',
	voiceNoteNotAvailable: '抽屉菜单视图下无法使用语音注释和录屏。请切换到全屏视图以使用这些功能。',
	playButtonTitle: '运行',
	deleteButtonTitle: '删除',
	pauseButtonTitle: '暂停',
	screenRecord: '屏幕录制',
	playbackReview: '查看回放'
};

export const IndividualAccountAttributeLabels = {
	attributesNotAvailableForDocument: '账户属性不可用于此文档。',
	noRelatedOnject: '无关联对象。关联一个对象以开始。',
	noAttributesAvailable: '没有可用属性',
	noRisksAvailable: '没有可用风险',
	attributeStandardRomms: '属性 标准重大错报风险',
	continueButtonTitle: '继续',
	closeButtonTitle: '取消',
	newAssertionModalPlaceholder: '这一选择将导致以下认定被识别为较低的固有风险，而这些认定之前并未被识别为相关认定。是否继续？',
	assertion: '认定',
	inherentRiskType: '固有风险',
	assertionModalTitle: '建立认定',
	riskType: '较低'
};

export const entities = [{
	id: 0,
	name: '全部'
},
{
	id: 1,
	name: '文档'
},
{
	id: 2,
	name: '引导表'
},
{
	id: 3,
	name: '账户'
},
{
	id: 4,
	name: '重大交易类别'
},
{
	id: 5,
	name: 'IT流程'
},
{
	id: 6,
	name: '审计计划'
},
{
	id: 7,
	name: '风险'
},
{
	id: 8,
	name: '任务'
},
{
	id: 9,
	name: '错报'
},
{
	id: 10,
	name: '缺陷'
},
{
	id: 11,
	name: 'GA 组成部分项目小组'
},
{
	id: 12,
	name: 'GA 组成部分项目小组 指引'
},
{
	id: 13,
	name: 'GA 组成部分项目小组 证据'
},
{
	id: 14,
	name: 'GA 范围'
},
{
	id: 15,
	name: 'GA 主审小组 指引'
},
{
	id: 16,
	name: 'GA 主审小组'
},
{
	id: 17,
	name: '客户请求'
},
{
	id: 18,
	name: '可能出错项（WCGW）'
},
{
	id: 19,
	name: '控制'
},
{
	id: 20,
	name: 'IT应用程序'
},
{
	id: 21,
	name: 'Canvas表格'
},
{
	id: 22,
	name: '表格小节'
},
{
	id: 23,
	name: '表格正文'
},
{
	id: 24,
	name: '认定'
},
{
	id: 25,
	name: '客户项目'
},
{
	id: 26,
	name: '客户集团'
},
{
	id: 27,
	name: '项目标签'
},
{
	id: 28,
	name: '项目'
},
{
	id: 29,
	name: '表格标题'
},
{
	id: 30,
	name: '表格状态'
},
{
	id: 31,
	name: '项目用户'
},
{
	id: 32,
	name: '客户集团用户'
},
{
	id: 33,
	name: '主要实质性程序（PSP） 索引'
},
{
	id: 34,
	name: 'IT一般控制（ITGC）'
},
{
	id: 35,
	name: 'IT风险'
},
{
	id: 36,
	name: '自动化单列项'
}
];

export const PaceType = [{
	id: 1,
	paceTypename: '低'
},
{
	id: 2,
	paceTypename: '中'
},
{
	id: 3,
	paceTypename: '高'
},
{
	id: 4,
	paceTypename: '严密监控'
}
];

export const DocumentHelper = {
	401: '无法完成该操作。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	413: '文档大小超过最大限制（250mb），无法上传。请缩小文档大小并重试。',
	412: '文档名称与该项目中已存在的文档名称重复',
	414: '名称超过最大长度（120个字符）。',
	4099: '已经存在同名文档。',
	/*this is a hack as we dont always know why conflict happened.*/
	410: '此文档已被删除，无法下载。',
	411: '文档不得为空。'
};

export const Errors = {
	/*Doc Helper Custom Messages */
	0: '连接丢失。重新连接并再次尝试。如果问题仍然存在，请联系帮助台。',
	10: 'EY Canvas文档助手已检测到问题。请点击<a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">此处</a>获得解决问题的指引。',
	101: '无效的项目状态。',
	102: '未发现有效的项目用户。',
	103: '缺少项目用户独立性合规。',
	104: '缺少所需的Azure AD 范围',
	105: '获取项目权限时发生错误。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	106: "Unauthorized. Contact your administrator and try again.",
	107: '找不到有效的项目用户。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	108: '项目配置信息不完整。请前往登录页，填写项目配置信息。',
	303: '目前正在上传一个同名的文档。',
	403: 'Access to this document is not available.  If this document is shared ensure you have access to the source engagement.  Refresh the page and try again.  If the error persists contact the Help Desk.',
	406: '文档不得为空。',
	412: '在此项目中已经存在同名文档。',
	414: '名称超过最大长度（120个字符）。',
	411: '文档不得为空。',
	500: '连接丢失。重新连接并再次尝试。如果问题仍然存在，请联系帮助台。',
	600: '该操作此时无法完成。请刷新页面并重试。如问题仍然存在，请联系帮助台。',
	601: '截图下载出错。刷新页面，然后重试。如果问题仍然存在，请联系帮助台。',
	602: '文档已在协作中',
	935: '用户没有足够的权限来执行该操作。',
	zip: '.zip文件无法上传，因为它包含一个或多个不受支持的文件类型，或者包含的.zip文件超过了最大嵌入量。',
	401000: '检测到网络变化，请重新加载页面继续。',

	/*Accounts*/
	1001: '创建账户指令失败。',
	1002: '缺少账户名称。',
	1003: 'The selected Account has been deleted. Close this modal to see the updated list.',
	1004: '未找到结果。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	1005: '无效的项目ID。',
	1006: '无效的账户类型。',
	1007: '无效的报表类型。',
	1008: '所选账户已删除。关闭此模式以查看更新的列表。',
	1009: '通过ID获取账户指令失败。',
	1010: '通过项目ID获取账户指令失败。',
	1011: '由于无效的请求，导致获取SEM汇总表的账户ID指令失败。',
	1012: '无效的汇总表类型。',
	1013: '创建账户复核指令失败。',
	1014: '删除账户复核指令失败。',
	1015: '无效的账户复核创建请求。',
	1016: '无效的账户复核ID。',
	1017: '账户不属于此项目或已被删除。',
	1018: '账户复核是由另一个用户创建。',
	1019: '账户已被另一个用户删除。请刷新页面并重试。',
	1020: '需要对账户执行主要实质性程序来进行更新',
	1024: '账户名称超过500个字符。',
	1025: '无法估计有限风险或非重大账户',
	1026: '账户不得有重复的认定',
	1027: '认定ID无效。',
	1037: '不得有重复的主要实质性程序索引',
	1039: '所选账户已被删除。关闭此模式以查看更新的列表。',
	1048: '账户执行类型无效。',
	1053: '账户具有关联的风险或估计，因此不能设置为有限风险账户或非重大账户。',
	1054: '待删除的认定有关联的风险或估计',
	1065: '您不能修改具有相关特别风险、舞弊风险或重大错报风险或估计相关的认定。请先删除关联。',
	1070: '当包含EY Helix时，试算表ID不能为空。',
	1072: '无法完成操作。请更新页面并重试。如果问题仍然存在，请联系帮助台。',

	1266: '项目的多用户编辑模式文档已达到最大数量。请选择部分文档进行重新签入，然后重试。如果问题仍然存在，请联系帮助台。',
	1267: 'Document is conflicted. Resolve conflicts and try again.  If the issue persists, contact the Help Desk.',
	1268: 'Document is already in co-edit mode. end co-edit mode and try again.  If the issue persists, contact the Help Desk.',
	1269: 'Document is a shared evidence. Unlink and try again.  If the issue persists, contact the Help Desk.',
	1270: '在多用户编辑模式下，无法删除或更新文档版本。请结束多用户编辑，然后重试。如果问题仍然存在，请联系帮助台。',
	1271: '文档未处于共同编辑模式或正在结束共同编辑模式。如果问题仍然存在，请联系帮助台',

	/*Assertions*/
	2001: '无效的创建请求。',
	2002: '缺少认定名称。',
	2003: '缺少认定。',
	2004: '获取认定指令失败。',
	2005: '无效的项目ID。',
	2006: '通过ID指令获取认定失败。',
	2007: '获取认定WCGW指令失败。',

	/*Risks*/
	4001: '目前不能完成此操作。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	4002: '缺少风险名称。',
	4003: '缺少风险。',
	4004: '获取风险指令失败。',
	4005: '无效的项目ID。',
	4006: '通过ID指令获取风险失败。',
	4007: '无效的查询请求。',
	4008: '此估计不再可用。请刷新页面并重试。如错误仍然存在，请联系帮助台。',
	4009: '无效的更新请求。',
	4010: '已经分配至该风险的特定WCGW',
	4011: 'WCGW列表不能为空。',
	4012: '无法获取风险类型。',
	4013: '无效的创建请求。',
	4014: '相关的认定无效。刷新页面，然后重试。如果此错误仍然存在，请联系帮助台。',
	4015: 'WCGW无效。刷新页面，然后重试。如果此错误仍然存在，请联系帮助台。',
	4016: '所选风险/估计均已删除。关闭此模式以查看更新的列表。',
	4017: '认定对该风险无效。刷新页面，然后重试。如果此错误仍然存在，请联系帮助台。',
	4018: '传递的风险类型ID无效。',
	4019: '风险名称无效。',
	4020: '文档ID无效。',
	4021: '风险名称不得超过500个字符。',
	4023: '认定ID列表不能为空。',
	4024: '创建有限风险文档记录表时出错。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	4025: '账户ID无效。',
	4026: '认定ID无效。',
	4027: '认定风险模型不能为空',
	4031: '无效的风险或表格正文选项。',
	4035: '若为特别风险或舞弊风险，则无法编辑“为较高风险”。',
	4036: '如果认定ID未传入，则知识库认定ID不能为空。知识库认定ID必须包括在所列举的类型中',
	4037: '此账户的知识库认定ID已存在。',
	4038: '风险类型ID与允许的选项不匹配。',
	4062: '此重大交易类别不再可用。请刷新页面并重试。如错误仍然存在，请联系帮助台。',
	4063: '无法编辑SCOT关联。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	4076: 'This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	4079: 'This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*TASK*/
	5001: 'Get all tasks failed.',
	5002: '此任务在此项目中不再可用。',
	5003: '获取任务的文档失败。无效的请求参数。',
	5004: '获取文档相关任务失败。无效的请求参数。',
	5005: '通过ID获取任务失败。',
	5006: '获取任务类别指令失败。',
	5007: '获取任务子类别指令失败。',
	5008: '获取任务描述指令失败。',
	5009: '获取任务客户请求指令失败。',
	5010: '保存任务客户请求指令失败。',
	5011: '您试图关联项目的任务已被删除或拒绝。',
	5012: '您试图关联的项目已被删除。',
	5013: '您试图关联项目的任务已被删除或拒绝。',
	5014: '您试图关联的文档已被删除。',
	5015: '获取任务证据指令失败。',
	5016: '获取WCGW任务指令失败。',
	5017: '项目ID应大于零。',
	5018: '无法完成关联。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	5019: '任务描述为空',
	5020: '所选任务已被删除或拒绝。因此，此操作目前无法完成。',
	5021: '来源项目ID缺失。',
	5022: '保存注解失败',
	5023: '未发现项目用户。',
	5024: '删除任务指令失败。',
	5025: '删除任务指令失败。',
	5026: '任务列表为空。',
	5027: '未发现复核。',
	5028: '文件名为必填项。',
	5029: '文件扩展名为必填项。',
	5030: '文件名不能包括：*/:<>\\?|"',
	5031: '文件名更新错误。',
	5032: '无效的文件ID。',
	5033: '未发现操作类型。',
	5034: '更改任务状态失败',
	5035: '您正在尝试执行的操作目前无法完成。请稍后再试。如果此错误持续存在，请联系服务台。',
	5036: '指令中正文不能为空。',
	5037: '请求在指令中不能为空。',
	5038: '输入唯一的文件名以继续。',
	5039: '文件名为必填项。',
	5040: '文件名限制为100个字符。',
	5041: '文件名不能包括：*/:<>\\?|"',
	5042: '所选任务被拒绝。',
	5043: '该任务是一个构建步骤任务。',
	5044: '该任务是一个里程碑任务。',
	5045: '该任务不是PSP或OSP类型。',
	5046: '超出字符数限制。',
	5047: '超出字符数限制。',
	5048: '必填字段。',
	5049: '无法从此任务中删除所选文档。刷新页面，然后重试。如果问题仍然存在，请联系服务台。',
	5050: '任务组ID不能为零或无效',
	5051: '任务部分ID不应为零或无效',
	5052: '尝试将此文档添加到当前任务时出错。指令失败。',
	5053: '尝试在当前任务中更新此文档时出错。指令失败。',
	5054: '尝试在当前任务中添加此文档的副本时出错。指令失败。',
	5055: '尝试重命名该文档时出错。指令失败。',
	5056: '不能编辑知识或组任务的任务标题。',
	5057: '任务类型应该是Ost类型',
	5058: '该文档与系统关联，因此无法从任务中删除。',
	5059: '无效的时间阶段值。',
	5060: '添加到证据错误。指令失败',
	5061: '另一个用户更新了所列报的信息。刷新页面，然后重试。如果错误仍然存在，请联系服务台。',
	5062: '所请求的文档在EY Atlas中不适用您所选择的项目类型。请与帮助台联系，以便可以将信息提供给内容作者，以供将来使用。',
	5063: '无效的补丁操作。',
	5064: '所选任务已被删除或拒绝。因此，此操作目前无法完成。',
	5065: '无法更新任务源类型。请求无效。',
	5066: '获取指引错误。指令失败。',
	5067: '无法更新任务性质类别。请求无效。',
	5068: '无法更新任务性质类别。请求无效。',
	5069: '删除任务分配指令失败。',
	5070: '一个或多个选定任务已被删除。如果错误持续存在，请再次尝试或联系帮助台。',
	5071: '无法更新任务。请求无效。',
	5072: '未发现编制人。请求无效。',
	5073: '未发现任务。',
	5074: '保存任务分配失败',
	5075: '除编制人外，同一项目组成员只能分配一项任务。如果错误持续存在，请再次尝试或联系服务台。',
	5076: '所选项目组成员不是此项目的活跃成员。如果错误持续存在，请再次尝试或联系服务台。',
	5077: '一个或多个选定任务已被删除。如果错误持续存在，请再次尝试或联系服务台。',
	5078: '所选任务分配已被删除。如果错误持续存在，请重试或联系服务台。',
	5079: '一个或多个选定任务已被删除。如果错误持续存在，请再次尝试或联系服务台。',
	5080: '文档版本记录不存在。',
	5081: '当前分配给此任务的用户不能分配为编制人。如果错误持续存在，请再次尝试或联系服务台。',
	5082: '更新失败。项目的文档名称应该唯一。刷新页面以删除此消息。',
	5083: '已超出任务详情字符数限制。',
	5084: '任务文档创建指令失败。',
	5085: '任务文档删除指令失败。',
	5086: '任务交接创建指令失败。',
	5087: '任务补丁指令失败。',
	5088: '此任务必须包含证据才能交接。再试一次或联系服务台。',
	5089: '与此任务相关联的所有证据（纸质配置文件除外）必须至少有一个编制人和复核人才能标记为完成。再试一次，如果错误仍然存在，请联系服务台。',
	5091: '纸质配置文件名称已经存在于项目中。',
	5092: '名称不能包含以下任何字符： */:<>\\?|\\',
	5093: '纸质文档名称超出最大长度（100个字符）。',
	5111: '所选账户、认定或有限风险账户已被删除。请刷新页面并重试。如仍有错误，请联系帮助台。',
	5116: '文档类型无效。',
	5139: '无法完成关联。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	5131: '无法完成关联。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	5146: '任务不能被标记为已完成。',
	5156: '任务关联关系无法编辑。请更新页面并重试。如果问题仍然存在，请联系帮助台。',

	/*WCGW*/
	6001: '创建可能出错项指令失败。',
	6002: '缺少可能出错项名称。',
	6003: '缺少可能出错项。',
	6004: '获取可能出错项指令失败。',
	6005: '无效的项目ID。',
	6006: '无效的认定ID。',
	6007: '通过ID获取可能出错项指令失败。',
	6008: '无效的请求。',
	6009: '无效的可能出错项ID。',
	6010: '该任务无法与可能出错项关联。',
	6011: '所选可能出错项被删除。',
	6012: '任务和可能出错项没有关联到同一个认定。',
	6013: '选定的任务不属于同一项目。',
	6014: '该任务无法与可能出错项解除关联。',
	6015: '该任务无法与可能出错项关联。',
	6016: '该任务被拒绝，无法与可能出错项关联。',
	6017: '该任务不是里程碑任务，并且不能与可能出错项关联。',
	6018: '该任务不是生成步骤任务，并且不能与可能出错项关联。',
	6019: '该任务不是PSP或OSP，并且不能与可能出错项关联。',
	6020: '任务和可能出错项关联到相同的认定，并且不能关联到可能出错项。',

	/*Engagement*/
	7001: '通过ID获取未发现。',
	7002: '按工作区ID获取项目指令失败。',
	7003: '获取全部项目实体指令失败。',
	7004: '按ID获取项目指令失败。',
	7005: '获取全部项目用户指令失败。',
	7006: '姓氏不得超过250个字符。',
	7007: '无效 用户 类型 错误。',
	7008: '名字不得超过250个字符。',
	7009: '用户GUI不能为空。',
	7010: '无效 用户 状态 错误。',
	7011: '创建项目用户指令失败。',
	7012: '{0}{1}不能被邀请，因为他/她已经是一个活跃或待定的项目组成员。',
	7013: '首字母缩写不得超过3个字符。',
	7014: 'EY Canvas登陆页目前无法访问。请重试，如问题仍然存在，请联系帮助台。',
	7015: '{0} {1}无法作为以下访问组被邀请：{2}已从项目中删除。刷新页面，然后重试。',
	7016: '{0} {1} 无法被邀请，该用户已为下列访问组的活跃成员：{2}',
	7017: '选定的组别不允许使用域名：{0}',
	7018: '电子邮件地址不能为空。',
	7019: '名字不能为空。',
	7020: '姓氏不能为空。',
	7021: '用户姓名首字母缩写不能为空。',
	7022: '用户主审办公室不能为空。',
	7023: '用户登录名不能为空。',
	7024: '用户安永角色不能为空。',
	7025: '用户项目角色不能为空。',
	7026: '无法完成操作。请重试，如问题仍然存在，请联系帮助台。',
	7027: '无效邮件地址',
	7028: '补丁项目用户指令失败。',
	7029: '项目用户——无效项目用户状态ID。',
	7030: '项目用户——无效项目用户角色ID。',
	7031: '一个或多个项目用户ID未找到。',
	7032: '电子邮件不得超过250个字符。',
	7033: '被请求的用户不能为空。',
	7034: '通用消息处理工具队列失败。',
	7035: '首字母缩写不得超过3个字符。',
	7036: '名字不得超过250个字符。',
	7037: '姓氏不得超过250个字符。',
	7038: '创建外部用户指令失败。',
	7039: '一个或多个用户不能被邀请，因为他们已经是活跃或待定的项目组成员。请刷新页面并重试，如问题仍然存在，请联系帮助台。',
	7040: '名字不得超过250个字符。',
	7041: '姓氏不得超过250个字符。',
	7042: '首字母不得超过3个字符。',
	7043: 'GPN不应大于250个字符。',
	7044: 'GUI不应大于250个字符。',
	7045: '通过ID获取外部用户失败。',
	7046: '用户不应为空。',
	7047: '目前无法保存更改。请重试，如果问题仍然存在，请联系帮助台。',
	7048: 'EY Canvas登陆页目前无法访问，需要编辑。请重试，如果问题仍然存在，请联系帮助台。',
	7049: '更新项目用户指令失败。',
	7050: '不能停用成员。项目必须至少有一个有权限管理项目的成员。更新您的选择并再次尝试。如果问题仍然存在，请联系帮助台。',
	7051: '通过ID获取内部用户失败。',
	7052: '未发现用户。',
	7053: '通过ID获取未发现。',
	7054: '通过项目ID指令获取快速链接失败。',
	7055: '权限不足，无法添加先前的成员。与具有适当权限的项目组成员沟通，以采取此行动。',
	7056: 'EY Canvas目前无法保存更改。请重试，如果问题仍然存在，请联系帮助台。',
	7057: '增加新成员的权限不足。与具有适当权限的项目组成员沟通，以采取此行动。',
	7058: '用户状态已更改。刷新页面，然后重试。如问题仍然存在，请联系帮助台。',
	7062: 'EY Canvas客户端目前无法访问，需要更新现有成员信息。请重试，如问题仍然存在，请联系帮助台。',
	7063: '无法停用外部成员。刷新页面，然后重试。如问题仍然存在，请联系帮助台。',
	7064: '修补程序操作无效。',
	7065: '无法在一个或多个访问组中激活{0}{1}。选定的组别不允许使用域名：{2}。',
	7066: '{0}不是有效用户。',
	7067: '创建外部用户指令失败。',
	7068: 'EY Canvas客户端目前无法访问，需要更新现有成员信息。请重试，如问题仍然存在，请联系帮助台。',
	7069: '选定的访问组未处于活动状态：{0}。移除并重试。',
	7072: '取消链接项目过程中出错。刷新页面，然后重试。如错误仍然存在，请联系帮助台。',
	7074: '无法保存更改。项目必须至少具有一位活跃成员，该成员具有管理项目的权限，且已解决独立性问题。如问题仍然存在，请联系帮助台。',
	7079: '提交独立性指令失败。',
	7080: '无效的用户ID，或提交的独立性不符要求，因为此用户ID不属于已登录用户。',
	7081: "请在完成所有问题后点击'提交'。使用'显示未完成问题'选项筛选未完成的问题。如果问题仍然存在，请联系帮助台。 ",
	7082: '未发现该请求的独立性文件。',
	7083: 'SDM汇总指令失败。',
	7084: '用户ID无效或已登录用户无权执行此独立性操作。',
	7085: '独立性批注应少于4000个字符。',
	7086: '独立性操作指令失败。',
	7087: '你必须为审计负责合伙人、项目合伙人或执行总监，才可向此用户授权访问权限、拒绝向其授权访问权限或覆盖其访问权限。',
	7088: '独立性提交变更失败，请稍后再试',
	7098: '无效Pace类型ID。',
	7099: '无可用独立性模板，请重试，如问题仍然存在，请联系帮助台。',
	7154: '没有找到当前Canvas表格的用户',
	7155: '恢复的项目不允许提交配置文档。',
	7156: '内容刷新中。请稍后再试。如问题长时间未解决，请联系IT帮助台。',
	7158: '文档不可用。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',

	/*SCOT*/
	8001: '创建重大交易类别指令失败。',
	8002: '缺少重大交易类别名称。',
	8003: '缺少重大交易类别。',
	8004: '获取重大交易类别指令失败。',
	8005: '无效的项目ID。',
	8006: '无效的认定ID。',
	8007: '通过ID指令获取重大交易类别失败。',
	8008: '无效的请求。',
	8009: 'The selected SCOT has been deleted. Close this modal to see the updated list.',
	8010: '项目ID不能为零值或空。',
	8011: '项目ID应大于零。',
	8012: '文档ID无效。',
	8013: '重大交易类别更新指令失败。',
	8014: '重大交易类别名称不能为零值或空。',
	8015: '重大交易类别名称不得超过500个字符。',
	8016: '重大交易类别策略类型无效。',
	8017: '重大交易类别类型无效。',
	8018: '重大交易类别IT应用程序无效。',
	8019: '当应用无IT应用程序时，重大交易类别IT应用程序应为空。',
	8028: 'The selected SCOT has been deleted. Close this modal to see the updated list.',

	/*User*/
	10001: '未发现登录的用户首选项。',
	10002: '用户全部获取指令失败。',
	10003: '用户存在获取指令失败。',
	10005: '无法检索用户详细信息',

	/*Risk Type*/
	11001: '无效的创建请求。',
	11002: '缺少风险类型名称。',
	11003: '缺少风险类型。',
	11004: '获取风险类型指令失败。',
	11005: '无效的项目ID。',

	/*TaskDocuments*/
	80004: '一个或多个任务无效。',
	80005: '文档ID无效。',

	/*Edit Control*/
	83001: '无法获取控制。',
	83002: '无法按ID获取控制',
	83003: '控制ID为空。',
	83004: '控制ID无效',
	83005: '文档ID无效。',
	83006: '缺少控制名称。',
	83007: '控制名称长度无效。',
	83008: '请求无效。',
	83009: '控制频率ID无效。',
	83010: '控制类型ID无效。',
	83011: '控制IT应用程序无效。',
	83012: '控制设计有效性类型ID无效。',
	83013: 'IT应用程序ID无效。',
	83014: '如果控制类型为手动预防或手动发现，仅允许服务机构类型的IT应用程序。',
	83015: '只有依赖IT的手动控制可以选择手动IPE测试。',
	83016: '对有此配置文件的项目不允许较低风险控制。',
	83017: 'FilterSCOT重复。',
	83018: '控制名称超过500个字符。',
	83019: '可能出错项ID无效。',
	83020: '不允许可能出错项ID重复。',
	83021: '不允许IT应用程序ID重复。',
	83022: '参数{0}无效。',
	83023: '当前页面无效。',
	83024: '页面大小无效。',
	83025: '搜索字符串不得超过100个字符。',
	83026: '没有服务机构的IT应用程序只能关联到依赖IT的手动控制或IT应用程序两类控制',
	83027: 'FilterWCGW重复。',
	83028: '知识控制ID无效',

	112000: '未找到源文档和目标文档的文档。',
	112001: '无法执行指令，因为请求无效。',
	112002: '指令中请求正文不能为零值或空。',
	112003: '源文档ID与目标文档ID应不相同。',
	112004: '源文档ID不应为零值或空。',
	112005: '目标文档ID不应为零值或空。',
	112006: '源项目ID不应为零值或空。',
	112007: '目标项目ID不应为零值或空。',
	112008: '指定项目的源文档无效。',
	112009: '指定项目的目标文档无效。',
	112010: '未找到目标项目ID。',
	112011: '进行源项目表格关联的角色权限不足。请联系项目管理员以获得足够的权限。',
	112012: '进行目标项目表格关联的角色权限不足。请联系项目管理员以获得足够的权限。',
	112013: '源项目用户无效。',
	112014: '目标项目用户无效。',
	112015: '用于共享的源文档类型无效。',
	112016: '用于共享的目标文档类型无效。',
	112017: '源文档类型与目标文档类型不匹配。',
	112018: '源资料表格ID与目标知识库表格ID不匹配。',
	112019: '源文档和目标文档已存在共享链接。',
	112020: '源文档和目标文档工作区不匹配。',
	112021: '目标文档不能作为目标。',
	112022: '所选活动已与其他活动共享，无法进一步选择该活动进行共享。',
	112023: '源文档不能作为目标。',
	112024: '无法找到目标文档ID的项目ID。',
	112025: '无法找到源文档ID的项目ID。',
	112026: '源文档ID和目标文档ID的源文档不能相同。',
	112027: '无法继续提交配置文档，因为项目已共享EY Canvas FIT促进工具活动。请删除活动链接以继续。',
	112028: '源项目ID和目标项目ID不能相同。',
	112029: '源项目ID或目标项目ID和文档ID应与定向发送的项目ID和文档ID匹配。',
	112030: '指定项目的文档无效。',
	112031: '文档ID不应为零或空。',
	112032: '项目ID不应为零或空。',
	112033: '目标文档ID应该是唯一的。',
	112034: '目标文档或源文档已共享。',
	112035: '对于现有已添加链接的回答关联文档，只能有一个目标。',

	/*MissingDocument*/
	116001: 'Create form failed.',
	116002: '找不到给定文档类型ID的知识库文档。',
	116004: '文档创建失败。请刷新或稍后再试。如果问题仍然存在，请联系帮助台。',

	/* Annotation Errors*/
	12001: '无效的请求参数。',
	12002: '创建注解指令失败。',
	12003: '获取注解失败。',
	12004: '无效的项目ID。',
	12005: 'ID集合必须大于零。',
	12006: 'ID集合不得为空。',
	12007: '无效任务ID。',
	12008: '无效的文档ID。',
	12009: '必须有有效的文档ID或任务ID。',
	12010: '需要上级答复。',
	12011: '无效状态ID。',
	12012: '文件类型应该为440GL。',
	12013: '无效注解类型。',
	12014: '无效项目用户。',
	12015: '此文件已被另一个用户删除。',
	12016: '更新注解指令失败。',
	12017: '您当前尝试回复的注释已被项目组其他成员删除。请刷新页面并重试。',
	12018: '注解更改操作类型不应为空。',
	12019: '复核注释已删除。',
	12020: '拟执行操作无效。',
	12021: '用户不是注解作者',
	12022: '编辑用户缺失。',
	12023: '创作用户不存在或不属于项目。',
	12024: '需要提供被分配用户。',
	12025: '被分配的用户不存在或不属于项目。',
	12026: '无效注释。',
	12027: '注释不得为空。',
	12028: '截止日期为必填项。',
	12029: '有效优先级为必填项。',
	12030: '此注释的状态已更改。您无法再编辑或回复注释。请关闭并重新打开窗口以查看更新的数据并继续编辑。',
	12031: '注解必须为最高级别。',
	12032: '注解不得为最高级别。',
	12033: '以下值中至少一个不能为空：优先级类型、到期日、状态或分配的用户。',
	12034: '注释超过了最大长度（4,000个字符）。',
	12035: '搜索字符串超过最大长度（500个字符）。',
	12036: '只接受任务ID或文档ID，两者都不允许。',
	12037: '无效的补丁操作。',
	12038: '您正尝试编辑一个已不存在的注释。刷新页面，然后重试。',
	12039: '您正尝试编辑与另一个项目组成员相同的注释。刷新页面，然后重试。',
	12040: '获取注解用户指令失败。',
	12041: '获取注解用户失败。无效的查询值。',
	12042: '文档类型无效，无法创建注解。',
	12043: '您正尝试向不再存在的任务或文档添加注释。刷新页面，然后重试。',
	12044: '您正尝试编辑一个已不存在的注释。刷新页面，然后重试。',
	12045: '未发现复核注释。',
	12046: '所选附注已被另一用户删除。',
	12047: '您只能删除处于打开状态的注释的回复。刷新页面，然后重试。',
	12048: '您正尝试改变一个已不存在的注释的状态。刷新页面，然后重试。',
	12049: '您正在尝试删除已经被其他项目组成员删除的注释的回复。刷新页面，然后重试。',
	12050: '您只能删除处于“关闭”状态的注释。',
	12051: '仅可为有效Helix文档创建备注型注解。',
	12052: '你所查找的备注型注解已被删除。',
	12060: '索引编号为必填，且数值需大于0且小于1000。',
	12061: '参考编号必须为空。',
	12062: '仅可为批注创建注解。',
	12066: '您正在尝试回复未开放的注释。',
	12067: '您正在尝试删除已经被其他项目组成员删除的批注的回复。请刷新页面并重试。',
	12068: '您正在尝试使用无效或已不存在的记录添加注释。请刷新页面并重试。',
	12069: '您正在尝试使用无效或已不存在的记录更新注释。请刷新页面并重试。',
	12070: '每个财务报表只能添加一个批注。请编辑现有内容。',
	12071: '无法删除信息。请重试。如果问题仍然存在，请联系帮助台。',

	/*FlowchartStepControl*/
	123054: 'Control relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',
	123045: 'This control is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepWCGW*/
	123022: '此流程图步骤不再可用。请刷新页面并重试。如错误仍然存在，请联系帮助台。',
	123023: '此流程图步骤不再可用。请刷新页面并重试。如错误仍然存在，请联系帮助台。',
	123055: 'WCGW relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepITApplicationSO*/
	123056: 'IT Application / service organization relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the issue persists.',



	/*FlowchartStepDocument*/
	123048: '无法编辑文档关联。请刷新页面并重试。如错误仍然存在，请联系帮助台。',
	123033: '此流程图步骤不再可用。请刷新页面并重试。如错误仍然存在，请联系帮助台。',
	123002: '此文档不再可用。请刷新页面并重试。如错误仍然存在，请联系帮助台。',

	/*Configuration*/
	13001: '未发现配置。',
	13002: '获取配置API指令失败。',

	/*Documents*/
	14001: '由于请求为空，因此无法执行指令。',
	14002: '未发现文档。指令失败。',
	14003: '项目ID应大于零。',
	14004: '文档ID不能为零值或空。',
	14005: '尝试获取相关任务时出错。指令失败。',
	14006: '所选文档无法删除。稍后再试。如果问题仍然存在，请联系帮助台。',
	14007: '发生意外错误。',
	14008: '签字期间发生意外错误。',
	14009: '添加签字时发生错误。',
	14010: '批准ID应大于零。',
	14011: '删除签字时发生错误。',
	14012: '正文不能为空。',
	14013: '所选文档无法取消链接。稍后再试。如果问题仍然存在，请联系帮助台。',
	14014: '通过文档ID指令获取账户失败。',
	14015: '无效的相关实体。',
	14016: '获取文档批准指令失败。',
	14017: '获取所有文档发现指令失败。',
	14018: '获取所有文档指令失败。',
	14019: '未找到签署。',
	14020: '无效的操作值。',
	14021: '无效的发现类型。',
	14022: '该文档不属于该项目。',
	14023: '该文档变更类型无效。',
	14024: '获取所有文档指令失败。参数无效。',
	14025: '创建文档复核时发生错误。API指令失败。',
	14026: '项目ID不能为空。API指令失败。',
	14027: '项目ID不能为空。',
	14028: '用户ID无效。',
	14029: '用户无权执行此操作。',
	14030: '在此项目中未发现具有已通过的ID的文档。',
	14031: '文档复核ID无效。',
	14032: '未发现文档复核。',
	14033: '已批准文档。',
	14034: '正在为工作区域中的另一个项目进行取消链接，或者该文档已被取消链接。刷新页面，然后重试。如果问题仍然存在，请联系帮助台。',
	14035: '该文档没有与指定的项目共享。',
	14036: '版本号应大于零。',
	14037: '此时不能完成操作。刷新页面，然后重试。如果问题仍然存在，请联系帮助台',
	14038: '无法获取文档更改原因。',
	14039: '无法通过ID获取文档更改原因。',
	14040: '无法更新更改原因。',
	14041: '无效的更改原因。',
	14042: '更新更改原因失败。',
	14043: '无法创建更改原因。',
	14044: '无法删除更改原因。',
	14045: '无效的更改原因ID。',
	14046: '文档不可用。刷新页面，然后重试。如果问题仍然存在，请联系帮助台。',
	14047: '文档已经分配了更改原因。',
	14048: '文档有冲突，请先解决，然后再继续。',
	14049: '无效的数据，无法检查文档是添加到项目组还是用户。',
	14050: '删除参考文档时出错。',
	14052: '搜索文本超出了允许的最大长度（500个字符）。',
	14053: '由于请求为空，因此无法执行指令。',
	14054: '无效的补丁操作。',
	14055: '解决文档历史记录指令失败。',
	14056: '尝试排列消息时发生错误。',
	14057: '项目中已经存在具有相同名称的文档。',
	14058: '已找到多个具有相同编号的文件版本。请联系帮助台，以获得进一步帮助。',
	14059: '无法找到已选定的文件版本。请刷新页面并重试。如果问题仍然存在，请联系帮助台获得进一步帮助。',
	14060: '目前不能完成该操作。请稍后重试。如果问题仍然存在，请联系帮助台获得进一步帮助。',
	14061: '文件未共享，无法取消该文件链接。如果问题仍然存在，请联系帮助台获得进一步帮助。',
	14062: '该文件机密性类型ID无效。',
	14063: '无法变更此文件类型的机密性类型。',
	14064: '用户没有权限。',
	14065: '自定义描述无效。 ',
	14066: '无法更新扩展名。 ',
	14067: '扩展名无效。 ',
	14068: '无法获取文件扩展名。 ',
	14069: '此文件已被另一个项目组成员取消链接。如果错误仍然存在，请刷新页面并联系帮助台',
	14070: 'Invalid file type.',
	14071: 'The selected document version is no longer available. Please close and reopen this window to see the latest set of historical versions for the document.',
	14072: '源文件ID不应该为零值或空。',
	14073: 'Canvas表格ID不应该为零值或空。',
	14074: 'Canvas表格ID不应重复。',
	14075: '无法将文件与Canvas表格关联。',
	14076: '未找到与源文件关联的Canvas表格。',
	14077: '未找到源文件。指令失败。',
	14078: '当前文件已与指定的Canvas表格关联。',
	14079: 'This document has been deleted and therefore it cannot be opened.',
	14080: 'The source approval user id is invalid.',
	14081: 'The source approval user id should valid GUID and must not be empty GUID.',
	14082: 'The modify user id is invalid.',
	14083: 'The modify user id should be a valid GUID and must not be empty GUID.',
	14084: 'File name cannot include: */:<>\\?|""',
	14085: 'The document name exceeded maximum length allowed.',
	14086: 'DocService failed while updating document details.',
	14087: 'The input is not valid.',
	14088: 'A input has duplicate document names.',
	14089: 'The bookmark observation is not valid.',
	14090: 'Request status has changed. Please refresh the page and try again if required. If the issue persists, contact the help desk.',
	14091: 'This request has been deleted. Refresh the page to view updated data. If the issue persists, contact the Help Desk.',
	14092: 'Document not eligible for update. Refresh the page and try again.  If the issue persists, contact the Help Desk.',

	/*SEM*/
	15001: '获取账户ID的SEM汇总表指令因无效请求而失败。',
	15002: '无效的账户ID。',
	15003: '无效的项目ID。',
	15004: '无效的汇总表类型。',
	15005: '未发现相关账户。刷新页面，然后重试。如果问题仍然存在，请联系帮助台。',

	/*Timephase*/
	16001: '获取时间阶段失败。',
	16002: '项目必须大于零。',
	16003: '时间阶段必须大于零。',
	16004: '无效的任务ID值。',
	16005: '无效的时间阶段值。',

	/*Validations*/
	17001: '缺少构建步骤ID或文档类型ID。',
	17003: 'The document could not be found. Refresh the page and try again. If the issue persists, contact Help Desk.',

	/*TaskGroupSection*/
	18001: '获取所有任务组部分指令失败。',

	/*Assignments*/
	19001: '创建任务指令失败。',
	19002: '获取任务指令失败。',

	/*Client Request*/
	21001: '客户请求关联指令失败。',

	/*Related Components*/
	22001: '获取相关组成部分指令失败。',

	/*Component Ceation*/
	22022: '本项目中已存在此组成部分名称',
	22024: '您尝试向其发送指引的组成部分不可用或已被删除。',
	22027: '集团指引未提及到期日。',
	22028: '未向组成部分发送范围指引。',
	22029: '没有需要发送给组成部分的新指引。',

	22040: '无法发送指引。请检查项目类型是否正确。',
	22048: '你尝试更新的组成部分系在复制项目时从另一个项目复制过来',

	/*Send Instruction*/
	22049: 'Group instructions cannot be sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk.',

	/*User Presence*/
	23001: '未发现已登录用户状态。',
	23002: '获取用户状态指令失败。',
	23003: '用户状态文档验证失败。',
	23004: '用户状态删除指令失败。',
	23005: '文档不再被任何其他用户打开。刷新页面，然后重试。如果问题仍然存在，请联系帮助台。',

	/* Forms */
	24001: '获取表格指令失败。',
	24002: '无效的项目ID。',
	24003: '文档ID不能为零值或空。',
	24005: '未找到表格标题。',
	24004: '无法找到文档。请刷新页面并重试。如问题仍然存在，请联系帮助服务中心。',
	24006: 'Section is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24007: '无效的请求参数。',
	24008: '标题ID不应该为零或空。',
	24009: '章节ID不应该为零或空。',
	24010: '表格正文应对更新操作失败。',
	24011: '表格正文回复更新请求无效。',
	24012: 'Body is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24013: '表格正文选项ID无效。',
	24014: '表格正文类型ID无效。',
	24015: '给定正文类型ID的请求正文无效。',
	24016: '给定正文类型ID的自由文本无效。',
	24017: '最大字符数，包括多文本格式标签。减少长度或删除不必要的格式，然后重试。',
	24018: '不允许回复给定的正文类型ID。',
	24019: '正文ID不应该为零或空。',
	24020: '无效的请求正文。',
	24021: '正文被删除。',
	24022: '国家不应为零或空。',
	24023: '语言不应为零或空。',
	24024: '下层服务线不应为零或空。',
	24025: 'Gam层面不应为零或空。',
	24026: '页眉创建指令失败。',
	24027: '创建标题的请求无效。',
	24028: '重复的单元创建指令失败。',
	24029: '无效的表格单元类型。',
	24030: '章节已删除。请刷新页面并重试，如仍有问题，请联系帮助台。',
	24031: '主要内容不是自定义主要内容。',
	24032: '页眉创建请求无效。',
	24033: '提供的文档实体ID无效。',
	24034: '提供的实体ID无效。',
	24035: '创建相关文档时出现错误。',
	24036: '删除相关文档时出现错误。',
	24037: '相关文件ID不应为零或空。',
	24038: '文件无效或不存在。',
	24039: '相关文档无效或不存在。',
	24040: '自定义创建正文失败。',
	24041: '创建正文的请求无效。',
	24042: '创建章节的请求无效。',
	24043: '按ID获取章节指令失败。',
	24044: '创建章节失败。',
	24045: '当前页面无效。',
	24046: '页面大小无效。',
	24047: '文档相关对象ID无效。',
	24048: '已将对象与canvas表格关联。',
	24049: '未找到对象。',
	24050: '实体UID中通过的ID无效。',
	24051: '如果提供实体ID，则必须提供实体UID；反之亦然。',
	24052: '创建Canvas表格快照失败。',
	24053: '按ID获取标题指令失败。',
	24054: '按ID获取正文指令失败。',
	24055: '创建表格配置文档时出现错误。',
	24056: '文档“表格配置”已存在。',
	24057: '文档“表格配置”验证失败。',
	24058: '文档“表格配置”不存在。',
	24059: '非自定义章节。',
	24060: '文档“表格配置”验证失败。如果PCAOB-IA为真，则PCAOB-FS应为真。',
	24061: '文档“表格配置”验证失败。如果PCAOB-FS为假，则PCAOB-IA应为假。',
	24062: "文档“表格配置”验证失败。如果为‘非复杂’，则‘PCAOB - FS’/‘PCAOB - IA'应为假。 ",
	24063: '国家ID无效。',
	24064: '语言ID无效。',
	24065: '非自定义标题。',
	24066: '创建表格章节相关对象失败。',
	24067: '未找到对象。',
	24068: '可能出错项与重大交易类别无关。',
	24069: '参数{0}无效。',
	24070: '母公司实体UID中通过的ID无效。',
	24071: '获取表格章节相关对象失败。',
	24072: '无可用章节。请刷新页面并重试。如果仍有问题，请联系帮助台。',
	24073: '无法检索截图。请刷新页面并重试。如果仍有问题，请联系帮助台。',
	24074: '所选文档没有可用截图。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	24075: '截图ID无效。',
	24076: '实体ID不得为零或空',
	24077: '传入的表格章节相关的对象ID无效。',
	24078: '表格章节相关的对象ID不得为零值或空。',
	24079: '提供的母公司实体ID无效。',
	24080: '表格章节相关对象：未发现母公司实体记录。',
	24081: '未找到对象。',
	24082: '未找到对象。',
	24083: '更新表格配置文件时出现错误。',
	24084: '文件“表格配置文件”不存在。',
	24085: '语言ID应大于零。',
	24086: '国家ID应大于零。',
	24087: '知识库已交付文档无法更新。更新项目配置文档，以更改此表格配置文档。',
	24088: '角色没有内容编辑权限。向项目管理员获取足够权限。',
	24089: '自定义标题名称超过最大长度（500个字符）。请调整名称，然后重试。',
	24090: '自定义章节名称超过最大长度（500个字符）。请调整名称，然后重试。',
	24091: '章节自定义标签超过最大长度（100个字符）。请调整名称，然后重试。',
	24092: '自定义正文名称超过最大长度（500个字符）。请调整名称，然后重试。',
	24093: '定制章节名称不应该为零值或空。',
	24094: '正文名称不应为零值或空。',
	24096: '定制标题栏名称不应该为零值或空。',
	24097: '此时不能完成覆盖操作。',
	24098: '源文件或目标文件类型ID无效。',
	24099: '源文件和目标文件信息库表格ID不一致。',
	24100: '源文件ID不能为零值或空。',
	24101: '目标文件ID不能为零值或空。',
	24103: '标题栏已删除。刷新页面，然后重试。如果问题仍然存在，请联系帮助台。',
	24104: '标题栏不可编辑',
	24105: '正文不能编辑。',
	24106: '更新操作无效。',
	24107: '文件“表格配置文件”验证失败。如果PCAOB-FS为真，则复杂应为真。',
	24108: '文件“表格配置文件”验证失败。如果PCAOB-IA为真，则PCAOB-FS应为真。',
	24110: '内容更新进行中。当前内容更新完成后，请在“Canvas表格内容更新”页面手动更新本表格的内容。',
	24111: '匹配和删除链接值不能相同。',
	24112: '源文件不能共享。',
	24114: '证据添加失败。刷新页面，然后重试。',
	24115: '证据移除失败。刷新页面，然后重试。',
	24116: '提交配置文件失败。刷新页面，然后重试。',
	24117: '表格回复不完整。刷新页面，然后重试。',
	24118: '文档ID和相关文档ID不能相同。',
	24119: '编辑正文相关对象失败。',
	24120: '正文相关对象ID不应为零值或空。',
	24121: '未找到对象。',
	24122: '并发令牌缺失',
	24124: '发现无效的目标文档。',
	24125: '未找到目标Canvas表格。',
	24126: '请求不应为零或空。',
	24127: '从EY Helix中导入数据失败。从EY Helix设置中重新导入数据，并重试。如果问题仍然存在，请联系帮助台。',
	24155: '对恢复的Canvas项目而言，重新提交项目文档设置是不允许的。',
	24164: '你没有更改该模板的适当权限。',
	24166: '不允许保存配置文档。该配置文档不可使用表格。',
	24167: '无法更新正文类型',
	24168: '“修改用户ID”不应为零值或空',
	24169: '只有“所关联回复更新”为错误时，“修改用户ID”才有值',
	24170: '请求的任务ID无效',
	24171: '请求的正文ID重复',
	24172: '章节比较指令失败',
	24173: '文档ID不能为空，且每次的不同文档数不能超过50个',
	24174: '请求正文文档ID中应包括路由文档',
	24175: '请求正文中的智能证据实体ID无效',
	24176: '正文文档ID应具有相同的知识库表格ID',
	24177: '章节ID无效',
	24178: '文档ID和文档ID列表的组合无效',
	24179: '文档ID列表不能大于50',
	24180: '无法更新重要性水平',
	24181: '无效的表格正文ID',
	24182: '无效的知识表格ID或知识表格正文ID',
	24183: '将文档与此表格关联时出错。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	24184: '该EY Canvas项目的用户未处于活动状态。通过管理团队邀请用户，然后重试。如问题仍然存在，请联系帮助台。',
	24188: 'One or more records are no longer available. Please refresh the page',

	/*Document Change Types*/
	25001: '获取文档更改类型指令失败。',
	25002: '无效的项目ID。',
	25003: '无效的请求。',

	/* Manage team */
	26001: '客户用户组获取全部指令失败。',
	26002: '创建新的客户组/电子邮件域名指令失败。',
	26003: '组名称不应为空。',
	26004: '电子邮件域名不能为空。',
	26005: '{0}电子邮件域名标签不得超过263个字符。',
	26006: '{0}电子邮件域名不得超过263个字符。',
	26007: '{0}电子邮件域的第一部分可以是*或字母数字，不允许使用其他特殊字符。',
	26008: '{0}如果第一部分是通配符，则后面必须有两个或多个部分。',
	26009: '{0}第一部分可以是*或字母数字，其他域名部分不允许特殊字符。',
	26010: '电子邮件域名应该是唯一的。',
	26011: '提供的客户访问组ID无效。',
	26012: '删除访问组时发生错误。确保没有分配给该组或该组成员的请求/外部任务，然后重试。',
	26013: '访问组已被删除，请刷新页面以获取最新数据。',
	26014: '至少需要一个电子邮件域名。',
	26015: 'EY Canvas客户端目前无法访问，需要更新现有成员信息。请重试，如果问题仍然存在，请联系服务台。',
	26016: '删除操作期间出错。',
	26017: '获取数据操作失败。',
	26018: '并发问题，访问组不再活跃。',
	26019: '保存操作失败。',
	26020: '分配了激活用户的电子邮件域名无法删除。更改尚未保存。',

	/* TimePhaseTypes - Milestones */
	27001: '获取里程碑详细信息失败。',

	/*Client Request Counts*/
	28001: '获取客户请求信息指令未获取任何结果。',

	/*Content updates Error messages*/
	29001: '通过ID获取未发现。',
	29002: '未发现操作类型。',
	29003: '未发现内容ID。',
	29004: '内容更新API指令失败。',
	29005: '正在进行内容更新请稍后再试，如果问题仍然存在，请联系帮助台。',
	29006: '无效的请求参数。',

	/*IT Process*/
	30001: '获取所有IT流程失败。',
	30002: '获取所有IT流程-无效的项目ID。',
	30003: 'IT流程名称不得为空。',
	30004: 'IT流程名称不应大于500个字符。',
	30005: '无法完成此操作。请更新页面并重试。如果问题仍然存在，请联系帮助台。',
	30006: '按ID获取IT流程失败。',
	30007: '无效的请求。',
	30008: 'IT流程不再可用。请更新页面并重试。如错误仍然存在，请联系帮助台。',
	30009: '未找到文档。',
	30010: '无法完成删除。刷新页面并重试。若问题仍然存在，请联系服务台。',
	30012: '无法完成删除。刷新页面并重试。若问题仍然存在，请联系服务台。',
	30017: '无法完成此操作。请更新页面并重试。如果问题仍然存在，请联系帮助台。',
	30018: '无法完成此操作。请更新页面并重试。如果问题仍然存在，请联系帮助台。',
	30019: '无法完成此操作。请更新页面并重试。如果问题仍然存在，请联系帮助台。',

	/*Checklist*/
	31001: '获取所有检查列表指令失败。',
	31002: '放置检查列表指令失败。',
	31003: '无效的检查列表参数。',
	31004: '无效的项目错误。',
	31005: '无效的请求参数错误。',
	31006: '无效的检查请求参数错误。',

	/*Archive*/
	32001: '无效的项目状态ID。',
	32002: '归档失败。',
	32003: 'V1 ARC指令失败，刷新页面，解决验证问题，然后再次尝试归档流程。如果错误仍然存在，请与帮助台联系。',
	32004: 'LDC中的项目状态更新失败。',
	32005: '无效项目缓存错误。',
	32006: '内容更新正在进行中。在内容更新完成之前，您无法归档此项目。如果错误仍然存在，请稍后再试并联系帮助台。',
	32007: 'ArcGUID为零值或空。',
	32008: 'FileGuid为零值或空。',
	32009: 'FileStoreHostTcp为零值或空。',
	32200: '此项目中有未解决的验证。刷新页面，解决验证问题，然后再次尝试归档流程。如果错误仍然存在，请与帮助台联系。',
	32300: '此项目中有未解决的验证。刷新页面，解决验证问题，然后再次尝试归档流程。如果错误仍然存在，请与帮助台联系。',

	/*RBAC*/
	33001: '未发现项目ID。',
	33002: '未发现用户ID。',

	/*Helix Linked Projects*/
	34001: '由于请求为空，因此无法执行指令。',
	34002: '项目ID不应为零值或空。',
	34003: '指令中正文不能为零值或空。',
	34004: '项目ID不能为零值或空。',
	34005: '项目名称不能为零值或空。',
	34006: '项目已链接，请刷新页面，然后重试。',
	34007: '获取所有Helix项目指令失败。',
	34008: '项目ID应大于零。',
	34010: '无法完成保存操作。刷新页面，然后重试。如果问题仍然存在，请联系帮助台。',
	34009: '项目ID已更改。请刷新页面并重试。',
	34011: '指令中货币类型不能为零值。',
	34012: '指令中货币代码不能为零值。',
	34013: '指令中业务单元不能为零值或空。',
	34014: '关联的EY Helix项目已被更改，无法更新设置。',
	34017: '无法连接至EY Helix。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	34018: '操作无法完成。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	34019: '导入完成，但数据无效。请刷新页面并点击导入重试。',
	34027: 'Helix项目进行中。',
	34036: '无法关联至EY Helix。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	34039: '此EY Helix项目不再是您项目的主要项目。请刷新页面并重试。',
	34040: '您必须具有<b>EY Helix导入</b>权限才能执行此操作。请与项目管理员沟通以获得访问权限。',

	/* PAANS */
	35001: '目前无法使用安永政策授权、批准和通知服务。我们无法确认所有相关政策是否已经完成。您尚未复核相关政策，请刷新页面并再次尝试。如果问题仍然存在，请联系帮助台。',

	/*Engagement Comments*/
	38001: '创建项目批注指令失败',
	38002: '获取全部项目批注指令失败',
	38003: '该操作无法完成。请刷新页面并重试。如仍有错误，请联系帮助台。',
	38004: '该操作无法完成。请刷新页面并重试。如仍有错误，请联系帮助台。',
	38005: '该操作无法完成。请刷新页面并重试。如仍有错误，请联系帮助台。',
	38006: '该操作无法完成。请刷新页面并重试。如仍有错误，请联系帮助台。',
	38007: '该操作无法完成。请刷新页面并重试。如仍有错误，请联系帮助台。',
	38008: '该操作无法完成。请刷新页面并重试。如仍有错误，请联系帮助台。',
	38009: '该操作无法完成。请刷新页面并重试。如仍有错误，请联系帮助台。',
	38010: '正文不能为零',
	38011: '批注文本不能为零或空',
	38012: '实体不存在于指定项目中',
	38013: '项目批注状态ID应大于零',
	38014: '母公司项目批注状态ID应大于零',
	38015: '无匹配特定标准的Canvas表格正文',
	38016: '您当前尝试回复的注释已被项目组其他成员删除。请刷新页面并重试。',
	38017: '提供的母公司项目批注已清除',
	38018: '提供的母公司项目批注本身即回复',
	38019: '提供的批注文本不能少于1个字符，不能多于4000个字符',
	38020: '提供的实体UID无效',
	38021: '提供的母公司实体UID无效',
	38022: '删除项目批注指令失败',
	38023: '批注ID不能为空',
	38024: '该操作应为有效操作',
	38025: '您正尝试编辑一个已不存在的批注。请更新页面并重试。',
	38026: '您不是批注所有者',
	38027: '未发现待更新批注',
	38028: '不允许更新已清除批注',
	38029: '只有作者可以更改批注文本',
	38030: '项目批注ID无效',
	38031: '批注状态ID不能为空',
	38032: '被请求的用户和项目之间没有关系记录。请刷新页面并重试。如仍有错误，请联系帮助台。',
	38033: '更新操作无效',
	38034: '被请求的批注ID正在被另一条批注使用',
	38035: '提供的批注ID无效',
	38036: '文档ID不能为零或空',
	38037: '将分配批注的用户ID不能为零',
	38038: '批注在打开或关闭时不能重新分配',
	38039: '您当前尝试删除的回复所对应的注释已被删除。请刷新页面并重试',
	38040: '回复不能设置到期日',
	38041: '回复不能设置优先级',
	38042: '批注应设置到期日',
	38043: '批注应设置优先级',
	38044: '您当前尝试编辑的回复所对应的批注已不存在。请刷新页面并重试',
	38045: '不允许编辑已分配至用户的项目状态ID及其内容或优先级',
	38046: '更新项目批注指令失败',
	38047: '您只能回复未解决状态的注释',
	39004: 'This tag group is no longer available. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*Risk-Estimate*/
	4064: '该风险不再可用。请刷新页面并重试。如错误仍然存在，请联系帮助台。',
	4065: '无法编辑风险关联。请刷新页面并重试。如问题仍然存在，请联系帮助台。',
	4066: '此估计不再可用。请刷新页面并重试。如错误仍然存在，请联系帮助台。',

	/*IT App/SO*/
	81001: '获取全部IT应用程序失败。',
	81002: '获取全部IT应用程序——无效的项目ID。',
	81003: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81004: '无法完成该操作。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	81005: '无法完成该操作。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	81006: '无效策略类型。',
	81007: '无效的文档ID。',
	81008: '按ID获取IT应用程序失败。',
	81009: '无法完成该操作。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	81010: '无法完成该操作。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	81011: '无法完成该操作。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	81012: 'IT应用程序ID不应为零或空。',
	81013: '无法编辑关联关系。请更新页面并重试。如错误仍然存在，请联系帮助台。',
	81014: '删除IT应用程序IT流程指令失败。',
	81015: 'IT应用程序IT流程ID不应为空。',
	81016: '无效的IT应用程序IT流程ID。',
	81017: '无法编辑关联关系。请更新页面并重试。如错误仍然存在，请联系帮助台。',
	81018: '创建IT应用程序IT流程指令失败。',
	81019: '取代IT应用程序的合格服务机构。',
	81020: '无法完成删除。刷新页面并重试。若问题仍然存在，请联系服务台。',
	81039: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81041: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/* ITControl */
	84001: '获取IT控制失败。',
	84002: '按ID获取IT控制失败。',
	84003: 'IT控制ID为零或空。',
	84004: '无效的IT控制ID。',
	84005: '文档ID无效。',
	84006: '缺少IT控制名称。',
	84007: '无效的IT控制名称长度。',
	84008: '无效的IT控制频率ID。',
	84009: '无效的IT控制方法类型ID。',
	84010: '无效的IT控制设计有效性类型ID。',
	84011: '无效的IT控制测试值。',
	84012: '无效的IT控制运行有效性类型ID。',
	84013: '缺少IT控制频率。',
	84014: '删除IT控制失败。',
	84015: '搜索字符不应超过100个字符。',
	84016: '无法完成该操作。刷新页面并重试。若问题仍然存在，请联系服务台。',
	84018: '无法完成该操作。刷新页面并重试。若问题仍然存在，请联系服务台。',
	84019: '无法完成更新。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',

	/*ITRisk*/
	86001: 'IT风险名称不得为空。',
	86002: 'IT风险名称不应大于500个字符。',
	86003: '无法完成此操作。请更新页面并重试。如果问题仍然存在，请联系帮助台。',
	86004: '按ID获取IT风险失败。',
	86005: '无法完成此操作。请更新页面并重试。如果问题仍然存在，请联系帮助台。',
	86006: '无法完成关联。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	86007: '无法完成此操作。请更新页面并重试。如果问题仍然存在，请联系帮助台。',
	86008: '无法完成关联。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	86009: '无法完成此操作。请更新页面并重试。如果问题仍然存在，请联系帮助台。',
	86010: '删除技术风险失败。',

	/*RiskFactorFormHeaders*/
	89001: '未找到风险因素关系。',
	89002: '未发现文档。',
	89003: '文档ID缺失。',
	89004: '理由长度超过4000个字符。',
	89005: '风险不能关联至选择的风险因素请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	89014: '风险ID无效。',
	89020: '无法保存风险因素。  刷新页面，然后重试。  如果问题仍然存在，请联系帮助台。',

	/*Materiality*/
	91001: '未找到重要性水平。',
	91002: '数据未保存。刷新页面，然后重试。如果问题仍然存在，请联系帮助台。',
	91003: '无效的重要性水平更新请求。',
	91004: '缺少“是否预计报告的年化金额”。',
	91005: '当将“是否预计报告的年化金额”更新为真时，必须指定“基础预测金额”。',
	91006: '基础预测金额不应超过15位或有小数位。',
	91007: '当将“是否预计报告的年化金额”更新为真时，“基础预测金额理由”必须为空。',
	91008: '基础金额预测理由太短或太长。',
	91009: '无效的重要性水平ID',
	91010: '无效的重要性水平正文类型ID',
	91011: '名义金额不能超过范围的上限',
	91012: '重要性水平金额不应超过15位数和4位小数',

	/*Group Structure - Sub Scopes */
	92013: '无法删除子范围，因为至少有一个相关的地区/组成部分项目小组。',
	92016: '子范围名称已存在',

	/*Helix Account Mappings */
	94001: '无法保存账户匹配。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	94004: '有一个或多个EY Canvas账户被删除，无法匹配。请刷新页面并重试。',
	94005: '无法恢复项目。请刷新页面并重试。如果问题持续，请联系帮助台。',

	/*Group Instructions */
	98001: '检索集团指引时出错。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	98002: '一个或多个知识指引章节ID无效。',
	98003: '创建集团指引时出现错误。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	98004: '指引名称不得为空。',
	98005: '指引名称不能超过500个字符。',
	98006: '指引描述不能为空。',
	98007: '指引描述不得超过30,000个字符。',
	98009: '指引名称不得重复。',
	98010: '到期日不能为空。',
	98011: '指引已删除。',
	98012: '未找到指引。',
	98013: '指引ID必须大于零。',
	98014: '保存集团指引时出错。 请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	98015: '无法删除指引的必要分配范围。',
	98016: '指引已删除',
	98017: '集团任务已删除。',
	98018: '删除后编辑集团任务。',
	98019: '未找到实体。',

	98020: '无法生成风险评估资料包。请刷新页面并重试。如问题仍然存在，请联系帮助台。',

	98021: '文档名称不得为空。',
	98022: '文档名称不得超过115个字符。',
	98023: '文档名称不得包含无效的XML字符。',
	98024: '数据包创建流程正在运行，完成该步骤可能需要十分钟。',
	98025: '一些指引无相关组成部分。分配组成部分至指引，然后再次生成集团风险评估沟通。',
	98026: '一些组成部分无相关账户。将账户关联至组成部分，然后再次生成集团风险评估沟通。',
	98027: '一些账户没有账户文档。创建账户文档，然后再次生成集团风险评估沟通。',
	98028: '此Canvas表格未与有效任务关联。',
	98029: '一个或多个组成部分仅供参考。',
	98030: '一个或多个组成部分并非来自此主审小组项目。',
	98031: '一个或多个范围并非来自此主审小组项目。',
	98032: '文档数量已超过限制。',
	98033: '无法从指引中删除要求的范围。',

	/*Estimate */
	115017: '未发现估计',

	/* Group Audit */
	106003: '错误。请刷新并重试。',

	/* TasksOverview */
	117001: '获取所有任务概览指令失败。',
	117002: '获取所有任务概览请求不得为空。',
	117003: '无效的项目ID。',
	117004: '无效的任务类别值。',
	117005: '无效的查看值。',
	117006: '无效的文档类别搜索值。',
	117007: '重复的任务类别ID。',
	117008: '重复的时间阶段ID。',
	117009: '重复的任务ID。',
	117010: '无效的文档ID。',
	117011: '重复的已分配给用户的ID。',

	/* Multientity */
	114001: '获取所有多实体失败。',
	114002: '标准任务实体名称不能为空。',
	114003: '标准任务实体名称不应超过500个字符。',
	114004: '标准任务法定名称不能为空。',
	114005: '标准任务法定名称不应超过500个字符。',
	114006: '只能使用MEST项目创建多实体。',
	114007: '创建多实体账户指令失败。',
	114008: '所选ST实体均已删除。关闭此模式以查看更新的列表。',
	114009: '账户ID无效。',
	114010: '标准任务实体简称不应超过100个字符。',
	114011: '标准任务实体提交配置文档请求无效。',
	114012: '标准任务实体提交配置文档请求正文无效。',
	114013: '标准任务实体提交配置文档请求正文应具有不同的标准任务实体ID。',
	114014: '提交配置文档请求的标准任务实体ID无效。',
	114015: '表格的回复未完成。请刷新页面并重试。',
	114016: '此项目无法进行内容更新。',
	114017: '项目没有多实体个人配置文档文件。',
	114018: '标准任务实体ID缺失多实体单个配置文档。',
	114019: '您正在匹配的一个或多个实体不再存在于项目中。请刷新页面并重试。',
	114020: '标准任务实体简称不能为空。',
	114021: 'DocumentId无效。',
	114022: 'EngagementId无效。',
	114023: 'STEntityDocument记录已存在。',
	114024: 'STEntityDocument记录不存在。',
	114025: 'STEntityDocument记录IsSystemAssociated。',
	114026: '无效的请求正文。',
	114028: '每个实体都没有配置文档。',
	114035: 'ST实体关联关系已存在。',
	114036: '请求所有实体更新时，至少应有一个配置文档有效。',
	114037: 'ST实体和账户的关系已被删除。',
	114038: 'Get all MultiEntity layers failed.',
	114039: 'Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',
	114040: 'Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',

	/* Sample List */
	121101: '样本列表ID无效。',
	121008: '样本列表ID无效。',
	121011: '此样本不再可用。请刷新页面并重试。如错误仍然存在，请联系帮助台。',
	121013: '此样本在本项目中不再可用。请刷新页面，然后重试。如果仍存在错误，请联系帮助台。',
	121016: '此样本不再可用。请刷新页面并重试。如果错误仍然存在，请联系帮助台。',
	121037: '执行此操作时出错。请刷新页面并重试。如果错误仍然存在，请联系帮助台。',
	121012: '无法更新属性状态。请刷新页面并重试。如仍有错误，请联系帮助台。',
	121025: '此文档不再可用。请刷新页面并重试。如错误仍然存在，请联系帮助台。',
	121027: '无法编辑文档关联。请刷新页面并重试。如问题仍然存在，请联系帮助台。',
	121028: '无法编辑文档关联。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	121014: '无法更新属性状态。请刷新页面并重试。如错误仍然存在，请联系帮助台。',
	121029: '此属性不再可用。请刷新页面并重试。如错误仍然存在，请联系帮助台。',
	121021: '无法更新属性状态。请刷新页面并重试。如仍有错误，请联系帮助台。',

	/*Control Attributes */
	122018: '无法完成操作。请刷新页面并重试。如问题仍然存在，请联系帮助台。',
	122021: '执行此操作时出错。请刷新页面并重试。如果错误仍然存在，请联系帮助台。',

	/*Flow chart*/
	123031: '表格正文ID无法链接至多个流程图。',

	1034: 'This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	1035: 'This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	/*Group Instructions */
	196033: '指引的分配项不能为0，必须至少为1。',

	/*Information Security */
	200001: '安永信息安全部禁止执行上述操作。请刷新页面并重试。如果问题仍然存在，请联系帮助台。 ',

	/*Tags */
	40007: '此标签不再可用。请刷新页面并重试。如果问题仍然存在，请联系帮助台。',
	40029: '无法编辑标记关联。请刷新页面并重试。如问题仍然存在，请联系帮助台。',
};

export const roleForMember = [{
	id: 1,
	role: '主管合伙人',
	roleAbbreviation: 'PIC'
},
{
	id: 2,
	role: '项目合伙人',
	roleAbbreviation: '项目合伙人'
},
{
	id: 3,
	role: '执行董事',
	roleAbbreviation: '执行总监'
},
{
	id: 4,
	role: '负责人',
	roleAbbreviation: '原则'
},
{
	id: 5,
	role: '高级经理',
	roleAbbreviation: '高级审计经理'
},
{
	id: 6,
	role: '经理',
	roleAbbreviation: '审计经理'
},
{
	id: 7,
	role: '高级审计员',
	roleAbbreviation: '高级审计员'
},
{
	id: 8,
	role: '职员',
	roleAbbreviation: '审计助理'
},
{
	id: 9,
	role: '实习生',
	roleAbbreviation: '实习生'
},
{
	id: 10,
	role: '项目质量复核人',
	roleAbbreviation: 'EQR'
},
{
	id: 11,
	role: '其他合伙人',
	roleAbbreviation: '其他合伙人'
},
{
	id: 12,
	role: 'GDS——职员',
	roleAbbreviation: 'GDS审计员'
},
{
	id: 13,
	role: '咨询（ITRA、TAS、人力资本或其他）',
	roleAbbreviation: 'ADV'
},
{
	id: 14,
	role: '税务',
	roleAbbreviation: '税务'
},
{
	id: 15,
	role: '管理支持服务',
	roleAbbreviation: 'ESS'
},
{
	id: 16,
	role: '总法律顾问',
	roleAbbreviation: 'GCO'
},
{
	id: 17,
	role: '审计质量复核人',
	roleAbbreviation: 'AQR'
},
{
	id: 18,
	role: 'ML组成部分项目小组',
	roleAbbreviation: 'MCT'
},
{
	id: 19,
	role: '客户主管',
	roleAbbreviation: '客户主管'
},
{
	id: 20,
	role: '客户职员',
	roleAbbreviation: '客户助理'
},
{
	id: 21,
	role: '内部审计主管',
	roleAbbreviation: '内部审计主管'
},
{
	id: 22,
	role: '内部审计职员',
	roleAbbreviation: '内部审计助理'
},
{
	id: 23,
	role: '监管者',
	roleAbbreviation: '监管机构'
},
{
	id: 24,
	role: '其他（如，尽职复核）',
	roleAbbreviation: '其他'
},
{
	id: 25,
	role: '办公室',
	roleAbbreviation: '办公室'
},
{
	id: 26,
	role: '地区',
	roleAbbreviation: 'Area'
},
{
	id: 27,
	role: '行业',
	roleAbbreviation: 'IND'
},
{
	id: 28,
	role: '国际',
	roleAbbreviation: 'NAT'
},
{
	id: 29,
	role: '全球',
	roleAbbreviation: 'GBL'
},
{
	id: 30,
	role: 'GDS-高级职员',
	roleAbbreviation: 'GDS-高级审计员'
},
{
	id: 31,
	role: 'GDS-经理',
	roleAbbreviation: 'GDS-经理'
}
];

export const accountConclusionTabs = {
	conclusions: '结论'
};

export const assertions = [{
	id: 1,
	assertionname: '完整性',
	assertionabbreviation: 'C',
	statementtypeid: 2,
	displayorder: 1
},
{
	id: 2,
	assertionname: '存在性',
	assertionabbreviation: 'E',
	statementtypeid: 2,
	displayorder: 2
},
{
	id: 3,
	assertionname: '计价',
	assertionabbreviation: 'V',
	statementtypeid: 2,
	displayorder: 3
},
{
	id: 4,
	assertionname: '权利和义务',
	assertionabbreviation: 'R&O',
	statementtypeid: 2,
	displayorder: 4
},
{
	id: 5,
	assertionname: '列报和披露',
	assertionabbreviation: 'P&D',
	statementtypeid: 2,
	displayorder: 5
},
{
	id: 6,
	assertionname: '完整性',
	assertionabbreviation: 'C',
	statementtypeid: 1,
	displayorder: 6
},
{
	id: 7,
	assertionname: '发生',
	assertionabbreviation: 'O',
	statementtypeid: 1,
	displayorder: 7
},
{
	id: 8,
	assertionname: '计量',
	assertionabbreviation: 'M',
	statementtypeid: 1,
	displayorder: 8
},
{
	id: 9,
	assertionname: '列报和披露',
	assertionabbreviation: 'P&D',
	statementtypeid: 1,
	displayorder: 9
},
{
	id: 10,
	assertionname: '完整性',
	assertionabbreviation: 'C',
	statementtypeid: 3,
	displayorder: 10
},
{
	id: 11,
	assertionname: '存在性/发生',
	assertionabbreviation: 'E/O',
	statementtypeid: 3,
	displayorder: 11
},
{
	id: 12,
	assertionname: '计量/计价',
	assertionabbreviation: 'M/V',
	statementtypeid: 3,
	displayorder: 12
},
{
	id: 13,
	assertionname: '权利和义务',
	assertionabbreviation: 'R&O',
	statementtypeid: 3,
	displayorder: 13
},
{
	id: 14,
	assertionname: '列报和披露',
	assertionabbreviation: 'P&D',
	statementtypeid: 3,
	displayorder: 14
}
];

export const documentChangeTypesOptions = [{
	value: 1,
	label: 'Non-administrative change'
},
{
	value: 2,
	label: 'Accepting revisions when the track changes functionality was used'
},
{
	value: 3,
	label: 'Adding additional cross referencing to evidence that already exists'
},
{
	value: 4,
	label: 'Adding an original confirmation response previously received via fax or email'
},
{
	value: 5,
	label: 'Cosmetic change'
},
{
	value: 6,
	label: '完成表格AP以及实质性职能评估'
},
{
	value: 7,
	label: 'Deleting or discarding superseded documentation'
},
{
	value: 8,
	label: 'Preparing the management letter'
},
{
	value: 9,
	label: 'Signing off on completion checklists relating to the archive process'
},
{
	value: 10,
	label: 'Sorting, collating and cross-referencing final documents',
},
{
	value: 12,
	label: 'MEST only: Modification to evidence related to entities with a report date later than the report date in EY Canvas'
},
{
	value: 11,
	label: 'When adjusted to the local time zone, the modification is on or before the report date (Americas only)',
}
];

export const KnowledgeFormProfileAnswer = [{
	value: 1,
	label: '复杂',
	display: true
},
{
	value: 2,
	label: '非复杂',
	display: true
},
{
	value: 3,
	label: '上市',
	display: true
},
{
	value: 4,
	label: '非上市',
	display: false
},
{
	value: 5,
	label: 'PCAOB - IA',
	display: true
},
{
	value: 6,
	label: '非PCAOB-IA',
	display: false
},
{
	value: 7,
	label: 'PCAOB - FS',
	display: true
},
{
	value: 8,
	label: '非PCAOB-FS',
	display: false
},
{
	value: 9,
	label: '集团审计',
	display: true
},
{
	value: 10,
	label: '非集团审计',
	display: false
},
{
	value: 11,
	label: '数字化',
	display: true
},
{
	value: 12,
	label: '核心',
	display: true
}
];

export const strategyType = [{
	StrategyTypeId: 1,
	StrategyTypeName: '控制'
},
{
	StrategyTypeId: 2,
	StrategyTypeName: '实质性'
}
];

export const controlTypeName = {
	1: '应用程序',
	2: '依赖IT的手动控制',
	3: '手动预防',
	4: '手动检查'
};

export const inCompleteList = [{
	value: 1,
	label: '不完整',
	title: '不完整'
}];

export const scotTypes = [{
	value: 1,
	label: '常规',
	title: '常规',
	isDisabled: false
},
{
	value: 2,
	label: '非常规',
	title: '非常规',
	isDisabled: false
},
{
	value: 3,
	label: '估计',
	title: '估计',
	isDisabled: false
}
];

export const scotTypesNew = [{
	value: 1,
	label: '常规',
	title: '常规',
	isDisabled: false
},
{
	value: 2,
	label: '非常规',
	title: '非常规',
	isDisabled: false
},
{
	value: 3,
	label: '估计',
	title: '估计',
	isDisabled: false
},
{
	value: 4,
	label: '财务报表决算过程',
	title: '财务报表决算过程',
	isDisabled: false
}
];

export const estimationTypes = [{
	value: 1,
	label: '否',
	title: '否',
	isDisabled: false
},
{
	value: 2,
	label: '是',
	title: '是',
	isDisabled: false
}
];

/* IT Control */
export const itApproachType = [{
	ITApproachTypeId: 1,
	ITApproachTypeName: '控制'
},
{
	ITApproachTypeId: 2,
	ITApproachTypeName: '实质性'
}
];

/*Controls */
export const controlFrequencyType = [{
	value: 1,
	label: '每天多次',
	title: '每天多次'
},
{
	value: 2,
	label: '每天',
	title: '每天'
},
{
	value: 3,
	label: '每周',
	title: '每周'
},
{
	value: 4,
	label: '每月',
	title: '每月'
},
{
	value: 5,
	label: '每季度',
	title: '每季度'
},
{
	value: 6,
	label: '每年',
	title: '每年'
},
{
	value: 8,
	label: '其他',
	title: '其他'
}
];

export const controlTypes = [{
	value: 1,
	label: 'IT应用程序控制'
},
{
	value: 2,
	label: '依赖IT的手动控制'
},
{
	value: 3,
	label: '手动预防'
},
{
	value: 4,
	label: '手动发现'
}
];

export const strategyTypeCheck = {
	1: '控制',
	2: '实质性'
};

export const designEffectivenessType = [{
	DesignEffectivenessTypeId: 1,
	DesignEffectivenessTypeName: '有效的'
},
{
	DesignEffectivenessTypeId: 2,
	DesignEffectivenessTypeName: '无效的'
}
];

export const controlEffectivenessType = [{
	ControlEffectivenessTypeId: 1,
	ControlEffectivenessTypeName: '有效的'
},
{
	ControlEffectivenessTypeId: 2,
	ControlEffectivenessTypeName: '无效的'
}
];

export const testing = [{
	testingId: 1,
	testingDescription: '是'
},
{
	testingId: 2,
	testingDescription: '否'
}
];

export const controlType = [{
	value: 1,
	label: 'IT应用程序控制',
	title: 'IT应用程序控制'
},
{
	value: 2,
	label: '依赖IT的手动控制',
	title: '依赖IT的手动控制'
},
{
	value: 3,
	label: '手动预防',
	title: '手动预防'
},
{
	value: 4,
	label: '手动检查',
	title: '手动检查'
}
];

export const aggregateITEvaluationType = [{
	value: 1,
	label: '支持',
	title: '支持'
},
{
	value: 2,
	label: '不支持',
	title: '不支持'
},
{
	value: 3,
	label: 'FS和ICFR支持',
	title: 'FS和ICFR支持'
},
{
	value: 4,
	label: 'FS仅支持',
	title: 'FS仅支持'
}
];

export const KnowledgeFormProfileQuestion = [{
	value: 1,
	label: '复杂'
},
{
	value: 2,
	label: '上市'
},
{
	value: 3,
	label: 'PCAOB - IA'
},
{
	value: 4,
	label: 'PCAOB - FS'
},
{
	value: 5,
	label: '位置'
},
{
	value: 6,
	label: '语言'
},
{
	value: 7,
	label: '集团审计'
},
{
	value: 8,
	label: 'Digital'
}
];

export const KnowledgeLanguage = [{
	value: 1,
	label: '英语'
},
{
	value: 2,
	label: '西班牙语（拉美）'
},
{
	value: 3,
	label: '法语（加拿大）'
},
{
	value: 4,
	label: '荷兰语'
},
{
	value: 5,
	label: '克罗地亚语'
},
{
	value: 6,
	label: '捷克语'
},
{
	value: 7,
	label: '丹麦语'
},
{
	value: 8,
	label: '芬兰语'
},
{
	value: 9,
	label: '德语（德国、奥地利）',
},
{
	value: 10,
	label: '匈牙利语'
},
{
	value: 11,
	label: '意大利语'
},
{
	value: 12,
	label: '日语（日本）'
},
{
	value: 13,
	label: '挪威语（挪威）'
},
{
	value: 14,
	label: '波兰语'
},
{
	value: 15,
	label: '斯洛伐克语'
},
{
	value: 16,
	label: '斯洛文尼亚语'
},
{
	value: 17,
	label: '瑞典语'
},
{
	value: 18,
	label: '阿拉伯语'
},
{
	value: 19,
	label: '简体中文（中国）'
},
{
	value: 20,
	label: '繁体中文（中国台湾）'
},
{
	value: 21,
	label: '希腊语'
},
{
	value: 22,
	label: '希伯来语（以色列）'
},
{
	value: 23,
	label: '印度尼西亚语'
},
{
	value: 24,
	label: '韩国（韩国）'
},
{
	value: 25,
	label: '葡萄牙语（巴西）'
},
{
	value: 26,
	label: '罗马尼亚语'
},
{
	value: 27,
	label: '俄语（俄罗斯）'
},
{
	value: 28,
	label: '泰语'
},
{
	value: 29,
	label: '土耳其语'
},
{
	value: 30,
	label: '越南语'
},
{
	value: 31,
	label: 'PCAOB——英文'
},
{
	value: 32,
	label: 'PCAOB——西班牙语（拉美）'
},
{
	value: 33,
	label: 'PCAOB——法语（加拿大）'
},
{
	value: 34,
	label: 'PCAOB——荷兰语'
},
{
	value: 35,
	label: 'PCAOB——克罗地亚语'
},
{
	value: 36,
	label: 'PCAOB——捷克语'
},
{
	value: 37,
	label: 'PCAOB——丹麦语'
},
{
	value: 38,
	label: 'PCAOB——芬兰语'
},
{
	value: 39,
	label: 'PCAOB——德语（德国、奥地利）',
},
{
	value: 40,
	label: 'PCAOB——匈牙利语'
},
{
	value: 41,
	label: 'PCAOB——意大利语'
},
{
	value: 42,
	label: 'PCAOB——日语（日本）'
},
{
	value: 43,
	label: 'PCAOB——挪威语（挪威）'
},
{
	value: 44,
	label: 'PCAOB——波兰语'
},
{
	value: 45,
	label: 'PCAOB——斯洛伐克语'
},
{
	value: 46,
	label: 'PCAOB——斯洛文尼亚语'
},
{
	value: 47,
	label: 'PCAOB——瑞典语'
},
{
	value: 48,
	label: 'PCAOB——阿拉伯语'
},
{
	value: 49,
	label: 'PCAOB——简体中文（中国）'
},
{
	value: 50,
	label: 'PCAOB——繁体中文（中国台湾）'
},
{
	value: 51,
	label: 'PCAOB——希腊语'
},
{
	value: 52,
	label: 'PCAOB——希伯来语（以色列）'
},
{
	value: 53,
	label: 'PCAOB——印尼语'
},
{
	value: 54,
	label: 'PCAOB——韩语（韩国）'
},
{
	value: 55,
	label: 'PCAOB——葡萄牙语（巴西）'
},
{
	value: 56,
	label: 'PCAOB——罗马尼亚语'
},
{
	value: 57,
	label: 'PCAOB——俄语（俄罗斯）'
},
{
	value: 58,
	label: 'PCAOB——泰语'
},
{
	value: 59,
	label: 'PCAOB——土耳其语'
},
{
	value: 60,
	label: 'PCAOB——越南语'
}
];

export const KnowledgeCountry = [{
	value: 1,
	label: '马约特岛'
},
{
	value: 2,
	label: '英属维尔京群岛'
},
{
	value: 3,
	label: '西班牙'
},
{
	value: 4,
	label: '伯利兹'
},
{
	value: 5,
	label: '秘鲁'
},

{
	value: 6,
	label: '斯洛伐克'
},
{
	value: 7,
	label: '委内瑞拉'
},
{
	value: 8,
	label: '挪威'
},
{
	value: 9,
	label: '福克兰群岛（马尔维纳斯）'
},
{
	value: 10,
	label: '莫桑比克'
},

{
	value: 11,
	label: '中国'
},
{
	value: 12,
	label: '苏丹'
},
{
	value: 13,
	label: '以色列'
},
{
	value: 14,
	label: '比利时'
},
{
	value: 15,
	label: '沙特阿拉伯'
},

{
	value: 16,
	label: '直布罗陀'
},
{
	value: 17,
	label: '关岛'
},
{
	value: 18,
	label: '诺福克群岛'
},
{
	value: 19,
	label: '赞比亚'
},
{
	value: 20,
	label: '留尼汪'
},

{
	value: 21,
	label: '阿塞拜疆'
},
{
	value: 22,
	label: '圣赫勒拿岛'
},
{
	value: 23,
	label: '伊朗'
},
{
	value: 24,
	label: '摩纳哥'
},
{
	value: 25,
	label: '圣皮埃尔和密克隆群岛'
},

{
	value: 26,
	label: '新西兰'
},
{
	value: 27,
	label: '库克群岛'
},
{
	value: 28,
	label: '圣卢西亚岛'
},
{
	value: 29,
	label: '津巴布韦'
},
{
	value: 30,
	label: '伊拉克'
},

{
	value: 31,
	label: '汤加'
},
{
	value: 32,
	label: '美属萨摩亚'
},
{
	value: 33,
	label: '马尔代夫'
},
{
	value: 34,
	label: '摩洛哥'
},
{
	value: 35,
	label: '国际审计准则（ISA）'
},

{
	value: 36,
	label: '阿尔巴尼亚'
},
{
	value: 37,
	label: '阿富汗'
},
{
	value: 38,
	label: '冈比亚'
},
{
	value: 39,
	label: '布基纳法索'
},
{
	value: 40,
	label: '托克劳'
},

{
	value: 41,
	label: '利比亚'
},
{
	value: 42,
	label: '加拿大'
},
{
	value: 43,
	label: '阿拉伯联合酋长国'
},
{
	value: 44,
	label: '朝鲜',
},
{
	value: 45,
	label: '蒙特塞拉特'
},

{
	value: 46,
	label: '格陵兰'
},
{
	value: 47,
	label: '卢旺达'
},
{
	value: 48,
	label: '斐济'
},
{
	value: 49,
	label: '吉布提'
},
{
	value: 50,
	label: '博茨瓦纳'
},

{
	value: 51,
	label: '科威特'
},
{
	value: 52,
	label: '马达加斯加'
},
{
	value: 53,
	label: '马恩岛'
},
{
	value: 54,
	label: '匈牙利'
},
{
	value: 55,
	label: '纳米比亚'
},

{
	value: 56,
	label: '马耳他'
},
{
	value: 57,
	label: '泽西岛'
},
{
	value: 58,
	label: '泰国'
},
{
	value: 59,
	label: '圣基茨和尼维斯'
},
{
	value: 60,
	label: '不丹'
},

{
	value: 61,
	label: '巴拿马'
},
{
	value: 62,
	label: '索马里'
},
{
	value: 63,
	label: '巴林'
},
{
	value: 64,
	label: '波斯尼亚和黑塞哥维那'
},
{
	value: 65,
	label: '法国'
},

{
	value: 66,
	label: '韩国',
},
{
	value: 67,
	label: '冰岛'
},
{
	value: 68,
	label: '葡萄牙'
},
{
	value: 69,
	label: '突尼斯'
},
{
	value: 70,
	label: '加纳'
},

{
	value: 71,
	label: '喀麦隆'
},
{
	value: 72,
	label: '希腊'
},
{
	value: 73,
	label: '法属南部领地'
},
{
	value: 74,
	label: '赫德岛和麦克唐纳群岛'
},
{
	value: 75,
	label: '安道尔'
},

{
	value: 76,
	label: '卢森堡'
},
{
	value: 77,
	label: '萨摩亚'
},
{
	value: 78,
	label: '安圭拉岛'
},
{
	value: 79,
	label: '荷兰'
},
{
	value: 80,
	label: '几内亚比绍'
},

{
	value: 81,
	label: '尼加拉瓜'
},
{
	value: 82,
	label: '巴拉圭'
},
{
	value: 83,
	label: '安提瓜和巴布达'
},
{
	value: 84,
	label: '国际财务报告准则（IFRS）'
},
{
	value: 85,
	label: '尼日尔'
},

{
	value: 86,
	label: '埃及'
},
{
	value: 87,
	label: '梵蒂冈'
},
{
	value: 88,
	label: '拉脱维亚'
},
{
	value: 89,
	label: '塞浦路斯'
},
{
	value: 90,
	label: '美国本土外小岛屿'
},

{
	value: 91,
	label: '俄罗斯'
},
{
	value: 92,
	label: '圣文森特和格林纳丁斯'
},
{
	value: 93,
	label: '根西岛'
},
{
	value: 94,
	label: '布隆迪'
},
{
	value: 95,
	label: '古巴'
},

{
	value: 96,
	label: '赤道几内亚'
},
{
	value: 97,
	label: '英属印度洋领地'
},
{
	value: 98,
	label: '瑞典'
},
{
	value: 99,
	label: '乌干达'
},
{
	value: 100,
	label: '马其顿',
},

{
	value: 101,
	label: '斯威士兰'
},
{
	value: 102,
	label: '萨尔瓦多'
},
{
	value: 103,
	label: '吉尔吉斯斯坦'
},
{
	value: 104,
	label: '爱尔兰'
},
{
	value: 105,
	label: '哈萨克斯坦'
},

{
	value: 106,
	label: '洪都拉斯'
},
{
	value: 107,
	label: '乌拉圭'
},
{
	value: 108,
	label: '格鲁吉亚'
},
{
	value: 109,
	label: '特立尼达和多巴哥'
},
{
	value: 110,
	label: '巴勒斯坦'
},

{
	value: 111,
	label: '马提尼克岛'
},
{
	value: 112,
	label: '奥兰群岛'
},
{
	value: 113,
	label: '法属波利尼西亚'
},
{
	value: 114,
	label: '科特迪瓦'
},
{
	value: 115,
	label: '黑山共和国'
},

{
	value: 116,
	label: '南非'
},
{
	value: 117,
	label: '南乔治亚岛和南桑威奇群岛'
},
{
	value: 118,
	label: '也门'
},
{
	value: 119,
	label: '中国香港'
},
{
	value: 120,
	label: '肯尼亚'
},

{
	value: 121,
	label: '乍得'
},
{
	value: 122,
	label: '哥伦比亚'
},
{
	value: 123,
	label: '哥斯达黎加'
},
{
	value: 124,
	label: '安哥拉'
},
{
	value: 125,
	label: '立陶宛'
},

{
	value: 126,
	label: '叙利亚'
},
{
	value: 127,
	label: '马来西亚'
},
{
	value: 128,
	label: '塞拉利昂'
},
{
	value: 129,
	label: '塞尔维亚'
},
{
	value: 130,
	label: '波兰'
},

{
	value: 131,
	label: '苏里南'
},
{
	value: 132,
	label: '海地'
},
{
	value: 133,
	label: '瑙鲁'
},
{
	value: 134,
	label: '圣多美和普林西比'
},
{
	value: 135,
	label: '斯瓦尔巴和扬·马延'
},

{
	value: 136,
	label: '新加坡'
},
{
	value: 137,
	label: '摩尔多瓦'
},
{
	value: 138,
	label: '中国台湾'
},
{
	value: 139,
	label: '塞内加尔'
},
{
	value: 140,
	label: '加蓬'
},

{
	value: 141,
	label: '墨西哥'
},
{
	value: 142,
	label: '塞舌尔'
},
{
	value: 143,
	label: '密克罗尼西亚联邦',
},
{
	value: 144,
	label: '阿尔及利亚'
},
{
	value: 145,
	label: '意大利'
},

{
	value: 146,
	label: '圣马力诺'
},
{
	value: 147,
	label: '利比里亚'
},
{
	value: 148,
	label: '巴西'
},
{
	value: 149,
	label: '克罗地亚'
},
{
	value: 150,
	label: '法罗群岛'
},

{
	value: 151,
	label: '帕劳'
},
{
	value: 152,
	label: '芬兰'
},
{
	value: 153,
	label: '菲律宾'
},
{
	value: 154,
	label: '牙买加'
},
{
	value: 155,
	label: '法属圭亚那'
},

{
	value: 156,
	label: '佛得角'
},
{
	value: 157,
	label: '缅甸'
},
{
	value: 158,
	label: '莱索托'
},
{
	value: 159,
	label: '美属维尔京群岛'
},
{
	value: 160,
	label: '开曼群岛'
},

{
	value: 161,
	label: '纽埃'
},
{
	value: 162,
	label: '多哥'
},
{
	value: 163,
	label: '白俄罗斯'
},
{
	value: 164,
	label: '多米尼加'
},
{
	value: 165,
	label: '印度尼西亚'
},

{
	value: 166,
	label: '乌兹别克斯坦'
},
{
	value: 167,
	label: '尼日利亚'
},
{
	value: 168,
	label: '瓦利斯和富图纳群岛'
},
{
	value: 169,
	label: '巴巴多斯'
},
{
	value: 170,
	label: '斯里兰卡'
},

{
	value: 171,
	label: '英国'
},
{
	value: 172,
	label: '厄瓜多尔'
},
{
	value: 173,
	label: '瓜德罗普岛'
},
{
	value: 174,
	label: '老挝'
},
{
	value: 175,
	label: '约旦'
},

{
	value: 176,
	label: '所罗门群岛'
},
{
	value: 177,
	label: '东帝汶'
},
{
	value: 178,
	label: '黎巴嫩'
},
{
	value: 179,
	label: '中非共和国'
},
{
	value: 180,
	label: '印度'
},

{
	value: 181,
	label: '圣诞岛'
},
{
	value: 182,
	label: '瓦努阿图'
},
{
	value: 183,
	label: '文莱'
},
{
	value: 184,
	label: '孟加拉国'
},
{
	value: 185,
	label: '南极洲'
},

{
	value: 186,
	label: '玻利维亚'
},
{
	value: 187,
	label: '土耳其'
},
{
	value: 188,
	label: '巴哈马'
},
{
	value: 189,
	label: '科摩罗'
},
{
	value: 190,
	label: '西撒哈拉'
},

{
	value: 191,
	label: '捷克共和国'
},
{
	value: 192,
	label: '乌克兰'
},
{
	value: 193,
	label: '爱沙尼亚'
},
{
	value: 194,
	label: '保加利亚'
},
{
	value: 195,
	label: '毛里塔尼亚'
},

{
	value: 196,
	label: '刚果民主共和国',
},
{
	value: 197,
	label: '列支敦士登'
},
{
	value: 198,
	label: '皮特凯恩'
},
{
	value: 199,
	label: '丹麦'
},
{
	value: 200,
	label: '马绍尔群岛'
},

{
	value: 201,
	label: '日本'
},
{
	value: 202,
	label: '奥地利'
},
{
	value: 203,
	label: '阿曼'
},
{
	value: 204,
	label: '蒙古国'
},
{
	value: 205,
	label: '塔吉克斯坦'
},

{
	value: 206,
	label: '瑞士'
},
{
	value: 207,
	label: '危地马拉'
},
{
	value: 208,
	label: '厄立特里亚'
},
{
	value: 209,
	label: '尼泊尔'
},
{
	value: 210,
	label: '马里'
},

{
	value: 211,
	label: '斯洛文尼亚'
},
{
	value: 212,
	label: '北马里亚纳群岛'
},
{
	value: 213,
	label: '（不适用）'
},
{
	value: 214,
	label: '阿鲁巴'
},
{
	value: 215,
	label: '刚果'
},

{
	value: 216,
	label: '卡塔尔'
},
{
	value: 217,
	label: '几内亚'
},
{
	value: 218,
	label: '美国'
},
{
	value: 219,
	label: '埃塞俄比亚'
},
{
	value: 220,
	label: '其他'
},

{
	value: 221,
	label: '圭亚那'
},
{
	value: 222,
	label: '德国'
},
{
	value: 223,
	label: '百慕大'
},
{
	value: 224,
	label: '特克斯和凯科斯群岛'
},
{
	value: 225,
	label: '澳大利亚'
},

{
	value: 226,
	label: '基里巴斯'
},
{
	value: 227,
	label: '波多黎各'
},
{
	value: 228,
	label: '巴基斯坦'
},
{
	value: 229,
	label: '毛里求斯'
},
{
	value: 230,
	label: '马拉维'
},

{
	value: 231,
	label: '土库曼斯坦'
},
{
	value: 232,
	label: '柬埔寨'
},
{
	value: 233,
	label: '智利'
},
{
	value: 234,
	label: '新喀里多尼亚'
},
{
	value: 235,
	label: '巴布亚新几内亚'
},

{
	value: 236,
	label: '布维岛'
},
{
	value: 237,
	label: '图瓦卢'
},
{
	value: 238,
	label: '库拉索'
},
{
	value: 239,
	label: '多米尼加共和国'
},
{
	value: 240,
	label: '越南'
},

{
	value: 241,
	label: '科科斯（基林）群岛'
},
{
	value: 242,
	label: '格林纳达'
},
{
	value: 243,
	label: '坦桑尼亚'
},
{
	value: 244,
	label: '阿根廷'
},
{
	value: 245,
	label: '中国澳门',
},

{
	value: 246,
	label: '贝宁'
},
{
	value: 247,
	label: '罗马尼亚'
},
{
	value: 248,
	label: '亚美尼亚'
},
{
	value: 249,
	label: '全球'
},
{
	value: 250,
	label: '中小企业国际财务报告准则'
},

{
	value: 251,
	label: '美国一般公认会计准则'
},
{
	value: 252,
	label: 'AICPA中小企业财务报告框​​架'
},
{
	value: 253,
	label: '南苏丹'
}
];

export const pagingSvgHoverText = {
	first: 'First Page',
	previous: 'Previous Page',
	next: 'Next Page',
	last: 'Last Page'
};

export const priorityTypesForDropdown = [{
	value: 1,
	label: '低',
	className: 'Low'
},
{
	value: 2,
	label: '中等',
	className: 'Medium'
},
{
	value: 3,
	label: '高',
	className: 'High'
},
{
	value: 4,
	label: '关键',
	className: 'Critical'
}
];

export const reviewNoteFilterTypes = [{
	value: 0,
	label: '全部'
},
{
	value: 1,
	label: '打开'
},
{
	value: 2,
	label: '已清除'
},
{
	value: 3,
	label: '已关闭'
}
];

export const reviewStatus = [{
	id: 1,
	name: '打开'
},
{
	id: 2,
	name: '已清除'
},
{
	id: 3,
	name: '已关闭'
}
];

export const reviewNoteOpenStatusOption = [{
	value: 2,
	label: '清除'
},
{
	value: 3,
	label: '关闭'
}
];

export const reviewNoteClearedStatusOption = [{
	value: 1,
	label: '已重新打开'
},
{
	value: 3,
	label: '关闭'
}
];

export const reviewNoteBulkClearedStatusOption = [{
	value: 1,
	label: '重现打开'
},
{
	value: 2,
	label: '清除'
},
{
	value: 3,
	label: '关闭'
}
];

export const reviewNoteClosedStatusOption = [{
	value: 1,
	label: '重新打开'
},
{
	value: 4,
	label: '删除'
}
];

export const taskTypeBadge = {
	1: 'OST',
	2: 'PST',
	3: 'WT',
	4: 'TOC',
	5: 'OSP',
	6: 'PSP',
	7: 'RT',
	8: 'GT',
	9: 'PIC',
	10: 'EQR',
	11: 'PIC/EQR',
	22: 'ACT',
	23: 'UDP'
};

export const riskTypes = [{
	id: 1,
	name: '特别风险',
	abbrev: 'SR',
	label: '重大',
	title: '特别风险'
},
{
	id: 2,
	name: '舞弊风险',
	abbrev: 'FR',
	label: '舞弊',
	title: '舞弊风险'
},
{
	id: 3,
	name: '重大错报风险',
	abbrev: 'R',
	label: '重大错报风险',
	title: '重大错报风险'
},
{
	id: 4,
	name: '非常低的风险估计',
	abbrev: 'VLRS',
	label: '非常低的风险估计',
	title: '非常低的风险估计'
},
{
	id: 5,
	name: '较低风险估计',
	abbrev: 'LRE',
	label: '较低风险估计',
	title: '较低风险估计'
},
{
	id: 6,
	name: '较高风险估计',
	abbrev: 'HRE',
	label: '较高风险估计',
	title: '较高风险估计'
},
{
	id: 7,
	name: '估计-未选择',
	abbrev: 'ENS',
	label: '估计-未选择',
	title: '估计-未选择'
}

];

export const relatedRisksDropdownRiskTypes = [{
	id: 1,
	name: '特别风险',
	abbrev: 'SR',
	label: '特别',
	title: '特别风险'
},
{
	id: 2,
	name: '舞弊风险',
	abbrev: 'FR',
	label: '舞弊',
	title: '舞弊风险'
},
{
	id: 3,
	name: '重大错报风险',
	abbrev: 'R',
	label: '重大错报风险',
	title: '重大错报风险'
}
];

export const estimateTypes = [{
	id: 4,
	name: '非常低风险估计（VLRE）',
	abbrev: 'VLRE',
	label: '非常低',
	title: '非常低风险估计'
},
{
	id: 5,
	name: '较低风险估计（LRE）',
	abbrev: 'LRE',
	label: '较低',
	title: '较低风险估计'
},
{
	id: 6,
	name: '较高风险估计（HRE）',
	abbrev: 'HRE',
	label: '较高',
	title: '较高风险估计'
},
{
	id: 7,
	name: '估计 - 未选择',
	abbrev: 'NA',
	label: '未选择',
	title: '估计 - 未选择'
}
];

export const statementTypes = [{
	id: 1,
	name: '利润表'
},
{
	id: 2,
	name: '资产负债表'
},
{
	id: 3,
	name: '二者'
}
];

export const RbacErrors = {
	106: '权限不足，无法编辑内容。请联系项目管理员以获得足够的权限。'
};

export const HelixProjectValidationErrors = {
	800: '你是否之前从未访问过EY Helix？前往',
	801: '你不是所关联项目的授权项目组成员。请与EY Helix项目管理员联系以获取访问权限。',
	901: '选择的EY Helix项目不再可用。点击EY Helix项目以关联新项目。',
	902: '选择的EY Helix项目已标记为待删除。点击EY Helix项目以关联新项目。',
	903: '选择的EY Helix项目已标记为待存储。点击EY Helix项目以关联新项目。',
	926: '选择的分析工具在EY Helix中不可用。刷新页面，然后重试。如果问题仍然存在，请联系帮助台。',
	927: '分析工具不可用于所关联项目。前往EY Helix完成数据和分析处理，然后继续。',
	928: 'EY Helix中分析工具无效或缺失。刷新页面，然后重试。如果问题仍然存在，请联系帮助台。',
	929: '关联的EY Helix项目发生错误。数据不可导入'
};

export const EngagementProfileRequirementErrors = {
	108: '项目配置文档不完整。'
};

export const IndependenceRequirementErrors = {
	103: '项目用户独立性合规性缺失。'
};

export const strategyTypes = [{
	id: 3,
	name: '范围内'
},
{
	id: 4,
	name: '范围外'
}
];

export const itAppTypes = [{
	value: 0,
	label: 'IT应用程序'
},
{
	value: 1,
	label: '服务机构'
}
];

export const confidentialityLevels = {
	[confidentialityTypes.DEFAULT]: '默认',
	[confidentialityTypes.LOW]: '低',
	[confidentialityTypes.MODERATE]: '中等',
	[confidentialityTypes.HIGH]: '高'

	// This has been disabled for release 2.5, uncomment if required
	// [confidentialityTypes.CONFIDENTIAL]: '机密'
};

export const formBodyOptionRiskTypes = [{
	id: 1,
	label: '特别风险'
},
{
	id: 2,
	label: '舞弊风险'
},
{
	id: 3,
	label: '重大错报风险'
}
];

export const formViewTypes = [{
	value: 0,
	label: '表格'
},
{
	value: 1,
	label: '变动'
},
{
	value: 2,
	label: '详情'
}
];

export const railFilterValidations = [{
	value: 0,
	label: '全部'
},
{
	value: 1,
	label: '不完整的回复'
},
{
	value: 2,
	label: '未解决批注'
}
];

export const aresRiskTypes = [{
	id: 1,
	name: '特别风险'
},
{
	id: 2,
	name: '舞弊风险'
},
{
	id: 3,
	name: '重大错报风险'
},
{
	id: 4,
	name: '非常低风险估计'
},
{
	id: 5,
	name: '较低风险估计'
},
{
	id: 6,
	name: '较高风险估计'
},
{
	id: 7,
	name: '估计——未选择'
}
];

export const materialityTypes = [{
	value: 1,
	label: '税前收入'
},
{
	value: 2,
	label: 'EBIT（息税前收益）'
},
{
	value: 3,
	label: 'EBITDA（息税折旧摊销前收益）'
},
{
	value: 4,
	label: '毛利'
},
{
	value: 5,
	label: '收入'
},
{
	value: 6,
	label: '运营费用'
},
{
	value: 7,
	label: '权益'
},
{
	value: 8,
	label: '资产'
},
{
	value: 9,
	label: '基于活动的衡量标准（其他）'
},
{
	value: 10,
	label: '税前损失'
},
{
	value: 11,
	label: '基于资本的衡量标准（其他）'
},
{
	value: 12,
	label: '基于收益的衡量标准（其他）'
}
];

export const helixCurrencyType = {
	[currencyType.Functional]: '功能',
	[currencyType.Reporting]: '报告'
};

export const controlRiskType = [{
	id: 1,
	name: '依赖'
},
{
	id: 2,
	name: '不依赖'
},
{
	id: 3,
	name: '测试管理层程序'
}
];

export const inherentRiskType = [{
	id: 1,
	name: '较高'
},
{
	id: 2,
	name: '较低'
},
{
	id: 3,
	name: '非常低'
}
];

export const AlraInherentRiskType = [{
	id: 3,
	name: '不相关'
},
{
	id: 2,
	name: '较低'
},
{
	id: 1,
	name: '较高'
}
];

export const scotInherentRiskType = [{
	id: 1,
	name: '较高'
},
{
	id: 2,
	name: '较低'
},
{
	id: 3,
	name: '非常规重大交易类别'
}
];

export const CRAStrings = {
	Minimal: '最低',
	Low: '低',
	'Low +SC': "低+SC",
	Moderate: '中',
	High: '高',
	'High +SC': "高+SC"
};

export const priorityType = [{
	id: 1,
	name: '低',
	className: 'Low',
	label: 'L'
},
{
	id: 2,
	name: '中等',
	className: 'Medium',
	label: 'M'
},
{
	id: 3,
	name: '高',
	className: 'High',
	label: 'H'
},
{
	id: 4,
	name: '关键',
	className: 'Critical',
	label: 'C'
}
];

export const kendoLabels = {
	addComment: '添加批注',
	addColumnBefore: '左侧插入列',
	addColumnAfter: '右侧插入列',
	addInlineComment: '新增行内批注',
	addRowAbove: ' 上方插入列',
	addRowBelow: ' 下方插入列',
	alignLeft: ' 左对齐',
	alignRight: ' 右对齐',
	alignCenter: ' 居中',
	alignFull: ' 两端对齐',
	backgroundColor: '背景颜色',
	bulletList: '插入无序列表',
	bold: ' 加粗',
	backColor: ' 高亮',
	createLink: '插入超链接',
	createTable: ' 创建表格',
	cleanFormatting: ' 清除格式',
	deleteRow: ' 删除整行',
	deleteColumn: ' 删除整列',
	deleteTable: '删除表格',
	fontSizeInherit: ' 字体大小',
	foreColor: '字体颜色',
	format: '格式',
	fontSize: ' 字体大小',
	hyperlink: '插入链接',
	italic: ' 斜体',
	indent: ' 缩进',
	insertTableHint: '创建一个{0}x{1}表格',
	huge: '巨大',
	'hyperlink-dialog-content-address': "网络地址",
	'hyperlink-dialog-title': "插入超链接",
	'hyperlink-dialog-content-title': "标题",
	'hyperlink-dialog-content-newwindow': "在新窗口中打开链接",
	'hyperlink-dialog-cancel': "取消",
	'hyperlink-dialog-insert': "插入",
	large: '大',
	noDataPlaceholder: '输入文本',
	normal: '正常',
	orderedList: '插入有序列表',
	outdent: ' 减少缩进',
	paragraphSize: '段落大小',
	print: '打印',
	pdf: '导出至PDF',
	redo: '恢复',
	removeFormatting: '删除格式',
	strikethrough: ' 删除线',
	small: '小',
	subscript: ' 下标',
	superscript: ' 上标',
	underline: ' 下划线',
	undo: '撤销',
	unlink: '取消关联'
};

export const kendoFormatOptions = [{
	text: '段落',
	value: 'p'
},
{
	text: '标题1',
	value: 'h1'
},
{
	text: '标题2',
	value: 'h2'
},
{
	text: '标题3',
	value: 'h3'
},
{
	text: '标题4',
	value: 'h4'
},
{
	text: '标题5',
	value: 'h5'
},
{
	text: '标题6',
	value: 'h6'
}
];

export const kendoFontSize = [{
	text: '8',
	value: '8px'
},
{
	text: '9',
	value: '9px'
},
{
	text: '10',
	value: '10px'
},
{
	text: '11',
	value: '11px'
},
{
	text: '12',
	value: '12px'
},
{
	text: '14',
	value: '14px'
},
{
	text: '16',
	value: '16px'
},
{
	text: '18',
	value: '18px'
},
{
	text: '20',
	value: '20px'
},
{
	text: '22',
	value: '22px'
},
{
	text: '24',
	value: '24px'
},
{
	text: '26',
	value: '26px'
},
{
	text: '28',
	value: '28px'
},
{
	text: '36',
	value: '36px'
},
{
	text: '48',
	value: '48px'
},
{
	text: '72',
	value: '72px'
}
];

export const ItFlowValidationLabels = {
	ITAppWithoutAtLeastOneRelatedITProcess: '非相关IT应用程序',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: '非相关ITGC',
	ITSPHasNorelatedITRisk: '非相关ITSP',
	ITProcessHasNoRelatedITApplication: '非相关IT流程',
	ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication: '缺少IT风险',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: '缺少ITGC或标记不存在ITGC',
	ITDMorITACWithNoRelatedITApplication: '应用程序/没有IT应用程序的ITDM控制',
	ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed: '待删除ITSP',
	ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed: '待删除ITGC',
	ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed: '待删除IT风险',
	ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed: '带有无效测试策略的ITGC',
	ITGCWithoutASelectedDesignEffectiveness: '缺少ITGC设计评估',
	SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero: '非相关重大交易类别',
	AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed: '对测试控制的应对不一致',
	SCOTWithHasNoITApplicationHasITDMOrAppControls: '缺少重大交易类别中的IT应用程序'
};

export const ISA315ITFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: ItFlowValidationLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: ItFlowValidationLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: ItFlowValidationLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: ItFlowValidationLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: ItFlowValidationLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed,
	label: ItFlowValidationLabels.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: ItFlowValidationLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
}
];

/* Notes modal labels */

export const reviewNoteModalLabels = {
	/*Review Notes*/
	engagement: '项目',
	emptyReplyErrorMsg: '添加文本以继续',
	lengthReplyErrorMsg: '回复必须少于4000个字符。',
	documentLabel: '文档',
	task: '任务',
	allEngagementFilterLabel: '所有其他项目',
	otherEngagementComments: '其他项目注释',
	notesModalInstructionalText: '查看和回复以下所选{0}的注释。',
	commentThread: '注释线',
	singleNoteInstructionalText: '查看和回复以下所选注释。',
	emptyNoteDetailsMessage: '选择注释并查看详情。点击control键或shift键批量选择多个复核注释。如果您要处理单个注释，请从列表中选择该注释。',
	documentReviewNotesLabel: '文档注释',
	addNewReviewNoteButtonText: '添加注释',
	noNotesAssociatedWithDocumentLabel: '使用以下输入信息添加注释。将注释分配给一位用户，并说明优先等级和到期日。',
	noNotesFound: '未发现注释',
	noNotesAssociatedWithTaskLabel: '没有与该任务相关联的{0}注释',
	allNotesLabel: '全部注释',
	charactersLabel: '字符',
	myNotesLabel: '我的注释',
	showClearedLabel: '显示已清空批注',
	showClosedLabel: '显示已关闭批注',
	assignedToLabel: '分配至',

	ofLabel: '的',
	enterNoteText: '输入注释',
	addNewNoteModalClose: '关闭',
	addNewNoteModalTitleLabel: '添加新注释',
	editNoteModalTitleLabel: '编辑注释',
	deleteIconHoverText: '删除',
	deleteIconModalAcceptText: '删除',
	deleteIconModalConfirmMessage: '是否确定删除对此注释的回复?',
	deleteIconModalConfirmMessageParent: '是否确定删除已选定注释?',
	deleteIconModalTitleLabel: '删除注释',
	deleteReplyIconModalTitle: '删除回复',
	emptyRepliesMessage: '尚未有回复',
	replyInputPlaceholder: '回复此注释',
	replyInputPlaceholderEdit: '使用注释或语音注释编辑回复',
	noteInputPlaceholderEdit: '使用注释或语音注释进行编辑',
	replyText: '回复文本',
	editReplyModelTitle: '编辑回复',
	editReplyPlaceholder: '输入回复',
	noteDueDateLabel: '到期',

	priorityLabel: '优先批注',
	dueDateLabel: '到期日',
	dueLabel: '到期',
	status: '状态',
	noteModifiedDateLabel: '已修改',
	cancelLabel: '取消',
	saveLabel: '保存',
	clearedBy: '清除人',
	closedBy: '关闭人',
	reopenedBy: '重新打开人',
	reply: '回复',
	editIconHoverTextLabel: '编辑',
	required: '必填',
	closeTitle: '关闭',
	otherEngagementNotes: '其他项目注释',
	closeLabel: '关闭',
	showMore: '显示更多',
	showLess: '显示更少',
	showMoreEllipsis: '显示更多...',
	showLessEllipsis: '显示更少...',
	noResultFound: '未找到结果',
	engagementNameLabel: '项目名称： ',
	taskReviewNotesLabel: '任务注释',
	fromUserLabel: '从',
	toUserLabel: '至',
	view: '查看',
	dueDateRequiredTextError: '截止日期为必填'
};

export const notesFilterLabels = [{
	id: notesFilter.allNotes,
	label: '所有注释',
	value: notesFilter.allNotes
},
{
	id: notesFilter.myNotes,
	label: '我的注释',
	value: notesFilter.myNotes
},
{
	id: notesFilter.authoredByMeNotes,
	label: '分配给我',
	value: notesFilter.authoredByMeNotes
}
];

export const reviewerAssignments = {
	taskLayoutHeaderAssignments: '工作分配',
	manageAssigmentsStep2: '编辑任务分配',
	editAssignment: '编辑工作分配',
	deleteAssignment: '删除工作分配',
	manageAssigmentsStep3: '完成工作分配',
	taskAssigmentStatusHeader: '工作分配状态',
	taskAssignmentName: '工作分配',
	dueDateAssigment: '到期',
	editDueDate: '可选：编辑到期前天数',
	teamMemberAssigmentLabel: '项目组成员',
	currentAssigmentLabel: '当期',
	handOffToAssigmentLabel: '已移交给： ',
	priorToEndDateLabel: '到期日前',
	noTimePhaseAssigmentLabel: '未分配时间阶段',
	closedByAssigmentLabel: '关闭人',
	onAssingmentLabel: '在',
	preparerAssigmentOpenTitleTip: '移交此任务以关闭您的任务分配',
	reviewerAssigmentOpenTitleTip: '将任务分配标记为已关闭',
	reviewerAssigmentClosedTitleTip: '将任务分配标记为未完成',
	AssigmentLabel: '移交此任务以关闭您的任务分配',
	timePhaseName: '时间阶段： ',
	timePhaseEndDate: '到期日： ',
	AssignmentType: [{
		id: 1,
		displayName: '编制人'
	},
	{
		id: 2,
		displayName: '详细复核人'
	},
	{
		id: 3,
		displayName: '一般复核人'
	},
	{
		id: 4,
		displayName: '合伙人'
	},
	{
		id: 5,
		displayName: 'EQR'
	},
	{
		id: 6,
		displayName: '其他'
	}
	],
	AssignmentStatus: [{
		id: 1,
		displayName: '未开始'
	},
	{
		id: 2,
		displayName: '进行中'
	},
	{
		id: 3,
		displayName: '已关闭'
	},
	{
		id: 4,
		displayName: '未分配'
	}
	],
	assignmentTableColumnHeader: '工作分配',
	teamMemberTableColumnHeader: '项目组成员',
	dueDaysTableColumnHeader: '到期',
	daysPriorToEndDate: '结束日期前天数',
	handoffButton: '移交'
};
/* Notes modal labels */

export const handOffModal = {
	title: ' 移交',
	description: '将此任务移交给下一位小组成员。如想签署证据文件，请选择以下选项。',
	dropdownLabel: ' 移交给',
	closeTitle: ' 取消',
	confirmButton: ' 移交',
	evidence: ' 证据',
	evidenceSignOffTitle: ' 全部签名为： ',
	existingSignOffs: ' 当前签字',
	noDocumentsAvailable: ' 无可用文档'
};

// manage scot modal labels
export const manageSCOTModal = {
	title: '管理重大交易类别',
	description: '您可以创建、编辑和删除已有重大交易类别。变动在保存后即生效。',
	addSCOTLink: '添加重大交易类别'
};

export const deleteSCOTModal = {
	title: '删除重大交易类别',
	description: '将删除以下重大交易类别。此操作无法撤销。'
};

export const manageITAppModalLabels = {
	title: '管理IT应用程序',
	description: '创建新的IT应用程序或编辑和删除下面的现有IT应用程序。',
	inputNameTitle: 'IT应用程序名称',
	deleteConfirmMessage: '是否确认删除IT应用程序<b>{0}</b>？此操作无法撤销。',
	addSuccessMessage: "已成功创建IT应用程序'{0}'",
	editSuccessMessage: "已成功保存对IT应用程序'{0}' 的编辑",
	deleteSuccessMessage: "'{0}' 已成功删除"
};

export const manageSOModalLabels = {
	title: '管理服务机构',
	description: '创建新的服务机构或编辑和删除下面的现有服务机构。',
	inputNameTitle: '服务机构名称',
	deleteConfirmMessage: '是否确认删除服务机构<b>{0}</b>？此操作无法撤销。',
	addSuccessMessage: "已成功创建服务机构'{0}'",
	editSuccessMessage: "已成功保存对服务机构'{0}' 的编辑",
	deleteSuccessMessage: "'{0}'已成功删除",
    addServiceOrganization: '添加服务机构',
	editServiceOrganization: '编辑服务机构',
	deleteServiceOrganization: '删除服务机构'
};

export const customNameModal = {
	title: '记账分录来源名称后缀',
	closeTitle: '取消',
	save: '保存',
	suffixRequired: '后缀为必填项！',
	suffix: '后缀',
	addSuffix: '添加后缀',
	editSuffix: '编辑后缀'
};

export const GuidedWorkFlowLabels = {
	RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM: '不相关事件和情况/错报风险',
	RisksUnrelatedToAnAssertionForGuidedWorkflow: '未关联风险',
	IncompleteMeasurementBasisForecastAmount: '基础不完整',
	IncompleteMeasurementBasisForecastAmountRationale: '基础依据为必填',
	IncompleteMeasurementBasisAdjustedAmount: '调整金额不完整',
	IncompletePlanningMateriality: 'PM不完整',
	PlanningMaterialityGreaterThanMaximumAmount: 'PM过大',
	IncompletePlanningMaterialityRationale: 'PM依据为必填',
	IncompleteTolerableError: 'TE不完整',
	TENotWithinRangeOfAllowedValues: 'TE百分比无效',
	IncompleteTolerableErrorRationale: 'TE依据为必填',
	IncompleteSAD: 'SAD不完整',
	SADGreaterThanMaximum: 'SAD过大',
	IncompleteSADRationale: 'SAD依据为必填',
	IncompletePACESelection: 'PACE不完整',
	AccountWithoutIndividualRiskAssessmentForm: '账户{0}缺失文档',
	EstimateWithoutIndividualEstimateForm: '估计{0}缺失文档',
	AccountWithoutIndividualAnalyticForm: '账户{0}缺失文档',
	MultiEntityWithoutIndividualProfileForm: '无多实体单项配置文档的实体',
	AccountAccountTypeIDDoesNotMatchAction: '账户指定选择不一致',
	AccountHasEstimateDoesNotMatchAction: '选择的账户估计不一致',
	AccountFormOptionHasRelatedRisksNotAssociatedToAccount: '没有关联账户的风险',
	AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk: '无风险的较高固有风险认定',
	AccountMissingSubstantiveProcedure: '无实质性程序的账户/实体',
	MultiEntityNotRelatedToALLPSTACTForRelatedAccount: '账户/实体需要更新内容',
	ComponentWithoutGroupInvolvementForm: '无集团参与表的组成部分（不包括仅供参考的组成部分）',
	ComponentWithoutRelatedGroupAssessmentInstruction: '无集团风险评估指引的组成部分',
	IncompleteAssertionRiskLevel: '认定风险水平不完整',
	EstimateAccountWithoutEsimatePSPIndex: '无估计主要实质性程序索引的估计账户',
	AccountExecutedWithoutRelatedComponent: '集团——无相关全面范围或特定范围组成部分的账户（在其他项目中执行）',
	MultiEntityAccountWithoutRelatedToAnyMultiEntity: '无相关实体的账户',
	ChangeNotSubmittedMultiEntityFullProfile: '未提交变更',
	ChangeNotSubmittedMultiEntityIndividualDocument: '未提交变更',
	AccountTypeWithMissingInformation: '账户缺失信息',
	DocumentUploadMissingRequiredPICEQRSignOffs: '缺少签字的证据',
	DocumentUploadMissingRequiredPICEQRSignOffRequirements: '缺少签字要求的证据',
	DocumentUploadMissingPreparerOrReviewerSignOffs: '文档上传——缺少编制人或复核人签字',
	ITAppWithoutAtLeastOneRelatedITProcess: '未关联IT应用程序',
	ITProcessHasNoRelatedITApplication: '未关联IT流程',
	ITGCWithoutASelectedDesignEffectiveness: '缺少IT一般控制设计评估',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: '未关联IT一般控制',
	ITSPHasNorelatedITRisk: '未关联IT实质性程序',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: '缺少IT一般控制或标记不存在',
	EstimateWithoutAccountRelated: '无关联账户的估计',
	EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount: '较高/较低风险估计与非估计账户相关',
	RiskEstimateRiskTypeIDDoesNotMatchAction: '回答的估计类别与“编辑估计”中的指定不一致',
	LowerorHigherRiskEstimateWithoutEstimateSCOT: '没有有效重大交易类别的估计',
	EstimateWithoutIndividualEstimateDocument: '估计缺少个别文档',
	EstimateAccountWithoutHigherOrLowerRiskEstimate: '无有效估计的账户',
	EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary: '估计账户没有较高或较低风险估计',
	EstimateScotWithoutHigherOrLowerRiskEstimate: '估计重大交易类别没有较高或较低风险估计',
	HigherRiskEstimateWithoutRisk: '无有效相关风险的较高风险估计',
	PICEQRSignOffRequirements: 'PIC or EQR Signoff requirement does not match response',
	AdjustmentsWithoutAnyEvidence: '无证据的调整',
	AdjustmentsThatDoNotNet: '非净额调整',
	DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration: '文档已签出或正在签出以进行协作',
	NonEngagementWideTasksMissingEvidence: '缺乏证据的非项目任务',
	EstimatesMustBeMarkedHigherRisk: '与非较高风险的估计相关的特别/舞弊风险',
	SCOTEstimateNoRelatedWalkthroughForm: '无穿行测试的重大交易类别/估计',
	SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2: '无关联账户的重大交易类别',
	AccountSignificantDisclosureWithNoRelatedSCOTV2: 'Account - Significant Account / Significant Disclosure that is not a CT only Account with no related SCOT or an Estimate when it is an Estimate Account',
	ITApplicationWithoutITAppRiskAssessmentIndividualDocument: 'Technology risk assessment missing document',
	ITApplicationWithoutITAppPlanningIndividualDocument: 'Technology missing document',
	FormContentWithoutHeader: 'Form Content without Header',
	RisksWithoutAnyRelatedAssertions: 'There are risks that have not been related to at least one assertion',
	AssertionsWithIncompleteCRA: 'There are assertions missing an inherent and/or control risk assessment',
	LimitedRiskOrInsignificantAccountMissingRationale: 'All limited risk and insignificant accounts shall have rationale provided',
	ITProcessWithoutWalkthroughDocument: 'ITProcess without IT process - Walkthrough - Individual',
	ITProcessIsUncategorized: 'IT Process - ITProcessTypeID is Uncategorized',
	ITProcessWithNoRelatedITApplication: 'ITProcess - ITProcess with no related IT Application'
};

export const GuidedWorkFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.RiskEstimateRiskTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.RiskEstimateRiskTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.FormContentWithoutHeader,
	label: GuidedWorkFlowLabels.FormContentWithoutHeader
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary
},
{
	validationId: validationTypes.EstimateScotWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateScotWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.HigherRiskEstimateWithoutRisk,
	label: GuidedWorkFlowLabels.HigherRiskEstimateWithoutRisk
},
{
	validationId: validationTypes.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM,
	label: GuidedWorkFlowLabels.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.RisksUnrelatedToAnAssertionForGuidedWorkflow,
	label: GuidedWorkFlowLabels.RisksUnrelatedToAnAssertionForGuidedWorkflow
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmount
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmountRationale,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmountRationale
},
{
	validationId: validationTypes.IncompleteMeasurementBasisAdjustedAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisAdjustedAmount
},
{
	validationId: validationTypes.IncompletePlanningMateriality,
	label: GuidedWorkFlowLabels.IncompletePlanningMateriality
},
{
	validationId: validationTypes.PlanningMaterialityGreaterThanMaximumAmount,
	label: GuidedWorkFlowLabels.PlanningMaterialityGreaterThanMaximumAmount
},
{
	validationId: validationTypes.IncompletePlanningMaterialityRationale,
	label: GuidedWorkFlowLabels.IncompletePlanningMaterialityRationale
},
{
	validationId: validationTypes.IncompleteTolerableError,
	label: GuidedWorkFlowLabels.IncompleteTolerableError
},
{
	validationId: validationTypes.TENotWithinRangeOfAllowedValues,
	label: GuidedWorkFlowLabels.TENotWithinRangeOfAllowedValues
},
{
	validationId: validationTypes.IncompleteTolerableErrorRationale,
	label: GuidedWorkFlowLabels.IncompleteTolerableErrorRationale
},
{
	validationId: validationTypes.IncompleteSAD,
	label: GuidedWorkFlowLabels.IncompleteSAD
},
{
	validationId: validationTypes.SADGreaterThanMaximum,
	label: GuidedWorkFlowLabels.SADGreaterThanMaximum
},
{
	validationId: validationTypes.IncompleteSADRationale,
	label: GuidedWorkFlowLabels.IncompleteSADRationale
},
{
	validationId: validationTypes.IncompletePACESelection,
	label: GuidedWorkFlowLabels.IncompletePACESelection
},
{
	validationId: validationTypes.AccountWithoutIndividualRiskAssessmentForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualRiskAssessmentForm
},
{
	validationId: validationTypes.EstimateWithoutIndividualEstimateForm,
	label: GuidedWorkFlowLabels.EstimateWithoutIndividualEstimateForm
},
{
	validationId: validationTypes.AccountWithoutIndividualAnalyticForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualAnalyticForm
},
{
	validationId: validationTypes.MultiEntityWithoutIndividualProfileForm,
	label: GuidedWorkFlowLabels.MultiEntityWithoutIndividualProfileForm
},
{
	validationId: validationTypes.AccountAccountTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountAccountTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.AccountHasEstimateDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountHasEstimateDoesNotMatchAction
},
{
	validationId: validationTypes.AccountFormOptionHasRelatedRisksNotAssociatedToAccount,
	label: GuidedWorkFlowLabels.AccountFormOptionHasRelatedRisksNotAssociatedToAccount
},
{
	validationId: validationTypes.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk,
	label: GuidedWorkFlowLabels.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk
},
{
	validationId: validationTypes.MultiEntityNotRelatedToALLPSTACTForRelatedAccount,
	label: GuidedWorkFlowLabels.MultiEntityNotRelatedToALLPSTACTForRelatedAccount
},
{
	validationId: validationTypes.AccountMissingSubstantiveProcedure,
	label: GuidedWorkFlowLabels.AccountMissingSubstantiveProcedure
},
{
	validationId: validationTypes.ComponentWithoutGroupInvolvementForm,
	label: GuidedWorkFlowLabels.ComponentWithoutGroupInvolvementForm
},
{
	validationId: validationTypes.ComponentWithoutRelatedGroupAssessmentInstruction,
	label: GuidedWorkFlowLabels.ComponentWithoutRelatedGroupAssessmentInstruction
},
{
	validationId: validationTypes.EstimateAccountWithoutEstimatePSPIndex,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutEsimatePSPIndex
},
{
	validationId: validationTypes.AssertionInherentRiskWithoutRelatedHigherRisk,
	label: GuidedWorkFlowLabels.IncompleteAssertionRiskLevel
},
{
	validationId: validationTypes.AccountGroupWithoutAComponent,
	label: GuidedWorkFlowLabels.AccountExecutedWithoutRelatedComponent
},
{
	validationId: validationTypes.MultiEntityAccountWithoutRelatedToAnyMultiEntity,
	label: GuidedWorkFlowLabels.MultiEntityAccountWithoutRelatedToAnyMultiEntity
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityFullProfile,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityFullProfile
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityIndividualDocument,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityIndividualDocument
},
{
	validationId: validationTypes.AccountWithMissingValues,
	label: GuidedWorkFlowLabels.AccountTypeWithMissingInformation
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffs
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffRequirements
},
{
	validationId: validationTypes.DocumentUploadMissingPreparerOrReviewerSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingPreparerOrReviewerSignOffs
},
{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: GuidedWorkFlowLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: GuidedWorkFlowLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: GuidedWorkFlowLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: GuidedWorkFlowLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: GuidedWorkFlowLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
},
{
	validationId: validationTypes.EstimateWithoutAccountRelated,
	label: GuidedWorkFlowLabels.EstimateWithoutAccountRelated
},
{
	validationId: validationTypes.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount,
	label: GuidedWorkFlowLabels.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount
},
{
	validationId: validationTypes.LowerorHigherRiskEstimateWithoutEstimateSCOT,
	label: GuidedWorkFlowLabels.LowerorHigherRiskEstimateWithoutEstimateSCOT
},
{
	validationId: validationTypes.PICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.PICEQRSignOffRequirements
},
{
	validationId: validationTypes.EstimatesMustBeMarkedHigherRisk,
	label: GuidedWorkFlowLabels.EstimatesMustBeMarkedHigherRisk
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.NonEngagementWideTasksMissingEvidence,
	label: GuidedWorkFlowLabels.NonEngagementWideTasksMissingEvidence
},
{
	validationId: validationTypes.AdjustmentsWithoutAnyEvidence,
	label: GuidedWorkFlowLabels.AdjustmentsWithoutAnyEvidence
},
{
	validationId: validationTypes.AdjustmentsThatDoNotNet,
	label: GuidedWorkFlowLabels.AdjustmentsThatDoNotNet
},
{
	validationId: validationTypes.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration,
	label: GuidedWorkFlowLabels.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration
},
{
	validationId: validationTypes.ITApplicationWithoutITAppRiskAssessmentIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppRiskAssessmentIndividualDocument
},
{
	validationId: validationTypes.SCOTEstimateNoRelatedWalkthroughForm,
	label: GuidedWorkFlowLabels.SCOTEstimateNoRelatedWalkthroughForm
},
{
	validationId: validationTypes.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2,
	label: GuidedWorkFlowLabels.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2
},
{
	validationId: validationTypes.AccountSignificantDisclosureWithNoRelatedSCOTV2,
	label: GuidedWorkFlowLabels.AccountSignificantDisclosureWithNoRelatedSCOTV2
},
{
	validationId: validationTypes.ITApplicationWithoutITAppPlanningIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppPlanningIndividualDocument
},
{
	validationId: validationTypes.RisksWithoutAnyRelatedAssertions,
	label: GuidedWorkFlowLabels.RisksWithoutAnyRelatedAssertions
},
{
	validationId: validationTypes.AssertionsWithIncompleteCRA,
	label: GuidedWorkFlowLabels.AssertionsWithIncompleteCRA
},
{
	validationId: validationTypes.LimitedRiskOrInsignificantAccountMissingRationale,
	label: GuidedWorkFlowLabels.LimitedRiskOrInsignificantAccountMissingRationale
},
{
	validationId: validationTypes.ITProcessWithoutWalkthroughDocument,
	label: GuidedWorkFlowLabels.ITProcessWithoutWalkthroughDocument
},
{
	validationId: validationTypes.ITProcessIsUncategorized,
	label: GuidedWorkFlowLabels.ITProcessIsUncategorized
},
{
	validationId: validationTypes.ITProcessWithNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessWithNoRelatedITApplication
}

];

// Label overrides (redefine here labels / objects that apply for a different part of the application)
export const resourceOverrides = {
	['Ares']: {
		labels: {
			notAROMM: '非重大错报风险',
			fraudRisk: '舞弊风险',
			significantRisk: '特别风险',
			identifiedRiskFactors: '已识别的事件/情况、重大错报风险、特别风险和舞弊风险',
			countUnassociatedRisk: '事件/情况不相关/未标记为“非重大错报风险”。'
		},
		riskTypes: [{
			id: 1,
			name: '特别风险',
			abbrev: 'S',
			label: '特别',
			title: '特别风险'
		},
		{
			id: 2,
			name: '舞弊风险',
			abbrev: 'F',
			label: '舞弊',
			title: '舞弊风险'
		},
		{
			id: 3,
			name: '重大错报风险',
			abbrev: 'R',
			label: '重大错报风险',
			title: '重大错报风险'
		}
		]
	}
};

export const jeSourceTypes = [{
	value: 1,
	label: '系统生成的'
},
{
	value: 2,
	label: '手工的'
},
{
	value: 3,
	label: '两者都是'
}
];

export const hasJournalEntriesOption = [{
	value: 1,
	label: '是'
},
{
	value: 2,
	label: '否'
}
];

export const filterReviewNoteStatus = [{
	value: 1,
	label: '打开'
},
{
	value: 2,
	label: '已清除'
},
{
	value: 3,
	label: '已关闭'
}
];

export const EntitiesLabels = {
	close: '关闭',
	cancel: '取消',
	repNoRecordMessage: '未找到结果',
	edit: '编辑',
	delete: '删除',
	actions: '操作',
	show: '显示',
	first: '首页',
	last: '尾页',
	prev: '上一页',
	next: '下一页',
	search: '搜索',
	primary: 'Primary',
	knowledgeRiskLabel: 'Risks from knowledge cannot be edited or deleted',


	[Entity.Account]: {
		manageEntity: '管理账户和披露',
		searchEntity: '搜索账户',
		createEntity: '新账户',
		entityName: '账户',
		entityNameCaps: '账户',
		entityNamePlural: '账户',
		placeholderText: '新建账户和披露，或编辑和删除下方现有账户和披露。',
		deleteConfirmLabel: '是否确认删除此账户？所有现有关联都将取消。此操作无法撤销。'
	},
	[Entity.Estimate]: {
		manageEntity: '管理估计',
		searchEntity: '搜索估计',
		createEntity: '新估计',
		entityName: '估计',
		entityNameCaps: '估计',
		entityNamePlural: '估计',
		placeholderText: '创建新估计或编辑并删除以下已有估计。',
		deleteConfirmLabel: '是否确认删除此估计？所有现有关联都将取消。此操作无法撤销。'
	},
	[Entity.Risk]: {
		manageEntity: '管理风险',
		searchEntity: '查找风险',
		createEntity: '新风险',
		entityName: '风险',
		entityNameCaps: '风险',
		entityNamePlural: '风险',
		placeholderText: '创建新风险或编辑并删除以下已有风险。',
		deleteConfirmLabel: '是否确实要删除此风险？所有现有关联都将取消。此操作无法撤销。'
	},
	[Entity.STEntity]: {
		manageEntity: '管理实体',
		searchEntity: '搜索实体',
		createEntity: '新实体',
		entityName: '实体',
		entityNameCaps: '实体',
		entityNamePlural: '实体',
		placeholderText: '创建新实体或编辑并删除以下已有实体。',
		deleteEntity: '删除实体',
		deleteConfirmLabel: '是否确认删除此实体？所有现有关联都将取消。此操作无法撤销。'
	},
	[Entity.Control]: {
		manageEntity: '管理控制',
		searchEntity: '搜索控制',
		createEntity: '新控制',
		entityName: '控制',
		entityNameCaps: '控制',
		entityNamePlural: '控制',
		placeholderText: '创建新控制或编辑和删除下面的现有控制。',
		deleteEntity: '删除控制',
		deleteConfirmLabel: '是否确认删除此控制？所有现有关联都将取消。此操作无法撤销。'
	},
	[Entity.ITProcess]: {
		manageEntity: '管理IT流程',
		searchEntity: '搜索IT流程',
		createEntity: '新建IT流程',
		entityName: 'IT流程',
		entityNameCaps: 'IT流程',
		entityNamePlural: 'IT流程',
		placeholderText: "创建新的IT流程或编辑和删除下面的现有IT流程。",
		deleteEntity: '删除IT流程',
		deleteConfirmLabel: '是否确认删除此IT流程？所有现有关联都将取消。此操作无法撤销。'
	},
	[Entity.ITRisk]: {
		manageEntity: '管理技术风险',
		searchEntity: '搜尋技术风险',
		createEntity: '新技术风险',
		entityName: '技术风险',
		entityNameCaps: '技术风险',
		entityNamePlural: '技术风险',
		placeholderText: '创建新技术风险或编辑并删除以下已有风险。',
		deleteEntity: '删除技术风险',
		deleteConfirmLabel: '是否确定删除此风险？所有现有关系将被一同删除。此操作无法撤销。'
	},
	[Entity.ITControl]: {
		ITGC: {
			manageEntity: '管理IT一般控制',
			searchEntity: '搜索IT一般控制',
			createEntity: '新建IT一般控制',
			editEntity: '编辑IT一般控制',
			viewEntity: '查看IT一般控制',
			entityName: 'IT一般控制',
			entityNameCaps: 'IT一般控制',
			entityNamePlural: 'IT一般控制',
			placeholderText: '新建IT一般控制或编辑并删除以下现有IT一般控制。',
			deleteEntity: '删除IT一般控制 ',
			close: '关闭',
			cancel: '取消',
			processIdRequired: 'IT流程为必填项',
			save: '保存',
			confirm: '确认',
			iTProcesslabel: 'IT流程',
			saveAndCloseLabel: '保存并关闭',
			saveAndCreateLabel: '保存并新建',
			deleteConfirmLabel: '是否确认删除此IT一般控制？所有现有关联都将取消。此操作无法撤销。',
			operationEffectiveness: '运行有效性',
			itDesignEffectivenessHeader: '设计有效性',
			itTestingColumnHeader: '测试',
			testingTitle: '测试',
			frequency: '频率',
			controlOpertaingEffectiveness: '运行有效性',
			designEffectiveness: '设计有效性',
			frequencyITGC: '选择频率',
			nameITGC: 'IT一般控制名称（必填）',
			itspNameRequired: 'IT实质性程序名称（必填）',
			noResultsFound: '未找到结果',
			selectITRisk: '选择技术风险（必选）',
			itRiskRequired: '技术风险（必填）',
			itRiskName: '技术风险',
			inputInvaildCharacters: '输入不能包含以下字符串：*/:<>\\?|"',
			itControlNameRequired: 'IT一般控制名称为必填项',
			itgcTaskDescription: '执行我们设计的IT一般控制测试，以获得充分、适当的审计证据，证明IT一般控制在整个依赖期内的运行有效性。',
			selectITProcess: '选择IT流程（必填）',
			itProcessRequired: 'IT流程（必填）',
			riskNameRequired: '技术风险为必填',
			addModalDescription: '描述IT一般控制内容。',
			editModalDescription: '编辑IT一般控制及其相关属性。',
			controlDesignEffectiveness: {
				[0]: {
					description: '未选择'
				},
				[1]: {
					description: '有效'
				},
				[2]: {
					description: '无效'
				}
			},
			controlTesting: {
				[0]: {
					description: '未选择'
				},
				[1]: {
					description: '是'
				},
				[2]: {
					description: '否'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: '未选择'
				},
				[1]: {
					description: '有效'
				},
				[2]: {
					description: '无效'
				}
			}
		},
		ITSP: {
			manageEntity: '管理IT实质性程序',
			searchEntity: '搜索IT实质性程序',
			createEntity: '新建IT实质性程序',
			editEntity: '编辑IT实质性程序',
			viewEntity: '查看IT实质性程序',
			inputInvaildCharacters: '输入不能包含以下字符串：*/:<>\\?|"',
			addModalDescription: '描述IT实质性程序内容。',
			editModalDescription: '编辑IT实质性程序及其相关属性。',
			entityName: 'IT实质性程序',
			selectITProcess: '选择IT流程（必填）',
			entityNameCaps: 'IT实质性程序',
			processIdRequired: 'IT流程为必填项',
			entityNamePlural: 'IT实质性程序',
			itspRequired: 'IT实质性程序名称为必填项',
			close: '关闭',
			cancel: '取消',
			iTProcesslabel: 'IT流程',
			save: '保存',
			confirm: '确认',
			saveAndCloseLabel: '保存并关闭',
			riskNameRequired: '技术风险为必填',
			saveAndCreateLabel: '保存并新建',
			placeholderText: '新建IT实质性程序或编辑并删除以下现有IT实质性程序。',
			deleteEntity: '删除IT一般控制 ',
			deleteConfirmLabel: '是否确认删除此IT实质性程序？所有现有关联都将取消。此操作无法撤销。',
			itspTaskDescription: '自行确定关于IT实质性程序的性质、时间和范围设计的任务描述，以获得充分、适当的审计证据，证明技术风险在整个依赖期间得到有效应对。<br />当IT实质性程序在中期日执行时，设计和执行程序，以获得额外的证据，证明我们对从中期程序所涵盖的期间到期末之间期间的IT风险进行了应对。我们对IT实质性程序的结果得出结论。',
			operationEffectiveness: '运行有效性',
			itDesignEffectivenessHeader: '设计有效性',
			itTestingColumnHeader: '测试',
			testingTitle: '测试',
			frequency: '频率',
			controlOpertaingEffectiveness: '运行有效性',
			designEffectiveness: '设计有效性',
			frequencyITGC: '选择频率',
			nameITGC: 'IT一般控制名称（必填）',
			itspNameRequired: 'IT实质性程序名称（必填）',
			noResultsFound: '未找到结果',
			selectITRisk: '选择技术风险（必选）',
			itRiskRequired: '技术风险（必填）',
			itRiskName: '技术风险',
			itProcessRequired: 'IT流程（必填）',
			controlDesignEffectiveness: {
				[0]: {
					description: '未选择'
				},
				[1]: {
					description: '有效'
				},
				[2]: {
					description: '无效'
				}
			},
			controlTesting: {
				[0]: {
					description: '未选择'
				},
				[1]: {
					description: '是'
				},
				[2]: {
					description: '否'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: '未选择'
				},
				[1]: {
					description: '有效'
				},
				[2]: {
					description: '无效'
				}
			},
		}
	},
	[Entity.ITSOApplication]: {
		manageEntity: '管理IT应用程序',
		searchEntity: '搜尋IT应用程序',
		createEntity: '建立IT应用程序',
		entityName: 'IT应用程序',
		entityNameCaps: 'IT应用程序',
		entityNamePlural: 'IT应用程序',
		placeholderText: '创建新的IT应用程序或编辑和删除下面的现有IT应用程序。',
		deleteEntity: '删除IT应用程序',
		deleteConfirmLabel: '是否确定删除选定IT应用程序? 所有现有关联都将取消。此操作无法撤销。'
	},
	[Entity.SCOT]: {
		manageEntity: '管理重大交易类别',
		searchEntity: '搜索重大交易类别',
		createEntity: '新建重大交易类别',
		entityName: '重大交易类别',
		entityNameCaps: '重大交易类别',
		entityNamePlural: '重大交易类别',
		placeholderText: '创建新的重大交易类别或编辑并删除下方现有重大交易类别。',
		deleteEntity: '删除重大交易类别',
		deleteConfirmLabel: '是否确认删除此重大交易类别？所有现有关联都将取消。此操作无法撤销。'
	},
	[Entity.SampleItem]: {
		manageEntity: 'Manage sample tags',
		searchEntity: '搜索标记',
		createEntity: '新标记',
		createManageTagEntity: '管理标记组',
		entityName: '样本标记',
		entityNamePlural: '标记',
		entityNameForTagGroupPlural: '标记组',
		placeholderText: '创建新标记或编辑并删除以下已有标记。如果您需要创建新的标记组，请点击<b>“管理标记组”</b>',
		deleteEntity: '删除样本标记',
		deleteConfirmLabel: '是否确定删除已选定标记? 这些标记将从与其相关的所有样本中删除。此操作无法撤销。'
	},
	[Entity.SampleTagGroups]: {
		manageEntity: '管理样本标记组',
		searchEntity: 'Search tag groups',
		createEntity: '新建标记组',
		entityName: 'sample tag group',
		entityNameCaps: '标记组',
		entityNamePlural: '标记组',
		placeholderText: '创建新的标记组或编辑并删除下面的现有样本标记组。',
		deleteConfirmLabel: '是否确定删除已选定标记? 这些标记将从与其相关的所有样本中删除。此操作无法撤销。'
	},
};

export const inherentRiskFactorTypes = [{
	id: 1,
	label: '复杂性',
	displayOrder: 1
},
{
	id: 2,
	label: '主观性  不确定性',
	displayOrder: 2
},
{
	id: 3,
	label: '舞弊或错误',
	displayOrder: 3
},
{
	id: 4,
	label: '变动',
	displayOrder: 4
},
{
	id: 5,
	label: '账户性质',
	displayOrder: 5
},
{
	id: 6,
	label: '关联方',
	displayOrder: 6
}
];

export const executionType = [{
	id: 1,
	label: 'PT',
	toolTip: '此项目中的程序[仅限主审小组]',
	value: '在此项目中[仅限主审小组]'
},
{
	id: 2,
	label: 'CT',
	toolTip: '其他项目中的程序[仅限组成部分项目小组]',
	value: '在其他项目中[仅限组成部分项目小组]'
},
{
	id: 3,
	label: 'PT/CT',
	toolTip: '此项目和其他项目的程序[主审小组/组成部分项目小组CT]',
	value: '在此项目和其他项目中[主审小组/组成部分项目小组]'
}
];

export const createEditAccountModalLabels = {
	createModalDescription: '在下方输入新账户详情，然后点击“<b>{0}</b>”完成。如想另外创建账户，请点击“<b>{1}</b>”。',
	editModalDescription: "编辑下方账户详情，然后点击'<b>{0}</b>' 完成。",
	close: '关闭',
	cancel: '取消',
	createAccount: '新增账户',
	editAccount: '编辑账户',
	newSignificantDisclosure: '新增重大披露',
	save: '保存',
	confirm: '确认',
	saveAndCloseLabel: '保存并关闭',
	saveAndCreateLabel: '保存并新建',
	accountNameLabel: '账户名称（必填）',
	accountDesignationLabel: '指定',
	accountExecutionTypeLabel: '将在哪个Canvas项目中执行并记录此账户的程序？',
	accountEstimateLabel: '此账户是否受估计影响？',
	yes: '是',
	no: '否',
	accountStatementTypeLabel: '报表类型',
	pspIndexDropdownLabel: '主要实质性程序索引（必填，至多选择5个）',
	removePSPIndexLabel: '删除主要实质性程序索引',
	assertionsLabel: '选择相关认定',
	accountTypeOptions: AccountType,
	assertionOptions: assertions,
	executionTypeOptions: executionType,
	statementTypeOptions: statementTypes,
	noOptionsMessage: '未找到结果',
	accountNameErrorMsg: '必填',
	pspIndexErrorMsg: '必填',
	assertionWarningMessage: '您不能修改具有相关特别风险、舞弊风险或重大错报风险或估计相关的认定。请先删除关联。',
	confirmChanges: '确认变更',
	executionTypeWarningMessage: '您要保存到此账户的变更将影响现有认定和主要实质性程序。链接将断开。是否要继续？此操作不可撤销。',
	contentUpdateToastMessage: '{0}可以进行内容更新。请从内容更新页面发起内容更新。',
	assertionsRequired: '应至少选择一个认定',
	pspIndexDisabledLabel: '至多选择5个主要实质性程序索引。请取消选择一个或多个选项以继续。'
};

export const createEditRiskModalLabels = {
	createModalDescription: '在下方输入新的风险详请，然后选择“<b>{0}</b>”完成。如想另外创建风险，请选择“<b>{1}</b>”。',
	editModalDescription: '编辑下方风险详请，然后选择“<b>{0}</b>”完成。',
	close: '关闭',
	cancel: '取消',
	createRisk: '新风险',
	editRisk: '编辑风险',
	riskType: '风险类型（必填）',
	riskTypeOptions: [{
		value: 1,
		label: '特别风险'
	},
	{
		value: 2,
		label: '舞弊风险'
	},
	{
		value: 3,
		label: '重大错报风险'
	}
	],
	save: '保存',
	saveAndCloseLabel: '保存并关闭',
	saveAndCreateLabel: '保存并新建',
	riskNameLabel: '风险名称（必填）',
	relatedAccountsAssertionsLabel: '相关账户及认定（可选填）',
	relateAccounts: '相关账户',
	assertionsLabel: '选择相关认定',
	riskNameErrorMsg: '必填',
	riskTypeErrorMsg: '必填',
	assertionOptions: assertions,
	removeAccountLabel: '删除账户',
	required: '必填',
	assertionsRequired: '应至少选择一个认定'
};

export const CreateEditMestLabels = {
	createModalTitle: '新实体',
	createModalDescription: '在下方输入新的实体详情，然后点击<b>{0}</b>结束。如想另外创建实体，点击“<b>{1}</b>”。',
	close: '关闭',
	cancel: '取消',
	save: '保存',
	confirm: 'Confirm',
	primary: 'Primary',
	saveAndCloseLabel: '保存并关闭',
	saveAndCreateLabel: '保存并新建',
	entityNameLabel: '实体名称（必填）',
	entityStandardIndexLabel: '实体标准索引（必填）',
	entityDescriptionLabel: '实体描述',
	entityNameErrorMsg: '必填',
	entityStandardIndexErrorMsg: '必填',
	editModalTitle: '编辑实体',
	editModalDescription: '在下方编辑实体详情，然后点击<b>{0}</b>结束。',
	primaryEntitySelectionLabel: 'Select as the primary entity',
	primaryEntitySelectionMsg: "Only one entity in the engagement can be selected as the primary entity, which will be the determinant for the content delivered to the engagement. An entity will need to be selected as the primary to be able to submit the engagement profile. \'Update content\' permission is required to make or edit the primary entity selection.",
	primaryEntityDisableSelectionLabel: "To change the primary entity designation, select from the \'Edit\' of the entity you wish to designate as primary",
	noAccessLabel: 'Unauthorized. Contact your administrator and try again.',
	primaryEntityConfirmationLabel: 'Primary entity confirmation',
	primaryEntityConfirmationDisplay: '{0} is currently selected as the primary entity. Are you sure you want to change the primary entity?',
	profileV2ChangeNotSubmittedBannerMessage: 'Changes have been made to the profile that will result in content updates. Submit the profile to receive the new content or revert the answers to the previous state.',
};

export const CreateEditITProcessLabels = {
	close: '关闭',
	cancel: '取消',
	yes: '是',
	no: '否',
	delete: '删除',
	save: '保存',
	saveAndCloseLabel: '保存并关闭',
	saveAndCreateLabel: '保存并新建',
	newITProcessLabel: '新建IT流程',
	editITProcessLabel: '编辑IT流程',
	viewITProcessLabel: '查看IT流程',
	addDescriptionLabel: '在下方输入新的IT流程详情，然后点击“<b>{0}</b>”完成。如想另外创建IT流程，点击“<b>{1}</b>”。',
	editDescriptionLabel: '在下方编辑IT流程详情，然后点击“<b>{0}</b>”完成。',
	iTProcessNameLabel: 'IT流程名称（必填）',
	confirm: '确认',
	confirmChanges: '确认',
	iTProcessNameErrorMsg: '必填',
	inputInvaildCharacters: '输入不能包含以下字符串：*/:<>\\?|"',
	remove: '移除'
};

export const CreateEditITRiskLabels = {
	close: '关闭',
	cancel: '取消',
	yes: '是',
	no: '否',
	delete: '删除',
	save: '保存',
	saveAndCloseLabel: '保存并关闭',
	saveAndCreateLabel: '保存并新建',
	newITRiskLabel: '新技术风险',
	editITRiskLabel: '编辑技术风险',
	itRiskNameLabel: '技术风险（必填）',
	confirm: '确认',
	confirmChanges: '确认',
	itRiskNameErrorMsg: '必填',
	itProcessNotSelectedErrorMsg: '必填',
	hasNoITGCLabel: '没有应对技术风险的IT一般控制',
	editModalDescription: '编辑技术风险描述。',
	createModalDescription: '输入技术风险描述',
	selectITProcess: '选择IT流程（必填）',
	noITProcessAvailable: '未创建IT流程',
	relatedITProcessLabel: '已关联IT流程',
	inputInvaildCharacters: '输入不能包含以下字符串：*/:<>\\?|"',
	remove: ' 删除'
};

export const CreateEditEstimateLabels = {
	createModalDescription: '在下方输入新的估计详情，然后点击“<b>{0}</b>”完成。如想另外创建估计，点击“<b>{1}</b>”。',
	editModalDescription: '编辑下方估计详情，然后点击“<b>{0}</b>”完成。',
	close: '关闭',
	cancel: '取消',
	save: '保存',
	saveAndCloseLabel: '保存并关闭',
	saveAndCreateLabel: '保存并新建',
	createModalTitle: '新建估计',
	editEstimateLabel: '编辑估计',
	estimateNameLabel: '估计名称（必填）',
	analysisPeriodBalance: '分析日余额（必填）',
	analysisPeriodDate: '分析日期（必填）',
	comparativePeriodBalance: '比较日余额（必填）',
	comparativePeriodDate: '比较日期（必填）',
	estimateCategory: '估计类别（必填）',
	confirm: '确认',
	estimateNameErrorMsg: '必填',
	analysisPeriodBalanceErrorMsg: '必填',
	analysisPeriodDateErrorMsg: '必填',
	comparativePeriodBalanceErrorMsg: '必填',
	comparativePeriodDateErrorMsg: '必填',
	estimateCategoryErrorMsg: '必填',
	remove: '移除',
	balanceNOTApplicable: '余额不适用',
	wtDetailPrefixForEstimate: '<p>Complete the estimate walkthrough task.</p>',

	riskLevelOptions: [{
		value: 4,
		label: '风险非常低'
	},
	{
		value: 5,
		label: '较低风险'
	},
	{
		value: 6,
		label: '较高风险'
	},
	{
		value: 7,
		label: '未选择'
	}
	]
};

export const CreateEditControlLabels = {
	createModalTitle: ' 新控制',
	editModalTitle: '编辑控制',
	viewModalTitle: '查看控制',
	close: ' 关闭',
	cancel: ' 取消',
	save: ' 保存',
	saveAndCloseLabel: ' 保存并关闭',
	saveAndCreateLabel: ' 保存并创建另一个',
	controlNameLabel: ' 控制名称（必填）',
	frequency: ' 频率',
	controlType: ' 控制类型',
	frequencyTypeOptions: controlFrequencyType,
	controlTypeOptions: controlTypes,
	designEffectiveness: ' 设计有效性',
	operatingEffectiveness: ' 运行有效性',
	testingLabel: ' 测试',
	lowerRiskLabel: 'Is control lower risk?',
	effective: ' 有效',
	ineffective: ' 无效',
	yes: ' 是',
	no: ' 否',
	required: ' 必填',
	remove: ' 删除',
	noOptionsMessage: ' 未找到结果',
	disabledTabTooltipMessage: '选择“控制类型”为“IT应用程序控制”或“依赖IT的手动控制”，以关联IT应用程序',
	itAppLabels: {
		tabLabel: 'IT应用程序',
		dropdownLabel: ' 关联IT应用程序',
		noRelatedItems: ' 无相关的IT应用程序',
		itApplications: 'IT应用程序'
	},
	soLabels: {
		tabLabel: '服务机构',
		dropdownLabel: '关联服务机构',
		noRelatedItems: '无相关的服务机构',
		serviceOrganizations: '服务机构'
	},
	controlNameErrorMsg: '必填',
	createModalDescriptionLabel: '在下方输入新的控制详情，然后点击“<b>{0}</b>”完成。如想另外创建控制，点击“<b>{1}</b>”。',
	editModalDescriptionLabel: '编辑下方控制详情，然后点击“<b>{0}</b>”完成。',
	viewModalDescriptionLabel: '查看控制和相关IT应用程序及服务机构。',
	wcgwLabel: '可能出错项'
};

export const ITApplicationTypeLabels = [{
	value: 1,
	label: '应用程序/工具'
},
{
	value: 2,
	label: '数据库'
},
{
	value: 3,
	label: '操作系统'
},
{
	value: 4,
	label: '网络'
},
{
	value: 6,
	label: '未分类'
}
];

export const CreateEditITApplicationLabels = {
	close: '关闭',
	cancel: '取消',
	yes: '是',
	no: '否',
	delete: '删除',
	save: '保存',
	saveAndCloseLabel: '保存并关闭',
	saveAndCreateLabel: '保存并新建',
	newITApplicationLabel: '建立IT应用程序',
	editITApplicationLabel: '编辑IT应用程序',
	iTApplicationNameLabel: 'IT应用程序名称',
	confirm: '确认',
	confirmChanges: '确认变更',
	noOptionsMessage: '未找到结果',
	iTAppNameErrorMsg: '必填',
	controls: '控制',
	substantive: '实质性',
	remove: '移除',
	iTApplicationStrategyLabel: 'IT应用程序策略',
	SCOTsLabel: '重大交易类别名称',
	StrategyLabel: '策略',
	ControlsLabel: '控制',
	ControlTypeLabel: '类型',
	addDescriptionLabel: "在下方输入新IT应用程序详情，然后点击'<b>{0}</b>'完成。如想另外创建IT应用程序，请点击'<b>{1}</b>'。",
	editDescriptionLabel: "在下方编辑IT应用程序详情，然后点击'<b>{0}</b>'完成。",
	scotErrorMessage: '重大交易类别可能并非与IT应用程序无关，因为存在关联的控制。',
	SCOTsLabels: {
		tabLabel: '重大交易类别',
		dropdownLabel: '关联重大交易类别',
		noRelatedItems: '无已关联重大交易类别'
	},
	ControlsLabels: {
		tabLabel: '控制',
		dropdownLabel: '关联控制',
		noRelatedItems: '无已关联控制'
	},
	strategyType: {
		1: '控制',
		2: '实质性',
		3: '依赖',
		4: '不依赖'
	},
	controlType: {
		1: 'IT应用程序',
		2: '依赖IT的手动控制',
		3: '手动预防',
		4: '手动检查'
	},
	technologyTypeOptions: ITApplicationTypeLabels,
	technologyType: 'Select technology type'
};

export const CreateEditSCOTLabels = {
	createModalTitle: '新建重大交易类别',
	editModalTitle: '编辑重大交易类别',
	viewModalTitle: '查看重大交易类别',
	createModalDescription: '在下方输入新的重大交易类别详情，然后点击“<b>{0}</b>”完成。如想另外创建重大交易类别，点击“<b>{1}</b>”。',
	editModalDescription: '编辑下方重大交易类别详情，然后点击“<b>{0}</b>”完成。',
	close: '关闭',
	cancel: '取消',
	save: '保存',
	saveAndCloseLabel: '保存并关闭',
	saveAndCreateLabel: '保存并新建',
	scotNameLabel: '重大交易类别名称（必填）',
	scotStrategyLabel: '重大交易类别策略',
	scotTypeLabel: '重大交易类别类型',
	hasEstimateLabel: '该重大交易类别是否受估计影响？',
	itAPPUsedLabel: '是否使用了任何IT应用程序？',
	routine: '常规',
	nonRoutine: '非常规',
	controls: '控制',
	substantive: '实质性',
	yes: '是',
	scotNameErrorMsg: '必填',
	remove: '移除',
	noOptionsMessage: '未找到结果',
	disabledTabTooltipMessage: '选择“是否使用了任何IT应用程序？”以关联IT应用程序',
	itAppLabels: {
		itApplications: '关联的IT应用程序',
		tabLabel: 'IT应用程序',
		dropdownLabel: '关联IT应用程序',
		noRelatedItems: '无关联的IT应用程序'
	},
	soLabels: {
		serviceOrganizations: '关联的服务机构',
		tabLabel: '服务机构',
		dropdownLabel: '关联服务机构',
		noRelatedItems: '无关联的服务机构'
	},
	wtDetailPrefix: '<p>对于所有常规和非常规重大交易类别和重大披露流程，我们通过执行穿行测试程序在每个期间确认我们的了解。此外，对于PCAOB审计，我们执行估计重大交易类别的穿行测试程序<br/>。对于我们采取控制依赖策略的所有重大交易类别，以及应对特别风险的控制，我们确认已适当设计和实施相关控制。我们确认，我们采取控制依赖策略的决定仍然适当。<br/><br/>我们得出结论，我们的文档记录准确描述了重大交易类别的运行，并且我们已经识别出了所有适当的可能出错项，包括使用IT产生的风险和相关控制（如适用）。<br/><br/>对于使用唯实质性策略的估计重大交易类别，我们根据我们的实质性程序确定我们对估计重大交易类别的了解是否适当。</p>',
};

export const ViewSampleItemLabels = {
	previous: '上一个',
	next: '下一个',
	sampleDateLabel: '样本日期',
	attributesHeader: '属性',
	statusHeader: '状态',
	noAttributesLabel: '无可用属性。',
	present: '存在',
	presentWithComments: '存在且随附意见',
	notPresent: '不存在',
	notApplicatable: '不适用',
	naLabel: '不适用',
	additionDocumentation: '其他文档记录',
	deleteSampleHeader: '删除样本',
	deleteSmapleDescription: '是否要删除选定的样本? 此操作无法撤销。',
	deleteSamplePreText: '样本描述',
	relateTagModalTitle: '将标签与样本关联',
	relateTagModalDescription: "将一个或多个标签与样本关联。若要添加新标签，请单击<b>'管理标签’</b>。标签关联将不会被存档，但标签本身将被存档，以便在后推期间使用。",
	relateTagTableHeader: '标签名称',
	relateTagTableSubHeader: '标签组',
	tagsCounter: '{0}个标签',
	tagCounter: '{0}个标签',
	relateTagGroupLabel: '标签组',
	relateTagSearchPlaceholder: '搜索',
	relateTagClearSearch: '清除',
	relateTagShowSelectedOnly: '仅显示相关',
	manageTagsLabel: '管理标签',
	addTag: '添加标记',
	supportingDocumentationTitle: '支持性文档记录',
	dropdownAll: '全部',
	noResultsLabel: '未找到结果',
	noDataLabel: '未找到数据',
	attributeStatusModalTitle: 'Mark all as present',
	attributeStatusModalCancelButton: '取消',
	attributeStatusModalConfirmButton: '保存',
	attributeStatusModalDescription: '是否确实要将属性标记为存在？只有未选择状态的属性才会被标记为存在。',
	attributeModalDeleteErrorMessage: '无法更新属性状态。请刷新页面并重试。如仍有错误，请联系帮助台。',
};

export const ShortRiskTypeForAres = {
	1: '重大',
	2: '舞弊',
	3: '固有',
	4: '非常低风险',
	5: '较低风险',
	6: '风险较高',
	7: '未选择'
};

export const RelateEstimateToAssertionLabels = {
	relateAccountsAndAssertions: '关联账户和认定',
	relateAccountsToEstimate: '将账户与估计关联',
	accounts: '账户',
	designation: '指定',
	relatedAssertions: '关联的认定',
	accountNameField: '账户名称',
	accountTypeIdField: '账户类型ID',
	assertionsField: '认定',
	executionTypeIdField: '执行类型ID',
	notSelected: '未选择',
	pathField: '路径',
	noAccountsAvailable: '无可用账户',
	noRelatedAccounts: '无与估计相关的账户',
	accountId: '账户ID',
	remove: '删除'
};

//Send instructions switcher
export const sendIntructionsSwitcherLabels = {

	[sendInstructionsSwitcherIds.groupInstructions]: '集团指引',
	[sendInstructionsSwitcherIds.groupRiskAssessment]: '集团风险评估'
};

export const RelateEstimateToAccountLabels = {
	relatEstimatesToAccount: '将估计与账户关联',
	showOnlyRelatedEstimates: '仅显示关联的对象',
	noEstimatesResult: labels.noResultsFound,
	noEstimatesLabel: '项目中未创建估计',
	estimateNameHeader: '估计名称',
	relatedEstimateCounter: '{0}个估计',
	relatedEstimatesCounter: '{0}个估计',
	relatedAccount: '账户/披露',
	close: labels.close
};

export const RelateAccountsToEstimateLabels = {
	relateAccountsToEstimate: '将账户与估计关联',
	showOnlyRelatedAccounts: '仅显示相关账户',
	noAccountsResult: labels.noResultsFound,
	noAccountsLabel: '项目中未创建账户',
	accountNameHeader: '账户名称',
	relatedAccountCounter: '{0}个账户',
	relatedAccountsCounter: '{0}个账户',
	relatedEstimate: labels.estimate,
	close: labels.close
};

export const SupportingDocumentationLabels = {
	evidence: '证据',
	priorPeriod: '上期',
	temporaryFiles: '临时文件',
	externalDocuments: '外部文档',
	addEvidenceBtn: '添加证据',
	addTemporaryFilesBtn: '添加临时文件',
	notes: '注释',
	signOffs: '签署',
	name: '姓名',
	supportingDocumentationTitle: '支持性文档记录',
	temporaryFilesEmptyPlaceholder1: '无关联临时文档。',
	temporaryFilesEmptyPlaceholder2: '若要关联临时文档，请点击{addTemporaryFiles}。',
	evidencePlaceholderLine1: '无相关证据。',
	evidencePlaceholderLine2: '如要关联证据，请点击{addEvidenceBtn}。',
	removeFromSample: '从样本中删除',
	unlink: '取消链接',
	retailControlEvidenceLabel: '我们是否保留了控制证据，以支持对该特定样本项属性的测试？',
	removeEvidenceModalTitle: '从样本中移除证据',
	removeEvidenceModalDesc: '是否确认移除此样本中的所有证据？',
	removeEvidenceErrorMessage: '这些文档在此样本中将不再可用。请刷新页面并重试。如错误仍然存在，请联系帮助台。'
}

export const deleteSampleItemAttributeModal = {
	modalDescription: '是否确实要删除此属性的选择？其他文档记录将被删除。',
	modalTitle: '删除所选内容',
	modalConfirmButton: '移除',
	modalCancelButton: '取消',
	additionalDocumentationLabel: '其他文档记录'
}
export const accountsFilterLabels = [{
	id: accountsFilter.allAccounts,
	label: '所有账户',
	value: accountsFilter.allAccounts
},
{
	id: accountsFilter.accountsWithRelatedEstimates,
	label: '具有已关联估计的账户',
	value: accountsFilter.accountsWithRelatedEstimates
},
{
	id: accountsFilter.accountsWithoutRelatedEstimates,
	label: '没有已关联估计的账户',
	value: accountsFilter.accountsWithoutRelatedEstimates
}
];

export const changeSampleItemAttributeModal = {
	modalDescription: '是否确定更改对此属性所做选择？将有其他文档记录被删除。',
	modalTitle: '更改选择',
	modalConfirmButton: '更改',
	modalCancelButton: '取消'
}
export const scotsFilterLabels = [{
	id: scotsFilter.allScots,
	label: '所有重大交易类别',
	value: scotsFilter.allScots
},
{
	id: scotsFilter.scotsWithRelatedEstimates,
	label: '具有已关联估计的重大交易类别',
	value: scotsFilter.scotsWithRelatedEstimates
},
{
	id: scotsFilter.scotsWithoutRelatedEstimates,
	label: '没有已关联估计的重大交易类别',
	value: scotsFilter.scotsWithoutRelatedEstimates
}
];

export const CreateEditTagGroupLabels = {
	createModalTitle: '新建样本标记组',
	editModalTitle: '编辑样本标记组',
	createModalDescription: '在下方输入标记组详情，然后点击  <b>\“保存并关闭\”</b>结束。如想另外创建标记组，请选择 <b>\“保存并另外创建\”</b>。',
	editModalDescription: '编辑下方标记组详情，然后点击“<b>{0}</b>”完成。',
	close: '关闭',
	cancel: '取消',
	save: '保存',
	saveAndCloseLabel: '保存并关闭',
	saveAndCreateLabel: '保存并另外创建',
	tagGroupNameLabel: '标记组名称（必填）',
	required: '必填'
};

export const CreateEditTagLabels = {
	createModalTitle: '新样本标记',
	editModalTitle: 'Edit sample tag',
	createModalDescription: '在下方输入标记详情，然后点击“<b>保存并关闭</b>”结束。如想另外创建标记，请选择“<b>保存并另外创建组成部分</b>”。',
	editModalDescription: `Edit the tag details below and select'<b>{0}</b>' to finish.`,
	tagNameLabel: '标记名称（必填）',
	tagGroupNameLabel: '标记组名称（必填）',
	tagColorLabel: '颜色（必填）',
	saveAndCloseLabel: '保存并关闭',
	saveAndCreateLabel: '保存并另外创建',
	cancelLabel: '取消',
	required: '必填',
	save: '保存',
	noresultsLabel: '没有可用的标记组',
	tagColors: [{
		text: '红色',
		color: '红色'
	},
	{
		text: '橙色',
		color: '橙色'
	},
	{
		text: '蓝绿色',
		color: '蓝绿色'
	},
	{
		text: '蓝色',
		color: '蓝色'
	},
	{
		text: '紫色',
		color: '紫色'
	},
	{
		text: '绿色',
		color: '绿色'
	}
	]
};

export const ITProcessFlowLabels = {
	itProcess: 'IT流程',
	technology: '技术',
	technologies: '技术',
	technologyName: '技术名称',
	supportingTechnologyName: '支持性技术名称',
	technologyType: '技术类型',
	showOnlyRelated: '仅显示已关联',
	technologiesCounter: '{0}技术',
	technologyCounter: '{0}技术',
	supportingTechnologyLabel: '支持性技术',
	relatedITAppNoDataPlaceholder: '没有与IT流程相关的技术。',
	relateTechnology: '相关技术',
	supportingITPAppNoDataPlaceholder: '没有支持IT流程的技术',
	itRiskHeader: 'IT风险',
	itgcHeader: 'IT一般控制',
	itspHeader: 'IT实质性程序',
	noDataPlaceholderITGC: '必须至少识别一个IT一般控制，或明确IT风险没有IT一般控制。创建{createNewITGC}、{relateITGC}或明确存在应对IT风险的{noITGC}。',
	noDataPlaceholderITSP: '如果我们将IT一般控制评估为无效，或确定不存在应对IT风险的IT一般控制，则执行IT实质性测试程序，以合理保证无效IT一般控制的相关IT流程内的风险未被不当利用。创建{createNewITSP}或{relateITSP}。',
	noRecordsFound: '没有对此IT流程识别到IT风险',
	noITGCPlaceholder: '没有应对该IT风险的IT一般控制。',
	relateSupportingTechnology: '相关支持性技术',
	relatedTechnologiesNotAvailable: '相关技术不適用于此文档',
	supportingTechNotAvailable: '支持性技术不適用于此文档',
	relatedITRisksNotAvailable: '相关IT风险不适用于本文档',
	relateITGC: 'Relate ITGCs',
	itRisk: 'IT风险',
	itgcCounter: '{0}IT一般控制',
	itgcsCounter: '{0}IT一般控制',
	itgcName: 'IT一般控制名称',

};

export const ITRisksFlowLabels = {
	itRisk: 'IT风险',
	relatedITRiskNoDataPlaceholder: '没有与IT流程相关的IT风险。',
	newITRisk: '新IT风险',
	relatedITRisksNotAvailable: '相关IT风险不适用于本文档',
	deleteConfirmLabel: '是否确定删除已选定IT风险? 此操作无法撤销。',
	deleteITRisk: '删除IT风险',
	CreateITFlowITRiskLabels: {
		close: '关闭',
		cancel: '取消',
		yes: '是',
		no: '否',
		delete: '删除',
		save: '儲存',
		saveAndCloseLabel: '儲存并关闭',
		saveAndCreateLabel: '儲存并另外创建',
		newITRiskLabel: '新IT风险',
		itRiskNameLabel: 'IT风险名称（必填）',
		itRiskCharactersLabel: '字符',
		itRiskOfLabel: '的',
		confirm: '确认',
		confirmChanges: '确认',
		itRiskNameErrorMsg: '必填',
		itProcessNotSelectedErrorMsg: '必填',
		hasNoITGCLabel: '没有应对该IT风险的IT一般控制',
		createModalDescription: '在下方输入IT详请，然后选择<b>“儲存并关闭”</b>完成。若要创建另一个IT风险，请选择“<b>儲存并另外创建</b>”。',
		relatedITProcessLabel: 'IT流程',
		inputInvaildCharacters: '输入資訊不能包含以下字符串：*/:<>\\?|"',
		remove: 'Remove',
		editModalDescription: "在下方编辑IT风险详请，然后点击'<b>{0}</b>'完成。",
		editITRiskLabel: 'Edit IT risk'
	}
};

export const ITProcessListingLabels = {
	all: '全部',
	manageChange: '管理变更',
	manageAccess: '管理访问权限',
	manageSecuritySettings: '管理安全设置',
	itOperations: 'IT运行',
	systemImplementation: '系统实施',
	category: '类别',
	uncategorized: '未分类',
	technologies: '技术',
};

export const ITProcessQuickFilterOptions = {
	0: ITProcessListingLabels.all,
	1: ITProcessListingLabels.manageChange,
	2: ITProcessListingLabels.manageAccess,
	3: ITProcessListingLabels.manageSecuritySettings,
	4: ITProcessListingLabels.itOperations,
	5: ITProcessListingLabels.systemImplementation
}

// Relate Technology Modal
export const RelateTechnologyModalLabels = {
	relateTechnology: '关联技术',
	relatedTechnologiesDescription: '技术名称',
	supportingTechnologyName: '支持性技术名称',
	technology: '{0} technology',
	technologies: '{0} technologies'
};

export const AccountStandardROMMListingLabels = {
	accountRisksNotAvailableForDocument: '该文档不支持账户风险分析。',
	noRelatedObject: '无关联对象。关联一个对象以开始。',
	noResultsFound: '无可用风险',
	acceptedText: '已接受',
	rejectedText: '已拒绝',
	allRisksRejected: '已拒绝所有风险',
	relevantAssertions: '相关认定',
	rejectLabel: '拒绝',
	acceptLabel: '接受',
	rejectionRationaleLabel: '拒收理由',
	rejectionCategoryText: '拒绝类别',
	editRejectionRationaleText: '编辑拒绝理由',
	rejectionRationalePlaceholder: "Are you sure you want to reject the selected risk? Enter the details below and select <strong>\'Reject\'</strong>.",
	cancel: 'Cancel',
	rejectionRationaleTextAreaPlaceholder: 'Rationale (required)',
	rejectionCategoryDropdownPlaceholder: 'Rejection category (required)',
	required: 'Required',
	preRejectedText: 'Pre-rejected',
	additionalContextLabel: 'Additional context',
	additionalContextwhyRiskShouldBeRejected: 'Click {0} to add additional context specific to this client for why this risk should be rejected.',
	hereLink: 'here',
	editAdditionalContextText: 'Edit additional context'
}

export const RejectionCategory = [{
	id: 1,
	label: '可能性-不存在发生的合理可能性（不考虑控制）'
},
{
	id: 2,
	label: '量级-潜在错报不重大'
},
{
	id: 3,
	label: '风险对本项目不重大'
},
{
	id: 4,
	label: '重大错报风险在另一个项目中应对（仅限集团审计）'
},
]

export const formLabels = {
	required: labels.required,
	maxLength: labels.maxLength
};

export const paginationLabels = {
	show: labels.pagingShowtext,
	first: '首页',
	last: '最后一页',
	prev: '上一页',
	next: '下一页'
};
