/* eslint-disable prettier/prettier,max-len */

import {
	Entity,
	GALinkStatus,
	confidentialityTypes,
	currencyType,
	gaRegion,
	gaRoleTypes,
	gaScopeType,
	notesFilter,
	pointOfContactTypes,
	validationTypes,
	KnowledgeSectionIds,
	sendInstructionsSwitcherIds,
	accountsFilter,
	scotsFilter,
	rejectionType
} from '../util/uiconstants';

/**
 * Created by calhosh on 4/14/2017.
 * ES MX resource file
 */
export const labels = {
	addEvidenceBtn: 'Añadir evidencias',
	multipleDocuments: 'Múltiples documentos',
	invalidEngagementId: 'Id del compromiso es invalido. Actualice la pagina e intente de nuevo. Contacte al Help desk si el error persiste',
	newComponent: 'Nuevo componente',
	workingOffline: 'Trabajando fuera de linea',
	syncInProgress: 'Sincronizacion en proceso',
	prepareOffline: 'Preparando los datos para fuera de linea',
	connectionAvailable: 'Conexión disponible',
	training: 'Entrenamiento',
	clickForOptions: 'Click para mas opciones',
	navReviewNotes: 'Notas de revision',
	navHeaderManageTeam: 'Administracion de equipo',
	navManageGroup: 'Administracion de grupo',
	manageObjects: 'Administrar objetos',
	navCRASummary: 'resumen de CRA',
	navAuditPlan: 'Plan de auditoria',
	navWorkPlan: 'Plan de trabajo',
	navSEM: 'Matriz de evaluacion sustantiva',
	navFindings: 'Hallazgos',
	navContentUpdates: 'Actualización de contenido',
	navCanvasFormUpdates: 'Actualizacion de formas de canvas',
	navCopyHub: 'Centro de copiado',
	navCopyHubNew: 'Copy Hub NUEVO',
	navArchiveChecklist: 'Checklist de archive',
	navExportHub: 'centro de exportacion',
	navReporting: 'reporte',
	navHelp: 'Ayuda general',
	validationNavHelp: 'Ayuda de validación',
	leaveUsFeedback: 'Déjanos tus comentarios',
	navDashboard: 'Pantalla principal',
	tasksPage: 'Tareas',
	documentsPage: 'documentos',
	collaborationPage: 'colaboracion',
	automationPage: 'Automatizacion',
	documentHelperConnectionIssue: 'Se ha detectado un problema con EY Canvas Document Helper. Haga clic <a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">aquí</a> para obtener instrucciones sobre cómo resolver este problema.',
	noContentAvailable: 'no existe contenido disponible',
	noSectionsAvailable: 'No hay secciones disponibles',
	noInformationAvailable: 'no hay información disponible',
	collapse: 'Colapsar',
	expand: 'Expandir',
	duplicate: 'Duplicar',
	duplicateSection: 'Sección duplicada',
	duplicateSectionHeader: '¿Está seguro de que desea duplicar la sección seleccionada?',
	deleteSection: 'Eliminar sección',
	deleteSectionHeader: '¿Estás seguro de que quieres eliminar la sección seleccionada?',
	deleteHeader: 'Eliminar encabezado',
	deleteHeaderTitle: '¿Está seguro de que desea eliminar el encabezado seleccionado?',
	confirmLabel: 'Confirmar',
	custom: 'Personalizado',
	selectHeader: 'Seleccionar encabezado',
	selectSection: 'Seleccionar sección',
	noResultsFound: 'No se han encontrado resultados',
	scot: 'SCOT',
	scotTypes: 'Tipo SCOT',
	frequency: 'Frecuencia',
	SelectFrequency: 'Seleccione frecuencia',
	SelectControlType: 'Selecciones tipo de control',
	itBadge: 'IT',
	soBadge: 'SO',
	noRecordsAvailable: 'No hay registros disponibles',
	noIncompleteResponseSummaryView: 'Sin respuestas incompletas',
	noUnresolvedCommentsSummaryView: 'Sin comentarios sin resolver',
	edit: 'Editar',
	editForm: 'Editar formulario',
	editControl: 'Editar control',
	delete: 'Borrar',
	remove: 'eliminar',
	noBodies: 'No hay cuerpos disponibles',
	relateDocuments: 'Relacionar documentos',
	relatedDocuments: 'Documentos relacionados',
	deleteBody: 'Eliminar texto del cuerpo',
	bodyDescription: '¿Está seguro de que desea eliminar el cuerpo del texto seleccionado?',
	description: 'Descripción',
	maxLengthForEditResponse: 'El texto del cuerpo supera la longitud máxima permitida',
	maxLengthForEditResponseWithCount: 'La respuesta contiene {#} caracteres que superan el máximo de {##} caracteres. Ajuste la respuesta reduciendo el texto o el formato e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de Soporte Técnico.',
	saveResponse: 'Descartar los cambios no guardará las ediciones realizadas en el texto de respuesta. Los comentarios cerrados o eliminados permanecerán resaltados. Confirme si desea descartar todos los cambios.',
	discardChangesModalText: 'Descartar cambios',
	seeBodyDescriptionText: 'Ver la descripción',
	hideBodyDescriptionText: 'Ocultar descripción',
	showBodyDescriptionText: 'Mostrar descripción',
	okLabel: 'Ok',
	addButtonLabel: 'Añadir',
	addEvidence: 'Añadir evidencia',
	addTemporaryFiles: 'Adición de archivos temporales',
	notemporaryDocs: 'No hay archivos temporales disponibles',
	relateFiles: 'Relacionar archivos',
	uploadFiles: 'Subir archivos',
	cancelButtonLabel: 'cancelar',
	clickEditResponseToContinue: 'Haga clic en el icono de edición para continuar',
	editResponse: 'Editar respuesta',
	save: 'Guardar',
	numericValuePlaceholder: 'Ingrese el importe',
	saveLabel: 'Guardar',
	cancelLabel: 'cancelar',
	closeLabel: 'Cerrar',
	editText: 'Editar',
	select: 'Seleccionar',
	selectScot: 'Seleccionar SCOT',
	clearHoverText: 'Aclarar',
	optional: 'Opcional',
	nodocumentsAdded: 'No se tienen documentos disponibles',
	errorBanner: '{0} Error',
	NetworkErrorMessage: 'La red de la aplicación esta experimentando un error. Actualice la pagina e intente de nuevo. Contacte el Help desk si el problema persiste',
	of: 'de',
	characters: 'caracteres',
	show: 'mostrar: ',
	views: 'Vista: ',
	primaryRelated: 'Formularios primario relacionados con Canvas',
	secondaryRelated: 'Formularios segundarios relacionados con Canvas',
	singleLineValuePlaceholder: 'Introducir texto',
	paceInputPlaceholder: 'PACE ID',
	multiLineValuePlaceholder: 'Introducir texto',
	riskFactorDescribe: 'Describir',
	riskFactorLabel: 'Evento relevante y condición/riesgo de error',
	riskFactorEmptyWarning: 'Evento y condición / riesgo de error falta de descripción',
	riskFactorNoDescription: 'Crear un nuevo evento relevante o seleccionar el existente y la condición / riesgo de error',
	fraudRiskTagMessage: 'Este evento relevante y la condición/riesgo de incorrección siempre da lugar a un riesgo de fraude o, si corresponde, debe designarse como no un riesgo de incorrección material',
	significantRiskTagMessage: 'Este evento relevante y la condición/riesgo de incorrección siempre da lugar a un riesgo significativo, riesgo de fraude o, si corresponde, debe designarse como no un riesgo de incorrección material',
	on: 'en',
	sliderDeSelectMessage: 'Arrastre el círculo para asignar un valor',
	yearPlaceholder: 'AAAA',
	dayPlaceholder: 'DD',
	monthPlaceholder: 'MM',
	amLabel: 'AM',
	pmLabel: 'PM',
	relatedEntities: 'Entidades vinculadas',
	eyServiceGateway: 'EY Service Gateway',
	eyAutomation: 'EY Automation',
	eyserviceGatewayAutomation: 'EY Service Gateway & Automation',
	creating: 'Creating...',
	cannotCreateUdp: 'Cannot create UDP. Time Phase cannot be empty',

	// 440GL
	rrdReminderTitle: 'Recordatorio del Resumen de Revisión y Aprobación (RAS)',
	rrdReminderMessage: 'La fecha de publicación del informe es {rrdDescription}. Recuerde firmar el RAS {rrdPendingDays}.',
	rrdReminderPendingDays: 'en {0} día(s)',

	//Create or Associate risks
	createAssociateRisksLabel: 'Crear riesgos nuevos o asociados',
	relatedRiskIsMissingWarning: 'Falta el riesgo relacionado',
	associateRiskDescription: 'Seleccione uno o más riesgos, o cree un nuevo riesgo, para asociarlo a la respuesta a la pregunta.',
	createNewRiskLabel: 'Crear un nuevo riesgo',
	noRiskIdentifiedLabel: 'No se identificaron riesgos',

	// GuidanceModal
	eyAtlasLink: 'EY Atlas',
	guidanceHeaderMessage: 'Este módulo contiene',
	guidanceModalHeader: 'Orientación',
	guidanceModalLabelText: 'Entrada',
	guidanceFooter: 'para más información.',
	guidanceSeveralEntriesText: 'varias entradas: ',
	guidanceVisitText: 'Visitar',
	guidanceClickText: 'Clic',
	guidanceHereText: 'aquí',
	guidanceFooterText: 'para más información.',
	analyticsInconsistencies: 'Al revisar los análisis, considere si hay cambios o actividades que no sean consistentes con nuestras expectativas. Estas situaciones pueden ser indicativas de nuevos riesgos, clases separadas de transacciones, cambios en los SCOT o riesgos de anulación por parte de la administración.',
	analyticsInconsistenciesOSJE: 'OSJE representa la contabilidad de partida doble para la cuenta seleccionada que se está analizando. Buscamos emparejamientos de cuentas nuevos o inusuales.',
	analyticsInconsistenciesActivityBySource: 'Actividad por fuente representa la actividad mensual bruta y las fuentes relacionadas para la cuenta seleccionada. Nos centramos en la actividad inusual o en los cambios en las fuentes o en el volumen de actividad de una fuente.',
	analyticsInconsistenciesPreparerAnalysis: 'El análisis del preparador resume la actividad bruta de un período a otro registrada por los preparadores para la cuenta seleccionada. Nos centramos en los cambios en los preparadores o en la actividad de las cuentas fuera de su función.',
	analyticsInconsistenciesAccountMetrics: 'Las métricas de la cuenta resumen la información clave sobre la cuenta que ayuda en la designación de la cuenta.',
	analyticsLoadingIsInProgress: 'El análisis solicitado aún se está cargando. Se podrá acceder a la pestaña una vez que se complete.',

	aboutDescriptioin: 'Edite las capas, estándares o lenguaje de GAM. Las ediciones desencadenarán una actualización de contenido.',
	aboutContentDescription: 'Edit the content layers, standards, language, or content driving entity. Edits will trigger a content update.',
	about: 'Sobre',
	formType: 'Tipo de formulario',
	gamLayer: 'Capas GAM',
	contentLayer: 'Capas de contenido',
	standard: 'Norma',
	language: 'Idioma',
	createdBy: 'Creado por',
	createdOn: 'Creado en',
	contentLastUpdatedBy: 'Contenido actualizado por última vez por',
	contentLastUpdatedOn: 'Contenido actualizado por última vez',
	notModified: 'No modificado',

	rolesInsufficientTooltip: 'Roles insuficientes para editar contenido. Trabaje con un administrador del compromiso para obtener suficientes derechos.',
	knowledgeFormToolTip: "Knowledge delivered documents cannot be updated. Update the Engagement Profile to change this form's profile.",
	selectTeamMember: 'Nombre o correo electrónico',

	// SeeMore component
	showMore: 'Mostrar mas',
	showLess: 'Mostrar menos',
	showMoreEllipsis: 'Mostrar mas...',
	showLessEllipsis: 'Mostrar menos...',

	relatedITapplicationSOs: 'Aplicaciones de TI/SOs relacionadas',
	aggregateITevaluations: 'Agregar evaluaciones de IT',
	lowerRisk: 'Bajo riesgo',
	controlLowerRisk: 'El control es de bajo riesgo',
	relatedITApplication: 'Aplicaciones de TI relacionadas',
	relatedITSO: 'Relacionados Sos',
	noITApplicationUsed: 'No se utiliza ninguna aplicación de TI',

	notSel: 'No seleccionado',
	internal: 'Interno',
	external: 'Externo',
	notSelected: 'No seleccionado',
	noOptionSelected: 'No seleccionado',
	tod: 'TOD',
	sap: 'SAP',
	int: 'INT',
	ext: 'EXT',
	toc: 'TOC',

	placeholderForSearch: 'Buscar',
	source: 'fuente',
	nature: 'naturaleza',
	testOfDetail: 'prueba de detalle',
	testOfControl: 'prueba de control',
	substantiveAnalyticalProcedure: 'Procedimiento analitico sustantivo',
	expandScot: '1. Expandir el SCOT',
	selectWCGWToDisplayTheResponsiveTask: '2. Seleccionar el riesgo de error material para desplegar las tareas responsivas',
	tasksWithNoRelatedWCGW: 'Tareas con riesgo de error material no relacionado',
	noTasksAvailable: 'No se tienen tareas disponibles',
	noWCGWAvailableForTask: 'No ROMM disponible',
	noSubstantiveTasksAvailable: 'No tareas sustantivas relacionadas',
	selectAssertionToRelateWCGW: 'Seleccione una aseveraciones para relacionar el riesgo al WCGW',
	significantAccounts: 'Cuenta significativa',
	riskName: 'Riesgo',
	accountName: 'cuenta',
	control: 'Control',
	controls: 'Controles',
	noScotsFound: 'No Scot relacionado',
	relatedwcgw: 'Relacionar WCGWs',
	relatedRisks: 'Relacionar riesgos',
	boltIconTitle: 'Riesgo relacionado',
	relatedITApp: 'Aplicación de TI relacionada/SO',
	instructions: 'Instrucciones',
	expandRisk: '1. Expanda un riesgo',
	selectAssertion: '2. seleccione una aseveracion',
	identifyRelatedWCGW: '3. identifique que WCGW relaciona al riesgo',
	clickAccount: '1. click en la cuenta',
	selectWCGW: '2. seleccione un WCGW',
	identifyRelatedTask: '3.Identifique las tareas que responden al WCGW',
	information: 'informacion',
	requiredAssertions: 'aseveracion requerida',
	wcgwWithoutTasks: 'WCGW sin tarea',
	rommAssociatedWNotRelyAssertion: 'ROMM esta asociado a una aseveracion con un riesgo de control o No Probar o una aseveracion con riesgo inherente Alto, cuando la cuenta es de estimacion',
	hasRiskAssociated: 'Riesgo asociado',
	clearSelections: 'Deseleccionar todo',
	romm: 'ROMM',
	riskOfMaterialMisstatementsWithoutRelatedTask: 'Riesgo de error material sin tarea relacionada',
	selectOneOrMoreTasksToSeeTheRelatedROMM: 'Seleccione 1 o mas tareas para ver la relacion con el ROMM',
	invalidRelatedEntity: 'Cuenta relacionada no encontrada. Actualice la pagina e intente de nuevo. Contacte al Help Desk si el problema persiste',
	noResultsAvailable: 'No resltados encontrados',
	riskOfMaterialMisstatement: 'Riesgo de error material',
	AccountConclusion: 'Conclusion de la cuenta',
	CanvasForm: 'Forma de canvas',
	IndependenceForm: 'Forma de independencia',
	Profile: 'Profile',
	AccountDetails: 'Detalles',
	Conclusions: 'Conclusiones',
	accountDetailsTab: 'Detalles',
	conclusionsTab: 'Conclusions',
	formsNoContentText: 'Sin contenido disponible',
	formsDocumentNoRelatedObjects: 'Sin objetos relacionados con documentos',
	formsNoRelatedObjects: 'No hay objetos relacionados',
	formsBodyHeaderControl: 'Controles',
	formsBodyDesignEffectiveness: 'Eficacia del diseño',
	formsScotsAndWcgws: 'SCOTs & WCGWs',
	wcgWAndRelatedControls: 'WCGWs y controles relacionados',
	controlAndRelatedItSO: 'Controles y aplicaciones de TI / SO relacionadas',
	type: 'Tipo',
	designEffectiveness: 'Eficacia del diseño',
	approch: 'Enfoque',
	controlOpertaingEffectiveness: 'Eficacia operativa',
	iTAppSO: 'Aplicación de TI/SO',
	iTProcess: 'Proceso de TI',
	iTControl: 'Controles de IT',
	iTRisk: 'Riesgos de TI',
	aggregateITEvaluation: 'Evaluación agregada de TI',
	relatedCanvasForm: 'Formulario de lienzo relacionado',
	relatedSections: 'Secciones relacionadas',
	validations: 'Validaciones',
	profileV2Validation: 'Cambios no enviados',
	profileV2ValidationModalDescription: 'Se han realizado cambios que resultarian en una actualización de contenido, pero aún no se han enviado. Si los cambios son intencionales, cierre ests vista y envíe las nuevas respuestas de perfil. Si los cambios no son intencionales, revise la vista Cambios y revierta manualmente las respuestas a las seleccionadas anteriormente.',
	profileV2ValidationCount: '1',
	itProcessWithoutRelatedTechnology: 'IT process without related technology',
	reviewNote: 'Notas de revision',
	editAssociations: 'Editar asociaciones',
	editAssociationsLower: 'editar asociaciones',
	riskWCGW: 'Riesgo: relacion de WCGW',
	wcgwTask: 'WCGW: Relacion de tareas',
	noAssertionFound: 'No se ha asociado ninguna aseveración. Haga clic {here} para asociar aseveraciones',
	limitedRiskAccountIdentifier: 'Cuenta de riesgo limitado',
	insignificantAccountIdentifier: 'Cuenta insignificante',
	noWCGWFound: 'No hay WCGWs relacionados al riesgo. Click en editar asociaciones para relacionar uno o mas WCGWs',
	noRelatedWCGWs: 'No WCGWs relacionados',
	noWCGWAvailable: 'No ROMM disponible para las aseveraciones seleccionadas',
	expandCollapse: 'Click aquí para expandir / colapsar',
	requiredAssertionsInfo: 'Mostrar unicamente las aseveraciones con un riesgo de control o no confiar y aseseveraciones relacionadas a un riesgo mas alto o estimacion',
	wCGWwithoutTasksInfo: 'Mostrar unicamente WCGWs relacionados a una aseveracion con una evaluacion de riesgo de control de no confiar y aseveraciones relacionadas a estimaciones de riesgo mas alto, que no tienen ninguna tarea de respuesta asociada',
	noBuildStepsAvailable: 'No se tienen pasos diseñados para ser presentados',
	risk: 'Riesgos',
	wcgw: 'WCGW',
	riskWcgw: 'Riesgo: WCGW',
	wcgwTasks: 'WCGW: tarea',
	riskWcgwLabel: 'Relacionar riesgo a WCGW',
	wcgwTasksLabel: 'Relacionar WCGW a Tareas',
	noRiskTypes: 'No se encontraron tipos de riesgo',
	saveRisk: 'Guardar',
	noRisksFound: 'No se tienen riesgos encontrados',
	haveBeenIdentified: 'Ha sido identificado',
	noAccountsFound: 'No se encontraron registros',
	noResponseAvailable: 'No hay respuesta disponible',
	noDocumentsAvailable: 'No hay documentos disponibles',
	noValue: 'Sin valor',
	showValidation: 'validaciones',
	noAccountsIdentified: 'No se tienen cuentas identificadas',
	noAssertionsIdentified: 'No se tienen aseveraciones identificadas',
	noWcgwsIdentified: 'No WCGWs han sido identificados',
	pastingImagesNotAllowed: 'Pegar imágenes no esta permitido. Si las imágenes son requeridas a subir, subalas como evidencia y refieralas',
	incompleteResponse: 'Respuestas incompletas',
	unresolvedComments: 'Comentarios no resueltos',
	inconsistentForms: 'Formularios inconsistentes',
	limitedRiskAccount: 'Cuenta de riesgo limitado',
	inherentRiskAssessment: 'Evaluación de riesgos inherentes',
	task: 'Tarea',
	selected: 'Seleccionado',
	displaytoc: 'Desplegar TOC',
	workingoffline: 'Trabajando fuera de linea',
	syncinprogress: 'Sincronizacion en proceso',
	prepareoffline: 'Preparando los datos para fuera de linea',
	connectionavilable: 'Conexión disponible',
	softwareUpdate: 'Actualizaciones de software',
	updateLater: 'Actualizar más tarde',
	updateNow: 'Actualiza ahora',
	updateMsg: 'Las actualizaciones de software están disponibles para EY Canvas. Seleccione Actualizar ahora para descargar e instalar las actualizaciones. La página se actualizará.',
	searchPlaceholder: 'Búsqueda',
	filter: 'Filtro',
	leftNavSearchPlaceholder: 'Buscar encabezados y secciones',
	back: 'Atrás',
	updateAvailable: 'Actualización disponible',
	contentUpdateAvailableTooltip: "Actualización de contenido disponible. Haga clic aquí para navegar a la pantalla'Actualizaciones de formularios de Canvas' para iniciar una actualización. ",
	moreMenu: 'Mas menu',
	signoffPreparer: 'Firma como preparador',
	signoffReviewer: 'Firma como revisor',
	pagingShowtext: 'Mostrar',
	searchDocuments: 'Buscar documentos',
	noRelatedDocuments: 'Sin documentos relacionados.',
	noRelatedObjects: 'No hay objetos relacionados',
	documentName: 'Nombre del documento',
	formDetails: 'Detalles del formulario',
	questionsAndResponses: 'Preguntas y respuestas',
	details: 'detalles',
	trackChanges: 'Seguimiento de cambios',
	goToTrackChanges: 'Ir a Control de cambios',
	attributes: 'Atributos',
	relatedActions: 'Acciones relacionadas',
	createCustom: 'Crear personalizado',
	createCustomButtonLabel: 'Create custom header, section, or body',
	overwriteForm: 'Sobrescribir formulario',
	decimalNaN: 'NaN - No es un número',
	noRelatedObjectsApplicable: 'No es necesario que los objetos se asocien a este formulario de Canvas',
	objects: 'Objetos',
	objectName: 'Nombre del objeto',
	addCustomDescription: 'Seleccione el tipo de contenido que desea agregar a este formulario de Canvas, introduzca los detalles y haga clic en guardar',
	headerTitle: 'Título del encabezado',
	sectionTitle: 'Título de sección (obligatorio)',
	aresSectionTitle: 'Título de la sección',
	customLabel: 'Etiqueta personalizada (opcional)',
	customBodyDescription: 'Descripción del cuerpo del texto',
	header: 'Encabezado',
	section: 'Sección',
	body: 'Cuerpo de texto',
	requiredWCGW: 'Obligatorio',
	headerTitleRequired: 'Se requiere el título del encabezado.',
	bodyDescriptionRequired: 'Se requiere descripción del cuerpo.',
	bodySectionRequired: 'Se requiere sección.',
	bodyHeaderRequired: 'Encabezado es obligatorio.',
	sectionTitleRequired: 'Se requiere el título de la sección.',
	headerRequiredMessage: 'Encabezado es obligatorio.',
	enterDecimalAmount: 'Introducir cantidad decimal',
	enterPercentage: 'Introduzca el porcentaje',
	completeRiskFactorAssessment: 'Evaluación completa de eventos y condiciones identificados.',
	noScotsEstimatesIdentified: 'No se han identificado SCOTs ni estimaciones',
	// Track changes
	trackChangesResponseLabel: 'Respuesta de versión de seguimiento de cambios',
	trackChangesVersionLabel: 'Versión de seguimiento de cambios',
	noResponseIdentified: 'No se identificó ninguna respuesta',

	// Compare responses
	compareResponsesLabel: 'Comparar respuestas',
	compareResponsesTitle: 'Comparar respuestas de entidad',
	compareResponseNoDataPlaceholder: 'No hay datos disponibles, ya que el compromiso solo tiene un documento del mismo tipo.',
	labelFor: 'Para',
	questions: 'Preguntas',
	answers: 'Respuestas',
	countOfResponses: 'Recuento de respuestas',
	openNotes: 'Notas abiertas',
	clearedNotes: 'Notas aclaradas',
	click: 'Clic',
	clickToViewAnswer: 'Ver la respuesta',
	clickToViewQuestionAnswer: 'Ver la pregunta y la respuesta',
	selectDocuments: 'Seleccionar documentos',
	selectedDocumentsCount: '{0} documentos seleccionados',
	selectedDocumentCount: '{0} documento seleccionado',
	associatedDocuments: 'Documentos relacionados',
	noAnswerProvided: 'No se ha proporcionado respuesta',

	// Workspace Engagement
	thisEngagement: 'Este compromiso',
	documentLocation: 'Ubicación del documento',
	otherEngagementsInWorkspace: 'Otros compromisos en el espacio de trabajo',
	added: 'Añadido',
	documentIneligibleForSharingMessage: 'Los documentos confidenciales no son elegibles para ser compartidos.',
	fitDocumentCannotbeSelected: 'Los documentos FIT no se pueden compartir entre compromisos.',

	//Helix Configuration
	helixConfigurationTitle: 'Integre los datos de EY Helix',
	helixConfigurationPageDescription: 'Valide el proyecto EY Helix vinculado e importe los datos a EY Canvas. Si ha cambiado alguna configuración de EY Helix a continuación o ha realizado cambios en sus datos de EY Helix después de haber importado los datos, tendrá que volver a importar los datos para que se realice la actualización.',
	linkedEYHelixProjects: 'Proyectos vinculados de EY Helix: ',
	client: 'Cliente: ',
	engagement: 'Compromiso: ',
	analysisDate: 'Fecha de análisis: ',
	eyHelixProjects: 'Proyectos de Ey Helix',
	noPrimaryEYHelixproject: 'No se ha identificado ningún proyecto primario de EY Helix.',
	here: 'aquí',
	identifyEyHelixProjects: 'para identificar un proyecto EY Helix e iniciar el flujo de trabajo.',
	eyHelix: 'EY Helix',
	primary: 'Primary',
	helixSettingsDescription: 'Haga clic en Editar para seleccionar la configuración que se aplicará al cargar EY Helix Analyzers.',
	editButton: 'Editar',
	helixSettingsModalTitle: 'Ajustes de EY Helix',
	currencyType: 'Tipo de cambio',
	currencyTypeError: 'El tipo de cambio no se pudo recuperar de Ey Helix. Confirmar si los datos se configuraron correctamente en EY Helix e intente nuevamente.',
	shortNumberFormat: 'Formato de número corto',
	shortNumberFormatFooter: 'Redondeo para aplicarse a los valores numéricos que se muestran en tablas de EY Helix.',
	eyHelixAnalyzerFilterMetadataError: 'No se pudo conectar a Ey Helix. Actualiza la página y vuelve a intentarlo. Si el problema persiste, póngase en contacto con Mesa de Ayuda.',
	functional: 'Funcional',
	reporting: 'Informes',
	currencyCode: 'Código de moneda',
	businessUnit: 'Unidad de negocio',
	roundingNumberFormat: 'Formato de número de redondeo',
	eyHelixProjectChangedLine1: 'Se ha cambiado el proyecto de EY Helix vinculado desde la última vez que se guardó la configuración de EY Helix.',
	eyHelixProjectChangedLine2: 'Haga clic en Editar para actualizar la configuración antes de que los datos puedan importarse o re-importarse desde EY Helix.',
	helixSettingsTimeline: 'Ajustes de EY Helix',
	helixMapEntitiesToBU: 'Asignar entidades a unidades de negocio',
	helixNumberOfMapEntities: 'Número de Unidades de Negocio mapeadas',
	importEYHelixDataTimeline: 'Importar datos de EY Helix',
	mapAccountsHelixTimeline: 'Mapa de Cuentas',
	setEYHelixSettings: 'Edite la configuración de EY Helix a continuación. Una vez guardados y importados los datos, las fechas de la fecha comparativa 1 y la fecha comparativa 2 seleccionadas se utilizarán como comparaciones con la fecha de análisis designada en la actividad OAR. Para comparar con una sola fecha comparativa, seleccione "Ninguna" en la selección comparativa de 2 fechas.',
	eyHelixDataHasChangedLine1: 'Los datos han cambiado desde que se guardó la última configuración de la hora. Haz nuevas selecciones a continuación y haz click',
	eyHelixDataHasChangedLine2: 'Para actualizar la configuración de EY Helix.',
	all: 'Todos',
	multiple: 'Múltiple',
	notApplicableAbbreviation: 'N / A',
	importEyHelixData: 'Importar datos de Helix EY',
	editScreenshot: 'Editar',
	deleteNote: 'Borrar',
	removeAnnotation: 'Quitar anotación',
	addAnnotation: 'Agregar anotación',
	addingBoundingBox: 'Seleccione un área en la captura de pantalla para anotar, confirme la anotación y haga clic en la marca de verificación para guardar.',
	cancelBoundingBox: 'Cancelar',
	deleteScreenshot: 'Eliminar captura de pantalla',
	openInFullscreen: 'Abrir en pantalla completa',
	helixURLErrorMessage1: 'La configuración del proyecto EY Helix asignado está incompleta.',
	helixURLErrorMessage2: 'Vaya a {0} página para actualizar.',
	helixIsNotEnabledMessage: 'EY Helix no está habilitado para su compromiso',
	helixSetup: 'Configuración de EY Helix',
	openAnalysisInHelix: 'Análisis abierto en EY Helix.',
	helixInvaliddate: 'Fecha no válida. Seleccione una fecha anterior a la fecha de análisis.',
	helixcomparativedateoptional: 'Fecha comparativa 2 de EY Helix (opcional)',
	helixpriorperioddate: 'Fecha comparativa de EY Helix 1',
	helixanalysisperiod: 'Fecha de análisis de EY Helix',
	helixfiscalDropDownLabel: 'Período {0} - {1}',

	helixSettingsEditButtonTitle: 'Editar',
	helixImportDataEditButtonTitle: 'Importar',
	helixImportInsufficientPermissionsMessage: 'Permisos insuficientes para realizar la importación de datos de Helix. Comuníquese con el administrador de su compromiso y solicite permiso para iniciar una importación de datos de Helix.',
	helixImportNotAllowed: 'El perfil de participación no permite la importación de datos de Helix',
	helixDeleteImportInsufficientPermissionsMessage: 'Permisos insuficientes para eliminar la importación de datos de EY Helix. Póngase en contacto con el administrador del compromiso y solicite permiso para eliminar los datos de EY Helix.',

	EYAccountMappingStepLabel: 'Gestionar el mapeo de cuentas',

	EYAccountMappingOptional: 'Opcional',
	EYAccountMappingStepTitleSettingsCompleted: 'Realizar la asignación de cuentas de EY',
	EYAccountMappingStepTitleSettingsIncomplete: 'Complete la configuración de EY Helix para acceder al módulo de mapeo de EY Helix',
	EYAccountMappingInstructions: 'Asigne cuentas de clientes a cuentas de EY y procesa cambios. Una vez finalizado el procesamiento, importe los datos que aparecen a continuación.',
	manageAccountMappingButtonLabel: 'Administrar la asignación de cuentas',

	//EY Helix Setup Card
	EYHelixSetupTitle: 'EY Helix',
	EYHelixSetupSubTitle: 'Configurar e importar datos a EY Canvas',
	LastImported: 'última importación',
	EYHelixSettings: 'Conexiones EY Helix',
	NoEYHelixProjectLinkedLabel1: 'Este compromiso no está vinculado a un proyecto primario de EY Helix. Por favor visite el',
	NoEYHelixProjectLinkedLabel2: 'página para establecer el enlace.',
	NoEYHelixProjectLinkedHperlink: 'Proyectos de EY Helix',
	NoDataImportedLabel: 'Los datos no han sido importados de EY Helix. Haga clic en Configuración de EY Helix para iniciar el proceso.',
	noHelixConnections: "Haga clic en \'EY Helix connections\' para crear una conexión. ",
	helixConnectionExists: 'Existe conexión',
	helixConnectionsExist: 'Existen conexiones',
	helixTrialBalanceImported: 'Alcance y estrategia de balance de comprobación importado',
	helixTrialBalancesImported: 'Alcance y estrategia de balances de comprobación importados',
	helixNoTrialBalanceImported: "Haga clic en \'EY Helix connections\' para importar un balance de prueba de alcance y estrategia. ",

	// EY Helix - Map Accounts
	mapAccountsHelixCanvas: 'Haga clic en Editar para asignar cuentas de EY Canvas a cuentas importadas desde EY Helix',
	mapAccountsHelixCanvasSubtitle: 'Arrastre y suelte cada cuenta de EY Helix para asignarla a una cuenta de EY Canvas. Haga clic en Administrar cuentas para crear o editar cuentas de EY Canvas.',
	mapAccountsHelixHeaderLabel: 'Cuentas de EY Helix',
	mapAccountsCanvasHeaderLabel: 'Cuentas de EY Canvas',
	mapAccountsConnectedLabel: 'Cuentas conectadas',
	mapAccountsShowMappedLabel: 'Mostrar cuentas asignadas',
	mapAccountsHideMappedLabel: 'Ocultar cuentas asignadas',
	mapAccountsManageLabel: 'Administrar cuentas',
	mapAccountsAndDisclosuresManageLabel: 'Gestionar cuentas y divulgaciones',
	mapAccountsNoCanvasAccounts: 'No se han identificado cuentas.',
	mapAccountsNoCanvasAccountsClick: 'Clic',
	mapAccountsNoCanvasAccountGetStarted: ' para empezar.',
	mapAccountsRemoveAccount: 'Eliminar',
	mapAccountsReImportHelixAccounts: 'La importación de datos de EY Helix no tuvo éxito. Vuelva a importar los datos e intente de nuevo.',
	mapAccountsReImportHelixAccountsHelpDesk: 'Si el problema persiste, póngase en contacto con mesa de ayuda.',
	mapAccountsNoHelixAccountHasBeenImported: 'No se han importado cuentas de EY Helix.',
	mapAccountsNoHelixAccountHasBeenImportedCheckData: 'Revise los datos en EY Helix y vuelva a intentarlo.',

	//Helix Analyzer
	accountNotRelatedToDocumentOnPhaseTwo: 'Ninguna cuenta relacionada con este documento',

	//PM TE SAD Widget
	materialityWidgetLabel: 'PM/TE/SAD',

	// TE labels
	planningmateriality: 'Materialidad de Planificación',
	requiredTePercentage: 'ET requerido',
	suggestedtepercentage: 'ET sugerido',
	currentperiodte: 'ET Período actual',
	priorperiodte: 'ET Período anterior',
	pmPriorPeriod: 'MP del Período anterior',
	tepercentage: 'ET Porcentual',
	teamount: 'ET Importe',
	teLabel: 'Error tolerable',
	sugestedSAD: 'Porcentaje de SAD sugerido: ',
	priorSAD: 'SAD Periodo previo',
	currentPeriodSAD: 'Período actual SAD',
	sadPercentage: 'SAD Porcentual',
	sadAmount: 'SAD Importe',
	rationaleLabel: 'Fundamento',
	suggestedTEPercentageInfo: 'Si se selecciona un porcentaje diferente en función de factores específicos de compromiso, se le pedirá que documente esos factores a continuación.',
	rationaleTEDescription: 'Introduzca la justificación de ET como porcentaje de PM teniendo en cuenta los atributos seleccionados anteriormente.',
	teAmmountInvalid: 'Introduzca un importe válido o seleccione 50% o 75%',
	highRiskTEAmmountInvalid: 'Introduce un importe válido o selecciona 50%',
	highRiskTERequired: 'Sobre la base de las respuestas a las consideraciones anteriores, el porcentaje de ET identificado es necesario y no se puede cambiar.',

	// EY Helix Map Entities to Business Units Modal
	mapEntitiesModalTitle: 'Gestionar entidades',
	mapEntitiesModalLeyendDescription: 'Gestiona la asociación entre entidades EY Canvas y unidades de negocio a partir del proyecto EY Helix a continuación.',
	mapEntitiesModalLeyendNote: 'Nota: Una Unidad de Negocio puede estar asociada a una o varias Entidades de EY Canvas. Una vez guardados e importados los datos, los datos asociados a las Unidades de Negocio en EY Helix se mostrarán como asignados a las Entidades de EY Canvas relacionadas.',
	mapEntitiesModalEntityCodeLabel: 'Código de la entidad',
	mapEntitiesModalEmptyEntitiesList: 'No se ha creado ninguna entidad.',
	mapEntitiesRelatedBusinessUnitDropdownPlaceholder: 'Unidad de negocio relacionada',
	mapEntitiesSelectedBusinessUnitsCount: '{0} unidades de negocio seleccionadas',

	//AdjustedBasis
	enterAmount: 'Introduzca el monto',
	basisAmount: 'Cantidad base',
	lowEndOfRange: 'Extremo bajo del rango',
	highEndOfRange: 'Extremo alto del rango',
	suggestedRange: 'Sobre la base de los factores anteriores, el uso de un porcentaje hacia la {0} del rango {1} a {2} puede ser apropiado. Si se selecciona una cantidad fuera de este rango en función de factores específicos del compromiso, se le pedirá que documente esos factores a continuación.',
	suggestedRangeLowPMBracket: 'Según los factores anteriores, un porcentaje de {0} puede ser apropiado. Si se selecciona una cantidad fuera de este porcentaje en función de factores específicos del compromiso, se le pedirá que documente esos factores.',
	middle: 'Media',
	lowerEnd: 'extremo inferior',
	higherEnd: 'extremo superior',
	lowEnd: 'Gama baja',
	priorPeriodPm: 'PM Periodo previo',
	suggestedRangeSummary: 'Rango sugerido',
	loadingMateriality: 'Cargando materialidad ...',
	pmBasisPercentage: 'Porcentaje de base PM',
	pmAmount: 'Importe de PM',
	currentPmAmount: 'Importe de MP del período actual',
	pmAmountPlaceholder: 'Ingrese la cantidad de MP',
	currentPeriodPm: 'PM del Período actual: ',
	enterRationale: 'Introduzca la justificación',
	rationaleDescription: 'Introduzca la justificación del porcentaje de base de PM teniendo en cuenta los atributos seleccionados anteriormente',
	pmValidationMessage: 'La materialidad de planificación no puede exceder el extremo superior del rango',
	sadValidationMessage: 'El monto nominal no puede exceder el extremo superior del rango.',
	sadRationaleDiscription: 'Ingrese la eacional del porcentaje SAD en consideración de los atributos seleccionados anteriormente',
	nopriorperiodDocs: 'No hay documentos de períodos anteriores disponibles',
	addPriorPeriodEvidence: 'Agregar evidencia del período anterior',
	addToEvidenceLabel: 'Agregar a la evidencia',
	moveToEvidenceLabel: 'Mover a evidencia',
	addToEvidenceModalDescription: 'Cree un nuevo nombre o mantenga el nombre existente para el documento seleccionado.',
	GoToSource: 'Ir a la fuente',
	//ITRiskITControls
	createNewITGC: 'Nuevo ITGC',
	relateITGC: 'Relate ITGCs',
	createNewITSP: 'Nuevo ITSP',
	relateITSP: 'Relacionar ITSP',
	noITGC: 'sin ITGC',
	itRiskForITGCITSP: 'Nombre del riesgo de TI (obligatorio)',
	createITGCModalDescription: "Ingrese los detalles de ITGC a continuación y seleccione'<b>{0}</b>' para finalizar. Para crear otro ITGC, seleccione'<b>{1}</b>'. ",
	createITSPModalDescription: "Ingrese los detalles de ITSP a continuación y seleccione'<b>{0}</b>' para finalizar. Para crear otro ITSP, seleccione'<b>{1}</b>'. ",
	controlDesignEffectiveness: {
		[0]: {
			description: 'No seleccionado'
		},
		[1]: {
			description: 'Eficaz'
		},
		[2]: {
			description: 'Ineficaz'
		}
	},
	controlOperationEffectiveness: {
		[0]: {
			description: 'No seleccionado'
		},
		[1]: {
			description: 'Eficaz'
		},
		[2]: {
			description: 'Ineficaz'
		}
	},
	controlTesting: {
		[0]: {
			description: 'No seleccionado'
		},
		[1]: {
			description: 'Sí'
		},
		[2]: {
			description: 'No'
		}
	},
	itAppTypes: {
		[0]: {
			label: 'Aplicación IT'
		},
		[1]: {
			label: 'SO'
		}
	},
	controlType: {
		[0]: {
			controlTypeName: '',
			shortName: 'No seleccionado'
		},
		[1]: {
			controlTypeName: 'Control de aplicaciones de TI',
			shortName: 'Aplicación'
		},
		[2]: {
			controlTypeName: 'Control manual dependiente de TI',
			shortName: 'ITDM'
		},
		[3]: {
			controlTypeName: 'Prevención manual',
			shortName: 'Prevención manual'
		},
		[4]: {
			controlTypeName: 'Detección manual',
			shortName: 'Detección manual'
		}
	},
	controlTypeEnumLabel: {
		[0]: {
			controlTypeName: 'No seleccionado'
		},
		[1]: {
			controlTypeName: 'Control de aplicaciones de TI'
		},
		[2]: {
			controlTypeName: 'Control manual dependiente de TI'
		},
		[3]: {
			controlTypeName: 'Manual preventivo'
		},
		[4]: {
			controlTypeName: 'Manual detectivo'
		}
	},
	controlFrequencyType: {
		[0]: {
			controlFrequencyTypeName: 'No seleccionado'
		},
		[1]: {
			controlFrequencyTypeName: 'Muchas veces al día'
		},
		[2]: {
			controlFrequencyTypeName: 'Diario'
		},
		[3]: {
			controlFrequencyTypeName: 'Semanal'
		},
		[4]: {
			controlFrequencyTypeName: 'Mensual'
		},
		[5]: {
			controlFrequencyTypeName: 'Trimestral'
		},
		[6]: {
			controlFrequencyTypeName: 'Anualmente'
		},
		[7]: {
			controlFrequencyTypeName: 'Control de aplicaciones de TI'
		},
		[8]: {
			controlFrequencyTypeName: 'Otro'
		}
	},
	strategyType: {
		[0]: {
			strategyTypeName: 'No seleccionado'
		},
		[1]: {
			strategyTypeName: 'Controles'
		},
		[2]: {
			strategyTypeName: 'sustantivo'
		},
		[3]: {
			strategyTypeName: 'confiar'
		},
		[4]: {
			strategyTypeName: 'No confiar'
		}
	},
	aggregateITEvaluationType: {
		[0]: {
			aggregateITEvaluationTypeName: 'No seleccionado'
		},
		[1]: {
			aggregateITEvaluationTypeName: 'Aoporte'
		},
		[2]: {
			aggregateITEvaluationTypeName: 'Sin soporte'
		},
		[3]: {
			aggregateITEvaluationTypeName: 'Soporte de FS & ICFR'
		},
		[4]: {
			aggregateITEvaluationTypeName: 'Solo soporte de FS'
		}
	},

	sampleItemFilterLabels: {
		filterTypeOfTags: 'Etiquetas',
		noFiltersAvailable: 'No hay filtros disponibles',
		filterToolTip: 'Filtro',
		clearAll: 'Limpiar todo',
		showMore: 'más',
		filters: 'Filtros',
		noResults: 'No se han encontrado resultados'
	},

	stratergyTypeLabels: {
		[0]: {
			label: 'No seleccionado'
		},
		[1]: {
			label: 'En alcance'
		},
		[2]: {
			label: 'Fuera de alcance'
		}
	},
	noChangeReasonCommentAvailable: 'Haga clic en Editar motivo en las opciones de la rueda dentada del documento para introducir el motivo del cambio.',
	changeReasonModalTitle: 'Editar razón de cambio',
	changeReasonModalText: 'Seleccione el motivo de los cambios realizados en el documento después de la fecha del informe. Si se han realizado varios cambios administrativos, seleccione el cambio más significativo de las siguientes opciones. Si se han realizado cambios administrativos y no administrativos, seleccione la opción no administrativa a continuación.',
	changeReasonUploadModalTitle: 'La razón para enviar documentos',
	changeReasonUploadModalText: 'Seleccione el motivo de los cambios realizados en el documento después de la fecha del informe. Si se han realizado varios cambios administrativos, seleccione el cambio más significativo de las siguientes opciones. Si se han realizado cambios administrativos y no administrativos, seleccione la opción no administrativa a continuación.',
	changeReasonModalComboPlaceholder: 'Seleccionar',
	changeReasonModalAnnotationText: 'Documentar las circunstancias encontradas y las razones para agregar la información; procedimientos de auditoría nuevos o adicionales, pruebas de auditoría obtenidas y conclusiones alcanzadas, y el efecto en nuestro informe de auditoría.',
	changeReasonUploadModalAnnotationText: 'Documentar las circunstancias encontradas y las razones para agregar la información; procedimientos de auditoría nuevos o adicionales, pruebas de auditoría obtenidas y conclusiones alcanzadas, y el efecto en nuestro informe de auditoría.',
	changeReasonModalAnnotationPlaceHolder: 'Introduzca el motivo del cambio',
	changeReasonModalChangeReasonRequired: 'Cambiar motivo para guardar',
	reasonColumnTitle: 'Motivo',
	shared: 'Compartido',
	shareStatusOwned: 'Propiedad de este compromiso.',
	shareStatusShared: 'Compartido dentro de este compromiso.',
	lastModifiedBy: 'Ultima modificación por',
	fileSize: ' | {1} KB',
	openedLabelText: 'Abierto',
	currentlyBeingModifiedBy: 'Actualmente se está modificando por',
	OpenGuidedWorkflowDocument: 'Abra este documento a través de la habilitación EY Canvas FIT',
	submitProfile: 'Enviar perfil',
	submitProfileFit: 'Enviar perfil',
	contentUpdateUnAuthorizedTooltipMessage: 'Permisos insuficientes para realizar la actualización de contenido. Póngase en contacto con el administrador del compromiso y solicite permiso para iniciar una actualización de contenido.',
	submitProfileValidationErrorMessage: 'El perfil solo se puede enviar cuando se hayan respondido todas las preguntas. Filtre las preguntas incompletas, completelas y envíelas de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	pickADate: 'Seleccione una fecha',

	/* Sign Offs */
	preparerHoverText: 'Firma como preparador',
	reviewerHoverText: 'Firma como revisor',
	preparerTitle: 'Firma del preparador',
	reviewerTitle: 'Firma del revisor',
	deleteHoverText: 'Eliminar la firma',
	preparer: 'Preparador',
	reviewer: 'Revisor',
	preparerLabel: 'P',
	reviewerLabel: 'R',
	noSignOffsAvailable: 'No hay firmas disponibles',
	none: 'Ninguna',
	partnerInChargeLabel: 'PIC',
	eqrLabel: 'EQR',
	documentSignoffRequiredLabel: 'Firmas requeridas de: ',

	relatedDocumentsTitle: 'Documentos relacionados',
	relatedTasksCount: '{0} Tareas relacionadas',
	relatedTasksTitle: 'Tareas relacionadas',
	relateTemporaryFiles: 'Relacionar archivos temporales',
	bodyRelatedDocumentsTitle: 'Documentos asociados a un cuerpo',
	relatedObjectsTitle: 'Objetos relacionados',
	relateDocumentsTitle: 'Gestionar documentos relacionados',
	relateDocumentsToBodyTitle: 'Añadir evidencia',
	relateDocumentsDesc: 'Seleccionar documentos que se asociarán al formulario de canvas',
	relateDocumentsToBodyDesc: 'Relacionar un documento de este compromiso o de otro compromiso en esta área de trabajo',
	relateDocumentsToTheBody: 'Relate a document from this engagement.',
	priorPeriodEvidencesToTheBody: 'Evidencias del período anterior relacionadas con el cuerpo',
	relatedDocunentEngdisabed: 'El documento no se comparte con este compromiso',
	showOnlyRelatedDocuments: 'Mostrar sólo documentos relacionados',
	manageDocuments: 'Gestionar documentos',
	documentCount: 'documento {0}',
	documentsCount: 'documentos {0}',
	relateDocumentsSearchPlaceholder: 'Buscar documentos',
	overwriteFormDesc: 'Seleccione un formulario para sobrescribir las respuestas con los datos del formulario de Canvas actual. Tenga en cuenta que el formulario de Canvas actual se moverá a Archivos temporales.',
	searchFormPlaceholder: 'Formulario de búsqueda',
	overwriteLabel: 'Sobrescribir',
	confirmOverwriteLabel: 'Confirmar sobrescritura',
	confirmOverwriteDesc: "¿Está seguro de que desea copiar el contenido del formulario'{0}' al formulario'{1}'? Las respuestas se sobrescribirán en el formulario'{2}', pero las pruebas y los objetos relacionados no lo harán. Las pruebas y los objetos relacionados aplicables deben volver a asociarse al formulario'{3}' una vez que se haya completado la sobrescritura. El formulario'{4}' conservará sus capas de perfiles /GAM existentes, por lo tanto, si ese formulario de perfiles es diferente del formulario'{5}', revise y limpie el contenido copiado. \n No cierre el explorador ni se aleje mientras el proceso de sobrescritura está en curso. Una vez que se haya completado el proceso de sobrescritura, el formulario'{6}' se moverá a Archivos temporales y se le navegará al formulario'{7}'. Esta acción no se puede deshacer. ",
	formSelectionRequired: 'Seleccione un formulario para anularlo.',

	open: 'Abierto',
	startCoeditMode: 'Iniciar la edición multiusuario',
	endCoeditMode: 'La edición multiusuario está finalizando',
	openReadOnly: 'Abrir solo lectura',
	copyLink: 'Copiar enlace',
	rename: 'Renombrar',
	viewHistory: 'Ver historial',
	documentOpenModelLabel: 'Documento que se está modificando',
	modelUserOpenedTheDocumentText: 'Este usuario abrió el documento',
	modelDocumentOpenedText: 'Este documento está siendo modificado por',
	modelOpenedDocumentConflictText: 'Abrir el documento podría causar conflictos, por lo que recomendamos abrir como de solo lectura. Si desea convertirse en el editor de este documento, entonces',
	clickHereEnabledText: 'haga clic aquí.',
	documentOptions: 'Opciones de documento',
	accountDetails: 'Detalles de la cuenta',

	// DAAS labels
	coEditModeIsEnding: 'La edición multiusuario está finalizando',
	coEditMode: 'Edición multiusuario',
	checkInInProgressMessage: 'Check-in en curso. El documento puede tardar hasta 20 minutos en registrarse. Actualice la pagina para obtener todas las actualizaciones',
	checkInInErrorLabel: 'Error en el Check-in',
	checkOutInProgressMessage: 'Check-out en curso. El documento puede tardar hasta 20 minutos en ser retirado. Actualice para obtener actualizaciones',
	checkOutInProgressLabel: 'Check-out en curso.',
	checkInInProgressLabel: 'Check-in en curso',
	checkOutInErrorLabel: 'Error en el Check-out',
	daasErrorMessage: 'La operación no se puede completar en este momento. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con Servicio de Soporte',
	coEditModeIsStarting: 'La edición multiusuario se está iniciando',
	daasOpenDocumentWarning: 'Multi-user editing may have been ended by another user. Refresh the page and try again.',
	beingEditedInCoeditMode: 'Being edited in multi-user edit. Edit started by {0}',
	beingEditedInCoeditModeOn: 'on {0}.',
	beingEditedInCoeditModeError: 'Being edited in multi-user edit',
	coEditModeAutomaticallyEnds: 'El documento está en modo de edición multiusuario, que finalizará automáticamente en {0} días.',
	coEditModeAutomaticallyEndsToday: 'El documento está en modo de edición multiusuario, que finalizará hoy.',
	daasStartCollaborationModeWarning: 'Collaboration mode may have been started by another user. Refresh the page and try again.',
	documentCurrentlyBeingModifiedTitle: 'Document currently being modified',
	documentCurrentlyBeingModifiedHeader: 'This document is currently being modified by {0}. This user opened the document',
	documentCurrentlyBeingModifiedBody: 'Starting multi-user edit mode could cause conflicts, so we recommend discussing with {0} before proceeding. Select {1} to start the multi-user edit mode or {2} to return without starting multi-user edit mode.',
	documentEndMultiUserEditingTitle: 'Finalizando edicion multiusuario',
	documentEndMultiUserEditingHeader: 'Warning: Other users may be actively editing this document.  This can be checked by opening the document and seeing if other users are currently in the file. Please confirm that all changes are complete before ending multi-user mode. File changes in multi-user mode may take up to 1 minute to be processed. Therefore, please wait at least 1 minute after exiting the file before ending multi-user mode.',
	documentEndMultiUserEditingBody: 'Select {0} to end the multi-user edit mode or {1} to return without ending multi-user edit mode.',
	startMultiuserEditing: 'Start',

	/* Engagement Comments */
	clear: 'Aclarar',
	close: 'Cerrar',
	reOpen: 'Re-abrir',
	reply: 'Agregar respuesta',
	replyLabel: 'Respuesta',
	unselectComment: 'deseleccionar comentarios',
	commentText: 'Ingrese texto',
	replyText: 'Texto de respuesta',
	openStatus: 'Abierto',
	clearedStatus: 'Aclarado',
	closedStatus: 'Cerrado',
	chartCommentsTitle: 'Notas de revisión',
	showComments: 'Mostrar comentarios',
	noRecordsFound: 'No se han encontrado registros',
	noCommentsFound: 'Deje un comentario utilizando las entradas a continuación. Asigne el comentario a un usuario y especifique la prioridad y la fecha de vencimiento.',
	newComment: 'Agregar comentario',
	addNoteTitle: 'Añadir nota',
	editComment: 'Editar comentario',
	newReply: 'Añadir respuesta',
	editReply: 'Editar respuesta',
	commentTextRequired: 'Texto de comentario obligatorio',
	replyTextRequired: 'Se requiere texto de respuesta',
	myComments: 'Mis comentarios',
	assignTo: 'Asignar a',
	theCommentMustBeAssigned: 'Asignado como requerido',
	priorityRequired: 'Prioridad requerida',
	dueDateRequired: 'Fecha de vencimiento requerida',
	assignedTo: 'Atribuído a',
	allComments: 'Todos los comentarios',
	assignedToMe: 'Asignado a mí',
	unassigned: 'Sin firmar',
	draggableCommentsPlaceholder: 'Introduzca texto para agregar un nuevo comentario',
	draggableNotesPlaceholder: 'Introducir texto para añadir una nueva nota',
	enterReply: 'Entrar en respuesta',
	dueDate: 'Fecha de vencimiento',
	commentsAmmount: '{count} comentarios',
	singleCommentAmmount: '{count} comentario',
	eyInternal: 'EY',
	noneAvailable: 'No disponible',

	navHelixProjects: 'Conexiones EY Helix',

	evidence: 'Evidencia',
	priorPeriod: 'Período anterior',
	temporaryFiles: 'Archivos temporales',
	priorPeriodEvidence: 'Periodo anterior',
	closed: 'Todas las asignaciones cerradas',

	/*Delete*/
	deleteFileTitle: 'Eliminar documento',
	deleteFileCloseBtnTitle: 'Cancelar',
	deleteFileConfirmBtnTitle: 'Eliminar',
	deleteFileCloseTitle: 'Cerca',
	deleteFileModalMessage: '¿Está seguro de que desea eliminar el documento seleccionado?',
	/*Rename*/
	editCanvasformObjects: 'Edite los atributos y haga clic en <b>Guardar</b>.',
	renameFileModalMessage: 'Cambie el nombre del documento y haga clic en Guardar.',
	renameScreenshotModalMessage: 'Cambie el nombre de la captura de pantalla y haga clic en Confirmar.',

	renameFileTitle: 'Cambiar el nombre del documento',
	fileNameRequired: 'Se requiere el nombre de archivo',
	invalidCharacters: 'El nombre de archivo no puede incluir: */:<>\\?|"',
	existingFileName: 'El nombre de archivo no es único. Actualice la página o cambie el nombre del archivo para quitar este mensaje.',
	maxLengthExceeded: 'El nombre del documento no puede superar los 115 caracteres.',

	STEntityProfileBannerMessage: 'Se han realizado cambios en uno o varios de los perfiles de entidad que darán lugar a actualizaciones de contenido. Vuelva a la página Entidades de perfil y haga clic en "Importar contenido" para recibir el nuevo contenido aplicable al perfil de la entidad o revertir las respuestas a un estado anterior.',
	independenceValidationForOwnForm: 'Se han realizado cambios en las respuestas de Independence, pero no se han enviados. Si los cambios son intencionales, asegúrese de enviar las respuestas. Si los cambios no son intencionales, revise la vista Cambios y revierta manualmente las respuestas a las selecciones anteriores.',
	independenceValidationForOthersForm: 'Se han realizado cambios en las respuestas de la Independencia, pero no han sido enviados por el miembro del equipo. Asegúrese de que el miembro del equipo revise los cambios y envíe los mismos si fueron intencionales.',
	insufficientRightsForIndependenceSubmission: 'Permisos insuficientes para editar contenido. Póngase en contacto con el administrador del compromiso y solicite permiso para editar contenido.',
	submitIndependenceProfileV2Message: 'Revise el perfil y confirme que las respuestas son precisas. Si es así, proporcione las firmas y continúe con el compromiso.',
	submitIndependenceProfileV2EditMessage: 'No se han realizado cambios en el perfil que den lugar a cambios en el contenido de interacción. Use la página de actualización de contenido de Engagement para realizar una actualización de contenido si es necesario.',
	insufficientRightsForProfileV2Submission: 'Permisos insuficientes para editar el perfil. Póngase en contacto con su administrador y solicite permiso para editar el perfil.',
	returnToDashboard: 'Volver al panel',
	returnToDashboardFit: 'Volver al panel de control',
	profileV2ChangeNotSubmittedBannerMessage: 'Changes have been made to the Profile that will result in content updates. Submit the Profile to receive the new content or revert the answers to the previous state.',
	independenceChangeNotSubmittedBannerMessage: 'Se han introducido cambios en el formulario independentista que deben volver a enviarse. Envíe el formulario de independencia o desactive este usuario para borrar la validación.',
	multiEntityIndividualProfileBannerMessage: 'Permisos insuficientes para editar el perfil. Póngase en contacto con el administrador del compromiso y solicite permiso para editar el perfil.',
	scotStrategy: 'Estrategia DE SCOT',
	wcgwStrategy: 'Estrategia de WCGW',
	itProcessStrategy: 'Estrategia de procesos de TI',

	/*Edit Wcgw*/
	editWcgw: 'Editar WCGW',
	viewWcgw: 'Ver WCGW',
	editScot: 'Editar SCOT',
	viewScot: 'Ver SCOT',
	showIncomplete: 'Mostrar incompleto',
	forms: 'Formularios',
	form: 'Forma',
	comments: 'Notas',
	changes: 'Cambios',
	editHeader: 'Editar encabezado',
	editSection: 'Editar sección',
	editBody: 'Editar cuerpo',
	editSectionDescription: "Edite los detalles de la sección y haga clic en'Guardar'. ",
	editHeaderDescription: "Edite los detalles del encabezado y haga clic en'Guardar'. ",
	editBodyDescription: "Edite los detalles del cuerpo y haga clic en'Guardar'. ",
	manageObject: 'Administrar objeto',
	relatedObjects: 'Objetos relacionados',

	/* Manage body objects */
	bro_manage_WCGWTask_title: 'Relacionar los WCGW',
	bro_manage_WCGWTask_instructions: 'Gestione los WCWW que son aplicables',
	bro_manage_WCGWTask_noDataLabel: 'No se han encontrado resultados',

	/*Add/Edit ITGC*/
	addITGC: 'Añadir ITGC',
	addNewITGC: 'Agregar nuevo ITGC',
	addExistingITGC: 'Agregar ITGC existente',
	addITGCDescription: 'Introduzca la descripción del ITGC.',
	itControlNameRequired: 'Se requiere el nombre ITGC',
	frequencyRequired: 'Se requiere frecuencia',
	frequencyITGC: 'Seleccionar frecuencia',
	nameITGC: 'Nombre ITGC (requerido)',
	iTProcesslabel: 'Proceso de TI',
	editITGC: 'Editar ITGC',
	editITSP: 'Editar ITSP',
	editITGCDescription: 'Editar el ITGC y sus atributos asociados',
	editITSPDescription: 'Editar el ITSP y sus atributos asociados',
	viewITGC: 'Ver ITGC',
	viewITSP: 'Ver ITSP',
	itgcTaskDescription: 'Realizar nuestras pruebas diseñadas de ITGC para obtener suficiente evidencia de auditoría apropiada de su efectividad operativa durante todo el período de confianza.',
	/**
	 * Add Edit ITGC
	 */
	addITSPDescription: 'Introduzca la descripción del ITSP.',
	selectITRisk: 'Seleccione el riesgo de TI (obligatorio)',
	itRiskRequired: 'Riesgo de TI (requerido)',
	itspNameRequired: 'Nombre ITSP (obligatorio)',
	itspTaskDescription: 'Personalice esta descripción de la tarea para diseñar la naturaleza, el tiempo y el alcance de los procedimientos sustantivos de TI para obtener evidencia de auditoría suficiente y apropiada de que los riesgos de TI se abordan de manera efectiva durante todo el período de confianza.<br />Cuando el procedimiento sustantivo de TI se realice a partir de una fecha intermedia, diseñe y realice procedimientos para obtener evidencia adicional de que los riesgos de TI se abordan durante el período cubierto por nuestros procedimientos intermedios hasta el final del período.<br />Concluimos sobre los resultados de nuestros procedimientos sustantivos de TI.',
	itspRequired: 'Se requiere el nombre ITSP',
	selectTestingStrategy: 'Diseñar la naturaleza, el tiempo y el alcance de nuestras pruebas de controles para obtener evidencia de auditoría suficiente y apropiada de que el control funciona de manera efectiva según lo diseñado durante todo el período de confianza para prevenir o detectar y corregir errores materiales a nivel de afirmación. <br /> Concluir sobre la efectividad operativa de los controles, evaluando los resultados de nuestras pruebas de controles, incluso cuando ampliamos el tamaño de nuestra muestra y realizamos pruebas de controles compensatorios.',
	itControlNameTest: 'Prueba {0}',

	/*Edit ITControl*/
	editITControl: 'Editar ITGC / ITSP',
	viewITControl: 'Ver ITGC / ITSP',

	/*Add/Edit ITRisk*/
	editITRisk: 'Editar el riesgo de TI',
	editITRiskDescription: 'Edite el riesgo de TI',
	viewITRisk: 'Ver el riesgo de TI',
	addITRisk: 'Agregue riesgo de TI',
	addITRiskDescription: 'Introduzca la descripción del riesgo de TI',
	selectITProcess: 'Seleccione el proceso de TI (requerido)',
	itRiskName: 'Riesgo de TI',
	itRiskNameRequired: 'Riesgo de TI (requerido)',
	riskNameRequired: 'Se requiere riesgo de TI',
	processIdRequired: 'Se requiere un proceso de TI',
	itProcessRequired: 'Proceso de TI (requerido)',
	hasNoITGC: 'No hay ITGC que aborden el riesgo de TI',

	/*Edit Risk*/
	editRisk: 'Editar riesgo',
	viewRisk: 'Vista de riesgo',

	/*Edit Control*/
	editControl: 'Editar control',
	viewControl: 'Ver control',
	scotRelatedControls: 'Controles relacionados con',
	applicationControl: 'Control de aplicaciones',
	iTDependentManualControl: 'Control manual dependiente de TI',
	noAapplicationControlAvailable: 'Sin controles de aplicación',
	noITDependentManualControlAvailable: 'Sin controles ITDM',
	isIPEManuallyTested: 'La parte automatizada de este control ITDM es solo el uso de reportes generados por el sistema que se prueban de manera sustantiva.',

	/*Edit ITProcess*/
	editITSOProcess: 'Editar proceso de TI/SO',
	viewITSOProcess: 'Ver el proceso de TI/SO',

	/*Edit ITApplication*/
	viewITAppSO: 'Ver aplicación de TI/SO',
	editITAppSO: 'Editar aplicación de TI/SO',
	strategy: 'Estrategia',
	nameRequired: 'Nombre requerido',
	name: 'Nombre',

	/*Snap shot*/
	currentVersion: 'Versión actual',
	compareVersion: 'Seleccione una versión para comparar',
	snapshotVersionNotAvailable: 'No hay versiones disponibles para la comparación',
	snapshots: 'Instantáneas',
	sharedFormWarning: "Este es un formulario de Canvas compartido. Los objetos y la evidencia existen en el compromiso original y no se agregarán a este compromiso al desvincularse. Consulte <a style-'color: #467cbe' href-'https://live.atlas.ey.com/#library/104/p/SL33184174-396647/C_33404446/C_38129691' target-'_blank'>enablement here</a> para obtener más detalles. ",
	fullView: 'Vista completa',
	defaultView: 'Vista predeterminada',
	print: 'Impresión',
	version: 'Versión',
	navigationUnavailable: "La navegación no está disponible en la vista Control de cambios y atributos. Vea'Preguntas y respuestas' para habilitar la navegación nuevamente. ",
	snapshotUpdate: 'Actualizado',
	snapshotNew: 'Nuevo',
	snapshotRemoved: 'Eliminado',
	snapshotRollforward: 'Creado en el momento de Roll-Forward',
	snapshotRestore: 'Creado en el momento de la restauración',
	snapshotCopy: 'Creado en el momento de la copia',

	/*Special Body*/
	priorPeriodAmount: 'Importe del período anterior',

	// Helix special body:
	helixScreenshotListLoading: 'Cargando capturas de pantalla...',
	helixScreenshotLoading: 'Cargando imagen de captura de pantalla...',
	helixScreenshotDeleting: 'Eliminando captura de pantalla...',
	helixNotesLoading: 'Cargando marcas de verificación...',
	helixNotesBoundingBoxShow: 'Mostrar anotaciones',
	helixNotesBoundingBoxHide: 'Ocultar anotaciones',
	helixNoteReferenceNumber: '#',
	helixNoteReferenceNumberPlaceholder: 'Ingrese el número de referencia',
	helixNoteText: 'Nota',
	helixNoteTextPlaceholder: 'Introduzca el texto de la marca de verificación',
	helixNoteAnnotate: 'Anotar',
	helixNoteAnnotateMessage: 'Seleccione un área en la captura de pantalla para anotar, confirme la anotación y haga clic en la marca de verificación para guardar.',
	helixRemoveAnnotation: 'Eliminar anotación',

	/* User lookup body */
	userLookupInstructionalText: 'Ingrese el nombre o el correo electrónico y presione enter para ver los resultados.',
	userLookupShortInstructionalText: 'Ingrese el nombre o el correo electrónico y presione enter',

	/*Guidance*/
	guidance: 'Dirección',
	noIncompleteBodies: 'Seleccione un Encabezado o Sección en el menú de navegación para ver el contenido',
	noUnresolvedComments: 'Seleccionar formulario de encabezado o sección del menu de navegación para ver el contenido',
	addComment: 'Añadir comentario',

	/*Independence*/
	otherFormIndependenceMessage: 'El contenido de este formulario de independencia se ha actualizado y el usuario no ha vuelto a iniciar sesión desde que sucedió. Como resultado, algunas respuestas pueden estar incompletas. El estado de independencia anterior se ha mantenido como referencia.',
	override: 'Anular',
	grantAccess: 'Acceso a subvenciones',
	denyAccess: 'Denegar el acceso',
	overrideSmall: 'Anular',
	grantAccessSmall: 'conceder acceso',
	denyAccessSmall: 'negar el acceso',
	status: 'Estado',
	undefined: 'Indefinido',
	incomplete: 'Incompleta',
	noMattersIdentified: 'No hay asuntos identificados',
	matterIdentifiedPendingAction: 'Materia identificada - Acción pendiente',
	matterResolvedDeniedAccess: 'Materia resuelta - Acceso denegado',
	matterResolvedGrantedAccess: 'Materia resuelta - Acceso concedido',
	notApplicable: 'No aplicable',
	restored: 'Restaurado',
	overridden: 'Reemplazado',
	priorNoMattersIdentified: 'Previo - No hay asuntos identificados',
	priorMatterIdentifiedPendingAction: 'Previo - Asuntos identificados - Acciones pendientes',
	priorMatterResolvedGrantedAccess: 'Previo - Asuntos identificados - Acceso concedido',
	priorMatterResolvedDeniedAccess: 'Previo - Asuntos identificados - Acceso denegado',
	byOn: 'por {0} en',
	byLabel: 'por',
	onLabel: 'en',
	modifiedBy: 'Modificado por',
	reason: 'Razón',
	submit: 'Enviar',
	submitTemplate: 'Enviar plantilla',
	independenceHoverText: 'Su rol debe ser Socio a cargo; Socio de participación; o Director Ejecutivo para conceder, denegar o anular el acceso a este usuario.',
	enterRationaleText: 'Introduzca la justificación de',
	enterRationalePlaceholderText: 'Introducir texto de razonamiento',
	requiredRationaleText: 'Razones (obligatorio)',
	rationaleTextRequired: 'Se requieren razones',

	sharedExternalWarning: 'Este formulario se comparte a través de Canvas Client Portal y los miembros externos del equipo pueden acceder a él. Solo ingrese respuestas y comentarios que deban compartirse con miembros externos del equipo.',
	independenceViewTemplateMessage: 'Este formulario sirve como plantilla para la investigación de independencia individual de cada miembro del equipo. <br /> Al completar la investigación de independencia, hay varias preguntas que se relacionan con los requisitos de independencia aplicables a la entidad bajo auditoría para las cuales cada miembro del equipo debe proporcionar una respuesta. Seleccione las respuestas adecuadas a estas preguntas. Las respuestas se sincronizarán con la consulta de independencia individual de cada miembro del equipo. Si un miembro del equipo ha seleccionado una respuesta diferente, tendrá que reconfirmar su independencia cuando vuelva a entrar en el compromiso. Si no vuelven a entrar en el compromiso, se conservarán su estado de independencia y sus respuestas anteriores. <br /> Solo los usuarios autorizados pueden realizar cambios en la plantilla de independencia. Hable con un administrador de compromiso. Cualquier cambio realizado debe enviarse, incluso si se deshace manualmente antes del archivo.',

	/**
	 * FORM OBJECTS: SCOT-WCGW-CONTROL
	 */
	fo_instructionalText: 'Seleccione los objetos que el formulario de lienzo está documentando',
	fsro_instructionalText: 'Administrar los objetos relacionados con esta Sección',
	relObj_title_risk: 'Riesgos',
	relObj_title_riskType: 'Tipo de riesgo',
	fo_showOnlyRelated: 'Mostrar solo objetos relacionados',
	scotsCount: '{0} SCOTs',
	wcgwsCount: 'WCGWs {0}',
	itsoCount: '{0} aplicaciones de TI / organización de servicios',
	controlsCount: '{0} Controles',
	itControlsCount: '{0} controles de TI',
	itGcCount: '{0} ITGCs',
	itSpCount: '{0} ITSPs',
	itProcessesCount: '{0} procesos de TI',
	risksCount: '{0} Riesgos',
	accountsCount: 'Cuentas {0}',

	stEntitiesCount: '{0} Entidades',

	componentsCount: '{0} Componentes',
	view: 'Vista',
	searchByScotName: 'Buscar por nombre SCOT',
	searchByWcgwName: 'Buscar por nombre del WCGW',
	searchByITSOAppName: 'Buscar por nombre de aplicación de TI/SO',
	searchByControlName: 'Buscar por nombre de control',
	searchByItControlName: 'Buscar por nombre de control de TI',
	searchByItProcessName: 'Buscar por nombre del Proceso de TI',
	searchByRiskName: 'Buscar por nombre del riesgo',
	searchByAccountName: 'Buscar por nombre de cuenta',
	searchBySTEntityName: 'Buscar por nombre de entidad',
	searchByEstimateName: 'Buscar por nombre de estimado',
	searchByComponentName: 'Buscar por nombre de componente',
	noScotsAvailable: 'No hay Scots disponibles en este compromiso.',
	noRisksAvailable: 'No hay riesgos disponibles en este compromiso.',
	noControlsAvailable: 'No hay controles disponibles en este compromiso.',
	noItControlsAvailable: 'No hay controles de TI disponibles en este compromiso.',
	noItProcessesAvailable: 'No hay procesos de TI disponibles en este compromiso.',
	noItApplicationsAvailable: 'No hay aplicaciones de TI disponibles en este compromiso.',
	noAccountsAvailableLabel: 'No hay cuentas disponibles en este compromiso.',
	noObjectsRelatedToForm: 'No hay objetos relacionados con este formulario de lienzo',
	noDocumentControlsAvailable: 'No hay controles asociados en este documento',
	noDocumentScotsAvailable: 'No se asocia ningún SCOT a este documento.',
	noSTEntitiesAvailable: 'No hay entidades disponibles en este compromiso.',
	noComponentsAvailable: 'No hay componentes disponibles en este compromiso.',
	editObjectDescription: 'Editar la asociación de objetos a este formulario',
	editObjectsLabel: 'Editar objetos',
	noITGCsOrITSPsHaveBeenIdentified: 'No se han identificado ITGC ni ITSP',
	noItProcessIdentified: 'No se ha identificado ningún proceso de TI',
	noControlsIdentified: 'No se han identificado controles',
	noRelatedRisksIdentified: 'No se han identificado riesgos significativos o de fraude relacionados',
	noItApplicationsIdentified: 'No se han identificado aplicaciones informáticas',
	noSCOTIdentified: 'No se ha identificado ningún SCOT',
	noWCGWIdentified: 'No se han identificado WCGW',
	maxLimitLabel: 'Se ha seleccionado el número máximo de objetos.',
	minLimitLabel: 'Se ha seleccionado un número mínimo de objetos.',

	relatedITAppsTitle: 'Procesos de TI y aplicaciones de TI relacionadas',
	relatedWCGWTasksTitle: 'WCGWs y tareas relacionadas',
	noRelatedTasks: 'Sin tareas relacionadas',
	noRelatedWcgw: 'No hay WCW relacionados',
	noRelatedControls: 'Sin controles relacionados',
	controlRelatedRisksTitle: 'Controles y riesgos relacionados',
	sCOTRelatedRisksTitle: 'Los SCOTs y los riesgos relacionados',
	scotRelatedItApp: 'Aplicaciones de TI relacionadas con SCOT',
	relatedItApps: 'Aplicaciones de TI relacionadas',
	relatedRisksTitle: 'Riesgos relacionados',
	relatedItRisksItProcessesTitle: 'ITGC y procesos de TI relacionados y riesgos de TI',
	testingTitle: 'Pruebas',
	strategyTitle: 'estrategia',
	yes: 'Sí',
	no: 'No',
	noRelatedRisks: 'No hay riesgos significativos o de fraude relacionados',
	closeAllComments: 'Cerrar todos los comentarios',
	closeComments: 'Comentarios cercanos',
	closeCommentsDescription: 'Todos los comentarios abiertos y borrados se cerrarán. ¿Estás seguro de que quieres cerrar todos los comentarios para este {0}?',
	addCanvasFormDigital: 'Digital',
	addCanvasFormCore: 'Núcleo',
	addCanvasFormNonComplex: 'No Complejo',
	addCanvasFormComplex: 'Complejo',
	addCanvasFormListed: 'Listado',
	addCanvasFormGroupAudit: 'Auditoría de grupo',
	addCanvasFormPCAOBFS: 'PCAOB-FS',
	addCanvasFormPCAOBIA: 'PCAOB-IA',
	addCanvasFormStandards: 'Estándares',
	addCanvasFormLanguage: 'Idioma',
	addCanvasFormNoResultFound: 'No se han encontrado resultados',
	addCanvasFormStandardsNotSelectedMessage: 'El estándar es un campo obligatorio',
	addCanvasFormLanguageNotSelectedMessage: 'El idioma es un campo obligatorio',

	/* Confidentiality */
	confidentialityPlaceholder: 'Seleccionar confidencialidad',
	confidentiality: 'Confidencialidad',
	confidentialityTitle: 'Documento confidencial',
	confidentialityText: 'Establezca el nivel de acceso necesario para que se abra este documento. Los administradores del compromiso establecen los niveles de acceso en la página Administrar equipo. Si ya se ha establecido este documento como confidencial, solo aquellos que pueden abrir el documento pueden cambiarlo.',
	confidentialityNotOpenable: 'El documento no se puede abrir porque sus permisos en el compromiso son insuficientes. Los niveles de acceso son establecidos por los administradores del compromiso en la gestión del equipo.',
	confidentialityTargetNotOpenable: 'Los documentos confidenciales solo pueden ser abiertos desde el compromiso de origen.',
	backToCCP: 'De vuelta a EY Canvas Client Portal',
	guidanceMessageBackToCCP: 'Después de rellenar este formulario, vuelva a EY Canvas Client Portal y envíe la solicitud a EY.',
	noProfileInformationFound: 'No se encontró información de perfil. Actualice la página y vuelva a intentarlo. Si el problema persiste, comuníquese con la mesa de ayuda.',
	confirmUpdate: 'Confirmar actualización',
	keepVersion: 'Mantener esta versión',
	conflictDescription: '{0} ha editado este texto {1} desde que se abrió. Seleccione la versión que debe conservarse.',
	currentConflictVersion: 'Versión actual',
	serverConflictVersion: 'Versión del servidor',
	conflictShowChanges: 'Mostrar control de cambios',
	sectionViewTrackChangesDropdownPlaceholder: 'Seleccionar versión',
	verifyingIndependence: 'Verificando el estatus de independencia, por favor espere.',
	creatingIndependenceForm: 'Creando formulario de independencia.',
	meCallFailed: 'No se pudo recuperar la información del usuario. Actualice la página y vuelva a intentarlo. Si el problema persiste, comuníquese con mesa de ayuda.',
	getUserByIdFailed: 'No se pudo recuperar el estado de independencia del usuario. Actualice la página y vuelva a intentarlo. Si el problema persiste, comuníquese con mesa de ayuda.',
	independenceFormCreationFailed: 'No se pudo crear el formulario de independencia del usuario. Actualice la página y vuelva a intentarlo. Si el problema persiste, comuníquese con mesa de ayuda.',
	gettingProfile: 'Obteniendo información del perfil, por favor espere.',
	invalidDocumentId: 'El ID del documento no es válido. Actualice la página y vuelva a intentarlo. Póngase en contacto con la mesa de ayuda si el error persiste.',
	returnToEditMode: 'Regrese al modo de edición',
	saveAndCloseButtonTitle: 'Guardar y cerrar',
	formCreationFailed: 'Error al crear el formulario. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',

	/*Sign-off requirements*/
	signOffRequirements: 'Requisitos de cierre de sesión',
	signoffRequirementsModalTitle: 'Requisitos de cierre de sesión',
	signoffRequirementsModalDescription1: 'Ajuste a continuación los requisitos de firmas ejecutiva de este documento',
	signoffRequirementsModalDescription2: 'Algunos requisitos de aprobación no se pueden ajustar porque son requeridos por EY Canvas.',
	signoffRequirementsModalSaveLabel: 'Guardar',
	signoffRequirementsModalCancelLabel: 'Cancelar',
	signoffRequirementsModalCloseLabel: 'Cerrar',
	signoffRequirementsModalPICLabel: 'PIC',
	signoffRequirementsModalEQRLabel: 'EQR',

	/*<Ares>*/
	/* View changes */
	viewChanges: 'Ver cambios',
	viewChangesModalTitle: 'Ver cambios',
	documentModificationAlert: 'Esta actividad fue modificada por última vez por',
	dismiss: 'Descartar',

	/*Task List*/
	aresPageTitle: 'Habilitación de EY Canvas FIT',
	aresPageSubtitle: 'Complete los pasos a continuación con la información solicitada sobre su auditoría.',
	summary: 'Resumen',
	aresNoDocumentFound: 'No hay otra información disponible para la actividad seleccionada',
	taskSubTitleNoValue: 'Sin descripción disponible',
	mainActivities: 'Principales actividades',
	unmarkComplete: 'Desmarcar completado',
	markCompleteTitleTip: 'Marca completa',
	disableMarkCompleteTitleTip: 'Asegúrese de que todos los documentos relacionados estén firmados por al menos un preparador y un revisor para marcar esta actividad completa',
	/*Activity Summary*/
	activitySummary: 'Resumen de la actividad',
	selectedAnswers: 'Respuestas seleccionadas',
	allAnswers: 'Todas las respuestas',
	incompleteResponses: 'Respuestas incompletas',
	previous: 'anterior',
	next: 'Siguiente',
	viewAsLabel: 'Ver como',
	rolePreparerLabel: 'Preparador',
	roleDetailedReviewerLabel: 'Revisor detallado',
	roleGeneralReviewerLabel: 'Revisor General',
	roleEQRLabel: 'EQR',
	/*Simple Helix*/
	helixPlaceholder: 'Se requiere el tipo de guía 7 con el url d ela guia para las capturas de pantalla de la helix',
	noNotesAvailable: 'No se han creado marcas de verificación',
	addScreenshot: 'Agregar captura de pantalla',
	replaceScreenshot: 'Reemplazar la captura de pantalla',
	replaceFrameDescription: '`Revise el siguiente analítico y haga clic en Reemplazar para reemplazar la captura de pantalla existente.',
	addNote: 'Agregar marca de verificación',
	notes: 'Marcas de verificación',
	noScreenshotsAvailable: 'Haga clic en {viewDataAnalytic} para comenzar',
	viewDataAnalytic: 'Ver análisis de datos',
	/* Delete modal Helix screenshot*/
	modalTitle: 'Eliminar captura de pantalla',
	sureDeleteBeforeName: '¿Desea eliminar la captura de pantalla?',
	sureDeleteAfterName: 'Al eliminar la captura de pantalla, todas las marcas de verificación asociadas también se eliminarán y esta acción no se puede deshacer.',

	/*uploadDocument body type */
	relateExistingDocuments: 'Relacionar los documentos existentes',
	fromEngagementOr: 'de este/otro(s) compromiso(s) o',
	browse: 'navegar',
	toUpload: 'subir',
	signoffs: 'Firmar',
	addDocument: 'Añadir documentos',
	uploadDocument: 'Cargar documento',
	relateDocument: 'Relacionar documentos existentes',
	generateAccountRiskAssessmentPackage: 'Generar ALRA del grupo',
	relateDocumentsToBodyAresTitle: 'Relacionar documentos',
	discardLabel: 'Descartar',
	uploadDocumentLabel: 'Subir documento',
	confirm: 'Confirmar',
	duplicateDocumentHeader: 'Uno o más documentos con el mismo nombre ya existen en este compromiso (ya sea como evidencia o archivos temporales).',
	duplicateDocumentInstruction: "Seleccione'Sobrescribir' para cargar el documento y reemplazar el archivo existente o'Descartar' para cancelar. Si este archivo existente está en Evidencia, entonces el documento se cargará en Evidencia. Si el archivo existente está en Archivos temporales, el documento se cargará en Archivos temporales. ",
	maxUploadFilesError: 'El sistema puede cargar un máximo de 10 documentos al mismo tiempo',
	/*</Ares>*/
	noTaskRelatedToThisDocument: 'Ninguna tarea relacionada con este documento',
	uncheckTrackChangesToSave: 'Unselect the track changes option to save',
	reviewRoleCloseCommentsTitle: 'Comentarios no resueltos',
	reviewRoleCloseCommentsDesc: 'Hay comentarios no resueltos que deben ser abordados. Utilice el filtro para identificar fácilmente los comentarios no resueltos.',

	/*Document Upload - PIC/EQR Required Body type*/
	requirementDetails: 'Detalles del requisito',

	//Risk Factors
	riskFactor: 'Evento relevante y condición/riesgo de error',
	manageRisk: 'Gestionar los riesgos',
	noRiskFactors: 'No se han identificado eventos relevantes ni condiciones/riesgos de error',
	relateRiskFactorsToRisks: 'Determinar la importancia de eventos y condiciones',
	riskType: 'Tipo de riesgo',
	relateToRisk: 'Relacionarse con el riesgo',
	noRisksIdentified: 'Sin riesgo identificado',
	notDefined: 'No definido',
	selectValidRiskType: 'Seleccione un tipo de riesgo válido',
	newRisk: 'Añadir nuevos riesgos',
	notAROMM: 'No es un riesgo de error material',
	describeRationale: 'Describir el racional',
	noRisksIdentifiedForTheSpecificRiskType: 'No hay {0} identificados',
	addAnAccount: 'Associate additional account',
	selectAnAccount: 'Seleccionar una cuenta',
	noAccountsHaveBeenIdentified: 'No se han identificado cuentas',
	accountSelected: 'cuenta',
	statementType: 'Tipo de instrucción',
	selectAssertions: 'Seleccionar una o más aserciones',
	noAssertionsIdentifiedForAccount: 'No se han identificado aserciones para esta cuenta',
	relatedAssertions: 'Aserciones relacionadas',
	editAccount: 'Editar cuenta y divulgación',
	addNewDescription: 'Añadir nueva descripción',
	editRiskFactorDescription: 'Editar descripción',
	enterRiskFactorDescription: 'Introduzca la descripción del evento relevante y la condición/riesgo de error',
	riskFactorDescriptionRequired: 'Se requiere una descripción del evento relevante y de la condición/riesgo de error',
	riskFactorDescription: 'Descripción del evento relevante y de la condición/riesgo de error',
	createNewAccount: 'Crear una nueva cuenta',
	createAccountLabel: 'Ha creado correctamente la cuenta {0}',
	updateAccountLabel: 'Ediciones guardadas correctamente en la cuenta {0}',
	deleteAccountLabel: 'ha sido eliminado con éxito',
	significanceLabel: 'Importancia',
	provideRationale: 'Por favor, proporcione una justificación para guardar su selección',
	clearRiskSignificance: 'Importancia y descripción claros del riesgo',
	clearSignificance: 'Borrar significado y descripcion',

	// Account Summary
	unavailable: 'No disponible',
	notAvailable: 'no disponible',
	fraudRisk: 'Riesgo de fraude',
	significantRisk: 'Riesgo significativo',
	significantRiskIndicator: 'SR',
	fraudRiskIndicator: 'FR',
	inherentRisk: 'ROMM',
	inherentRiskIndicator: 'ROMM',
	prioryearbalance: 'Saldo del período anterior: ',
	accounts: 'Cuentas',
	accountsOther: 'Cuenta - Otros',
	accountsSignDis: 'Divulgación significativa',
	xMateriality: 'Múltiplos de la PM',
	xTEMateriality: 'x TE',
	estimateAssociated: 'Estimación asociada',
	designation: 'designación',
	noAccountshasbeenIdentified: 'No se ha identificado ninguna cuenta.',
	noAccountsOtherhasbeenIdentified: 'No se identificaron otras cuentas.',
	noAccountsSigfhasbeenIdentified: 'No se identificaron divulgaciones significativas.',
	addOtherAccounts: 'Agregar cuenta - Otro',
	addSignificantDisclosure: 'Agregar divulgación significativa',
	pydesignation: 'Designación previa: ',
	notapplicable: 'N/A',
	noApplicable: 'No aplicable',
	changeDesignationMessage: 'Está a punto de cambiar la designación de la cuenta',
	changeDesignationTitle: 'Cambiar la designación de la cuenta',
	continue: 'continuar',
	currentYearBalance: 'Período actual',
	currentPeriodBalance: 'Período actual',
	priorYear: 'Año anterior',
	priorYearDesignation: 'Designación del período anterior',
	priorYearEstimation: 'Estimación del período anterior',
	priorPeriodChange: '% de variación',
	analytics: 'Analítica',
	notROMMHeader: 'No es un riesgo de error material',
	manageEyCanvasAccounts: 'Administrar cuentas de EY Canvas',
	manageAccountMapping: 'Administrar la asignación de cuentas',
	manageAccountMappingCloseButton: 'Use el botón en la parte inferior de la página para cerrar.',
	manageAccountMappingToasterMessage: 'No se pudo conectar a EY Helix. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	inherentRiskTypeChangeMessage: 'Al cambiar las aseveraciones de la cuenta de relevantes a no relevantes, se eliminarán ciertas asociaciones en Canvas, incluidas las WCGW a las aseveraciones, y los PSP se degradarán a OSP. Haga clic en "Confirmar" si desea continuar. Haga clic en "Cancelar" para regresar sin cambios.',

	analyticsIconDisabled: 'EY Helix no está habilitado para su compromiso',
	//Estimate
	estimatesTitle: 'Estimaciones',
	relatedAccount: 'Cuentas Relacionadas',
	relatedAccounts: 'Cuentas relacionadas',
	currentBalance: 'Saldo actual',
	priorBalance: 'Saldo anterior',
	unrelateEstimates: 'Cuenta de estimación no relacionada',
	manageEstimates: 'Gestionar estimaciones',
	noEstimatesCreated: 'No se han creado estimaciones.',
	createEstimates: 'Crear presupuesto',
	estimateStarted: 'Para empezar',
	createEstimateDocument: 'Crear documentación de estimación',
	noEstimatesFound: 'No se han identificado estimaciones',
	relateEstimateLink: 'Relacionar estimaciones',

	//Journal Source
	noJournalEntrySourcesAreAvailable: 'No hay fuentes de entradas de diario disponibles.',
	jeSourceName: 'Fuente JE',
	jeSourceNameTooltip: 'Fuente JE',
	changeInUse: 'Cambio de uso',
	grossValue: 'Valor bruto de transacciones relacionadas',
	relatedTransactions: 'Número de transacciones relacionadas',
	relevantTransactions: 'Relevante para una clase significativa de transacciones',
	expandAll: 'Expandir todo',
	collapseAll: 'Desplegar todo',
	descriptionLabel: 'Proporcione una breve descripción del propósito de esta fuente.',
	jeSourceTypesLabel: '¿Los asientos de diario se registran a través de esta fuente, generados por el sistema o manualmente?',
	journalEntries: '¿Se utiliza esta fuente para registrar entradas de diario no estándar (es decir, transacciones o ajustes no recurrentes e inusuales)?',
	identifySCOTsLabel: 'Identifique los SCOT asociados a la fuente JE (seleccione todos los que correspondan)',
	systemGeneratedLabel: 'Generado por el sistema',
	manualLabel: 'Manual',
	bothLabel: 'Ambos',
	accountEstimateLabel: 'Est',
	addSCOTLabel: 'Añadir SCOT',
	newSCOTLabel: 'Nuevo SCOT',
	addSCOTModalDescription: 'Puede crear SCOTs. Los cambios se aplicarán una vez guardados.',
	scotnameRequired: 'Se requiere el nombre SCOT.',
	scotCategoryRequired: 'Se requiere la categoría SCOT.',
	estimateRequired: 'Se requiere estimación.',

	jeSourcesLabel: 'Fuentes JE',
	jeSourcesToSCOTs: 'Fuentes de JE a SCOTs',
	scotsToSignificantAccounts: 'SCOTs a cuentas significativas',

	//Modal common labels.
	modalCloseTitle: 'cerrar',
	modalConfirmButton: 'Guardar cambios',
	modalCancelTitle: 'Cancelar',
	modalSave: 'Guardar',
	modalSaveAndClose: 'Guardar y cerrar',
	modalSaveAndAdd: 'Guardar y agregar',
	modalSaveAndCreateAnother: 'Guardar y crear otro',

	//Add & Manage Risks
	addNewRiskModalTitle: 'Añadir nuevos riesgos',
	manageRisksListModalTitle: 'Gestionar los riesgos',
	riskInfoMessage: 'Los cambios se aplicarán una vez guardados.',
	risksListInstructionalText: 'Puede editar y eliminar los riesgos existentes. También puede agregar uno nuevo, si es necesario.',
	risksListEmptyArray: 'No hay riesgos disponibles. Agregar nuevo riesgo para empezar',
	addNewRiskButtonLabel: 'Añadir nuevos riesgos',
	labelRiskName: 'Nombre del riesgo',
	riskDescriptionLabel: 'Descripción del riesgo',
	selectRiskType: 'Tipo de riesgo',
	requiredRiskName: 'Se requiere el nombre del riesgo',
	requiredRiskType: 'Se requiere un tipo de riesgo',
	deleteRiskTrashLabel: 'Eliminar riesgo',
	undoDeleteRiskTrashLabel: 'Deshacer eliminación',
	notARommLabel: 'No hay riesgo de incorrección material',
	identifiedRiskFactors: 'Eventos/condiciones/riesgos identificados de error, riesgos de error material, riesgos significativos y riesgos de fraude.',
	noneIdentified: 'Ninguno identificado',
	countUnassociatedRisk: "Los eventos/condiciones/riesgos de error no están relacionados/no están marcados como'No es un riesgo de error material/Riesgo de error material'. ",

	// Bar Chart / Account Summary
	accountsTotal: 'Total de cuentas {0}',
	accountSummary: 'Resumen de la cuenta',
	allAccounts: 'Todas las cuentas',
	significantAccountsBarChart: 'Cuentas significativas',
	limitedAccounts: 'Cuentas de riesgo limitado',
	insignificantAccounts: 'Cuentas insignificantes',
	noAccountsHasBeenIdentifiedBarChart: 'No se han identificado {0}.',
	selectedTotalAccountsCounter: '{0}/{1} cuentas',
	identifyInsignificantAccounts: 'Identificar cuentas insignificantes',
	identifySignificantAccounts: 'Identificar cuentas significativas',
	identifyLimitedAccounts: 'Identificar cuentas de riesgo limitado',
	preInsigniAccounts: 'Cuenta previamente insignificante que ya no es menor que el ET en el período actual.',
	nonDesignatedInsignificant: 'Esta cuenta no se puede designar como insignificante. Haga clic en la lista desplegable de designación para cambiar la designación de esta cuenta.',
	tolerableError: 'El error tolerable no está disponible. Actualice la materialidad e inténtelo de nuevo.',
	documentContainerLabel: 'Documento',
	clickcreateformtogenerate: 'Haga clic en {0} para generar documentación de cuenta de riesgo limitado.',
	createform: 'Crear formulario',
	createLimitedRiskAccountDocumentation: 'Crear documentación de cuenta de riesgo limitado',
	limitedAccountDocumentName: 'Documentación de la cuenta de riesgo limitada',

	//Modal Confirm Switch account
	modalSwitchTitle: 'Cambios no guardados',
	modalConfirmSwitch: 'Confirmar',
	modalConfirmSwitchDescription: 'Los cambios no se guardan y se perderán si decide continuar. ¿Quieres continuar?',

	//Modal Edit Account
	manageAccountsModalTitle: 'Administrar cuentas de EY Canvas',
	editAccountLinkLabel: 'Editar cuenta',
	editAccountInstructionalText: 'Puede editar o eliminar cuentas de lienzo EY existentes o crear otras nuevas. Los cambios se aplicarán una vez guardados.',
	selectAnAccountLabel: 'Seleccionar cuenta',
	accountNameLabel: 'Nombre de cuenta',
	accountLabel: 'cuenta',
	accountDesignationLabel: 'Designación de la cuenta',
	accountStatementTypeLabel: 'Tipo de instrucción',
	accountRelatedAssertionsLabel: 'Aserciones relacionadas',
	accountHasEstimateLabel: '¿La estimación afecta a la cuenta?',
	requiredAccountName: 'Se requiere el nombre de la cuenta',
	requiredAccountDesignation: 'Se requiere la designación de la cuenta',
	requiredStatementType: 'Se requiere el tipo de instrucción',
	requiredRelatedAssertions: 'Seleccionar una aserción',
	pspIndexDropdownLabel: 'Seleccionar índice PSP',
	removePSPIndexLabel: 'Remover el índice PSP',
	addPSPIndexLink: 'Agregar índice psp',
	pspIsRequired: 'Se requiere un índice psp',

	//Delete account modal
	deleteAccount: 'Eliminar cuenta',
	deleteAccountModalMessage: '¿Está seguro de que desea eliminar la cuenta seleccionada?',
	cannotBeUndone: 'Esto no se puede deshacer.',
	guidedWorkflow: 'Habilitación de EY Canvas FIT',
	scotSummary: 'Resumen de la SCOT',
	scopeAndStrategy: 'Alcance y estrategia',
	ToggleSwitch: {
		Inquire: 'Preguntar',
		Completed: 'Completado',
		isOn: 'Sí',
		isOff: 'No'
	},
	navExecution: 'Ejecución',
	navCanvasEconomics: 'EY Canvas Economics',
	navOversight: 'Supervisión de EY Canvas',
	navConclusion: 'Conclusión',
	navTeamMemberIndependence: 'Independencia de los miembros del equipo',
	navGroupAudit: 'Gestión de grupos',
	navGroupActivityFeed: 'Fuente de actividades de grupo',
	navPrimaryStatus: 'Estado principal',
	navComponentStatus: 'Estado del componente',
	navGroupStatus: 'Estado del grupo',
	navEngagementManagement: 'Gestión del compromiso',
	navProfile: 'Perfil',
	navItSummary: 'Resumen de TI',
	nav440GL: 'Cambios después de la fecha del informe',
	navGroupStructureSummary: 'Estructura del grupo',
	navGroupInstructionSummary: 'Instrucciones de grupo',
	navGroupInvolvement: 'Participación grupal',
	navNotApplicable: 'No aplicable',
	cropScreenshot: 'Recortar captura de pantalla',
	cropScreenshotModalDescription: 'Recorte la captura de pantalla para incluir solo las partes relevantes. El recorte eliminará las anotaciones existentes, las marcas de verificación se conservarán y se podrán anotar nuevamente. El recorte no se puede deshacer.',
	crop: 'Recortar',
	replace: 'Reemplazar',
	nameTheScreenshot: 'Nombre de la captura de pantalla',
	nameLabel: 'Nombre',
	takeScreenshot: 'Agregar captura de pantalla',
	measurementBasis: 'Base de medición',
	MeasurementBasisMessage: 'Según el mapeo de datos subyacente de EY Helix, parece que la base de medición seleccionada no está en la posición esperada (por ejemplo, ingresos antes de impuestos en una posición de débito). Considere si: ',
	measurementBasisProjectMappedCorrectly: 'Los datos en el proyecto EY Helix están mapeados correctamente,',
	measurementBasisAppropriateValue: 'Una base de medición diferente puede ser apropiada, o',
	measurementBasisAdjustValue: 'Puede ser apropiado ajustar el valor de la base de medición como se indica a continuación',
	basisValueFromHelix: 'Valor del balance de prueba',
	rationaleDeterminationLabel: 'Justificación de cómo se determinó esta cantidad',
	loginInstructions: 'inicie sesión y siga las instrucciones para configurar su cuenta.',
	gotoText: 'Ir a',
	asOfDate: 'A partir de la fecha',
	annualizedBasisValue: 'Valor base anualizado',
	basisValue: 'Valor base',
	isAnnualizedAmountRepresentative: '¿El valor base anualizado es representativo de la cantidad que se espera que se notifique al final del período?',
	isAnnualizedAmountRepresentativeForAssetsOrEquity: '¿Es el valor representativo de la cantidad que se espera que se informe al final del período de auditoría?',

	enterExpectedFinancialPerioadAmount: 'Ingrese el monto esperado al final del período de auditoría',
	enterRationaleAmountDetermined: 'Ingrese la justificación de cómo se determinó esta cantidad',
	printNotAvailable: '{0} no tiene contenido y, por lo tanto, no se muestra ninguna información',
	canvasFormPrintNotAvailable: 'Canvas Form Print no está disponible actualmente. Inténtelo de nuevo o póngase en contacto con el servicio de asistencia si el error persiste.',
	saving: 'Guardando...',
	removing: 'Quitar..',
	activitySummaryTitle: 'Resumen de la actividad',
	currentLabel: 'Actual',
	PMLabel: 'MP',
	planningMaterialityLabel: 'Material de planificación',
	TELabel: 'ET',
	tolerableErrorLabel: 'Error tolerable',
	SADLabel: 'SAD',
	SADNominalAmountLabel: 'Importe nominal SAD',
	PriorLabel: 'Previo',
	editRichText: 'Editar texto',
	noTypeTwoResponseAvailable: 'No hay respuesta disponible. <br /> Haga clic en {clickHere} para responder.',
	clickHere: 'aquí',
	helixNavigationTitle: 'Ir a la página de configuración de EY Helix para vincular o configurar un proyecto de EY Helix',
	helixNavigationLink: 'Ir a EY Helix',
	measurementBasisValue: 'Valor base de medición',
	inlineSaveUnsavedChanges: 'Hay cambios no guardados, ¿desea continuar?',
	rationaleIncomplete: 'Razonado incompleto',
	projectMismatchDisplayMessageOnDataImport: 'Su proyecto principal de EY Helix ha cambiado. Confirme la configuración de EY Helix e importe los datos del nuevo proyecto.',
	importSuccess: 'Los datos de EY Helix se importaron con éxito.',
	importHelix: 'Haga clic en Importar para importar datos de libro mayor general desde Ey Helix.',
	importLabel: 'Importar',
	reImportLabel: 'Volver a Importar',
	lastImportedBy: 'Último importado por',
	lastImportedOn: 'Último importado en',
	dataImportedFromProjec: 'Datos importados del proyecto.',
	reImportConfirmationTitle: 'Re-importación de datos de Ey Helix',
	reImportConfirmationMessagePart1: 'La reimportación de datos de EY Helix cambiará los datos existentes o agregará nuevos datos dentro de las actividades relevantes y esto no se puede deshacer.',
	reImportConfirmationMessagePart2: 'Para obtener más información y un resumen de cómo el proceso de reimportación de datos afecta a la habilitación de EY Canvas FIT, consulte EY Atlas.',
	defineRisksTitle: 'Definir riesgos',
	assessAndDefineRisksTitle: 'Evaluar y definir riesgos.',
	identifiedRiskFactorsTitle: 'Eventos/condiciones identificados y riesgos relacionados: ',
	descriptionIncompleteLabel: 'Descripción incompleta',
	noRiskHasBeenRelatedMsg: 'No se ha relacionado ningún riesgo.',
	rationaleIncompleteMsg: 'Base logica incompleta',
	loading: 'Carga...',
	importInProgressLabel: 'Importación en curso. Esto podría tardar varios minutos. Actualice la página para ver el estado actualizado.',
	importInProgressMessage: 'Importación de datos de Helix en curso. Actualice la página para ver el estado de importación.',
	importHelixProjectError: 'Se ha producido un error al importar datos de EY Helix. Compruebe que el estado del proyecto de EY Helix se muestra como Analytics disponible, actualice la página y haga clic en Importar o Volver a importar. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	importDeletionConfirmationMsg: '¿Está seguro de que desea eliminar la importación de datos de EY Helix? Eliminar importación eliminará los datos existentes dentro de las actividades relevantes y esto no se puede deshacer.',
	deletePreviousImport: 'Eliminar la importación de EY Helix',
	//Assess Risks - Summary Page
	assessRisksAccordionLabel: 'Riesgos asociados',
	assessRisksNoItemsFound: 'No se han identificado riesgos.',
	assessRiskAccountsAndRelatedAssertions: 'Cuentas y aserciones relacionadas.',
	assessRiskNoAccountsLinked: 'Ninguna cuenta asociada',
	accountRiskAssessmentSummary: 'Cuentas y divulgaciones',
	// Flow chart
	flowchartTitle: 'Diagrama de flujo',
	launchFlowchart: 'Lanzar Diagrama de flujo',
	clickherelink: 'Haga clic aquí',
	orderToCashPlacement: 'Orden de cobro',
	orderToCashPlacementMessage: 'el Departamento de Programa negocia acuerdos con nuevos clientes y negocia la renovación de contratos y/o la modificación de contratos con los clientes existentes. Un contrato contiene detalles como: precios, términos y garantías',
	saveFlowChart: 'Guardar',
	newstep: 'Nuevo paso',
	zoomIn: 'Acercar',
	zoomOut: 'Alejar',
	resetZoom: 'Restablecer zoom',
	toogleInteractivity: 'Alternar Interactividad',
	fitView: 'Ajustar vista',
	numberOfSteps: 'Número de pasos',
	flowchartSuccessfullyCreated: 'Diagrama de flujo creado con éxito.',
	flowchartLinkedEvidenceMessage: 'Este diagrama de flujo se ha creado en otro compromiso. El acceso al diagrama de flujo en este compromiso se eliminará cuando se desvincule la evidencia.',
	flowchartSmartEvidenceSourceIdNullMessage: 'No hay SCOT disponible.',
	noTaskDocumentAvailableFlowchart: 'Este documento es un archivo temporal. Por favor, relacione una tarea como prueba para acceder a los detalles del diagrama de flujo',
	// Control Attributes
	controlAttributes: 'Atributos de control',
	noControlAttributes: 'No hay control disponible',
	flowchartStepMoremenu: 'Menú: "Más"',
	createControlAttributes: 'No hay atributos de control disponibles.<br />Haga clic en {clickHere} para crear un nuevo atributo de control.',
	createNewControlAttribute: 'Nuevo atributo',
	editControlAttribute: 'Editar atributo',
	createControlAtttributeInstructions: "Ingrese los detalles del atributo a continuación y seleccione <b>\'Guardar y cerrar\'</b> para finalizar. Para crear otro atributo, seleccione <b>\'Guardar y crear otro\'.</b> Los atributos se ordenarán según el índice de atributos. ",
	editControlAttributeInstructions: "Edite los detalles del atributo a continuación y seleccione <b>\'Guardar\'</b> para finalizar. Los atributos se ordenarán en función del índice de atributos. ",
	editAttributeButtonLabel: 'Editar atributo',
	deleteAttributeButtonLabel: 'Eliminar atributo',
	deleteControlAttributeInstructions: '¿Está seguro de que desea eliminar el atributo seleccionado? Esta acción no se puede deshacer.',
	// Control Attributes Form
	requiredAttributeIndexLabel: 'Índice de atributos (obligatorio)',
	requiredAttributeDescriptionLabel: 'Descripción del atributo (obligatorio)',
	errorMessageAttributeIndexRequired: 'Obligatorio',
	errorMessageAttributeDescriptionRequired: 'Obligatorio',
	errorMessageAttributeDescriptionMaxLength: 'La respuesta contiene {#} caracteres, que superan el máximo de {##} caracteres. Ajusta la descripción reduciendo el texto o el formato e inténtalo de nuevo. Si el problema persiste, póngase en contacto con el Servicio de Asistencia.',
	errorMessageAttributeTestingTypesRequired: 'Obligatorio',
	proceduresLabel: 'Procedimientos a realizar',
	modalRequiredProceduresLabel: 'Procedimientos a realizar (obligatorios)',
	attributeTestingTypesLabel: {
		inquiry: 'Indagación',
		observation: 'Observación',
		inspection: 'Inspección',
		reperformance: 'Reejecución/recálculo'
	},

	/*CRA Badge*/
	ir: 'IR',
	cr: 'CR',
	cra: 'CRA',
	incompleteCra: 'CRA incompleta',
	incomplete: 'Incompleta',

	//Progess bar labels
	savingProgress: 'Guardando...',
	discardChangesLabel: 'Descartar cambios',
	removeFromBody: 'Eliminar del cuerpo',
	uploading: 'Cargando ...',
	uploadComplete: 'Carga completa',
	downloadComplete: 'Descarga completa',
	processing: 'Procesando...',

	/* ISA BODIES */
	/* Common */
	deleteEntityConfirmation: '¿Está seguro de que desea eliminar <b>{0}</b>? Esta acción no se puede deshacer.',
	/* ITAPP-SCOT */
	searchScot: 'Búsqueda de scot',
	addITApp: 'Añadir aplicación de TI',
	relatedItApp: 'Aplicaciones de TI relacionadas',
	itApplicationHeader: 'Aplicaciones de TI',
	scotNoDataPlaceholder: 'No hay información disponible',
	noScotsOrControlsPlaceholder: 'Sin SCOTs o controles relacionados; {noScotsOrControlsPlaceholderEditAssoc}.',
	noScotsOrControlsPlaceholderEditAssoc: 'editar asociaciones',
	noScotsOrControlsPlaceholderTarget: 'No hay SCOTs o Controles relacionados.',
	scotHeader: 'SCOTs',
	controlsHeader: 'Controles',
	controlsApplicationHeader: 'Aplicacióm',
	controlsITDMHeader: 'ITDM',
	itAppScotNoDataPlaceholderLabel: 'No se han añadido aplicaciones informáticas.',
	itAppScotNoDataPlaceholder: 'No se han agregado aplicaciones de TI.<br /> Para comenzar, haga clic en {itAppScotNoDataPlaceholderAddItApp}',
	itAppScotNoDataPlaceholderAddItApp: 'Agregar aplicación de TI',
	editItAppOption: 'Editar aplicación de TI',
	removeItAppOption: 'Eliminar aplicación de TI',
	viewItAppOption: 'Ver aplicación de TI',
	editScotsISA: 'Editar scot',
	viewScotISA: 'Ver SCOT',
	viewControlISA: 'Ver controles',
	scotAndRelatedControls: 'SCOTs y controles',
	otherControls: 'Otros controles',
	controlTypeLabel: 'Tipo de control',

	/*SCOT-ITAPP*/
	addOrRelateItAppPlaceholder: '{identifyRelatedItApps} o documento que el {documentScotHasNoItApps}.',
	identifyRelatedItApps: 'Identificar aplicaciones de TI relacionadas',
	documentScotHasNoItApps: 'SCOT no tiene aplicaciones de TI relacionadas',
	correctScotDocumentationPlaceholder: 'El SCOT ha sido designado como no compatible con una aplicación informática. Se han asociado una aplicación y/o un control ITDM a este SCOT, {correctScotDocumentation}.',
	correctScotDocumentation: 'Visite nuevamente la designación de que ninguna aplicación de TI admite este SCOT',
	controlsWithoutRelatedItApps: 'Controles sin aplicaciones de TI relacionadas',
	controlsWithRelatedItApps: 'Controles con aplicaciones de TI relacionadas',

	/*AddEditITApplication */
	saveAndCreateNewButtonTitle: 'Guardar y crear nuevo',
	instructionalMessage: 'Agregue la aplicación de TI y seleccione los SCOTs relacionados. Asociar controles relacionados con la aplicación de TI, cuando corresponda.',
	ITApplicationNamePlaceholder: 'Nombre de la aplicación de TI (requerido)',
	scotDropdownPlaceholderText: 'Seleccione los SCOTs para que estén relacionados con la aplicación de TI',
	selectScotPlaceholderText: 'Seleccione los SCOTs para que estén relacionados con la aplicación de TI',
	selectControlPlaceholderText: 'Seleccione Controles para que estén relacionados con la aplicación de TI',
	noRelatedScotsPlaceholderText: 'Sin SCOTs relacionados',
	noRelatedControlsPlaceholderText: 'Sin controles relacionados',
	CreateSOModelTitle: 'Agregar organización de servicios',
	CreateSOInstructionalMessage: "Ingrese los detalles de la nueva organización de servicio a continuación y seleccione'<b>{0}</b>' para finalizar. Para crear otra organización de servicios, seleccione'<b>{1}</b>'.', //'Cree una nueva organización de servicios y asociar SCOTs y Controles relacionados con ella. ",
	saveAndCloseLabel: 'Guardar y cerrar',
	saveAndCreateLabel: 'Guardar y crear otro',
	SONamePlaceholder: 'Nombre de la organización de servicio (requerido)',
	SOSelectScotPlaceholderText: 'Seleccionar SCOTs relacionados con la organización del servicio',
	SOSelectControlPlaceholderText: 'Seleccione Controles relacionados con la organización del servicio',
	CreatedSOLabel: 'SO agregado',
	createdITAppLabel: 'Aplicación de TI agregada',
	searchNoResultFoundText: 'No se ha encontrado ningún resultado',
	searchNoResultsFoundText: 'No se han encontrado resultados',
	iTApplicationNameRequired: 'Se requiere el nombre de la aplicación de TI',
	soNameRequired: 'Se requiere el nombre de la organización de servicio',
	editITAppDesctiptionLabel: 'Editar la aplicación de TI y los SCOTs y controles asociados',
	editSODescriptionLabel: "Edite los detalles de la organización de servicio a continuación y seleccione <b>'Guardar'</b> para finalizar. ",
	viewITApplication: 'Ver aplicación de TI',
	itApplicationName: 'Nombre de la aplicación de TI',
	serviceOrganizationName: 'Nombre de la organización de servicio',
	newItApplication: 'Nueva aplicación de TI',

	/*Add/Edit ITProcess*/
	itProcessName: 'Nombre del proceso de TI',
	addItProcessDescription: 'Crear un proceso de TI',
	addItProcess: 'Añadir proceso de TI',
	itProcessNameRequired: 'Se requiere el nombre del proceso de TI',
	editItProcess: 'Editar proceso de TI',
	editItProcessDescription: 'Editar el proceso de TI',
	viewItProcess: 'Ver proceso de TI',
	taskTitle: 'Entender y documentar el proceso de TI: {0}',
	taskDescription: 'Documentar nuestra comprensión del proceso de TI. Adjunte el formulario pertinente como evidencia para respaldar nuestro entendimiento del proceso de TI.<br />Cuando los ITGC aparezcan en la sección Atributos de la tarea, realice procedimientos de recorrido para confirmar nuestra comprensión de los ITGC y evalúe su diseño e implementación. Adjuntar evidencia de nuestros procedimientos.',
	newItProcess: 'Nuevo proceso de TI',
	noITProcessesFound: 'No se han identificado procesos de TI',

	/* IT Process - Task */
	itProcessSearchPlaceholder: 'Proceso de búsqueda de TI',
	itProcessHeader: 'Proceso de TI',
	itProcessTasksHeader: 'Tareas',
	itProcessAddUPD: 'Añadir UDP',
	itProcessEditItProcess: 'Editar proceso de TI',
	itProcessRelateUDP: 'Relacionar UDP',
	itProcessViewProcess: 'Ver proceso de TI',
	itProcessNoDataPlaceholder: 'No se han agregado procesos de TI.<br /> Para comenzar, haga clic en {addItProcess}.',
	itProcessSourceInstructionalText: 'Al menos un UDP debe estar relacionado con el proceso de TI. {itProcessSourceInstructionalTextCreateUdp} o {itProcessSourceInstructionalTextRelateUdp}',
	itProcessSourceInstructionalTextCreateUdp: 'Crear un nuevo UDP',
	itProcessSourceInstructionalTextRelateUdp: 'relacionar un UDP existente.',
	itProcessTargetInstructionalText: 'Ningún UDP está relacionado con el proceso de TI.',

	/* IT APP IT PROCESSES RELATION*/
	itApplicationHeaderRelate: 'Aplicaciones de TI',
	itProcessesHeaderRelate: 'Proceso de TI',
	itAppNoDataPlaceHolderLabel: 'No se han identificado aplicaciones informáticas.',
	itAppNoDataPlaceHolder: 'No se han identificado aplicaciones de TI.<br />{identifyItApp}',
	identifyItApp: 'Identifique una aplicación de TI.',
	itProcessNoDataPlaceHolderRelationLabel: 'No se ha identificado ningún proceso informático.',
	itProcessNoDataPlaceHolderRelation: 'No se ha identificado ningún proceso TI <br /> {identifyItProcess}',
	identifyItProcess: 'Identifique un proceso de TI.',
	editItApp: 'Editar aplicación de TI',
	deleteItApp: 'Eliminar aplicación de TI',
	drag: 'Arrastre el proceso de TI a las aplicaciones de TI relacionadas',
	// editItProcess: 'Edit IT process',
	deleteItProcess: 'Eliminar proceso de TI',
	unassociatedProcess: '{0} procesos no relacionados',
	unassociatedItApplications: '{0} aplicaciones de TI no relacionadas',
	showOnlyUnrelated: 'Mostrar solo lo no relacionado - {0}',
	searchItProcess: 'Búscar proceso de TI',
	searchItApplication: 'Buscar aplicación de TI',
	itProcessesLabel: 'Procesos de TI',
	itApplicationsLabel: 'Aplicaciones de TI',
	showAllItAplications: 'Mostrar todas las aplicaciones de TI',
	showAllItProcesses: 'Mostrar todos los procesos de TI',
	relatedToLabel: "Lista de todos los <span class='child-entity-count'>{count}</span> <span class='child-entity-name'>{child}</span> relacionados con <span class='parent-entity-object-name'>{parent}</span> ",

	/* IT Process > IT Risk */
	itProcessItRiskNoDataPlaceholder: 'No se ha identificado ningún proceso de TI.<br/>{identifyItProcess}',
	itProcessItRiskNoDataPlaceholderTarget: 'No se ha identificado ningún proceso informático.',
	itApplication: 'Aplicaciones de TI',
	itGC: 'ITGCs',
	addItRiskBtnTitle: 'Agregue riesgo de TI',
	itProcessItRiskUnrelatedITGC: 'ITGC no asociados',
	itProcessItRiskUnrelatedITGCUppercase: 'ITGC no asociados',
	itProcessItRiskNoRisksNoControlsPlaceholder: 'No es necesario incluir los riesgos de TI o ITGC para este proceso de TI porque no hay controles de aplicación o ITDM con una evaluación de diseño efectiva relacionada con las aplicaciones de TI asociadas a este proceso de TI.',
	itProcessItRiskNoRisksControlsPlaceholder: 'Basado en la aplicación identificada y los controles ITDM {itRiskIdentify} para este proceso de TI',
	itRiskIdentify: 'Los riesgos de TI deben ser identificados',
	itProcessItRiskItProcessContentTitle: 'Riesgos de TI y ITGC',
	itProcessItRiskItRiskNoItgcRequiredPlaceholder: 'No hay ITGCs que aborden el riesgo de TI.',
	itProcessItRiskItRiskItgcRequiredPlaceholder: 'Todos los riesgos identificados deben tener al menos un ITGC identificado o una designación de que el riesgo de TI no tiene ITGCs.<br/> Identifique un {newITGC} o {existingITGC} que aborde el riesgo de TI o indique que hay {noItRisksIdentified} que aborden el riesgo de TI.',
	noItRisksIdentified: 'Sin ITGCs',
	newITGC: 'nuevo ITGC',
	existingITGC: 'ITGC existente',
	unrelatedItGCModalMessage: 'Elimine los ITGC no asociados que ya no sean necesarios.',
	unrelatedItGCModalNoDataPlaceholder: 'Sin ITGC no asociado',
	removeItRisk: 'Elimine el riesgo de TI',
	deleteItRiskConfirmation: '¿Está seguro de que desea eliminar el riesgo de TI <b>{0}</b>? Esta acción eliminará el riesgo de TI y no se puede deshacer.',
	relateItGcTitle: 'Relacionar ITGC',
	relateItGcEntityTitle: 'Riesgo de TI',
	relateItGcDescription: 'Seleccione los ITGCs que sean relevantes para el riesgo de TI.',
	relateItGcSearchPlaceholder: 'Buscar ITGC',
	relateItGcShowSelectedOnlyText: 'Mostrar solo los ITGCs relacionados',
	relateItGcNoDataPlaceholder: 'No hay ITGCs disponibles. Cree un nuevo ITGC para continuar.',
	relateITSPTitle: 'Relacionar ITSP',
	relateITSPDescription: 'Seleccione los ITSPs que sean relevantes para el riesgo de TI.',
	relateITSPSearchPlaceholder: 'Buscar por el nombre ITSP',
	relateITSPShowSelectedOnlyText: 'Mostrar solo los ITSPs relacionados',
	relateITSPNoDataPlaceholder: 'No hay ITSP disponible. Cree un nuevo ITSP para continuar.',

	/* IT Process Task Relationship */
	relateUDP: 'Relacionar UDP',
	relateUDPDescription: 'Seleccione las tareas UDP que sean relevantes para el proceso de TI.',
	relateUDPListHeaderItemName: 'Nombre de la tarea',
	relateUDPSearchPlaceholder: 'Buscar por nombre de tarea',
	relateUDPNoResultsFoundPlaceholder: 'No se han encontrado resultados',
	relateUDPCountLabel: '{0} tareas',
	relateUDPClose: 'Cerrar',
	relateUDPShowOnlyRelatedTasks: 'Mostrar solo tareas relacionadas',
	relateUDPNoDataFoundPlaceholder: 'No hay tareas disponibles',
	relateUDPNoDataPlaceHolder: 'No se ha identificado ningún proceso de TI',

	/* ITGC test strategy */
	itProcessItRiskItGcWithoutDesignEffectiveness: 'ITGC sin efectividad de diseño',
	searchItGC: 'Buscar proceso de TI',
	itGCNoDataPlaceHolder: 'No se ha identificado ningún proceso de TI',
	addItRisks: 'Agregue riesgos de TI',
	itDMHeader: 'ITDM',
	itAppHeader: 'Aplicación de TI',
	itTestHeader: 'Prueba',
	itTestingHeader: 'Pruebas',
	itgcHeader: 'ITGCs',
	controlsSelectedHeader: 'Controles seleccionados para las pruebas',
	iTRisksAndITGCs: 'Riesgos de TI y ITGC',
	NoITGCForITRiskPlaceholder: 'No existen ITGC en el entorno de TI para abordar este riesgo de TI',
	ITGCsNotIdentifiedRiskNoITGCs: 'No se han identificado los ITGC para este riesgo. {identifyAnITGC} o designar que {itRiskHasNoITGCs}.',
	identifyAnITGC: 'Identificar un ITGC',
	itRiskHasNoITGCs: 'El riesgo de TI no tiene ITGC',

	/**
	 * IT SO > SCOT
	 */
	searchItSO: 'Organización del servicio de búsqueda',
	addItSOBtnTitle: 'Agregar organización de servicios',
	itSoNoDataPlaceHolder: 'No se han identificado organizaciones de servicio.<br/><a>{identifyAnSo}<a/>',
	noItSoDataPlaceHolder: 'No se han identificado organizaciones de servicios.',
	identifyAnSo: 'Identificar una organización de servicios',
	soHeader: 'Organización de servicios',
	editSO: 'Editar organización del servicio',
	deleteSO: 'Eliminar organización de servicios',
	viewSO: 'Ver organización del servicio',
	controlRelatedToSO: 'Controles relacionados a la SO',

	/**
	 * Manage IT SP
	 */
	addITSP: 'Agregar ITSP',
	searchPlaceholderManageITSP: 'Buscar proceso de TI',
	noManageITSPDataPlaceholder: 'No se ha identificado ningún proceso de TI',
	itRiskColumnHeader: 'Riesgos TI',
	itDesignEffectivenessHeader: 'Eficacia del diseño',
	itTestingColumnHeader: 'Pruebas',
	itGCColumnHeader: 'ITGCs',
	itSPColumnHeader: 'ITSPs',
	searchClearButtonTitle: 'Limpiar',
	itProcessItRiskUnrelatedITSP: ' ITSP no asociados',
	manageITSPUnrelatedITSPUppercase: 'ITSPs no asociados',
	unrelatedITSPModalMessage: 'Elimine los ITSPs no asociados que ya no son necesarios.',
	unrelatedITSPModalNoDataPlaceholder: 'Sin ITSP no asociado',
	noITGCPlaceholderMessageFragment1: 'Todos los riesgos identificados deben tener al menos un ITGC identificado o una designación de que el riesgo de TI no tiene ITGC: ',
	noITGCPlaceholderMessageFragment2: 'Identificar un',
	noITGCPlaceholderMessageFragment3: 'nuevo ITGC',
	noITGCPlaceholderMessageFragment4: 'o',
	noITGCPlaceholderMessageFragment5: 'ITGC existente',
	noITGCPlaceholderMessageFragment6: 'que aborda el riesgo de TI o indica que hay',
	noITGCPlaceholderMessageFragment7: 'no ITGCs',
	noITGCPlaceholderMessageFragment8: 'que abordan el riesgo de TI',
	addNewITSP: 'Agregar nuevo ITSP',
	addExistingITSP: 'Agregar ITSP existente',
	noITSPPlaceholderMessageFragment1: 'Si hemos evaluado los ITGC como ineficaces o hemos determinado que no existen ITGC para abordar el riesgo de TI, es posible que podamos realizar procedimientos de prueba sustantivos de TI (ITSP) para obtener una garantía razonable de que el riesgo de TI dentro del proceso de TI asociado con el ITGC ineficaz no fue explotado.',
	noITSPPlaceholderMessageFragment2: 'Identificar un nuevo ITSP',
	noITSPPlaceholderMessageFragment3: 'o',
	noITSPPlaceholderMessageFragment4: 'relacionar un ITSP existente',
	noITSPPlaceholderMessageFragment5: '.',
	noITGCsExitForITRisk: 'No existen ITGC para el riesgo de TI.',
	noITSPExitForITRisk: 'No se ha identificado ningún ITSP para el riesgo de TI.',
	manageITSPItemExpansionMessage: 'Riesgos TI',
	noITGCExists: 'No hay ITGCs que aborden el riesgo de TI.',
	iTGCName: 'Nombre ITGC',
	itSPName: 'Nombre ITSP',
	operationEffectiveness: 'Eficacia operativa',
	savingLabel: 'Guardando',
	deletingLabel: 'Eliminando',
	removingLabel: 'Quitando',
	itFlowModalDescription: 'Vaya a {itSummaryLink} para editar/eliminar estos objetos que ya no son aplicables para la contratación.',
	itSummaryLink: 'Pantalla Resumen de TI',
	manageITSPYes: 'Sí',
	manageITSPNo: 'No',

	understandITProcess: 'Comprender los procesos de TI',
	activity: 'Actividad',
	unsavedPageChangesMessage: 'Tiene cambios no guardados que se perderán si decide continuar. ¿Estás seguro de que quieres salir de esta página?',
	unsavedChangesTitle: 'Cambios no guardados',
	unsavedChangesLeave: 'Salir de esta página',
	unsavedChangesStay: 'Permanezca en esta página',

	notificationDownErrorMessage: 'La función Notificaciones no está disponible temporalmente. Actualice la página e inténtelo de nuevo. Si este mensaje persiste, póngase en contacto con el servicio de asistencia.',
	notificationUpbutSomeLoadingErrorMessage: 'Se ha producido un error técnico que impide que la función Notificaciones funcione. Actualice la página e inténtelo de nuevo.',
	markCompleteError: 'Todos los documentos presentados requieren al menos la aprobación de un preparador y un revisor.',
	markCompleteDescription: 'Todos los documentos deben ser firmados por al menos un preparador y un revisor para marcar la actividad como completa.',
	lessthan: 'Menos que',
	openingFitGuidedWorkflowFormError: 'No se puede abrir el formulario de habilitación de EY Canvas FIT',
	timeTrackerErrorFallBackMessage: 'La función de seguimiento de tiempo no está disponible temporalmente. Actualice la página e inténtelo de nuevo. Si este mensaje persiste, póngase en contacto con el servicio de Soporte Técnico.',
	timeTrackerLoadingFallbackMessage: 'La función de seguimiento de tiempo no está disponible temporalmente. Estará disponible en breve.',
	priorPeriodRelateDocument: 'Relacionar evidencia de períodos anteriores',
	selectedValue: 'Valor seleccionado',
	serviceGateway: 'Portal de servicio',
	docNameRequired: 'El nombre no puede estar vacío',
	docInvalidCharacters: 'El nombre no puede incluir: */:<>\\?|"',
	invalidComment: 'No se pudo agregar el comentario. Si selecciona varios elementos en una lista, seleccione solo un elemento y vuelva a intentarlo. Si el problema persiste, actualice la página y vuelva a intentarlo o póngase en contacto con el servicio de asistencia.',
	inputInvaildCharacters: 'La entrada no puede incluir la siguiente cadena de caracteres: */:<>\\?|"',

	// FIT Navigation panel
	relatedActivities: 'Actividades relacionadas',
	backToRelatedActivities: 'Volver a actividades relacionadas',
	backToMainActivities: 'Volver a las actividades principales',
	formOptions: 'Opciones de formulario',

	// FIT Sharing
	shareActivity: 'Compartir actividad',
	shareLabel: 'Compartir',
	shareInProgress: 'Compartir en progreso',
	manageSharing:
		"Compartir esta actividad requiere el permiso de usuario 'Administrar el uso compartido de EY Canvas FIT enablement'. Vaya a la página 'Administrar equipo' para administrar los permisos o póngase en contacto con otro miembro del equipo",
	dropdownPlaceholderSA: 'Seleccione el compromiso para compartir con',
	fitSharingModalInfo: 'Comparta esta actividad con una o más actividades del mismo compromiso o de otro compromiso en el mismo espacio de trabajo. Si las actividades seleccionadas aún no se han compartido, se sobrescribirán las respuestas de las actividades seleccionadas a continuación. Si la actividad es compartida, solo se puede seleccionar una y las respuestas de esta actividad se sobrescribirán.',
	lastModifiedDate: 'Modificado por última vez el: ',
	noActivityToShare: 'No hay actividad disponible para compartir',
	activityNotShared: '{0} no se compartió',
	activityShareSuccessfull: '{0} compartido correctamente',
	sharedWithAnotherFITActivity: 'Esta actividad se comparte con otra actividad',
	sharedActivityWithAnotherCanvas: 'Compartir actividad con otra habilitación de EY Canvas FIT',
	shareActivityModalTitle: 'Compartir actividad',
	showRelationshipsTitle: 'Mostrar relaciones',
	shareActivityEngagement: 'Compromiso',
	shareActivityRelationshipsModalTitle: 'Relaciones de actividad compartidas',
	shareActivityWorkspaceHeading: 'Esta actividad se comparte con los siguientes compromisos y actividades relacionadas dentro del mismo espacio de trabajo.',
	shareModalOkTitle: 'Compartir',
	shareModalContinueLabel: 'Continuar',
	selectedActivityInfoLabel: 'en el compromiso seleccionado se modificó por última vez el: ',
	noSharedActivityInfoLabel: 'Este compromiso no tiene otro documento de este mismo tipo con el que compartir.',
	alreadyHasSharedActivityInfoLabel: 'La actividad seleccionada ya está compartida con otras actividades. Al compartir la actividad actual, se sincronizarán las respuestas de la actividad seleccionada con la actividad actual.',
	selectActivityResponsesForSharingLabel: 'Seleccione qué respuestas del documento deben reemplazar a las demás: ',
	selectActivityResponsesForCurrentRadioLabel: 'Compartir respuestas del documento actual al documento seleccionado anteriormente',
	selectActivityResponsesForSelectedRadioLabel: 'Compartir respuestas del documento seleccionado al documento actual',
	selectActivityResponsesWarningEarlierTimeLabel: "The current activity was modified at an earlier time compared to the selected engagement's activity. Please consider this before confirming the sharing option's below the table.",
	selectActivityResponsesWarningModifiedMoreRecentlyLabel: 'La actividad actual se modificó más recientemente en comparación con la actividad del documento seleccionado. Tenga esto en cuenta antes de confirmar la opción de uso compartido anterior.',
	selectActivityUnsuccessfulMessage: 'No se pudo compartir. Por favor, intenta de nuevo. Si el problema persiste, comuníquese con la mesa de ayuda de EY.',
	otherEngagemntDropdownlabel: 'Otras interacciones en el espacio de trabajo: ',
	documentSearchPlaceholder: 'Documento de búsqueda',
	showOnlySelected: 'Mostrar solo seleccionados',

	//FIT Copy
	copyLabel: 'Copiar',
	copyActivity: 'Actividad de copia',
	copyInProgress: 'Copia en curso',
	fitCopyModalInfo: 'Copie las respuestas de esta actividad a una o más actividades de la mismo compromiso o de otro compromiso en el mismo espacio de trabajo..',
	dropdownPlaceholderCA: 'Seleccione el compromiso a la que desea copiar',
	noCopyActivityInfoLabel: 'Este encargo no contiene un documento de este mismo tipo al que copiar.',
	copyActivityHoverLabel: 'Esta actividad ya está compartida con otras actividades y no se puede copiar en',
	copyActivityWarningEarlierTimeLabel: 'La actividad actual se modificó en un momento anterior en comparación con la actividad del compromiso seleccionado. Por favor, tenga esto en cuenta antes de confirmar las opciones de copia.',

	//Unlink
	unlinkModalTitle: 'Desvincular actividad',
	unlinkModalDescription: '¿Está seguro de que desea desvincular la actividad seleccionada?',
	unlinkLabel: 'Desvincular ',
	insufficientPermissionsLabel: 'Permiso insuficiente',
	unlinkFailMessage: 'Error al desvincular. Actualice e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia de EY.',
	unlinkSuccessfulMessage: 'Desvinulación exitosa',
	unlinkInProgressLabel: 'Desvinculación en progreso',
	unlinkError: 'Error de desvinculación',
	unlinkInProgressInfo: 'Desvincular en curso. Esto puede tardar hasta quince minutos. Una vez completada la desvinculación, este formulario deberá cerrarse y volver a abrirse.',

	/** Manage scot modal labels */
	scotName: 'Nombre SCOT',
	scotCategory: 'Categoría SCOT',
	estimate: 'Estimación',
	noScotsAvailablePlaceHolder: 'No SCOT disponible. Agregue un nuevo SCOT para comenzar',
	addScotDisableTitle: 'Complete todos los detalles del SCOT para agregar un nuevo SCOT',
	deleteScotTrashLabel: 'Eliminar Scot',
	undoDeleteScotTrashLabel: 'Deshacer eliminación',
	scotNameValidationMessage: 'Se requiere el nombre SCOT',
	scotCategoryValidationMessage: 'Se requiere la categoría SCOT',
	scotWTTaskDescription: '<p>Para todos los SCOT rutinarios y no rutinarios y los procesos de divulgación significativos, confirmamos nuestra comprensión cada período mediante la realización de procedimientos de revisión. Además, para las auditorías de la PCAOB, realizamos procedimientos de revisión de los SCOT de estimación.<br/>Para todos los SCOT, cuando adoptamos una estrategia de confianza en los controles, y para los controles que abordan riesgos significativos, confirmamos que los controles relevantes se diseñaron e implementaron adecuadamente. Confirmamos que nuestra decisión de adoptar una estrategia de confianza en los controles sigue siendo adecuada.<br/><br/>Concluimos que nuestra documentación describe con precisión el funcionamiento del SCOT y hemos identificado todas las WCGW adecuadas, incluidos los riesgos que surgen del uso de TI y controles relevantes (cuando corresponda).<br/><br/> Para los SCOT de estimación cuando usamos una estrategia solo sustantiva, determinamos si nuestra comprensión de la SCOT de estimación es adecuada en función de nuestros procedimientos sustantivos.</p>',

	relate: 'Relacionar',
	unrelate: 'No relacionar',
	related: 'Relacionado',
	relatedSCOTs: 'SCOT relacionados',
	thereAreNoSCOTsIdentified: 'No hay SCOT identificados',
	selectSCOTsToBeRelated: 'Seleccionar SCOTs para ser relacionados',

	//OAR Tables
	OARBalanceSheet: 'Hoja de balance',
	OARIncomeStatement: 'Estado de resultados',
	OARCurrentPeriod: 'Fecha de análisis',
	OARAmountChangeFrom: 'Cambiar de',
	OARPercentageChangeFrom: '% de variación desde',
	OARNoDataAvailable: 'Datos no disponibles. Revise la página {0} e importe los datos para continuar.',
	OARAnnotationLabel: 'Haga clic para revisar el motivo de los cambios inesperados o la falta de cambios esperados',
	OARAnnotationSelectedIcon: 'Documentar el motivo de los cambios inesperados o la falta de cambios esperados',
	OARAnnotationModalTitle: 'Anotación',
	OARAnnotationModalPlaceholder: 'Documente los elementos que parezcan inusuales, cambios inesperados o falta de cambios esperados.',
	OARWithAnnotationLabel: 'Documentación de cambios inesperados',
	OARAnnotation: 'Anotación',
	OARAccTypeWithAnnotationCountLabel: '{0} anotaciones dentro del tipo de cuenta',
	OARSubAccTypeWithAnnotationCountLabel: '{0} anotaciones dentro del subtipo de cuenta',
	OARColumnA: 'A',
	OARColumnB: 'B',
	OARColumnC: 'C',
	OARComparative1Period: 'Fecha comparativa 1',
	OARComparative2Period: 'Fecha comparativa 2',
	OARExpand: 'Expandir clase de cuenta',
	OARCollapse: 'Contraer clase de cuenta',
	OARHelixNavigationLink: 'Visite EY Helix para obtener información adicional',
	OARPrintNoDataAvailable: 'No hay datos disponibles',
	OARAdjustedBalance: 'Saldo ajustado',
	OARLegendLabel: 'Los valores marcados con * indican que incluyen un ajuste. Vaya al módulo Ajuste para obtener más detalles.',
	OARAccountType: 'Tipo de cuenta',
	astrixLabel: '*',

	//OAR Helix integration
	helixIntegrationModalDescription: 'Se trata de un texto pendiente de definir',
	OSJETabText: 'Otro entrada del diario',
	activityAnalysisTabText: 'Análisis de la actividad',
	preparerAnalysisTabText: 'Análisis del preparador',
	accountMetricsTabText: 'Métricas de la cuenta',
	noAnalyticsData: 'No hay análisis disponibles para mostrar',

	printActivitiesTitle: 'Imprimir actividades',
	printActivitiesModalInfo: 'Seleccione las actividades que le gustaría incluir.',
	printActivitiesModalConfirmButton: 'Combinar PDF',
	printActivitiesDropdownLabel: 'Actividades FIT',
	printActivitiesAll: 'Todo',
	oarSetupText: 'Vaya a la página {0} para vincular o configurar un proyecto de EY Helix',
	helixNotAvailable: 'EY Helix no está disponible para su compromiso.',
	dragDropUploadPlaceholder: 'Arrastre y suelte uno o más documentos o haga clic en <span>{addDocument}</span>',

	noTaskAssociatedToastMessage: 'Dado que el formulario Canvas se encuentra en archivos temporales, los documentos agregados también se han agregado a los archivos temporales',

	// chart labels.
	assets: 'Activo',
	liabilities: 'Pasivo',
	equity: 'Equidad',
	revenues: 'Ingresos',
	expenses: 'Expensas',
	noAccountsAvailable: 'No hay cuentas disponibles',

	// ALRA
	ALRAFilterByAccount: 'Filtrar por cuenta',
	ALRANoRecords: 'No se han encontrado resultados',
	ALRAAssertions: 'Afirmaciones',
	ALRAInherent: 'Factores de riesgo inherentes',
	ALRAHigher: 'Factores de riesgo más altos',
	ALRAAccountDisclosure: 'Cuentas/Divulgaciones',
	ALRAType: 'Tipo',
	ALRAName: 'Nombre',
	ALRARisks: 'Riesgos',
	ALRAC: 'C',
	ALRAEO: 'E/O',
	ALRAMV: 'M/V',
	ALRARO: 'R&O',
	ALRAPD: 'P&D',
	ALRAR: 'R',
	ALRANoRisksAssociated: 'No hay riesgo relacionado con esta cuenta',
	ALRAAccountsDisclosureName: 'Nombre de las cuentas/divulgación',
	ALRAHigherRisk: 'Mayor riesgo',
	ALRAHigherInherentRisk: 'Mayor riesgo inherente',
	ALRAHigherRiskCode: 'H',
	ALRALowerRisk: 'Menor riesgo',
	ALRALowerInherentRisk: 'Menor riesgo inherente',
	ALRALowerRiskCode: 'L',
	ALRALimitedRiskAccount: 'La cuenta ha sido identificada como de riesgo limitado',
	ALRAInsignificantRiskAccount: 'La cuenta ha sido identificada como insignificante',
	ALRADesignations: 'Designaciones',
	ALRABalances: 'Balances',
	ALRADesignation: 'Designación',
	ALRAAnalysisPeriod: 'Fecha de análisis',
	ALRAxTE: 'xTE',
	ALRAPercentageChangeFrom: '% de cambio desde',
	ALRAPriorPeriodDesignation: 'Designación de período anterior',
	ALRAPriorPeriodEstimate: 'Estimación del período anterior',
	ALRAComparativePeriod1: 'Fecha comparativa 1',
	ALRAComparativePeriod2: 'Fecha comparativa 2',
	ALRASelectUpToThreeOptions: 'Selecciona hasta 3 opciones',
	ALRASelectUpToTwoOptions: 'Seleccione hasta 2 opciones',
	ALRAValidations: 'Validaciones',
	ALRANoSignOffs: 'Sin firmas',
	ALRAIncompleteInherentRisk: 'Riesgo inherente incompleto',
	ALRARelatedDocuments: 'Documentos Relacionados',
	ALRAGreaterExtent: 'Mayor extensión',
	ALRALesserExtent: 'Menor extensión',
	ALRARiskRelatedToAssertion: 'Riesgo asociado',
	ALRAContributesToHigherInherentRisk: 'Riesgo asociado y contribuye al mayor riesgo inherente',

	// Assess inherent risk
	HigherRiskAssertionWithoutRisksThatContributesToTheHigherInherentRisk: 'La aseveración se identifica como un riesgo inherente más alto sin al menos un riesgo que contribuya al mayor riesgo inherente. Asocie los riesgos e identifique qué riesgos contribuyen al mayor riesgo inherente de la afirmación.',

	//MEST - Multi-entity account Execution Type selection listing
	account: 'Cuenta',
	taskByEntity: 'Tarea por entidad',
	bodyInformation: 'Debe hacer click en Importar contenido a continuación para guardar los cambios.',

	/*user search component*/
	seachInputRequired: 'Entrada de búsqueda requerida',
	nameOrEmail: 'Nombre o correo electrónico',
	emailForExternal: 'Correo electrónico',
	noRecord: 'No se han encontrado resultados',
	userSearchPlaceholder: 'Ingrese el nombre o el correo electrónico y presione enter para ver los resultados.',
	userSearchPlaceholderForExternal: 'Ingrese el correo electrónico y presione enter para ver los resultados.',
	clearAllValues: 'Borrar todos los valores',
	inValidEmail: 'Por favor, introduzca un correo electrónico válido',

	//reactive frame
	maxTabsLocked: 'Se ha alcanzado el número máximo de pestañas permitidas. Desancla y cierra una de las pestañas para abrir una nueva.',
	openInNewTab: 'Abrir en una nueva pestaña',
	unPinTab: 'Pestaña Desanclar',
	pinTab: 'Pestaña Anclar',
	closeDrawer: 'Cerrar cajón',
	minimize: 'Minimizar',

	accountHeader: 'Cuentas',
	sCOTSummaryAccountNoDataLabel: 'Cada SCOT deberá estar relacionado con al menos una cuenta o divulgación significativa. Seleccione una cuenta o divulgación significativa existente para relacionarla con este SCOT',
	sCOTSummaryNoDataLabel: 'No se han creado SCOTs',
	scotSearchNoResultsFound: 'No results found',
	scotSummary225TabsName: {
		[0]: {
			label: 'Mostrar por cuenta'
		},
		[1]: {
			label: 'Mostrar por SCOT'
		}
	},

	// Display Account Balances
	currentPeriodAccountBalance: 'Saldo de la cuenta del período en curso: ',
	priorPeriodAccountBalance: 'Saldo de la cuenta del período anterior: ',

	ALRANoResults: 'No se han encontrado resultados. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	associatedRomsCount: 'Número total de riesgos asociados: {0}',
	alraMessage: "La respuesta de designación de cuenta no está alineada con la designación en'Editar cuenta y divulgación' ",
	estimateCategoryResponseNotAlignedToDesignation: 'La respuesta de la categoría de Estimación no está alineada con la designación en "Editar estimación"',


	// Analytics Overview
	analyticsOverviewTitle: 'Descripción general de Analytics',
	noSignificantAccountRecords: 'No se han creado cuentas significativas.',
	noSignificantAccountMapped: 'No se han asignado cuentas significativas a la entidad seleccionada.',
	noLimitedAccountMapped: 'No se han asignado cuentas limitadas a la entidad seleccionada.',
	openAnalyticDocumentation: 'Abrir documentación analítica',
	openLimitedRiskAccountDocumentation: 'Abrir documentación de cuenta de riesgo limitado',
	associatedSCOTs: 'SCOTS asociados: ',
	analysisPeriodLabel: 'Fecha de análisis {0}',
	analysisPeriodChangeLabel: '% de variación con respecto a {0}',
	xTELabel: 'xTE',
	risksLabel: 'Riesgos',
	comparativePeriod1: 'Fecha comparativa 1 {0}',
	analysisPeriodTitle: 'Fecha de análisis',
	analysisPeriodChangeTitle: '% de cambio desde',
	comparativePeriodTitle: 'Fecha comparativa 1',
	noAccountAvailable: 'No hay cuenta disponible',

	// Estimates
	titleEstimateCategory: 'Categoría de estimación',
	titleRisks: 'Riesgos',

	voiceNoteNotAvailable: 'La nota de voz y la grabación de pantalla no están disponibles en la vista de cajón. Cambie a la vista de pantalla completa para usar estas funciones.',

	financialStatementType: {
		[1]: {
			label: 'Activo'
		},
		[2]: {
			label: 'Activo corriente'
		},
		[3]: {
			label: 'Activo no corriente'
		},
		[4]: {
			label: 'Pasivo'
		},
		[5]: {
			label: 'Pasivo corriente'
		},
		[6]: {
			label: 'Pasivo no corriente'
		},
		[7]: {
			label: 'Capital'
		},
		[8]: {
			label: 'Ingresos'
		},
		[9]: {
			label: 'Gasto'
		},
		[10]: {
			label: 'Ingresos/(Gastos) No Operativos'
		},
		[11]: {
			label: 'Otro Resultado Integral (OCI)'
		},
		[12]: {
			label: 'Otro'
		},
		[13]: {
			label: 'Tipo de cuenta'
		},
		[14]: {
			label: 'Subtipo de cuenta'
		},
		[15]: {
			label: 'Clase de cuenta'
		},
		[16]: {
			label: 'Subclase de cuenta'
		},
		[17]: {
			label: 'Neto (Ingresos) / Gastos'
		}
	},
	accountTypes: {
		[1]: {
			label: 'Cuenta significativa'
		},
		[2]: {
			label: 'Cuenta de riesgo limitado'
		},
		[3]: {
			label: 'Cuenta insignificante'
		},
		[4]: {
			label: 'Otra cuenta'
		},
		[5]: {
			label: 'Revelación importante'
		}
	},
	noClientDataAvailable: 'No hay datos disponibles',

	analysisPeriod: 'Fecha de análisis',
	comparativePeriod: 'Fecha comparativa',
	perchangeLabel: '% de cambio',

	entityCreateAccountLabel: 'Crear una cuenta y divulgación',
	insignificantAccount: 'Cuenta insignificante',
	noAccountRecords: 'No se han identificado cuentas',
	noAccountsForEntity: 'No se asignan cuentas ni divulgaciones a la entidad seleccionada.',
	noLimitedRiskAccountRecords: 'No hay cuentas de riesgo limitado disponibles.',
	createAccount: 'Crear una cuenta',
	createDocument: 'Crear documento',
	noAccountResults: 'No se identificaron cuentas.',
	createGroupInvolvementDocument: 'Crear formulario de participación',
	chooseVersionsToCompare: 'Elija la versión para comparar',
	noTrackChangesOption: 'Versiones sin cambios de control disponibles',
	trackChangesDefaultMessage: 'Seleccione una versión del menú desplegable "Elegir versión para comparar" para continuar.',
	whichRiskContributeToHigherRisk: '¿Qué riesgo(s) contribuyen(n) a la aseveración de mayor riesgo?',

	//multi-entity Entity List
	createMultiEntity: 'Nueva entidad',
	editMultiEntity: 'Editar entidad',
	noEntitiesAvailableCreateNewLink: 'Haga clic aquí',
	noEntitiesAvailable: 'No se ha creado ninguna entidad. {noEntitiesAvailableCreateNewLink} para empezar',
	noEntitiesFound: 'No se han encontrado resultados',
	createMultiEntityProfile: 'Crear perfil de entidad',

	createEntity: 'Crear entidad',
	includeEntities: 'La lista de MEST debe incluir al menos una entidad. {createEntity} para empezar.',
	//multi-entity table
	multiEntityCode: 'Índice estándar de la entidad',
	multiEntityName: 'Nombre de la entidad',
	multiEntityGroup: 'Grupo de entidades',
	multiEntityActions: 'Acciones',
	relateMultiEntityUngrouped: 'Desagrupar',
	selectAll: 'Seleccionar todo',
	entitiesSelected: 'Entidades seleccionadas',
	entitySelected: 'Entidad seleccionada',
	meNoEntitiesAvailable: 'No hay entidades disponibles',
	meSwitchEntities: 'Cambiar entidades',
	meSelectEntity: 'Seleccionar entidad',
	allEntities: 'Todas las entidades',
	noEntitiesIdentified: 'No se identificaron entidades',
	contentDeliveryInProcessMessage: 'Entrega de contenido en curso. El contenido puede tardar hasta diez minutos en entregarse.',
	importContent: 'Importar contenido',
	profileSubmit: 'Envío de perfil',
	importPSPs: 'Importar PSP',
	contentUpdateInsufficienRolesLabel: 'Roles insuficientes para actualizar el contenido. Trabaje con un administrador del compromiso para obtener derechos suficientes.',
	// MEST Switcher
	meEntitySwitcher: 'Conmutador de entidades',
	//Error Boundary
	errorBoundaryMessage: 'Ha ocurrido un error. Por favor, actualiza la página e intenta de nuevo. Si el problema persiste, contacta al servicio de asistencia.',
	sectionName: 'Nombre de la sección',
	maxLength: 'El texto no puede exceder {number} de caracteres.',
	required: 'Obligatorio',
	yearAgo: 'hace un año',
	yearsAgo: 'hace años',
	monthAgo: 'hace un mes',
	monthsAgo: 'hace meses',
	weekAgo: 'hace una semana',
	weeksAgo: 'hace semanas',
	daysAgo: 'hace días',
	dayAgo: 'hace un día',
	today: 'Hoy',
	todayLowercase: 'hoy',
	yesterday: 'Ayer',
	approaching: 'acercándose',

	associatedToInherentRiskFactor: 'Asociado al factor de riesgo inherente',

	createMissingDocument: 'Crear el documento que falta',
	createMissingDocumentBody: 'Haga clic en "Confirmar" para crear el documento para los elementos relacionados que actualmente carecen de documento.',
	documentCreationSuccessMsg: 'Creación de documentos en curso. Actualiza la página para ver las actualizaciones.',

	noRisksRelatedToAssertion: 'No hay riesgos relacionados con esta aseveración de la cuenta.',

	noAssertionsRelatedToAccount: 'No hay aseveraciones relacionadas con esta cuenta',

	sharing: 'Compartiendo',

	risksUnrelatedToAccountAssertion: 'Riesgos no relacionados con la aseveración de cuenta',
	cantCompleteTask: 'Faltan firmas a los documentos asociados a la tarea. Abra la tarea, complete los documentos que faltan y, a continuación, vuelva a intentar Marcar como completado.',
	cantCompleteTasksTitle: 'No se puede marcar como completo',
	ok: 'De acuerdo',
	documentsEngagementShareLabel: 'Seleccione el documento dentro del compromiso con el que desea compartir',
	documentsEngagementCopyLabel: 'Seleccione el documento dentro del compromiso que desea copiar',
	lastModifiedon: 'Última modificación el',
	newAccountAndDisclosure: 'Nueva cuenta y divulgación',
	newAccountORDisclosure: 'Cuenta nueva o divulgación',

	externalDocuments: 'Documentos externos',
	noExternalDocumentsAvailable: 'No hay documentos externos disponibles',
	addExternalDocuments: 'Agregar documentos externos',
	relateExternalDocuments: 'Relacionar documentos externos',

	helixNotMappedToAccount: 'Los datos de EY Helix no están asignados a esta cuenta. Actualice el mapeo y vuelva a importar los datos para continuar.',
	trackChangesNotAvailableForSpecialBodyDisplayMessage: 'La funcionalidad de control de cambios no está disponible para las respuestas que se indican a continuación.',
	noDocumentRelatedObjectsApplicable: 'No es necesario asociar objetos a este formulario de flujo de trabajo guiado',
	helixViewerLoader: 'Cargando el visor Helix...',
	trackChangesViewDefaultMessage: 'Algunas respuestas no tienen la funcionalidad de control de cambios. Esto se indicará con el siguiente mensaje en los detalles de la actividad subyacente: "La funcionalidad de marcas de revisión de cambios no está disponible para las respuestas que se indican a continuación". Por lo tanto, la falta de notificación de los cambios a continuación no indica que se hayan realizado cambios.',

	//Relate task modal
	relateTasksTitle: 'Relacionar tareas',
	taskLocationLabel: 'Ubicación de la tarea',
	relateTaskInstructionalText: 'Agregue o elimine tareas con las que el documento debe estar relacionado. Si el documento es una evidencia, al eliminarlo de la última tarea se moverá a archivos temporales.',
	noResultFound: 'No se han encontrado resultados.',
	relatedTaskCounter: '{0} tarea',
	relatedTasksCounter: '{0} tareas',
	onlyShowRelatedTasks: 'Mostrar solo tareas relacionadas',
	relateTaskName: 'Nombre de la tarea',
	relateTaskType: 'Tipo',

	/*Relate Entities*/
	relateEntitiesTitle: 'Relacionar entidades',
	relateEntitiesSearchPlaceholder: 'Entrar para buscar por nombre',
	relateEntitiesName: 'Nombre de la entidad',
	relateEntitiesIndex: 'Índice estándar de la entidad',
	relatedEntitiesCounter: '{0} entidades',
	relatedEntityCounter: '{0} entidades',
	onlyShowRelatedEntities: 'Mostrar solo entidades relacionadas',
	entity: 'Entidad',

	step01: 'Paso 01',
	step02: 'Paso 02',
	shareActivityStep1Description: 'Seleccionar compromiso y documento',
	shareActivityStep2Description: 'Elegir qué respuestas de documento deben reemplazar a las demás',
	documentsShareLabel: 'Comparta la respuesta del documento seleccionado a continuación con el resto de los documentos.',
	selectedActivity: 'Actividad seleccionada',
	sharedHoverLabel: 'Esta actividad ya se comparte con otras actividades. Compartir esta actividad, sincronizará las respuestas de esta actividad con toda la actividad compartida',
	noAssertionsRelatedLabel: 'No hay afirmaciones relacionadas.',

	// Bulk mark complete:
	bulkMarkCompleteInstructionalText: 'Todos los documentos deben estar firmados por al menos un preparador y un revisor para marcar la actividad como completada..',
	bulkMarkCompleteEngagementColumn: 'Compromiso',
	bulkMarkCompleteDocumentsMissingSignOffs: 'Faltan aprobaciones. Haga clic en {bulkMarkCompleteMissingSignOffsClickableText} para firmar',
	bulkMarkCompleteMissingSignOffsClickableText: 'aquí',
	bulkMarkCompleteNoAccessToEngagement: 'No tienes acceso al compromiso en el que se encuentra esta tarea',
	bulkMarkCompleteInProgressMessage: 'El proceso está en curso. Puede tardar hasta diez minutos. Por favor, actualice para obtener actualizaciones',
	bulkMarkCompleteRelatedDocumentsModalTitle: 'Firmas de documentos',
	bulkMarkCompleteFilterUnreadyTasks: 'Mostrar solo las tareas con aprobaciones de documentos faltantes.',
	bulkMarkCompleteNotAllowedModalTitle: 'No se puede marcar como completo',
	bulkMarkCompleteNotAllowedModalDescription: 'Debe seleccionar al menos una tarea para marcarla como completada',
	bulkMarkCompleteRelatedDocumentsModalDescription: 'Para marcar como completado la tarea seleccionada, todos los documentos deben estar firmados por al menos un preparador y un revisor.',
	bulkMarkCompleteRelatedDocumentsModalRefreshSignoffs: 'Actualizar firmas y notas',
	selectedTaskCounter: '({0}) tarea seleccionada',
	selectedTasksCounter: '({0}) tareas seleccionadas',

	// Mark complete (old):
	markCompleteNotAllowedModalDescription: 'A los documentos asociados a la tarea relacionada les faltan firmas. Abra la tarea, complete los documentos que faltan y, a continuación, vuelva a intentar Marcar como completado',
	markCompleteInstructionalText: 'Todos los documentos deben ser firmados por al menos un preparador y un revisor para marcar la actividad como completada',

	// Adobe Analytics
	aaCookieConsentTitle: 'Bienvenidos a',
	aaCookieContentPrompt: '¿Desea permitir las cookies?',
	aaCookieConsentExplanation: '<p>Además de las cookies que son estrictamente necesarias para el funcionamiento de este sitio web, utilizamos los siguientes tipos de cookies para mejorar su experiencia y nuestros servicios: <cookies fuertes>funcionales</fuertes> para mejorar su experiencia (por ejemplo, recordar la configuración), <cookies fuertes>Rendimiento</fuertes> para medir el rendimiento del sitio web y mejorar su experiencia,  <cookies de publicidad/segmentación >strong</strong>, que son establecidas por terceros con los que ejecutamos campañas publicitarias y nos permiten proporcionarle anuncios relevantes para usted.</p><p>Revise nuestra <a target="_blank" href="https://www.ey.com/en_us/cookie-policy">política de cookies</a> para obtener más información.</p>',
	aaCookieConsentExplanationWithDoNotTrack: '<p>Además de las cookies que son estrictamente necesarias para el funcionamiento de este sitio web, utilizamos los siguientes tipos de cookies para mejorar su experiencia y nuestros servicios: <cookies fuertes>funcionales</fuertes> para mejorar su experiencia (por ejemplo, recordar la configuración), <cookies fuertes>Rendimiento</fuertes> para medir el rendimiento del sitio web y mejorar su experiencia,  <cookies > fuerte/de segmentación</strong>, que son establecidas por terceros con los que ejecutamos campañas publicitarias y nos permiten proporcionarle anuncios relevantes para usted.</p><p>Hemos detectado que ha habilitado la configuración No rastrear en su navegador; como resultado, las cookies publicitarias/de orientación se desactivan automáticamente.</p><p>Revise nuestra <a target="_blank" href="https://www.ey.com/en_us/cookie-policy">política de cookies</a> para obtener más información.</p>',
	aaCookieConsentDeclineOptionalAction: 'Rechazo las cookies opcionales',
	aaCookieConsentAcceptAllAction: 'Acepto todas las cookies',
	aaCookieConsentCustomizeAction: 'Personalizar cookies',
	aaCookieConsentCustomizeURL: 'https://www.ey.com/en_us/cookie-settings',

	// Cookie Settings
	cookieSettings: {
		title: 'Configuración de cookies',
		explanation: 'Proporcione su consentimiento para el uso de cookies en ey.com y en la plataforma My EY. Seleccione uno o más de los tipos de cookies que se enumeran a continuación y, a continuación, guarde su(s) selección(es). Consulte la lista a continuación para obtener detalles sobre los tipos de cookies y su propósito.',
		emptyCookieListNotice: 'Las cookies de esta categoría no se utilizan en esta aplicación',
		nameTableHeader: 'Nombre de la cookie',
		providerTableHeader: 'Proveedor de cookies',
		purposeTableHeader: 'Finalidad de la cookie',
		typeTableHeader: 'Tipo de cookie',
		durationTableHeader: 'Duración de la cookie',
		formSubmit: 'Guardar mi selección',
		requiredCookieListTitle: 'Cookies requeridas',
		functionalCookieListTitle: 'Cookies funcionales',
		functionalCookieAcceptance: 'Acepto las siguientes cookies funcionales',
		functionalCookieExplanation: 'Cookies de funcionalidad, que nos permiten mejorar su experiencia (por ejemplo, recordando cualquier configuración que haya seleccionado).',
		performanceCookieListTitle: 'Cookies de rendimiento',
		performanceCookieAcceptance: 'Acepto las siguientes cookies de rendimiento',
		performanceCookieExplanation: 'Cookies de rendimiento, que nos ayudan a medir el rendimiento del sitio web y mejorar su experiencia. Al utilizar cookies de rendimiento, no almacenamos ningún dato personal y solo utilizamos la información recopilada a través de estas cookies de forma agregada y anónima.',
		advertisingCookieListTitle: 'Cookies de segmentación',
		advertisingCookieAcceptance: 'Acepto las cookies publicitarias/de segmentación que se indican a continuación',
		advertisingCookieExplanation: 'Cookies publicitarias/de orientación, que utilizamos para rastrear la actividad y las sesiones del usuario para que podamos ofrecer un servicio más personalizado, y (en el caso de las cookies publicitarias) que son establecidas por los terceros con los que ejecutamos campañas publicitarias y nos permiten proporcionar anuncios relevantes para usted.',
		doNotTrackNotice: 'Hemos detectado que ha habilitado la configuración No rastrear en su navegador; Como resultado, las cookies publicitarias/de orientación se desactivan automáticamente.',
	},
	accountFormsMissing: 'Faltan formularios de cuenta para {0} cuenta(s)',
	createAccountForms: 'Crear formulario(s) de cuenta',
	createAccountFormsDescription: 'Haga clic en "Confirmar" para crear el documento para los elementos relacionados a los que actualmente les falta un documento.',
	createMissingDocuments: 'Cuentas relacionadas a las que actualmente les faltan documentos',
	accountDocumentsCreated: 'Entrega de contenido en curso. La entrega del contenido puede tardar hasta diez minutos.',

	evidenceMissingPICSignoffs: 'Faltan la (s) firma (s) del PIC en la evidencia.',
	evidenceMissingEQRSignoffs: 'Faltan la(s) firma (s) del EQR en la evidencia',
	evidenceMissingPICEQRSignoffs: 'Falta(s) la(s) firma(s) del PIC y/o EQR en la evidencia',
	evidenceMissingPICSignoffRequirements: 'Falta(n) el(los) requisito(s) de firma del PIC en la evidencia',
	evidenceMissingEQRSignoffRequirements: 'Falta(n) el(los) requisito(s) de firma del EQR en la evidencia',
	evidenceMissingPICEQRSignoffRequirements: 'Falta(n) el(los) requisito(s) de firma del PIC y/o EQR en la evidencia',
	evidenceMissingSignoffs: 'Falta firma en evidencia',

	// Bulk task relate
	bulkTaskRelateFailureMessage: 'Algunos de los documentos seleccionados no se han podido asociar a la(s) tarea(s) seleccionada(s).',
	/*endoflabels*/
	evidenceMissingPreparerOrReviwerSignoffs: 'Carga de documentos: faltan firmas del preparador o del revisor',

	manageITProcess: 'Gestionar el proceso de TI',
	manageITRisk: 'Gestione el riesgo tecnológico',
	manageITControl: 'Gestione el control de TI',
	manageITSP: 'Administrar ITSP',
	manageITApp: 'Administrar aplicaciones de TI',
	manageSCOT: 'Gestionar los SCOTs',
	addAresCustomDescription: 'Seleccione el tipo de contenido que se agregará a este formulario de flujo de trabajo guiado, ingrese los detalles y haga clic en guardar.',

	documentImportSuccess: '{0} ha sido creado con éxito. El contenido puede tardar hasta diez minutos en entregarse.',
	documentImportFailure: 'Error en la creación del documento. Actualice o vuelva a intentarlo después de un tiempo. Si el problema persiste, póngase en contacto con Servicio de Soporte.',
	formNotAvailable: 'No se encontró ningún formulario de lienzo coincidente.',
	selectTask: 'Seleccione Tarea para relacionarla con la guía',
	canvas: 'Canvas',
	selectEngagement: 'Seleccionar compromiso',

	//Modal Manage sub-scope
	manageSubScopeTitle: 'Administrar Sub alcances',
	manageSubScopeDescription: 'Cree nuevos sub alcances o edite o elimine los sub alcances existentes a continuación.',
	addSubScope: 'Agregar un sub alcance',
	subScopeName: 'Nombre del sub alcance',
	knowledgeScope: 'Alcance del conocimiento',
	subScopeAlreadyExist: 'El nombre del sub alcance ya existe',
	subScopes: 'Sub alcances',
	notAvailableSubScopes: 'No hay sub alcances disponibles.',
	SubScopeNameValidation: 'La longitud del nombre del sub alcance supera los 255 caracteres.',

	//CRA Summary
	manageAccount: 'Administrar cuenta',
	newAccount: 'Nueva cuenta',

	noRelatedObjectITProcessFlow: 'No hay ningún objeto relacionado. Relaciona un objeto para empezar.',

	//Add New Flow Chart Steps
	flowChartNewSteps: {
		newStepTitle: 'Nuevo paso',
		placeholderText_1: 'Ingrese los detalles del paso a continuación y seleccione',
		placeholderText_2: "'Guardar y cerrar' ",
		placeholderText_3: ' para terminar. Para crear otro paso, seleccione',
		placeholderText_4: "'Guardar y crear otro'. ",
		columnLabel: 'Columna (obligatorio)',
		counterOf: 'de',
		counterChar: 'Caracteres',
		stepNameLabel: 'Nombre del paso (obligatorio)',
		errorMsgStepNameRequired: 'El nombre del paso es obligatorio',
		stepDescLabel: 'Descripción del paso (obligatorio)',
		stepDescPlaceholder: 'Introduzca la descripción del paso',
		errorMsgStepDescRequired: 'Se requiere una descripción del paso',
		required: 'Obligatorio',
		errorMsgStepDescExceedMaxLength: 'La descripción del paso supera el número máximo de caracteres permitidos',
		buttonCancel: 'Cancelar',
		buttonSaveAndClose: 'Guardar y cerrar',
		buttonSaveAndCreateAnother: 'Guardar y crear otro',
		errorMsgColumnRequired: 'La columna es obligatoria',
		headerNameForWCGW: 'Nombre de WCGW',
		headerNameForControl: 'Nombre del Control',
		headerNameForITApp: 'Nombre de la aplicación de TI',
		headerNameForServiceOrganisation: 'Nombre de la organización de servicio',
		relateLabelForWCGW: 'Relacionar WCGWs',
		relateLabelForControl: 'Relacionar Controles',
		relateLabelForITApp: 'Relacionar aplicaciones de TI',
		relateLabelForServiceOrganisation: 'Relacionar organizaciones de servicios',
		designEffectiveness: 'Eficacia del diseño',
		testing: 'Pruebas',
		lowerRisk: 'Menor riesgo',
		wcgwNoRowsMessage: 'Ningún WCGW ha sido relacionado. Haga clic {0} para empezar',
		controlNoRowsMessage: 'No se han relacionado controles. Haga clic {0} para empezar',
		itAppNoRowsMessage: 'No se han relacionado aplicaciones de TI. Haga clic {0} para empezar',
		serviceOrganisationNoRowsMessage: 'No se ha relacionado a ninguna organización de servicios. Haga clic {0} para empezar',
		wgcwTabLabel: 'WCGWs',
		controlsTabLabel: 'Controles',
		itAppsTabLabel: 'Aplicaciones informáticas',
		serviceOrganisationTabLabel: 'Organizaciones de servicios',
		connectionSuccessMessage: 'Conexión creada correctamente.',
		connectionFailedMessage: 'No se pudo establecer una conexión. Por favor, inténtelo de nuevo.',
		selfConnectFailMessage: 'La fuente y el destino no pueden ser lo mismo.',
		connectionDuplicateMessage: 'La conexión ya existe.',
		connectionDeleteSuccessMessage: 'La conexión se ha eliminado correctamente.',
		connectionDeleteFailMessage: 'No se pudo eliminar la conexión. Por favor, inténtelo de nuevo.',
		editStepFailMessage: 'No se pudo editar el paso. Por favor, inténtelo de nuevo.',
		flowchartStepGetByIdFailMessage: 'Paso inválido, actualice y vuelva a intentarlo.',
		flowchartStepGetByIdFailureMessage: 'Este paso del diagrama de flujo ya no está disponible. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Soporte si el error persiste.',
		newStepFailureMessage: 'No se pudo crear un nuevo paso. Por favor, inténtelo de nuevo.',
		deleteConnector: 'Eliminar conector',
		edgeConnectorOptions: 'Opciones de Conector',
		edgeStartPoint: 'Punto de partida',
		edgeEndPoint: 'End Point',
		relateDocumentToFlowchartStepError: 'La operación no se puede completar en este momento. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
		relateDocumentsOrObjects: 'Relacionar documentos u objetos',
		thisstep: 'a este paso'
	},

	flowChartWCGW: {
		wcgwsCounter: '{0} WCGW',
		wcgwCounter: '{0} WCGW',
		headerName: 'Relacionar WCGWs',
		showOnlyRelatedText: 'Mostrar solo relacionados',
		noResultsFound: 'No se han encontrado resultados'
	},

	flowchartITAPPSO: {
		showOnlyRelatedText: 'Mostrar solo relacionados',
		noResultsFound: 'No se han encontrado resultados'
	},

	flowChartITApplication: {
		itApplicationsCounter: '{0} aplicaciones informáticas',
		itApplicationCounter: '{0} aplicación informática',
		headerName: 'Relacionar aplicaciones de TI',
		columnName: 'Nombre de la aplicación de TI',
		noDataFound: 'No se han encontrado aplicaciones informáticas'
	},

	flowChartITSO: {
		itSOsCounter: '{0} Organizaciones de servicios',
		itSOCounter: '{0} Organización de servicios',
		headerName: 'Relacionar organizaciones de servicios',
		columnName: 'Nombre de la organización de servicio',
		noDataFound: 'No se han encontrado organizaciones de servicio'
	},

	flowChartControl: {
		controlsCounter: 'Relacionar Controles {0}',
		headerName: 'Relacionar Controles',
		showOnlyRelatedText: 'Mostrar solo relacionados',
		noResultsFound: 'No se han encontrado resultados',
		noWCGWs: 'No se ha creado ningún control'
	},

	relateSCOT: {
		header: 'Relacionar SCOTs',
		estimate: 'Estimar',
		scotsCounter: '{0} SCOTs',
		scotCounter: '{0} SCOT',
		headerName: 'Nombre de SCOT',
		showOnlyRelated: 'Mostrar solo relacionados',
		noResultsFound: 'No se han encontrado resultados',
		noScotCreated: 'No se han creado SCOTs en el compromiso'
	},

	relatedStepObjects: {
		relatedWCGWs: 'WCGWs Relacionados',
		relatedControls: 'Controles relacionados',
		relatedDocuments: 'Evidencias relacionadas',
		relatedITApplications: 'Aplicaciones informáticas relacionadas',
		relatedSOs: 'Organizaciones de servicios relacionados'
	},

	flowchartEditSteps: {
		nextStep: 'Siguiente paso',
		previousStep: 'Paso previo',
		editStepTitle: 'Editar paso',
		editPlaceholderText_1: 'Edite los pasos, los detalles y los objetos relacionados a continuación. Click',
		editPlaceholderText_2: "'Guardar y cerrar' ",
		editPlaceholderText_3: 'para guardar y volver al diagrama de flujo. Al navegar a otros pasos con las opciones que se indican a continuación, se guardarán las actualizaciones.',
		draftEditStepFailMessage: 'No se puede crear el paso del diagrama de flujo. Actualice la página y vuelva a intentarlo. Póngase en contacto con Servicio de Soporte si el error persiste.',
	},

	flowChartStepmoreMenu: {
		edit: 'Editar',
		delete: 'Borrar'
	},

	relateEstimate: {
		scot: 'SCOT',
		strategy: 'Estrategia SCOT',
		type: 'Tipo',
		noSCOT: 'Cada estimación debe estar relacionada con al menos un SCOT. Clic',
		noSCOTmsg: ' Para empezar',
		estimate: 'Estimar',
		routine: 'Rutina',
		nonRoutine: 'No rutinario',
		notSelected: 'No seleccionado',
		relateSCOTs: 'Relacionar SCOTs',
		remove: 'eliminar',
		noEstimate: 'No hay estimación disponible',
	},

	flowChartStepIcons: {
		wcgws: 'WCGWs',
		controls: 'Controles',
		iTApps: 'Aplicaciones informáticas',
		serviceOrganisations: 'Organizaciones de servicios'
	},

	flowChartStepIcon: {
		wcgw: 'WCGW',
		control: 'Control',
		iTApp: 'Aplicación de TI',
		serviceOrganisation: 'Organización de servicios',
		evidence: 'Evidencia'
	},

	flowChartErrorMessage: {
		stepOutsideOfTheColumns: 'Steps cannot be placed outside of the flowchart area',
		stepBetweenTheColumns: 'Steps cannot be placed between the columns',
		stepOnTopOrTooCloseToAnotherStep: 'Steps cannot be placed on top of the other steps'
	},

	//Delete Flow Chart Steps
	flowChartStepsDelete: {
		deletestep: 'Eliminar paso',
		deleteStepModalMessage: '¿Estás seguro de que quieres eliminar este paso? Todos los WCGW, controles, aplicaciones de TI, organizaciones de servicios y evidencia relacionadas con el paso eliminado no estarán relacionados.',
		cannotBeUndone: 'Contratos de clientes nuevos o renovados',
		deleteStepFailMessage: 'No se pudo eliminar el paso. Por favor, inténtelo de nuevo',
		deleteDraftStepErrorMessage: 'El paso creado en borrador no se eliminó. Para eliminar este paso, seleccione el paso y vuelva a realizar la acción de eliminación.',
	},
	notEntered: 'No ingresado',
	estimateCategory: 'Categoría de estimación',
	noResultsFoundWithPeriod: 'No se encontraron resultados',
	noEstimateAvailable: 'No hay estimación disponible',
	noRelatedObject: 'No hay objeto relacionado.',
	relateAnObject: 'Relacionar un objeto',
	copyrightMessage: 'Copyright © <año> todos los derechos reservados',
	leadsheet: 'Hoja principal',
	controlName: 'Nombre del Control',
	noControlAvailable: 'No hay control disponible',
	independenceError: 'Todas las respuestas incompletas deben completarse antes de enviar la independencia.',
	riskTypeNotAssociated: 'El riesgo recién agregado no coincide con los tipos de riesgo permitidos y, por lo tanto, no aparece a continuación. Agregue otro riesgo del tipo permitido o seleccione de la lista a continuación',
	accountsAndRelatedEstimates: 'Cuentas y estimaciones relacionadas',
	noEstimatesAssociated: 'No hay estimaciones asociadas',
	noAssertionsAvailable: 'No hay aseveraciones disponibles',
	noAccountsOrDisclosuresAvailable: 'No hay cuentas ni divulgaciones disponibles',

	relateEstimateToRisk: {
		riskType: 'Tipo de riesgo',
		risk: 'Riesgo',
		hasestimate: "Has estimate?",
		accounts: 'Cuentas',
		isItRelevant: '¿Es relevante?',
		assertions: 'Aseveraciones',
		invalidRiskParentRiskErrMsg: 'Registro no encontrado. Actualice la página para continuar.',
		noEstimate: 'No hay estimación disponible',
		invalidRelateRiskOrEstimateRelationErrMsg: 'El objeto ya ha sido relacionado. Actualice la página para continuar.',
		invalidUnRelateRiskOrEstimateRelationErrMsg: 'El objeto ya ha sido desvinculado. Actualice la página para continuar.'
	},

	savingChanges: 'Guardar cambios',
	showEstimateAccountsWithoutEstimates: 'Mostrar cuentas de estimación sin estimaciones',
	showEstimateSCOTsWithoutEstimates: 'Mostrar SCOTs de estimación sin estimaciones',
	manageSCOTs: 'Gestionar los SCOTs',
	sCOTsAndRelatedEstimates: 'SCOTs y estimaciones relacionadas',
	relateEstimateToRiskNoDataMessage: 'No hay registros disponibles, relacione al menos una cuenta y aseveración con un riesgo asociado, si corresponde.',
	maps: 'Mapas',
	mapsUpbutSomeLoadingErrorMessage: 'Se ha producido un error técnico que impide que la función de mapas funcione. Actualice la página y vuelva a intentarlo.',
	mapsDownErrorMessage: 'La función Mapas no está disponible temporalmente. Actualice la página y vuelva a intentarlo. Si este mensaje persiste, póngase en contacto con el Servicio de Soporte.',
	financialStatements: 'Estados Financieros',
	serviceGatewayAutomation: 'Puerta de enlace de servicios y automatización',
	priorPeriodCategory: 'Categoría de período anterior',
	relatedAccountWithColon: 'Cuentas relacionadas: ',
	noRelatedAccount: 'No hay cuentas relacionadas',
	noRetionaleAvailable: 'No se tiene justificación',
	leftNavIconApprovals: 'firmas',
	editDuplicateSectionHeader: 'Edite los detalles de la sección y haga clic en Guardar.',

	relatedEvidences: 'Relacionar evidencias',
	relatedEvidencesInstruction: 'Relacione una evidencia desde este compromiso.',
	relatedTemporaryFilesInstruction: 'Relacione un documento temporal desde este compromiso.',
	noDataLabel: 'No se han encontrado datos',
	editDuplicateSection: 'Editar sección',
	showOnlyRelated: 'Mostrar solo relacionados',
	aiChatbot: 'EYQ Assurance Knowledge',
	StEntityNoRecords: 'No se encuentran asignadas cuentas ni divulgaciones a la entidad seleccionada.',
	versionLabel: 'Versión',
	relatedEstimates: 'Estimaciones relacionadas',
	viewEvidenceRelatedToBody: 'View evidence related to the body',
	selectHeaderFromRail: 'Select a header from left navigation pane to proceed',
	manageITProcesses: 'Gestione los procesos de TI',
	rationaleForLR: 'Justificación de la cuenta de riesgo limitado',
	rationaleForInsignificant: 'Justificación de la cuenta insignificante',
	rationalIsMissing: 'Falta la justificación.',
	craSummaryText1: 'Cada cuenta o divulgación significativa debe tener al menos una aseveración relacionada. Clic',
	scotDetails223: {
		relatedAccounts: 'Cuentas relacionadas',
		scotType: 'Tipo',
		manageScot: 'Gestionar los SCOTs',
		editScot: 'Editar SCOTs',
		scotNotAvailableMessage: 'Los SCOT no están disponibles para este documento',
		relatedScotNotAvailableMessage: 'No hay SCOT relacionado. Relacionar un SCOT desde la página de atributos para empezar',
		risksDocumented: 'Riesgos documentados en este WT',
		risksAvailableHeader: 'Sí',
		risksNotAvailableHeader: 'No',
		viewRelatedRisks: 'Ver riesgos relacionados',
		noRelatedAccountsMessage: 'No hay cuentas relacionadas'
	},

	scotDetails226: {
		noscotsidentified: 'No SCOTs have been identified'
	},

	scotDetails224: {
		riskRelatedWalkthrough: 'Riesgos relacionados en este Walkthrough',
		relatedToWTDocuments: 'Related to other WT documents',
		riskNotRelatedWalkthrough: 'Riesgos no relacionados en este Walkthrough',
		substantiveNotSufficient: 'Lo sustantivo no es suficiente',
		journalEntry: 'Entrada de diario',
		noDirectRiskSourcesAreAvailable: 'No hay riesgos relacionados',
		scotNotAvailableMessage: 'Los SCOT no están disponibles para este documento',
		relatedScotNotAvailableMessage: 'No hay SCOT relacionado. Relacionar un SCOT desde la página de atributos para empezar',
		relatedDocuments: 'Related documents',
		risk: "Risk:",
		riskSpecialCircumstances: 'Risk special circumstances',
		relateInstructionText: "This risk has been identified in another SCOT.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		unrelateInstructionText: "This risk has been identified in the critical path of another walkthrough.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		concurrencyErrorMessage: "This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.",
	},
	ipe: 'IPE',
	scotSummary198: {
		noAccountsDisclosureCreated: 'No se han creado cuentas o divulgaciones significativas',
		noScotEstimateIdentified: 'No se han identificado SCOTs ni estimaciones.',
		noScotIdentified: 'No se identificó a ningún SCOT',
		scots: 'SCOTs',
		estimates: 'Estimaciones',
		errorMessage: 'Esta acción no se pudo completar. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Soporte si el error persiste.',
		noResultsFound: 'No se han encontrado resultados',
		searchAccounts: 'Buscar cuentas',
		searchScotEstimates: 'Buscar SCOTs/estimaciones',
		accounts: 'Cuentas',
		scotsOrEstimate: 'SCOTs/Estimaciones',
		accountNotRelatedToScotValidation: 'Cada cuenta o divulgación significativa deberá estar relacionada con al menos un SCOT. Seleccione una casilla de verificación para relacionar un SCOT relevante con esta cuenta significativa o divulgación significativa.',
		scotNotRelatedToAccountValidation: 'Cada SCOT deberá estar relacionado con al menos una cuenta o divulgación significativa. Seleccione una casilla de verificación para relacionar este SCOT con una relevante Cuenta significativa o Divulgación significativa.',
		showValidations: 'Mostrar validaciones',
	},
	scotSummary225: {
		relatedScots: 'SCOTs relacionados',
		relatedAcconts: 'Cuentas relacionadas',
		scotListHeader: 'SCOTs y estimaciones',
		noScotsMessage: 'Cada cuenta o revelación significativa deberá estar relacionada con al menos un SCOT. Seleccione un SCOT existente para que se relacione con esta cuenta significativa o divulgación significativa',
		noAccountsMessage: 'No significant accounts or disclosures have been created.',
		noAccountsAvailableOnSearch: 'No results found',
		relateAccounts: 'Relate accounts and disclosures',
		noAccountsCreated: 'No accounts have been created',
		noScotsCreated: 'No SCOTs have been created',
		relateScots: 'Relate SCOTs',
	},
	bodyUnavailableInCCP: 'Este contenido no está disponible a través del Portal de Cliente de EY Canvas.',
	pyBalance: 'Saldo PY',
	cyBalance: 'Saldo CY',
	designationNotDefined: 'Designación no definida',
	controlRiskAssessment: 'Evaluación de riesgos de control',
	first: 'Primero',
	noImportedTrialBalance: 'No hay balance de comprobación importado.',
	placeHolderMessageWhenHelixMappingIsTrue: 'Haga clic en {0} para relacionar un nuevo analizador.',
	documentPrintSuccess: 'Document print in progress. It may take up to ten minutes. Once completed, the print will be added to the temporary files.',
	documentPrintError: 'Error en la impresión del documento. Actualice o vuelva a intentarlo después de un tiempo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	backToEvidenceWarningMessage: 'This action could not be completed. Please refresh and try again. If issue persists, please contact the Help Desk.',
	rationaleMissingForLR: 'Each limited risk account shall have a rationale provided',
	rationaleMissingForIR: 'Each insignificant account shall have a rationale provided',
	craSummaryText2: ' Each account shall have designation determined. Click',
	contentDrivingEntity: 'Content driving entity',
	contentDrivingEntityPlaceholder: 'Content driving entity has not been selected',
	rationaleForPlaceholder: 'Provide rationale for this account designation',
	contentDrivingEntityRequired: 'Content driving entity (required)',
	refreshContentLayers: 'Refresh content layers',
	noAccessLabel: 'Unauthorized. Contact your administrator and try again.',
	copyForHelpDeskDetails: 'Copy for Help Desk details',
	copyForHelpDeskDetailsSuccess: 'Details copied to clipboard',

	//toast activity as guest user
	sharedGuidedworkflowEvidenceWarning: 'This is a shared Guided Workflow Activity. The objects and evidence exist in the original engagement and will not be added to this engagement upon unlink. See <a style="color: #467cbe" href="https://live.atlas.ey.com/#library/104?pref=20058/9/5" target="_blank">enablement here</a> for further details.',
	sharedGuidedworkflowResponseWarning: "This is a shared Guided Workflow Activity. The responses are being shared with other activities in the same workspace. View relationships by accessing the'Show relationships' menu item from this activity's summary section."
};

export const groupStructure = {
	createComponent: 'Nuevo componente',
	deleteComponent: 'Eliminar componente',
	manageComponents: 'Administrar componentes',
	emptyComponents: 'No se ha creado ningún componente. Cree un {newComponent} para empezar.',
	scope: 'Alcance',
	role: 'Rol',
	pointOfContact: 'Punto de contacto',
	linkRequest: 'Solicitud de enlace',
	instructions: 'Instrucciones',
	instructionsSent: 'Instrucciones enviadas',
	status: 'Estado',
	createComponentInstructionalText: "Introduzca los detalles del componente a continuación y seleccione <b>'Guardar y cerrar'</b> para finalizar. Para crear otro componente, seleccione <b>'Guardar y crear otro'</b>. ",
	componentName: 'Nombre del componente',
	region: 'Región',
	notUsingCanvas: 'No usará EY Canvas',
	referenceOnly: 'Solo referencia',
	saveAndCreateAnother: 'Guardar y crear otro',
	dueDate: 'Fecha de vencimiento',
	components: 'Componentes',
	allocation: 'Asignación',
	documents: 'Evidence',
	discussions: 'Discusiones',
	EDAP: 'EDAPs',
	siteVisits: 'Visitas al sitio',
	reviewWorkComponent: 'Revisar el trabajo realizado',
	other: 'Otro',
	significantUpdates: 'Actualizaciones importantes',
	executionComplete: 'Ejecución completa',
	gaRoleTypesLabel: [{
		id: 1,
		displayName: 'Primario'
	},
	{
		id: 2,
		displayName: 'Regional'
	},
	{
		id: 3,
		displayName: 'Componente'
	}
	],
	gaLinkStatusLabel: [{
		id: GALinkStatus.NotSent,
		displayName: 'Enviar'
	},
	{
		id: GALinkStatus.Sent,
		displayName: 'Enviado/No aceptado'
	},
	{
		id: GALinkStatus.ComponentNotUsingCanvas,
		displayName: 'No usará EY Canvas'
	},
	{
		id: GALinkStatus.ReferenceOnly,
		displayName: 'Solo referencia'
	},
	{
		id: GALinkStatus.Accepted,
		displayName: 'Aceptado'
	},
	{
		id: GALinkStatus.Rejected,
		displayName: 'Rechazado'
	},
	{
		id: GALinkStatus.Unlinked,
		displayName: 'Desvinculado'
	},
	{
		id: GALinkStatus.Pending,
		displayName: 'Enviado/No aceptado'
	}
	],
	notAvailable: 'no disponible',
	search: labels.searchPlaceholder,
	noResultsFound: 'No se han encontrado resultados.',
	noComponentsFound: 'No se encontraron componentes.',
	contentSwitcher: [{
		id: gaRoleTypes.primary,
		displayName: 'Primario'
	},
	{
		id: gaRoleTypes.component,
		displayName: 'Componente'
	},
	{
		id: gaRoleTypes.regional,
		displayName: 'Región'
	}
	],
	gaRegionTypesLabel: {
		id: gaRegion.notApplicable,
		displayName: 'No aplicable'
	},
	//TODO: To be removed
	pointOfContactValues: [{
		id: pointOfContactTypes.EYcontact,
		displayName: 'Contacto de EY'
	},
	{
		id: pointOfContactTypes.externalContact,
		displayName: 'Contacto externo'
	}
	],
	saveAndClose: labels.modalSaveAndClose,
	cancelBtn: labels.modalCancelTitle,
	gaScopesValues: [{
		id: gaScopeType.full,
		displayName: 'Full'
	},
	{
		id: gaScopeType.specific,
		displayName: 'Específico'
	},
	{
		id: gaScopeType.specifiedAuditProcedures,
		displayName: 'Procedimientos específicos'
	},
	{
		id: gaScopeType.review,
		displayName: 'Revisión'
	}
	],
	edit: labels.edit,
	delete: labels.delete,
	tooltipIcon: 'Seleccione esta opción si el componente no utiliza EY Canvas para recibir instrucciones de auditoría de grupo y enviar entregables entre oficinas.',
	tooltipReferenceIcon: 'Los componentes designados como <b>Referencia solamente</b> se utilizan únicamente con fines organizativos. Estas interacciones de componentes no recibirán solicitudes de vínculos, instrucciones ni tareas, y esta participación de equipo principal tampoco recibirá una tarea de grupo.',
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	modalConfirmBtnLabel: labels.confirmLabel,
	clear: 'claro',
	clearUpper: labels.clear,
	nameOrEmail: 'Ingrese el correo electrónico del punto de contacto de EY',
	editComponent: 'Editar componente',
	editComponentInstructionalText: "Edite los detalles del componente a continuación y seleccione <b>\'Guardar\'</b> para finalizar. ",
	linkAlreadyAcceptedInfo: 'Solo se puede editar el campo de correo electrónico, ya que la solicitud de enlace ya se ha enviado al equipo de componentes.',
	sendAll: 'Enviar todo',
	send: 'Enviar',
	resend: 'Reenviar',
	scopeAndStrategy: 'Alcance y estrategia',
	execution: 'Ejecución',
	conclusion: 'Conclusión',
	reportingForms: 'Formularios de información',
	manageGroupPermission: 'No tiene el permiso <b>Manage group</b> para realizar esta acción. Solicite el permiso <b>Manage group</b> a un administrador del compromiso.',
	manageComponentModalDesc: 'Cree nuevos componentes o edite y elimine los componentes existentes a continuación.',
	editLinkInfo: 'Solo se puede editar el campo de correo electrónico, ya que la solicitud de enlace ya se ha enviado al equipo de componentes.',
	invalidPointOfContact: 'Se necesita un punto de contacto para enviar la solicitud de enlace. Edite el componente para agregar un punto de contacto.',
	manageComponentModalActions: 'Acciones',
	manageComponentModalComponents: 'Componentes',
	manageComponentModalDelete: 'Borrar',
	noThereAtLeastOneComponentToSendAll: 'No hay componentes con estados aptos para enviar una solicitud de vínculo. El estado de un componente debe ser <b>Send</b> o <b>Resend</b> para enviar una solicitud de vínculo.',
	showKnowledgeDescription: 'Mostrar título y descripción de Conocimiento',
	hideKnowledgeDescription: 'Ocultar el título y la descripción de Conocimiento',
	instructionName: 'Introduzca el nombre de la instrucción',
	instructionDescriptionPlaceholder: 'Introduzca la descripción de la instrucción',
	selectDueDate: 'Fecha de vencimiento (obligatoria)',
	show: 'Mostrar',
	allocationHeader: 'Asignación',
	allocationInstructionForKnowledge: 'Las instrucciones de conocimiento solo se pueden asignar por ámbito. Seleccione el (los) ámbito (s) relevantes a continuación.',
	allocationInstructionForCustom: 'Las instrucciones personalizadas se pueden asignar por ámbito o por componente. Seleccione la asignación de instrucciones a continuación y, a continuación, asigne a los ámbitos o componentes relevantes.',
	allocateScope: 'Asignar a ámbitos',
	allocateComponent: 'Asignar a componentes',
	pillScopesPlural: 'alcances',
	pillScopesSingular: 'alcance',
	pillComponentsPlural: 'componentes',
	pillComponentsSingular: 'componente',
	selectScopesPlaceholder: 'Seleccionar alcances',
	selectComponentsPlaceholder: 'Seleccionar componentes',
	searchNoResultFoundText: labels.searchNoResultFoundText,
	newCustomInstruction: 'Nueva instrucción personalizada',
	instructionNameNewCustomInstruction: 'Nombre de la instrucción',
	addCustom: 'Agregar personalizado',
	custom: 'Personalizado',
	required: 'Obligatorio',
	remove: 'eliminar',
	selectAll: 'Seleccionar todo',
	unselectAll: 'Deseleccionar todo',
	lowerPoC: 'punto de contacto',
	editPoCTooltip: 'Punto de contacto no válido o nulo. Edite el punto de contacto para enviar una solicitud de enlace.',
	recomendationType: [{
		id: 1,
		label: 'Requerido'
	},
	{
		id: 2,
		label: 'Opcional'
	},
	{
		id: 3,
		label: 'No aplica'
	}
	],
	confirmLabel: labels.confirmLabel,
	deleteComponentInstructionalText: '<b>¿Está seguro de que desea eliminar este componente de la estructura del grupo?</b><br />Cuando se elimine el componente, se eliminará el enlace al componente y los compromisos ya no podrán intercambiar documentación. Además, todas las asociaciones entre el componente y sus cuentas e instrucciones, serán eliminadas.',
	noActivitiesAvailable: 'No hay actividades disponibles.',
	relatedComponents: 'Componentes relacionados',
	relatedComponentsSingular: 'componente relacionado',
	relatedComponentsPlural: 'componentes relacionados',
	publish: 'Publicar',
	publishModalHeader: 'Publicar cambios',
	publishChangesInstructional: '<b>¿Está seguro de que desea publicar cambios en el resumen de instrucciones de grupo?</b><br />Se sobrescribirá el conjunto anterior de instrucciones de grupo. Una vez que se publican los cambios, las instrucciones actualizadas se pueden enviar desde el resumen de instrucciones del grupo.',
	publishManageGroupPermission: 'Debes tener el permiso de Administrar grupo para realizar esta acción. Solicita permiso a un administrador de compromiso.',
	lastPublished: 'Última vez publicado: ',
	publishChangesNotAvailable: 'Aún no disponible',
	noRecordsFound: labels.noRecordsFound,
	deleteInstruction: 'Eliminar instrucción',
	deleteInstructionInstructionalText: '<b>¿Está seguro de que desea eliminar la instrucción? </b><br />Esta acción no se puede deshacer.',
	sendInstructionsTitle: 'Enviar instrucciones',
	sendInstructionsInstructionalText: 'Asegúrese de que se hayan publicado las instrucciones más recientes haciendo clic en "Publicar" en la página subyacente. A continuación, revise las instrucciones del componente que se indican a continuación y seleccione "Enviar" para enviar las instrucciones al compromiso Componente.',
	instructionsAlreadySent: 'La última versión de las instrucciones ya ha sido enviada.',
	missingDueDates: 'Falta la fecha de vencimiento del formulario de denuncia.',
	createInstructionsModalButton: 'Crear instrucciones',
	createInstructionsModalActionToastMessageStart: 'Faltan instrucciones para la evaluación de riesgos de grupo para',
	createInstructionsModalActionToastMessageEnd: ' Componentes.',
	createInstructionsModalDescription: 'Los siguientes componentes de alcance completo y específico no tienen asignadas instrucciones de evaluación de riesgos de grupo. Al seleccionar <b>Crear</b> se creará una instrucción de evaluación de riesgos de grupo para cada componente que se enumera a continuación.',
	createInstructionsModalScope: 'Scope',
	createInstructionsModalHeader: 'Crear instrucciones',
	createInstructionsModalmodalConfirmBtnLabel: 'Crear',
	createInstructionsModalmodalCancelBtnLabel: 'Cancelar',
	createInstructionsModalmodalCloseBtnTitletip: 'Cerrar',
	createInstructionsModalNewGraInstructionDescription: 'Se adjunta la evaluación de riesgos de las cuentas relevantes para su componente. Revise la evaluación de riesgos y asegúrese de que su compromiso tenga identificadas esas cuentas y riesgos. Cualquier riesgo adicional que se identifique localmente o en el que el equipo Componente no esté de acuerdo debe comunicarse con el equipo Primario para que tanto el equipo Primario como el equipo Componente puedan ajustar la evaluación de riesgos en consecuencia.',
	createInstructionsModalErrorMessage: 'Error en la creación de instrucciones de evaluación de riesgos de grupo para los siguientes componentes: <b>{0}</b>. Actualice la página e inténtelo de nuevo.',
	createInstructionsDuplicatedModalErrorMessage: 'Error en la creación de la instrucción de evaluación de riesgos de grupo. El nombre de la instrucción no se puede duplicar.',
	gaLinkActionTooltip: {
		NotUsingCanvasLabel: 'No usará EY Canvas',
		NotUsingCanvas: 'Al hacer clic en <b>Enviar</b> se crearán las tareas de grupo <br/> primario para este componente, pero <br/> no se enviarán instrucciones.',
		NotLinkedLabel: 'No vinculado',
		NotLinked: 'La solicitud de vínculo no se ha enviado a <br/> el equipo de componentes. Envíe el enlace <br/> solicitud para enviar instrucciones.',
		Unlinked: 'Desvinculado'
	},
	viewHistory: 'Ver historial',
	viewSentInstructionsTitle: 'Ver instrucciones enviadas',
	save: labels.saveLabel,
	cancel: labels.cancelLabel,
	viewHistoryInstructionalText: 'Seleccione la instrucción para ver las versiones anteriores de las instrucciones enviadas al equipo de componentes.',
	viewHistorySelectInstruction: 'Seleccionar instrucción',
	viewHistoryDateSent: 'Fecha de envío: ',
	viewHistoryStatus: 'Estado: ',
	viewHistoryStatusAccepted: 'Aceptado',
	viewHistoryStatusPending: 'Pendiente',
	viewHistoryStatusRejected: 'Rechazado',
	viewHistoryStatusSystemError: 'System error',
	viewHistorySelectVersion: 'Seleccionar versión',
	noAccountsFound: 'No se han encontrado cuentas ni divulgaciones en este ni en otros compromisos. <br />Seleccione {link} para crear cuentas nuevas o editar cuentas o divulgaciones existentes.',
	generalCommunications: 'Comunicaciones generales',
	reportingDeliverables: 'Resultados de los informes',
	changesPublishedNotSent: 'Cambios no enviados',
	changesPublishedBrNotSent: 'Cambios<br/>no enviado',
	changesPublishedNotSentYes: 'Sí',
	deleteSubScopeInstructionalTextModal: '¿Está seguro de que desea eliminar <br/> el sub-ámbito seleccionado?',
	deleteSubScopeTitleModal: 'Eliminar subámbito',
	riskAssessmentModal: {
		headerText: 'Evaluación de riesgos',
		modalCloseBtnTitletip: labels.close,
		manageAndDisclosures: 'Enlace Administrar cuentas y divulgaciones',
		next: 'Siguiente componente',
		back: 'Componente anterior'
	},
	riskAssessment: 'Evaluación de riesgos',
	preview: 'Vista previa',
	accountsAndDisclosureSummary: 'Cuenta y divulgación',
	noAccountSnapshotPlaceholder: 'No hay datos de cuenta para mostrar para este componente.',
	createOversightProjectButtonLabel: 'Crear proyecto de EY Canvas Oversight',
	createOversightProjectTitle: '¿Desea que se cree un proyecto de EY Canvas Oversight con este compromiso principal?',
	createOversightProjectDescription: 'Los compromisos regionales y/o de componentes de EY Canvas identificados en esta estructura de grupo se completarán automáticamente como parte de la configuración del proyecto EY Canvas Oversight.',
	createOversightModalHeader: 'Nombre del proyecto EY Canvas Oversight',
	createOversightModalDescription: 'Introduzca el nombre del proyecto EY Canvas Oversight.',
	createOversightModalTextLabel: 'Nombre del proyecto',
	projectRedirectionButtonLabel: 'Proyectos EY Canvas Oversight',
	projectAssociationTextLabel: 'Hay proyectos EY Canvas Oversight relacionados con este compromiso.',
	sendLinkDisableTooltip: 'Este compromiso fue copiado, incluidos los componentes del flujo de Auditoría de Grupo. Los enlaces no se pueden volver a enviar. Cree un nuevo componente y envíe un vínculo, según sea necesario.',
	instructionsCannotBeSentUntilPublished: 'Instructions cannot be sent until they are published.'
};

export const groupInvolvement = {
	NoComponentsAvailables: 'No se ha creado ningún componente. <b>Administrar componentes</b> para empezar.',
	GroupInvolvementToastMsgStart: 'Falta el formulario de participación grupal para',
	GroupInvolvementToastMsgEnd: ' componente(s).',
	CreateGroupInvolvementHeader: 'Crear formulario(s) de participación',
	GroupInvolvementInstructionalText: "Los siguientes componentes no tienen asignados formularios de participación del Grupo.<br/> Selección de' <b>Crear</b>' creará un formulario de participación grupal para cada componente que se enumera a continuación. ",
	createGroupInvolvementDocumentErrorMessage: 'Error en la creación del documento de participación del grupo para los siguientes componentes: <b>{0}</b>. Actualice la página e inténtelo de nuevo.',
	createGroupInvolvementDocumentSuccessMessage: 'Formulario(s) de participación grupal(es) creado(s) con éxito. Actualice la página en 30 segundos para ver los documentos disponibles.',
	involvementTypePlanned: 'Tipo de participación planificada',
	significantUpdatesToPlannedInvolvement: 'Actualizaciones significativas de la participación planificada',
	executionComplete: 'Ejecución completa',
	generateGroupInvolvementCommunications: 'Imprima el (los) formulario(s) de participación',
	generateGroupInvolvementInstructionalText: "Los siguientes componentes tienen asociados formularios de participación del Grupo. Seleccione qué componente' s formularios de participación del grupo que se incluirán en un documento a continuación.<br /><br /> Una vez seleccionados los componentes, seleccione <b>' Crear' </b> creará un documento de participación del Grupo con cada componente' s documento de participación del grupo que se enumera a continuación. ",
	componentTeams: 'Equipos Componentes',
	noComponentsSelectedErrorMessage: 'Seleccione los componentes para crear una comunicación de participación en el grupo.',
	documentName: '{taskName} paquete de participación de grupo',
	selectAll: groupStructure.selectAll,
	unselectAll: groupStructure.unselectAll,
	modalConfirmBtnLabel: groupStructure.createInstructionsModalmodalConfirmBtnLabel,
	modalCancelBtnLabel: groupStructure.cancelBtn,
	modalCloseBtnTitletip: groupStructure.modalCloseBtnTitletip
};

export const itPlanning = {
	supportingITColumnsHeaders: {
		applicationTool: {
			name: 'Applications/Tools'
		},
		network: {
			name: 'Redes'
		},
		database: {
			name: 'Bases de datos'
		},
		operatingSystem: {
			name: 'Sistemas Operativos'
		}
	},
	relatedITProcessesColumnsHeaders: {
		relatedITProcess: 'Related IT processes',
		category: 'Category'
	},
	itPlanningPlaceholders: {
		smartEvidenceSourceEntityId: 'Tecnología relacionada no disponible para este documento',
		smartEvidenceSourceId: 'Ningún objeto relacionado. Relacionar un objeto para empezar.',
	},
	relatedITProcessesPlaceholders: {
		smartEvidenceSourceEntityId: 'Related IT process not available for this document',
		smartEvidenceSourceId: 'Ningún objeto relacionado. Relacionar un objeto para empezar.',
		relatedITProcessEmpty: 'No IT process related to the technology'
	},
	noTechnologiesIdentified: 'No se han identificado tecnologías',
	supportingITEmpty: 'No hay aplicaciones/herramientas de apoyo relacionadas con la tecnología',
	supportingITNetworkEmpty: 'Ausencia de redes de apoyo relacionadas con la tecnología',
	searchPlaceholder: 'Búsqueda',
	newTechnology: 'Nueva tecnología',
	noSupportingDatabases: 'No hay bases de datos de soporte relacionadas con la tecnología',
	createEntityFormDocument: 'Crear documento',
	noSupportingOperatingSystem: 'No supporting operating systems related to the technology',
	manageTechnology: 'Manage technology'
};

export const itRiskFactors = {
	accepted: 'Aceptado',
	rejected: 'Rechazada',
	accept: 'Aceptar',
	reject: 'Rechazar',
	rejectionRationale: 'Justificación del rechazo',
	rejectionCategory: 'Categoría de rechazo',
	rejectionRationaleRequired: 'Rejection rationale (required)',
	rejectionCategoryRequired: 'Rejection category (required)',
	riskName: 'Risk name',
	smartEvidenceValidations: {
		smartEvidenceSourceEntityId: 'Factores de riesgo no disponibles para este documento',
		smartEvidenceSourceId: 'No related object. Relate an object to get started.'
	},
	manageChangePlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'Todos los riesgos han sido rechazados'
	},
	manageOperationsPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	},
	manageAccessPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	},
	SDLCPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'Todos los riesgos han sido rechazados'
	},
	manageSecuritySettingsPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	}
};

export const rejectionTypeResource = [{
	id: rejectionType.itRiskOther,
	label: 'ITRisk Other'
},
{
	id: rejectionType.itRiskOption2,
	label: 'ITRisk - "Option 2"'
},
{
	id: rejectionType.itRiskOption3,
	label: 'ITRisk - "Option 3"'
}
];

export const sampleList = {
	newSample: 'Nueva muestra',
	createSampleModalDescription: "Ingrese los detalles de la muestra a continuación y seleccione'<b>{0}</b>' para terminar. Para crear otra muestra, seleccione'<b>{1}</b>'. ",
	saveAndCreateAnother: 'Guardar y crear otro',
	saveAndClose: 'Guardar y cerrar',
	sampleDescription: 'Descripción de la muestra (obligatorio)',
	sampleDate: 'Fecha de muestra (obligatorio)',
	sampleListId: 'ID de lista de muestra',
	ok: 'Aceptar',
	addSample: 'Agregar muestra',
	cancel: 'Cancelar',
	saveAndCloseForHeader: 'Guardar y cerrar',
	saveAndCreateAnotherHeader: 'Guardar y crear otro',
	required: 'Obligatorio',
	description: 'Descripción de la muestra',
	date: 'Fecha',
	attributeStatus: 'Atributos',
	tags: 'Etiquetas',
	open: 'Abierto',
	notApplicableLabel: 'No aplicable',
	notPresent: 'No presente',
	present: 'Presente',
	pagingShowtext: 'Mostrar',
	placeHolderMessage: 'No hay muestras disponibles. Haga clic en {clickHere} para comenzar.',
	noSampleListAvailable: 'No hay lista de muestras disponible',
	editSample: 'Editar muestra',
	editSampleDescription: "Edite los detalles de la muestra a continuación y seleccione'<b>{0}</b>' para terminar. ",
	editSampleSave: 'Guardar',
	sampleCanNotBeCreated: 'No se pueden crear muestras para este documento.',
	noRelatedObject: 'No hay ningún objeto relacionado. Relaciona un objeto para empezar.',
	noResultsFound: 'No se han encontrado resultados'
};

export const AdditionDocumentationLabels = {
	addAdditionalDocumentation: 'Añadir documentación adicional',
	editAdditionalDocTitle: 'Editar documentación adicional',
	removeAdditionalDocumentation: 'Eliminar documentación adicional',
	cancel: 'Cancelar',
	save: 'Guardar',
	of: 'de',
	additionalDocTitlePlaceholder: 'Documentación adicional (obligatorio)',
	additionalDocTitle: 'Documentación adicional (obligatorio)',
	remove: 'eliminar',
	enterAdditionalDocTitle: "Ingrese la documentación adicional a continuación y seleccione <b>'{0}'</b> para finalizar. ",
	editAdditionalDocDesc: "Edite la documentación adicional a continuación y seleccione <b>'{0}'</b> para finalizar. ",
	characters: 'caracteres',
	required: 'Obligatorio',
	descriptionMaxLengthError: 'La respuesta supera el máximo permitido.',
	attributeIndexLabel: 'Índice de atributos'
};

export const sampletAttributeConstants = [{
	id: 1,
	label: 'Abrir'
},
{
	id: 3,
	label: 'Presentar'
},
{
	id: 7,
	label: 'Presentar con comentarios'
},
{
	id: 5,
	label: 'No está presente'
},
{
	id: 4,
	label: 'No aplicable'
},
];

export const groupInstructions = {
	ALRAPackageModalTitle: 'Nombre del paquete ALRA',
	ALRAPackageModalInstructionalText: 'Escriba el nombre del paquete ALRA que va a agregar a Evidencia.',
	ALRAPackageModalNameField: 'Introduzca el nombre',
	ALRAPackageSuccessToastMessage: 'Se ha iniciado el proceso de creación del paquete. Puede tardar hasta diez minutos en completarse.',
	ALRAPackageInProgressToastMessage: 'El proceso de creación del paquete está en curso, puede tardar hasta diez minutos en completarse.',
	delete: labels.delete,
	deleteSectionModalTitle: labels.deleteSection,
	deleteSectionInstructionalText: '<b>¿Está seguro de que desea eliminar la sección?</b><br />Esta acción no se puede deshacer.',
	deleteSectionTooltipText: 'Las instrucciones deben eliminarse<br />antes de que la sección sea eliminada.',
	modalConfirmBtnLabel: labels.confirmLabel,
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	missing: 'Pendiente (validar)',
	sendAllModalTriggerButton: 'Enviar todo',
	sendAllModalTooltipText: 'No hay instrucciones disponibles para enviar a los equipos de componentes.',
	publishModalTooltipText: 'Las instrucciones de grupo deben publicarse antes de que se envíen. Cuando se publican las instrucciones, los cambios se guardan como nuevas instrucciones, anulando la versión anterior de las instrucciones. Estas nuevas instrucciones se pueden enviar a los equipos de componentes.',
	sendAllModalErrorMessage: 'Group instructions for the following Components were not sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk. <br /> <b>{0}</b>',
	sendAllModalHeaderText: 'Enviar todas las instrucciones',
	sendAllModalConfirmBtnLabel: 'Enviar',
	sendAllModalCancelBtnLabel: 'Cancelar',
	sendAllModalCloseBtnTitletip: 'Cerrar',
	sendAllModalDescription: 'Al seleccionar <b>Enviar</b> se enviarán instrucciones a los siguientes equipos de componentes.',
	generateGroupRiskAssessmentCommunications: 'Generar ALRA del grupo',
	bulkALRAPackageName: '{instructionName} paquete de evaluación de riesgos a nivel de cuenta',
	groupInstructionSummaryReport: 'Informe resumido de instrucción grupal',
	groupInstructionSummaryReportTitletip: 'Vea y exporte los detalles de las instrucciones de grupo, el historial de instrucciones y los cambios en la asignación de componentes/cuentas.',
	exportGroupRiskAssessment: 'Exportar resumen',
	reportingDeliverables: groupStructure.reportingDeliverables,
	groupRiskAssessment: 'Evaluación de riesgos grupales'
};

export const sectionTitles = [{
	id: KnowledgeSectionIds.GeneralCommunications,
	sectionTitle: groupStructure.generalCommunications
},
{
	id: KnowledgeSectionIds.ScopeOfWork,
	sectionTitle: 'Alcance del trabajo'
},
{
	id: KnowledgeSectionIds.ReportingForms,
	sectionTitle: groupStructure.reportingDeliverables
},
{
	id: KnowledgeSectionIds.ProceduresPerformedCentrally,
	sectionTitle: 'Procedimientos realizados de forma centralizada'
},
{
	id: KnowledgeSectionIds.GroupRiskAssessment,
	sectionTitle: groupInstructions.groupRiskAssessment
},
{
	id: KnowledgeSectionIds.OtherCommunications,
	sectionTitle: 'Otras comunicaciones'
}
];

export const groupAuditToolbar = {
	search: labels.placeholderForSearch
};

export const AccountType = [{
	id: 1,
	accounttypename: 'Cuenta significativa'
},
{
	id: 2,
	accounttypename: 'Cuenta de riesgo limitado'
},
{
	id: 3,
	accounttypename: 'Cuenta insignificante'
},
{
	id: 4,
	accounttypename: 'Otra cuenta'
},
{
	id: 5,
	accounttypename: 'Revelación importante'
}
];

export const PriorityType = [{
	value: 1,
	label: 'Bajo'
},
{
	value: 2,
	label: 'Medio'
},
{
	value: 3,
	label: 'Alto'
},
{
	value: 4,
	label: 'Crítico'
}
];

export const AccountSummaryAccountType = [{
	id: '0',
	accounttypename: 'Todas las cuentas'
},
{
	id: '1',
	accounttypename: 'Cuentas significativas'
},
{
	id: '2',
	accounttypename: 'Cuentas de riesgo limitado'
},
{
	id: '3',
	accounttypename: 'Cuentas insignificantes'
},
{
	id: '4',
	accounttypename: 'Cuenta - Otros'
},
{
	id: '5',
	accounttypename: 'Divulgaciones significativas'
}
];

export const TaskStatus = [{
	id: 1,
	status: 'abrir'
},
{
	id: 2,
	status: 'en progreso'
},
{
	id: 3,
	status: 'En revisión'
},
{
	id: 4,
	status: 'Completado'
},
{
	id: 5,
	status: 'Removido'
}
];

export const reviewNoteLabels = {
	/*Review Notes*/
	emptyNoteDetailsMessage: 'Seleccione una nota para ver los detalles. Para habilitar los controles masivos, utilice la tecla de control o desplazamiento y seleccione varias notas de revisión. Si desea trabajar en una nota individual, seleccione esa nota de la lista.',
	documentReviewNotesLabel: 'Notas de documento',
	addNewReviewNoteButtonText: 'Añadir nota',
	noNotesAssociatedWithDocumentLabel: 'No hay notas asociadas a este documento',
	allNotesLabel: 'Todas las notas',
	charactersLabel: 'Caracteres',
	myNotesLabel: 'Mis notas',
	showClearedLabel: 'Mostrar aclarado',
	showClosedLabel: 'Mostrar cerrado',
	toLabel: 'a',
	toUserLabel: 'Para',
	ofLabel: 'de',
	textAreaPlaceholder: 'Introducir nota',
	addNewNoteModalClose: 'Cerrar',
	addNewNoteModalTitleLabel: 'Añadir nueva nota',
	editNoteModalTitleLabel: 'Editar nota',
	deleteIconHoverText: 'Eliminar',
	deleteIconModalAcceptText: 'Eliminar',
	deleteIconModalConfirmMessage: '¿Está seguro de que desea eliminar su respuesta a esta nota?',
	deleteIconModalConfirmMessageParent: '¿Está seguro de que desea eliminar la nota seleccionada?',
	deleteIconModalTitleLabel: 'Eliminar nota',
	deleteReplyIconModalTitle: 'Eliminar respuesta',
	emptyRepliesMessage: 'No hay respuestas todavía',
	replyInputPlaceholder: 'Responder a esta nota',
	replyText: 'Texto de respuesta',
	editReplyModelTitle: 'Editar respuesta',
	noteDueDateLabel: 'Vence',
	fromUserLabel: 'De',
	priorityLabel: 'Prioridad',
	dueDateLabel: 'Fecha de vencimiento',
	dueLabel: 'Vencimiento',
	status: 'Estado',
	noteModifiedDateLabel: 'Modificado: ',
	cancelLabel: 'cancelar',
	saveLabel: 'Guardar',
	clearedBy: 'Aclarado por',
	closedBy: 'Cerrado por',
	reopenedBy: 'Reabierto por',
	reply: 'Respuesta',
	editIconHoverTextLabel: 'Editar',
	required: 'Obligatorio',
	closeTitle: 'Cerrado',
	otherEngagementNotes: 'Otras notas del compromiso',
	closeLabel: 'Cerrar',
	showMore: 'Mostrar mas',
	showLess: 'Mostrar menos',
	showMoreEllipsis: 'Mostrar mas...',
	showLessEllipsis: 'Mostrar menos...',
	noResultFound: 'No se han encontrado resultados',
	engagementNameLabel: 'Nombre del compromiso: ',
	drag: 'Arrastrar',
	formMaxLength: 'El texto no puede exceder {number} caracteres.',
	voiceNoteButtonLabel: 'Nota de voz',
	stopRecordingButtonLabel: 'Detener',
	reopen: 'Reabrir',
	noNotesFound: 'No se encontraron notas',
	noNotesFoundInstructional: 'Deje una nota utilizando las entradas a continuación. Asigne la nota a un usuario y especifique la prioridad y la fecha de vencimiento.',
	microphoneBlockedMessage: 'Permita que el navegador acceda a su micrófono para usar notas de voz. Si ya está permitido, actualice e inténtelo de nuevo.',
	microphoneBlockedOnVideoMessage: 'Permita que el navegador acceda a su micrófono para usar la voz en la grabación de pantalla. Si ya está permitido, actualice e inténtelo de nuevo.',
	notInMainWindowVoice: 'Las grabaciones de voz no están permitidas dentro del cajón, abra el documento en una nueva pestaña para realizar la acción.',
	notInMainWindowScreen: 'Las grabaciones de pantalla no están permitidas dentro del cajón, abra el documento en una nueva pestaña para realizar la acción.',
	voiceNoteNotAvailable: 'La nota de voz y la grabación de pantalla no están disponibles en la vista de cajón. Cambie a la vista de pantalla completa para usar estas funciones.',
	playButtonTitle: 'Correr',
	deleteButtonTitle: 'Borrar',
	pauseButtonTitle: 'Pausa',
	screenRecord: 'Grabación de pantalla',
	playbackReview: 'Revisión de reproducción'
};

export const IndividualAccountAttributeLabels = {
	attributesNotAvailableForDocument: 'Los atributos de la cuenta no están disponibles para este documento.',
	noRelatedOnject: 'No hay ningún objeto relacionado. Relaciona un objeto para empezar.',
	noAttributesAvailable: 'No hay atributos disponibles',
	noRisksAvailable: 'No hay riesgos disponibles',
	attributeStandardRomms: 'Atributo ROMM estándar',
	continueButtonTitle: 'Continuar',
	closeButtonTitle: 'Cancelar',
	newAssertionModalPlaceholder: 'Esta selección dará lugar a que se identifiquen como de menor riesgo inherente la(s) siguiente(s) aseveración(es) que no se habían identificado previamente como relevantes. ¿Desea continuar?',
	assertion: 'Aseveración',
	inherentRiskType: 'Riesgo inherente',
	assertionModalTitle: 'Nueva aseveración',
	riskType: 'Más bajo'
};

export const entities = [{
	id: 0,
	name: 'Todo'
},
{
	id: 1,
	name: 'Documento'
},
{
	id: 2,
	name: 'LeadSchedule'
},
{
	id: 3,
	name: 'Cuenta'
},
{
	id: 4,
	name: 'SCOT'
},
{
	id: 5,
	name: 'Proceso de TI'
},
{
	id: 6,
	name: 'Plan de auditoría'
},
{
	id: 7,
	name: 'Riesgo'
},
{
	id: 8,
	name: 'Tarea'
},
{
	id: 9,
	name: 'Error'
},
{
	id: 10,
	name: 'Deficiencia'
},
{
	id: 11,
	name: 'Componente GA'
},
{
	id: 12,
	name: 'Instrucción de componentes GA'
},
{
	id: 13,
	name: 'Evidencia del componente GA'
},
{
	id: 14,
	name: 'Alcance de la AG'
},
{
	id: 15,
	name: 'Instrucción Primaria de GA'
},
{
	id: 16,
	name: 'PRIMARIA DE GA'
},
{
	id: 17,
	name: 'Solicitud del cliente'
},
{
	id: 18,
	name: 'WCGW'
},
{
	id: 19,
	name: 'Control'
},
{
	id: 20,
	name: 'Aplicación de TI'
},
{
	id: 21,
	name: 'Canvas Form'
},
{
	id: 22,
	name: 'Sección de formulario'
},
{
	id: 23,
	name: 'Cuerpo del formulario'
},
{
	id: 24,
	name: 'Aseveración'
},
{
	id: 25,
	name: 'Compromiso con el cliente'
},
{
	id: 26,
	name: 'Grupo de clientes'
},
{
	id: 27,
	name: 'Etiqueta del compromiso'
},
{
	id: 28,
	name: 'Compromiso'
},
{
	id: 29,
	name: 'Encabezado del formulario'
},
{
	id: 30,
	name: 'Estado del formulario'
},
{
	id: 31,
	name: 'Usuario de compromiso'
},
{
	id: 32,
	name: 'Usuario del grupo de clientes'
},
{
	id: 33,
	name: 'Índice PSP'
},
{
	id: 34,
	name: 'ITGC'
},
{
	id: 35,
	name: 'Riesgo de TI'
},
{
	id: 36,
	name: 'Elemento de línea de automatización'
}
];

export const PaceType = [{
	id: 1,
	paceTypename: 'Bajo'
},
{
	id: 2,
	paceTypename: 'Moderado'
},
{
	id: 3,
	paceTypename: 'Alto'
},
{
	id: 4,
	paceTypename: 'Monitoreo cercano'
}
];

export const DocumentHelper = {
	401: 'No se puede completar la operación. Actualice la página y vuelva a intentarlo. Si el problema persiste, comuníquese con el servicio de asistencia',
	413: 'El documento excede el tamaño de archivo máximo permitido (250 mb) y no se puede cargar. Reduzca el tamaño del archivo y vuelva a intentarlo.',
	412: 'Ya existe un documento con nombre en este compromiso',
	414: 'EL nombre excede el maximo (120 caracteres)',
	4099: 'El documento con el mismo nombre ya existe',
	/*this is a hack as we dont always know why conflict happened.*/
	410: 'Este documento ha sido eliminado y por lo tanto no se puede abrir.',
	411: 'No se permiten documentos vacíos.'
};

export const Errors = {
	/*Doc Helper Custom Messages */
	0: 'conexión perdida. Reconecte e intente de nuevo. Si el problema persiste, contacte al help desk',
	10: 'Se ha detectado un problema con EY Canvas Document Helper. Haga clic en <a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">here</a> para obtener instrucciones sobre cómo resolver este problema.',
	101: 'Estatus de compromiso invalido',
	102: 'Compromiso valido, usuario no encontrado',
	103: 'Cumplimiento de independencia del usuario del compromiso faltante',
	104: 'Alcances de AD Azure faltantes',
	105: 'Se ha producido un error al obtener permisos de interacción. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	106: "Unauthorized. Contact your administrator and try again.",
	107: 'No se ha encontrado un usuario deel compromiso válido. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	108: 'El perfil de compromiso está incompleto. Vaya a página de destino y perfil completo.',
	303: 'Un documento con el mismo nombre esta actualmente siendo cargado',
	403: 'Access to this document is not available.  If this document is shared ensure you have access to the source engagement.  Refresh the page and try again.  If the error persists contact the Help Desk.',
	406: 'El documento no puede estar vacío.',
	412: 'Ya existe un documento con el mismo nombre en este compromiso.',
	414: 'EL nombre excede el maximo (120 caracteres)',
	411: 'No se permiten documentos vacíos.',
	500: 'conexión perdida. Reconecte e intente de nuevo. Si el problema persiste, contacte al help desk',
	600: 'La operación no se puede completar en este momento. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	601: 'Error al descargar la captura de pantalla. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	602: 'El documento ya está en Colaboración',
	935: 'El usuario no tiene permisos suficientes para realizar la operación.',
	zip: 'El archivo .zip no puede ser subido debido a que contiene uno o mas tipos de archivos no soportables o contiene mas del importe maximo de archivos .zip permitidos',
	401000: 'Se detecto un cambio de red, de favor recarge la pagina para continuar',

	/*Accounts*/
	1001: 'llamado de cuenta fallo',
	1002: 'Nombre de la cuenta faltante',
	1003: 'The selected Account has been deleted. Close this modal to see the updated list.',
	1004: 'No se han encontrado resultados. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	1005: 'ID del compromiso invalido',
	1006: 'tipo de cuenta invalido',
	1007: 'tipo de estado invalido',
	1008: 'La cuenta seleccionada ha sido eliminada. Cierre este modo para ver la lista actualizada.',
	1009: 'Obtener cuenta por ID fallo',
	1010: 'Obtener cuenta por Compromiso fallo',
	1011: 'Obtener resumen SEM para cuenta fallo debido a requesicion invalida',
	1012: 'resumen de tipo invalido',
	1013: 'Crear revision de cuentas fallo',
	1014: 'Borrar revision de cuentas fallo',
	1015: 'Requisicion de revision de creacion de cuenta fallo',
	1016: 'ID de revision de cuenta invalido',
	1017: 'La cuenta no es parte de este compromiso o ha sido borrado',
	1018: 'La revision de cuenta ha sido creada por otro usuario',
	1019: 'La cuenta ha sido eliminada por otro usuario. Actualice la página e inténtelo de nuevo.',
	1020: 'La cuenta necesita actualizar PSP',
	1024: 'El nombre de la cuenta tiene más de 500 caracteres.',
	1025: 'Riesgo limitado o cuenta insignificante no se puede estimar',
	1026: 'La cuenta no puede tener aserciones duplicadas',
	1027: 'ID de afirmación inválido',
	1037: 'No puede tener índices PSP duplicados',
	1039: 'La cuenta seleccionada ha sido eliminada. Cierre este modal para ver la lista actualizada.',
	1048: 'La cuenta ha sido de tipo de ejecución inválido.',
	1053: 'La cuenta tiene un riesgo o estimación asociado, no se puede establecer como cuenta de riesgo limitado o cuenta insignificante.',
	1054: 'Las aseveraciones que se van a eliminar tienen asociado un Riesgo o Estimación',
	1065: 'No puede realizar cambios en las aseveraciones que tengan un riesgo significativo, o riesgo de fraude, o riesgo de error material o una estimación relacionada. Primero debe eliminar estas relaciones.',
	1070: 'El identificador de saldo de prueba no puede ser nulo cuando se incluye Helix.',
	1072: 'La acción no puede completarse. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',

	1266: 'El compromiso ha alcanzado el número máximo de documentos en modo de edición multiusuario. Vuelva a registrar algunos documentos y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	1267: 'Document is conflicted. Resolve conflicts and try again.  If the issue persists, contact the Help Desk.',
	1268: 'Document is already in co-edit mode. end co-edit mode and try again.  If the issue persists, contact the Help Desk.',
	1269: 'Document is a shared evidence. Unlink and try again.  If the issue persists, contact the Help Desk.',
	1270: 'Las versiones de los documentos no se pueden eliminar ni actualizar en el modo de edición multiusuario. Finalice la edición multiusuario y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	1271: 'El documento no está en modo de co-edición o el modo de co-edición final está en curso. Si el problema persiste, póngase en contacto con el Servicio de Soporte',

	/*Assertions*/
	2001: 'Requisicion de crear invalida',
	2002: 'Nombre de la aseveracion Faltante',
	2003: 'Aseveracion faltante',
	2004: 'Obtener aseveracion fallida',
	2005: 'ID del compromiso invalido',
	2006: 'Obtener aseveracion fallida',
	2007: 'Obtener WCGWs fallo',

	/*Risks*/
	4001: 'No se pudo completar la operación en este momento. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con mesa de ayuda.',
	4002: 'Nombre del riesgo faltante',
	4003: 'Riesgo faltante',
	4004: 'Obtener riesgo fallo',
	4005: 'ID del compromiso invalido',
	4006: 'Obtener riesgo por ID fallo',
	4007: 'Requisicion de Query invalida',
	4008: 'Esta estimación ya no está disponible. Actualice la página y vuelva a intentarlo. Póngase en contacto con Servicio de Soporte si el error persiste.',
	4009: 'Requisicion de actualizacion invalida',
	4010: 'WCGW especifico asignado a riesgo',
	4011: 'la lista de WCGW no puede ser null',
	4012: 'Fallo para tener tipos de riesgo',
	4013: 'Requisicion de creacion invalida',
	4014: 'La relacion de la aseveracion no es valida. Actualice la pagina e intente de nuevo. Si el problema persiste contacte al Help desk',
	4015: 'El WCGW no es valido. Actualice la pagina e intente de nuevo. Si el problema persiste contacte al help desk',
	4016: 'Se ha eliminado el riesgo/estimación seleccionado. Cierre este modo para ver la lista actualizada.',
	4017: 'La aseveracion no es valido. Actualice la pagina e intente de nuevo. Si el problema persiste contacte al help desk',
	4018: 'El ID del tipo de riesgo no es valido',
	4019: "RiskName no es valido' o Nombre de riesgo no es valido. ",
	4020: 'ID del documento invalido',
	4021: 'RiskName no debe tener más de 500 caracteres o Nombre de riesgo no debe tener más de 500 caracteres',
	4023: 'La lista AssertionIds no puede ser nula.',
	4024: 'El formulario de documentación de cuenta de riesgo limitado no se pudo crear debido a un error. Actualiza la página y vuelve a intentarlo. Si el problema persiste, póngase en contacto con mesa de ayuda.',
	4025: 'ID de cuenta no válido.',
	4026: 'Id. de aseveración no válido.',
	4027: 'El modelo de riesgo de Aseveración no puede estar vacío',
	4031: 'Opción Riesgo o Cuerpo del Formulario no válido.',
	4035: 'No se puede editar IsHigherRisk para riesgos significativos o de fraude.',
	4036: 'KnowledgeAssertionId no puede estar vacío si no se pasa ningún AssertionId. KnowledgeAssertionId debe estar en enumeraciones',
	4037: 'KnowledgeAssertionId ya existe para esta cuenta.',
	4038: 'RiskTypeId no coincide con las opciones permitidas.',
	4062: 'Este SCOT ya no está disponible. Actualice la página y vuelva a intentarlo. Póngase en contacto con Servicio de Soporte si el error persiste.',
	4063: 'La relación SCOT no se puede editar. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con Servicio de Soporte.',
	4076: 'This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	4079: 'This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*TASK*/
	5001: 'Get all tasks failed.',
	5002: 'Esta tarea no esta disponible en este compromiso',
	5003: 'Obtener documentos para las tareas fallo. Parametros de requisicion fallidos',
	5004: 'Obtener documentos para relacionar tareas fallo. Parametros de requisicion fallidos',
	5005: 'Obtener tareas por ID fallo',
	5006: 'Obtener categorias de tareas fallo',
	5007: 'Obtener su categorias de tareas fallo',
	5008: 'Obtener descripcion de tareas fallo',
	5009: 'Obtener tareas de requisicion del cliente fallo',
	5010: 'Guardar las tareas de requisicion del cliente fallo',
	5011: 'Las tareas que esta intentando relacionar han sido borrados o rechazados',
	5012: 'La cosa que esta intentando relacionar ha sido borrado',
	5013: 'La tarea con la que intenta relacionar elementos se ha eliminado o rechazado.',
	5014: 'Se ha eliminado el documento que está intentando relacionar.',
	5015: 'Error en la llamada de obtención de pruebas de tareas.',
	5016: 'Error al obtener la llamada de tarea WCGW.',
	5017: 'El ID del compromiso debe ser mayor a cero',
	5018: 'No se puede completar la asociación. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el Help Desk.',
	5019: 'La descripción de la tarea está vacía',
	5020: 'La tarea seleccionada se ha eliminado o rechazado. Como tal, esta acción no se puede completar en este momento.',
	5021: 'El origen del ID del compromiso no se encuentra',
	5022: 'Error al guardar anotación',
	5023: 'Usuario del compromiso no encontrado',
	5024: 'Error en la llamada de tareas de eliminacion',
	5025: 'Error en la llamada de tareas de eliminacion',
	5026: 'La lista de tareas está vacía',
	5027: 'Revisión no encontrada.',
	5028: 'Se requiere el nombre del archivo.',
	5029: 'Se requiere extensión de archivo.',
	5030: 'El nombre de archivo no puede incluir: */:<>\\?|"',
	5031: 'Error al actualizar el nombre del documento.',
	5032: 'Identificación de documento no válido.',
	5033: 'Tipo de operación no encontrado.',
	5034: 'Error al cambiar el estado de la tarea',
	5035: 'La acción que está intentando realizar no se puede completar en este momento. Por favor, inténtelo de nuevo más tarde. Póngase en contacto con el servicio de ayuda si este error persiste.',
	5036: 'Body no puede ser null o vacío en la llamada.',
	5037: 'La solicitud no puede ser nula en la llamada.',
	5038: 'Introducir un nombre de archivo único para proceder.',
	5039: 'Se requiere el nombre de archivo.',
	5040: 'El nombre de archivo está limitado a 100 caracteres.',
	5041: 'El nombre de archivo no puede incluir: */:<>\\?|"',
	5042: 'Se rechaza la tarea seleccionada.',
	5043: 'La tarea es una tarea de paso de compilación.',
	5044: 'La tarea es una tarea de milestone',
	5045: 'La tarea no es ni de tipo PSP u OSP.',
	5046: 'Se supera el límite de caracteres.',
	5047: 'Se supera el límite de caracteres.',
	5048: 'Campo obligatorio.',
	5049: 'El documento seleccionado no se puede remover de esta tarea. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el Help Desk.',
	5050: 'Identificación de grupo de tareas no debe ser cero o no válido',
	5051: 'Identificación de sección de tareas no debe ser cero o no válido',
	5052: 'Error al intentar agregar este documento a la tarea actual. Error en la llamada.',
	5053: 'Error al intentar actualizar este documento en la tarea actual. Error en la llamada.',
	5054: 'Error al intentar agregar una copia de este documento en la tarea actual. Error en la llamada.',
	5055: 'Error al intentar cambiar el nombre del documento. Error en la llamada.',
	5056: 'No se puede editar el título de la tarea para el conocimiento o la tarea de grupo',
	5057: 'El tipo de tarea debe ser Tipo Ost',
	5058: 'Este documento no se puede remover de la tarea porque está asociado al sistema.',
	5059: 'Valor de fase de tiempo no válido.',
	5060: 'Añadir a error de evidencia. Llamada fallida',
	5061: 'Otro usuario ha actualizado la información presentada. Actualice la página e inténtelo de nuevo. Si el error persiste, póngase en contacto con el Help Desk.',
	5062: 'El documento de requisicion no esta disponible para el canal de su compromiso selecciondo en EY Atlas. De favor contacte al Help desk para que la informacion pueda ser dada a los autores de contenido para incluirlo en el futuro',
	5063: 'Operación de Patch invalida',
	5064: 'Las tareas seleccionadas han sido borradas o rechazadas. Por lo tanto, la accion no puede ser completada en este momento',
	5065: 'No se puede actualizar el tipo de fuente de la tarea. Requesicion invalida',
	5066: 'Error al obtener la guia. La llamada fallo',
	5067: 'No se puede actualizar el tipo y naturaleza de la tarea. Requisicion invalida',
	5068: 'No se puede actualizar el tipo y naturaleza de la tarea. Requisicion invalida',
	5069: 'Asignacion de borrar tarea, fallo',
	5070: 'Uno o mas tareas seleccionadas han sido borradas. Intente de nuevo o contacte al Help Desk si el error continua',
	5071: 'No se puede actualizar la asignacion. La requesicion es invalida',
	5072: 'Preparador no encontrado. La solicitud no es válida.',
	5073: 'Asignación no encontrada.',
	5074: 'Error al guardar la asignación de tareas',
	5075: 'El mismo miembro del equipo solo se puede asignar a una tarea que no sea Preparador. Inténtelo de nuevo o póngase en contacto con el Help Desk si el error persiste.',
	5076: 'El miembro del equipo seleccionado no es un miembro activo de esta compromiso. Inténtelo de nuevo o póngase en contacto con el Help Desk si el error persiste.',
	5077: 'Se han eliminado una o varias tareas seleccionadas. Inténtelo de nuevo o póngase en contacto con el Help Desk si el error persiste.',
	5078: 'Se ha removido la asignación de tareas seleccionada. Inténtelo de nuevo o póngase en contacto con el Help Desk si el error persiste.',
	5079: 'Se han eliminado una o varias tareas seleccionadas. Inténtelo de nuevo o póngase en contacto con el Help Desk si el error persiste.',
	5080: 'El registro del documento seleccionado no existe',
	5081: 'El usuario que está asignado actualmente a esta tarea no se puede reasignar como preparador. Inténtelo de nuevo o póngase en contacto con el Help Desk si el error persiste.',
	5082: 'Error en la actualización. El nombre del documento debe ser único para un compromiso. Actualice la página para quitar este mensaje.',
	5083: 'Se supera el límite de caracteres en el detalle de la tarea.',
	5084: 'Error en la llamada al crear documento de tarea.',
	5085: 'Error en la llamada al eliminar documento de tarea.',
	5086: 'Los desvíos de tareas crean un error en la llamada.',
	5087: 'Error en la llamada del parche de tareas.',
	5088: 'Esta tarea debe contener pruebas para entregar. Inténtelo de nuevo o póngase en contacto con el Help Desk.',
	5089: 'Todas las pruebas asociadas a esta tarea (con la excepción de los paper profiles) deben tener al menos un preparador y revisor para ser marcarda completada. Inténtelo de nuevo y póngase en contacto con el Help Desk si el error persiste.',
	5091: 'El nombre del paper profile ya existe en el compromiso.',
	5092: 'El nombre no puede contener ninguno de los siguientes caracteres: */:<>\\?|\\',
	5093: 'El nombre del perfil de papel supera la longitud máxima (100 caracteres).',
	5111: 'Se ha eliminado la cuenta seleccionada, la aserción o la cuenta de riesgo limitado. Actualice la página e inténtelo de nuevo. Póngase en contacto con mesa de ayuda si el error persiste.',
	5116: 'El tipo de documento no es válido.',
	5139: 'No se puede completar la asociación. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de Help Desk.',
	5131: 'No se puede completar la asociación. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de Help Desk.',
	5146: 'La tarea no se puede marcar como completada.',
	5156: 'La relación de tareas no se puede editar. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',

	/*WCGW*/
	6001: 'Error en la llamada al crear WCGW.',
	6002: 'Falta el nombre WCGW.',
	6003: 'Falta WCGW.',
	6004: 'Error al obtener la llamada WCGW.',
	6005: 'Identificación del compromiso no válido.',
	6006: 'Identificación de aserción no válido.',
	6007: 'Error al obtener la llamada WCGW por Id.',
	6008: 'Solicitud no válida.',
	6009: 'Identificación de WCGW no valido',
	6010: 'La tarea no se pudo asociar al WCGW.',
	6011: 'Se elimina el WCGW seleccionado.',
	6012: 'La tarea y el WCGW no estan relacionados con la misma aserción',
	6013: 'La tarea seleccionada no pertenece al mismo compromiso.',
	6014: 'La tarea no se pudo desvincular del WCGW.',
	6015: 'La tarea no se pudo asociar al WCGW.',
	6016: 'La tarea se rechaza y no se puede asociar al WCGW.',
	6017: 'La tarea no es un milestone y no se puede asociar al WCGW.',
	6018: 'La tarea no es una tarea de paso de compilación y no se puede asociar al WCGW.',
	6019: 'La tarea no es un PSP u OSP y no se puede asociar al WCGW.',
	6020: 'La tarea y WCGW están asociados a la misma aserción y no se pueden asociar al WCGW.',

	/*Engagement*/
	7001: 'No se encuentra el ID',
	7002: 'Error en la llamada al obtener compromiso por identificación de espacio de trabajo.',
	7003: 'Error en la llamada de GetAll a las entidades del compromiso.',
	7004: 'Error al obtener compromiso mediante la llamada de identificación.',
	7005: 'Error al llamar a todos los usuarios del compromiso.',
	7006: 'El apellido no debe superar los 250 caracteres.',
	7007: 'Error de tipo de usuario no válido.',
	7008: 'El nombre no debe superar los 250 caracteres.',
	7009: 'La GUI del usuario no puede ser nulo.',
	7010: 'Error de estado de usuario no válido.',
	7011: 'Error en la llamada para crear el usuario del compromiso.',
	7012: '{0} {1} no se puede invitar porque ya es un miembro activo o pendiente del equipo.',
	7013: 'Las iniciales no deben superar los 3 caracteres.',
	7014: 'La página de destino de EY Canvas no se encuentta accesible en este momento. Inténtelo de nuevo y si el problema persiste, póngase en contacto con el Help Desk.',
	7015: '{0} {1} no se pueden invitar como los siguientes grupos de acceso: {2} se han eliminado del compromiso. Actualice e inténtelo de nuevo.',
	7016: '{0} {1} no se puede invitar, ya que el usuario ya es miembro activo del equipo en los siguientes grupos de acceso: {2}',
	7017: 'Dominio no está permitido para grupos seleccionados: {0}',
	7018: 'La dirección de correo electrónico no debe ser nula.',
	7019: 'Nombre no debe ser null.',
	7020: 'Apellido no debe ser nulo',
	7021: 'La inicial del usuario no debe ser nula.',
	7022: 'El usuario de la oficina primaria no debe ser nulo.',
	7023: 'El nombre de inicio de sesión del usuario no debe ser nulo.',
	7024: 'El rol de usuario EY no debe ser nulo.',
	7025: 'El rol de ususario del compromiso no debe ser nulo.',
	7026: 'No se puede completar la operación. Inténtelo de nuevo y si el problema persiste, póngase en contacto con el Help Desk.',
	7027: 'Dirección de correo electrónico no válida',
	7028: 'Error en la llamada de usuario de Patch Engagement.',
	7029: 'Usuario del compromiso: Estatus del ID del usuario del compromiso no válido.',
	7030: 'Usuario del compromiso: Rol del ID del usuario del compromiso no válido.',
	7031: 'Uno o más ID de usuario del compromiso no encontrados.',
	7032: 'El correo electrónico no debe tener más de 250 caracteres.',
	7033: 'El usuario solicitado no puede ser nulo.',
	7034: 'Error en la cola del procesador de mensajes universal.',
	7035: 'Las iniciales no deben superar los 3 caracteres.',
	7036: 'El nombre no debe superar los 250 caracteres.',
	7037: 'El apellido no debe superar los 250 caracteres.',
	7038: 'Error al crear una llamada de usuario externa.',
	7039: 'No se puede invitar a uno o varios usuarios porque ya son miembros activos o pendientes del equipo. Actualice la página e inténtelo de nuevo, si el problema persiste, póngase en contacto con el Help Desk.',
	7040: 'El nombre no debe superar los 250 caracteres.',
	7041: 'Apellido no debe exceder de 250 caracteres',
	7042: 'Iniciales no deben exceder 3 caracteres',
	7043: 'GPN no debe de ser mayor a 250 caracteres',
	7044: 'GUI no debe de ser mayor a 250 caracteres',
	7045: 'Otener usuario externo por Id fallo',
	7046: 'El usuario no debe de ser null',
	7047: 'No se pueden guardar lo cambios en este momento. Intente de nuevo o si el problema persiste, contacte al Help Desk',
	7048: 'La pagina principal de EY Canvas no es accesible en este momento y es requerida para editar. Intente de nuevo si el problema persiste, contacte al Help Desk',
	7049: 'Llamada del usuario para actualizar el compromiso fallo',
	7050: 'Los miembros no pueden ser desactivados. El compromiso debe al menos tener un miembro que tenga permisos para adminisrar el compromiso. Actualice la selección e intente de nuevo. Si el problema persiste, contacte al Help Desk',
	7051: 'Otener usuario interno por Id fallo',
	7052: 'Usuario no encontrado',
	7053: 'Obtener por ID no encontrado',
	7054: 'Obtener links rapidos por ID del compromiso fallo',
	7055: 'Derechos insuficienctes para agregar a miembros previos. Hable con un miembro del compromiso con los derechos apropiados para tomar esta accion',
	7056: 'EY Canvas no puede salvar los cambios en este momento. Intente de nuevo si el problema persiste, contacte al Help Desk',
	7057: 'Derechos insuficientes para agregar nuevos miembros. Hable con un miembro del compromiso con los derechos apropiados para tomar esta accion',
	7058: 'El estado del usuario ha cambiado. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el Help Desk.',
	7062: 'EY Canvas Client Portal no es accesible en este momento y es necesario para actualizar la información de miembro existente. Inténtelo de nuevo y si el problema persiste, póngase en contacto con el Help Desk.',
	7063: 'No se pueden desactivar miembros externos. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el Help Desk.',
	7064: 'Operación de parche no válida.',
	7065: '{0} {1} no se pueden activar en uno o varios grupos de acceso. El dominio no está permitido para los grupos seleccionados: {2}.',
	7066: '{0} no es un usuario válido.',
	7067: 'Error al poner la llamada de usuario externa.',
	7068: 'EY Canvas Client Portal no es accesible en este momento y es necesario para actualizar la información de miembro existente. Inténtelo de nuevo y si el problema persiste, póngase en contacto con el Help Desk.',
	7069: 'Los grupos de acceso seleccionados no están activos: {0}. Eliminelos e intentelo de nuevo.',
	7072: 'Se ha producido un error durante el proceso de desvinculación del compromiso. Actualice la página e inténtelo de nuevo. Póngase en contacto con el Help Desk si el error persiste.',
	7074: 'Los cambios no se pueden guardar. El compromiso debe tener al menos un miembro activo que tenga permisos para administrar el compromiso y haya resuelto la independencia. Si el problema persiste, póngase en contacto con el Help Desk.',
	7079: 'Error en la llamada de independencia de envío.',
	7080: 'No se permite el id de usuario no válido o el envío de independencia, ya que el ID de usuario no pertenece al usuario que ha iniciado sesión.',
	7081: "Complete todas las preguntas antes de hacer clic en'Enviar'. Utilice la opción'Mostrar incompleto' para filtrar las preguntas incompletas. Si el problema persiste, llame al servicio de asistencia. ",
	7082: 'No se ha encontrado ningún documento de independencia para la solicitud.',
	7083: 'Error en la solicitud de resumen de SDM.',
	7084: 'El ID de usuario no es válido o No se permite la acción de independencia para el usuario que ha iniciado sesión.',
	7085: 'El comentario de independencia debe tener menos de 4.000 caracteres.',
	7086: 'Error en la llamada a la acción de independencia.',
	7087: 'Su rol debe ser Socio a cargo; Socio de participación; o Director Ejecutivo para conceder, denegar o anular el acceso a este usuario.',
	7088: 'Error al enviar cambios por error, inténtelo de nuevo más tarde.',
	7098: 'Indentificador de tipo de PACE no válido',
	7099: 'No hay una plantilla de Independence disponible, inténtelo de nuevo y, si el problema persiste, llame a mesa de ayuda.',
	7154: 'No se encuentran usuarios para el formulario de pantalla actual',
	7155: 'El perfil de envío no está permitido para la interacción restaurada.',
	7156: 'El contenido se está actualizando. Inténtalo de nuevo más tarde. Si el problema persiste durante mucho tiempo, póngase en contacto con el servicio de ayuda de TI.',
	7158: 'El documento no está disponible. Actualiza la página y vuelve a intentarlo. Si el problema persiste, póngase en contacto con mesa de ayuda.',

	/*SCOT*/
	8001: 'Llamada para Crear el SCOT fallo',
	8002: 'Nombre del SCOT faltante',
	8003: 'SCOT Faltante',
	8004: 'Llamada para obtener el SCOT fallo',
	8005: 'ID del compromiso invalido',
	8006: 'Id de aseveracon invalido',
	8007: 'Llamada para obtener el SCOT fallo',
	8008: 'Requisicion invalida',
	8009: 'The selected SCOT has been deleted. Close this modal to see the updated list.',
	8010: 'SCOT ID no puede ser nulo o vacío.',
	8011: 'SCOT ID debe ser mayor que cero.',
	8012: 'El ID de documento no es válido.',
	8013: 'Error en la llamada de actualización de Scot.',
	8014: 'El nombre SCOT no puede ser nulo o vacío.',
	8015: 'El nombre SCOT no puede tener más de 500 caracteres.',
	8016: 'El tipo de estrategia SCOT no es válido.',
	8017: 'El tipo de SCOT no es válido.',
	8018: 'Scot ITApplication no es válido.',
	8019: "SCOT - Aplicación de TI debería estar vacía cuando se aplica'No tiene aplicación de TI' ",
	8028: 'The selected SCOT has been deleted. Close this modal to see the updated list.',

	/*User*/
	10001: 'Preferencias de usuario para accesar no encontrados',
	10002: 'Error en la llamada de obtener todos los usuarios.',
	10003: 'Error al recibir llamada de presencia de usuario.',
	10005: 'No se pueden recuperar los detalles del usuario',

	/*Risk Type*/
	11001: 'Solicitud de creación no válida.',
	11002: 'Falta el nombre del tipo de riesgo.',
	11003: 'Falta el tipo de riesgo.',
	11004: 'Error en la llamada al obtener el tipo de riesgo.',
	11005: 'Identificación de compromiso no válida.',

	/*TaskDocuments*/
	80004: 'Una o más tareas no son válidas.',
	80005: 'El ID del documento no es válido.',

	/*Edit Control*/
	83001: 'Error al obtener los controles',
	83002: 'Error sl obtener el control por id.',
	83003: 'El ID de control es nulo o está vacío.',
	83004: 'Id. de control no válido',
	83005: 'El ID del documento no es válido.',
	83006: 'Falta el nombre del control.',
	83007: 'Longitud del nombre de control no válido.',
	83008: 'Solicitud no válida.',
	83009: 'Frecuencia de control no válidaId.',
	83010: 'TypeId de control no válido.',
	83011: 'Aplicaciones de control inválidas.',
	83012: 'DesignEffectivenessTypeId. de control no válido.',
	83013: 'ITApplicationIds no válidos.',
	83014: 'Solo se permiten aplicaciones de tipo SO IT, si el tipo de control es de tipo de prevención manual o de detección manual.',
	83015: 'Solo ITDM puede tener seleccionadas las pruebas IPE manuales.',
	83016: 'Los controles de menor riesgo no están permitidos para los compromisos con este perfil.',
	83017: 'Duplicar FilterSCOTs.',
	83018: 'El nombre del control tiene más de 500 caracteres.',
	83019: 'Identificaciones de WCGW no válidas.',
	83020: 'No se permiten identificaciones de WCGW duplicadas.',
	83021: 'No se permiten identificadores de aplicación de IT',
	83022: 'El parámetro {0} no es válido.',
	83023: 'Página actual no válida.',
	83024: 'Tamaño de página no válido.',
	83025: 'La cadena de búsqueda no debe tener más de 100 caracteres.',
	83026: 'IT app sin organización de servicios solo puede asociarse a un control de tipo ITDependentManualControl o ITApplicationControl',
	83027: 'Filtro duplicadoWCGWs.',
	83028: 'Id. de control de conocimiento no válido.',

	112000: 'No se han encontrado documentos para los documentos de origen y de destino.',
	112001: 'No se puede realizar la llamada porque la solicitud es nula.',
	112002: 'El cuerpo de la solicitud no puede ser nulo ni estar vacío en la llamada.',
	112003: 'El documentId de origen y de destino no debe ser el mismo.',
	112004: 'El documentId de origen no debe ser nulo ni estar vacío.',
	112005: 'El documentId de destino no debe ser nulo ni estar vacío.',
	112006: 'Source EngagementId no debe ser nulo ni estar vacío.',
	112007: 'Target EngagementId no debe ser nulo ni estar vacío.',
	112008: 'El documento de origen no es válido para un compromiso determinado.',
	112009: 'El documento de destino no es válido para una participación determinada.',
	112010: 'No se encontró el engagement de destino.',
	112011: 'Roles insuficientes para vincular formularios al compromiso de origen. Trabaje con un administrador de compromiso para obtener derechos suficientes.',
	112012: 'Roles insuficientes para vincular formularios para el compromiso de destino. Trabaje con un administrador de interacción para obtener derechos suficientes.',
	112013: 'Usuario no válido para  el compromiso de origen.',
	112014: 'Usuario no válido para el compromiso de destino.',
	112015: 'Tipo de documento de origen no válido para compartir.',
	112016: 'Tipo de documento de destino no válido para compartir.',
	112017: 'Los tipos de documento de origen y destino no coinciden.',
	112018: 'Los FormId de conocimiento de origen y de destino no coinciden.',
	112019: 'El enlace para compartir ya existe para los documentos de origen y destino.',
	112020: 'El espacio de trabajo de interacciones de origen y destino no coincide.',
	112021: 'El documento de destino no puede ser un destino.',
	112022: 'La actividad seleccionada ya está compartida con otras actividades y no se puede seleccionar para compartirla más.',
	112023: 'El documento de origen no puede ser un destino.',
	112024: 'No se puede encontrar el identificador de compromiso del documentId de destino.',
	112025: 'No se puede encontrar el identificador de compromiso del documentId de origen.',
	112026: 'El documento de origen del documentId de origen y de destino no debe ser igual.',
	112027: 'No se puede continuar con el envío del perfil ya que el compromiso ha compartido las actividades de habilitación de EY Canvas FIT. Desvincular las actividades para continuar.',
	112028: 'El EngagementId de origen y de destino no debe ser igual.',
	112029: 'El EngagementId y el documentId de origen o destino deben coincidir con el EngagementId y el documentId de la ruta.',
	112030: 'El documento no es válido para un compromiso determinado.',
	112031: 'DocumentId no debe ser nulo ni estar vacío.',
	112032: 'EngagementId no debe ser null ni estar vacío.',
	112033: 'Los identificadores de documento objetivo deben ser únicos.',
	112034: 'Documentos de destino o de origen ya compartidos.',
	112035: 'Para el documento de relación de respuesta vinculado existente, solo puede haber un único objetivo.',

	/*MissingDocument*/
	116001: 'Create form failed.',
	116002: 'No se ha encontrado ningún documento de conocimiento para el DocumentTypeID especificado.',
	116004: 'Error en la creación del documento. Actualice o inténtelo de nuevo después de un tiempo. Si el problema persiste, póngase en contacto con Mesa de ayuda.',

	/* Annotation Errors*/
	12001: 'Parámetros de solicitud no válidos.',
	12002: 'Error al crear llamada de anotación.',
	12003: 'Error en Obtener anotación.',
	12004: 'Identificación de compromiso no válido.',
	12005: 'Los identificadores de la colección deben ser mayores que cero.',
	12006: 'La colección de identificadores no puede estar vacía.',
	12007: 'Identificación de tarea no válido.',
	12008: 'Identificación de documento no válido.',
	12009: 'Debe tener DocumentId o Task ID válidos.',
	12010: 'Las respuestas requieren el padre.',
	12011: 'Identificación de estado no válido.',
	12012: 'El tipo de documento debe ser 440GL.',
	12013: 'Tipo de annotación no válido.',
	12014: 'Usuario de engagement no válido.',
	12015: 'Este documento ha sido borrado por otro usuario.',
	12016: 'Error al actualizar la llamada de anotación.',
	12017: 'La nota a la que está respondiendo ha sido eliminada por otro miembro del equipo. Actualice la página e inténtelo de nuevo.',
	12018: 'El tipo de acción de cambio de anotación no debe ser Nulo.',
	12019: 'Nota de revisión eliminada',
	12020: 'Acción no válida que realizar.',
	12021: 'El usuario no es autor de la anotación.',
	12022: 'Falta usuario de creación.',
	12023: 'El usuario de creación no existe ni pertenece al compromiso.',
	12024: 'Se requiere un usuario asignado.',
	12025: 'El usuario asignado no existe ni pertenece al compromiso.',
	12026: 'Comentarios no válidos.',
	12027: 'Los comentarios no deben estar vacíos.',
	12028: 'Fecha de vencimiento requerida.',
	12029: 'Se requiere prioridad válida.',
	12030: 'El estado de esta nota ha cambiado. Ya no puede editar ni responder a la nota. Por favor cierre y vuelva a abrir la ventana para ver los datos actualizados y continuar editando.',
	12031: 'La anotación debe ser de nivel superior.',
	12032: 'La anotación no debe ser de nivel superior.',
	12033: 'Al menos uno de los siguientes valores no debe de estar vacio: tipo prioritario, fecha limite, estatus o usuario asigando',
	12034: 'EL comentario excede la extension maxima (4,000 caracteres)',
	12035: 'Busqueda excede el maximo de longitud (500 caracteres)',
	12036: 'Unicamente el ID de tarea o ID de documento pueden ser aceptados ambos no estan permitidos',
	12037: 'Operación de Patch invalida',
	12038: 'Esta tratando de editar una nota que no existe. Actualice la pagina e intente de nuevo',
	12039: 'Esta tratando de editar la misma nota que otro miembro del equipo. Actualice la pagina e intente de nuevo.',
	12040: 'Llamada de obtener la anotacion de usuarios fallo',
	12041: 'Obtener la anotacion de usuarios fallo. Query invalido',
	12042: 'El tipo de documento no es valido para crear una anotacion',
	12043: 'Esta tratando de editar una nota a una tarea o documento que no existe. Actualice la pagina e intente de nuevo',
	12044: 'Esta tratando de editar una respuesta a una nota que no existe. Actualice la pagina e intente de nuevo',
	12045: 'Nota de revision no encontrada',
	12046: 'La nota seleccionada ya ha sido eliminada por otro usuario.',
	12047: 'Unicamente puede borrar respuesta a notas que estan en status de abierto. Actualice la pagina e intente de nuevo',
	12048: 'Esta tratando de cambiar el estatus a una nota que ya no existe. Actualice la pagina e intente de nuevo',
	12049: 'Esta tratando de borrar o replicar una nota que ha sido borrada por otro miembro del equipo. Actualice la pagina e intente de nuevo',
	12050: 'Unicamente puede borrar notas que estan en status de cerrado',
	12051: 'Las anotaciones de tipo comentario solo se pueden crear para documentos Helix válidos.',
	12052: 'La anotación de tipo Comentario que está buscando ha sido eliminada',
	12060: 'Se requiere número de referencia, debe ser mayor que 0 e inferior a 1000.',
	12061: 'El número de referencia debe ser nulo.',
	12062: 'Las anotaciones solo se pueden crear para un comentario.',
	12066: 'Está intentando responder a una nota que no está abierta.',
	12067: 'Está intentando eliminar una respuesta a un comentario que ya ha sido eliminado por otro miembro del equipo. Actualice la página y vuelva a intentarlo.',
	12068: 'Está intentando agregar una nota con una grabación que no es válida o que ya no existe. Actualice la página y vuelva a intentarlo.',
	12069: 'Está intentando actualizar una nota con una grabación que no es válida o que ya no existe. Actualice la página y vuelva a intentarlo.',
	12070: 'Solo puede agregar un comentario por estado financiero. Edite el existente.',
	12071: 'No se pudo eliminar la información. Inténtalo de nuevo. Si el problema persiste, comuníquese con la mesa de ayuda.',

	/*FlowchartStepControl*/
	123054: 'Control relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',
	123045: 'This control is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepWCGW*/
	123022: 'Este paso del diagrama de flujo ya no está disponible. Actualice la página y vuelva a intentarlo. Póngase en contacto con Servicio de Soporte si el error persiste.',
	123023: 'Este paso del diagrama de flujo ya no está disponible. Actualice la página y vuelva a intentarlo. Póngase en contacto con Servicio de Soporte si el error persiste.',
	123055: 'WCGW relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepITApplicationSO*/
	123056: 'IT Application / service organization relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the issue persists.',



	/*FlowchartStepDocument*/
	123048: 'La relación del documento no se puede editar. Actualice la página y vuelva a intentarlo. Póngase en contacto con Servicio de Soporte si el error persiste.',
	123033: 'Este paso del diagrama de flujo ya no está disponible. Actualice la página y vuelva a intentarlo. Póngase en contacto con Servicio de Soporte si el error persiste.',
	123002: 'Este documento ya no está disponible. Actualice la página y vuelva a intentarlo. Póngase en contacto con Servicio de Soporte si el error persiste.',

	/*Configuration*/
	13001: 'Configuraciones no encontradas',
	13002: 'Obtener configuraciones de API fallo',

	/*Documents*/
	14001: 'No se puede desarrollar la llamada, debido a que la requesicion es null',
	14002: 'Documento no encontrado, la llamada fallo',
	14003: 'El Id del compromiso debe ser mas grande a cero',
	14004: 'El Id del documento no debe ser null o vacio',
	14005: 'Error mientras se trataba de asociar las tareas. La llamada fallo',
	14006: 'El documento seleccionado no puede ser borrado. Intente de nuevo. Si el problema persiste contacte al Help desk',
	14007: 'Un error inesperado a ocurrido',
	14008: 'Un error inesperado a ocurrido durante la firma',
	14009: 'Un error ocurrio mientras se agregaba una firma',
	14010: 'El id de aprobacion no debe ser mayor a cero',
	14011: 'Un error ocurrio mientras delegaba la firma',
	14012: 'El cuerpo no puede estar vacio',
	14013: 'El documento seleccionado no puede ser desvinculado. Intente mas tarde. Si el problema persiste contacte al Help Desk',
	14014: 'Obtener la cuenta por Documento fallo',
	14015: 'Entidad relacionada invalida',
	14016: 'Obtener la aprobacion del documento fallo',
	14017: 'Obtener los hallazgos de todos los documentos, fallo',
	14018: 'Obtener todos los documentos fallo',
	14019: 'No se ha encontrado la firma de registro.',
	14020: 'Valor de accion invalida',
	14021: 'tipo de hallazgo invalido',
	14022: 'EL documento no pertenece al compromiso',
	14023: 'EL tipo de cambio del documento no es valido',
	14024: 'Obtener todos los documentos fallo. El parametro no es valido',
	14025: 'Un error ocurrio mientras se creaba la revision del documento. La llamada de API fallo',
	14026: 'Un error ocurrio mientras se borraba la revision del documento. La llamada de API fallo',
	14027: 'El Id del compromiso no debe ser null o vacio',
	14028: 'El Id de usuario es invalido',
	14029: 'EL usuario no esta autorizado para desarrollar esta accion',
	14030: 'No se encontro un documento con el Id de pase en este compromiso',
	14031: 'El Id de revision del documento no es valido',
	14032: 'El documento de revision no fue encontrado',
	14033: 'EL documento ha sido aprobado',
	14034: 'Un proceso de desvincular, esta en proceso para otro compromiso en este espacio de trabajo o el documento ha sido previamente desvinculado. Actualice la pagina e intente de nuevo. Si el problema persiste contacte al Help Desk',
	14035: 'El documento no esta compartido con el compromiso original',
	14036: 'El numero de version debe ser mayor a cero',
	14037: 'La operación no puede ser completada en este momento. Actualice la pagina e intente de nuevo. Si el problema persiste contacte al Help Desk',
	14038: 'La documentacion de las razones del cambo, fallo',
	14039: 'La documentacion de las razones del cambo por ID, fallo',
	14040: 'La actualizacion de la razon del cambio, fallo',
	14041: 'Cambio de razon invalido',
	14042: 'La actualizacion de la razon del cambio, fallo',
	14043: 'La razon de crear el cambio, fallo',
	14044: 'La razon de borrar el cambio, fallo',
	14045: 'Id de la razon de cambio es invalida',
	14046: 'El documento no esta disponible. Actualice la pagina e intente de nuevo. Si el problema persiste contacte al Hep Desk',
	14047: 'El documento ya tiene una razon de cambio asignada',
	14048: 'El documento tiene un conflicto, de favor resolver antes de proceder',
	14049: 'Los datos invalidos para revisar el documento es agregado al equipo o usuario',
	14050: 'Error mientras el documento de referencia es removido',
	14052: 'La busqueda de texto excede la longitud maxima permitida (500 caracteres)',
	14053: 'No se puede hacer la llamada debido a que la requesicion es null',
	14054: 'Operación del patch invalida',
	14055: 'La historia de documentos resueltos fallo',
	14056: 'Un error ha ocurrido cuando se tató de hacer queue al mensaje',
	14057: 'Un documento con el mismo nombre existe en el compromiso',
	14058: 'Se ha encontrado más de una versión de documento con el mismo número. Póngase en contacto con el departamento de soporte técnico para obtener más ayuda.',
	14059: 'No se pudo encontrar la versión del documento seleccionado. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el departamento de soporte técnico para obtener más ayuda.',
	14060: 'La operación no se pudo completar en ese momento. Inténtalo de nuevo más tarde. Si el problema persiste, póngase en contacto con el departamento de soporte técnico para obtener más ayuda.',
	14061: 'El documento no se comparte, no se puede desvincular el documento. Si el problema persiste, póngase en contacto con el departamento de soporte técnico para obtener más ayuda.',
	14062: 'El identificador de tipo de confidencialidad del documento no es válido.',
	14063: 'No se puede cambiar el tipo de confidencialidad de este tipo de documento.',
	14064: 'El usuario no tiene permiso.',
	14065: 'Descripción personalizada no válida.',
	14066: 'Error al actualizar la extensión.',
	14067: 'Extensión no válida.',
	14068: 'Error al obtener extensiones de documento.',
	14069: 'Este documento ya ha sido desvinculado por otro miembro del equipo. Actualice la página y póngase en contacto con el departamento de soporte técnico si el error persiste',
	14070: 'Invalid file type.',
	14071: 'The selected document version is no longer available. Please close and reopen this window to see the latest set of historical versions for the document.',
	14072: 'El ID del documento de origen no debe ser nulo ni estar vacío.',
	14073: 'Los identificadores del formulario de Canvas no deben ser nulos ni estar vacíos.',
	14074: 'Los identificadores del formulario de Canvas no deben ser duplicados.',
	14075: 'Error al asociar el documento a formularios de EY Canvas.',
	14076: 'No se encontraron formularios de Canvas asociados al documento de origen.',
	14077: 'No se encontró el documento de origen. Error de llamada.',
	14078: 'El documento actual ya está asociado con formularios de Canvas entregados',
	14079: 'This document has been deleted and therefore it cannot be opened.',
	14080: 'The source approval user id is invalid.',
	14081: 'The source approval user id should valid GUID and must not be empty GUID.',
	14082: 'The modify user id is invalid.',
	14083: 'The modify user id should be a valid GUID and must not be empty GUID.',
	14084: 'File name cannot include: */:<>\\?|""',
	14085: 'The document name exceeded maximum length allowed.',
	14086: 'DocService failed while updating document details.',
	14087: 'The input is not valid.',
	14088: 'A input has duplicate document names.',
	14089: 'The bookmark observation is not valid.',
	14090: 'Request status has changed. Please refresh the page and try again if required. If the issue persists, contact the help desk.',
	14091: 'This request has been deleted. Refresh the page to view updated data. If the issue persists, contact the Help Desk.',
	14092: 'Document not eligible for update. Refresh the page and try again.  If the issue persists, contact the Help Desk.',

	/*SEM*/
	15001: 'Obtener el resumen de SEM para una Id de cuenta fallo, debido a una requisicion invalida',
	15002: 'Id de la cuenta invalido',
	15003: 'ID del compromiso invalido',
	15004: 'Tipo de resumen invalido',
	15005: 'La cuenta relacionada no puede ser encontrada. Actualice la pagina e intente de nuevo. Contacte al help desk si el problema persiste',

	/*Timephase*/
	16001: 'Obtener las fases de tiempo fallo',
	16002: 'El compromiso debe de ser mayor a cero',
	16003: 'La fase del tiempo debe ser mayo a cero',
	16004: 'Valor de Id de la tarea invalida',
	16005: 'Valor de la fase de tiempo invalida',

	/*Validations*/
	17001: 'id de paso de construccion o Id de tipo de documento faltante',
	17003: 'The document could not be found. Refresh the page and try again. If the issue persists, contact Help Desk.',

	/*TaskGroupSection*/
	18001: 'Obtener toda la seccion del grupo d etareas fallo',

	/*Assignments*/
	19001: 'Llamada de creacion de asignacion fallo',
	19002: 'Llamada de obtener asignacion, fallo',

	/*Client Request*/
	21001: 'La llamada de requisicion de asociacion del cliente, fallo',

	/*Related Components*/
	22001: 'La llamada de obtener componentes relacionados, fallo',

	/*Component Ceation*/
	22022: 'El nombre del componente ya existe en este compromiso',
	22024: 'El componente al que está intentando enviar instrucciones no está disponible o ya se ha eliminado.',
	22027: 'Instrucción grupal encontrada sin fecha de vencimiento.',
	22028: 'La instrucción de ámbito aún no se ha publicado para el componente.',
	22029: 'No se ha publicado ninguna instrucción nueva para el componente que se va a enviar.',

	22040: 'No se pudieron enviar instrucciones. Compruebe que el tipo de compromiso sea correcto.',
	22048: 'El componente que está intentando actualizar está copiado de otro compromiso mientras se copia el compromiso.',

	/*Send Instruction*/
	22049: 'Group instructions cannot be sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk.',

	/*User Presence*/
	23001: 'La presencia de logged in no fue encontrada',
	23002: 'La llamada de presencia de usuario fallo',
	23003: 'La validacion de la presencia del documento del usuario, fallo',
	23004: 'La llamada de presencia de borrado de usuario, fallo',
	23005: 'El documento ya no esta abierto por otro usuario. Actualice la pagina e intente de nuevo. Contacte al Help desk si el problema persiste',

	/* Forms */
	24001: 'La llamada de obtener la forma, fallo',
	24002: 'Id de compromiso invalido',
	24003: 'El Id del documento no debe de ser null o vacio',
	24005: 'No se encontró el encabezado del formulario.',
	24004: 'No se pudo encontrar el documento. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con mesa de ayuda.',
	24006: 'Section is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24007: 'Parámetros de solicitud no válidos.',
	24008: 'El identificador de encabezado no debe ser nulo ni vacío.',
	24009: 'El identificador de sección no debe ser nulo ni vacío.',
	24010: 'Error en la operación de actualización de la respuesta del cuerpo del formulario.',
	24011: 'Solicitud no válida para la actualización de la respuesta del cuerpo del formulario.',
	24012: 'Body is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24013: 'Identificador de opción de cuerpo de formulario no válido.',
	24014: 'Identificador de tipo de cuerpo de formulario no válido.',
	24015: 'Cuerpo de solicitud no válido para un identificador de tipo de cuerpo determinado.',
	24016: 'Texto libre no válido para un identificador de tipo de cuerpo determinado.',
	24017: 'Se logro el maximo de caracteres, incluyendo las etiquetas de formato de texto rico. Reduzca la longitud o remueva el formato innecesario e intente de nuevo',
	24018: 'Para un tipo de cuerpo dado, no se permite la respuesta de identificación.',
	24019: 'El identificador del cuerpo no debe ser nulo ni vacío.',
	24020: 'Requisicion del cuerpo es invalida',
	24021: 'el cuerpo fue borrado',
	24022: 'El país no debe ser nulo o vacío.',
	24023: 'El lenguaje no debe ser nulo o vacío.',
	24024: 'La subsericeLine no debe ser nula o vacía.',
	24025: 'GamLayers no debe ser nulo o vacío.',
	24026: 'Error en la llamada de creación de encabezados.',
	24027: 'Solicitud no válida para la creación de encabezados.',
	24028: 'Error en la llamada de creación de la unidad duplicada.',
	24029: 'Tipo de unidad de formulario no válido.',
	24030: 'Se ha suprimido la sección. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	24031: 'El cuerpo del texto no es texto de cuerpo personalizado.',
	24032: 'La solicitud de creación de encabezados no es válida.',
	24033: 'El identificador de entidad del documento suministrado no es válido.',
	24034: 'El identificador de entidad suministrado no es válido.',
	24035: 'Se ha producido un error al crear un documento relacionado.',
	24036: 'Se ha producido un error al eliminar el documento relacionado.',
	24037: 'ElIDdeldocumentorelacionado no puede ser nulo o vacío.',
	24038: 'El documento no es válido o no existe.',
	24039: 'El documento relacionado no es válido o no existe.',
	24040: 'Error en la creación del cuerpo personalizado.',
	24041: 'La solicitud de creación de cuerpos no es válida.',
	24042: 'La solicitud de creación de la sección no es válida.',
	24043: 'la seccion obtenida por ID falló',
	24044: 'La creación de la sección falló.',
	24045: 'Página actual no válida.',
	24046: 'Tamaño de página no válido.',
	24047: 'Identificador de objeto relacionado con el documento no válido.',
	24048: 'El objeto ya está relacionado con el formulario de Canvas.',
	24049: 'Objeto no encontrado.',
	24050: 'La entidad UIdanterior no es válida.',
	24051: 'Si se proporciona EntityId, EntityUid debe proporcionarse y viceversa.',
	24052: 'Error en la creación de instantáneas de formulario de lienzo.',
	24053: 'Error en el encabezado para obtener ID.',
	24054: 'El cuerpo obtenido por el ID de la llamado ha fallado',
	24055: 'Se ha producido un error al crear el perfil de formulario.',
	24056: 'Document FormProfile ya existe.',
	24057: 'Error en la validación de Document FormProfile.',
	24058: 'Document FormProfile no existe.',
	24059: 'La sección no es personalizada.',
	24060: 'Error en la validación del perfil del formulario del documento. Si PCAOB-IA es verdadera, entonces PCAOB-FS debería ser verdadera.',
	24061: 'Error en la validación del perfil del formulario del documento. Si PCAOB-FS es falso, entonces PCAOB-IA debería ser falso.',
	24062: "Error en la validación del perfil del formulario del documento. Si es'No complejo', entonces'PCAOB - FS' /'PCAOB - IA' debería ser falso. ",
	24063: 'CountryId no es válido.',
	24064: 'LanguageId no es válido.',
	24065: 'El encabezado no es personalizado.',
	24066: 'Error en la creación de objetos relacionados con la sección de formulario.',
	24067: 'Objeto no encontrado.',
	24068: 'Wcgw no está relacionado con un Scot.',
	24069: 'El parámetros {0} no es válido.',
	24070: 'La entidad primaria Uid anterior no es válida.',
	24071: 'Error en el objeto relacionado con la sección de formulario.',
	24072: 'La sección no está disponible. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con mesa de ayuda.',
	24073: 'No se puede recuperar la instantánea. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con mesa de ayuda.',
	24074: 'Las instantáneas no están disponibles para el documento seleccionado. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	24075: 'Identificador de instantánea no válido.',
	24076: 'Entity Id no debe ser nulo ni estar vacío',
	24077: 'El ID del objeto relacionado con la sección de formulario anterior no es válido.',
	24078: 'El identificador del objeto relacionado en la sección del formulario no debe ser nulo ni estar vacío.',
	24079: 'El ID de entidad principal proporcionado no es válido.',
	24080: 'FormSectionRelatedObject : No se ha encontrado el registro de entidad principal.',
	24081: 'Objeto no encontrado',
	24082: 'Objeto no encontrado',
	24083: 'Se ha producido un error en la actualización del perfil del formulario.',
	24084: 'Document FormProfile no existe.',
	24085: 'El identificador de idioma debe ser mayor que 0.',
	24086: 'El identificador de país debe ser mayor que 0.',
	24087: 'Los documentos de conocimiento entregados no se pueden actualizar. Actualice el perfil de participación para cambiar este perfil de formularios.',
	24088: 'Roles insuficientes para editar contenido. Trabaje con un administrador de interacción para obtener suficientes derechos.',
	24089: 'El nombre del encabezado personalizado ha superado la longitud máxima (500 caracteres). Ajuste el nombre y vuelva a intentarlo.',
	24090: 'El nombre de la sección personalizada ha superado la longitud máxima (500 caracteres). Ajuste el nombre y vuelva a intentarlo.',
	24091: 'La sección Etiqueta personalizada ha superado la longitud máxima (100 caracteres). Ajuste el nombre y vuelva a intentarlo.',
	24092: 'El nombre del cuerpo personalizado ha superado la longitud máxima (500 caracteres). Ajuste el nombre y vuelva a intentarlo.',
	24093: 'El nombre de sección personalizado no debe ser nulo ni estar vacío.',
	24094: 'Nombre del cuerpo no debe ser nulo o vacío.',
	24096: 'El nombre de encabezado personalizado no debe ser nulo ni estar vacío.',
	24097: 'La sobrescritura no se puede completar en este momento.',
	24098: 'El identificador de tipo de documento de origen o de destino no es válido.',
	24099: 'El identificador del formulario de conocimiento del documento de origen y de destino no es el mismo.',
	24100: 'El ID de documento de origen no debe ser nulo ni estar vacío.',
	24101: 'El ID de documento de destino no debe ser nulo ni estar vacío.',
	24103: 'Se ha eliminado el encabezado. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	24104: 'El encabezado no se puede editar.',
	24105: 'El cuerpo no se puede editar.',
	24106: 'La acción de actualización no es válida.',
	24107: 'Error en la validación de Document FormProfile. Si PCAOB-FS es true entonces complejo debe ser true.',
	24108: 'Error en la validación de Document FormProfile. Si PCAOB-IA es verdadero entonces PCAOB-FS debe ser verdadero.',
	24110: 'Actualización de contenido actualmente en curso. Actualice manualmente el contenido de este formulario en la página de actualización de contenido del formulario de Canvas una vez completada la actualización de contenido actual.',
	24111: 'Los valores de Mapa y Desvinculación no pueden ser los mismos.',
	24112: 'El documento de origen no debe compartirse.',
	24114: 'No se pudo agregar la evidencia. Actualice la página e inténtelo de nuevo.',
	24115: 'No se pudo quitar la evidencia. Actualice la página e inténtelo de nuevo.',
	24116: 'No se pudo enviar el perfil. Actualice la página e inténtelo de nuevo.',
	24117: 'El formulario tiene respuestas incompletas. Actualice la página e inténtelo de nuevo.',
	24118: 'El ID del documento y el ID del documento relacionado no pueden ser iguales.',
	24119: 'Error al editar objetos relacionados en el campo.',
	24120: 'El ID del objeto relacionado con el cuerpo no debe ser nulo ni estar vacío.',
	24121: 'Objeto no encontrado.',
	24122: 'Falta token de concurrencia',
	24124: 'Se han encontrado documentos de destino no válidos.',
	24125: 'Formulario(s) de canvas de destino no encontrado(s).',
	24126: 'La solicitud no debe ser nula o vacía.',
	24127: 'La importación de datos de EY Helix no tuvo éxito. Importe datos de nuevo desde la configuración de EY Helix e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	24155: 'El perfil de envío no está permitido para el compromiso restaurado.',
	24164: 'No tiene los permisos adecuados para realizar cambios en la plantilla.',
	24166: 'No se permite guardar perfil. El formulario no está disponible para este perfil.',
	24167: 'El tipo de cuerpo no se puede actualizar',
	24168: 'Modify user ID no debe ser nulo ni estar vacío',
	24169: 'Modify User ID solo puede tener valor cuando linkedResponseUpdate es false',
	24170: 'ID de tarea no válido en la solicitud',
	24171: 'IDs de cuerpo duplicados en la solicitud',
	24172: 'Error en la llamada de comparación de secciones',
	24173: 'Los identificadores de documento no pueden estar vacíos. Además, no puede exceder los 50 documentos distintos a la vez',
	24174: 'El documento de ruta debe formar parte de los Document IDs del cuerpo de la solicitud',
	24175: 'Identificador de entidad de evidencia inteligente no válido en el cuerpo de la solicitud',
	24176: 'Los documentos del cuerpo deben tener el mismo identificador de formulario de conocimiento',
	24177: 'ID de sección no válido',
	24178: 'Combinación no válida de DocumentID y DocumentIDList',
	24179: 'La lista de DocumentIds no puede ser mayor a 50',
	24180: 'La materialidad no se pudo actualizar',
	24181: 'ID de cuerpo de formulario no válido',
	24182: 'ID de formulario de conocimiento o ID de cuerpo de formulario de conocimiento no válidos',
	24183: 'Se ha producido un error al relacionar el documento con este formulario. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con Servicio de Soporte.',
	24184: 'El usuario no está activo para este compromiso de EY Canvas. Invite al usuario a través de administrar equipo y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Asistencia',
	24188: 'One or more records are no longer available. Please refresh the page',

	/*Document Change Types*/
	25001: 'Obtener los cambios a tipos de documentos, fallo',
	25002: 'ID del compromiso invalido',
	25003: 'Requisicion invalida',

	/* Manage team */
	26001: 'Obtener los grupos de usuarios de grupo de clientes, fallo',
	26002: 'crear nuevo cliente grupo / el dominio de la llamada de Email fallo',
	26003: 'El nombre del grupo no debe de ser null',
	26004: 'El dominio del email no debe de ser null',
	26005: '{0} La etiqueta del dominio de email no debe de exceder 263 caracteres',
	26006: '{0} El dominio del email no debe de exceder263 caracteres',
	26007: '{0} La primera parte del dominio del email puede ser * o alfanumerico, otros caracteres especiales no estan permitidos',
	26008: '{0} Si la primera parte es una carta comodin, debe de tener una o mas partes posteriores',
	26009: '{0} La primera parte puede ser * o alfanumerico, otras partes del dominio con caracteres especiales no estan permitidas',
	26010: 'El dominio del email debe de ser unico',
	26011: 'El acceso al Id del grupo no es valido',
	26012: 'Un error ocurrio mientras se borraba el acceso al grupo. Asegurese de que no hay requisiciones ni tareas externas asignadas a este grupo o a los miembros de este grupo e intente de nuevo.',
	26013: 'El acceso al grupo ha sido borrado, de favor refresque la pagina para obtener los ultimos datos',
	26014: 'Al menos un dominio de email es requerido',
	26015: 'EL EY Canvas Client Portal no es accesible en este momento y se requiere actualizar la informacion de los miembros actuales. Intente de nuevo y si el problema persiste contacte al Help Desk',
	26016: 'Error durante la operación de borrado',
	26017: 'La operación de obtener datos fallo',
	26018: 'Problemas de concurrencia, el acceso al grupo no esta activo',
	26019: 'La operación de guardar, fallo',
	26020: 'Los dominios de Email no pueden ser removidos cuando se tienen usuarios activos asignados. Los cambios no pueden ser Guardados',

	/* TimePhaseTypes - Milestones */
	27001: 'Obtener los detalles del milestone fallo',

	/*Client Request Counts*/
	28001: 'Obtener las requisiciones del cliente, no arroja ningun resultado',

	/*Content updates Error messages*/
	29001: 'Obtener Id no encontrado',
	29002: 'Tipo de accion no encontrado',
	29003: 'El Id del contenido no fue encontrado',
	29004: 'la actualizacion del contenido de API fallo',
	29005: 'La actualizacion del contenido en proceso. Intente de nuevo, si el problema persiste, contacte al Help Desk',
	29006: 'Parametros de requisicion invalidos',

	/*IT Process*/
	30001: 'Obtener todo el Proceso de TI, fallo',
	30002: 'Obtener todo el proceso de TI - Id del compromiso invalido',
	30003: 'El nombre ITProcess no puede estar vacío.',
	30004: 'ITProcessName no debe tener más de 500 caracteres.',
	30005: 'Esta acción no se pudo completar. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	30006: 'Error en la llamada id por proceso de obtención.',
	30007: 'Solicitud no válida.',
	30008: 'El proceso de TI ya no está disponible. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Soporte si el error persiste.',
	30009: 'Documento no encontrado.',
	30010: 'No se puede completar la eliminación. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	30012: 'No se puede completar la eliminación. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	30017: 'Esta acción no se pudo completar. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	30018: 'Esta acción no se pudo completar. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	30019: 'Esta acción no se pudo completar. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',

	/*Checklist*/
	31001: 'Obtener todo el checklist, fallo',
	31002: 'Poner todo el checklist, fallo',
	31003: 'Parametros de Checklist invalidos',
	31004: 'Error de compromiso invalido',
	31005: 'Parametros de requisicion de error invalidos',
	31006: 'Error de parametro de requisicion de chequeo invalido',

	/*Archive*/
	32001: 'Id del estatus del compromiso invalido',
	32002: 'Archive fallo',
	32003: 'V1 llamada de Archive fallo, actualice la pagina, resuelva las validaciones e intente el proceso de archivo de nuevo. Contacte al help desk si el error persiste',
	32004: 'Status de actualizacion de compromiso en LDC fallo',
	32005: 'Error del cache del compromiso, invalidado',
	32006: 'Una actualizacion de contenido esta en proceso. No puede archiva este compromiso hasta que la actualizacion de contenido este completada. Intente de nuevo mas tarde y contacte al Help Desk si el error persiste',
	32007: 'ArcGUID es nulo o vacio',
	32008: 'FileGuid es nulo o vacio',
	32009: 'FileStoreHostTcp es nulo o vacio',
	32200: 'Existen validaciones sin resolver en este comrpomiso. Actualice la pagina, resuelva las validaciones e intente de nuevo el proceso de archive. Contacte al help desk si el error continua',
	32300: 'Existen validaciones sin resolver en este comrpomiso. Actualice la pagina, resuelva las validaciones e intente de nuevo el proceso de archive. Contacte al help desk si el error continua',

	/*RBAC*/
	33001: 'EL id del compromiso no fue encontrado',
	33002: 'El id del usuario no fue encontrado',

	/*Helix Linked Projects*/
	34001: 'No se puede realiar la llamada debido a que la requisicion es nula',
	34002: 'El id del compromiso no puede ser nulo o vacio',
	34003: 'EL cuerpo no puede ser nulo o vacio en la llamada',
	34004: 'El Id del proyecto no puede ser nulo o vacio',
	34005: 'EL nomrbe del proyecto no puede ser nulo o vacio',
	34006: 'EL proyecto ya ha sido linkeado, actualice la pagina e intente de nuevo',
	34007: 'Obtener todos los proyectos de Helix fallo',
	34008: 'El Id del compromiso debe ser mas grande a cero',
	34010: 'No se pudo completar la opción guardar. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	34009: 'La identificación del proyecto ha cambiado. Actualiza la página y vuelve a intentarlo.',
	34011: 'El tipo de moneda no puede ser nulo en la llamada.',
	34012: 'El código de moneda no puede ser nulo en la llamada.',
	34013: 'La unidad de negocio no puede ser nula o vacía en la llamada.',
	34014: 'El proyecto vinculado en EY Helix ha cambiado, no se puede actualizar la configuración.',
	34017: 'No se pudo conectar a EY Helix. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	34018: 'La operación no se pudo completar. Actualice la página y vuelva a intentarlo. Si el problema persiste, comuníquese con la mesa de ayuda.',
	34019: 'Importación completada pero los datos no son válidos. Actualiza la página y haga clic en Importar para intentarlo de nuevo.',
	34027: 'Proyecto Helix está en progreso.',
	34036: 'No se pudo conectar a EY Helix. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	34039: 'Este proyecto EY Helix ya no es el proyecto principal para su compromiso. Actualice la página e inténtelo de nuevo.',
	34040: 'Debe tener el permiso <b>EY Helix Import</b> para realizar esta acción. Hable con un administrador del compromiso para obtener acceso.',

	/* PAANS */
	35001: 'La politica de autorizacion de EY, aprobaciones y servicio de notificaciones no esta disponible en este momento. No podemos confirmar si todas las politicas relevantes han sido completadas, si no reviso las politicas relevantes, actualice la pagina e intente de nuevo, si el problema persiste contacte al help desk.',

	/*Engagement Comments*/
	38001: 'Crear llamada de EngagementComments falló',
	38002: 'La llamada GetAll EngagementComments falló',
	38003: 'No se puede completar esta acción. Actualice la página e inténtelo de nuevo. Si el error persiste, póngase en contacto con el servicio de asistencia',
	38004: 'No se puede completar esta acción. Actualice la página e inténtelo de nuevo. Si el error persiste, póngase en contacto con el servicio de asistencia',
	38005: 'No se puede completar esta acción. Actualice la página e inténtelo de nuevo. Si el error persiste, póngase en contacto con el servicio de asistencia',
	38006: 'No se puede completar esta acción. Actualice la página e inténtelo de nuevo. Si el error persiste, póngase en contacto con el servicio de asistencia',
	38007: 'No se puede completar esta acción. Actualice la página e inténtelo de nuevo. Si el error persiste, póngase en contacto con el servicio de asistencia',
	38008: 'No se puede completar esta acción. Actualice la página e inténtelo de nuevo. Si el error persiste, póngase en contacto con el servicio de asistencia',
	38009: 'No se puede completar esta acción. Actualice la página e inténtelo de nuevo. Si el error persiste, póngase en contacto con el servicio de asistencia',
	38010: 'El cuerpo no debe ser nulo',
	38011: 'CommentText no debe ser nulo o vacío',
	38012: 'La entidad no existe en el compromiso dado',
	38013: 'EngagementCommentStatusID debe ser mayor que 0',
	38014: 'El ID del comentario del compromiso primario debe ser mayor que 0',
	38015: 'No había ningún cuerpo de formulario de Canvas que coincidiera con los criterios especificados',
	38016: 'La nota a la que está respondiendo ha sido eliminada por otro miembro del equipo. Actualice la página e inténtelo de nuevo',
	38017: 'Se borra el comentario del compromiso primario proporcionado',
	38018: 'El comentario de compromiso primario proporcionado es una respuesta en sí misma.',
	38019: 'El texto del comentario proporcionado no debe tener menos de 1 ni más de 4000 caracteres.',
	38020: 'El UID de entidad proporcionado no es válido',
	38021: 'El UID de la entidad principal proporcionado no es válido',
	38022: 'No se pudo eliminar la llamada de EngagementComments',
	38023: 'El ID del comentario no debe estar vacía',
	38024: 'La acción debe ser una acción válida.',
	38025: 'Está intentando eliminar un comentario que ya no existe. Actualice la página y vuelva a intentarlo.',
	38026: 'Usted no es el propietario del comentario',
	38027: 'No se encontró ningún comentario para actualizar.',
	38028: 'No se permiten actualizaciones sobre comentarios borrados',
	38029: 'Solo el autor puede cambiar el texto del comentario',
	38030: 'El ID del comentario de compromiso no es válido',
	38031: 'El ID de estado del comentario no debe estar vacío.',
	38032: 'No había ningún registro de relación entre el usuario solicitado y el compromiso. Actualice la página e inténtelo de nuevo. Si el error persiste, póngase en contacto con el servicio de asistencia',
	38033: 'La acción de actualización no es válida',
	38034: 'El CommentId solicitado ya está siendo utilizado por otro comentario.',
	38035: 'El CommentId solicitó ya está siendo utilizado por otro comentario.',
	38036: 'El ID del documento no debe ser nulo ni estar vacío',
	38037: 'El ID del usuario al que se le asignará el comentario no debe ser nulo',
	38038: 'No se debe reasignar el comentario al abrirlo o cerrarlo.',
	38039: 'La nota de la que estás eliminando una respuesta ya ha sido eliminada. Actualiza la página e inténtalo de nuevo',
	38040: 'La respuesta no debe tener fecha de vencimiento',
	38041: 'La respuesta no debe tener prioridad',
	38042: 'El comentario debe tener una fecha de vencimiento',
	38043: 'El comentario debe tener prioridad.',
	38044: 'Está intentando editar una respuesta a un comentario que ya no existe. Actualiza la página e inténtalo de nuevo',
	38045: 'No se permite editar tanto el ID de estado del compromiso como su contenido asignado al usuario o la prioridad.',
	38046: 'No se pudo actualizar la llamada de EngagementComments',
	38047: 'Solo puedes responder a las notas abiertas',
	39004: 'This tag group is no longer available. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*Risk-Estimate*/
	4064: 'Este riesgo ya no está disponible. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Soporte si el error persiste.',
	4065: 'La relación de riesgo no se puede editar. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	4066: 'Esta estimación ya no está disponible. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Soporte si el error persiste.',

	/*IT App/SO*/
	81001: 'Error en la aplicación de TI.',
	81002: 'Obtenga todas las aplicaciones de TI - compromiso no válido.',
	81003: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81004: 'No se pudo completar la operación. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	81005: 'No se pudo completar la operación. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	81006: 'Tipo de estrategia no válido.',
	81007: 'Id. de documento no válido.',
	81008: 'Error en obtener la Aplicación de TI por ID.',
	81009: 'No se pudo completar la operación. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	81010: 'No se pudo completar la operación. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	81011: 'No se pudo completar la operación. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	81012: 'El ID de la aplicación de TI no debe ser nulo ni estar vacío.',
	81013: 'La relación no se puede editar. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Soporte si el error persiste.',
	81014: 'Aplicación de TI eliminada - Proceso de TI fallido',
	81015: 'Aplicación de TI - Proceso de TI - ID no debería estar vacío',
	81016: 'Aplicación de TI - Proceso de TI  no valido',
	81017: 'La relación no se puede editar. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Soporte si el error persiste.',
	81018: 'Crear riesgo de TI - Control de TI falló',
	81019: 'Organización de servicios aprobada en lugar de Application TI.',
	81020: 'No se puede completar la eliminación. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	81039: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81041: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/* ITControl */
	84001: 'Error al obtener controles de TI.',
	84002: 'Error al obtener el control de TI por id.',
	84003: 'El identificador de control de TI es nulo o está vacío.',
	84004: 'Identificador de control de TI no válido.',
	84005: 'El identificador del documento no es válido.',
	84006: 'Falta el nombre del control de TI.',
	84007: 'Longitud de nombre de control de TI no válida.',
	84008: 'ID de frecuencia de control de TI no válido.',
	84009: 'ID de tipo de enfoque de control de TI no válido.',
	84010: 'ID de tipo de efectividad de diseño de control de TI no válido.',
	84011: 'Valor de prueba de control de TI no válido',
	84012: 'Id. de tipo de eficacia operativa de control de TI no válido.',
	84013: 'Falta frecuencia de control de TI.',
	84014: 'Error al eliminar el control de TI.',
	84015: 'La cadena de búsqueda no debe tener más de 100 caracteres.',
	84016: 'No se pudo completar la operación. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	84018: 'No se pudo completar la operación. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	84019: 'No se puede completar la actualización. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de Help Desk.',

	/*ITRisk*/
	86001: 'El nombre ITRisk no puede estar vacío.',
	86002: 'El nombre ITRisk no debe tener más de 500 caracteres.',
	86003: 'Esta acción no se pudo completar. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	86004: 'Error al obtener itrisk por llamada id.',
	86005: 'Esta acción no pudo llevarse a cabo. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	86006: 'No se puede completar la asociación. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el Help Desk.',
	86007: 'Esta acción no pudo llevarse a cabo. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	86008: 'No se puede completar la asociación. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el Help Desk.',
	86009: 'Esta acción no pudo llevarse a cabo. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	86010: 'La eliminacion del riesgo de tecnología falló',

	/*RiskFactorFormHeaders*/
	89001: 'No se encontró relación con los factores de riesgo.',
	89002: 'Documento no encontrado.',
	89003: 'Falta el identificador de documento.',
	89004: 'La longitud de la lógica supera los 4000 caracteres.',
	89005: 'El riesgo no se pudo relacionar con el factor de riesgo seleccionado. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con mesa de ayuda.',
	89014: 'El identificador de riesgo no es válido',
	89020: 'El factor de riesgo no se puede guardar.  Actualice la página e inténtelo de nuevo.  Si el problema persiste, póngase en contacto con el departamento de soporte técnico',

	/*Materiality*/
	91001: 'Materialidad no encontrada.',
	91002: 'No se pudieron guardar los datos. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el departamento de soporte técnico.',
	91003: 'Solicitud de actualización de materialidad no válida.',
	91004: 'Falta eslacantidadanualizadaqueseesperainformar.',
	91005: "Al actualizar eslacantidadanualizadaqueseesperainformar a'verdadero', es obligatorio especificar Previsióndeimportebase. ",
	91006: 'La Previsióndeimportebase no debe ser superior a 15 dígitos ni tener decimales.',
	91007: "Al actualizar eslacantidadanualizadaqueseesperainformar a'verdadero', Justuficacióndeprevisióndeimportebase debe ser nulo. ",
	91008: 'El  razonamiento de la cantidad de pronóstico base es demasiado corta o demasiado larga.',
	91009: 'ID de materialidad no válido',
	91010: 'ID de tipo de cuerpo de materialidad no válida',
	91011: 'La cantidad nominal no puede exceder el extremo superior del rango',
	91012: 'La cantidad de materialidad no debe ser superior a 15 dígitos y 4 decimales',

	/*Group Structure - Sub Scopes */
	92013: 'El sub-ámbito no se puede eliminar porque hay al menos una región o un equipo de componentes relacionados.',
	92016: 'El nombre del sub-ámbito ya existe',

	/*Helix Account Mappings */
	94001: 'No se pudo guardar la asignación de cuentas. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	94004: 'Una o más cuentas de EY Canvas se han eliminado y no se han podido asignar. Actualice la página e inténtelo de nuevo.',
	94005: 'No se puede reanudar el proyecto. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',

	/*Group Instructions */
	98001: 'Se ha producido un error al recuperar instrucciones de grupo. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	98002: 'Uno o más identificadores de sección de instrucciones de conocimiento no son válidos.',
	98003: 'Se ha producido un error al crear instrucciones de grupo. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	98004: 'El nombre de la instrucción no puede estar vacío.',
	98005: 'El nombre de la instrucción no puede tener más de 500 caracteres.',
	98006: 'La descripción de la instrucción no puede estar vacía.',
	98007: 'La descripción de la instrucción no puede tener más de 30.000 caracteres.',
	98009: 'El nombre de la instrucción no se puede duplicar.',
	98010: 'La fecha de vencimiento no puede estar vacía.',
	98011: 'Instrucción ya eliminada.',
	98012: 'Instrucción no encontrada.',
	98013: 'El ID de instrucción debe ser mayor que cero.',
	98014: 'Se ha producido un error al guardar las instrucciones de grupo. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',
	98015: 'Los ámbitos obligatorios de la instrucción no se pueden eliminar.',
	98016: 'Se ha eliminado la instrucción',
	98017: 'La tarea de grupo ya se ha eliminado.',
	98018: 'Edite la tarea de grupo después de eliminarla.',
	98019: 'Entidad no encontrada.',

	98020: 'No se puede generar el paquete de evaluación de riesgos. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el servicio de asistencia.',

	98021: 'El nombre del documento no puede estar vacío.',
	98022: 'El nombre del documento no puede tener más de 115 caracteres.',
	98023: 'El nombre del documento no puede tener caracteres XML no válidos.',
	98024: 'El proceso de creación del paquete está en curso, puede tardar hasta diez minutos en completarse',
	98025: 'Algunas instrucciones no tienen componentes relacionados. Asigne componentes a las instrucciones y, a continuación, vuelva a generar comunicaciones de evaluación de riesgos de grupo.',
	98026: 'Algun (os) componente (s) no tienen cuentas relacionadas. Relacione las cuentas con los componentes y, a continuación, vuelva a generar comunicaciones de evaluación de riesgos del grupo.',
	98027: 'Alguna(s) Cuenta(s) no tienen(n) documento(s) de Cuenta. Cree el documento de la cuenta y, a continuación, vuelva a generar comunicaciones de evaluación de riesgos del grupo.',
	98028: 'Este formulario de lienzo no está asociado a una tarea válida.',
	98029: 'Uno o más componentes son solo de referencia.',
	98030: 'Uno o varios componentes no son de este compromiso principal del equipo.',
	98031: 'Uno o varios ámbitos no son de este compromiso de equipo principal.',
	98032: 'El número de documentos ha superado el límite.',
	98033: 'Los alcances requeridos no se pueden eliminar de la instrucción.',

	/*Estimate */
	115017: 'Estimación no encontrada',

	/* Group Audit */
	106003: 'Error. Actualice e inténtelo de nuevo.',

	/* TasksOverview */
	117001: 'Error al obtener todas las llamadas de TaskOverview.',
	117002: 'Obtener toda la solicitud TaskOverview no puede estar vacía.',
	117003: 'ID de compromiso no válido.',
	117004: 'Valor de TaskCategory no válido.',
	117005: 'Valor de vista no válido.',
	117006: 'Invalid DocumentCategorySearch value.',
	117007: 'Duplicar los identificadores de TaskCategory.',
	117008: 'Identificadores de fase de tiempo duplicados.',
	117009: 'ID de tarea duplicados.',
	117010: 'Duplicar ID de documento.',
	117011: 'Duplicar los ID de AssignedToUser.',

	/* Multientity */
	114001: 'Falló la obtención de todas las MultiEntity.',
	114002: 'STEntityName no puede estar vacío.',
	114003: 'STEntityName no debe tener más de 500 caracteres.',
	114004: 'STLegalName no puede estar vacío.',
	114005: 'STLegalName no debe tener más de 500 caracteres.',
	114006: 'Multi Entity solo se puede crear con compromisos MEST.',
	114007: 'Falló la llamada para crear la cuenta Multi Entity.',
	114008: 'Se ha eliminado la STEntity seleccionada. Cierre este modo para ver la lista actualizada.',
	114009: 'ID de cuenta no válido.',
	114010: 'STEntityShortName no debe tener más de 100 caracteres.',
	114011: 'La solicitud de envío de perfil de STEntity no es válida.',
	114012: 'El cuerpo de la solicitud de envío de perfil no es válido.',
	114013: 'El cuerpo de la solicitud de perfil de envío de STEntity debe tener STEntityID distintos.',
	114014: 'Los STEntityID para enviar solicitud de perfil no son válidos.',
	114015: 'El formulario tiene respuestas incompletas. Actualice la página e inténtelo de nuevo.',
	114016: 'La actualización de contenido está deshabilitada para esto.',
	114017: 'El compromiso no tiene un documento de perfil individual Multi Entity.',
	114018: 'Faltan identificadores de STEntity Perfil individual de varias entidades.',
	114019: 'Una o varias entidades que está asignando ya no existen en la interacción. Actualice la página e inténtelo de nuevo.',
	114020: 'STEntityShortName no puede estar vacío.',
	114021: 'DocumentId no válido.',
	114022: 'EngagementId no válido.',
	114023: 'STEntityDocument ya existe.',
	114024: 'El registro STEntityDocument no existe.',
	114025: 'STEntityDocument record IsSystemAssociated.',
	114026: 'Cuerpo de solicitud no válido.',
	114028: 'Cada entidad no tiene un documento de perfil.',
	114035: 'La relación de STEntity ya existe.',
	114036: 'Al menos un documento de perfil debe ser válido cuando se solicite la actualización de todas las entidades.',
	114037: 'Ya se ha eliminado la relación entre el STEntity y la cuenta.',
	114038: 'Get all MultiEntity layers failed.',
	114039: 'Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',
	114040: 'Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',

	/* Sample List */
	121101: 'ID de lista de muestra no válido.',
	121008: 'ID de lista de muestra no válido.',
	121011: 'Este ejemplo ya no está disponible. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Soporte si el error persiste.',
	121013: 'Este ejemplo ya no está disponible en este compromiso. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Asistencia si el error persiste.',
	121016: 'Esta prueba ya no está disponible. Actualice la página y vuelva a intentarlo. Póngase en contacto con mesa de ayuda si el error persiste.',
	121037: 'Se ha producido un error al realizar esta acción. Actualice la página y vuelva a intentarlo. Póngase en contacto con mesa de ayuda si el error persiste.',
	121012: 'El estado del atributo no se puede actualizar. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Soporte si el error persiste.',
	121025: 'Este documento ya no está disponible. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Soporte si el error persiste.',
	121027: 'La relación del documento no se puede editar. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	121028: 'La relación del documento no se puede editar. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	121014: 'El estado del atributo no se puede actualizar. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Soporte si el error persiste.',
	121029: 'Este atributo ya no está disponible. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Soporte si el error persiste.',
	121021: 'El estado del atributo no se puede actualizar. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Soporte si el error persiste.',

	/*Control Attributes */
	122018: 'No se pudo completar la acción. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	122021: 'Se ha producido un error al realizar esta acción. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Mesa de Ayuda si el error persiste.',

	/*Flow chart*/
	123031: 'El ID  del formulario no se puede vincular a más de un diagrama de flujo.',

	1034: 'This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	1035: 'This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	/*Group Instructions */
	196033: 'Las instrucciones no pueden tener 0 posiciones de asignación, deben tener al menos 1.',

	/*Information Security */
	200001: 'La acción tomada fue prohibida por EY Information Security. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con Servicio de Soporte.',

	/*Tags */
	40007: 'Esta etiqueta ya no está disponible. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
	40029: 'La relación de etiquetas no se puede editar. Actualice la página y vuelva a intentarlo. Si el problema persiste, póngase en contacto con el Servicio de Soporte.',
};

export const roleForMember = [{
	id: 1,
	role: 'Socio a cargo',
	roleAbbreviation: 'PIC'
},
{
	id: 2,
	role: 'Socio del compromiso',
	roleAbbreviation: 'EP'
},
{
	id: 3,
	role: 'Director Ejecutivo',
	roleAbbreviation: 'Director ejecutivo'
},
{
	id: 4,
	role: 'Director',
	roleAbbreviation: 'Principal'
},
{
	id: 5,
	role: 'Gerente Senior',
	roleAbbreviation: 'Gerente Sr.'
},
{
	id: 6,
	role: 'Gerente',
	roleAbbreviation: 'Gerente'
},
{
	id: 7,
	role: 'Senior',
	roleAbbreviation: 'Senior'
},
{
	id: 8,
	role: 'Staff',
	roleAbbreviation: 'Staff'
},
{
	id: 9,
	role: 'Interno',
	roleAbbreviation: 'Intrn'
},
{
	id: 10,
	role: 'Revisor de calidad del compromiso',
	roleAbbreviation: 'EQR'
},
{
	id: 11,
	role: 'Otro Socio',
	roleAbbreviation: 'Otro socio'
},
{
	id: 12,
	role: 'Staff de GDS',
	roleAbbreviation: 'GDS Staff'
},
{
	id: 13,
	role: 'Asesoria (ITRA, TAS, Human capital u otro)',
	roleAbbreviation: 'ADV'
},
{
	id: 14,
	role: 'Impuestos',
	roleAbbreviation: 'Impuestos'
},
{
	id: 15,
	role: 'Servicios de soporte ejecutivo',
	roleAbbreviation: 'ESS'
},
{
	id: 16,
	role: 'Abogado General',
	roleAbbreviation: 'GCO'
},
{
	id: 17,
	role: 'Revisor de calidad de auditoria',
	roleAbbreviation: 'AQR'
},
{
	id: 18,
	role: 'ML del equipo componente',
	roleAbbreviation: 'MCT'
},
{
	id: 19,
	role: 'Supervisor del cliente',
	roleAbbreviation: 'C. Supervisor'
},
{
	id: 20,
	role: 'Staff del cliente',
	roleAbbreviation: 'C.Staff'
},
{
	id: 21,
	role: 'Supervisor de auditoria interna',
	roleAbbreviation: 'IA Supervisor'
},
{
	id: 22,
	role: 'Staff de Auditoria interna',
	roleAbbreviation: 'IA Staff'
},
{
	id: 23,
	role: 'Regulador',
	roleAbbreviation: 'Regulador'
},
{
	id: 24,
	role: 'Otro( revision de due diligence)',
	roleAbbreviation: 'Otro'
},
{
	id: 25,
	role: 'Oficina',
	roleAbbreviation: 'Oficina'
},
{
	id: 26,
	role: 'Area',
	roleAbbreviation: 'Area'
},
{
	id: 27,
	role: 'Industria',
	roleAbbreviation: 'IND'
},
{
	id: 28,
	role: 'Nacional',
	roleAbbreviation: 'NAT'
},
{
	id: 29,
	role: 'Global',
	roleAbbreviation: 'GBL'
},
{
	id: 30,
	role: 'Senior de GDS',
	roleAbbreviation: 'GDS Senior'
},
{
	id: 31,
	role: 'Gerente de GDS',
	roleAbbreviation: 'Gerente de GDS'
}
];

export const accountConclusionTabs = {
	conclusions: 'conclusiones'
};

export const assertions = [{
	id: 1,
	assertionname: 'Integridad',
	assertionabbreviation: 'C',
	statementtypeid: 2,
	displayorder: 1
},
{
	id: 2,
	assertionname: 'Existencia',
	assertionabbreviation: 'E',
	statementtypeid: 2,
	displayorder: 2
},
{
	id: 3,
	assertionname: 'Valuación',
	assertionabbreviation: 'V',
	statementtypeid: 2,
	displayorder: 3
},
{
	id: 4,
	assertionname: 'Derechos y obligaciones',
	assertionabbreviation: 'R&O',
	statementtypeid: 2,
	displayorder: 4
},
{
	id: 5,
	assertionname: 'Presentación y Divulgación',
	assertionabbreviation: 'P&D',
	statementtypeid: 2,
	displayorder: 5
},
{
	id: 6,
	assertionname: 'Integridad',
	assertionabbreviation: 'C',
	statementtypeid: 1,
	displayorder: 6
},
{
	id: 7,
	assertionname: 'Ocurrencia',
	assertionabbreviation: 'O',
	statementtypeid: 1,
	displayorder: 7
},
{
	id: 8,
	assertionname: 'Medición',
	assertionabbreviation: 'M',
	statementtypeid: 1,
	displayorder: 8
},
{
	id: 9,
	assertionname: 'Presentación y Divulgación',
	assertionabbreviation: 'P&D',
	statementtypeid: 1,
	displayorder: 9
},
{
	id: 10,
	assertionname: 'Integridad',
	assertionabbreviation: 'C',
	statementtypeid: 3,
	displayorder: 10
},
{
	id: 11,
	assertionname: 'Existencia/Ocurrencia',
	assertionabbreviation: 'E/O',
	statementtypeid: 3,
	displayorder: 11
},
{
	id: 12,
	assertionname: 'Medición/Valoración',
	assertionabbreviation: 'M/V',
	statementtypeid: 3,
	displayorder: 12
},
{
	id: 13,
	assertionname: 'Derechos y obligaciones',
	assertionabbreviation: 'R&O',
	statementtypeid: 3,
	displayorder: 13
},
{
	id: 14,
	assertionname: 'Presentación y Divulgación',
	assertionabbreviation: 'P&D',
	statementtypeid: 3,
	displayorder: 14
}
];

export const documentChangeTypesOptions = [{
	value: 1,
	label: 'Cambio no administrativo'
},
{
	value: 2,
	label: 'Aceptar revisiones cuando se utilizó la funcionalidad de seguimiento de cambios'
},
{
	value: 3,
	label: 'Añadir referencias cruzadas adicionales a pruebas que ya existen'
},
{
	value: 4,
	label: 'Añadir una respuesta de confirmación original recibida previamente por fax o correo electrónico'
},
{
	value: 5,
	label: 'Cambio cosmético'
},
{
	value: 6,
	label: 'Completar el Formulario AP y evaluación sustancial de roles'
},
{
	value: 7,
	label: 'Eliminar o descartar la documentación reemplazada'
},
{
	value: 8,
	label: 'Preparación de la carta de la administración'
},
{
	value: 9,
	label: 'Firmar listas de comprobación de finalización relacionadas con el proceso de archivado'
},
{
	value: 10,
	label: 'Clasificación, conferencia y cruce de documentos finales',
},
{
	value: 12,
	label: 'solo MEST: Modificación de las pruebas relacionadas con entidades con fecha de informe después de la fecha del informe en EY Canvas'
},
{
	value: 11,
	label: 'Cuando se ajusta a la zona horaria local, la modificación está en o antes de la fecha del informe (solo para las Américas)',
}
];

export const KnowledgeFormProfileAnswer = [{
	value: 1,
	label: 'Complejo',
	display: true
},
{
	value: 2,
	label: 'No complejo',
	display: true
},
{
	value: 3,
	label: 'Listado',
	display: true
},
{
	value: 4,
	label: 'No Listado',
	display: false
},
{
	value: 5,
	label: 'PCAOB - IA',
	display: true
},
{
	value: 6,
	label: 'No PCAOB-IA',
	display: false
},
{
	value: 7,
	label: 'PCAOB - FS',
	display: true
},
{
	value: 8,
	label: 'No PCAOB-FS',
	display: false
},
{
	value: 9,
	label: 'Auditoría de grupo',
	display: true
},
{
	value: 10,
	label: 'Auditoría no grupal',
	display: false
},
{
	value: 11,
	label: 'Digital',
	display: true
},
{
	value: 12,
	label: 'Core',
	display: true
}
];

export const strategyType = [{
	StrategyTypeId: 1,
	StrategyTypeName: 'Controles'
},
{
	StrategyTypeId: 2,
	StrategyTypeName: 'Sustantivo'
}
];

export const controlTypeName = {
	1: 'Aplicación',
	2: 'ITDM',
	3: 'Manual preventivo',
	4: 'Manual detectivo'
};

export const inCompleteList = [{
	value: 1,
	label: 'Incompleta',
	title: 'Incompleta'
}];

export const scotTypes = [{
	value: 1,
	label: 'Rutinario',
	title: 'Rutinario',
	isDisabled: false
},
{
	value: 2,
	label: 'No Rutinario',
	title: 'No Rutinario',
	isDisabled: false
},
{
	value: 3,
	label: 'Estimación',
	title: 'Estimación',
	isDisabled: false
}
];

export const scotTypesNew = [{
	value: 1,
	label: 'Rutinario',
	title: 'Rutinario',
	isDisabled: false
},
{
	value: 2,
	label: 'No rutinario',
	title: 'No rutinario',
	isDisabled: false
},
{
	value: 3,
	label: 'Estimacion',
	title: 'Estimacion',
	isDisabled: false
},
{
	value: 4,
	label: 'FSCP',
	title: 'FSCP',
	isDisabled: false
}
];

export const estimationTypes = [{
	value: 1,
	label: 'No',
	title: 'No',
	isDisabled: false
},
{
	value: 2,
	label: 'Sí',
	title: 'Sí',
	isDisabled: false
}
];

/* IT Control */
export const itApproachType = [{
	ITApproachTypeId: 1,
	ITApproachTypeName: 'Controles'
},
{
	ITApproachTypeId: 2,
	ITApproachTypeName: 'sustantivo'
}
];

/*Controls */
export const controlFrequencyType = [{
	value: 1,
	label: 'Muchas veces al día',
	title: 'Muchas veces al día'
},
{
	value: 2,
	label: 'Diario',
	title: 'Diario'
},
{
	value: 3,
	label: 'Semanal',
	title: 'Semanal'
},
{
	value: 4,
	label: 'Mensual',
	title: 'Mensual'
},
{
	value: 5,
	label: 'Trimestral',
	title: 'Trimestral'
},
{
	value: 6,
	label: 'Anualmente',
	title: 'Anualmente'
},
{
	value: 8,
	label: 'Otros',
	title: 'Otros'
}
];

export const controlTypes = [{
	value: 1,
	label: 'Control de aplicaciones IT'
},
{
	value: 2,
	label: 'Control manual dependiente de IT'
},
{
	value: 3,
	label: 'Prevención manual'
},
{
	value: 4,
	label: 'Detección manual'
}
];

export const strategyTypeCheck = {
	1: 'Controles',
	2: 'Sustantivo'
};

export const designEffectivenessType = [{
	DesignEffectivenessTypeId: 1,
	DesignEffectivenessTypeName: 'Efectivo'
},
{
	DesignEffectivenessTypeId: 2,
	DesignEffectivenessTypeName: 'Inefectivo'
}
];

export const controlEffectivenessType = [{
	ControlEffectivenessTypeId: 1,
	ControlEffectivenessTypeName: 'Efectivo'
},
{
	ControlEffectivenessTypeId: 2,
	ControlEffectivenessTypeName: 'Inefectivo'
}
];

export const testing = [{
	testingId: 1,
	testingDescription: 'Sí'
},
{
	testingId: 2,
	testingDescription: 'No'
}
];

export const controlType = [{
	value: 1,
	label: 'Control de aplicaciones de TI',
	title: 'Control de aplicaciones de TI'
},
{
	value: 2,
	label: 'Control manual dependiente de TI',
	title: 'Control manual dependiente de TI'
},
{
	value: 3,
	label: 'Manual preventivo',
	title: 'Manual preventivo'
},
{
	value: 4,
	label: 'Manual detectivo',
	title: 'Manual detectivo'
}
];

export const aggregateITEvaluationType = [{
	value: 1,
	label: 'Soporte',
	title: 'Soporte'
},
{
	value: 2,
	label: 'Sin soporte',
	title: 'Sin soporte'
},
{
	value: 3,
	label: 'Soporte de FS & ICFR',
	title: 'Soporte de FS & ICFR'
},
{
	value: 4,
	label: 'Solo soporte de FS',
	title: 'Solo soporte de FS'
}
];

export const KnowledgeFormProfileQuestion = [{
	value: 1,
	label: 'Complejo'
},
{
	value: 2,
	label: 'Listado'
},
{
	value: 3,
	label: 'PCAOB - IA'
},
{
	value: 4,
	label: 'PCAOB - FS'
},
{
	value: 5,
	label: 'Ubicación'
},
{
	value: 6,
	label: 'Idioma'
},
{
	value: 7,
	label: 'Auditoría de grupo'
},
{
	value: 8,
	label: 'Digital'
}
];

export const KnowledgeLanguage = [{
	value: 1,
	label: 'Inglés'
},
{
	value: 2,
	label: 'Español (América Latina)'
},
{
	value: 3,
	label: 'Francés (Canadá)'
},
{
	value: 4,
	label: 'Holandés'
},
{
	value: 5,
	label: 'Croata'
},
{
	value: 6,
	label: 'Checo'
},
{
	value: 7,
	label: 'Danés'
},
{
	value: 8,
	label: 'Finlandés'
},
{
	value: 9,
	label: 'Alemán (Alemania, Austria)',
},
{
	value: 10,
	label: 'Húngaro'
},
{
	value: 11,
	label: 'Italiano'
},
{
	value: 12,
	label: 'Japonés (Japón)'
},
{
	value: 13,
	label: 'Noruego (Noruega)'
},
{
	value: 14,
	label: 'Polaco'
},
{
	value: 15,
	label: 'Eslovaco'
},
{
	value: 16,
	label: 'Eslovenia'
},
{
	value: 17,
	label: 'Sueco'
},
{
	value: 18,
	label: 'árabe'
},
{
	value: 19,
	label: 'Chino simplificado (China)'
},
{
	value: 20,
	label: 'Chino tradicional (Taiwán)'
},
{
	value: 21,
	label: 'Griego'
},
{
	value: 22,
	label: 'Hebreo (Israel)'
},
{
	value: 23,
	label: 'Indonesio'
},
{
	value: 24,
	label: 'Corea (República de Corea)'
},
{
	value: 25,
	label: 'Portugués (Brasil)'
},
{
	value: 26,
	label: 'Rumano'
},
{
	value: 27,
	label: 'Ruso (Rusia)'
},
{
	value: 28,
	label: 'Thai'
},
{
	value: 29,
	label: 'Turco'
},
{
	value: 30,
	label: 'Vietnamita'
},
{
	value: 31,
	label: 'PCAOB - Inglés'
},
{
	value: 32,
	label: 'PCAOB - Español (América Latina)'
},
{
	value: 33,
	label: 'PCAOB - Francés (Canadá)'
},
{
	value: 34,
	label: 'PCAOB - Holandés'
},
{
	value: 35,
	label: 'PCAOB - Croata'
},
{
	value: 36,
	label: 'PCAOB - Checo'
},
{
	value: 37,
	label: 'PCAOB - Danés'
},
{
	value: 38,
	label: 'PCAOB - Finlandés'
},
{
	value: 39,
	label: 'PCAOB - Alemán (Alemania, Austria)',
},
{
	value: 40,
	label: 'PCAOB - Húngaro'
},
{
	value: 41,
	label: 'PCAOB - Italiano'
},
{
	value: 42,
	label: 'PCAOB - Japonés (Japón)'
},
{
	value: 43,
	label: 'PCAOB - Noruego (Noruega)'
},
{
	value: 44,
	label: 'PCAOB - Polaco'
},
{
	value: 45,
	label: 'PCAOB - Eslovaco'
},
{
	value: 46,
	label: 'PCAOB - Esloveno'
},
{
	value: 47,
	label: 'PCAOB - Sueco'
},
{
	value: 48,
	label: 'PCAOB - Árabe'
},
{
	value: 49,
	label: 'PCAOB - Chino simplificado (China)'
},
{
	value: 50,
	label: 'PCAOB - Chino tradicional (Taiwán)'
},
{
	value: 51,
	label: 'PCAOB - Griego'
},
{
	value: 52,
	label: 'PCAOB - Hebreo (Israel)'
},
{
	value: 53,
	label: 'PCAOB - Indonesio'
},
{
	value: 54,
	label: 'PCAOB - Coreano (República de Corea)'
},
{
	value: 55,
	label: 'PCAOB - Portugués (Brasil)'
},
{
	value: 56,
	label: 'PCAOB - Rumano'
},
{
	value: 57,
	label: 'PCAOB - Ruso (Rusia)'
},
{
	value: 58,
	label: 'PCAOB - Tailandés'
},
{
	value: 59,
	label: 'PCAOB - Turco'
},
{
	value: 60,
	label: 'PCAOB - Vietnamita'
}
];

export const KnowledgeCountry = [{
	value: 1,
	label: 'Mayotte'
},
{
	value: 2,
	label: 'Islas Vírgenes Británicas'
},
{
	value: 3,
	label: 'España'
},
{
	value: 4,
	label: 'Belice'
},
{
	value: 5,
	label: 'Perú'
},

{
	value: 6,
	label: 'Eslovaquia'
},
{
	value: 7,
	label: 'Venezuela'
},
{
	value: 8,
	label: 'Noruega'
},
{
	value: 9,
	label: 'Islas Malvinas'
},
{
	value: 10,
	label: 'Mozambique'
},

{
	value: 11,
	label: 'China'
},
{
	value: 12,
	label: 'Sudán'
},
{
	value: 13,
	label: 'Israel'
},
{
	value: 14,
	label: 'Bélgica'
},
{
	value: 15,
	label: 'Arabia Saudita'
},

{
	value: 16,
	label: 'Gibraltar'
},
{
	value: 17,
	label: 'Guam'
},
{
	value: 18,
	label: 'Islas Norfolk'
},
{
	value: 19,
	label: 'Zambia'
},
{
	value: 20,
	label: 'Reunión'
},

{
	value: 21,
	label: 'Azerbaiyán'
},
{
	value: 22,
	label: 'Santa Helena'
},
{
	value: 23,
	label: 'Irán'
},
{
	value: 24,
	label: 'Mónaco'
},
{
	value: 25,
	label: 'Saint Pierre y Miquelon'
},

{
	value: 26,
	label: 'Nueva Zelanda'
},
{
	value: 27,
	label: 'Islas Cook'
},
{
	value: 28,
	label: 'Santa Lucía'
},
{
	value: 29,
	label: 'Zimbabwe'
},
{
	value: 30,
	label: 'Irak'
},

{
	value: 31,
	label: 'Tonga'
},
{
	value: 32,
	label: 'Samoa Americana'
},
{
	value: 33,
	label: 'Maldivas'
},
{
	value: 34,
	label: 'Marruecos'
},
{
	value: 35,
	label: 'Normas internacionales de auditoría (ISA)'
},

{
	value: 36,
	label: 'Albania'
},
{
	value: 37,
	label: 'Afganistán'
},
{
	value: 38,
	label: 'Gambia'
},
{
	value: 39,
	label: 'Burkina Faso'
},
{
	value: 40,
	label: 'Tokelau'
},

{
	value: 41,
	label: 'Libia'
},
{
	value: 42,
	label: 'Canadá'
},
{
	value: 43,
	label: 'Emiratos Arabes Unidos'
},
{
	value: 44,
	label: 'Corea, República Popular Democrática de',
},
{
	value: 45,
	label: 'Montserrat'
},

{
	value: 46,
	label: 'Groenlandia'
},
{
	value: 47,
	label: 'Ruanda'
},
{
	value: 48,
	label: 'Fiji'
},
{
	value: 49,
	label: 'Djibouti'
},
{
	value: 50,
	label: 'Botswana'
},

{
	value: 51,
	label: 'Kuwait'
},
{
	value: 52,
	label: 'Madagascar'
},
{
	value: 53,
	label: 'Isla de Man'
},
{
	value: 54,
	label: 'Hungría'
},
{
	value: 55,
	label: 'Namibia'
},

{
	value: 56,
	label: 'Malta'
},
{
	value: 57,
	label: 'Jersey'
},
{
	value: 58,
	label: 'Tailandia'
},
{
	value: 59,
	label: 'San Cristóbal y Nieves'
},
{
	value: 60,
	label: 'Bhután'
},

{
	value: 61,
	label: 'Panamá'
},
{
	value: 62,
	label: 'Somalia'
},
{
	value: 63,
	label: 'Bahréin'
},
{
	value: 64,
	label: 'Bosnia y Herzegovina'
},
{
	value: 65,
	label: 'Francia'
},

{
	value: 66,
	label: 'Corea, República de',
},
{
	value: 67,
	label: 'Islandia'
},
{
	value: 68,
	label: 'Portugal'
},
{
	value: 69,
	label: 'Túnez'
},
{
	value: 70,
	label: 'Ghana'
},

{
	value: 71,
	label: 'Camerún'
},
{
	value: 72,
	label: 'Grecia'
},
{
	value: 73,
	label: 'Territorios del Sur francés'
},
{
	value: 74,
	label: 'Heard and McDonald Islands'
},
{
	value: 75,
	label: 'Andorra'
},

{
	value: 76,
	label: 'Luxemburgo'
},
{
	value: 77,
	label: 'Samoa'
},
{
	value: 78,
	label: 'Anguila'
},
{
	value: 79,
	label: 'Países Bajos'
},
{
	value: 80,
	label: 'Guinea-Bissau'
},

{
	value: 81,
	label: 'Nicaragua'
},
{
	value: 82,
	label: 'Paraguay'
},
{
	value: 83,
	label: 'Antigua y Barbuda'
},
{
	value: 84,
	label: 'Norma Internacional de Información Financiera (NIIF)'
},
{
	value: 85,
	label: 'Nigeria'
},

{
	value: 86,
	label: 'Egipto'
},
{
	value: 87,
	label: 'Estado de la Ciudad del Vaticano'
},
{
	value: 88,
	label: 'Letonia'
},
{
	value: 89,
	label: 'Chipre'
},
{
	value: 90,
	label: 'Islas secundarias menores de EE. UU.'
},

{
	value: 91,
	label: 'Rusia'
},
{
	value: 92,
	label: 'San Vicente y las Granadinas'
},
{
	value: 93,
	label: 'Guernsey'
},
{
	value: 94,
	label: 'Burundi'
},
{
	value: 95,
	label: 'Cuba'
},

{
	value: 96,
	label: 'Guinea Ecuatorial'
},
{
	value: 97,
	label: 'Territorio británico del Océano Indico'
},
{
	value: 98,
	label: 'Suecia'
},
{
	value: 99,
	label: 'Uganda'
},
{
	value: 100,
	label: 'Macedonia, la ex República Yugoslava de'
},

{
	value: 101,
	label: 'Suiza'
},
{
	value: 102,
	label: 'El Salvador'
},
{
	value: 103,
	label: 'Kirguistán'
},
{
	value: 104,
	label: 'Irlanda'
},
{
	value: 105,
	label: 'Kazajstán'
},

{
	value: 106,
	label: 'Honduras'
},
{
	value: 107,
	label: 'Uruguay'
},
{
	value: 108,
	label: 'Georgia'
},
{
	value: 109,
	label: 'Trinidad y Tobago'
},
{
	value: 110,
	label: 'Autoridad Palestina'
},

{
	value: 111,
	label: 'Martinique'
},
{
	value: 112,
	label: 'Islas Aland'
},
{
	value: 113,
	label: 'Polinesia Francesa'
},
{
	value: 114,
	label: 'Costa de Marfil'
},
{
	value: 115,
	label: 'Montenegro'
},

{
	value: 116,
	label: 'Sudáfrica'
},
{
	value: 117,
	label: 'Georgia del Sur y las Islas Sandwich del Sur'
},
{
	value: 118,
	label: 'Yemen'
},
{
	value: 119,
	label: 'China de Hong Kong'
},
{
	value: 120,
	label: 'Kenia'
},

{
	value: 121,
	label: 'Chad'
},
{
	value: 122,
	label: 'Colombia'
},
{
	value: 123,
	label: 'Costa Rica'
},
{
	value: 124,
	label: 'Angola'
},
{
	value: 125,
	label: 'Lituania'
},

{
	value: 126,
	label: 'Siria'
},
{
	value: 127,
	label: 'Malasia'
},
{
	value: 128,
	label: 'Sierra Leona'
},
{
	value: 129,
	label: 'Serbia'
},
{
	value: 130,
	label: 'Polonia'
},

{
	value: 131,
	label: 'Suriname'
},
{
	value: 132,
	label: 'Haití'
},
{
	value: 133,
	label: 'Nauru'
},
{
	value: 134,
	label: 'Santo Tomé y Príncipe'
},
{
	value: 135,
	label: 'Svalbard y Jan Mayen'
},

{
	value: 136,
	label: 'Singapur'
},
{
	value: 137,
	label: 'Moldavia'
},
{
	value: 138,
	label: 'Taiwán'
},
{
	value: 139,
	label: 'Senegal'
},
{
	value: 140,
	label: 'Gabón'
},

{
	value: 141,
	label: 'México'
},
{
	value: 142,
	label: 'Seychelles'
},
{
	value: 143,
	label: 'Micronesia,Estados Federados de'
},
{
	value: 144,
	label: 'Argelia'
},
{
	value: 145,
	label: 'Italia'
},

{
	value: 146,
	label: 'San Marino'
},
{
	value: 147,
	label: 'Liberia'
},
{
	value: 148,
	label: 'Brasil'
},
{
	value: 149,
	label: 'Croacia'
},
{
	value: 150,
	label: 'Islas Feroe'
},

{
	value: 151,
	label: 'Palau'
},
{
	value: 152,
	label: 'Finlandia'
},
{
	value: 153,
	label: 'Filipinas'
},
{
	value: 154,
	label: 'Jamaica'
},
{
	value: 155,
	label: 'Guayana Francesa'
},

{
	value: 156,
	label: 'Cabo Verde'
},
{
	value: 157,
	label: 'Myanmar'
},
{
	value: 158,
	label: 'Lesotho'
},
{
	value: 159,
	label: 'Islas Vírgenes estadounidenses'
},
{
	value: 160,
	label: 'Islas Caimán'
},

{
	value: 161,
	label: 'Niue'
},
{
	value: 162,
	label: 'Togo'
},
{
	value: 163,
	label: 'Belarús'
},
{
	value: 164,
	label: 'Dominica'
},
{
	value: 165,
	label: 'Indonesia'
},

{
	value: 166,
	label: 'Uzbekistán'
},
{
	value: 167,
	label: 'Nigeria'
},
{
	value: 168,
	label: 'Wallis y Futuna'
},
{
	value: 169,
	label: 'Barbados'
},
{
	value: 170,
	label: 'Sri Lanka'
},

{
	value: 171,
	label: 'Reino Unido'
},
{
	value: 172,
	label: 'Ecuador'
},
{
	value: 173,
	label: 'Guadalupe'
},
{
	value: 174,
	label: 'Laos'
},
{
	value: 175,
	label: 'Jordania'
},

{
	value: 176,
	label: 'Islas Salomón'
},
{
	value: 177,
	label: 'Timor Oriental'
},
{
	value: 178,
	label: 'Líbano'
},
{
	value: 179,
	label: 'República Centroafricana'
},
{
	value: 180,
	label: 'India'
},

{
	value: 181,
	label: 'Isla de Navidad'
},
{
	value: 182,
	label: 'Vanuatu'
},
{
	value: 183,
	label: 'Brunei'
},
{
	value: 184,
	label: 'Bangladesh'
},
{
	value: 185,
	label: 'Antartida'
},

{
	value: 186,
	label: 'Bolivia'
},
{
	value: 187,
	label: 'Turquía'
},
{
	value: 188,
	label: 'Bahamas'
},
{
	value: 189,
	label: 'Comoras'
},
{
	value: 190,
	label: 'Sahara Occidental'
},

{
	value: 191,
	label: 'República Checa'
},
{
	value: 192,
	label: 'Ucrania'
},
{
	value: 193,
	label: 'Estonia'
},
{
	value: 194,
	label: 'Bulgaria'
},
{
	value: 195,
	label: 'Mauritania'
},

{
	value: 196,
	label: 'Congo,La República Democrática del'
},
{
	value: 197,
	label: 'Liechtenstein'
},
{
	value: 198,
	label: 'Pitcairn'
},
{
	value: 199,
	label: 'Dinamarca'
},
{
	value: 200,
	label: 'Islas Marshall'
},

{
	value: 201,
	label: 'Japón'
},
{
	value: 202,
	label: 'Austria'
},
{
	value: 203,
	label: 'Omán'
},
{
	value: 204,
	label: 'Mongolia'
},
{
	value: 205,
	label: 'Tajikistán'
},

{
	value: 206,
	label: 'Suiza'
},
{
	value: 207,
	label: 'Guatemala'
},
{
	value: 208,
	label: 'Eritrea'
},
{
	value: 209,
	label: 'Nepal'
},
{
	value: 210,
	label: 'Malí'
},

{
	value: 211,
	label: 'Eslovenia'
},
{
	value: 212,
	label: 'Islas Marianas del Norte'
},
{
	value: 213,
	label: '(No aplicable)'
},
{
	value: 214,
	label: 'Aruba'
},
{
	value: 215,
	label: 'Congo'
},

{
	value: 216,
	label: 'Qatar'
},
{
	value: 217,
	label: 'Guinea'
},
{
	value: 218,
	label: 'Estados Unidos'
},
{
	value: 219,
	label: 'Etiopía'
},
{
	value: 220,
	label: 'Otros'
},

{
	value: 221,
	label: 'Guyana'
},
{
	value: 222,
	label: 'Alemania'
},
{
	value: 223,
	label: 'Bermuda'
},
{
	value: 224,
	label: 'Islas Turcas y Caicos'
},
{
	value: 225,
	label: 'Australia'
},

{
	value: 226,
	label: 'Kiribati'
},
{
	value: 227,
	label: 'Puerto Rico'
},
{
	value: 228,
	label: 'Pakistán'
},
{
	value: 229,
	label: 'Mauricio'
},
{
	value: 230,
	label: 'Malawi'
},

{
	value: 231,
	label: 'Turkmenistán'
},
{
	value: 232,
	label: 'Camboya'
},
{
	value: 233,
	label: 'Chile'
},
{
	value: 234,
	label: 'Nueva Caledonia'
},
{
	value: 235,
	label: 'Papua Nueva Guinea'
},

{
	value: 236,
	label: 'Isla Bouvet'
},
{
	value: 237,
	label: 'Tuvalu'
},
{
	value: 238,
	label: 'Curacao'
},
{
	value: 239,
	label: 'República Dominicana'
},
{
	value: 240,
	label: 'Vietnam'
},

{
	value: 241,
	label: 'Islas Cocos (Keeling)'
},
{
	value: 242,
	label: 'Granada'
},
{
	value: 243,
	label: 'Tanzania'
},
{
	value: 244,
	label: 'Argentina'
},
{
	value: 245,
	label: 'Macao, China'
},

{
	value: 246,
	label: 'Benin'
},
{
	value: 247,
	label: 'Rumanía'
},
{
	value: 248,
	label: 'Armenia'
},
{
	value: 249,
	label: 'global'
},
{
	value: 250,
	label: 'NIIF para pymes'
},

{
	value: 251,
	label: 'US GAAP'
},
{
	value: 252,
	label: 'Marco de información financiera de AICPA para pequeñas y medianas entidades'
},
{
	value: 253,
	label: 'Sudán del Sur'
}
];

export const pagingSvgHoverText = {
	first: 'Primera página',
	previous: 'Página anterior',
	next: 'Siguiente página',
	last: 'Last Page'
};

export const priorityTypesForDropdown = [{
	value: 1,
	label: 'Bajo',
	className: 'Low'
},
{
	value: 2,
	label: 'Medio',
	className: 'Medium'
},
{
	value: 3,
	label: 'Alto',
	className: 'High'
},
{
	value: 4,
	label: 'Crítico',
	className: 'Critical'
}
];

export const reviewNoteFilterTypes = [{
	value: 0,
	label: 'Todos'
},
{
	value: 1,
	label: 'Abierto'
},
{
	value: 2,
	label: 'Aclarado'
},
{
	value: 3,
	label: 'Cerrado'
}
];

export const reviewStatus = [{
	id: 1,
	name: 'Abierto'
},
{
	id: 2,
	name: 'Aclarado'
},
{
	id: 3,
	name: 'Cerrado'
}
];

export const reviewNoteOpenStatusOption = [{
	value: 2,
	label: 'Borrar'
},
{
	value: 3,
	label: 'Cerrar'
}
];

export const reviewNoteClearedStatusOption = [{
	value: 1,
	label: 'Reabrir'
},
{
	value: 3,
	label: 'Cerrar'
}
];

export const reviewNoteBulkClearedStatusOption = [{
	value: 1,
	label: 'Reabrir'
},
{
	value: 2,
	label: 'Borrar'
},
{
	value: 3,
	label: 'Cerrar'
}
];

export const reviewNoteClosedStatusOption = [{
	value: 1,
	label: 'Reabrir'
},
{
	value: 4,
	label: 'Borrar'
}
];

export const taskTypeBadge = {
	1: 'OST',
	2: 'PST',
	3: 'WT',
	4: 'TOC',
	5: 'OSP',
	6: 'PSP',
	7: 'RT',
	8: 'GT',
	9: 'PIC',
	10: 'EQR',
	11: 'PIC/EQR',
	22: 'ACT',
	23: 'UDP'
};

export const riskTypes = [{
	id: 1,
	name: 'Riesgos significativos',
	abbrev: 'SR',
	label: 'Significativos',
	title: 'Riesgo significativo'
},
{
	id: 2,
	name: 'Riesgos de fraude',
	abbrev: 'FR',
	label: 'Fraude',
	title: 'Riesgo de fraude'
},
{
	id: 3,
	name: 'Riesgo de error material',
	abbrev: 'R',
	label: 'Riesgo de error material',
	title: 'Riesgo de error material'
},
{
	id: 4,
	name: 'Riesgo estimado muy bajo',
	abbrev: 'VLRS',
	label: 'Riesgo estimado muy bajo',
	title: 'Riesgo estimado muy bajo'
},
{
	id: 5,
	name: 'Riesgo estimado bajo',
	abbrev: 'LRE',
	label: 'Riesgo estimado bajo',
	title: 'Riesgo estimado bajo'
},
{
	id: 6,
	name: 'Riesgo estimado alto',
	abbrev: 'HRE',
	label: 'Riesgo estimado alto',
	title: 'Riesgo estimado alto'
},
{
	id: 7,
	name: 'Estimación - No seleccionado',
	abbrev: 'ENS',
	label: 'Estimación - No seleccionado',
	title: 'Estimación - No seleccionado'
}

];

export const relatedRisksDropdownRiskTypes = [{
	id: 1,
	name: 'Riesgos significativos',
	abbrev: 'SR',
	label: 'Significativo',
	title: 'Riesgo significativo'
},
{
	id: 2,
	name: 'Riesgos de fraude',
	abbrev: 'FR',
	label: 'Fraude',
	title: 'Riesgo de fraude'
},
{
	id: 3,
	name: 'Riesgo de error material',
	abbrev: 'R',
	label: 'Riesgo de error material',
	title: 'Riesgo de error material'
}
];

export const estimateTypes = [{
	id: 4,
	name: 'Estimación de riesgo muy baja',
	abbrev: 'VLRE',
	label: 'Muy bajo',
	title: 'Estimación de riesgo muy baja'
},
{
	id: 5,
	name: 'Menor estimación del riesgo',
	abbrev: 'LRE',
	label: 'Bajo',
	title: 'Estimación del riesgo menor'
},
{
	id: 6,
	name: 'Estimación de riesgo más alta',
	abbrev: 'HRE',
	label: 'Alto',
	title: 'Estimación de riesgo más alta'
},
{
	id: 7,
	name: 'Estimación - No seleccionado',
	abbrev: 'NA',
	label: 'No seleccionado',
	title: 'Estimación - No seleccionado'
}
];

export const statementTypes = [{
	id: 1,
	name: 'cuenta de resultados'
},
{
	id: 2,
	name: 'balance'
},
{
	id: 3,
	name: 'ambos'
}
];

export const RbacErrors = {
	106: 'Permisos insuficientes para editar contenido. Trabaje con un administrador del compromiso para obtener derechos suficientes.'
};

export const HelixProjectValidationErrors = {
	800: 'Parece que no ha accedido a EY Helix antes? Ir a',
	801: 'No es un miembro autorizado del equipo del proyecto vinculado. Póngase en contacto con el administrador del proyecto EY Helix para obtener acceso.',
	901: 'El proyecto EY Helix seleccionado ya no está disponible. Haga clic en Proyectos de EY Helix para vincular un nuevo proyecto.',
	902: 'El proyecto EY Helix seleccionado está marcado para su eliminación. Haga clic en Proyectos de EY Helix para vincular un nuevo proyecto.',
	903: 'El proyecto EY Helix seleccionado está marcado para su almacenamiento. Haga clic en Proyectos de EY Helix para vincular un nuevo proyecto.',
	926: 'El analizador seleccionado no está disponible en EY Helix. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el departamento de soporte técnico.',
	927: 'Los análisis no están disponibles en el proyecto vinculado. Vaya a EY Helix y complete los pasos de procesamiento de datos y análisis para continuar.',
	928: 'Analizador no válido o faltante en EY Helix. Actualice la página e inténtelo de nuevo. Si el problema persiste, póngase en contacto con el departamento de soporte técnico.',
	929: 'Se ha producido un error relacionado con el proyecto EY Helix vinculado. Los datos no se pueden importar.'
};

export const EngagementProfileRequirementErrors = {
	108: 'El perfil del compromiso no esta completo'
};

export const IndependenceRequirementErrors = {
	103: 'Falta de cumplimiento de la independencia del usuario del compromiso.'
};

export const strategyTypes = [{
	id: 3,
	name: 'En alcance'
},
{
	id: 4,
	name: 'Fuera de alcance'
}
];

export const itAppTypes = [{
	value: 0,
	label: 'Aplicación de TI'
},
{
	value: 1,
	label: 'Organización de servicios'
}
];

export const confidentialityLevels = {
	[confidentialityTypes.DEFAULT]: 'Predeterminado',
	[confidentialityTypes.LOW]: 'Bajo',
	[confidentialityTypes.MODERATE]: 'Moderado',
	[confidentialityTypes.HIGH]: 'Alto'

	// This has been disabled for release 2.5, uncomment if required
	// [confidentialityTypes.CONFIDENTIAL]: 'Confidencial'
};

export const formBodyOptionRiskTypes = [{
	id: 1,
	label: 'Riesgo significativo'
},
{
	id: 2,
	label: 'Riesgo de fraude'
},
{
	id: 3,
	label: 'Riesgo de error material'
}
];

export const formViewTypes = [{
	value: 0,
	label: 'Forma'
},
{
	value: 1,
	label: 'Cambios'
},
{
	value: 2,
	label: 'Detalles'
}
];

export const railFilterValidations = [{
	value: 0,
	label: 'Todo'
},
{
	value: 1,
	label: 'Respuestas incompletas'
},
{
	value: 2,
	label: 'Comentarios no resueltos'
}
];

export const aresRiskTypes = [{
	id: 1,
	name: 'Riesgo significativo'
},
{
	id: 2,
	name: 'Riesgo de fraude'
},
{
	id: 3,
	name: 'Riesgo de error material'
},
{
	id: 4,
	name: 'Estimación de riesgo muy bajo'
},
{
	id: 5,
	name: 'Estimación del riesgo más bajo'
},
{
	id: 6,
	name: 'Estimación del riesgo más alto'
},
{
	id: 7,
	name: 'Estimación - No seleccionada'
}
];

export const materialityTypes = [{
	value: 1,
	label: 'Ingresos antes de impuestos'
},
{
	value: 2,
	label: 'EBIT (Ganancias antes de intereses e impuestos)'
},
{
	value: 3,
	label: 'EBITDA (Ganancias antes de intereses, impuestos, depreciación y amortización)'
},
{
	value: 4,
	label: 'Margen bruto'
},
{
	value: 5,
	label: 'Ingresos'
},
{
	value: 6,
	label: 'Gastos de funcionamiento'
},
{
	value: 7,
	label: 'Equidad'
},
{
	value: 8,
	label: 'Activo'
},
{
	value: 9,
	label: 'Medidas basadas en actividades (Otros)'
},
{
	value: 10,
	label: 'Pérdidas antes de impuestos'
},
{
	value: 11,
	label: 'Medidas basadas en el capital (Otros)'
},
{
	value: 12,
	label: 'Medidas basadas en los ingresos (Otros)'
}
];

export const helixCurrencyType = {
	[currencyType.Functional]: 'Funcional',
	[currencyType.Reporting]: 'Reportes'
};

export const controlRiskType = [{
	id: 1,
	name: 'Confiar'
},
{
	id: 2,
	name: 'No confiar'
},
{
	id: 3,
	name: 'Prueba mp'
}
];

export const inherentRiskType = [{
	id: 1,
	name: 'Más alto'
},
{
	id: 2,
	name: 'Más bajo'
},
{
	id: 3,
	name: 'Muy bajo'
}
];

export const AlraInherentRiskType = [{
	id: 3,
	name: 'No es relevante'
},
{
	id: 2,
	name: 'Más bajo'
},
{
	id: 1,
	name: 'Más alto'
}
];

export const scotInherentRiskType = [{
	id: 1,
	name: 'Superior'
},
{
	id: 2,
	name: 'Inferior'
},
{
	id: 3,
	name: 'SCOT no rutinario'
}
];

export const CRAStrings = {
	Minimal: 'Mínimo',
	Low: 'Bajo',
	'Low +SC': "Bajo + sc",
	Moderate: 'Moderado',
	High: 'Elevado',
	'High +SC': "Alto + sc"
};

export const priorityType = [{
	id: 1,
	name: 'Bajo',
	className: 'Low',
	label: 'L'
},
{
	id: 2,
	name: 'Medio',
	className: 'Medium',
	label: 'M'
},
{
	id: 3,
	name: 'Elevado',
	className: 'High',
	label: 'E'
},
{
	id: 4,
	name: 'Crítico',
	className: 'Critical',
	label: 'C'
}
];

export const kendoLabels = {
	addComment: 'Añadir comentario',
	addColumnBefore: 'Añadir columna a la izquierda',
	addColumnAfter: 'Añadir columna a la derecha',
	addInlineComment: 'Agregar comentario en línea',
	addRowAbove: 'Agregar fila arriba',
	addRowBelow: 'Añadir fila a continuación',
	alignLeft: 'Justificar la izquierda',
	alignRight: 'Justificar el derecho',
	alignCenter: 'Justificar centro',
	alignFull: 'Justificar completo',
	backgroundColor: 'Color de fondo',
	bulletList: 'Insertar lista desordenada',
	bold: 'Negrita',
	backColor: 'Destacar',
	createLink: 'Insertar hyperlink',
	createTable: 'Crear tabla',
	cleanFormatting: 'Formato limpio',
	deleteRow: 'Eliminar fila',
	deleteColumn: 'Eliminar columna',
	deleteTable: 'Eliminar tabla',
	fontSizeInherit: 'Tamaño de fuente',
	foreColor: 'Color de la fuente',
	format: 'Formato',
	fontSize: 'Tamaño de fuente',
	hyperlink: 'Insertar enlace',
	italic: 'Itálica',
	indent: 'Sangrar',
	insertTableHint: 'Crear una tabla {0} por {1}',
	huge: 'Enorme',
	'hyperlink-dialog-content-address': "Dirección web",
	'hyperlink-dialog-title': "Insertar hipervínculo",
	'hyperlink-dialog-content-title': "Título",
	'hyperlink-dialog-content-newwindow': "Abrir enlace en una nueva ventana",
	'hyperlink-dialog-cancel': "Cancelar",
	'hyperlink-dialog-insert': "Insertar",
	large: 'Grande',
	noDataPlaceholder: 'Introducir texto',
	normal: 'Normal',
	orderedList: 'Insertar lista ordenada',
	outdent: 'Sangría',
	paragraphSize: 'Tamaño del párrafo',
	print: 'Imprimir',
	pdf: 'Exportar a PDF',
	redo: 'Rehacer',
	removeFormatting: 'Eliminar formato',
	strikethrough: 'Tachar',
	small: 'Pequeño',
	subscript: 'Subíndice',
	superscript: 'Superíndice',
	underline: 'Subrayar',
	undo: 'Deshacer',
	unlink: 'Desvincular'
};

export const kendoFormatOptions = [{
	text: 'Párrafo',
	value: 'p'
},
{
	text: 'Encabezado 1',
	value: 'h1'
},
{
	text: 'Encabezado 2',
	value: 'h2'
},
{
	text: 'Encabezado 3',
	value: 'h3'
},
{
	text: 'Encabezado 4',
	value: 'h4'
},
{
	text: 'Encabezado 5',
	value: 'h5'
},
{
	text: 'Encabezado 6',
	value: 'h6'
}
];

export const kendoFontSize = [{
	text: '8',
	value: '8px'
},
{
	text: '9',
	value: '9px'
},
{
	text: '10',
	value: '10px'
},
{
	text: '11',
	value: '11px'
},
{
	text: '12',
	value: '12px'
},
{
	text: '14',
	value: '14px'
},
{
	text: '16',
	value: '16px'
},
{
	text: '18',
	value: '18px'
},
{
	text: '20',
	value: '20px'
},
{
	text: '22',
	value: '22px'
},
{
	text: '24',
	value: '24px'
},
{
	text: '26',
	value: '26px'
},
{
	text: '28',
	value: '28px'
},
{
	text: '36',
	value: '36px'
},
{
	text: '48',
	value: '48px'
},
{
	text: '72',
	value: '72px'
}
];

export const ItFlowValidationLabels = {
	ITAppWithoutAtLeastOneRelatedITProcess: 'Aplicación de TI no asociada',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: 'ITGC no asociado',
	ITSPHasNorelatedITRisk: 'ITSP no relacionado',
	ITProcessHasNoRelatedITApplication: 'Proceso de TI no asociado',
	ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication: 'Falta riesgo de TI',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: 'Falta ITGC o marca de que no existe ninguno',
	ITDMorITACWithNoRelatedITApplication: 'Control de aplicaciones/ITDM sin aplicación de TI',
	ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'ITSP a ser eliminado',
	ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'ITGC a ser eliminado',
	ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'Riesgo de TI a ser eliminado',
	ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed: 'ITGC con estrategia de prueba no válida',
	ITGCWithoutASelectedDesignEffectiveness: 'Falta la evaluación del diseño del ITGC',
	SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero: 'SCOT no relacionado',
	AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed: 'Respuesta incoherente para las pruebas de control',
	SCOTWithHasNoITApplicationHasITDMOrAppControls: 'Falta aplicación de TI en SCOT'
};

export const ISA315ITFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: ItFlowValidationLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: ItFlowValidationLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: ItFlowValidationLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: ItFlowValidationLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: ItFlowValidationLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed,
	label: ItFlowValidationLabels.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: ItFlowValidationLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
}
];

/* Notes modal labels */

export const reviewNoteModalLabels = {
	/*Review Notes*/
	engagement: 'Compromiso',
	emptyReplyErrorMsg: 'Agregar texto para continuar',
	lengthReplyErrorMsg: 'La respuesta no puede exceder los 4000 caracteres.',
	documentLabel: 'Documento',
	task: 'Tarea',
	allEngagementFilterLabel: 'Todos los demás compromisos',
	otherEngagementComments: 'Otras notas de compromiso',
	notesModalInstructionalText: 'Vea y responda a las notas de los {0} seleccionados a continuación.',
	commentThread: 'Hilo de notas',
	singleNoteInstructionalText: 'Vea y responda a la nota seleccionada a continuación.',
	emptyNoteDetailsMessage: 'Seleccione una nota para ver los detalles. Para habilitar los controles masivos, utilice la tecla de control o mayús y seleccione varias notas de revisión. Si desea trabajar en una nota individual, seleccione esa nota de la lista.',
	documentReviewNotesLabel: 'Notas del documento',
	addNewReviewNoteButtonText: 'Añadir nota',
	noNotesAssociatedWithDocumentLabel: 'Deje una nota utilizando las entradas a continuación. Asigne la nota a un usuario y especifique la prioridad y la fecha de vencimiento.',
	noNotesFound: 'No se han encontrado notas',
	noNotesAssociatedWithTaskLabel: 'No hay notas {0} asociadas a la tarea.',
	allNotesLabel: 'Todas las notas',
	charactersLabel: 'Caracteres',
	myNotesLabel: 'Mis notas',
	showClearedLabel: 'Mostrar borrado',
	showClosedLabel: 'Espectáculo cerrado',
	assignedToLabel: 'Asignado a',

	ofLabel: 'de',
	enterNoteText: 'Introducir nota',
	addNewNoteModalClose: 'Cerrar',
	addNewNoteModalTitleLabel: 'Añadir nueva nota',
	editNoteModalTitleLabel: 'Editar nota',
	deleteIconHoverText: 'Borrar',
	deleteIconModalAcceptText: 'Borrar',
	deleteIconModalConfirmMessage: '¿Está seguro de que desea eliminar su respuesta a esta nota?',
	deleteIconModalConfirmMessageParent: '¿Está seguro de que desea eliminar la nota seleccionada?',
	deleteIconModalTitleLabel: 'Eliminar nota',
	deleteReplyIconModalTitle: 'Eliminar respuesta',
	emptyRepliesMessage: 'Aún no hay respuestas',
	replyInputPlaceholder: 'Responder a esta nota',
	replyInputPlaceholderEdit: 'Editar la respuesta con una nota o una nota de voz',
	noteInputPlaceholderEdit: 'Editar con una nota o una nota de voz',
	replyText: 'Texto de respuesta',
	editReplyModelTitle: 'Editar respuesta',
	editReplyPlaceholder: 'Introduzca la respuesta',
	noteDueDateLabel: 'Dos',

	priorityLabel: 'Prioridad',
	dueDateLabel: 'Fecha de vencimiento',
	dueLabel: 'Dos',
	status: 'Estado',
	noteModifiedDateLabel: 'Modificado',
	cancelLabel: 'Cancelar',
	saveLabel: 'Salvar',
	clearedBy: 'Despejado por',
	closedBy: 'Cerrado por',
	reopenedBy: 'Reabierto por',
	reply: 'Respuesta',
	editIconHoverTextLabel: 'Editar',
	required: 'Obligatorio',
	closeTitle: 'Cerrar',
	otherEngagementNotes: 'Otras notas del compromiso',
	closeLabel: 'Cerrar',
	showMore: 'Mostrar más',
	showLess: 'Mostrar menos',
	showMoreEllipsis: 'Mostrar más...',
	showLessEllipsis: 'Mostrar menos...',
	noResultFound: 'No se han encontrado resultados',
	engagementNameLabel: 'Nombre del compromiso: ',
	taskReviewNotesLabel: 'Notas de tareas',
	fromUserLabel: 'De',
	toUserLabel: 'Para',
	view: 'Vista',
	dueDateRequiredTextError: 'Se requiere fecha de vencimiento'
};

export const notesFilterLabels = [{
	id: notesFilter.allNotes,
	label: 'Todas las notas',
	value: notesFilter.allNotes
},
{
	id: notesFilter.myNotes,
	label: 'Mis notas',
	value: notesFilter.myNotes
},
{
	id: notesFilter.authoredByMeNotes,
	label: 'Asignado a mí',
	value: notesFilter.authoredByMeNotes
}
];

export const reviewerAssignments = {
	taskLayoutHeaderAssignments: 'Asignaciones',
	manageAssigmentsStep2: 'Editar asignación de tareas',
	editAssignment: 'Editar asignación',
	deleteAssignment: 'Eliminar asignación',
	manageAssigmentsStep3: 'Completar tarea',
	taskAssigmentStatusHeader: 'Estatus de la asignación',
	taskAssignmentName: 'Asignación',
	dueDateAssigment: 'Vence',
	editDueDate: 'Opcional: Editar días antes de la fecha de finalización',
	teamMemberAssigmentLabel: 'Miembro del equipo',
	currentAssigmentLabel: 'Actual',
	handOffToAssigmentLabel: 'Entregado a: ',
	priorToEndDateLabel: 'antes de la fecha de finalización',
	noTimePhaseAssigmentLabel: 'Sin fase de tiempo asignada',
	closedByAssigmentLabel: 'Cerrado por',
	onAssingmentLabel: 'en',
	preparerAssigmentOpenTitleTip: 'Entregue esta tarea para cerrar la asignación de tareas',
	reviewerAssigmentOpenTitleTip: 'Marcar la asignación de tareas como cerrada',
	reviewerAssigmentClosedTitleTip: 'Marcar la asignación de tareas como abierta',
	AssigmentLabel: 'Entregue esta tarea para cerrar la asignación de tareas',
	timePhaseName: 'Fase de tiempo: ',
	timePhaseEndDate: 'Fecha final: ',
	AssignmentType: [{
		id: 1,
		displayName: 'Preparador'
	},
	{
		id: 2,
		displayName: 'Revisor de detalles'
	},
	{
		id: 3,
		displayName: 'Revisor general'
	},
	{
		id: 4,
		displayName: 'Socio'
	},
	{
		id: 5,
		displayName: 'EQR'
	},
	{
		id: 6,
		displayName: 'Otro'
	}
	],
	AssignmentStatus: [{
		id: 1,
		displayName: 'Abierto'
	},
	{
		id: 2,
		displayName: 'En progreso'
	},
	{
		id: 3,
		displayName: 'Cerrado'
	},
	{
		id: 4,
		displayName: 'No asignado'
	}
	],
	assignmentTableColumnHeader: 'Asignación',
	teamMemberTableColumnHeader: 'Miembro del equipo',
	dueDaysTableColumnHeader: 'Debido',
	daysPriorToEndDate: 'Días antes de la fecha de finalización',
	handoffButton: 'Entrega'
};
/* Notes modal labels */

export const handOffModal = {
	title: 'Traslade',
	description: 'Entregue esta tarea al siguiente miembro del equipo. Para firmar un archivo de evidencia, seleccione una de las opciones a continuación.',
	dropdownLabel: 'Traslado a',
	closeTitle: 'Cancelar',
	confirmButton: 'Traslade',
	evidence: 'Evidencia',
	evidenceSignOffTitle: 'Inicie sesión en todo como: ',
	existingSignOffs: 'Sesiones existentes',
	noDocumentsAvailable: 'No hay documentos disponibles'
};

// manage scot modal labels
export const manageSCOTModal = {
	title: 'Administrar SCOT',
	description: 'Puede crear, editar o eliminar SCOT existentes. Los cambios se aplicarán una vez guardados.',
	addSCOTLink: 'Añadir SCOT'
};

export const deleteSCOTModal = {
	title: 'Eliminar SCOT',
	description: 'Se eliminarán los siguientes SCOT. La acción no se puede deshacer.'
};

export const manageITAppModalLabels = {
	title: 'Administrar aplicaciones de TI',
	description: 'Cree nuevas aplicaciones de TI o edite y elimine las aplicaciones de TI existentes a continuación.',
	inputNameTitle: 'Nombre de la aplicación de TI',
	deleteConfirmMessage: '¿Está seguro de que desea eliminar la aplicación de TI <b>{0}</b>? Esto no se puede deshacer.',
	addSuccessMessage: "Se creó con éxito la aplicación de TI'{0}'",
	editSuccessMessage: "Ediciones guardadas correctamente en la aplicación de TI'{0}'",
	deleteSuccessMessage: "'{0}' se ha eliminado con éxito"
};

export const manageSOModalLabels = {
	title: 'Administrar organizaciones de servicios',
	description: 'Cree nuevas organizaciones de servicio o edite y elimine las organizaciones de servicio existentes a continuación.',
	inputNameTitle: 'Nombre de la organización de servicios',
	deleteConfirmMessage: '¿Está seguro de que desea eliminar la organización de servicio <b>{0}</b>? Esto no se puede deshacer.',
	addSuccessMessage: "Se ha creado correctamente la organización de servicios'{0}'",
	editSuccessMessage: "Ediciones guardadas correctamente en la organización de servicio'{0}'",
	deleteSuccessMessage: "'{0}' se ha eliminado correctamente",
    addServiceOrganization: 'Agregar organización de servicios',
	editServiceOrganization: 'Editar organización del servicio',
	deleteServiceOrganization: 'Eliminar organización de servicios'
};

export const customNameModal = {
	title: 'Sufijo de nombre de fuente JE',
	closeTitle: 'Cancelar',
	save: 'Guardar',
	suffixRequired: 'Sufijo requerido!',
	suffix: 'Sufijo',
	addSuffix: 'Agregar sufijo',
	editSuffix: 'Editar sufijo'
};

export const GuidedWorkFlowLabels = {
	RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM: 'Eventos y condiciones no relacionados / riesgo de incorrección',
	RisksUnrelatedToAnAssertionForGuidedWorkflow: 'Riesgos no relacionados',
	IncompleteMeasurementBasisForecastAmount: 'Base incompleta',
	IncompleteMeasurementBasisForecastAmountRationale: 'Justificación de la base requerida',
	IncompleteMeasurementBasisAdjustedAmount: 'Cantidad ajustada incompleta',
	IncompletePlanningMateriality: 'PM incompleto',
	PlanningMaterialityGreaterThanMaximumAmount: 'PM demasiado grande',
	IncompletePlanningMaterialityRationale: 'PM Justificación requerida',
	IncompleteTolerableError: 'TE incompleto',
	TENotWithinRangeOfAllowedValues: 'Porcentaje de TE inválido',
	IncompleteTolerableErrorRationale: 'Requerido',
	IncompleteSAD: 'SAD Incompleto',
	SADGreaterThanMaximum: 'SAD demasiado grande',
	IncompleteSADRationale: 'SAD requiere justificación',
	IncompletePACESelection: 'PACE incompleto',
	AccountWithoutIndividualRiskAssessmentForm: 'Documento faltante de cuenta{0}',
	EstimateWithoutIndividualEstimateForm: 'Documento faltante de estimación{0}',
	AccountWithoutIndividualAnalyticForm: 'Documento faltante de cuenta{0}',
	MultiEntityWithoutIndividualProfileForm: 'Documento de Perfil Individual de Entidad sin Entidad Múltiple',
	AccountAccountTypeIDDoesNotMatchAction: 'Selección incoherente de la designación de la cuenta',
	AccountHasEstimateDoesNotMatchAction: 'Selección de estimación de cuenta inconsistente',
	AccountFormOptionHasRelatedRisksNotAssociatedToAccount: 'Riesgo sin asociación de cuenta relacionada',
	AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk: 'Mayor aseveración de riesgo inherente sin riesgo',
	AccountMissingSubstantiveProcedure: 'Cuenta/Entidades sin procedimiento sustantivo',
	MultiEntityNotRelatedToALLPSTACTForRelatedAccount: 'Las cuentas/entidades requieren actualización de contenido',
	ComponentWithoutGroupInvolvementForm: 'Componente sin un formulario de participación grupal (excluyendo componentes de solo referencia)',
	ComponentWithoutRelatedGroupAssessmentInstruction: 'Componente sin instrucción de evaluación de riesgos grupal',
	IncompleteAssertionRiskLevel: 'Nivel de riesgo de aserción incompleto',
	EstimateAccountWithoutEsimatePSPIndex: 'Estimación de la cuenta sin estimación del índice PSP',
	AccountExecutedWithoutRelatedComponent: 'Grupo - Cuentas (ejecutadas en otros compromisos) sin un Componente de Alcance Completo o Específico relacionado',
	MultiEntityAccountWithoutRelatedToAnyMultiEntity: 'Cuenta sin entidad vinculada',
	ChangeNotSubmittedMultiEntityFullProfile: 'Cambios no enviados',
	ChangeNotSubmittedMultiEntityIndividualDocument: 'Cambios no enviados',
	AccountTypeWithMissingInformation: 'Información faltante de la cuenta',
	DocumentUploadMissingRequiredPICEQRSignOffs: 'Falta (n) las firmas en la Evidencia',
	DocumentUploadMissingRequiredPICEQRSignOffRequirements: 'Faltan requisitos de firma en la evidencia',
	DocumentUploadMissingPreparerOrReviewerSignOffs: 'Carga de documentos: faltan firmas del preparador o del revisor',
	ITAppWithoutAtLeastOneRelatedITProcess: 'Aplicación de TI no relacionada',
	ITProcessHasNoRelatedITApplication: 'Proceso de TI no relacionado',
	ITGCWithoutASelectedDesignEffectiveness: 'Falta la evaluación del diseño de ITGC',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: 'ITGC no relacionado',
	ITSPHasNorelatedITRisk: 'ITSP no relacionado',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: 'Falta ITGC o marcar que no existe ninguno',
	EstimateWithoutAccountRelated: 'Estimación sin relación a cuentas',
	EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount: 'Estimación de riesgo mayor/menor relacionada con alguna cuenta no estimada',
	RiskEstimateRiskTypeIDDoesNotMatchAction: "La respuesta de la categoría de Estimación no está alineada con la designación en'Editar Estimación'",
	LowerorHigherRiskEstimateWithoutEstimateSCOT: 'Estimado sin SCOT válido',
	EstimateWithoutIndividualEstimateDocument: 'Estimación de documentos individuales faltantes',
	EstimateAccountWithoutHigherOrLowerRiskEstimate: 'Cuenta sin estimado válido',
	EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary: 'Cuenta de estimación sin estimación de mayor o menor riesgo',
	EstimateScotWithoutHigherOrLowerRiskEstimate: 'Estimación de SCOT sin estimación de mayor o menor riesgo',
	HigherRiskEstimateWithoutRisk: 'Estimación de riesgo alta sin riesgo válido relacionado',
	PICEQRSignOffRequirements: 'PIC or EQR Signoff requirement does not match response',
	AdjustmentsWithoutAnyEvidence: 'Ajustes sin evidencia',
	AdjustmentsThatDoNotNet: 'Ajustes que no se netan',
	DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration: 'Documento retirado o en proceso de cierre para colaboración',
	NonEngagementWideTasksMissingEvidence: 'Falta de evidencia en tareas de gran alcance',
	EstimatesMustBeMarkedHigherRisk: 'Riesgo significativo/de fraude relacionado con una estimación que no es un riesgo alto',
	SCOTEstimateNoRelatedWalkthroughForm: 'SCOT/Estimación sin Walkthrough',
	SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2: 'SCOT sin cuenta relacionada',
	AccountSignificantDisclosureWithNoRelatedSCOTV2: 'Account - Significant Account / Significant Disclosure that is not a CT only Account with no related SCOT or an Estimate when it is an Estimate Account',
	ITApplicationWithoutITAppRiskAssessmentIndividualDocument: 'Technology risk assessment missing document',
	ITApplicationWithoutITAppPlanningIndividualDocument: 'Technology missing document',
	FormContentWithoutHeader: 'Form Content without Header',
	RisksWithoutAnyRelatedAssertions: 'There are risks that have not been related to at least one assertion',
	AssertionsWithIncompleteCRA: 'There are assertions missing an inherent and/or control risk assessment',
	LimitedRiskOrInsignificantAccountMissingRationale: 'All limited risk and insignificant accounts shall have rationale provided',
	ITProcessWithoutWalkthroughDocument: 'ITProcess without IT process - Walkthrough - Individual',
	ITProcessIsUncategorized: 'IT Process - ITProcessTypeID is Uncategorized',
	ITProcessWithNoRelatedITApplication: 'ITProcess - ITProcess with no related IT Application'
};

export const GuidedWorkFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.RiskEstimateRiskTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.RiskEstimateRiskTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.FormContentWithoutHeader,
	label: GuidedWorkFlowLabels.FormContentWithoutHeader
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary
},
{
	validationId: validationTypes.EstimateScotWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateScotWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.HigherRiskEstimateWithoutRisk,
	label: GuidedWorkFlowLabels.HigherRiskEstimateWithoutRisk
},
{
	validationId: validationTypes.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM,
	label: GuidedWorkFlowLabels.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.RisksUnrelatedToAnAssertionForGuidedWorkflow,
	label: GuidedWorkFlowLabels.RisksUnrelatedToAnAssertionForGuidedWorkflow
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmount
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmountRationale,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmountRationale
},
{
	validationId: validationTypes.IncompleteMeasurementBasisAdjustedAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisAdjustedAmount
},
{
	validationId: validationTypes.IncompletePlanningMateriality,
	label: GuidedWorkFlowLabels.IncompletePlanningMateriality
},
{
	validationId: validationTypes.PlanningMaterialityGreaterThanMaximumAmount,
	label: GuidedWorkFlowLabels.PlanningMaterialityGreaterThanMaximumAmount
},
{
	validationId: validationTypes.IncompletePlanningMaterialityRationale,
	label: GuidedWorkFlowLabels.IncompletePlanningMaterialityRationale
},
{
	validationId: validationTypes.IncompleteTolerableError,
	label: GuidedWorkFlowLabels.IncompleteTolerableError
},
{
	validationId: validationTypes.TENotWithinRangeOfAllowedValues,
	label: GuidedWorkFlowLabels.TENotWithinRangeOfAllowedValues
},
{
	validationId: validationTypes.IncompleteTolerableErrorRationale,
	label: GuidedWorkFlowLabels.IncompleteTolerableErrorRationale
},
{
	validationId: validationTypes.IncompleteSAD,
	label: GuidedWorkFlowLabels.IncompleteSAD
},
{
	validationId: validationTypes.SADGreaterThanMaximum,
	label: GuidedWorkFlowLabels.SADGreaterThanMaximum
},
{
	validationId: validationTypes.IncompleteSADRationale,
	label: GuidedWorkFlowLabels.IncompleteSADRationale
},
{
	validationId: validationTypes.IncompletePACESelection,
	label: GuidedWorkFlowLabels.IncompletePACESelection
},
{
	validationId: validationTypes.AccountWithoutIndividualRiskAssessmentForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualRiskAssessmentForm
},
{
	validationId: validationTypes.EstimateWithoutIndividualEstimateForm,
	label: GuidedWorkFlowLabels.EstimateWithoutIndividualEstimateForm
},
{
	validationId: validationTypes.AccountWithoutIndividualAnalyticForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualAnalyticForm
},
{
	validationId: validationTypes.MultiEntityWithoutIndividualProfileForm,
	label: GuidedWorkFlowLabels.MultiEntityWithoutIndividualProfileForm
},
{
	validationId: validationTypes.AccountAccountTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountAccountTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.AccountHasEstimateDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountHasEstimateDoesNotMatchAction
},
{
	validationId: validationTypes.AccountFormOptionHasRelatedRisksNotAssociatedToAccount,
	label: GuidedWorkFlowLabels.AccountFormOptionHasRelatedRisksNotAssociatedToAccount
},
{
	validationId: validationTypes.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk,
	label: GuidedWorkFlowLabels.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk
},
{
	validationId: validationTypes.MultiEntityNotRelatedToALLPSTACTForRelatedAccount,
	label: GuidedWorkFlowLabels.MultiEntityNotRelatedToALLPSTACTForRelatedAccount
},
{
	validationId: validationTypes.AccountMissingSubstantiveProcedure,
	label: GuidedWorkFlowLabels.AccountMissingSubstantiveProcedure
},
{
	validationId: validationTypes.ComponentWithoutGroupInvolvementForm,
	label: GuidedWorkFlowLabels.ComponentWithoutGroupInvolvementForm
},
{
	validationId: validationTypes.ComponentWithoutRelatedGroupAssessmentInstruction,
	label: GuidedWorkFlowLabels.ComponentWithoutRelatedGroupAssessmentInstruction
},
{
	validationId: validationTypes.EstimateAccountWithoutEstimatePSPIndex,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutEsimatePSPIndex
},
{
	validationId: validationTypes.AssertionInherentRiskWithoutRelatedHigherRisk,
	label: GuidedWorkFlowLabels.IncompleteAssertionRiskLevel
},
{
	validationId: validationTypes.AccountGroupWithoutAComponent,
	label: GuidedWorkFlowLabels.AccountExecutedWithoutRelatedComponent
},
{
	validationId: validationTypes.MultiEntityAccountWithoutRelatedToAnyMultiEntity,
	label: GuidedWorkFlowLabels.MultiEntityAccountWithoutRelatedToAnyMultiEntity
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityFullProfile,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityFullProfile
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityIndividualDocument,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityIndividualDocument
},
{
	validationId: validationTypes.AccountWithMissingValues,
	label: GuidedWorkFlowLabels.AccountTypeWithMissingInformation
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffs
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffRequirements
},
{
	validationId: validationTypes.DocumentUploadMissingPreparerOrReviewerSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingPreparerOrReviewerSignOffs
},
{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: GuidedWorkFlowLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: GuidedWorkFlowLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: GuidedWorkFlowLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: GuidedWorkFlowLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: GuidedWorkFlowLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
},
{
	validationId: validationTypes.EstimateWithoutAccountRelated,
	label: GuidedWorkFlowLabels.EstimateWithoutAccountRelated
},
{
	validationId: validationTypes.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount,
	label: GuidedWorkFlowLabels.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount
},
{
	validationId: validationTypes.LowerorHigherRiskEstimateWithoutEstimateSCOT,
	label: GuidedWorkFlowLabels.LowerorHigherRiskEstimateWithoutEstimateSCOT
},
{
	validationId: validationTypes.PICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.PICEQRSignOffRequirements
},
{
	validationId: validationTypes.EstimatesMustBeMarkedHigherRisk,
	label: GuidedWorkFlowLabels.EstimatesMustBeMarkedHigherRisk
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.NonEngagementWideTasksMissingEvidence,
	label: GuidedWorkFlowLabels.NonEngagementWideTasksMissingEvidence
},
{
	validationId: validationTypes.AdjustmentsWithoutAnyEvidence,
	label: GuidedWorkFlowLabels.AdjustmentsWithoutAnyEvidence
},
{
	validationId: validationTypes.AdjustmentsThatDoNotNet,
	label: GuidedWorkFlowLabels.AdjustmentsThatDoNotNet
},
{
	validationId: validationTypes.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration,
	label: GuidedWorkFlowLabels.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration
},
{
	validationId: validationTypes.ITApplicationWithoutITAppRiskAssessmentIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppRiskAssessmentIndividualDocument
},
{
	validationId: validationTypes.SCOTEstimateNoRelatedWalkthroughForm,
	label: GuidedWorkFlowLabels.SCOTEstimateNoRelatedWalkthroughForm
},
{
	validationId: validationTypes.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2,
	label: GuidedWorkFlowLabels.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2
},
{
	validationId: validationTypes.AccountSignificantDisclosureWithNoRelatedSCOTV2,
	label: GuidedWorkFlowLabels.AccountSignificantDisclosureWithNoRelatedSCOTV2
},
{
	validationId: validationTypes.ITApplicationWithoutITAppPlanningIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppPlanningIndividualDocument
},
{
	validationId: validationTypes.RisksWithoutAnyRelatedAssertions,
	label: GuidedWorkFlowLabels.RisksWithoutAnyRelatedAssertions
},
{
	validationId: validationTypes.AssertionsWithIncompleteCRA,
	label: GuidedWorkFlowLabels.AssertionsWithIncompleteCRA
},
{
	validationId: validationTypes.LimitedRiskOrInsignificantAccountMissingRationale,
	label: GuidedWorkFlowLabels.LimitedRiskOrInsignificantAccountMissingRationale
},
{
	validationId: validationTypes.ITProcessWithoutWalkthroughDocument,
	label: GuidedWorkFlowLabels.ITProcessWithoutWalkthroughDocument
},
{
	validationId: validationTypes.ITProcessIsUncategorized,
	label: GuidedWorkFlowLabels.ITProcessIsUncategorized
},
{
	validationId: validationTypes.ITProcessWithNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessWithNoRelatedITApplication
}

];

// Label overrides (redefine here labels / objects that apply for a different part of the application)
export const resourceOverrides = {
	['Ares']: {
		labels: {
			notAROMM: 'No es un riesgo de error material',
			fraudRisk: 'Riesgo de fraude',
			significantRisk: 'Riesgo significativo',
			identifiedRiskFactors: 'Evento/condición identificada, riesgo de error material, riesgos significativos y riesgos de fraude',
			countUnassociatedRisk: "Los eventos/condiciones no están relacionados/no están marcados como'No es un riesgo de error material'."
		},
		riskTypes: [{
			id: 1,
			name: 'Riesgo significativo',
			abbrev: 'S',
			label: 'Significativo',
			title: 'Riesgo significativo'
		},
		{
			id: 2,
			name: 'Riesgo de fraude',
			abbrev: 'F',
			label: 'Fraude',
			title: 'Riesgo de fraude'
		},
		{
			id: 3,
			name: 'Riesgo de error material',
			abbrev: 'R',
			label: 'Riesgo de error material',
			title: 'Riesgo de error material'
		}
		]
	}
};

export const jeSourceTypes = [{
	value: 1,
	label: 'Generado por Sistema'
},
{
	value: 2,
	label: 'Manual'
},
{
	value: 3,
	label: 'Ambos'
}
];

export const hasJournalEntriesOption = [{
	value: 1,
	label: 'Sí'
},
{
	value: 2,
	label: 'No'
}
];

export const filterReviewNoteStatus = [{
	value: 1,
	label: 'Abierto'
},
{
	value: 2,
	label: 'Aclarado'
},
{
	value: 3,
	label: 'Cerrado'
}
];

export const EntitiesLabels = {
	close: 'Cerrar',
	cancel: 'Cancelar',
	repNoRecordMessage: 'No se han encontrado resultados',
	edit: 'Editar',
	delete: 'Borrar',
	actions: 'Acciones',
	show: 'Mostrar',
	first: 'Primero',
	last: 'Último',
	prev: 'Pagina anterior',
	next: 'Siguiente página',
	search: 'Buscar',
	primary: 'Primary',
	knowledgeRiskLabel: 'Risks from knowledge cannot be edited or deleted',


	[Entity.Account]: {
		manageEntity: 'Administrar cuentas y divulgaciones',
		searchEntity: 'Buscar cuentas',
		createEntity: 'Cuenta nueva',
		entityName: 'cuenta',
		entityNameCaps: 'Cuenta',
		entityNamePlural: 'Cuentas',
		placeholderText: 'Cree nuevas cuentas y divulgaciones o edite y elimine las cuentas y divulgaciones existentes a continuación.',
		deleteConfirmLabel: '¿Estás seguro de que quieres eliminar esta cuenta? Se eliminarán todas las relaciones existentes. Esta acción no se puede deshacer.'
	},
	[Entity.Estimate]: {
		manageEntity: 'Gestionar estimados',
		searchEntity: 'Buscar estimados',
		createEntity: 'Nueva estimación',
		entityName: 'estimar',
		entityNameCaps: 'Estimar',
		entityNamePlural: 'Estimaciones',
		placeholderText: 'Cree nuevos presupuestos o edite y elimine los presupuestos existentes a continuación.',
		deleteConfirmLabel: '¿Estás seguro de que quieres eliminar esta estimación? Se eliminarán todas las relaciones existentes. Esta acción no se puede deshacer.'
	},
	[Entity.Risk]: {
		manageEntity: 'Gestionar riesgos',
		searchEntity: 'Buscar riesgo',
		createEntity: 'Nuevo riesgo',
		entityName: 'riesgo',
		entityNameCaps: 'Riesgo',
		entityNamePlural: 'Riesgos',
		placeholderText: 'Cree nuevos riesgos o edite y elimine los riesgos existentes a continuación.',
		deleteConfirmLabel: '¿Estás seguro de que quieres eliminar este riesgo? Se eliminarán todas las relaciones existentes. Esta acción no se puede deshacer.'
	},
	[Entity.STEntity]: {
		manageEntity: 'Administrar entidades',
		searchEntity: 'Entidades de búsqueda',
		createEntity: 'Nueva entidad',
		entityName: 'entidad',
		entityNameCaps: 'Entidad',
		entityNamePlural: 'Entidades',
		placeholderText: 'Cree nuevas entidades o edite y elimine las entidades existentes a continuación.',
		deleteEntity: 'Eliminar entidad',
		deleteConfirmLabel: '¿Está seguro de que desea eliminar esta entidad? Se eliminarán todas las relaciones existentes. Esta acción no se puede deshacer.'
	},
	[Entity.Control]: {
		manageEntity: 'Gestionar el control',
		searchEntity: 'Buscar Control',
		createEntity: 'Nuevo control',
		entityName: 'control',
		entityNameCaps: 'Control',
		entityNamePlural: 'Controles',
		placeholderText: 'Cree nuevos controles o edite y elimine los controles existentes a continuación.',
		deleteEntity: 'Eliminar Control',
		deleteConfirmLabel: '¿Está seguro de que desea eliminar este control? Se eliminarán todas las relaciones existentes. Esta acción no se puede deshacer.'
	},
	[Entity.ITProcess]: {
		manageEntity: 'Gestione los procesos de TI',
		searchEntity: 'Proceso de búsqueda de TI',
		createEntity: 'Nuevo proceso informático',
		entityName: 'Proceso informático',
		entityNameCaps: 'Procesos de TI',
		entityNamePlural: 'Procesos de TI',
		placeholderText: "Cree nuevos procesos de TI o edite y elimine los procesos de TI existentes a continuación.",
		deleteEntity: 'Eliminar proceso de TI',
		deleteConfirmLabel: '¿Está seguro de que desea eliminar este proceso informático? Se eliminarán todas las relaciones existentes. Esta acción no se puede deshacer.'
	},
	[Entity.ITRisk]: {
		manageEntity: 'Gestione los riesgos tecnológicos',
		searchEntity: 'Buscar Riesgos tecnológicos',
		createEntity: 'Nuevo riesgo tecnológico',
		entityName: 'Riesgo tecnológico',
		entityNameCaps: 'Riesgos tecnológicos',
		entityNamePlural: 'Riesgos tecnológicos',
		placeholderText: 'Cree nuevos riesgos tecnológicos o edite y elimine los riesgos tecnológicos existentes a continuación.',
		deleteEntity: 'Eliminar el riesgo tecnológico',
		deleteConfirmLabel: '¿Estás seguro de que quieres eliminar este riesgo tecnológico? Se eliminarán todas las relaciones existentes. Esta acción no se puede deshacer.'
	},
	[Entity.ITControl]: {
		ITGC: {
			manageEntity: 'Gestione los ITGC',
			searchEntity: 'Buscar ITGCs',
			createEntity: 'Nuevo ITGC',
			editEntity: 'Editar ITGC',
			viewEntity: 'Ver ITGC',
			entityName: 'ITGC',
			entityNameCaps: 'ITGC',
			entityNamePlural: 'ITGCs',
			placeholderText: 'Cree un nuevo ITGC o edite y elimine los ITGC existentes a continuación.',
			deleteEntity: 'Eliminar ITGC',
			close: 'Cerrar',
			cancel: 'Cancelar',
			processIdRequired: 'Se requiere un proceso de TI',
			save: 'Guardar',
			confirm: 'Confirmar',
			iTProcesslabel: 'Proceso TI',
			saveAndCloseLabel: 'Guardar y cerrar',
			saveAndCreateLabel: 'Guardar y crear otra',
			deleteConfirmLabel: '¿Está seguro de que desea eliminar este ITGC? Se eliminarán todas las relaciones existentes. Esta acción no se puede deshacer.',
			operationEffectiveness: 'Eficacia operativa',
			itDesignEffectivenessHeader: 'Eficacia del diseño',
			itTestingColumnHeader: 'Pruebas',
			testingTitle: 'Pruebas',
			frequency: 'Frecuencia',
			controlOpertaingEffectiveness: 'Eficacia operativa',
			designEffectiveness: 'Eficacia del diseño',
			frequencyITGC: 'Seleccionar frecuencia',
			nameITGC: 'Nombre de ITGC (obligatorio)',
			itspNameRequired: 'Nombre de ITSP (obligatorio)',
			noResultsFound: 'No se han encontrado resultados',
			selectITRisk: 'Seleccione el Riesgo Tecnológico (obligatorio)',
			itRiskRequired: 'Riesgo Tecnológico (obligatorio)',
			itRiskName: 'Riesgo Tecnológico',
			inputInvaildCharacters: 'La entrada no puede incluir la siguiente cadena de caracteres: */:<>\\?|"',
			itControlNameRequired: 'El nombre de ITGC es obligatorio',
			itgcTaskDescription: 'Realizar nuestras pruebas diseñadas de ITGC para obtener evidencia de auditoría suficiente y apropiada de su efectividad operativa durante todo el período de confianza.',
			selectITProcess: 'Seleccione el proceso de TI (obligatorio)',
			itProcessRequired: 'Proceso de TI (obligatorio)',
			riskNameRequired: 'Riesgo Tecnológico es requerido',
			addModalDescription: 'Introduzca la descripción del ITGC.',
			editModalDescription: 'Edite el ITGC y sus atributos asociados.',
			controlDesignEffectiveness: {
				[0]: {
					description: 'No seleccionado'
				},
				[1]: {
					description: 'Eficaz'
				},
				[2]: {
					description: 'Ineficaz'
				}
			},
			controlTesting: {
				[0]: {
					description: 'No seleccionado'
				},
				[1]: {
					description: 'Sí'
				},
				[2]: {
					description: 'No'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: 'No seleccionado'
				},
				[1]: {
					description: 'Eficaz'
				},
				[2]: {
					description: 'Ineficaz'
				}
			}
		},
		ITSP: {
			manageEntity: 'Gestionar los ITSPs',
			searchEntity: 'Buscar ITSPs',
			createEntity: 'Nuevo ITSP',
			editEntity: 'Editar ITSP',
			viewEntity: 'Ver ITSP',
			inputInvaildCharacters: 'La entrada no puede incluir la siguiente cadena de caracteres: */:<>\\?|"',
			addModalDescription: 'Introduzca la descripción del ITSP.',
			editModalDescription: 'Edite el ITSP y sus atributos asociados.',
			entityName: 'ITSP',
			selectITProcess: 'Seleccione el proceso de TI (obligatorio)',
			entityNameCaps: 'ITSP',
			processIdRequired: 'Se requiere un proceso de TI',
			entityNamePlural: 'ITSPs',
			itspRequired: 'El nombre de ITSP es obligatorio',
			close: 'Cerrar',
			cancel: 'Cancelar',
			iTProcesslabel: 'Proceso TI',
			save: 'Guardar',
			confirm: 'Confirmar',
			saveAndCloseLabel: 'Guardar y cerrar',
			riskNameRequired: 'Riesgo Tecnológico es requerido',
			saveAndCreateLabel: 'Guardar y crear otra',
			placeholderText: 'Cree un nuevo ITSP o edite y elimine los ITSP existentes a continuación.',
			deleteEntity: 'Eliminar ITGC',
			deleteConfirmLabel: '¿Está seguro de que desea eliminar este ITSP? Se eliminarán todas las relaciones existentes. Esta acción no se puede deshacer.',
			itspTaskDescription: 'Personalice esta descripción de la tarea para diseñar la naturaleza, el momento y el alcance de los procedimientos sustantivos de TI para obtener evidencia de auditoría suficiente y apropiada de que los riesgos tecnológicos se abordan de manera efectiva durante todo el período de confianza.<br />Cuando el procedimiento sustantivo de TI se realiza a partir de una fecha intermedia, diseñe y realice procedimientos para obtener evidencia adicional de que los riesgos tecnológicos se abordan durante el período cubierto por nuestros procedimientos intermedios hasta el final del período.<br />We concluir sobre los resultados de nuestros procedimientos sustantivos de TI.',
			operationEffectiveness: 'Eficacia operativa',
			itDesignEffectivenessHeader: 'Eficacia del diseño',
			itTestingColumnHeader: 'Pruebas',
			testingTitle: 'Pruebas',
			frequency: 'Frecuencia',
			controlOpertaingEffectiveness: 'Eficacia operativa',
			designEffectiveness: 'Eficacia del diseño',
			frequencyITGC: 'Seleccionar frecuencia',
			nameITGC: 'Nombre de ITGC (obligatorio)',
			itspNameRequired: 'Nombre de ITSP (obligatorio)',
			noResultsFound: 'No se han encontrado resultados',
			selectITRisk: 'Seleccione el Riesgo Tecnológico (obligatorio)',
			itRiskRequired: 'Riesgo Tecnológico (obligatorio)',
			itRiskName: 'Riesgo Tecnológico',
			itProcessRequired: 'Proceso de TI (obligatorio)',
			controlDesignEffectiveness: {
				[0]: {
					description: 'No seleccionado'
				},
				[1]: {
					description: 'Eficaz'
				},
				[2]: {
					description: 'Ineficaz'
				}
			},
			controlTesting: {
				[0]: {
					description: 'No seleccionado'
				},
				[1]: {
					description: 'Sí'
				},
				[2]: {
					description: 'No'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: 'No seleccionado'
				},
				[1]: {
					description: 'Eficaz'
				},
				[2]: {
					description: 'Ineficaz'
				}
			},
		}
	},
	[Entity.ITSOApplication]: {
		manageEntity: 'Gestionar aplicación de IT',
		searchEntity: 'Buscar aplicación IT',
		createEntity: 'Nueva aplicación IT',
		entityName: 'Aplicación de IT',
		entityNameCaps: 'Aplicaciones de IT',
		entityNamePlural: 'Aplicaciones de IT',
		placeholderText: 'Cree nuevas aplicaciones de TI o edite y elimine las aplicaciones de TI existentes a continuación.',
		deleteEntity: 'Eliminar aplicación de IT',
		deleteConfirmLabel: '¿Está seguro de que desea eliminar esta aplicación informática? Se eliminarán todas las relaciones existentes. Esta acción no se puede deshacer.'
	},
	[Entity.SCOT]: {
		manageEntity: 'Gestionar los SCOTs',
		searchEntity: 'Buscar SCOTs',
		createEntity: 'Nuevo SCOT',
		entityName: 'SCOT',
		entityNameCaps: 'SCOTs',
		entityNamePlural: 'SCOTs',
		placeholderText: 'Cree nuevos SCOT o edite y elimine los SCOT existentes a continuación.',
		deleteEntity: 'Eliminar SCOT',
		deleteConfirmLabel: '¿Estás seguro de que quieres eliminar este SCOT? Se eliminarán todas las relaciones existentes. Esta acción no se puede deshacer.'
	},
	[Entity.SampleItem]: {
		manageEntity: 'Manage sample tags',
		searchEntity: 'Buscar etiquetas',
		createEntity: 'Nueva etiqueta',
		createManageTagEntity: 'Administrar grupos de etiquetas',
		entityName: 'Etiqueta de muestra',
		entityNamePlural: 'Etiquetas',
		entityNameForTagGroupPlural: 'Grupo de etiquetas',
		placeholderText: "Cree nuevas etiquetas o edite y elimine las etiquetas existentes a continuación. Si necesita crear nuevos grupos de etiquetas, haga clic en <b>'Administrar grupos de etiquetas'</b>",
		deleteEntity: 'Eliminar etiqueta de muestra',
		deleteConfirmLabel: '¿Está seguro de que desea eliminar la etiqueta seleccionada? Se eliminará de todas las muestras con las que esté relacionado. Esta acción no se puede deshacer.'
	},
	[Entity.SampleTagGroups]: {
		manageEntity: 'Administrar grupos de etiquetas de muestra',
		searchEntity: 'Search tag groups',
		createEntity: 'Nuevo grupo de etiquetas',
		entityName: 'sample tag group',
		entityNameCaps: 'Grupo de etiquetas',
		entityNamePlural: 'Grupos de etiquetas',
		placeholderText: 'Cree nuevos grupos de etiquetas o edite y elimine los grupos de etiquetas de muestra existentes a continuación.',
		deleteConfirmLabel: '¿Está seguro de que desea eliminar la etiqueta seleccionada? Se eliminará de todas las muestras con las que esté relacionado. Esta acción no se puede deshacer.'
	},
};

export const inherentRiskFactorTypes = [{
	id: 1,
	label: 'Complejidad',
	displayOrder: 1
},
{
	id: 2,
	label: 'Incertidumbre subjetiva',
	displayOrder: 2
},
{
	id: 3,
	label: 'Fraude o error',
	displayOrder: 3
},
{
	id: 4,
	label: 'Cambio',
	displayOrder: 4
},
{
	id: 5,
	label: 'Naturaleza de la cuenta',
	displayOrder: 5
},
{
	id: 6,
	label: 'Partes relacionadas',
	displayOrder: 6
}
];

export const executionType = [{
	id: 1,
	label: 'PT',
	toolTip: 'Procedimientos en este compromiso [Solo-PT]',
	value: 'En este compromiso [Solo PT]'
},
{
	id: 2,
	label: 'CT',
	toolTip: 'Procedimientos en otro(s) compromiso(s) [Solo CT]',
	value: 'En otro(s) compromiso(s) [Solo CT]'
},
{
	id: 3,
	label: 'PT/CT',
	toolTip: 'Procedimientos en este y otro(s) compromiso(s) [PT/CT]',
	value: 'En este y otros compromiso(s) [PT/CT]'
}
];

export const createEditAccountModalLabels = {
	createModalDescription: "Ingrese los detalles de la nueva cuenta a continuación y seleccione'<b>{0}</b>' para finalizar. Para crear otra cuenta, seleccione'<b>{1}</b>'.",
	editModalDescription: "Edita los detalles de la cuenta a continuación y selecciona'<b>{0}</b>' para terminar.",
	close: 'Cerrar',
	cancel: 'Cancelar',
	createAccount: 'Nueva cuenta',
	editAccount: 'Editar cuenta',
	newSignificantDisclosure: 'Nueva revelación significativa',
	save: 'Guardar',
	confirm: 'Confirmar',
	saveAndCloseLabel: 'Guardar y cerrar',
	saveAndCreateLabel: 'Guardar y crear otra',
	accountNameLabel: 'Nombre de la cuenta (obligatorio)',
	accountDesignationLabel: 'Designación',
	accountExecutionTypeLabel: '¿En qué participación de Canvas se realizarán y documentarán los procedimientos para esta cuenta?',
	accountEstimateLabel: '¿Esta cuenta se ve afectada por una estimación?',
	yes: 'Sí',
	no: 'No',
	accountStatementTypeLabel: 'Tipo de declaración',
	pspIndexDropdownLabel: 'Índice PSP (obligatorio, seleccione hasta 5)',
	removePSPIndexLabel: 'Eliminar índice PSP',
	assertionsLabel: 'Seleccionar aseveraciones relevantes',
	accountTypeOptions: AccountType,
	assertionOptions: assertions,
	executionTypeOptions: executionType,
	statementTypeOptions: statementTypes,
	noOptionsMessage: 'No se han encontrado resultados',
	accountNameErrorMsg: 'Obligatorio',
	pspIndexErrorMsg: 'Obligatorio',
	assertionWarningMessage: 'No puede realizar cambios en las aseveraciones que tengan un riesgo significativo, o riesgo de fraude, o riesgo de error material o una estimación relacionada. Primero debe eliminar estas relaciones.',
	confirmChanges: 'Confirmar cambios',
	executionTypeWarningMessage: 'Los cambios que vayas a guardar en esta cuenta afectarán a las aseveraciones y PSP existentes, los enlaces se eliminarán. ¿Estás seguro de que quieres continuar? Esta acción no se puede deshacer.',
	contentUpdateToastMessage: 'La actualización de contenido está disponible para {0}. Inicie la actualización de contenido desde la página de actualización de contenido.',
	assertionsRequired: 'Se debe seleccionar al menos una aserción',
	pspIndexDisabledLabel: 'Seleccione hasta cinco índices de PSP. Anule la selección de una o más opciones para continuar.'
};

export const createEditRiskModalLabels = {
	createModalDescription: "Ingrese los nuevos detalles de riesgo a continuación y seleccione'<b>{0}</b>' para finalizar. Para crear otro riesgo, seleccione'<b>{1}</b>'.",
	editModalDescription: "Edite los detalles de riesgo a continuación y seleccione'<b>{0}</b>' para finalizar.",
	close: 'Cerrar',
	cancel: 'Cancelar',
	createRisk: 'Nuevo riesgo',
	editRisk: 'Riesgo de edición',
	riskType: 'Tipo de riesgo (obligatorio)',
	riskTypeOptions: [{
		value: 1,
		label: 'Riesgo significativo'
	},
	{
		value: 2,
		label: 'Riesgo de fraude'
	},
	{
		value: 3,
		label: 'Riesgo de error material'
	}
	],
	save: 'Guardar',
	saveAndCloseLabel: 'Guardar y cerrar',
	saveAndCreateLabel: 'Guardar y crear otro',
	riskNameLabel: 'Nombre del riesgo (obligatorio)',
	relatedAccountsAssertionsLabel: 'Cuentas y aseveraciones relacionadas (opcional)',
	relateAccounts: 'Relacionar cuentas',
	assertionsLabel: 'Seleccionar aseveraciones relevantes',
	riskNameErrorMsg: 'Obligatorio',
	riskTypeErrorMsg: 'Obligatorio',
	assertionOptions: assertions,
	removeAccountLabel: 'Eliminar cuenta',
	required: 'Obligatorio',
	assertionsRequired: 'Se debe seleccionar al menos una aserción'
};

export const CreateEditMestLabels = {
	createModalTitle: 'Nueva entidad',
	createModalDescription: "Ingrese los detalles de la nueva entidad a continuación y seleccione'<b>{0}</b>' para finalizar. Para crear otra entidad, seleccione'<b>{1}</b>'.",
	close: 'Cerrar',
	cancel: 'Cancelar',
	save: 'Guardar',
	confirm: 'Confirm',
	primary: 'Primary',
	saveAndCloseLabel: 'Guardar y cerrar',
	saveAndCreateLabel: 'Guardar y crear otro',
	entityNameLabel: 'Nombre de la entidad (obligatorio)',
	entityStandardIndexLabel: 'Índice estándar de entidad (obligatorio)',
	entityDescriptionLabel: 'Descripción de la Entidad',
	entityNameErrorMsg: 'Obligatorio',
	entityStandardIndexErrorMsg: 'Obligatorio',
	editModalTitle: 'Editar entidad',
	editModalDescription: "Edite los detalles de la entidad a continuación y seleccione'<b>{0}</b>' para finalizar.",
	primaryEntitySelectionLabel: 'Select as the primary entity',
	primaryEntitySelectionMsg: "Only one entity in the engagement can be selected as the primary entity, which will be the determinant for the content delivered to the engagement. An entity will need to be selected as the primary to be able to submit the engagement profile. \'Update content\' permission is required to make or edit the primary entity selection.",
	primaryEntityDisableSelectionLabel: "To change the primary entity designation, select from the \'Edit\' of the entity you wish to designate as primary",
	noAccessLabel: 'Unauthorized. Contact your administrator and try again.',
	primaryEntityConfirmationLabel: 'Primary entity confirmation',
	primaryEntityConfirmationDisplay: '{0} is currently selected as the primary entity. Are you sure you want to change the primary entity?',
	profileV2ChangeNotSubmittedBannerMessage: 'Changes have been made to the profile that will result in content updates. Submit the profile to receive the new content or revert the answers to the previous state.',
};

export const CreateEditITProcessLabels = {
	close: 'Cerrar',
	cancel: 'Cancelar',
	yes: 'Sí',
	no: 'No',
	delete: 'borrar',
	save: 'Guardar',
	saveAndCloseLabel: 'Guardar y cerrar',
	saveAndCreateLabel: 'Guardar y crear otra',
	newITProcessLabel: 'Nuevo proceso de TI',
	editITProcessLabel: 'Editar proceso de TI',
	viewITProcessLabel: 'Ver el proceso de TI',
	addDescriptionLabel: "Introduzca los detalles del nuevo proceso de TI a continuación y seleccione'<b>{0}</b>' para finalizar. Para crear otro proceso informático, seleccione'<b>{1}</b>'.",
	editDescriptionLabel: "Edite los detalles del proceso de TI a continuación y seleccione'<b>{0}</b>' para finalizar.",
	iTProcessNameLabel: 'Nombre del proceso de TI (obligatorio)',
	confirm: 'Confirmar',
	confirmChanges: 'Confirmar',
	iTProcessNameErrorMsg: 'Obligatorio',
	inputInvaildCharacters: 'La entrada no puede incluir la siguiente cadena de caracteres: */:<>\\?|"',
	remove: 'Eliminar'
};

export const CreateEditITRiskLabels = {
	close: 'Cerrar',
	cancel: 'Cancelar',
	yes: 'Sí',
	no: 'No',
	delete: 'borrar',
	save: 'Salvar',
	saveAndCloseLabel: 'Guardar y cerrar',
	saveAndCreateLabel: 'Guardar y crear otro',
	newITRiskLabel: 'Nuevo riesgo tecnológico',
	editITRiskLabel: 'Editar riesgo tecnológico',
	itRiskNameLabel: 'Riesgo tecnológico (obligatorio)',
	confirm: 'Confirmar',
	confirmChanges: 'Confirmar',
	itRiskNameErrorMsg: 'Obligatorio',
	itProcessNotSelectedErrorMsg: 'Obligatorio',
	hasNoITGCLabel: 'No hay ITGC que aborden el riesgo tecnológico',
	editModalDescription: 'Edite la descripción del riesgo tecnológico.',
	createModalDescription: 'Introduzca la descripción del riesgo tecnológico.',
	selectITProcess: 'Seleccione el proceso de TI (obligatorio)',
	noITProcessAvailable: 'No se han creado procesos de TI',
	relatedITProcessLabel: 'Proceso de TI relacionado',
	inputInvaildCharacters: 'La entrada no puede incluir la siguiente cadena de caracteres: */:<>\\?|"',
	remove: 'Remover'
};

export const CreateEditEstimateLabels = {
	createModalDescription: "Ingrese los nuevos detalles del estimado a continuación y seleccione'<b>{0}</b>' para finalizar. Para crear otro estimado, seleccione'<b>{1}</b>'.",
	editModalDescription: "Edite los detalles de estimación a continuación y seleccione'<b>{0}</b>' para finalizar.",
	close: 'Cerrar',
	cancel: 'Cancelar',
	save: 'Guardar',
	saveAndCloseLabel: 'Guardar y cerrar',
	saveAndCreateLabel: 'Guardar y crear otro',
	createModalTitle: 'Nueva estimación',
	editEstimateLabel: 'Editar estimación',
	estimateNameLabel: 'Nombre de la estimación (obligatorio)',
	analysisPeriodBalance: 'Balance de la fecha de análisis (obligatorio)',
	analysisPeriodDate: 'Fecha de análisis (obligatorio)',
	comparativePeriodBalance: 'Saldo en fechas comparativas (obligatorio)',
	comparativePeriodDate: 'Fecha comparativa (obligatorio)',
	estimateCategory: 'Categoría de estimación (obligatorio)',
	confirm: 'Confirmar',
	estimateNameErrorMsg: 'Obligatorio',
	analysisPeriodBalanceErrorMsg: 'Obligatorio',
	analysisPeriodDateErrorMsg: 'Obligatorio',
	comparativePeriodBalanceErrorMsg: 'Obligatorio',
	comparativePeriodDateErrorMsg: 'Obligatorio',
	estimateCategoryErrorMsg: 'Obligatorio',
	remove: 'Eliminar',
	balanceNOTApplicable: 'Saldo no aplicable',
	wtDetailPrefixForEstimate: '<p>Complete the estimate walkthrough task.</p>',

	riskLevelOptions: [{
		value: 4,
		label: 'Riesgo muy bajo'
	},
	{
		value: 5,
		label: 'Menor riesgo'
	},
	{
		value: 6,
		label: 'Mayor riesgo'
	},
	{
		value: 7,
		label: 'No seleccionado'
	}
	]
};

export const CreateEditControlLabels = {
	createModalTitle: 'Nuevo control',
	editModalTitle: 'Editar control',
	viewModalTitle: 'Vista por controles',
	close: 'Cerrar',
	cancel: 'Cancelar',
	save: 'Guardar',
	saveAndCloseLabel: 'Guardar y cerrar',
	saveAndCreateLabel: 'Guardar y crear otro',
	controlNameLabel: 'Nombre del control (obligatorio)',
	frequency: 'Frecuencia',
	controlType: 'Tipo de Control',
	frequencyTypeOptions: controlFrequencyType,
	controlTypeOptions: controlTypes,
	designEffectiveness: 'Efectividad del Diseño',
	operatingEffectiveness: 'Efectividad Operativa',
	testingLabel: 'Pruebas',
	lowerRiskLabel: 'Is control lower risk?',
	effective: 'Efectivo',
	ineffective: 'Inefectivo',
	yes: 'Sí',
	no: 'No',
	required: 'Requerido',
	remove: 'Remover',
	noOptionsMessage: 'No se encontraron resultados',
	disabledTabTooltipMessage: "Seleccione'Tipo de control' como'Control de aplicación de TI' o'Control manual dependiente de TI' para relacionar aplicaciones de TI",
	itAppLabels: {
		tabLabel: 'Aplicaciones IT',
		dropdownLabel: 'Relacionar aplicaciones IT',
		noRelatedItems: 'No hay aplicaciones de IT relacionadas',
		itApplications: 'Aplicaciones informáticas'
	},
	soLabels: {
		tabLabel: 'Sos',
		dropdownLabel: 'Relacionar organizaciones de servicios',
		noRelatedItems: 'No hay organizaciones de servicios relacionadas',
		serviceOrganizations: 'Organizaciones de servicios'
	},
	controlNameErrorMsg: 'Obligatorio',
	createModalDescriptionLabel: "Ingrese los nuevos detalles de control a continuación y seleccione'<b>{0}</b>' para finalizar. Para crear otro control, seleccione'<b>{1}</b>'.",
	editModalDescriptionLabel: "Edite los detalles del control a continuación y seleccione'<b>{0}</b>' para finalizar.",
	viewModalDescriptionLabel: 'Vea el control y las aplicaciones de TI relacionadas y las organizaciones de servicios.',
	wcgwLabel: 'WCGW'
};

export const ITApplicationTypeLabels = [{
	value: 1,
	label: 'Aplicación/Herramienta'
},
{
	value: 2,
	label: 'Base de datos'
},
{
	value: 3,
	label: 'Sistema Operativo'
},
{
	value: 4,
	label: 'Red'
},
{
	value: 6,
	label: 'Sin categoría'
}
];

export const CreateEditITApplicationLabels = {
	close: 'Cerrar',
	cancel: 'Cancelar',
	yes: 'Sí',
	no: 'No',
	delete: 'Borrar',
	save: 'Guardar',
	saveAndCloseLabel: 'Guardar y cerrar',
	saveAndCreateLabel: 'Guardar y crear otro',
	newITApplicationLabel: 'Nueva aplicación de IT',
	editITApplicationLabel: 'Editar aplicación de IT',
	iTApplicationNameLabel: 'Nombre de aplicación de IT',
	confirm: 'Confirmar',
	confirmChanges: 'Confirmar cambios',
	noOptionsMessage: 'No se han encontrado resultados',
	iTAppNameErrorMsg: 'Obligatorio',
	controls: 'Controles',
	substantive: 'Sustantivo',
	remove: 'Eliminar',
	iTApplicationStrategyLabel: 'Estrategia de aplicaciones de IT',
	SCOTsLabel: 'Nombres de SCOTs',
	StrategyLabel: 'Estrategia',
	ControlsLabel: 'Controles',
	ControlTypeLabel: 'Tipo',
	addDescriptionLabel: "Introduzca los detalles de la nueva aplicación de IT a continuación y seleccione'<b>{0}</b>' para finalizar. Para crear otra aplicación de IT, seleccione'<b>{1}</b>'.",
	editDescriptionLabel: "Edite los detalles de la aplicación de IT a continuación y seleccione'<b>{0}</b>' para finalizar.",
	scotErrorMessage: 'El SCOT no puede ser ajeno a la aplicación de IT, ya que existen controles relacionados.',
	SCOTsLabels: {
		tabLabel: 'SCOTs',
		dropdownLabel: 'Relacionar SCOTs',
		noRelatedItems: 'No hay SCOTs relacionados'
	},
	ControlsLabels: {
		tabLabel: 'Controles',
		dropdownLabel: 'Controles de relación',
		noRelatedItems: 'No hay controles relacionados'
	},
	strategyType: {
		1: 'Controles',
		2: 'Sustantivo',
		3: 'Confiar',
		4: 'No confiar'
	},
	controlType: {
		1: 'Aplicación de TI',
		2: 'Manual dependiente de TI',
		3: 'Prevención manual',
		4: 'Detección manual'
	},
	technologyTypeOptions: ITApplicationTypeLabels,
	technologyType: 'Select technology type'
};

export const CreateEditSCOTLabels = {
	createModalTitle: 'Nuevo SCOT',
	editModalTitle: 'Editar SCOT',
	viewModalTitle: 'Ver SCOT',
	createModalDescription: "Ingrese los nuevos detalles de SCOT a continuación y seleccione'<b>{0}</b>' para finalizar. Para crear otro SCOT, seleccione'<b>{1}</b>'.",
	editModalDescription: "Edite los detalles de SCOT a continuación y seleccione'<b>{0}</b>' para finalizar.",
	close: 'Cerrar',
	cancel: 'Cancelar',
	save: 'Guardar',
	saveAndCloseLabel: 'Guardar y cerrar',
	saveAndCreateLabel: 'Guardar y crear otro',
	scotNameLabel: 'Nombre SCOT (obligatorio)',
	scotStrategyLabel: 'Estrategia SCOT',
	scotTypeLabel: 'Tipo SCOT',
	hasEstimateLabel: '¿Este SCOT se ve afectado por una estimación?',
	itAPPUsedLabel: '¿Se utiliza alguna aplicación de TI?',
	routine: 'Rutina',
	nonRoutine: 'No rutinario',
	controls: 'Controles',
	substantive: 'Sustantivo',
	yes: 'Sí',
	scotNameErrorMsg: 'Obligatorio',
	remove: 'Eliminar',
	noOptionsMessage: 'No se han encontrado resultados',
	disabledTabTooltipMessage: "Seleccione'¿Se utiliza alguna aplicación de TI?' para relacionar las aplicaciones de TI",
	itAppLabels: {
		itApplications: 'Aplicaciones de TI relacionadas',
		tabLabel: 'Aplicaciones de TI',
		dropdownLabel: 'Relacionar aplicaciones de TI',
		noRelatedItems: 'No hay aplicaciones de TI relacionadas'
	},
	soLabels: {
		serviceOrganizations: 'Organizaciones de servicios relacionados',
		tabLabel: 'OSs',
		dropdownLabel: 'Relacionar organizaciones de servicios',
		noRelatedItems: 'No hay organizaciones de servicios relacionada'
	},
	wtDetailPrefix: '<p>Para todos los SCOT rutinarios y no rutinarios y los procesos de divulgación significativos, confirmamos nuestra comprensión en cada período mediante la realización de procedimientos de recorrido. Además, para las auditorías PCAOB, realizamos procedimientos de estimación de SCOTs.<br/>Para todos los SCOTs cuando adoptamos una estrategia de confianza en los controles, y para los controles que abordan riesgos significativos, confirmamos que los controles relevantes han sido diseñados e implementados adecuadamente. Confirmamos que nuestra decisión de adoptar una estrategia de confianza en los controles sigue siendo apropiada.<br/><br/>Concluimos que nuestra documentación describe con precisión el funcionamiento del SCOT y hemos identificado todos los WCGW apropiados, incluidos los riesgos que surgen del uso de TI, y los controles relevantes (cuando corresponda).<br/><br/> Para la estimación de SCOTs cuando utilizamos una estrategia solo sustantiva,  determinamos si nuestra comprensión de la estimación SCOT es apropiada en función de nuestros procedimientos sustantivos.</p>',
};

export const ViewSampleItemLabels = {
	previous: 'Anterior',
	next: 'Siguiente',
	sampleDateLabel: 'Fecha de la muestra',
	attributesHeader: 'Atributos',
	statusHeader: 'Estado',
	noAttributesLabel: 'No hay atributos disponibles.',
	present: 'Presente',
	presentWithComments: 'Presentar con comentarios',
	notPresent: 'No está presente',
	notApplicatable: 'No aplicable',
	naLabel: 'N/A',
	additionDocumentation: 'Documentación adicional',
	deleteSampleHeader: 'Eliminar muestra',
	deleteSmapleDescription: '¿Está seguro de que desea eliminar la muestra seleccionada? Esta acción no se puede deshacer.',
	deleteSamplePreText: 'Descripción de la muestra',
	relateTagModalTitle: 'Relacionar etiquetas con la muestra',
	relateTagModalDescription: "Relacione una o más etiquetas con la muestra. Para agregar una nueva etiqueta, haga clic en <b>'Administrar etiquetas'</b>. Las asociaciones de etiquetas no se archivarán, pero las propias etiquetas se archivarán para que estén disponibles para su uso en la puesta al día.",
	relateTagTableHeader: 'Nombre de la etiqueta',
	relateTagTableSubHeader: 'Grupo de etiquetas',
	tagsCounter: 'Etiquetas {0}',
	tagCounter: 'Etiqueta {0}',
	relateTagGroupLabel: 'Grupo de etiquetas',
	relateTagSearchPlaceholder: 'Buscar',
	relateTagClearSearch: 'Limpiar',
	relateTagShowSelectedOnly: 'Mostrar solo elementos relacionados',
	manageTagsLabel: 'Administrar etiquetas',
	addTag: 'Agregar etiqueta',
	supportingDocumentationTitle: 'Documentación de soporte',
	dropdownAll: 'Todos',
	noResultsLabel: 'No se han encontrado resultados',
	noDataLabel: 'No se han encontrado datos',
	attributeStatusModalTitle: 'Mark all as present',
	attributeStatusModalCancelButton: 'Cancelar',
	attributeStatusModalConfirmButton: 'Guardar',
	attributeStatusModalDescription: '¿Está seguro de que desea marcar los atributos como presentes? Solo los atributos que no tengan ningún estado seleccionado se marcarán como presentes.',
	attributeModalDeleteErrorMessage: 'El estado del atributo no se puede actualizar. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Soporte si el error persiste.',
};

export const ShortRiskTypeForAres = {
	1: 'Significativo',
	2: 'Fraude',
	3: 'Inherente',
	4: 'Riesgo muy bajo',
	5: 'Menor riesgo',
	6: 'Mayor riesgo',
	7: 'No seleccionado'
};

export const RelateEstimateToAssertionLabels = {
	relateAccountsAndAssertions: 'Relacionar cuentas y aseveraciones',
	relateAccountsToEstimate: 'Relacionar las cuentas con la estimación',
	accounts: 'Cuentas',
	designation: 'Designación',
	relatedAssertions: 'Aseveraciones relacionadas',
	accountNameField: 'accountName',
	accountTypeIdField: 'accountTypeId',
	assertionsField: 'Aseveraciones',
	executionTypeIdField: 'executionTypeId',
	notSelected: 'No seleccionado',
	pathField: 'ruta',
	noAccountsAvailable: 'No hay cuentas disponibles',
	noRelatedAccounts: 'No hay cuentas relacionadas con la estimación',
	accountId: 'accountId',
	remove: 'Remover'
};

//Send instructions switcher
export const sendIntructionsSwitcherLabels = {

	[sendInstructionsSwitcherIds.groupInstructions]: 'Instrucciones de grupo',
	[sendInstructionsSwitcherIds.groupRiskAssessment]: 'Evaluación de riesgos del grupo'
};

export const RelateEstimateToAccountLabels = {
	relatEstimatesToAccount: 'Relacionar las estimaciones con la cuenta',
	showOnlyRelatedEstimates: 'Mostrar solo estimaciones relacionadas',
	noEstimatesResult: labels.noResultsFound,
	noEstimatesLabel: 'No se han creado estimaciones en el compromiso',
	estimateNameHeader: 'Nombre de la estimación',
	relatedEstimateCounter: '{0} estimación',
	relatedEstimatesCounter: '{0} estimaciones',
	relatedAccount: 'Cuenta/divulgación',
	close: labels.close
};

export const RelateAccountsToEstimateLabels = {
	relateAccountsToEstimate: 'Relacionar  cuentas con la estimación',
	showOnlyRelatedAccounts: 'Mostrar solo cuentas relacionadas',
	noAccountsResult: labels.noResultsFound,
	noAccountsLabel: 'No se han creado cuentas en el compromiso',
	accountNameHeader: 'Nombre de la cuenta',
	relatedAccountCounter: '{0} cuenta',
	relatedAccountsCounter: '{0} cuentas',
	relatedEstimate: labels.estimate,
	close: labels.close
};

export const SupportingDocumentationLabels = {
	evidence: 'Evidencia',
	priorPeriod: 'Periodo anterior',
	temporaryFiles: 'Archivos temporales',
	externalDocuments: 'Documentos externos',
	addEvidenceBtn: 'Añadir evidencias',
	addTemporaryFilesBtn: 'Agregar archivo temporal',
	notes: 'Notas',
	signOffs: 'Firmas',
	name: 'Nombre',
	supportingDocumentationTitle: 'Documentación de soporte',
	temporaryFilesEmptyPlaceholder1: 'No hay documentos temporales relacionados.',
	temporaryFilesEmptyPlaceholder2: 'Para relacionar un documento temporal, haga clic en {addTemporaryFiles}.',
	evidencePlaceholderLine1: 'No hay evidencia relacionada',
	evidencePlaceholderLine2: 'Para relacionar una evidencia, haga clic en {addEvidenceBtn}.',
	removeFromSample: 'Retirar de la muestra',
	unlink: 'Desvincular',
	retailControlEvidenceLabel: '¿Se conservaron pruebas de control para respaldar nuestras pruebas de los atributos de este elemento de muestra específico?',
	removeEvidenceModalTitle: 'Eliminar evidencia de la muestra',
	removeEvidenceModalDesc: '¿Está seguro de que desea eliminar todas las pruebas de esta muestra?',
	removeEvidenceErrorMessage: 'Estos documentos ya no están disponibles en este ejemplo. Actualice la página y vuelva a intentarlo. Póngase en contacto con el Servicio de Soporte si el error persiste.'
}

export const deleteSampleItemAttributeModal = {
	modalDescription: '¿Está seguro de que desea eliminar la selección de este atributo? Se eliminará la documentación adicional.',
	modalTitle: 'Eliminar selección',
	modalConfirmButton: 'Eliminar',
	modalCancelButton: 'Cancelar',
	additionalDocumentationLabel: 'Documentación adicional'
}
export const accountsFilterLabels = [{
	id: accountsFilter.allAccounts,
	label: 'Todas las cuentas',
	value: accountsFilter.allAccounts
},
{
	id: accountsFilter.accountsWithRelatedEstimates,
	label: 'Cuentas con estimación relacionada',
	value: accountsFilter.accountsWithRelatedEstimates
},
{
	id: accountsFilter.accountsWithoutRelatedEstimates,
	label: 'Cuentas sin estimación relacionada',
	value: accountsFilter.accountsWithoutRelatedEstimates
}
];

export const changeSampleItemAttributeModal = {
	modalDescription: '¿Está seguro de que desea cambiar la selección de este atributo? Se eliminará la documentación adicional.',
	modalTitle: 'Cambiar selección',
	modalConfirmButton: 'Cambiar',
	modalCancelButton: 'Cancelar'
}
export const scotsFilterLabels = [{
	id: scotsFilter.allScots,
	label: 'Todos los SCOTs',
	value: scotsFilter.allScots
},
{
	id: scotsFilter.scotsWithRelatedEstimates,
	label: 'SCOTs con estimación relacionada',
	value: scotsFilter.scotsWithRelatedEstimates
},
{
	id: scotsFilter.scotsWithoutRelatedEstimates,
	label: 'SCOTs sin estimación relacionada',
	value: scotsFilter.scotsWithoutRelatedEstimates
}
];

export const CreateEditTagGroupLabels = {
	createModalTitle: 'Nuevo grupo de etiquetas de muestra',
	editModalTitle: 'Editar grupo de etiquetas de muestra',
	createModalDescription: "Ingrese los detalles del grupo de etiquetas a continuación y seleccione <b>\'Guardar y cerrar\'</b> para finalizar. Para crear otro grupo de etiquetas, seleccione <b>\'Guardar y crear otro\'</b>.",
	editModalDescription: "Edite los detalles del grupo de etiquetas a continuación y seleccione'<b>{0}</b>' para finalizar.",
	close: 'Cerrar',
	cancel: 'Cancelar',
	save: 'Guardar',
	saveAndCloseLabel: 'Guardar y cerrar',
	saveAndCreateLabel: 'Guardar y crear otro',
	tagGroupNameLabel: 'Nombre del grupo de etiquetas (obligatorio)',
	required: 'Obligatorio'
};

export const CreateEditTagLabels = {
	createModalTitle: 'Nueva etiqueta de muestra',
	editModalTitle: 'Edit sample tag',
	createModalDescription: "Ingrese los detalles de la etiqueta a continuación y seleccione'<b>Guardar y cerrar</b>' para finalizar. Para crear otra etiqueta, seleccione'<b>Guardar y crear otra</b>'.",
	editModalDescription: `Edit the tag details below and select'<b>{0}</b>' to finish.`,
	tagNameLabel: 'Nombre de la etiqueta (obligatorio)',
	tagGroupNameLabel: 'Nombre del grupo de etiquetas (obligatorio)',
	tagColorLabel: 'Color (obligatorio)',
	saveAndCloseLabel: 'Guardar y cerrar',
	saveAndCreateLabel: 'Guardar y crear otro',
	cancelLabel: 'Cancelar',
	required: 'Obligatorio',
	save: 'Guardar',
	noresultsLabel: 'No hay grupos de etiquetas disponibles',
	tagColors: [{
		text: 'Rojo',
		color: 'rojo'
	},
	{
		text: 'Naranja',
		color: 'naranja'
	},
	{
		text: 'Cerceta',
		color: 'cerceta'
	},
	{
		text: 'Azul',
		color: 'azul'
	},
	{
		text: 'Morado',
		color: 'morado'
	},
	{
		text: 'Verde',
		color: 'verde'
	}
	]
};

export const ITProcessFlowLabels = {
	itProcess: 'Proceso de TI',
	technology: 'Tecnología',
	technologies: 'Tecnologías',
	technologyName: 'Nombre de la Tecnología',
	supportingTechnologyName: 'Nombre de la Tecnología de apoyo',
	technologyType: 'Tipo de Tecnología',
	showOnlyRelated: 'Mostrar solo relacionados',
	technologiesCounter: '{0} Tecnologías',
	technologyCounter: 'Tecnología {0}',
	supportingTechnologyLabel: 'Tecnología de apoyo',
	relatedITAppNoDataPlaceholder: 'Ninguna tecnología relacionada con el proceso de TI',
	relateTechnology: 'Relacionar la Tecnología',
	supportingITPAppNoDataPlaceholder: 'No hay Tecnología que soporte el proceso de TI',
	itRiskHeader: 'Riesgos IT',
	itgcHeader: "ITGC's",
	itspHeader: "ITSP's",
	noDataPlaceholderITGC: 'Se debe identificar al menos un ITGC o una designación que indique que el riesgo de TI no tiene ITGC. Cree {createNewITGC}, {relateITGC} o indique que hay {noITGC} que abordan el riesgo de TI.',
	noDataPlaceholderITSP: 'Si hemos evaluado los ITGC como ineficaces o hemos determinado que no existen ITGC para abordar el riesgo de TI, es posible que podamos realizar procedimientos de prueba sustantivos de TI (ITSP) para obtener una garantía razonable de que el riesgo de TI dentro del proceso de TI asociado con el ITGC ineficaz no fue explotado. Cree {createNewITSP} o {relateITSP}.',
	noRecordsFound: 'No se han identificado riesgos de TI para este proceso de TI',
	noITGCPlaceholder: 'No hay ITGC que aborden el riesgo de TI.',
	relateSupportingTechnology: 'Relacionar la tecnología de apoyo',
	relatedTechnologiesNotAvailable: 'Tecnologías relacionadas no disponibles para este documento',
	supportingTechNotAvailable: 'Tecnologías de soporte no disponibles para este documento',
	relatedITRisksNotAvailable: 'Riesgos de TI relacionados no disponibles para este documento',
	relateITGC: 'Relate ITGCs',
	itRisk: 'Riesgo de IT',
	itgcCounter: '{0} ITGC',
	itgcsCounter: '{0} ITGC',
	itgcName: 'Nombre de ITGC',

};

export const ITRisksFlowLabels = {
	itRisk: 'Riesgo Informático',
	relatedITRiskNoDataPlaceholder: 'No hay riesgos informáticos relacionados con el proceso informático',
	newITRisk: 'Nuevo riesgo informático',
	relatedITRisksNotAvailable: 'Riesgos de TI relacionados no disponibles para este documento',
	deleteConfirmLabel: '¿Está seguro de que desea eliminar el riesgo de TI seleccionado? Esta acción no se puede deshacer.',
	deleteITRisk: 'Eliminar el Riesgo de IT',
	CreateITFlowITRiskLabels: {
		close: 'Cerrar',
		cancel: 'Cancelar',
		yes: 'Sí',
		no: 'No',
		delete: 'borrar',
		save: 'Guardar',
		saveAndCloseLabel: 'Guardar y cerrar',
		saveAndCreateLabel: 'Guarde y crear otro',
		newITRiskLabel: 'Nuevo riesgo informático',
		itRiskNameLabel: 'Nombre del riesgo de TI (obligatorio)',
		itRiskCharactersLabel: 'Caracteres',
		itRiskOfLabel: 'de',
		confirm: 'Confirmar',
		confirmChanges: 'Confirmar',
		itRiskNameErrorMsg: 'Obligatorio',
		itProcessNotSelectedErrorMsg: 'Obligatorio',
		hasNoITGCLabel: 'No hay ITGC que aborden el riesgo de TI',
		createModalDescription: "Introduzca los detalles de riesgo de TI a continuación y seleccione'<b>Guardar y cerrar</b>' para finalizar. Para crear otro riesgo de TI, seleccione'<b>Guardar y crear otro</b>'.",
		relatedITProcessLabel: 'Proceso de TI',
		inputInvaildCharacters: 'La entrada no puede incluir la siguiente cadena de caracteres: */:<>\\?|"',
		remove: 'Remove',
		editModalDescription: "Edite los detalles de Riesgo de IT a continuación y seleccione'<b>Guardar</b>' para finalizar.",
		editITRiskLabel: 'Edit IT risk'
	}
};

export const ITProcessListingLabels = {
	all: 'Todo',
	manageChange: 'Gestionar el cambio',
	manageAccess: 'Administrar el acceso',
	manageSecuritySettings: 'Administrar la configuración de seguridad',
	itOperations: 'Operaciones de TI',
	systemImplementation: 'Implementación del Sistema',
	category: 'Categoría',
	uncategorized: 'Sin categorizar',
	technologies: 'Tecnologías',
};

export const ITProcessQuickFilterOptions = {
	0: ITProcessListingLabels.all,
	1: ITProcessListingLabels.manageChange,
	2: ITProcessListingLabels.manageAccess,
	3: ITProcessListingLabels.manageSecuritySettings,
	4: ITProcessListingLabels.itOperations,
	5: ITProcessListingLabels.systemImplementation
}

// Relate Technology Modal
export const RelateTechnologyModalLabels = {
	relateTechnology: 'Relacionar tecnología',
	relatedTechnologiesDescription: 'Nombre de la tecnología',
	supportingTechnologyName: 'Nombre de la tecnología de apoyo',
	technology: '{0} technology',
	technologies: '{0} technologies'
};

export const AccountStandardROMMListingLabels = {
	accountRisksNotAvailableForDocument: 'Los riesgos de la cuenta no están disponibles para este documento.',
	noRelatedObject: 'No hay ningún objeto relacionado. Relaciona un objeto para empezar.',
	noResultsFound: 'No hay riesgos disponibles.',
	acceptedText: 'Aceptado',
	rejectedText: 'Rechazada',
	allRisksRejected: 'Todos los riesgos han sido rechazados',
	relevantAssertions: 'Aseveraciones relevantes',
	rejectLabel: 'Rechazar',
	acceptLabel: 'Aceptar',
	rejectionRationaleLabel: 'Justificación del rechazo',
	rejectionCategoryText: 'Categoría de rechazo',
	editRejectionRationaleText: 'Justificación del rechazo de edición',
	rejectionRationalePlaceholder: "Are you sure you want to reject the selected risk? Enter the details below and select <strong>\'Reject\'</strong>.",
	cancel: 'Cancel',
	rejectionRationaleTextAreaPlaceholder: 'Rationale (required)',
	rejectionCategoryDropdownPlaceholder: 'Rejection category (required)',
	required: 'Required',
	preRejectedText: 'Pre-rejected',
	additionalContextLabel: 'Additional context',
	additionalContextwhyRiskShouldBeRejected: 'Click {0} to add additional context specific to this client for why this risk should be rejected.',
	hereLink: 'here',
	editAdditionalContextText: 'Edit additional context'
}

export const RejectionCategory = [{
	id: 1,
	label: 'Probabilidad: No hay posibilidad razonable de que ocurra (sin tener en cuenta los controles)'
},
{
	id: 2,
	label: 'Magnitud: El error potencial es irrelevante'
},
{
	id: 3,
	label: 'El riesgo no es material para este compromiso'
},
{
	id: 4,
	label: 'El ROMM se aborda en otro compromiso (solo Auditorías de Grupo)'
},
]

export const formLabels = {
	required: labels.required,
	maxLength: labels.maxLength
};

export const paginationLabels = {
	show: labels.pagingShowtext,
	first: 'Primera página',
	last: 'Última página',
	prev: 'Página anterior',
	next: 'Siguiente página'
};
