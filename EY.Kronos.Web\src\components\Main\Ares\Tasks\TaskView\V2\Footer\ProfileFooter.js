import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components';
import {
	serviceURL,
	independenceStatus,
	ProfileActionTypes,
	validationTypes,
	ToastMessageVariants
} from '../../../../../../../util/uiconstants';
import { labels } from '../../../../../../../util/utils';
import Env from '../../../../../../../util/env';
import { useEngagement, useUserPreferences } from '../../../../../../../util/customHooks';
import { useAresUrlParameters, useIsDrawerMode } from '../../../../../../../util/Ares/customHooks';
import { clearProfileData, submitProfile } from '../../../../../../../actions/FormData/formdataactions';
import { setIsModalOpen } from '../../../../../../../actions/setismodalopen';
import { createCanvasFormSnapshot } from '../../../../../../../actions/Task/signoffsaction';
import { setFormV2ToasterVisibility } from '../../../../../../../actions/toastactions';
import { getLastSectionByDocumentId, getStEntitiesPrimaryEntity } from '../../../../../../../selectors/Ares/selectors';
import { getValidationsForDocumentId } from '../../../../../../../selectors/Forms/canvasFormSelectors';
import Button from '../../../../../../Common/BaseComponentWrappers/MotifButton';
import EllipsesControl from '../../../../../../Common/EllipsesControl/EllipsesControl';
import LoadingIndicator from '../../../../../../Common/LoadingIndicator/LoadingIndicator';

export default function ProfileFooter({ engagementId, document, isLoading, setIsLoading }) {
	const dispatch = useDispatch();

	const toastMessagesFeatureName = 'GuidedWorkflow';

	const { sectionId } = useAresUrlParameters();

	const documentId = document?.id;
	const engagementStatusId = engagement?.engagementStatusId;

	const engagement = useEngagement();
	const userPreferences = useUserPreferences();
	const isDrawerMode = useIsDrawerMode();

	const validations = useSelector((store) => getValidationsForDocumentId(store, documentId));
	const lastFormSection = useSelector((store) => getLastSectionByDocumentId(store, documentId));
	const isLastSection = lastFormSection?.id === sectionId;

	const permissions = userPreferences?.permissions;

	let hasPermission = permissions?.canUpdateEngagementContent;

	const showLink =
		hasPermission &&
		engagement.isProfileComplete === true &&
		engagement.engagementStatusId !== independenceStatus.RESTORED;

	const showSubmitButton = isLastSection;
	const disableSubmitButton = !hasPermission || engagement.engagementStatusId === independenceStatus.RESTORED;

	const returnToDashboard = useCallback(() => {
		const redirectionUrl = `${Env.getURL('hercules_web')}/dashboard?engagementId=${engagementId}`;

		if (isDrawerMode) {
			window.open(redirectionUrl, '_blank');
		} else window.location.href = redirectionUrl;
	}, [engagementId, isDrawerMode]);

	const redirectionAfterSubmitProfile = useCallback(
		(profileActionType) => {
			let redirectionUrl;
			if (profileActionType !== ProfileActionTypes.profileEdit) {
				redirectionUrl = Env.getURL('Zeus_web');
			} else {
				redirectionUrl = `${Env.getURL('hercules_web')}/contentupdates?engagementid=${engagementId}`;
			}

			if (isDrawerMode) {
				window.open(redirectionUrl, '_blank');
			} else window.location.href = redirectionUrl;
		},
		[engagementId, isDrawerMode]
	);

	const onSubmitProfileClick = useCallback(
		(evt) => {
			dispatch(setIsModalOpen(false));

			if (disableSubmitButton) {
				dispatch(
					setFormV2ToasterVisibility(
						true,
						ToastMessageVariants.Error,
						labels.insufficientRightsForProfileV2Submission,
						toastMessagesFeatureName
					)
				);
				return;
			} else {
				if (
					validations?.length > 0 &&
					(validations.some((t) => t.validationTypeId === validationTypes.FinalizeIncompleteCanvasFromResponses) ||
						validations.some((t) => t.validationTypeId === validationTypes.FormContentWithoutHeader) ||
						validations.some((t) => t.validationTypeId === validationTypes.MultiEntityWithoutIndividualProfileForm))
				) {
					dispatch(
						setFormV2ToasterVisibility(
							true,
							ToastMessageVariants.Error,
							labels.submitProfileValidationErrorMessage,
							toastMessagesFeatureName
						)
					);
					return;
				}

				setIsLoading(true);

				dispatch(clearProfileData()).then(() => {
					return dispatch(submitProfile(engagementId, documentId))
						.then((profileResponse) => {
							// Snapshot creation
							let snapshotPromise = Promise.resolve();

							if (profileResponse.isContentUpdated) {
								let snapshotRequestURL =
									Env.getBaseURL(serviceURL, engagementId) + 'Forms/' + documentId + '/Snapshots';

								snapshotPromise = dispatch(createCanvasFormSnapshot(snapshotRequestURL, { ApprovalId: 0 })).finally(
									() => {
										setIsLoading(false);
										redirectionAfterSubmitProfile(profileResponse.profileActionType);
									}
								);
							} else {
								dispatch(
									setFormV2ToasterVisibility(
										true,
										ToastMessageVariants.Warning,
										labels.submitIndependenceProfileV2EditMessage,
										toastMessagesFeatureName
									)
								).then(() => setIsLoading(false));
							}
							return snapshotPromise;
						})
						.catch(() => {
							setIsLoading(false);
						});
				});
			}
		},
		[dispatch, engagementId, documentId, hasPermission, engagementStatusId, validations]
	);

	return (
		<StyledProfileFooter className="StyledProfileFooter" showLink={showLink}>
			{isLoading && <LoadingIndicator show fullscreen styleName="LoadingIndicator StyledProfileFooter" />}
			{showLink && (
				<Button className="returnToDashboardLink" variant="text-alt" size="large">
					<EllipsesControl
						content={labels.returnToDashboardFit}
						tooltip={labels.returnToDashboardFit}
						displayId={''}
						noOfLines={1}
						onClick={returnToDashboard}
					/>
				</Button>
			)}
			{showSubmitButton && (
				<Button
					className="submit-profile-btn"
					onClick={onSubmitProfileClick}
					variant="primary"
					showLabel
					buttonLabel={labels.submitProfileFit}
					size="large"
					disabled={disableSubmitButton}
				/>
			)}
		</StyledProfileFooter>
	);
}

const StyledProfileFooter = styled.section`
	display: flex;
	flex-direction: row;
	align-items: center;
	.submit-profile-btn {
		${(props) => (props.showLink ? 'margin-left: var(--px-50);' : null)}
	}
`;
