/* eslint-disable prettier/prettier,max-len */

import {
	Entity,
	GALinkStatus,
	confidentialityTypes,
	currencyType,
	gaRegion,
	gaRoleTypes,
	gaScopeType,
	notesFilter,
	pointOfContactTypes,
	validationTypes,
	KnowledgeSectionIds,
	sendInstructionsSwitcherIds,
	accountsFilter,
	scotsFilter,
	rejectionType
} from '../util/uiconstants';

/**
 * Created by calhosh on 4/14/2017.
 * PT BR resource file
 */
export const labels = {
	addEvidenceBtn: 'Adicionar evidência',
	multipleDocuments: 'Vários documentos',
	invalidEngagementId: 'A ID do engagament é inválida. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	newComponent: 'novo componente',
	workingOffline: 'Trabalhando offline',
	syncInProgress: 'Sincronização em andamento',
	prepareOffline: 'Preparando dados para trabalhar offline',
	connectionAvailable: 'Conexão disponível',
	training: 'Treinamento',
	clickForOptions: 'Clique para mais opções',
	navReviewNotes: 'Notas de revisão',
	navHeaderManageTeam: 'Gerenciar a equipe',
	navManageGroup: 'Gerenciar o grupo',
	manageObjects: 'Gerenciar objetos',
	navCRASummary: 'Resumo do CRA',
	navAuditPlan: 'Plano de auditoria',
	navWorkPlan: 'Plano de trabalho',
	navSEM: 'Matriz de avaliação substantiva (SEM)',
	navFindings: 'Observações',
	navContentUpdates: 'Atualização de conteúdo',
	navCanvasFormUpdates: 'Atualizações do formulário do Canvas',
	navCopyHub: 'Copy hub',
	navCopyHubNew: 'NOVO Copy hub',
	navArchiveChecklist: 'Checklist de arquivamento',
	navExportHub: 'Hub de exportação',
	navReporting: 'Relatórios',
	navHelp: 'Ajuda geral',
	validationNavHelp: 'Ajuda de validação',
	leaveUsFeedback: 'Deixe-nos comentários',
	navDashboard: 'Painel resumo',
	tasksPage: 'Tarefas',
	documentsPage: 'Documentos',
	collaborationPage: 'Colaboração',
	automationPage: 'Automação',
	documentHelperConnectionIssue: 'Foi detectado um problema com o EY Canvas Document Helper. Clique <a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">aqui</a> para obter instruções sobre como resolver este problema.',
	noContentAvailable: 'Nenhum conteúdo disponível',
	noSectionsAvailable: 'Sem seções disponíveis',
	noInformationAvailable: 'Não há informações disponíveis',
	collapse: 'Agrupar',
	expand: 'Expandir',
	duplicate: 'Duplicar',
	duplicateSection: 'Seção duplicada',
	duplicateSectionHeader: 'Tem certeza de que deseja duplicar a seção selecionada?',
	deleteSection: 'Excluir seção',
	deleteSectionHeader: 'Tem certeza de que deseja excluir a seção selecionada?',
	deleteHeader: 'Excluir cabeçalho',
	deleteHeaderTitle: 'Tem certeza de que deseja excluir o cabeçalho selecionado?',
	confirmLabel: 'Confirmar',
	custom: 'Personalizar',
	selectHeader: 'Selecione cabeçalho',
	selectSection: 'Selecionar Seção',
	noResultsFound: 'Nenhum resultado encontrado',
	scot: 'SCOT',
	scotTypes: 'Tipo de SCOT',
	frequency: 'Frequência',
	SelectFrequency: 'Selecione a frequência',
	SelectControlType: 'Selecione o tipo de controle',
	itBadge: 'IT',
	soBadge: 'SO',
	noRecordsAvailable: 'Sem registros disponíveis',
	noIncompleteResponseSummaryView: 'Sem respostas incompletas',
	noUnresolvedCommentsSummaryView: 'Sem comentários não resolvidos',
	edit: 'Editar',
	editForm: 'Editar Formulário',
	editControl: 'Editar controle',
	delete: 'Excluir',
	remove: 'Remover',
	noBodies: 'Sem corpos disponíveis',
	relateDocuments: 'Relacionar documentos',
	relatedDocuments: 'Documentos relacionados',
	deleteBody: 'Excluir corpo do texto',
	bodyDescription: 'Tem certeza de que deseja excluir o corpo do texto selecionado?',
	description: 'Descrição',
	maxLengthForEditResponse: 'O corpo do texto excede o comprimento máximo permitido',
	maxLengthForEditResponseWithCount: 'A resposta contém {#} caracteres que excede o máximo de {##} caracteres. Ajuste a resposta reduzindo o texto ou a formatação e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	saveResponse: 'Descartar as alterações não salvará quaisquer edições feitas ao texto de resposta. Comentários fechados ou excluídos permanecerão em destacados. Por favor, confirme se você quer descartar todas as alterações.',
	discardChangesModalText: 'Descartar mudanças',
	seeBodyDescriptionText: 'Veja a descrição',
	hideBodyDescriptionText: 'Ocultar descrição',
	showBodyDescriptionText: 'Mostre a descrição',
	okLabel: 'Ok',
	addButtonLabel: 'Adicionar',
	addEvidence: 'Adicionar evidência',
	addTemporaryFiles: 'Adicione arquivos temporários',
	notemporaryDocs: 'Nenhum arquivo temporário disponível',
	relateFiles: 'Relacionar arquivos',
	uploadFiles: 'Fazer upload de arquivos',
	cancelButtonLabel: 'Cancelar',
	clickEditResponseToContinue: 'Clique no ícone de edição para continuar',
	editResponse: 'Editar resposta',
	save: 'Salvar',
	numericValuePlaceholder: 'Digite número',
	saveLabel: 'Salvar',
	cancelLabel: 'Cancelar',
	closeLabel: 'Fechar',
	editText: 'Editar',
	select: 'Selecione',
	selectScot: ' Selecionar SCOTs',
	clearHoverText: 'Limpar',
	optional: '(opcional)',
	nodocumentsAdded: 'Sem documentos disponíveis',
	errorBanner: '{0} Erros(s)',
	NetworkErrorMessage: 'A rede está experimentando um problema. Atualize a página e tente novamente mais tarde. Entre em contato com o Help Desk se o erro persistir.',
	of: 'de',
	characters: 'Caracteres',
	show: 'mostrar: ',
	views: 'Visualizar: ',
	primaryRelated: 'Formulários primários relacionados ao Canvas',
	secondaryRelated: 'Formulários secundários relacionados ao Canvas',
	singleLineValuePlaceholder: 'Digite texto',
	paceInputPlaceholder: 'ID do PACE',
	multiLineValuePlaceholder: 'Digite texto',
	riskFactorDescribe: 'Descrever',
	riskFactorLabel: 'Evento relevante e condição/risco de distorção',
	riskFactorEmptyWarning: 'Evento e condição/risco de distorção faltando descrição',
	riskFactorNoDescription: 'Criar novo ou selecionar evento relevante existente e condição / risco de distorção',
	fraudRiskTagMessage: 'Este evento relevante e condição/risco de distorção sempre dá origem a um risco de fraude ou, se aplicável, deve ser designado como não sendo um risco de distorção relevante',
	significantRiskTagMessage: 'Este evento relevante e condição/risco de distorção relevante sempre dá origem a um risco significativo, risco de fraude ou, se aplicável, deve ser designado como não sendo um risco de distorção relevante',
	on: 'sobre',
	sliderDeSelectMessage: 'Arraste o círculo para atribuir um valor',
	yearPlaceholder: 'AAAA',
	dayPlaceholder: 'DD',
	monthPlaceholder: 'MM',
	amLabel: 'AM',
	pmLabel: 'PM',
	relatedEntities: 'Entidades relacionadas',
	eyServiceGateway: 'EY Service Gateway',
	eyAutomation: 'EY Automation',
	eyserviceGatewayAutomation: 'EY Service Gateway & Automation',
	creating: 'Creating...',
	cannotCreateUdp: 'Cannot create UDP. Time Phase cannot be empty',

	// 440GL
	rrdReminderTitle: 'Lembrete de Resumo de Revisão e Aprovação',
	rrdReminderMessage: 'A Data de Emissão do Relatório é {rrdDescription}. Lembre-se de assinar o Resumo de Revisão e Aprovação {rrdPendingDays}.',
	rrdReminderPendingDays: 'em {0} dia(s)',

	//Create or Associate risks
	createAssociateRisksLabel: 'Criar riscos novos ou associados',
	relatedRiskIsMissingWarning: 'Está faltando o risco relacionado',
	associateRiskDescription: 'Selecione um ou mais riscos ou crie um novo risco para associar à resposta da pergunta.',
	createNewRiskLabel: 'Crie um novo risco',
	noRiskIdentifiedLabel: 'Nenhum risco identificado',

	// GuidanceModal
	eyAtlasLink: 'Atlas EY',
	guidanceHeaderMessage: 'Este módulo contém',
	guidanceModalHeader: 'Orientação',
	guidanceModalLabelText: 'Entrada',
	guidanceFooter: 'Para maiores informações.',
	guidanceSeveralEntriesText: 'várias entradas: ',
	guidanceVisitText: 'Visita',
	guidanceClickText: 'Clique',
	guidanceHereText: 'aqui',
	guidanceFooterText: 'Para maiores informações.',
	analyticsInconsistencies: 'Ao revisar as análises, considere se há alterações ou atividades inconsistentes com nossas expectativas. Estas situações podem ser indicativas de novos riscos, classes separadas de transações, alterações nas SCOT ou riscos de substituição da gestão.',
	analyticsInconsistenciesOSJE: 'OSJE representa a contabilização por partidas dobradas da conta selecionada que está sendo analisada. Procuramos pares de contas novos ou incomuns.',
	analyticsInconsistenciesActivityBySource: 'Atividade por fonte representa a atividade mensal bruta e as fontes relacionadas da conta selecionada. Nós nos concentramos em atividades não usuais ou mudanças nas fontes ou no volume de atividade de uma fonte.',
	analyticsInconsistenciesPreparerAnalysis: 'A análise do preparador resume a atividade bruta período após período registrada pelos preparadores para a conta selecionada. Nós nos concentramos em mudanças nos preparadores ou atividades em contas fora de sua função.',
	analyticsInconsistenciesAccountMetrics: 'As métricas da conta resumem informações importantes sobre a conta que auxiliam na designação da conta.',
	analyticsLoadingIsInProgress: 'A análise solicitada ainda está sendo carregada. A guia estará acessível quando isso for concluído.',

	aboutDescriptioin: 'Editar as camadas, padrões ou idiomas GAM. As edições desencadearão uma atualização de conteúdo.',
	aboutContentDescription: 'Edit the content layers, standards, language, or content driving entity. Edits will trigger a content update.',
	about: 'Sobre',
	formType: 'Tipo de formulário',
	gamLayer: 'Camadas GAM',
	contentLayer: 'Camadas de conteúdo',
	standard: 'Norma',
	language: 'Idioma',
	createdBy: 'Criado por',
	createdOn: 'Criado em',
	contentLastUpdatedBy: 'Conteúdo atualizado pela última vez por',
	contentLastUpdatedOn: 'Conteúdo atualizado pela última vez em',
	notModified: 'Não modificado',

	rolesInsufficientTooltip: 'Funções insuficientes para editar conteúdo. Trabalhe com um administrador do engagement para obter direitos suficientes.',
	knowledgeFormToolTip: 'Os documentos entregues da seção de conhecimento não podem ser atualizados. Atualizar o Perfil do Engagement para alterar o perfil deste formulário.',
	selectTeamMember: 'Nome ou e-mail',

	// SeeMore component
	showMore: 'Mostrar mais',
	showLess: 'Mostrar menos',
	showMoreEllipsis: 'Mostre mais...',
	showLessEllipsis: 'Mostrar menos...',

	relatedITapplicationSOs: 'Aplicativos/SOs de TI relacionados',
	aggregateITevaluations: 'Avaliações de IT agregadas',
	lowerRisk: 'Risco baixo',
	controlLowerRisk: 'Controle é de risco baixo',
	relatedITApplication: 'Aplicativos de TI relacionados',
	relatedITSO: 'SOs relacionados',
	noITApplicationUsed: 'Nenhum aplicativo de TI usado',

	notSel: 'NÃO SELECIONADO',
	internal: 'Interno',
	external: 'Externo',
	notSelected: 'Não selecionado',
	noOptionSelected: 'Não selecionado',
	tod: 'TOD',
	sap: 'SAP',
	int: 'INT',
	ext: 'EXT',
	toc: 'TOC',

	placeholderForSearch: 'Busca',
	source: 'Fonte',
	nature: 'Natureza',
	testOfDetail: 'Teste de detalhes',
	testOfControl: 'Teste de controle',
	substantiveAnalyticalProcedure: 'Procedimento analítico substantivo',
	expandScot: '1. Expandir uma SCOT',
	selectWCGWToDisplayTheResponsiveTask: '2. Selecione o risco de distorção relevante para exibir as tarefas responsivas',
	tasksWithNoRelatedWCGW: 'Tarefas sem risco relacionado de distorção relevante',
	noTasksAvailable: 'Sem tarefas disponíveis',
	noWCGWAvailableForTask: 'Sem ROMM disponível',
	noSubstantiveTasksAvailable: 'Sem tarefas substantivas relacionadas',
	selectAssertionToRelateWCGW: 'Selecione uma assertiva para relacionar risco com WCGW',
	significantAccounts: 'Contas significativas',
	riskName: 'Risco: ',
	accountName: 'Conta: ',
	control: 'Controle',
	controls: 'Controles',
	noScotsFound: 'Sem Scots relacionados',
	relatedwcgw: 'WCGWs relacionados',
	relatedRisks: 'Riscos Relacionados: ',
	boltIconTitle: 'Risco Relacionado',
	relatedITApp: 'Aplicativo/SO de TI relacionado',
	instructions: 'Instruções: ',
	expandRisk: '1. Expandir um risco',
	selectAssertion: '2. Selecione uma assertiva',
	identifyRelatedWCGW: '3. Identifique que o WCGW está relacionado ao Risco',
	clickAccount: '1. Clique em uma conta',
	selectWCGW: '2. Selecione um WCGW',
	identifyRelatedTask: '3. Identifique as tarefas que respondem ao WCGW',
	information: 'Informações',
	requiredAssertions: 'Assertivas necessárias',
	wcgwWithoutTasks: 'WCGW sem tarefas',
	rommAssociatedWNotRelyAssertion: 'ROMM está associado a uma assertiva vinculado à um risco com estratégia de não confiança em controles ou uma assertiva com risco inerente High onde a conta tem uma estimativa',
	hasRiskAssociated: 'Risco Associado',
	clearSelections: 'Desmarque Todos',
	romm: 'ROMM',
	riskOfMaterialMisstatementsWithoutRelatedTask: 'Risco de distorções materiais sem tarefa relacionada',
	selectOneOrMoreTasksToSeeTheRelatedROMM: 'Selecione uma ou mais tarefas para ver o ROMM relacionado',
	invalidRelatedEntity: 'A conta relacionada não pode ser encontrada. Atualize a página e tente novamente. Entre em contato com o Help Desk  se o problema persistir.',
	noResultsAvailable: 'Nenhum resultado encontrado',
	riskOfMaterialMisstatement: 'Risco de distorção relevante',
	AccountConclusion: 'Conclusão da conta',
	CanvasForm: 'Formulário do Canvas',
	IndependenceForm: 'Form de Independência',
	Profile: 'Perfil',
	AccountDetails: 'Detalhes',
	Conclusions: 'Conclusões',
	accountDetailsTab: 'Detalhes',
	conclusionsTab: 'Conclusões',
	formsNoContentText: 'Não há conteúdo disponível.',
	formsDocumentNoRelatedObjects: 'Nenhum objeto relacionado ao documento',
	formsNoRelatedObjects: 'Sem objetos relacionados',
	formsBodyHeaderControl: 'Controles',
	formsBodyDesignEffectiveness: 'Eficácia do desenho',
	formsScotsAndWcgws: 'SCOTs & WCGWs',
	wcgWAndRelatedControls: 'WCGWs e controles relacionados',
	controlAndRelatedItSO: 'Controles e IT apps/Sos relacionados',
	type: 'Tipo',
	designEffectiveness: 'Eficácia do design',
	approch: 'Aproximação',
	controlOpertaingEffectiveness: 'Eficácia operacional',
	iTAppSO: 'Aplicativo de TI/SO',
	iTProcess: 'Processo de TI',
	iTControl: 'Controle de IT',
	iTRisk: 'Riscos de TI',
	aggregateITEvaluation: 'Avaliação agregada de TI',
	relatedCanvasForm: 'Formulário do Canvas relacionado',
	relatedSections: 'Seções relacionadas',
	validations: 'Validações',
	profileV2Validation: 'Alterações não submetidas',
	profileV2ValidationModalDescription: 'Foram feitas alterações que resultariam em uma atualização de conteúdo, mas não foram submetidas. Se as alterações forem intencionais, feche esse modelo e envie as novas respostas do perfil. Se as alterações não forem intencionais, revise a tela de Alterações e reverta manualmente as respostas para as seleções anteriores.',
	profileV2ValidationCount: '1',
	itProcessWithoutRelatedTechnology: 'IT process without related technology',
	reviewNote: 'Notas de revisão',
	editAssociations: 'Editar associações',
	editAssociationsLower: 'editar associações',
	riskWCGW: 'Risco:Relação WCGW',
	wcgwTask: 'WCGW:Relacionamento de tarefa',
	noAssertionFound: 'Nenhuma assertiva foi associada. Clique {here} para associar assertivas',
	limitedRiskAccountIdentifier: 'Conta de risco limitado',
	insignificantAccountIdentifier: 'Conta insignificante',
	noWCGWFound: 'Não há WCGWs relacionados a esse risco. Clique em Editar associações para relacionar um ou mais WCGWs.',
	noRelatedWCGWs: 'Sem WCGWs relacionados',
	noWCGWAvailable: 'Não há ROMM disponível para a afirmação selecionada',
	expandCollapse: 'Clique aqui para expandir / recolher',
	requiredAssertionsInfo: 'Mostre apenas assertivas com avaliação de risco de estratégia em não confiança em controles e assertivas relacionadas a estimativas de risco mais altas',
	wCGWwithoutTasksInfo: 'Mostre apenas WCGWs relacionados a uma assertiva com uma avaliação de risco com estratégia em não confiança em controle e assertivas relacionadas a estimativas de risco mais altas, que não têm nenhuma tarefa substantiva responsiva associada',
	noBuildStepsAvailable: 'Não há etapas de construção disponíveis para serem apresentadas.',
	risk: 'Riscos',
	wcgw: 'WCGW',
	riskWcgw: 'Risco: WCGW',
	wcgwTasks: 'WCGW: Tarefa',
	riskWcgwLabel: 'Relacionar risco com WCGW',
	wcgwTasksLabel: 'Relacionar WCGW à tarefa',
	noRiskTypes: 'Nenhum tipo de risco encontrado',
	saveRisk: 'Salvar',
	noRisksFound: 'Nenhum risco encontrado',
	haveBeenIdentified: 'foram identificados',
	noAccountsFound: 'Nenhum registro encontrado',
	noResponseAvailable: 'Nenhuma resposta disponível',
	noDocumentsAvailable: 'Nenhum documento disponível',
	noValue: 'Sem valor',
	showValidation: 'Validações',
	noAccountsIdentified: 'Nenhuma conta foi identificada.',
	noAssertionsIdentified: 'Nenhuma assertiva foi identificada',
	noWcgwsIdentified: 'Nenhum WCGW foi identificado',
	pastingImagesNotAllowed: 'O uso de imagens não é permitido. Se forem necessárias imagens, carregue-as como evidências em formato de papel de trabalho no canvas.',
	incompleteResponse: 'Respostas incompletas',
	unresolvedComments: 'Comentários não resolvidos',
	inconsistentForms: 'Formulários inconsistentes',
	limitedRiskAccount: 'Conta de risco limitada',
	inherentRiskAssessment: 'Avaliação de risco inerente',
	task: 'Tarefa',
	selected: 'Selecionado',
	displaytoc: 'Exibir TOC',
	workingoffline: 'Trabalhando offline',
	syncinprogress: 'Sincronização em andamento',
	prepareoffline: 'Preparando dados para offline',
	connectionavilable: 'Conexão disponível',
	softwareUpdate: 'Atualizações de software',
	updateLater: 'Atualizar mais tarde',
	updateNow: 'Atualizar agora',
	updateMsg: 'Atualizações de software estão disponíveis para o EY Canvas. Selecione Atualizar agora para baixar e instalar as atualizações. A página será atualizada.',
	searchPlaceholder: 'Pesquisa',
	filter: 'Filtro',
	leftNavSearchPlaceholder: 'Pesquisar cabeçalhos e seções',
	back: 'Voltar',
	updateAvailable: 'Atualização disponível',
	contentUpdateAvailableTooltip: "Atualização de conteúdo disponível. Clique aqui para navegar até a tela 'Atualizações do formulário de tela' para iniciar uma atualização. ",
	moreMenu: 'Mais Menu',
	signoffPreparer: 'Assinar como preparador',
	signoffReviewer: 'Assinar como revisor',
	pagingShowtext: 'Mostrar',
	searchDocuments: 'Pesquisar documentos',
	noRelatedDocuments: 'Sem documentos relacionados.',
	noRelatedObjects: 'Sem objetos relacionados',
	documentName: 'Nome do documento',
	formDetails: 'Detalhes do formulário',
	questionsAndResponses: 'Perguntas e respostas',
	details: 'Detalhes',
	trackChanges: 'Acompanhar mudanças',
	goToTrackChanges: 'Vá para acompanhar as alterações',
	attributes: 'Atributos',
	relatedActions: 'Ações relacionadas',
	createCustom: 'Criar customização',
	createCustomButtonLabel: 'Create custom header, section, or body',
	overwriteForm: 'Substituir formulário',
	decimalNaN: 'NaN - Não é um número',
	noRelatedObjectsApplicable: 'Os objetos não são requeridos para serem associados a este Formulário do Canvas',
	objects: 'Objetos',
	objectName: 'Nome do objeto',
	addCustomDescription: 'Selecione o tipo de conteúdo a ser adicionado a este Formulário do Canvas, insira detalhes e clique em salvar',
	headerTitle: 'Título de cabeçalho',
	sectionTitle: 'Título de seção (obrigatório)',
	aresSectionTitle: 'Título da seção',
	customLabel: 'Rótulo personalizado (opcional)',
	customBodyDescription: 'Descrição do corpo do texto',
	header: 'Cabeçalho',
	section: 'Seção',
	body: 'Corpo do texto',
	requiredWCGW: 'Requerido',
	headerTitleRequired: 'Título de cabeçalho requerido.',
	bodyDescriptionRequired: 'Descrição do corpo é necessária.',
	bodySectionRequired: 'Seção é necessária.',
	bodyHeaderRequired: 'Cabeçalho é necessário.',
	sectionTitleRequired: 'Título de seção é necessário.',
	headerRequiredMessage: 'Cabeçalho é necessário.',
	enterDecimalAmount: 'Digite valor decimal',
	enterPercentage: 'Insira a porcentagem',
	completeRiskFactorAssessment: 'Avaliação completa de eventos e condições identificados.',
	noScotsEstimatesIdentified: 'Nenhuma SCOT ou estimativa foi identificada',
	// Track changes
	trackChangesResponseLabel: 'Acompanhar alteração da versão da resposta',
	trackChangesVersionLabel: 'Versão do acompanhamento de mudanças',
	noResponseIdentified: 'Nenhuma resposta identificada',

	// Compare responses
	compareResponsesLabel: 'Comparar respostas',
	compareResponsesTitle: 'Comparar as respostas da entidade',
	compareResponseNoDataPlaceholder: 'Não há dados disponíveis, pois o engagement possui apenas um documento do mesmo tipo.',
	labelFor: 'para',
	questions: 'Questões',
	answers: 'Respostas',
	countOfResponses: 'Contagem de respostas',
	openNotes: 'Abrir notas',
	clearedNotes: 'Notas limpas',
	click: 'Clicar',
	clickToViewAnswer: 'para ver a resposta',
	clickToViewQuestionAnswer: 'para ver perguntas e respostas',
	selectDocuments: 'Selecione documentos',
	selectedDocumentsCount: '{0} documentos selecionados',
	selectedDocumentCount: '{0} documento selecionado',
	associatedDocuments: 'Documentos relacionados',
	noAnswerProvided: 'Nenhuma resposta fornecida',

	// Workspace Engagement
	thisEngagement: 'Este projeto',
	documentLocation: 'Localização do documento',
	otherEngagementsInWorkspace: 'Outros projetos no espaço de trabalho',
	added: 'Adicionado',
	documentIneligibleForSharingMessage: 'Documentos confidenciais não podem ser compartilhados.',
	fitDocumentCannotbeSelected: 'Os documentos FIT não podem ser compartilhados entre projetos.',

	//Helix Configuration
	helixConfigurationTitle: 'Integre dados do EY Helix',
	helixConfigurationPageDescription: 'Valide o projeto EY Helix vinculado e importe dados para o EY Canvas. Se você alterou alguma configuração do EY Helix abaixo ou fez alterações em seus dados do EY Helix após importar os dados, você terá que importar novamente os dados para que a atualização seja feita.',
	linkedEYHelixProjects: 'Projetos EY Helix vinculados: ',
	client: 'Cliente: ',
	engagement: 'Engagement: ',
	analysisDate: 'Data da análise: ',
	eyHelixProjects: 'Projetos EY Helix',
	noPrimaryEYHelixproject: 'Nenhum projeto primário do EY Helix foi identificado.',
	here: 'aqui',
	identifyEyHelixProjects: 'para identificar um projeto do EY Helix e iniciar o fluxo de trabalho.',
	eyHelix: 'EY Helix',
	primary: 'primário',
	helixSettingsDescription: 'Clique em Editar para selecionar as configurações que serão aplicadas ao carregar os EY Helix Analyzers.',
	editButton: 'Editar',
	helixSettingsModalTitle: 'Configurações do EY Helix',
	currencyType: 'Tipo de moeda',
	currencyTypeError: 'O tipo de moeda não pôde ser recuperado no EY Helix. Confirme se os dados estão configurados corretamente no EY Helix e tente novamente.',
	shortNumberFormat: 'Formato de número curto',
	shortNumberFormatFooter: 'Arredondamento a ser aplicado aos valores numéricos exibidos nas tabelas do EY Helix.',
	eyHelixAnalyzerFilterMetadataError: 'Não foi possível conectar-se ao EY Helix. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	functional: 'Funcional',
	reporting: 'Relatório',
	currencyCode: 'Código de moeda',
	businessUnit: 'Unidade de negócios',
	roundingNumberFormat: 'Formato de número de arredondamento',
	eyHelixProjectChangedLine1: 'O projeto EY Helix vinculado foi alterado desde a última vez que as configurações do EY Helix foram salvas.',
	eyHelixProjectChangedLine2: 'Clique em Editar para atualizar as configurações antes que os dados possam ser importados ou re-importados do EY Helix.',
	helixSettingsTimeline: 'Configurações do EY Helix',
	helixMapEntitiesToBU: 'Mapear entidades para unidades de negócios',
	helixNumberOfMapEntities: 'Número de Unidades de Negócios mapeadas',
	importEYHelixDataTimeline: 'Importar dados do EY Helix',
	mapAccountsHelixTimeline: 'Mapear contas',
	setEYHelixSettings: "Edite suas configurações do EY Helix abaixo. Depois de salvos e os dados importados, as datas comparativas 1 e 2 selecionadas serão usadas como comparações com a data de análise designada na atividade OAR. Para comparar com apenas uma data comparativa, selecione 'Nenhum' na seleção comparativa de 2 datas. ",
	eyHelixDataHasChangedLine1: 'Os dados mudaram desde a última vez que as configurações foram salvas. Faça novas seleções abaixo e clique',
	eyHelixDataHasChangedLine2: 'para atualizar as configurações do EY Helix.',
	all: 'Todos',
	multiple: 'Múltiplo',
	notApplicableAbbreviation: 'N/A',
	importEyHelixData: 'Importar dados do EY Helix',
	editScreenshot: 'Editar',
	deleteNote: 'Excluir',
	removeAnnotation: 'Remover anotação',
	addAnnotation: 'Adicionar anotação',
	addingBoundingBox: 'Selecione uma área na captura de tela para anotar, confirme a anotação e clique na marca de seleção para salvar.',
	cancelBoundingBox: 'Cancelar',
	deleteScreenshot: 'Excluir captura de tela',
	openInFullscreen: 'Abrir na tela cheia',
	helixURLErrorMessage1: 'A configuração para o projeto mapeado do EY Helix está incompleta.',
	helixURLErrorMessage2: 'Acesse a página {0} para atualizar.',
	helixIsNotEnabledMessage: 'EY Helix não está habilitado para o seu engagement.',
	helixSetup: 'Configuração do EY Helix',
	openAnalysisInHelix: 'Análise aberta no EY Helix',
	helixInvaliddate: 'Data inválida. Selecione uma data anterior à data de análise.',
	helixcomparativedateoptional: 'Data comparativa EY Helix 2 (opcional)',
	helixpriorperioddate: 'Data comparativa EY Helix 1',
	helixanalysisperiod: 'Data de análise do EY Helix',
	helixfiscalDropDownLabel: 'Período {0} - {1}',

	helixSettingsEditButtonTitle: 'Editar',
	helixImportDataEditButtonTitle: 'Importar',
	helixImportInsufficientPermissionsMessage: 'Permissões insuficientes para realizar a importação de dados do Helix. Entre em contato com seu administrador do engagement e solicite permissão para iniciar uma importação de dados do Helix.',
	helixImportNotAllowed: 'O perfil do engagement não permite a importação de dados Helix',
	helixDeleteImportInsufficientPermissionsMessage: 'Permissões insuficientes para excluir a importação de dados do EY Helix. Entre em contato com o administrador do seu engagement e solicite permissão para excluir os dados do EY Helix.',

	EYAccountMappingStepLabel: 'Gerenciar mapeamento de contas',

	EYAccountMappingOptional: 'Opcional',
	EYAccountMappingStepTitleSettingsCompleted: 'Realizar mapeamento de contas EY',
	EYAccountMappingStepTitleSettingsIncomplete: 'Conclua as configurações do EY Helix para acessar o Módulo de Mapeamento do EY Helix',
	EYAccountMappingInstructions: 'Mapeie contas de clientes para contas EY e processe alterações. Assim que o processamento for concluído, importe os dados abaixo.',
	manageAccountMappingButtonLabel: 'Gerenciar mapeamento de contas',

	//EY Helix Setup Card
	EYHelixSetupTitle: 'EY Helix',
	EYHelixSetupSubTitle: 'Configurar e importar dados para o EY Canvas',
	LastImported: 'Última importação',
	EYHelixSettings: 'Conexões EY Helix',
	NoEYHelixProjectLinkedLabel1: 'Este engagement não está vinculado a um projeto primário do EY Helix. Por favor, visite a',
	NoEYHelixProjectLinkedLabel2: 'página para estabelecer o link.',
	NoEYHelixProjectLinkedHperlink: 'Projetos do EY Helix',
	NoDataImportedLabel: 'Os dados não foram importados do EY Helix. Clique em configurações do EY Helix para iniciar o processo.',
	noHelixConnections: "Clique em 'Conexões EY Helix' para criar uma conexão. ",
	helixConnectionExists: 'conexão existente',
	helixConnectionsExist: 'conexões existentes',
	helixTrialBalanceImported: 'Saldo de teste de Escopo & Estratégia importados',
	helixTrialBalancesImported: 'Saldos de teste de Escopo & Estratégia importados',
	helixNoTrialBalanceImported: "Clique em 'Conexões EY Helix' para importar um saldo de teste de Escopo & Estratégia. ",

	// EY Helix - Map Accounts
	mapAccountsHelixCanvas: 'Clique em Editar para mapear contas do EY Canvas para contas importadas da EY Helix',
	mapAccountsHelixCanvasSubtitle: 'Arraste e solte cada conta do EY Helix para mapear para uma conta do EY Canvas. Clique em Gerenciar contas para criar ou editar contas do EY Canvas.',
	mapAccountsHelixHeaderLabel: 'Contas EY Helix',
	mapAccountsCanvasHeaderLabel: 'Contas EY Canvas',
	mapAccountsConnectedLabel: 'Contas conectadas',
	mapAccountsShowMappedLabel: 'Mostrar contas mapeadas',
	mapAccountsHideMappedLabel: 'Ocultar contas mapeadas',
	mapAccountsManageLabel: 'Gerenciar contas',
	mapAccountsAndDisclosuresManageLabel: 'Gerenciar contas e divulgações',
	mapAccountsNoCanvasAccounts: 'Nenhuma conta foi identificada.',
	mapAccountsNoCanvasAccountsClick: 'Clique',
	mapAccountsNoCanvasAccountGetStarted: ' para começar.',
	mapAccountsRemoveAccount: 'Retirar',
	mapAccountsReImportHelixAccounts: 'A importação de dados do EY Helix não foi bem-sucedida. Reimporte os dados e tente novamente.',
	mapAccountsReImportHelixAccountsHelpDesk: 'Se o problema persistir, entre em contato com o Help Desk',
	mapAccountsNoHelixAccountHasBeenImported: 'Nenhuma conta EY Helix foi importada.',
	mapAccountsNoHelixAccountHasBeenImportedCheckData: 'Verifique os dados no EY Helix e tente novamente.',

	//Helix Analyzer
	accountNotRelatedToDocumentOnPhaseTwo: 'Nenhuma conta relacionada a este documento',

	//PM TE SAD Widget
	materialityWidgetLabel: 'PM/TE/SAD',

	// TE labels
	planningmateriality: 'Materialidade de planejamento',
	requiredTePercentage: 'ET Obrigatório',
	suggestedtepercentage: 'TE sugerido',
	currentperiodte: 'Período Atual TE',
	priorperiodte: 'Período Anterior TE',
	pmPriorPeriod: 'Período anterior MP',
	tepercentage: 'Porcentagem TE',
	teamount: 'Montante TE',
	teLabel: 'Erro tolerável',
	sugestedSAD: 'Porcentagem SAD sugerida: ',
	priorSAD: 'Período anterior SAD',
	currentPeriodSAD: 'SAD do período atual',
	sadPercentage: 'Porcentagem SAD',
	sadAmount: 'Montante SAD',
	rationaleLabel: 'Justificativa',
	suggestedTEPercentageInfo: 'Se uma porcentagem diferente for selecionada com base em fatores específicos do engajamento, você será solicitado a documentar esses fatores abaixo.',
	rationaleTEDescription: 'Insira a justificativa para TE como uma porcentagem de PM em consideração aos atributos selecionados acima.',
	teAmmountInvalid: 'Insira um valor válido ou selecione 50% ou 75%',
	highRiskTEAmmountInvalid: 'Insira um valor válido ou selecione 50%',
	highRiskTERequired: 'Com base nas respostas às considerações acima, o percentual do ET identificado é obrigatório e não pode ser alterado.',

	// EY Helix Map Entities to Business Units Modal
	mapEntitiesModalTitle: 'Gerenciar entidades',
	mapEntitiesModalLeyendDescription: 'Gerencie o mapeamento entre Entidades e Unidades de Negócios do EY Canvas do projeto do EY Helix abaixo.',
	mapEntitiesModalLeyendNote: 'Nota: Uma Unidade de Negócio pode estar associada a uma ou múltiplas Entidades do EY Canvas. Uma vez salvos e os dados importados, os dados associados às Unidades de Negócios no EY Helix serão exibidos como mapeados para as Entidades EY Canvas relacionadas.',
	mapEntitiesModalEntityCodeLabel: 'Código da entidade',
	mapEntitiesModalEmptyEntitiesList: 'Nenhuma entidade foi criada.',
	mapEntitiesRelatedBusinessUnitDropdownPlaceholder: 'Unidade de Negócios Relacionada',
	mapEntitiesSelectedBusinessUnitsCount: '{0} Unidades de negócios selecionadas',

	//AdjustedBasis
	enterAmount: 'Digite a quantidade',
	basisAmount: 'Montante base',
	lowEndOfRange: 'Baixo limite de alcance',
	highEndOfRange: 'Alto limite de alcance',
	suggestedRange: 'Com base nos fatores acima, o uso de uma porcentagem para o {0} do intervalo {1} a {2} pode ser apropriado. Se um valor for selecionado fora desse intervalo com base em fatores específicos do engagement, você será solicitado a documentar esses fatores abaixo.',
	suggestedRangeLowPMBracket: 'Com base nos fatores acima, uma porcentagem de {0} pode ser apropriada. Se um valor for selecionado fora dessa porcentagem com base em fatores específicos de engagement, você será solicitado a documentar esses fatores.',
	middle: 'meio',
	lowerEnd: 'extremidade inferior',
	higherEnd: 'extremidade superior',
	lowEnd: 'baixo nível',
	priorPeriodPm: 'Período anterior PM',
	suggestedRangeSummary: 'Intervalo sugerido',
	loadingMateriality: 'Carregando materialidade ...',
	pmBasisPercentage: 'Base percentual  PM',
	pmAmount: 'Montante PM',
	currentPmAmount: 'Montante da MP do período atual',
	pmAmountPlaceholder: 'Insira o valor da MP',
	currentPeriodPm: 'Período atual PM: ',
	enterRationale: 'Digite o racional',
	rationaleDescription: 'Digite o racional para a porcentagem base de PM em consideração aos atributos selecionados acima',
	pmValidationMessage: 'A materialidade planejada não pode exceder o limite superior do intervalo',
	sadValidationMessage: 'O valor nominal não pode exceder o limite superior do intervalo.',
	sadRationaleDiscription: 'Insira o racional para o parcentual do SAD em consideração aos atributos selecionados acima',
	nopriorperiodDocs: 'Nenhum documento do período anterior disponível',
	addPriorPeriodEvidence: 'Adicionar evidência do período anterior',
	addToEvidenceLabel: 'Adicionar à evidência',
	moveToEvidenceLabel: 'Mover para evidência',
	addToEvidenceModalDescription: 'Crie um novo nome ou mantenha o nome existente para o documento selecionado.',
	GoToSource: 'Vá para a fonte',
	//ITRiskITControls
	createNewITGC: 'Novo ITGC',
	relateITGC: 'Relate ITGCs',
	createNewITSP: 'Novo ITSP',
	relateITSP: 'Relacionar ITSP',
	noITGC: 'sem ITGCs',
	itRiskForITGCITSP: 'Nome do risco de TI (obrigatório)',
	createITGCModalDescription: "Insira os detalhes do ITGC abaixo e selecione '<b>{0}</b>' para finalizar. Para criar outro ITGC, selecione '<b>{1}</b>'. ",
	createITSPModalDescription: "Insira os detalhes do ITSP abaixo e selecione '<b>{0}</b>' para finalizar. Para criar outro ITSP, selecione '<b>{1}</b>'. ",
	controlDesignEffectiveness: {
		[0]: {
			description: 'Não selecionado'
		},
		[1]: {
			description: 'Eficaz'
		},
		[2]: {
			description: 'Ineficaz'
		}
	},
	controlOperationEffectiveness: {
		[0]: {
			description: 'Não selecionado'
		},
		[1]: {
			description: 'Eficaz'
		},
		[2]: {
			description: 'Ineficaz'
		}
	},
	controlTesting: {
		[0]: {
			description: 'Não selecionado'
		},
		[1]: {
			description: 'sim'
		},
		[2]: {
			description: 'Não'
		}
	},
	itAppTypes: {
		[0]: {
			label: 'IT App'
		},
		[1]: {
			label: 'SO'
		}
	},
	controlType: {
		[0]: {
			controlTypeName: '',
			shortName: 'Não selecionado'
		},
		[1]: {
			controlTypeName: 'IT Application Control',
			shortName: 'Aplicativo'
		},
		[2]: {
			controlTypeName: 'IT Dependent Manual Control',
			shortName: 'ITDM'
		},
		[3]: {
			controlTypeName: 'Prevenção manual',
			shortName: 'Prevenção manual'
		},
		[4]: {
			controlTypeName: 'Detecção manual',
			shortName: 'Detecção manual'
		}
	},
	controlTypeEnumLabel: {
		[0]: {
			controlTypeName: 'Não selecionado'
		},
		[1]: {
			controlTypeName: 'Controle de aplicativos de TI'
		},
		[2]: {
			controlTypeName: 'Controle manual dependente de TI'
		},
		[3]: {
			controlTypeName: 'Prevenção manual'
		},
		[4]: {
			controlTypeName: 'Detecção manual'
		}
	},
	controlFrequencyType: {
		[0]: {
			controlFrequencyTypeName: 'Não selecionado'
		},
		[1]: {
			controlFrequencyTypeName: 'Muitas vezes por dia'
		},
		[2]: {
			controlFrequencyTypeName: 'Diariamente'
		},
		[3]: {
			controlFrequencyTypeName: 'Semanalmente'
		},
		[4]: {
			controlFrequencyTypeName: 'Mensalmente'
		},
		[5]: {
			controlFrequencyTypeName: 'Trimestralmente'
		},
		[6]: {
			controlFrequencyTypeName: 'Anualmente'
		},
		[7]: {
			controlFrequencyTypeName: 'Controle de aplicativos de TI'
		},
		[8]: {
			controlFrequencyTypeName: 'Outro'
		}
	},
	strategyType: {
		[0]: {
			strategyTypeName: 'Não selecionado'
		},
		[1]: {
			strategyTypeName: 'Controles'
		},
		[2]: {
			strategyTypeName: 'substantivo'
		},
		[3]: {
			strategyTypeName: 'confiar'
		},
		[4]: {
			strategyTypeName: 'Não Confiar'
		}
	},
	aggregateITEvaluationType: {
		[0]: {
			aggregateITEvaluationTypeName: 'Não selecionado'
		},
		[1]: {
			aggregateITEvaluationTypeName: 'Suporta'
		},
		[2]: {
			aggregateITEvaluationTypeName: 'Não suporta'
		},
		[3]: {
			aggregateITEvaluationTypeName: 'Suporte FS e ICFR'
		},
		[4]: {
			aggregateITEvaluationTypeName: 'Suporte somente FS'
		}
	},

	sampleItemFilterLabels: {
		filterTypeOfTags: 'Tags',
		noFiltersAvailable: 'Nenhum filtro disponível',
		filterToolTip: 'Filtro',
		clearAll: 'Limpar tudo',
		showMore: 'mais',
		filters: 'Filtros',
		noResults: 'Nenhum resultado encontrado'
	},

	stratergyTypeLabels: {
		[0]: {
			label: 'Não selecionado'
		},
		[1]: {
			label: 'Dentro do escopo'
		},
		[2]: {
			label: 'Fora do escopo'
		}
	},
	noChangeReasonCommentAvailable: 'Clique em editar o motivo nas configurações do documento para inserir o motivo da alteração.',
	changeReasonModalTitle: 'Editar razão para a alteração',
	changeReasonModalText: 'Selecione o motivo da alteração(s) feita no documento após a data do relatório. Se várias alterações administrativas tiverem sido feitas, selecione a alteração mais significativa das opções abaixo. Se foram feitas alterações administrativas e não administrativas, selecione abaixo a opção não-administrativa.',
	changeReasonUploadModalTitle: 'O motivo para envio de documentos',
	changeReasonUploadModalText: 'Selecione o motivo da alteração(s) feita no documento após a data do relatório. Se várias alterações administrativas tiverem sido feitas, selecione a alteração mais significativa das opções abaixo. Se foram feitas alterações administrativas e não administrativas, selecione abaixo a opção não-administrativa.',
	changeReasonModalComboPlaceholder: 'Selecionar',
	changeReasonModalAnnotationText: 'Documentar as circunstâncias encontradas e as razões para adicionar as informações; novos ou adicionais procedimentos de auditoria realizados, evidências de auditoria obtidas e conclusões alcançadas, e o efeito em nosso relatório de auditoria.',
	changeReasonUploadModalAnnotationText: 'Documentar as circunstâncias encontradas e as razões para adicionar as informações; novos ou adicionais procedimentos de auditoria realizados, evidências de auditoria obtidas e conclusões alcançadas, e o efeito em nosso relatório de auditoria.',
	changeReasonModalAnnotationPlaceHolder: 'Digite o motivo da alteração',
	changeReasonModalChangeReasonRequired: 'Alterar motivo para salvar',
	reasonColumnTitle: 'Motivo',
	shared: 'Compartilhado',
	shareStatusOwned: 'Propriedade deste engagement.',
	shareStatusShared: 'Compartilhado dentro deste engagement.',
	lastModifiedBy: 'Última modificação por',
	fileSize: ' | {1} KB',
	openedLabelText: 'Aberto',
	currentlyBeingModifiedBy: 'Atualmente sendo modificado por',
	OpenGuidedWorkflowDocument: 'Abra este documento por meio da ativação do EY Canvas FIT',
	submitProfile: 'Enviar perfil',
	submitProfileFit: 'Enviar perfil',
	contentUpdateUnAuthorizedTooltipMessage: 'Permissões insuficientes para realizar atualização de conteúdo. Entre em contato com seu administrador do engagement e solicite permissão para iniciar uma atualização de conteúdo.',
	submitProfileValidationErrorMessage: 'O perfil só pode ser enviado quando todas as perguntas tiverem sido respondidas. Filtre para perguntas incompletas, complete-as e envie-as novamente. Se o problema persistir, entre em contato com o Help Desk.',
	pickADate: 'Selecione uma data',

	/* Sign Offs */
	preparerHoverText: 'Assinatura como preparador',
	reviewerHoverText: 'Assinatura como revisor',
	preparerTitle: 'Assinatura do preparador',
	reviewerTitle: 'Assinatura do revisor',
	deleteHoverText: 'Remover aprovação',
	preparer: 'Preparador',
	reviewer: 'Revisor',
	preparerLabel: 'P',
	reviewerLabel: 'R',
	noSignOffsAvailable: 'Não há assinaturas disponíveis',
	none: 'Nenhum',
	partnerInChargeLabel: 'PIC',
	eqrLabel: 'EQR',
	documentSignoffRequiredLabel: 'Assinaturas necessárias de: ',

	relatedDocumentsTitle: 'Documentos relacionados',
	relatedTasksCount: '{0} Tarefas relacionadas',
	relatedTasksTitle: 'Tarefas relacionadas',
	relateTemporaryFiles: 'Relacionar arquivos temporários',
	bodyRelatedDocumentsTitle: 'Documentos associados a um corpo',
	relatedObjectsTitle: 'Objetos relacionados',
	relateDocumentsTitle: 'Gerenciar documentos relacionados',
	relateDocumentsToBodyTitle: 'Adicionar evidências',
	relateDocumentsDesc: 'Selecione documentos a serem associados ao Formulário de Tela',
	relateDocumentsToBodyDesc: 'Relacionar um documento deste projeto ou de outro projeto neste espaço de trabalho',
	relateDocumentsToTheBody: 'Relate a document from this engagement.',
	priorPeriodEvidencesToTheBody: 'Evidências de período anterior relacionadas ao corpo',
	relatedDocunentEngdisabed: 'O documento não é compartilhado com este engagement',
	showOnlyRelatedDocuments: 'Mostrar apenas documentos relacionados',
	manageDocuments: 'Gerenciar documentos',
	documentCount: '{0} documento',
	documentsCount: '{0} documentos',
	relateDocumentsSearchPlaceholder: 'Pesquisar documentos',
	overwriteFormDesc: 'Selecione um formulário para substituir as respostas com os dados do formulário do Canvas atual. Observe que o formulário do Canvas atual será movido para Arquivos Temporários.',
	searchFormPlaceholder: 'Formulário de pesquisa',
	overwriteLabel: 'Substituir',
	confirmOverwriteLabel: 'Confirmar a substituição',
	confirmOverwriteDesc: "Tem certeza de que deseja copiar o conteúdo do formulário '{0}' para o formulário '{1}'? As respostas serão substituídas no formato '{2}', mas as evidências e objetos relacionados não serão. Evidências e objetos relacionados aplicáveis ​​devem ser reassociados ao formulário '{3}' após a sobregravação ter sido concluída. O formulário '{4}' manterá suas camadas de perfil/GAM existentes, portanto, se esse perfil for diferente do formulário '{5}', revise e limpe o conteúdo copiado. \n Não feche o navegador nem navegue enquanto o processo de substituição estiver em andamento. Após a conclusão do processo de substituição, o formulário '{6}' será movido para Arquivos temporários e você será direcionado para o formulário '{7}'. Essa ação não pode ser desfeita. ",
	formSelectionRequired: 'Selecione um formulário para substituir.',

	open: 'Aberto',
	startCoeditMode: 'Iniciar edição de multi-usuário',
	endCoeditMode: 'Encerrar a edição de multi-usuário',
	openReadOnly: 'Aberto Somente leitura aberta',
	copyLink: 'Copiar link',
	rename: 'Renomear',
	viewHistory: 'Ver histórico',
	documentOpenModelLabel: 'Documento atualmente sendo modificado',
	modelUserOpenedTheDocumentText: 'Este usuário abriu o documento',
	modelDocumentOpenedText: 'Este documento está sendo modificado por',
	modelOpenedDocumentConflictText: 'Abrir este documento pode causar conflitos, por isso recomendamos abrir apenas como leitura. Se você gostaria de se tornar o editor deste documento, então',
	clickHereEnabledText: 'clique aqui.',
	documentOptions: 'Opções de documentos',
	accountDetails: 'Detalhes da conta',

	// DAAS labels
	coEditModeIsEnding: 'A edição de multi-usuário está terminando',
	coEditMode: 'Edição de multi-usuário',
	checkInInProgressMessage: 'Check-in em andamento. O check-in do documento pode levar até 20 minutos. Atualize para obter atualizações',
	checkInInErrorLabel: 'Falha no check-in',
	checkOutInProgressMessage: 'Check-out em andamento. O check-out do documento pode levar até 20 minutos. Atualize para atualizações',
	checkOutInProgressLabel: 'Check-out em andamento.',
	checkInInProgressLabel: 'Check-in em andamento',
	checkOutInErrorLabel: 'Falha no check-out.',
	daasErrorMessage: 'A operação não pode ser concluída neste momento. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	coEditModeIsStarting: 'A edição de multi-usuário está começando',
	daasOpenDocumentWarning: 'Multi-user editing may have been ended by another user. Refresh the page and try again.',
	beingEditedInCoeditMode: 'Being edited in multi-user edit. Edit started by {0}',
	beingEditedInCoeditModeOn: 'on {0}.',
	beingEditedInCoeditModeError: 'Being edited in multi-user edit',
	coEditModeAutomaticallyEnds: 'O documento está em modo de edição de multi-usuário, que terminará automaticamente em {0} dias.',
	coEditModeAutomaticallyEndsToday: 'O documento está em modo de edição de multi-usuário, que terminará hoje.',
	daasStartCollaborationModeWarning: 'Collaboration mode may have been started by another user. Refresh the page and try again.',
	documentCurrentlyBeingModifiedTitle: 'Document currently being modified',
	documentCurrentlyBeingModifiedHeader: 'This document is currently being modified by {0}. This user opened the document',
	documentCurrentlyBeingModifiedBody: 'Starting multi-user edit mode could cause conflicts, so we recommend discussing with {0} before proceeding. Select {1} to start the multi-user edit mode or {2} to return without starting multi-user edit mode.',
	documentEndMultiUserEditingTitle: '​Encerrar edição de multi-usuário',
	documentEndMultiUserEditingHeader: 'Warning: Other users may be actively editing this document.  This can be checked by opening the document and seeing if other users are currently in the file. Please confirm that all changes are complete before ending multi-user mode. File changes in multi-user mode may take up to 1 minute to be processed. Therefore, please wait at least 1 minute after exiting the file before ending multi-user mode.',
	documentEndMultiUserEditingBody: 'Select {0} to end the multi-user edit mode or {1} to return without ending multi-user edit mode.',
	startMultiuserEditing: 'Start',

	/* Engagement Comments */
	clear: 'Limpar',
	close: 'Fechar',
	reOpen: 'Reabrir',
	reply: 'Adicionar resposta',
	replyLabel: 'Responder',
	unselectComment: 'Desmarcar comentário',
	commentText: 'Digite o texto',
	replyText: 'Texto de resposta',
	openStatus: 'Aberto',
	clearedStatus: 'Resolvido',
	closedStatus: 'Fechado',
	chartCommentsTitle: 'Revisar notas',
	showComments: 'Mostrar comentários',
	noRecordsFound: 'Nenhum registro encontrado',
	noCommentsFound: 'Deixe um comentário usando as entradas abaixo. Atribua o comentário a um usuário e especifique a prioridade e a data de vencimento.',
	newComment: 'Adicionar comentário',
	addNoteTitle: 'Adicionar nota',
	editComment: 'Editar comentário',
	newReply: 'Adicionar resposta',
	editReply: 'Editar resposta',
	commentTextRequired: 'Texto de comentário necessário',
	replyTextRequired: 'Texto de resposta necessário',
	myComments: 'Meus comentários',
	assignTo: 'Atribuir a',
	theCommentMustBeAssigned: 'Atribuíção requerida para',
	priorityRequired: 'Prioridade necessária',
	dueDateRequired: 'Data de vencimento requerida',
	assignedTo: 'Atribuído a: ',
	allComments: 'Todos os comentários',
	assignedToMe: 'Atribuído a mim',
	unassigned: 'Não atribuído',
	draggableCommentsPlaceholder: 'Digite o texto para adicionar um novo comentário',
	draggableNotesPlaceholder: 'Digite o texto para adicionar uma nova nota',
	enterReply: 'Digite a resposta',
	dueDate: 'Data de vencimento',
	commentsAmmount: '{count} comentários',
	singleCommentAmmount: '{count} comentário',
	eyInternal: 'EY',
	noneAvailable: 'Não disponível',

	navHelixProjects: 'Conexões do EY Helix',

	evidence: 'Evidência',
	priorPeriod: 'Período anterior',
	temporaryFiles: 'Arquivos temporários',
	priorPeriodEvidence: 'Período anterior',
	closed: 'Todas as atribuições encerradas',

	/*Delete*/
	deleteFileTitle: 'Excluir documento',
	deleteFileCloseBtnTitle: 'Cancelar',
	deleteFileConfirmBtnTitle: 'Excluir',
	deleteFileCloseTitle: 'Fechar',
	deleteFileModalMessage: 'Você tem certeza de que deseja excluir o documento selecionado?',
	/*Rename*/
	editCanvasformObjects: 'Edite os atributos e clique em <b>Salvar</b>.',
	renameFileModalMessage: 'Renomeie o documento e clique em Salvar.',
	renameScreenshotModalMessage: 'Renomeie a captura de tela e clique em Confirmar.',

	renameFileTitle: 'Renomear documento',
	fileNameRequired: 'O nome do arquivo é necessário',
	invalidCharacters: 'O nome do arquivo não pode incluir: */:<>\\?|"',
	existingFileName: 'O nome do arquivo não é único. Atualize a página ou renomeie o arquivo para remover esta mensagem.',
	maxLengthExceeded: 'O nome do documento não pode exceder 115 caracteres.',

	STEntityProfileBannerMessage: 'Foram feitas alterações em um ou mais perfis de entidade que resultarão em atualizações de conteúdo. Navegue de volta para a página Entidades de perfil e clique em "Importar conteúdo" para receber o novo conteúdo aplicável ao perfil da entidade ou reverter as respostas para um estado anterior.',
	independenceValidationForOwnForm: 'Foram feitas alterações nas respostas da Independência, mas não foram apresentadas. Se as alterações forem intencionais, certifique-se de que as respostas sejam submetidas. Se as alterações não forem intencionais, revise a exibição Alterações e reverta manualmente as respostas para seleções anteriores.',
	independenceValidationForOthersForm: 'Mudanças foram feitas nas respostas do Independência, mas não foram submetidas pelo membro da equipe. Certifique-se de que o membro da equipe revise as alterações e envie o mesmo se elas forem intencionais.',
	insufficientRightsForIndependenceSubmission: 'Permissões insuficientes para editar conteúdo. Entre em contato com o administrador do engagement e solicite permissão para editar o conteúdo.',
	submitIndependenceProfileV2Message: 'Revise o perfil e confirme se as respostas estão corretas. Se for isso, por favor forneça assinaturas e continue com o projeto.',
	submitIndependenceProfileV2EditMessage: 'Nenhuma alteração foi feita no perfil que resultaria na alteração do conteúdo do projeto. Use a página de atualização de conteúdo do Projeto para realizar uma atualização de conteúdo, se necessário.',
	insufficientRightsForProfileV2Submission: 'Permissões insuficientes para editar o perfil. Entre em contato com o administrador do projeto e solicite permissão para editar o perfil.',
	returnToDashboard: 'Retornar ao Dashboard',
	returnToDashboardFit: 'Retornar ao painel',
	profileV2ChangeNotSubmittedBannerMessage: 'Changes have been made to the Profile that will result in content updates. Submit the Profile to receive the new content or revert the answers to the previous state.',
	independenceChangeNotSubmittedBannerMessage: 'Foram feitas mudanças na forma de independência que precisam ser reapresentadas. Envie o formulário de independência ou desative este usuário para limpar a validação.',
	multiEntityIndividualProfileBannerMessage: 'Permissões insuficientes para editar o perfil. Entre em contato com o administrador do engagement e solicite permissão para editar o perfil.',
	scotStrategy: 'Estratégia SCOT',
	wcgwStrategy: 'Estratégia WCGW',
	itProcessStrategy: 'Estratégia de processos de TI',

	/*Edit Wcgw*/
	editWcgw: 'Editar WCGW',
	viewWcgw: 'Exibir WCGW',
	editScot: 'Editar SCOT',
	viewScot: 'Ver SCOT',
	showIncomplete: 'Mostrar incompleto',
	forms: 'Forms',
	form: 'Formulário',
	comments: 'Notas',
	changes: 'Mudanças',
	editHeader: 'Editar cabeçalho',
	editSection: 'Editar seção',
	editBody: 'Editar corpo',
	editSectionDescription: 'Edite os detalhes da seção e clique em ‘Salvar’.',
	editHeaderDescription: "Edite os detalhes do cabeçalho e clique em 'Salvar'. ",
	editBodyDescription: 'Edite os detalhes do corpo e clique em ‘Salvar’.',
	manageObject: 'Gerenciar objeto',
	relatedObjects: 'Objetos relacionados',

	/* Manage body objects */
	bro_manage_WCGWTask_title: 'Relacionar WCGWs',
	bro_manage_WCGWTask_instructions: 'Gerenciar os WCGWs aplicáveis',
	bro_manage_WCGWTask_noDataLabel: 'Nenhum resultado encontrado',

	/*Add/Edit ITGC*/
	addITGC: 'Adicionar ITGC',
	addNewITGC: 'Adicionar novo ITGC',
	addExistingITGC: 'Adicionar ITGC existente',
	addITGCDescription: 'Insira a descrição do ITGC.',
	itControlNameRequired: 'O nome do ITGC é obrigatório',
	frequencyRequired: 'A frequência é obrigatória',
	frequencyITGC: 'Selecionar frequência',
	nameITGC: 'Nome ITGC (obrigatório)',
	iTProcesslabel: 'processo de TI',
	editITGC: 'Editar ITGC',
	editITSP: 'Editar ITSP',
	editITGCDescription: 'Edite o ITGC e seus atributos associados',
	editITSPDescription: 'Edite o ITSP e seus atributos associados',
	viewITGC: 'Ver ITGC',
	viewITSP: 'Ver ITSP',
	itgcTaskDescription: 'Realize nossos testes projetados de ITGCs para obter evidência de auditoria apropriada e suficiente de sua eficácia operacional durante todo o período de confiança.',
	/**
	 * Add Edit ITGC
	 */
	addITSPDescription: 'Digite a descrição ITSP.',
	selectITRisk: 'Selecione o risco de TI (necessário)',
	itRiskRequired: 'Risco de TI (reqjuerido)',
	itspNameRequired: 'Nome ITSP (requerido)',
	itspTaskDescription: 'Personalize esta descrição de tarefa para desenhar a natureza, a época e a extensão dos procedimentos substantivos de TI para obter evidência de auditoria suficiente e apropriada de que os riscos de TI são tratados de forma efetiva durante todo o período de confiança.<br />Quando o procedimento substantivo de TI for executado a partir de uma data interina, desenhar e executar procedimentos para obter evidências adicionais de que os riscos de TI são abordados durante o período coberto por nossos procedimentos interinos até o final do período.<br />Concluímos com os resultados de nossos procedimentos substantivos de TI.',
	itspRequired: 'Nome ITSP é requerido',
	selectTestingStrategy: 'Conceber a natureza, a época e a extensão dos nossos testes de controles para obter evidência de auditoria suficiente e apropriada de que o controle funciona efetivamente conforme desenhado ao longo do período de confiança para prevenir ou detectar e corrigir distorções materiais ao nível da assertiva. <br /> Concluir sobre a efetividade operacional dos controles, avaliando os resultados de nossos testes de controles, inclusive quando ampliamos nosso tamanho de amostra e realizamos testes de controles compensatórios.',
	itControlNameTest: 'Teste {0}',

	/*Edit ITControl*/
	editITControl: 'Editar ITGC / ITSP',
	viewITControl: 'Ver ITGC / ITSP',

	/*Add/Edit ITRisk*/
	editITRisk: 'Edit IT risk',
	editITRiskDescription: 'Edite o risco de TI',
	viewITRisk: 'View IT risk',
	addITRisk: 'Adicionar risco de TI',
	addITRiskDescription: 'Insira a descrição do risco de TI',
	selectITProcess: 'Selecione o processo de TI (obrigatório)',
	itRiskName: 'Risco de TI',
	itRiskNameRequired: 'Risco de TI (obrigatório)',
	riskNameRequired: 'O risco de TI é necessário',
	processIdRequired: 'O processo de TI é necessário',
	itProcessRequired: 'Processo de TI (obrigatório)',
	hasNoITGC: 'Não existem ITGCs que endereçam o risco de TI',

	/*Edit Risk*/
	editRisk: 'Editar risco',
	viewRisk: 'Ver risco',

	/*Edit Control*/
	editControl: 'Editar controle',
	viewControl: 'Ver controle',
	scotRelatedControls: 'Controles relacionados a',
	applicationControl: 'Controle de aplicativos',
	iTDependentManualControl: 'Controle manual dependente de TI',
	noAapplicationControlAvailable: 'Sem controles de aplicativos',
	noITDependentManualControlAvailable: 'Sem controles ITDM',
	isIPEManuallyTested: 'A parte automatizada deste controle ITDM é apenas o uso de relatórios gerados pelo sistema que são testados substantivamente',

	/*Edit ITProcess*/
	editITSOProcess: 'Editar processo de TI/SO',
	viewITSOProcess: 'Exibir processo de TI/SO',

	/*Edit ITApplication*/
	viewITAppSO: 'Exibir IT App/SO',
	editITAppSO: 'Editar IT App/SO',
	strategy: 'Estratégia',
	nameRequired: 'Nome necessário',
	name: 'Nome',

	/*Snap shot*/
	currentVersion: 'Versão atual',
	compareVersion: 'Selecione uma versão para comparar',
	snapshotVersionNotAvailable: 'Não há versões disponíveis para comparação',
	snapshots: 'Imagem instantânea',
	sharedFormWarning: "Este é um formulário do Canvas compartilhado. Os objetos e evidências existem no engagement original e não serão adicionados a este engagement ao desvincular. Veja <a style='color: #467cbe' href='https://live.atlas.ey.com/#library/104/p/SL33184174-396647/C_33404446/C_38129691' target='_blank'>enablement aqui</a> para mais detalhes. ",
	fullView: 'Visualização completa',
	defaultView: 'Visualização padrão',
	print: 'Imprimir',
	version: 'Versão',
	navigationUnavailable: "A navegação não está disponível na visualização de Rastrear alterações e Atributos. Visualize 'Perguntas e respostas' para habilitar a navegação novamente. ",
	snapshotUpdate: 'Atualizado',
	snapshotNew: 'Novo',
	snapshotRemoved: 'Removido',
	snapshotRollforward: 'Criado na época do Roll-Forward',
	snapshotRestore: 'Criado no momento da Restauração',
	snapshotCopy: 'Criado no momento da cópia',

	/*Special Body*/
	priorPeriodAmount: 'Valor do período anterior',

	// Helix special body:
	helixScreenshotListLoading: 'Carregando capturas de tela...',
	helixScreenshotLoading: 'Carregando imagem de captura de tela...',
	helixScreenshotDeleting: 'Excluindo captura de tela...',
	helixNotesLoading: 'Carregando tickmarks...',
	helixNotesBoundingBoxShow: 'Mostrar anotações',
	helixNotesBoundingBoxHide: 'Ocultar anotações',
	helixNoteReferenceNumber: '#',
	helixNoteReferenceNumberPlaceholder: 'Digite o número de referência',
	helixNoteText: 'Nota',
	helixNoteTextPlaceholder: 'Digite o texto do tickmark',
	helixNoteAnnotate: 'Anotar',
	helixNoteAnnotateMessage: 'Selecione uma área na captura de tela para anotar, confirmar a anotação e clique na marca de verificação para salvar.',
	helixRemoveAnnotation: 'Excluir anotações',

	/* User lookup body */
	userLookupInstructionalText: 'Digite o nome ou e-mail e pressione enter para ver os resultados.',
	userLookupShortInstructionalText: 'Digite o nome ou e-mail e pressione enter',

	/*Guidance*/
	guidance: 'Orientação',
	noIncompleteBodies: 'Selecione um cabeçalho ou seção no menu de navegação para exibir conteúdo',
	noUnresolvedComments: 'Selecione o cabeçalho ou seção no menu de navegação para visualizar o conteúdo',
	addComment: 'Adicione comentário',

	/*Independence*/
	otherFormIndependenceMessage: 'O conteúdo deste formulário de independência foi atualizado e o usuário não efetuou login novamente desde que isso aconteceu. Como resultado, algumas respostas podem estar incompletas. O status de independência anterior foi mantido para referência.',
	override: 'Substituir',
	grantAccess: 'Conceder acesso',
	denyAccess: 'Negar acesso',
	overrideSmall: 'Substituir',
	grantAccessSmall: 'concessão de acesso',
	denyAccessSmall: 'negando acesso',
	status: 'Status',
	undefined: 'Indefinido',
	incomplete: 'Incompleto',
	noMattersIdentified: 'Sem assuntos identificados',
	matterIdentifiedPendingAction: 'Assunto Identificado -  Ação Pendente',
	matterResolvedDeniedAccess: 'Assunto resolvido - Acesso negado',
	matterResolvedGrantedAccess: 'Assunto resolvido - Acesso concedido',
	notApplicable: 'Não aplicável',
	restored: 'Restaurado',
	overridden: 'Substituído',
	priorNoMattersIdentified: 'Anterior - Sem Assuntos Identificados',
	priorMatterIdentifiedPendingAction: 'Anterior - Questão identificada - Ação pendente',
	priorMatterResolvedGrantedAccess: 'Anterior - Questão Resolvida - Acesso Concedido',
	priorMatterResolvedDeniedAccess: 'Anterior - Questão Resolvida - Acesso Negado',
	byOn: 'por {0} em',
	byLabel: 'por',
	onLabel: 'em',
	modifiedBy: 'Modificado por',
	reason: 'Razão',
	submit: 'Enviar',
	submitTemplate: 'Enviar modelo',
	independenceHoverText: 'Sua função deve ser Sócio Responsável, Sócio, ou Diretor Executivo para conceder, negar ou substituir o acesso para este usuário.',
	enterRationaleText: 'Digite o racional para',
	enterRationalePlaceholderText: 'Digite o texto racional',
	requiredRationaleText: 'Racional (necessário)',
	rationaleTextRequired: 'O racional é necessário',

	sharedExternalWarning: 'Este formulário é compartilhado por meio do Portal do cliente do Canvas e acessível por membros externos da equipe. Insira apenas respostas e comentários que devem ser compartilhados com membros externos da equipe.',
	independenceViewTemplateMessage: 'Este formulário serve como um modelo para a consulta de independência individual de cada membro da equipe. <br/> Ao concluir a consulta de independência, existem várias questões que se relacionam com os requisitos de independência aplicáveis à entidade sob auditoria para as quais cada membro da equipe deve prestar uma resposta. Selecione as respostas apropriadas a essas perguntas.  As respostas serão sincronizadas com a consulta de independência individual de cada membro da equipe. Se um membro da equipe selecionou uma resposta diferente, ele terá que reconfirmar sua independência ao retornar ao engagement. Se eles não entrarem novamente no engagement, seu status de independência e respostas anteriores serão preservados. <br /> Apenas usuários autorizados podem fazer alterações no modelo de independência. Fale com um administrador do engagement. Todas as alterações feitas devem ser enviadas, mesmo que sejam desfeitas manualmente antes do arquivamento.',

	/**
	 * FORM OBJECTS: SCOT-WCGW-CONTROL
	 */
	fo_instructionalText: 'Selecione os objetos que o Formulário do Canvas está documentando',
	fsro_instructionalText: 'Gerencie os objetos relacionados a esta Seção',
	relObj_title_risk: 'Riscos',
	relObj_title_riskType: 'Tipo do risco',
	fo_showOnlyRelated: 'Mostrar apenas objetos relacionados',
	scotsCount: 'SCOTs {0}',
	wcgwsCount: '{0} WCGWs',
	itsoCount: '{0} aplicativos  de TI/organização de serviços',
	controlsCount: 'Controles {0}',
	itControlsCount: 'controles de TI {0}',
	itGcCount: '{0} ITGCs',
	itSpCount: '{0} ITSPs',
	itProcessesCount: '{0} processos de TI',
	risksCount: '{0} Riscos',
	accountsCount: '{0} contas',

	stEntitiesCount: '{0} Entidades',

	componentsCount: '{0} Componentes',
	view: 'Visualizar',
	searchByScotName: 'Pesquisar pelo nome da SCOT',
	searchByWcgwName: 'Pesquisar pelo nome do WCGW',
	searchByITSOAppName: 'Pesquisar pelo nome do aplicativo de IT/SO',
	searchByControlName: 'Pesquisar pelo nome do Controle',
	searchByItControlName: 'Pesquisar pelo nome do Controle de TI',
	searchByItProcessName: 'Pesquise por nome do processo de TI',
	searchByRiskName: 'Pesquisar pelo nome do Risco',
	searchByAccountName: 'Pesquisar por nome da conta',
	searchBySTEntityName: 'Pesquisar por nome da entidade',
	searchByEstimateName: 'Pesquisar por nome da estimativa',
	searchByComponentName: 'Pesquisar por nome do Componente',
	noScotsAvailable: 'Nenhuma SCOT está disponível neste engagement.',
	noRisksAvailable: 'Não há riscos neste engagement.',
	noControlsAvailable: 'Não há controles disponíveis neste engagement.',
	noItControlsAvailable: 'Não há controles de TI disponíveis neste engagement.',
	noItProcessesAvailable: 'Não há processos de TI disponíveis neste engagement.',
	noItApplicationsAvailable: 'Não há aplicativos de TI disponíveis neste engagement.',
	noAccountsAvailableLabel: 'Nenhuma conta está disponível neste engagement.',
	noObjectsRelatedToForm: 'Nenhum objeto relacionado a este Formulário do Canvas',
	noDocumentControlsAvailable: 'Nenhum control está associado neste documento',
	noDocumentScotsAvailable: 'Nenhum SCOTs está associado a este documento.',
	noSTEntitiesAvailable: 'Nenhuma entidade disponível neste engagement.',
	noComponentsAvailable: 'Nenhum componente disponível neste engagement.',
	editObjectDescription: 'Editar a associação de objetos a este formulário',
	editObjectsLabel: 'Editar objetos',
	noITGCsOrITSPsHaveBeenIdentified: 'Nenhum ITGC ou ITSP foi identificado',
	noItProcessIdentified: 'Nenhum processo de TI foi identificado',
	noControlsIdentified: 'Nenhum controle foi identificado',
	noRelatedRisksIdentified: 'Não foram identificados riscos significativos ou de fraude relacionados',
	noItApplicationsIdentified: 'Nenhum aplicativo de TI foi identificado',
	noSCOTIdentified: 'Nenhuma SCOT foi identificada',
	noWCGWIdentified: 'Nenhum WCGW foi identificado',
	maxLimitLabel: 'O número máximo de objetos foi selecionado.',
	minLimitLabel: 'O número mínimo de objetos foi selecionado.',

	relatedITAppsTitle: 'Processos de TI e aplicações de TI relacionadas',
	relatedWCGWTasksTitle: 'WCGWs e tarefas relacionadas',
	noRelatedTasks: 'Sem tarefas relacionadas',
	noRelatedWcgw: 'Sem WCGWs relacionados',
	noRelatedControls: 'Sem controles relacionados',
	controlRelatedRisksTitle: 'Controles e riscos relacionados',
	sCOTRelatedRisksTitle: 'SCOTs e riscos relacionados',
	scotRelatedItApp: 'Aplicativos de TI relacionados a SCOT',
	relatedItApps: 'Aplicativos de TI relacionados',
	relatedRisksTitle: 'Riscos relacionados',
	relatedItRisksItProcessesTitle: 'ITGCs e processos de TI relacionados e riscos de TI',
	testingTitle: 'Testando',
	strategyTitle: 'Estratégia',
	yes: 'Sim',
	no: 'Não',
	noRelatedRisks: 'Nenhum risco significativo ou de fraude relacionado',
	closeAllComments: 'Fechar todos os comentários',
	closeComments: 'Fechar comentários',
	closeCommentsDescription: 'Todos os comentários abertos e limpos serão fechados. Tem certeza de que deseja fechar todos os comentários para este {0}?',
	addCanvasFormDigital: 'Digital',
	addCanvasFormCore: 'Núcleo',
	addCanvasFormNonComplex: 'Não Complexo',
	addCanvasFormComplex: 'Complexo',
	addCanvasFormListed: 'Listado',
	addCanvasFormGroupAudit: 'Auditoria de grupo',
	addCanvasFormPCAOBFS: 'PCAOB-FS',
	addCanvasFormPCAOBIA: 'PCAOB-IA',
	addCanvasFormStandards: 'Padrões',
	addCanvasFormLanguage: 'Idioma',
	addCanvasFormNoResultFound: 'Nenhum resultado encontrado',
	addCanvasFormStandardsNotSelectedMessage: 'Padrão é um campo necessário',
	addCanvasFormLanguageNotSelectedMessage: 'A linguagem é um campo obrigatório',

	/* Confidentiality */
	confidentialityPlaceholder: 'Selecione a confidencialidade',
	confidentiality: 'Confidencialidade',
	confidentialityTitle: 'Confidencialidade do documento',
	confidentialityText: "Defina o nível de acesso requerido para que este documento seja aberto. Os níveis de acesso são definidos por Administradores do Engagement na página 'Gerenciar equipes'. Se a confidencialidade já tiver sido definida para este documento, somente aqueles que podem abrir o documento podem alterá-lo. ",
	confidentialityNotOpenable: 'O documento não pode ser aberto porque seus direitos no engagement são insuficientes. Os níveis de acesso são definidos pelos Administradores do Engagement em Gerenciar Equipe.',
	confidentialityTargetNotOpenable: 'Documentos confidenciais só podem ser abertos a partir do engagement de origem.',
	backToCCP: 'De volta ao EY Canvas Client Portal',
	guidanceMessageBackToCCP: 'Após preencher este formulário, volte ao EY Canvas Client Portal e envie a solicitação a EY.',
	noProfileInformationFound: 'Nenhuma informação de perfil encontrada. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	confirmUpdate: 'Confirmar atualização',
	keepVersion: 'Mantenha esta versão',
	conflictDescription: '{0} editou este texto {1} desde que foi aberto. Selecione a versão que deve ser mantida.',
	currentConflictVersion: 'Versão atual',
	serverConflictVersion: 'Versão do servidor',
	conflictShowChanges: 'Mostrar mudanças',
	sectionViewTrackChangesDropdownPlaceholder: 'Selecione a versão',
	verifyingIndependence: 'Verificando o status de independência, por favor, espere.',
	creatingIndependenceForm: 'Criando formulário de independência.',
	meCallFailed: 'Falha ao recuperar informações do usuário. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	getUserByIdFailed: 'Falha ao recuperar o status de independência do usuário. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	independenceFormCreationFailed: 'Falha ao criar formulário de independência do usuário. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	gettingProfile: 'Obtendo informações de perfil, por favor, aguarde.',
	invalidDocumentId: 'O ID do documento é inválido. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	returnToEditMode: 'Retornar ao modo de edição',
	saveAndCloseButtonTitle: 'Salvar e Fechar',
	formCreationFailed: 'Falha ao criar formulário. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',

	/*Sign-off requirements*/
	signOffRequirements: 'Requisitos de assinatura',
	signoffRequirementsModalTitle: 'Requisitos de assinatura',
	signoffRequirementsModalDescription1: 'Ajuste os requisitos de aprovação executiva deste documento abaixo.',
	signoffRequirementsModalDescription2: 'Alguns requisitos de aprovação não podem ser ajustados porque são exigidos pelo EY Canvas.',
	signoffRequirementsModalSaveLabel: 'Salvar',
	signoffRequirementsModalCancelLabel: 'Cancelar',
	signoffRequirementsModalCloseLabel: 'Fechar',
	signoffRequirementsModalPICLabel: 'PIC',
	signoffRequirementsModalEQRLabel: 'EQR',

	/*<Ares>*/
	/* View changes */
	viewChanges: 'Ver alterações',
	viewChangesModalTitle: 'Ver alterações',
	documentModificationAlert: 'Esta atividade foi modificada pela última vez por',
	dismiss: 'Dispensar',

	/*Task List*/
	aresPageTitle: 'Ativação do EY Canvas FIT',
	aresPageSubtitle: 'Conclua as etapas abaixo com as informações solicitadas sobre sua auditoria.',
	summary: 'Sumário',
	aresNoDocumentFound: 'Nenhuma outra informação disponível para a atividade selecionada',
	taskSubTitleNoValue: 'Nenhuma descrição disponível',
	mainActivities: 'Principais atividades',
	unmarkComplete: 'Desmarcar concluído',
	markCompleteTitleTip: 'Marcar concluído',
	disableMarkCompleteTitleTip: 'Certifique-se de que todos os documentos relacionados sejam assinados por pelo menos um preparador e um revisor para marcar esta atividade como concluída',
	/*Activity Summary*/
	activitySummary: 'Resumo da atividade',
	selectedAnswers: 'Respostas selecionadas',
	allAnswers: 'Todas as respostas',
	incompleteResponses: 'Respostas incompletas',
	previous: 'anterior',
	next: 'próximo',
	viewAsLabel: 'Veja como',
	rolePreparerLabel: 'Preparador',
	roleDetailedReviewerLabel: 'Revisor de detalhe',
	roleGeneralReviewerLabel: 'Revisor Geral',
	roleEQRLabel: 'EQR',
	/*Simple Helix*/
	helixPlaceholder: 'O tipo de orientação 7 com URL de orientação é necessário para capturas de tela de hélice.',
	noNotesAvailable: 'Nenhum tickmark criado',
	addScreenshot: 'Adicionar captura de tela',
	replaceScreenshot: 'Substituir captura de tela',
	replaceFrameDescription: '`Revise a análise abaixo e clique em Substituir para substituir a captura de tela existente.`',
	addNote: 'Adicionar tickmark',
	notes: 'Tickmarks',
	noScreenshotsAvailable: 'Clique em {viewDataAnalytic} para começar',
	viewDataAnalytic: 'Ver análise de dados',
	/* Delete modal Helix screenshot*/
	modalTitle: 'Excluir captura de tela',
	sureDeleteBeforeName: 'Deseja excluir a captura de tela?',
	sureDeleteAfterName: 'Na exclusão da captura de tela, todas os tickmarks associadas também serão excluídas e essa ação não poderá ser desfeita.',

	/*uploadDocument body type */
	relateExistingDocuments: 'Relacionar documentos existentes',
	fromEngagementOr: 'deste/outro(s) projetos(s) ou',
	browse: 'navegar',
	toUpload: 'para carregar',
	signoffs: 'Aprovações',
	addDocument: 'Adicionar documentos',
	uploadDocument: 'Enviar documento',
	relateDocument: 'Relacionar documentos existentes',
	generateAccountRiskAssessmentPackage: 'Gerar grupo ALRA',
	relateDocumentsToBodyAresTitle: 'Relacionar documentos',
	discardLabel: 'Descartar',
	uploadDocumentLabel: 'Documento de upload',
	confirm: 'Confirmar',
	duplicateDocumentHeader: 'Um ou mais documentos com o mesmo nome já existem neste contrato (seja como Evidências ou arquivos temporários).',
	duplicateDocumentInstruction: "Selecione 'Sobregravar' para carregar o documento e substitua o arquivo existente ou 'Descartar' para cancelar. Se este arquivo existente estiver em Evidência, então o documento será enviado para o Evidence. Se o arquivo existente estiver em arquivos temporários, o documento será carregado em arquivos temporários. ",
	maxUploadFilesError: 'O sistema pode carregar no máximo 10 documentos ao mesmo tempo',
	/*</Ares>*/
	noTaskRelatedToThisDocument: 'Nenhuma tarefa relacionada a este documento',
	uncheckTrackChangesToSave: 'Unselect the track changes option to save',
	reviewRoleCloseCommentsTitle: 'Comentários não resolvidos',
	reviewRoleCloseCommentsDesc: 'Há comentários não resolvidos a serem abordados. Utilize o filtro para identificar facilmente os comentários não resolvidos.',

	/*Document Upload - PIC/EQR Required Body type*/
	requirementDetails: 'Detalhes do requisito',

	//Risk Factors
	riskFactor: 'Evento relevante e condição/risco de distorção',
	manageRisk: 'Gerenciar riscos',
	noRiskFactors: 'Não foram identificados eventos e condições/riscos relevantes de distorção',
	relateRiskFactorsToRisks: 'Determinar o significado de eventos e condições',
	riskType: 'Tipo de risco',
	relateToRisk: 'Relacionar-se com o risco',
	noRisksIdentified: 'Nenhum risco identificado',
	notDefined: 'Não definido',
	selectValidRiskType: 'Selecione um tipo de risco válido',
	newRisk: 'Adicionar novos riscos',
	notAROMM: 'Não é um risco de distorção material',
	describeRationale: 'Descrever a lógica',
	noRisksIdentifiedForTheSpecificRiskType: 'Não foram identificados {0}',
	addAnAccount: 'Associar conta adicional',
	selectAnAccount: 'Selecione uma conta',
	noAccountsHaveBeenIdentified: 'Nenhuma conta foi identificada.',
	accountSelected: 'conta',
	statementType: 'Tipo de declaração',
	selectAssertions: 'Selecione uma ou mais assertivas',
	noAssertionsIdentifiedForAccount: 'Nenhuma assertiva foi identificada para esta conta',
	relatedAssertions: 'Assertivas relacionadas',
	editAccount: 'Editar conta & divulgação',
	addNewDescription: 'Adicionar nova descrição',
	editRiskFactorDescription: 'Editar descrição',
	enterRiskFactorDescription: 'Insira a descrição do evento relevante e da condição / risco de distorção',
	riskFactorDescriptionRequired: 'Evento relevante e descrição da condição/risco de distorção necessária',
	riskFactorDescription: 'Evento relevante e descrição da condição/risco de distorção',
	createNewAccount: 'Criar nova conta',
	createAccountLabel: 'A conta foi criada com sucesso {0}',
	updateAccountLabel: 'Edições salvas com sucesso na conta {0}',
	deleteAccountLabel: 'foi deletado com sucesso',
	significanceLabel: 'Significância',
	provideRationale: 'Forneça uma justificativa para salvar sua seleção',
	clearRiskSignificance: 'Significado claro e descrição do risco',
	clearSignificance: 'Significado e descrição claros',

	// Account Summary
	unavailable: 'Indisponível',
	notAvailable: 'Não disponível',
	fraudRisk: 'Risco de fraude',
	significantRisk: 'Risco Significativo',
	significantRiskIndicator: 'RS',
	fraudRiskIndicator: 'RF',
	inherentRisk: 'RDM',
	inherentRiskIndicator: 'RDM',
	prioryearbalance: 'Saldo do período anterior: ',
	accounts: 'Contas',
	accountsOther: 'Conta - Outra',
	accountsSignDis: 'Divulgação significativa',
	xMateriality: 'Múltiplos de PM',
	xTEMateriality: 'x ET',
	estimateAssociated: 'Estimativa associada',
	designation: 'Designação',
	noAccountshasbeenIdentified: 'Nenhuma conta foi identificada.',
	noAccountsOtherhasbeenIdentified: 'Nenhuma outra conta identificada.',
	noAccountsSigfhasbeenIdentified: 'Nenhuma divulgação significativa identificada.',
	addOtherAccounts: 'Adicionar conta - Outra',
	addSignificantDisclosure: 'Adicionar divulgação significativa',
	pydesignation: 'Designação prévia: ',
	notapplicable: 'N/A',
	noApplicable: 'Não aplicável',
	changeDesignationMessage: 'Você está prestes a alterar a designação da conta',
	changeDesignationTitle: 'Alterar designação de conta',
	continue: 'continuar',
	currentYearBalance: 'Período atual',
	currentPeriodBalance: 'Período atual',
	priorYear: 'Ano anterior',
	priorYearDesignation: 'Designação do período anterior',
	priorYearEstimation: 'Estimativa do período anterior',
	priorPeriodChange: '% mudança',
	analytics: 'Analítico',
	notROMMHeader: 'Não é um risco de distorção relevante',
	manageEyCanvasAccounts: 'Gerenciar contas do EY Canvas',
	manageAccountMapping: 'Gerenciar mapeamento de contas',
	manageAccountMappingCloseButton: 'Use o botão na parte inferior da página para fechar.',
	manageAccountMappingToasterMessage: 'Não foi possível se conectar ao EY Helix. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	inherentRiskTypeChangeMessage: 'Ao alterar as assertivas da conta de relevantes para não relevantes, certas associações no Canvas, incluindo WCGWs para  assertivas, serão removidas e os PSPs serão rebaixados para OSPs. Clique em “Confirmar” se desejar prosseguir. Clique em “Cancelar” para retornar sem alterações.',

	analyticsIconDisabled: 'EY Helix não está habilitado para o seu engagement.',
	//Estimate
	estimatesTitle: 'Estimativas',
	relatedAccount: 'Contas relacionadas',
	relatedAccounts: 'Contas relacionadas',
	currentBalance: 'Saldo Atual',
	priorBalance: 'Saldo Anterior',
	unrelateEstimates: 'conta estimada não relacionada',
	manageEstimates: 'Gerenciar estimativas',
	noEstimatesCreated: 'Nenhuma estimativa foi criada.',
	createEstimates: 'Criar estimativa',
	estimateStarted: 'para começar',
	createEstimateDocument: 'Criar documentação de estimativa',
	noEstimatesFound: 'Nenhuma estimativa foi identificada',
	relateEstimateLink: 'Relacionar estimativas',

	//Journal Source
	noJournalEntrySourcesAreAvailable: 'Nenhuma fonte de entrada de diário está disponível.',
	jeSourceName: 'Fonte JE',
	jeSourceNameTooltip: 'Fonte JE',
	changeInUse: 'Mudança em uso',
	grossValue: 'Valor bruto das transações relacionadas',
	relatedTransactions: 'Número de transações relacionadas',
	relevantTransactions: 'Relevante para classe significativa de transações',
	expandAll: 'Expandir tudo',
	collapseAll: 'Recolher tudo',
	descriptionLabel: 'Forneça uma breve descrição da finalidade desta fonte',
	jeSourceTypesLabel: 'Os lançamentos contábeis manuais são registrados através desta fonte, sistema gerado ou manual?',
	journalEntries: 'Esta fonte é usada para registrar lançamentos contábeis não padronizados (ou seja, transações ou ajustes não recorrentes e incomuns)?',
	identifySCOTsLabel: 'Identifique as SCOTs associados à fonte JE (selecione todos os que se aplicam)',
	systemGeneratedLabel: 'Sistema gerado',
	manualLabel: 'Manual',
	bothLabel: 'Ambos',
	accountEstimateLabel: 'Est',
	addSCOTLabel: 'Adicionar SCOT',
	newSCOTLabel: 'Nova SCOT',
	addSCOTModalDescription: 'Você pode criar as SCOTs. As alterações serão aplicadas depois de salvas.',
	scotnameRequired: 'O nome da SCOT é requerido.',
	scotCategoryRequired: 'A categoria da SCOT é requerida.',
	estimateRequired: 'A estimativa é requerida.',

	jeSourcesLabel: 'Fontes de JE',
	jeSourcesToSCOTs: 'Fontes de JE para as SCOTs',
	scotsToSignificantAccounts: 'SCOTs para contas significativas',

	//Modal common labels.
	modalCloseTitle: 'fechar',
	modalConfirmButton: 'Salvar alterações',
	modalCancelTitle: 'Cancelar',
	modalSave: 'Salvar',
	modalSaveAndClose: 'Salvar e Fechar',
	modalSaveAndAdd: 'Salvar e adicionar',
	modalSaveAndCreateAnother: 'Salvar e criar outro',

	//Add & Manage Risks
	addNewRiskModalTitle: 'Adicionar novos riscos',
	manageRisksListModalTitle: 'Gerenciar riscos',
	riskInfoMessage: 'As alterações serão aplicadas uma vez salvas.',
	risksListInstructionalText: 'Você pode editar e excluir os riscos existentes. Você também pode adicionar um novo, se necessário.',
	risksListEmptyArray: 'Nenhum risco disponível. Adicione um novo risco para começar',
	addNewRiskButtonLabel: 'Adicionar novos riscos',
	labelRiskName: 'Nome de risco',
	riskDescriptionLabel: 'Descrição do risco',
	selectRiskType: 'Tipo de risco',
	requiredRiskName: 'Nome de risco é necessário',
	requiredRiskType: 'O tipo de risco é necessário',
	deleteRiskTrashLabel: 'Excluir risco',
	undoDeleteRiskTrashLabel: 'Desfazer excluir',
	notARommLabel: 'Nenhum risco de erro material',
	identifiedRiskFactors: 'Eventos/condições/riscos de distorção relevantes identificados, riscos de distorção material, riscos significativos e riscos de fraude.',
	noneIdentified: 'Nenhum identificado',
	countUnassociatedRisk: 'Os eventos/condições/riscos de distorção material não estão relacionados/não estão marcados como “Não é um risco de distorção material/Risco de distorção material”.',

	// Bar Chart / Account Summary
	accountsTotal: 'total de contas {0}',
	accountSummary: 'Resumo da conta',
	allAccounts: 'Todas as contas',
	significantAccountsBarChart: 'Contas significativas',
	limitedAccounts: 'Contas de risco limitadas',
	insignificantAccounts: 'Contas insignificantes',
	noAccountsHasBeenIdentifiedBarChart: 'Nenhum {0} foi identificado.',
	selectedTotalAccountsCounter: '{0}/{1} contas',
	identifyInsignificantAccounts: 'Identificar contas insignificantes',
	identifySignificantAccounts: 'Identificar contas significativas',
	identifyLimitedAccounts: 'Identificar contas de risco limitado',
	preInsigniAccounts: 'Conta anteriormente insignificante que não é mais inferior ao TE no período atual.',
	nonDesignatedInsignificant: 'Esta conta não pode ser designada como Insignificante. Clique na lista de designação para alterar a designação desta conta.',
	tolerableError: 'Erro tolerável não está disponível. Atualize a materialidade e tente novamente.',
	documentContainerLabel: 'Documento',
	clickcreateformtogenerate: 'Clique em {0} para gerar a documentação da conta de risco limitada.',
	createform: 'Criar formulário',
	createLimitedRiskAccountDocumentation: 'Criar documentação de conta de risco limitado',
	limitedAccountDocumentName: 'Documentação da conta de risco limitado',

	//Modal Confirm Switch account
	modalSwitchTitle: 'Alterações não salvas',
	modalConfirmSwitch: 'Confirmar',
	modalConfirmSwitchDescription: 'As alterações não são salvas e serão perdidas se você decidir continuar. Você quer continuar?',

	//Modal Edit Account
	manageAccountsModalTitle: 'Gerenciar contas do EY Canvas',
	editAccountLinkLabel: 'Editar conta',
	editAccountInstructionalText: 'Você pode editar ou excluir contas existentes do EY Canvas ou criar novas. As alterações serão aplicadas uma vez salvas.',
	selectAnAccountLabel: 'Selecionar conta',
	accountNameLabel: 'Nome da conta',
	accountLabel: 'conta',
	accountDesignationLabel: 'Designação de conta',
	accountStatementTypeLabel: 'Tipo de declaração',
	accountRelatedAssertionsLabel: 'Afirmações relacionadas',
	accountHasEstimateLabel: 'A conta foi impactada pela estimativa?',
	requiredAccountName: 'O nome da conta é necessário',
	requiredAccountDesignation: 'A designação da conta é necessária',
	requiredStatementType: 'O tipo de declaração é necessário',
	requiredRelatedAssertions: 'Selecione uma afirmação',
	pspIndexDropdownLabel: 'Selecione o índice PSP',
	removePSPIndexLabel: 'Remover o índice PSP',
	addPSPIndexLink: 'Adicionar índice PSP',
	pspIsRequired: 'É necessário índice PSP',

	//Delete account modal
	deleteAccount: 'Excluir conta',
	deleteAccountModalMessage: 'Tem certeza de que deseja excluir a conta selecionada?',
	cannotBeUndone: 'Isso não pode ser desfeito.',
	guidedWorkflow: 'Ativação do EY Canvas FIT',
	scotSummary: 'Resumo da SCOT',
	scopeAndStrategy: 'Escopo & estratégia',
	ToggleSwitch: {
		Inquire: 'Perguntar',
		Completed: 'Concluído',
		isOn: 'Sim',
		isOff: 'Não '
	},
	navExecution: 'Excecução',
	navCanvasEconomics: 'EY Canvas Economics',
	navOversight: 'Substituição de EY Canvas',
	navConclusion: 'Conclusão',
	navTeamMemberIndependence: 'Independência do membro da equipe',
	navGroupAudit: 'Gerenciamento de grupo',
	navGroupActivityFeed: 'Feed da Atividade de grupo',
	navPrimaryStatus: 'Status principal',
	navComponentStatus: 'Status do componente',
	navGroupStatus: 'Status do grupo',
	navEngagementManagement: 'Gestão do engagemnet',
	navProfile: 'Perfil',
	navItSummary: 'Resumo de TI',
	nav440GL: 'Alterações após a data do relatório',
	navGroupStructureSummary: 'Estrutura do grupo',
	navGroupInstructionSummary: 'Instruções do grupo',
	navGroupInvolvement: 'Envolvimento do grupo',
	navNotApplicable: 'Não é aplicável',
	cropScreenshot: 'Corte de captura de tela',
	cropScreenshotModalDescription: 'Corte a captura de tela para incluir apenas partes relevantes. O corte removerá as anotações existentes, os tickmarks serão mantidas e poderão ser anotadas novamente. O corte não pode ser desfeito.',
	crop: 'Corte',
	replace: 'Substituir',
	nameTheScreenshot: 'Nome da captura de tela',
	nameLabel: 'Nome',
	takeScreenshot: 'Adicionar captura de tela',
	measurementBasis: 'Base de medição',
	MeasurementBasisMessage: 'Com base no mapeamento de dados subjacente do EY Helix, parece que a base de medição selecionada não está na posição esperada (por exemplo, receita antes dos impostos em uma posição de débito). Considere se: ',
	measurementBasisProjectMappedCorrectly: 'Os dados no projeto EY Helix estão mapeados corretamente,',
	measurementBasisAppropriateValue: 'Uma base de medição diferente pode ser apropriada, ou',
	measurementBasisAdjustValue: 'Pode ser apropriado ajustar o valor da base de medição conforme fornecido abaixo',
	basisValueFromHelix: 'Trial balance value',
	rationaleDeterminationLabel: 'Justificativa de como esse valor foi determinado',
	loginInstructions: 'faça login e siga as instruções para configurar sua conta.',
	gotoText: 'Ir para',
	asOfDate: 'A partir da data',
	annualizedBasisValue: 'Valor base anualizado',
	basisValue: 'Valor base',
	isAnnualizedAmountRepresentative: 'O valor base anualizado é representativo da quantia que se espera ser relatada no final do período?',
	isAnnualizedAmountRepresentativeForAssetsOrEquity: 'O valor é representativo do valor esperado a ser relatado no final do período de auditoria?',

	enterExpectedFinancialPerioadAmount: 'Digite o valor esperado no final do período de auditoria',
	enterRationaleAmountDetermined: 'Digite o racional de como essa quantidade foi determinada',
	printNotAvailable: '{0} não tem conteúdo e, portanto, nenhuma informação é mostrada',
	canvasFormPrintNotAvailable: 'Impressão de formulário do Canvas não está disponível no momento. Por favor, tente novamente ou entre em contato com o Help Desk se o erro persistir.',
	saving: 'Salvando...',
	removing: 'Remover..',
	activitySummaryTitle: 'Resumo da atividade',
	currentLabel: 'Atual',
	PMLabel: 'PM',
	planningMaterialityLabel: 'Materialidade de planejamento',
	TELabel: 'TE',
	tolerableErrorLabel: 'Erro tolerável',
	SADLabel: 'SAD',
	SADNominalAmountLabel: 'Valor nominal do SAD',
	PriorLabel: 'Anterior',
	editRichText: 'Editar texto',
	noTypeTwoResponseAvailable: 'Nenhuma resposta disponível. <br /> Clique em {clickHere} para responder.',
	clickHere: 'aqui',
	helixNavigationTitle: 'Vá para a página configuração EY Helix para vincular ou configurar um projeto EY Helix',
	helixNavigationLink: 'Vá para EY Helix',
	measurementBasisValue: 'Valor da base de medição',
	inlineSaveUnsavedChanges: 'Existem alterações não salvas, deseja continuar?',
	rationaleIncomplete: 'Racional incompleto',
	projectMismatchDisplayMessageOnDataImport: 'Seu projeto principal do EY Helix foi alterado. Por favor, confirme as configurações do EY Helix e importe os dados do novo projeto.',
	importSuccess: 'Dados do EY Helix importados com sucesso.',
	importHelix: 'Clique em Importar para importar dados gerais do razão contábil do EY Helix.',
	importLabel: 'Importar',
	reImportLabel: 'Reimportar',
	lastImportedBy: 'Importado pela última vez por',
	lastImportedOn: 'Última importação em',
	dataImportedFromProjec: 'Dados importados do projeto',
	reImportConfirmationTitle: 'Re-importar dados do EY Helix',
	reImportConfirmationMessagePart1: 'A reimportação de dados do EY Helix irá alterar os dados existentes ou adicionar novos dados dentro de atividades relevantes e isso não pode ser desfeito.',
	reImportConfirmationMessagePart2: 'Para obter mais informações e um resumo de como o processo de reimportação de dados afeta a habilitação do EY Canvas FIT, consulte o EY Atlas.',
	defineRisksTitle: 'Definir riscos',
	assessAndDefineRisksTitle: 'Avaliar e definir riscos',
	identifiedRiskFactorsTitle: 'Eventos/condições identificados e riscos relacionados: ',
	descriptionIncompleteLabel: 'Descrição incompleta',
	noRiskHasBeenRelatedMsg: 'Nenhum risco foi relacionado',
	rationaleIncompleteMsg: 'Racional incompleto',
	loading: 'Carregando...',
	importInProgressLabel: 'Importação em andamento. Isso pode levar vários minutos. Atualize a página para ver o status atualizado.',
	importInProgressMessage: 'Importação de dados do Helix em andamento. Atualize a página para ver o status da importação.',
	importHelixProjectError: 'Ocorreu um erro ao importar dados do EY Helix. Verifique se o status do projeto EY Helix está aparecendo como Analytics disponível, atualize a página e clique em Importar ou Reimportar novamente. Se o problema persistir entre em contato com o Help Desk.',
	importDeletionConfirmationMsg: 'Tem certeza de que deseja excluir a importação de dados do EY Helix? Excluir importação excluirá os dados existentes nas atividades relevantes e isso não poderá ser desfeito.',
	deletePreviousImport: 'Excluir importação do EY Helix',
	//Assess Risks - Summary Page
	assessRisksAccordionLabel: 'Riscos associados',
	assessRisksNoItemsFound: 'Nenhum risco foi identificado.',
	assessRiskAccountsAndRelatedAssertions: 'Contas e assertivas relacionadas',
	assessRiskNoAccountsLinked: 'Nenhuma conta associada',
	accountRiskAssessmentSummary: 'Contas e divulgações',
	// Flow chart
	flowchartTitle: 'Fluxograma',
	launchFlowchart: 'Fluxograma de lançamento',
	clickherelink: 'Clique aqui',
	orderToCashPlacement: 'Ordem para colocação de caixa',
	orderToCashPlacementMessage: 'o Departamento de Programas negocia acordos com novos clientes e negocia a renovação de contratos e/ou modificação de contratos com clientes existentes. Um contrato contém detalhes como: preços, termos e garantias',
	saveFlowChart: 'Salvar',
	newstep: 'Nova etapa',
	zoomIn: 'Ampliar zoom',
	zoomOut: 'Diminuir zoom',
	resetZoom: 'Redefinir zoom',
	toogleInteractivity: 'Interatividade do Toogle',
	fitView: 'Ajustar visualização',
	numberOfSteps: 'Número de etapas',
	flowchartSuccessfullyCreated: 'Fluxograma criado com sucesso.',
	flowchartLinkedEvidenceMessage: 'Este fluxograma foi criado em outro engaement. O acesso ao fluxograma deste engagement será removido quando as evidências forem desvinculadas.',
	flowchartSmartEvidenceSourceIdNullMessage: 'Nenhuma SCOT disponível.',
	noTaskDocumentAvailableFlowchart: 'Este documento é um arquivo temporário. Por favor, relacione a uma tarefa como evidência para acessar os detalhes do fluxograma',
	// Control Attributes
	controlAttributes: 'Atributos de controle',
	noControlAttributes: 'Nenhum controle disponível',
	flowchartStepMoremenu: 'Mais menu',
	createControlAttributes: 'Nenhum atributo de controle disponível.<br />Clique em {clickHere} para criar um novo atributo de controle.',
	createNewControlAttribute: 'Novo atributo',
	editControlAttribute: 'Editar atributo',
	createControlAtttributeInstructions: "Insira os detalhes do atributo abaixo e selecione <b>\'Salvar e fechar\'</b> para finalizar. Para criar outro atributo, selecione <b>\'Salvar e criar outro\'.</b> Os atributos serão classificados com base no índice de atributos. ",
	editControlAttributeInstructions: "Edite os detalhes do atributo abaixo e selecione <b>\'Salvar\'</b> para finalizar. Os atributos serão classificados com base no índice de atributos. ",
	editAttributeButtonLabel: 'Editar atributo',
	deleteAttributeButtonLabel: 'Excluir atributo',
	deleteControlAttributeInstructions: 'Tem certeza de que deseja excluir o atributo selecionado? Esta ação não pode ser desfeita.',
	// Control Attributes Form
	requiredAttributeIndexLabel: 'Índice de atributos (obrigatório)',
	requiredAttributeDescriptionLabel: 'Descrição do atributo (obrigatório)',
	errorMessageAttributeIndexRequired: 'Obrigatório',
	errorMessageAttributeDescriptionRequired: 'Obrigatório',
	errorMessageAttributeDescriptionMaxLength: 'A resposta contém {#} caracteres que excede o máximo de {##} caracteres. Ajuste a descrição reduzindo o texto ou a formatação e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	errorMessageAttributeTestingTypesRequired: 'Obrigatório',
	proceduresLabel: 'Procedimentos a serem realizados',
	modalRequiredProceduresLabel: 'Procedimentos a serem realizados (obrigatório)',
	attributeTestingTypesLabel: {
		inquiry: 'Investigação',
		observation: 'Observação',
		inspection: 'Inspeção',
		reperformance: 'Reexecução/recálculo'
	},

	/*CRA Badge*/
	ir: 'IR',
	cr: 'CR',
	cra: 'CRA',
	incompleteCra: 'CRA incompleto',
	incomplete: 'Incompleto',

	//Progess bar labels
	savingProgress: 'Salvando…',
	discardChangesLabel: 'Descartar alterações',
	removeFromBody: 'Remover do corpo',
	uploading: 'Enviando…',
	uploadComplete: 'Envio completo',
	downloadComplete: 'Download completo',
	processing: 'Processando…',

	/* ISA BODIES */
	/* Common */
	deleteEntityConfirmation: 'Tem certeza de que deseja excluir <b>{0}</b>? Essa ação não pode ser desfeita.',
	/* ITAPP-SCOT */
	searchScot: 'Pesquisar SCOT',
	addITApp: 'Adicionar aplicativo de TI',
	relatedItApp: 'Aplicativos de TI relacionados',
	itApplicationHeader: 'Aplicativos de TI',
	scotNoDataPlaceholder: 'Nenhuma informação disponível',
	noScotsOrControlsPlaceholder: 'Sem SCOTs ou controles relacionados; {noScotsOrControlsPlaceholderEditAssoc}.',
	noScotsOrControlsPlaceholderEditAssoc: 'editar associações',
	noScotsOrControlsPlaceholderTarget: 'Nenhum SCOT ou Controles relacionados.',
	scotHeader: 'SCOTs',
	controlsHeader: 'Controles',
	controlsApplicationHeader: 'Aplicativo',
	controlsITDMHeader: 'ITDM',
	itAppScotNoDataPlaceholderLabel: 'Nenhum aplicativo de TI foi adicionado.',
	itAppScotNoDataPlaceholder: 'Nenhum aplicativo de TI foi adicionado.<br /> Para começar, clique em {itAppScotNoDataPlaceholderAddItApp}',
	itAppScotNoDataPlaceholderAddItApp: 'Adicionar aplicativo de TI',
	editItAppOption: 'Editar aplicativo de TI',
	removeItAppOption: 'Excluir aplicativo de TI',
	viewItAppOption: 'Ver aplicativo de TI',
	editScotsISA: 'Editar SCOT',
	viewScotISA: 'Ver SCOT',
	viewControlISA: 'Exibir controles',
	scotAndRelatedControls: 'SCOTs e controles',
	otherControls: 'Outros controles',
	controlTypeLabel: 'Tipo de controlo',

	/*SCOT-ITAPP*/
	addOrRelateItAppPlaceholder: '{identifyRelatedItApps} ou documento que o {documentScotHasNoItApps}.',
	identifyRelatedItApps: 'Identificar aplicativos de TI relacionados',
	documentScotHasNoItApps: 'A SCOT não tem aplicativos de TI relacionados',
	correctScotDocumentationPlaceholder: 'A SCOT foi designada como não sendo suportada por um aplicativo de TI. Aplicativo e/ou controle(s) ITDM foram associados a esta SCOT, {correctScotDocumentation}.',
	correctScotDocumentation: 'revisitar a designação de que nenhum aplicativo de TI suporta esta SCOT',
	controlsWithoutRelatedItApps: 'Controles sem aplicativos de TI relacionados',
	controlsWithRelatedItApps: 'Controles com aplicativos de TI relacionados',

	/*AddEditITApplication */
	saveAndCreateNewButtonTitle: 'Salvar e criar novo',
	instructionalMessage: 'Adicione o aplicativo de TI e selecione os SCOTs relacionados. Controles associados relacionados ao aplicativo de TI, quando aplicável.',
	ITApplicationNamePlaceholder: 'Nome do aplicativo de TI (obrigatório)',
	scotDropdownPlaceholderText: 'Selecione os SCOTs a serem relacionados ao aplicativo de TI',
	selectScotPlaceholderText: 'Selecione os SCOTs a serem relacionados ao aplicativo de TI',
	selectControlPlaceholderText: 'Selecione os controles a serem relacionados ao aplicativo de TI',
	noRelatedScotsPlaceholderText: 'Nenhum SCOT relacionado',
	noRelatedControlsPlaceholderText: 'Sem controles relacionados',
	CreateSOModelTitle: 'Adicionar organização de serviços',
	CreateSOInstructionalMessage: "Insira os detalhes da nova organização de serviço abaixo e selecione '<b>{0}</b>' para finalizar. Para criar outra organização de serviço, selecione '<b>{1}</b>'.`, //'Crie uma nova organização de serviço e associe SCOTs e controles relacionados a ela. ",
	saveAndCloseLabel: 'Salvar e fechar',
	saveAndCreateLabel: 'Salve e crie outro',
	SONamePlaceholder: 'Nome da organização de serviço (obrigatório)',
	SOSelectScotPlaceholderText: 'Selecione SCOTs relacionados à organização de serviços',
	SOSelectControlPlaceholderText: 'Selecione controles relacionados à organização de serviços',
	CreatedSOLabel: 'SO adicionado',
	createdITAppLabel: 'Aplicativo de TI adicionado',
	searchNoResultFoundText: 'nenhum resultado encontrado',
	searchNoResultsFoundText: 'Nenhum resultado encontrado',
	iTApplicationNameRequired: 'O nome do aplicativo de TI é obrigatório',
	soNameRequired: 'O nome da organização de serviço é obrigatório',
	editITAppDesctiptionLabel: 'Edite o aplicativo de TI e os SCOTs e controles associados',
	editSODescriptionLabel: "Edite os detalhes da organização do serviço abaixo e selecione <b>'Salvar'</b> para finalizar. ",
	viewITApplication: 'Ver aplicativo de TI',
	itApplicationName: 'Nome do aplicativo de TI',
	serviceOrganizationName: 'Nome da organização do serviço',
	newItApplication: 'Novo aplicativo de TI',

	/*Add/Edit ITProcess*/
	itProcessName: 'Nome do processo de TI',
	addItProcessDescription: 'Crie um processo de TI',
	addItProcess: 'Adicionar processo de TI',
	itProcessNameRequired: 'O nome do processo de TI é obrigatório',
	editItProcess: 'Editar processo de TI',
	editItProcessDescription: 'Edite o processo de TI',
	viewItProcess: 'Visualizar processo de TI',
	taskTitle: 'Entenda e documente o processo de TI: {0}',
	taskDescription: 'Documente nossa compreensão do processo de TI. Anexe o formulário relevante como evidência para apoiar nossa compreensão do processo de TI.<br />Quando os ITGCs aparecerem na seção Atributos da Tarefa, execute procedimentos de passo a passo para confirmar nossa compreensão dos ITGCs e avaliar seu desenho e implementação. Anexe as evidências de nossos procedimentos.',
	newItProcess: 'Novo processo de TI',
	noITProcessesFound: 'Nenhum processo de TI foi identificado',

	/* IT Process - Task */
	itProcessSearchPlaceholder: 'Search IT process',
	itProcessHeader: 'Processo de TI',
	itProcessTasksHeader: 'Tarefas',
	itProcessAddUPD: 'Adicionar UDP',
	itProcessEditItProcess: 'Editar processo de TI',
	itProcessRelateUDP: 'Relacionar UDP',
	itProcessViewProcess: 'Visualizar processo de TI',
	itProcessNoDataPlaceholder: 'Nenhum processo de TI foi adicionado.<br /> Para iniciar, clique em {addItProcess}.',
	itProcessSourceInstructionalText: 'Pelo menos um UDP deve estar relacionado ao processo de TI. {itProcessSourceInstructionalTextCreateUdp} ou {itProcessSourceInstructionalTextRelateUdp}',
	itProcessSourceInstructionalTextCreateUdp: 'Criar um novo UDP',
	itProcessSourceInstructionalTextRelateUdp: 'relacionar um UDP existente.',
	itProcessTargetInstructionalText: 'Nenhum UDP está relacionado ao processo de TI.',

	/* IT APP IT PROCESSES RELATION*/
	itApplicationHeaderRelate: 'Aplicativos de TI',
	itProcessesHeaderRelate: 'Processos de TI',
	itAppNoDataPlaceHolderLabel: 'Nenhum aplicativo de TI foi identificado.',
	itAppNoDataPlaceHolder: 'Nenhum aplicativo de TI foi identificado.<br />{identifyItApp}',
	identifyItApp: 'Identifique um aplicativo de TI.',
	itProcessNoDataPlaceHolderRelationLabel: 'Nenhum processo de TI foi identificado.',
	itProcessNoDataPlaceHolderRelation: 'Nenhum processo de TI foi identificado. <br /> {identifyItProcess}',
	identifyItProcess: 'Identifique um processo de TI.',
	editItApp: 'Editar aplicativo de TI',
	deleteItApp: 'Excluir aplicativo de TI',
	drag: 'Arraste o processo de TI para os aplicativos de TI relacionados',
	// editItProcess: 'Edit IT process',
	deleteItProcess: 'Excluir processo de TI',
	unassociatedProcess: '{0} processos não relacionados',
	unassociatedItApplications: '{0} aplicativos de TI não relacionados',
	showOnlyUnrelated: 'Mostrar apenas não relacionado - {0}',
	searchItProcess: 'Pesquisar processo de TI',
	searchItApplication: 'Pesquisar aplicativo de TI',
	itProcessesLabel: 'processos de TI',
	itApplicationsLabel: 'aplicativos de TI',
	showAllItAplications: 'Mostrar todos os aplicativos de TI',
	showAllItProcesses: 'Mostrar todos os processos de TI',
	relatedToLabel: "Lista de todos os <span class='child-entity-count'>{count}</span> <span class='child-entity-name'>{child}</span> relacionados a <span class='parent- nome-objeto-entidade'>{parent}</span> ",

	/* IT Process > IT Risk */
	itProcessItRiskNoDataPlaceholder: 'Nenhum processo de TI foi identificado.<br/>{identifyItProcess}',
	itProcessItRiskNoDataPlaceholderTarget: 'Nenhum processo de TI foi identificado.',
	itApplication: 'Aplicativos de TI',
	itGC: 'ITGCs',
	addItRiskBtnTitle: 'Adicionar risco de TI',
	itProcessItRiskUnrelatedITGC: 'ITGCs não associados',
	itProcessItRiskUnrelatedITGCUppercase: 'ITGCs não associados',
	itProcessItRiskNoRisksNoControlsPlaceholder: 'Não há exigência de incluir os riscos de TI ou ITGCs para este processo de TI porque não há controles de aplicativos ou ITDM com uma avaliação de projeto efetiva relacionada aos aplicativos de TI associados a este processo de TI.',
	itProcessItRiskNoRisksControlsPlaceholder: 'Com base no aplicativo identificado e nos controles de ITDM {itRiskIdentify} para este processo de TI',
	itRiskIdentify: 'Os riscos de TI devem ser identificados',
	itProcessItRiskItProcessContentTitle: 'Riscos de TI e ITGCs',
	itProcessItRiskItRiskNoItgcRequiredPlaceholder: 'Não há ITGCs que abordem o risco de TI.',
	itProcessItRiskItRiskItgcRequiredPlaceholder: 'Todos os riscos identificados devem ter pelo menos um ITGC identificado ou uma designação de que o risco de TI não possui ITGCs.<br/>Identifique um {newITGC} ou {existingITGC} que resolva o risco de TI ou indique que existem {noItRisksIdentified} que abordam o risco de TI.',
	noItRisksIdentified: 'sem ITGCs',
	newITGC: 'novo ITGC',
	existingITGC: 'ITGC existente',
	unrelatedItGCModalMessage: 'Exclua os ITGCs não associados que não são mais necessários.',
	unrelatedItGCModalNoDataPlaceholder: 'Nenhum ITGC não associado',
	removeItRisk: 'Remover o risco de TI',
	deleteItRiskConfirmation: 'Tem certeza de que deseja remover o risco de TI <b>{0}</b>? Esta ação excluirá o risco de TI e não poderá ser desfeita.',
	relateItGcTitle: 'Relacionar ITGC',
	relateItGcEntityTitle: 'Risco de TI',
	relateItGcDescription: 'Selecione os ITGCs relevantes para o risco de TI.',
	relateItGcSearchPlaceholder: 'Pesquisar ITGC',
	relateItGcShowSelectedOnlyText: 'Mostrar apenas ITGCs relacionados',
	relateItGcNoDataPlaceholder: 'Sem ITGCs disponíveis. Crie um novo ITGC para continuar.',
	relateITSPTitle: 'Relacionar ITSP',
	relateITSPDescription: 'Selecione os ITSPs relevantes para o risco de TI.',
	relateITSPSearchPlaceholder: 'Pesquisar pelo nome ITSP',
	relateITSPShowSelectedOnlyText: 'Mostrar apenas ITSPs relacionados',
	relateITSPNoDataPlaceholder: 'Não há ITSP disponível. Crie um novo ITSP para continuar.',

	/* IT Process Task Relationship */
	relateUDP: 'Relacionar UDP',
	relateUDPDescription: 'Selecione as tarefas UDP relevantes para o processo de TI.',
	relateUDPListHeaderItemName: 'Nome da tarefa',
	relateUDPSearchPlaceholder: 'Pesquisar pelo nome da tarefa',
	relateUDPNoResultsFoundPlaceholder: 'Nenhum resultado encontrado',
	relateUDPCountLabel: '{0} tarefas',
	relateUDPClose: 'Fechar',
	relateUDPShowOnlyRelatedTasks: 'Mostrar apenas tarefas relacionadas',
	relateUDPNoDataFoundPlaceholder: 'Sem tarefas disponíveis',
	relateUDPNoDataPlaceHolder: 'Nenhum processo de TI foi identificado',

	/* ITGC test strategy */
	itProcessItRiskItGcWithoutDesignEffectiveness: 'ITGCs sem eficácia de design',
	searchItGC: 'Pesquisar processo de TI',
	itGCNoDataPlaceHolder: 'Nenhum processo de TI foi identificado',
	addItRisks: 'Adicione riscos de TI',
	itDMHeader: 'ITDM',
	itAppHeader: 'aplicativo de TI',
	itTestHeader: 'Teste',
	itTestingHeader: 'Teste',
	itgcHeader: 'ITGCs',
	controlsSelectedHeader: 'Controles selecionados para teste',
	iTRisksAndITGCs: 'Riscos de TI e ITGCs',
	NoITGCForITRiskPlaceholder: 'Não existem ITGCs no ambiente de TI para lidar com esse risco de TI',
	ITGCsNotIdentifiedRiskNoITGCs: 'ITGCs não foram identificados para este risco. {identifyAnITGC} ou designar que o {itRiskHasNoITGCs}.',
	identifyAnITGC: 'Identifique um ITGC',
	itRiskHasNoITGCs: 'O risco de TI não tem ITGCs',

	/**
	 * IT SO > SCOT
	 */
	searchItSO: 'Organização do serviço de pesquisa',
	addItSOBtnTitle: 'Adicionar organização de serviço',
	itSoNoDataPlaceHolder: 'Nenhum serviço da organização foi identificado.<br/><a>{identifyAnSo}<a/>',
	noItSoDataPlaceHolder: 'Nenhum serviço da organização foi identificado.',
	identifyAnSo: 'Identifique uma serviço da organização',
	soHeader: 'Organização de serviços',
	editSO: 'Editar organização de serviço',
	deleteSO: 'Excluir organização de serviço',
	viewSO: 'Ver serviço da organização',
	controlRelatedToSO: 'Controles relacionados ao SO',

	/**
	 * Manage IT SP
	 */
	addITSP: 'Adicionar ITSP',
	searchPlaceholderManageITSP: 'Pesquisar processo de TI',
	noManageITSPDataPlaceholder: 'Nenhum processo de TI foi identificado',
	itRiskColumnHeader: 'Riscos de TI',
	itDesignEffectivenessHeader: 'Eficácia do projeto',
	itTestingColumnHeader: 'Teste',
	itGCColumnHeader: 'ITGCs',
	itSPColumnHeader: 'ITSPs',
	searchClearButtonTitle: 'Claro',
	itProcessItRiskUnrelatedITSP: ' ITSPs não associados',
	manageITSPUnrelatedITSPUppercase: 'ITSPs não associados',
	unrelatedITSPModalMessage: 'Exclua ITSPs não associados que não são mais necessários.',
	unrelatedITSPModalNoDataPlaceholder: 'Nenhum ITSP não associado',
	noITGCPlaceholderMessageFragment1: 'Todos os riscos identificados devem ter pelo menos um ITGC identificado ou uma designação de que o risco de TI não possui ITGCs: ',
	noITGCPlaceholderMessageFragment2: 'Identifique um',
	noITGCPlaceholderMessageFragment3: 'novo ITGC',
	noITGCPlaceholderMessageFragment4: 'ou',
	noITGCPlaceholderMessageFragment5: 'ITGC existente',
	noITGCPlaceholderMessageFragment6: 'que aborda o risco de TI ou indica que existem',
	noITGCPlaceholderMessageFragment7: 'sem ITGCs',
	noITGCPlaceholderMessageFragment8: 'que abordam o risco de TI',
	addNewITSP: 'Adicionar novo ITSP',
	addExistingITSP: 'Adicionar ITSP existente',
	noITSPPlaceholderMessageFragment1: 'Se avaliarmos os ITGCs como ineficazes ou determinarmos que não existem ITGCs para lidar com o risco de TI, podemos realizar procedimentos de teste substantivos de TI (ITSPs) para obter garantia razoável de que o risco de TI dentro do processo de TI associado ao ITGC ineficaz não foi explorado.',
	noITSPPlaceholderMessageFragment2: 'Identifique um novo ITSP',
	noITSPPlaceholderMessageFragment3: 'ou',
	noITSPPlaceholderMessageFragment4: 'relacionar um ITSP existente',
	noITSPPlaceholderMessageFragment5: '.',
	noITGCsExitForITRisk: 'Não existem ITGCs para o risco de TI.',
	noITSPExitForITRisk: 'Nenhum ITSP foi identificado para o risco de TI.',
	manageITSPItemExpansionMessage: 'Riscos de TI',
	noITGCExists: 'Não existem ITGCs que endereçam o risco de TI.',
	iTGCName: 'Nome ITGC',
	itSPName: 'Nome ITSP',
	operationEffectiveness: 'Eficácia da operação',
	savingLabel: 'Salvando',
	deletingLabel: 'Deletando',
	removingLabel: 'Removendo',
	itFlowModalDescription: 'Vá para o {itSummaryLink} para editar/remover esses objetos que não são mais aplicáveis para o engagement.',
	itSummaryLink: 'Tela de resumo de TI',
	manageITSPYes: 'Sim',
	manageITSPNo: 'Não',

	understandITProcess: 'Entendimento dos processos de TI',
	activity: 'Atividade',
	unsavedPageChangesMessage: 'Você tem alterações não salvas que serão perdidas se você optar por continuar. Tem certeza de que deseja sair desta página?',
	unsavedChangesTitle: 'Alterações não salvas',
	unsavedChangesLeave: 'Sair desta página',
	unsavedChangesStay: 'Fique nesta página',

	notificationDownErrorMessage: 'O recurso Notificações está temporariamente indisponível. Atualize a página e tente novamente. Se esta mensagem persistir, entre em contato com o Help Desk.',
	notificationUpbutSomeLoadingErrorMessage: 'Ocorreu um erro técnico que está impedindo o funcionamento do recurso Notificações. Atualize a página e tente novamente.',
	markCompleteError: 'Todos os documentos apresentados requerem a aprovação de pelo menos um preparador e um revisor.',
	markCompleteDescription: 'Todos os documentos devem ser assinados por pelo menos um preparador e um revisor para marcar a atividade como concluída. ',
	lessthan: 'Menor que',
	openingFitGuidedWorkflowFormError: 'Não é possível abrir o formulário de ativação do EY Canvas FIT',
	timeTrackerErrorFallBackMessage: 'O recurso Controle de tempo está temporariamente indisponível. Atualize a página e tente novamente. Se esta mensagem persistir, entre em contato com o Help Desk.',
	timeTrackerLoadingFallbackMessage: 'O recurso Controle de tempo está temporariamente indisponível. Em breve estará disponível.',
	priorPeriodRelateDocument: 'Relacionar evidências do período anterior',
	selectedValue: 'Valor selecionado',
	serviceGateway: 'Porta de entrada de serviço',
	docNameRequired: 'O nome não pode estar vazio',
	docInvalidCharacters: 'O nome não pode incluir: */:<>\\?|"',
	invalidComment: 'Não foi possível adicionar o comentário. Se selecionar vários itens em uma lista, selecione apenas um único item e tente novamente. Se o problema persistir, atualize a página e tente novamente ou entre em contato com o Help Desk.',
	inputInvaildCharacters: 'A entrada não pode incluir a seguinte sequência de caracteres: */:<>\\?|"',

	// FIT Navigation panel
	relatedActivities: 'Atividades relacionadas',
	backToRelatedActivities: 'Voltar para atividades relacionadas',
	backToMainActivities: 'Voltar para as atividades principais',
	formOptions: 'Opções de formulário',

	// FIT Sharing
	shareActivity: 'Compartilhar atividade',
	shareLabel: 'Compartilhar',
	shareInProgress: 'Compartilhamento em andamento',
	manageSharing: "O compartilhamento desta atividade requer a permissão do usuário 'Gerenciar o compartilhamento da habilitação do EY Canvas FIT'. Navegue até a página 'Gerenciar equipe' para gerenciar permissões ou entre em contato com outro membro da equipe ",
	dropdownPlaceholderSA: 'Selecione o engagement com o qual compartilhar',
	fitSharingModalInfo: 'Compartilhe esta atividade com uma ou mais atividades do mesmo engagement ou de outro engagement no mesmo espaço de trabalho. Se as atividades selecionadas ainda não estiverem compartilhadas, as respostas nas atividades selecionadas abaixo serão substituídas. Se a atividade for compartilhada, apenas uma poderá ser selecionada e as respostas nesta atividade serão substituídas.',
	lastModifiedDate: 'Última modificação em: ',
	noActivityToShare: 'Nenhuma atividade disponível para compartilhar',
	activityNotShared: '{0} não foi compartilhado',
	activityShareSuccessfull: '{0} compartilhado com sucesso',
	sharedWithAnotherFITActivity: 'Esta atividade é compartilhada com outra atividade',
	sharedActivityWithAnotherCanvas: 'Compartilhe atividades com outra ativação do EY Canvas FIT',
	shareActivityModalTitle: 'Compartilhar atividade',
	showRelationshipsTitle: 'Mostrar relacionamentos',
	shareActivityEngagement: 'Engagement',
	shareActivityRelationshipsModalTitle: 'Relacionamentos de atividades compartilhadas',
	shareActivityWorkspaceHeading: 'Esta atividade está sendo compartilhada com os engagements e atividades relacionadas abaixo no mesmo workspace.',
	shareModalOkTitle: 'Compartilhar',
	shareModalContinueLabel: 'Continuar',
	selectedActivityInfoLabel: 'no engagmeent selecionado foi modificado pela última vez em: ',
	noSharedActivityInfoLabel: 'Este engagement não possui outro documento deste mesmo tipo para compartilhar.',
	alreadyHasSharedActivityInfoLabel: 'A atividade selecionada já está compartilhada com outras atividades. Compartilhar a atividade atual sincronizará as respostas da atividade selecionada com a atividade atual.',
	selectActivityResponsesForSharingLabel: 'Selecione quais respostas do documento devem substituir as outras: ',
	selectActivityResponsesForCurrentRadioLabel: 'Compartilhe respostas do documento atual com o documento selecionado acima',
	selectActivityResponsesForSelectedRadioLabel: 'Compartilhe as respostas do documento selecionado acima com o documento atual',
	selectActivityResponsesWarningEarlierTimeLabel: "The current activity was modified at an earlier time compared to the selected engagement's activity. Please consider this before confirming the sharing option's below the table.",
	selectActivityResponsesWarningModifiedMoreRecentlyLabel: 'A atividade atual foi modificada mais recentemente em comparação com a atividade do documento selecionado. Considere isso antes de confirmar a opção de compartilhamento acima.',
	selectActivityUnsuccessfulMessage: 'Falha ao compartilhar. Por favor tente novamente. Se o problema persistir, entre em contato com o Help Desk da EY.',
	otherEngagemntDropdownlabel: 'Outros engagements no espaço de trabalho: ',
	documentSearchPlaceholder: 'Documento de pesquisa',
	showOnlySelected: 'Mostrar apenas selecionados',

	//FIT Copy
	copyLabel: 'Cópia',
	copyActivity: 'Cópia da atividade',
	copyInProgress: 'Cópia em andamento',
	fitCopyModalInfo: 'Copiar respostas desta atividade para uma ou mais atividades do mesmo engagement ou de outro engagement na mesma área de trabalho.',
	dropdownPlaceholderCA: 'Selecione o engagement para o qual copiar',
	noCopyActivityInfoLabel: 'Este engagement não possui documento deste mesmo tipo para copiar.',
	copyActivityHoverLabel: 'Esta atividade já está compartilhada com outras atividades e não pode ser copiada',
	copyActivityWarningEarlierTimeLabel: 'A atividade atual foi modificada anteriormente em comparação com a atividade do engagement selecionado. Considere isso antes de confirmar as opções de cópia.,',

	//Unlink
	unlinkModalTitle: 'Desvincular atividade',
	unlinkModalDescription: 'Tem certeza de que deseja desvincular a atividade selecionada?',
	unlinkLabel: 'Desvincular',
	insufficientPermissionsLabel: 'Permissão insuficiente',
	unlinkFailMessage: 'Falha ao desvincular. Atualize e tente novamente. Se o problema persistir, entre em contato com o Help Desk da EY.',
	unlinkSuccessfulMessage: 'Desvinculação com sucesso',
	unlinkInProgressLabel: 'Desvinculação em andamento',
	unlinkError: 'Erro ao desvincular',
	unlinkInProgressInfo: 'Desvinculação em andamento. Isso pode levar até quinze minutos. Após a desvinculação ser concluída, este formulário precisará ser fechado e aberto novamente.',

	/** Manage scot modal labels */
	scotName: 'Nome da SCOT',
	scotCategory: 'Categoria da SCOT',
	estimate: 'Estimativa',
	noScotsAvailablePlaceHolder: 'Nenhuma SCOT disponível. Adicione uma nova SCOT para começar',
	addScotDisableTitle: 'Preencha todos os dados da SCOT para adicionar uma nova SCOT',
	deleteScotTrashLabel: 'Excluir SCOT',
	undoDeleteScotTrashLabel: 'Desfazer exclusão',
	scotNameValidationMessage: 'O nome da SCOT é obrigatório',
	scotCategoryValidationMessage: 'A categoria da SCOT é obrigatória',
	scotWTTaskDescription: '<p>Para todas as SCOTs rotineiras e não rotineiras e processos de divulgação significativos, confirmamos nosso entendimento a cada período realizando procedimentos de passo a passo. Além disso, para auditorias PCAOB, realizamos procedimentos de passo a passo de SCOTs de estimativa.Para todos os SCOTs quando adotamos uma estratégia de confiança de controles e para controles que tratam de riscos significativos, confirmamos que os controles relevantes foram projetados e implementados adequadamente. Confirmamos que nossa decisão de adotar uma estratégia de confiança de controles ainda é apropriada.Concluímos que nossa documentação descreve com precisão a operação da SCOT e identificamos todos os WCGWs apropriados, incluindo riscos decorrentes do uso de TI e controles relevantes (quando aplicável). Para estimativa de SCOTs quando usamos uma estratégia apenas substantiva, determinamos se nossa compreensão da estimativa SCOT é apropriada com base em nossos procedimentos substantivos.</p>',

	relate: 'Relacionar',
	unrelate: 'Não relacionado',
	related: 'Relacionada',
	relatedSCOTs: 'SCOTs relacionadas',
	thereAreNoSCOTsIdentified: 'Não há SCOTs identificadas',
	selectSCOTsToBeRelated: 'Selecione as SCOTs a serem relacionados',

	//OAR Tables
	OARBalanceSheet: 'Balanço patrimonial',
	OARIncomeStatement: 'Demontração do resultado',
	OARCurrentPeriod: 'Data da análise',
	OARAmountChangeFrom: 'Mudar de',
	OARPercentageChangeFrom: '% mudança de',
	OARNoDataAvailable: 'Não há dados disponíveis. Revise a página {0} e importe os dados para prosseguir.',
	OARAnnotationLabel: 'Clique para revisar o motivo de alterações inesperadas ou falta de alterações esperadas',
	OARAnnotationSelectedIcon: 'Razão do documento para mudanças inesperadas ou falta de mudanças esperadas',
	OARAnnotationModalTitle: 'Anotação',
	OARAnnotationModalPlaceholder: 'Itens de documento que parecem incomuns, mudanças inesperadas ou falta de mudanças esperadas.',
	OARWithAnnotationLabel: 'Documentação de mudanças inesperadas',
	OARAnnotation: 'Anotação',
	OARAccTypeWithAnnotationCountLabel: '{0} anotações dentro do tipo de conta',
	OARSubAccTypeWithAnnotationCountLabel: '{0} anotações dentro do subtipo de conta',
	OARColumnA: 'A',
	OARColumnB: 'B',
	OARColumnC: 'C',
	OARComparative1Period: 'Data comparativa 1',
	OARComparative2Period: 'Data comparativa 2',
	OARExpand: 'Expandir classe de conta',
	OARCollapse: 'Recolher classe de conta',
	OARHelixNavigationLink: 'Acesse o EY Helix para obter informações adicionais',
	OARPrintNoDataAvailable: 'Não há dados disponíveis',
	OARAdjustedBalance: 'Saldo Ajustado',
	OARLegendLabel: 'Valores com * indicam que incluem um ajuste. Navegue até o módulo de Ajuste para mais detalhes.',
	OARAccountType: 'Tipo de conta',
	astrixLabel: '*',

	//OAR Helix integration
	helixIntegrationModalDescription: 'Este é um texto pendente de definição',
	OSJETabText: 'Outro lado do journal entry',
	activityAnalysisTabText: 'Análise de atividades',
	preparerAnalysisTabText: 'Análise do preparador',
	accountMetricsTabText: 'Métricas da conta',
	noAnalyticsData: 'Nenhuma análise disponível para exibição',

	printActivitiesTitle: 'Imprimir atividades',
	printActivitiesModalInfo: 'Por favor, selecione quais atividades você gostaria de incluir.',
	printActivitiesModalConfirmButton: 'Combinar PDF',
	printActivitiesDropdownLabel: 'Atividades FIT',
	printActivitiesAll: 'Tudo',
	oarSetupText: 'Vá para a página {0} para vincular ou configurar um projeto do EY Helix',
	helixNotAvailable: 'EY Helix não está disponível para o seu engagement.',
	dragDropUploadPlaceholder: 'Arraste e solte um ou mais documentos ou clique em <span>{addDocument}</span>',

	noTaskAssociatedToastMessage: 'Como o formulário do Canvas está em arquivos temporários, os documentos adicionados também foram adicionados a arquivos temporários',

	// chart labels.
	assets: 'Ativos',
	liabilities: 'Passivo',
	equity: 'Patrimônio líquido',
	revenues: 'Receitas',
	expenses: 'Despesas',
	noAccountsAvailable: 'Nenhuma conta disponível',

	// ALRA
	ALRAFilterByAccount: 'Filtro por conta',
	ALRANoRecords: 'Nenhum resultado encontrado',
	ALRAAssertions: 'Assertivas',
	ALRAInherent: 'Fatores de risco inerente',
	ALRAHigher: 'Fatores de risco alto',
	ALRAAccountDisclosure: 'Contas/Divulgações',
	ALRAType: 'Tipo',
	ALRAName: 'Nome',
	ALRARisks: 'Riscos',
	ALRAC: 'C',
	ALRAEO: 'E/O',
	ALRAMV: 'M/V',
	ALRARO: 'R&O',
	ALRAPD: 'P&D',
	ALRAR: 'R',
	ALRANoRisksAssociated: 'Nenhum risco relacionado a esta conta',
	ALRAAccountsDisclosureName: 'Nome das contas/divulgações',
	ALRAHigherRisk: 'Risco alto',
	ALRAHigherInherentRisk: 'Risco inerente mais alto',
	ALRAHigherRiskCode: 'H',
	ALRALowerRisk: 'Risco baixo',
	ALRALowerInherentRisk: 'Risco inerente mais baixo',
	ALRALowerRiskCode: 'L',
	ALRALimitedRiskAccount: 'A conta foi identificada como de risco limitado',
	ALRAInsignificantRiskAccount: 'A conta foi identificada como insignificante',
	ALRADesignations: 'Designações',
	ALRABalances: 'Saldos',
	ALRADesignation: 'Designação',
	ALRAAnalysisPeriod: 'Data da análise',
	ALRAxTE: 'xET',
	ALRAPercentageChangeFrom: '% de alteração de',
	ALRAPriorPeriodDesignation: 'Designação de período anterior',
	ALRAPriorPeriodEstimate: 'Estimativa do período anterior',
	ALRAComparativePeriod1: 'Data comparativa 1',
	ALRAComparativePeriod2: 'Data comparativa 2',
	ALRASelectUpToThreeOptions: 'Selecione até 3 opções',
	ALRASelectUpToTwoOptions: 'Selecione até 2 opções',
	ALRAValidations: 'Validações',
	ALRANoSignOffs: 'Sem assinaturas',
	ALRAIncompleteInherentRisk: 'Risco inerente incompleto',
	ALRARelatedDocuments: 'Documentos relacionados',
	ALRAGreaterExtent: 'Maior extensão',
	ALRALesserExtent: 'Menor extensão',
	ALRARiskRelatedToAssertion: 'Risco associado',
	ALRAContributesToHigherInherentRisk: 'Risco associado e contribui para o maior risco inerente',

	// Assess inherent risk
	HigherRiskAssertionWithoutRisksThatContributesToTheHigherInherentRisk: 'A assertiva é identificada como risco inerente mais alto sem pelo menos um risco que contribua para o risco inerente mais alto. Associe o(s) risco(s) e identifique quais riscos contribuem para o maior risco inerente da assertiva.',

	//MEST - Multi-entity account Execution Type selection listing
	account: 'Conta',
	taskByEntity: 'Tarefa por entidade',
	bodyInformation: 'Você deve clicar em Importar Conteúdo abaixo para salvar quaisquer alterações.',

	/*user search component*/
	seachInputRequired: 'Entrada de pesquisa obrigatória',
	nameOrEmail: 'Nome ou e-mail',
	emailForExternal: 'E-mail',
	noRecord: 'Nenhum resultado encontrado',
	userSearchPlaceholder: 'Digite o nome ou e-mail e pressione Enter para ver os resultados.',
	userSearchPlaceholderForExternal: 'Digite o e-mail e pressione Enter para ver os resultados.',
	clearAllValues: 'Limpar todos os valores',
	inValidEmail: 'Por favor insira um e-mail válido',

	//reactive frame
	maxTabsLocked: 'Máximo de guias permitidas atingido. Solte e feche uma das guias para abrir uma nova.',
	openInNewTab: 'Abrir em nova aba',
	unPinTab: 'Desafixar guia',
	pinTab: 'Fixar Guia',
	closeDrawer: 'Fechar gaveta',
	minimize: 'Minimizar',

	accountHeader: 'Contas',
	sCOTSummaryAccountNoDataLabel: 'Cada SCOT deve estar relacionada a pelo menos uma conta ou divulgação significativa. Selecione uma conta ou divulgação significativa existente para relacionar a esta SCOT',
	sCOTSummaryNoDataLabel: 'Nenhuma SCOT foi criada',
	scotSearchNoResultsFound: 'No results found',
	scotSummary225TabsName: {
		[0]: {
			label: 'Mostrar por conta '
		},
		[1]: {
			label: 'Mostrar por SCOT '
		}
	},

	// Display Account Balances
	currentPeriodAccountBalance: 'Saldo da conta do período atual: ',
	priorPeriodAccountBalance: 'Saldo da conta do período anterior: ',

	ALRANoResults: 'Nenhum resultado encontrado. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	associatedRomsCount: 'Número total de riscos associados: {0}',
	alraMessage: "A resposta de designação de conta não está alinhada com a designação em 'Editar conta e divulgação ",
	estimateCategoryResponseNotAlignedToDesignation: "A resposta da categoria da estimativa não está alinhada com a designação em 'Editar estimativa' ",


	// Analytics Overview
	analyticsOverviewTitle: 'Visão geral do Analytics',
	noSignificantAccountRecords: 'Nenhuma conta significativa foi criada.',
	noSignificantAccountMapped: 'Nenhuma conta significativa está mapeada para a entidade selecionada.',
	noLimitedAccountMapped: 'Nenhuma conta limitada está mapeada para a entidade selecionada.',
	openAnalyticDocumentation: 'Documentação analítica aberta',
	openLimitedRiskAccountDocumentation: 'Abra a documentação da conta de risco limitado',
	associatedSCOTs: 'SCOTS associadas: ',
	analysisPeriodLabel: 'Data de análise {0}',
	analysisPeriodChangeLabel: '% de alteração de {0}',
	xTELabel: 'xET',
	risksLabel: 'Riscos',
	comparativePeriod1: 'Data comparativa 1 {0}',
	analysisPeriodTitle: 'Data da análise',
	analysisPeriodChangeTitle: '% de alteração de',
	comparativePeriodTitle: 'Data comparativa 1',
	noAccountAvailable: 'Nenhuma conta disponível',

	// Estimates
	titleEstimateCategory: 'Categoria da estimativa',
	titleRisks: 'Riscos',

	voiceNoteNotAvailable: 'Nota de voz e gravação de tela não estão disponíveis na visualização de gaveta. Mude para a visualização em tela inteira para usar esses recursos.',

	financialStatementType: {
		[1]: {
			label: 'Ativo'
		},
		[2]: {
			label: 'Ativo circulante'
		},
		[3]: {
			label: 'Ativo não circulante'
		},
		[4]: {
			label: 'Passivo'
		},
		[5]: {
			label: 'Passivo circulante'
		},
		[6]: {
			label: 'Passivo não circulante'
		},
		[7]: {
			label: 'Patrimônio líquido'
		},
		[8]: {
			label: 'Receita'
		},
		[9]: {
			label: 'Despesa'
		},
		[10]: {
			label: 'Receita/(despesa) não operacional'
		},
		[11]: {
			label: 'Outros Resultados Abrangentes (ORA)'
		},
		[12]: {
			label: 'Outros'
		},
		[13]: {
			label: 'Tipo da conta'
		},
		[14]: {
			label: 'Subtipo da conta'
		},
		[15]: {
			label: 'Classe da conta'
		},
		[16]: {
			label: 'Subclasse da conta'
		},
		[17]: {
			label: 'Receita/(despesa) líquida'
		}
	},
	accountTypes: {
		[1]: {
			label: 'Conta significativa'
		},
		[2]: {
			label: 'Conta de risco limitado'
		},
		[3]: {
			label: 'Conta insignificante'
		},
		[4]: {
			label: 'Outra conta'
		},
		[5]: {
			label: 'Divulgação Significativa'
		}
	},
	noClientDataAvailable: 'Sem dados disponíveis',

	analysisPeriod: 'Data da análise',
	comparativePeriod: 'Data comparativa',
	perchangeLabel: '% mudança',

	entityCreateAccountLabel: 'Criar conta e divulgação',
	insignificantAccount: 'Conta insignificante',
	noAccountRecords: 'Nenhuma conta foi identificada',
	noAccountsForEntity: 'Nenhuma conta ou divulgação é mapeada para a entidade selecionada.',
	noLimitedRiskAccountRecords: 'Nenhuma conta de risco limitado disponível.',
	createAccount: 'Criar uma conta',
	createDocument: 'Criar documento',
	noAccountResults: 'Nenhuma conta identificada.',
	createGroupInvolvementDocument: 'Criar formulário de envolvimento',
	chooseVersionsToCompare: 'Escolha a versão para comparar',
	noTrackChangesOption: 'Nenhuma versão com controle de alterações disponível',
	trackChangesDefaultMessage: "Selecione uma versão no menu suspenso 'Escolher versão para comparar' para continuar. ",
	whichRiskContributeToHigherRisk: 'Que risco(s) contribuem para a assertiva de risco mais elevado?',

	//multi-entity Entity List
	createMultiEntity: 'Nova entidade',
	editMultiEntity: 'Editar entidade',
	noEntitiesAvailableCreateNewLink: 'Clique aqui',
	noEntitiesAvailable: 'Nenhuma entidade foi criada. {noEntitiesAvailableCreateNewLink} para começar',
	noEntitiesFound: 'Nenhum resultado encontrado',
	createMultiEntityProfile: 'Criar perfil de entidade',

	createEntity: 'Criar entidade',
	includeEntities: 'A lista de multi-entidades deve incluir pelo menos uma entidade. {createEntity} para começar.',
	//multi-entity table
	multiEntityCode: 'Índice padrão da entidade',
	multiEntityName: 'Nome da Entidade',
	multiEntityGroup: 'Grupo da Entidade',
	multiEntityActions: 'Ações',
	relateMultiEntityUngrouped: 'Desagrupado',
	selectAll: 'Selecionar tudo',
	entitiesSelected: 'entidades selecionadas',
	entitySelected: 'entidade selecionada',
	meNoEntitiesAvailable: 'Nenhuma entidade disponível',
	meSwitchEntities: 'Trocar entidades',
	meSelectEntity: 'Selecione a entidade',
	allEntities: 'Todas as entidades',
	noEntitiesIdentified: 'Nenhuma entidade identificada',
	contentDeliveryInProcessMessage: 'Entrega de conteúdo em andamento. Pode levar até dez minutos para que o conteúdo seja entregue.',
	importContent: 'Importar conteúdo',
	profileSubmit: 'Envio de perfil',
	importPSPs: 'Importar PSPs',
	contentUpdateInsufficienRolesLabel: 'Funções insuficientes para atualizar o conteúdo. Trabalhe com um administrador do engagement para obter direitos suficientes.',
	// MEST Switcher
	meEntitySwitcher: 'Alternador de entidade',
	//Error Boundary
	errorBoundaryMessage: 'Ocorreu um erro. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	sectionName: 'Nome da Seção',
	maxLength: 'O texto não pode exceder {number} caracteres.',
	required: 'Obrigatório',
	yearAgo: 'ano atrás',
	yearsAgo: 'anos atrás',
	monthAgo: 'mês atrás',
	monthsAgo: 'meses atrás',
	weekAgo: 'semana atrás',
	weeksAgo: 'semanas atrás',
	daysAgo: 'dias atrás',
	dayAgo: 'dia atrás',
	today: 'Hoje',
	todayLowercase: 'hoje',
	yesterday: 'Ontem',
	approaching: 'se aproximando',

	associatedToInherentRiskFactor: 'Associado ao fator de risco inerente',

	createMissingDocument: 'Criar documento ausente',
	createMissingDocumentBody: "Clique em 'Confirmar' para criar o documento para os itens relacionados atualmente faltando um documento. ",
	documentCreationSuccessMsg: 'Criação do documento em andamento. Atualize a página para atualizações.',

	noRisksRelatedToAssertion: 'Não há riscos relacionados a esta assertiva da conta.',

	noAssertionsRelatedToAccount: 'Nenhuma assertiva relacionada a esta conta',

	sharing: 'Compartilhamento',

	risksUnrelatedToAccountAssertion: 'Riscos não relacionados à assertiva da conta',
	cantCompleteTask: 'Os documentos associados à tarefa relacionada não possuem assinaturas. Abra a tarefa, preencha os documentos ausentes e tente Marcar como Concluída novamente.',
	cantCompleteTasksTitle: 'Não foi possível marcar como concluído',
	ok: 'OK',
	documentsEngagementShareLabel: 'Selecione o documento dentro do engagement para compartilhar com',
	documentsEngagementCopyLabel: 'Selecione o documento dentro do engagement para copiar',
	lastModifiedon: 'Última modificação em',
	newAccountAndDisclosure: 'Nova conta & divulgação',
	newAccountORDisclosure: 'Nova conta ou divulgação',

	externalDocuments: 'Documentos Externos',
	noExternalDocumentsAvailable: 'Nenhum documento externo disponível',
	addExternalDocuments: 'Adicionar documentos externos',
	relateExternalDocuments: 'Relacionar documentos externos',

	helixNotMappedToAccount: 'Os dados do EY Helix não estão mapeados para esta conta. Atualize o mapeamento e reimporte os dados para continuar.',
	trackChangesNotAvailableForSpecialBodyDisplayMessage: 'A funcionalidade de alterações controladas não está disponível para as respostas abaixo.',
	noDocumentRelatedObjectsApplicable: 'Os objetos não precisam ser associados a este formulário de fluxo de trabalho guiado',
	helixViewerLoader: 'Carregando o visualizador do Helix...',
	trackChangesViewDefaultMessage: 'Certas respostas não possuem funcionalidade de controle de alterações. Isso será indicado com a seguinte mensagem nos detalhes da atividade subjacente: "A funcionalidade de alterações controladas não está disponível para as respostas abaixo." Consequentemente, a falta de notificação das alterações abaixo não indica que as alterações foram feitas.',

	//Relate task modal
	relateTasksTitle: 'Relacionar tarefas',
	taskLocationLabel: 'Localização da tarefa',
	relateTaskInstructionalText: 'Adicione ou remova tarefas às quais o documento deve estar relacionado. Se o documento for uma evidência, removê-lo da última tarefa irá movê-lo para arquivos temporários.',
	noResultFound: 'Nenhum resultado encontrado.',
	relatedTaskCounter: '{0} tarefa',
	relatedTasksCounter: '{0} tarefas',
	onlyShowRelatedTasks: 'Mostrar apenas tarefas relacionadas',
	relateTaskName: 'Nome da tarefa',
	relateTaskType: 'Tipo',

	/*Relate Entities*/
	relateEntitiesTitle: 'Relacionar entidades',
	relateEntitiesSearchPlaceholder: 'Digite para pesquisar por nome',
	relateEntitiesName: 'Nome da entidade',
	relateEntitiesIndex: 'Índice padrão da entidade',
	relatedEntitiesCounter: '{0} entidades',
	relatedEntityCounter: '{0} entidades',
	onlyShowRelatedEntities: 'Mostrar apenas entidades relacionadas',
	entity: 'Entidade',

	step01: 'Passo 01',
	step02: 'Etapa 02',
	shareActivityStep1Description: 'Selecione o engagement e o documento',
	shareActivityStep2Description: 'Escolha quais respostas do documento devem substituir as outras',
	documentsShareLabel: 'Compartilhe a resposta do documento selecionado abaixo com o restante dos documentos.',
	selectedActivity: 'Atividade selecionada',
	sharedHoverLabel: 'Esta atividade já está compartilhada com outras atividades. Compartilhar esta atividade sincronizará as respostas desta atividade com todas as atividades compartilhadas',
	noAssertionsRelatedLabel: 'Nenhuma assertiva relacionada.',

	// Bulk mark complete:
	bulkMarkCompleteInstructionalText: 'Todos os documentos devem ser assinados por pelo menos um preparador e um revisor para marcar a atividade como concluída.',
	bulkMarkCompleteEngagementColumn: 'Engagement',
	bulkMarkCompleteDocumentsMissingSignOffs: 'Faltando assinaturas. Clique em {bulkMarkCompleteMissingSignOffsClickableText} para assinar.',
	bulkMarkCompleteMissingSignOffsClickableText: 'aqui',
	bulkMarkCompleteNoAccessToEngagement: 'Você não tem acesso ao engagement dessa tarefa',
	bulkMarkCompleteInProgressMessage: 'Processo está em andamento. Pode levar até dez minutos. Atualize para atualizações',
	bulkMarkCompleteRelatedDocumentsModalTitle: 'Assinaturas de documentos',
	bulkMarkCompleteFilterUnreadyTasks: 'Mostrar apenas tarefas com documentos com assinaturas faltando.',
	bulkMarkCompleteNotAllowedModalTitle: 'Não foi possível marcar como concluído',
	bulkMarkCompleteNotAllowedModalDescription: 'Você precisa selecionar pelo menos uma tarefa para marcar como concluída',
	bulkMarkCompleteRelatedDocumentsModalDescription: 'Para concluir a tarefa selecionada, todos os documentos devem ser assinados por pelo menos um preparador e um revisor.',
	bulkMarkCompleteRelatedDocumentsModalRefreshSignoffs: 'Atualizar assinaturas e notas',
	selectedTaskCounter: '({0}) tarefa selecionada',
	selectedTasksCounter: '({0}) tarefas selecionadas',

	// Mark complete (old):
	markCompleteNotAllowedModalDescription: 'Os documentos associados à tarefa relacionada estão faltando assinaturas. Abra a tarefa, complete os documentos ausentes e tente marcar como concluído novamente.',
	markCompleteInstructionalText: 'Todos os documentos devem ser assinados por pelo menos um preparador e um revisor para marcar a atividade como concluída.',

	// Adobe Analytics
	aaCookieConsentTitle: 'Bem-vindo ao',
	aaCookieContentPrompt: 'Você deseja permitir cookies?',
	aaCookieConsentExplanation: '<p>Em adição aos cookies que são estritamente necessários para operar este website, utilizamos os seguintes tipos de cookies para melhorar a sua experiência e os nossos serviços: <strong>Cookies funcionais</strong> para melhorar a sua experiência (por exemplo, lembrar configurações), <strong>Cookies de desempenho</strong> para medir o desempenho do site e melhorar sua experiência, <strong>cookies de publicidade/segmentação</strong>, que são definidos por terceiros com quem executamos campanhas publicitárias e nos permitem fornecer anúncios relevantes para você.</p><p>Revise nossa <a target="_blank" href="https://www.ey.com/en_us/cookie-policy">política de cookies</a> para obter mais informações.</p>',
	aaCookieConsentExplanationWithDoNotTrack: '<p>Em adição aos cookies que são estritamente necessários para operar este site, utilizamos os seguintes tipos de cookies para melhorar a sua experiência e os nossos serviços: <strong>Cookies funcionais</strong> para melhorar a sua experiência (por exemplo, lembrar configurações), <strong>Cookies de desempenho</strong> para medir o desempenho do site e melhorar sua experiência, <strong>cookies de publicidade/segmentação</strong>, que são definidos por terceiros com quem executamos campanhas publicitárias e nos permitem fornecer anúncios relevantes para você.</p><p>Detectamos que você ativou a configuração Não Rastrear em seu navegador; como resultado, os cookies de publicidade/segmentação são desativados automaticamente.</p><p>Revise nossa <a target="_blank" href="https://www.ey.com/en_us/cookie-policy">política de cookies </a> para obter mais informações.</p>',
	aaCookieConsentDeclineOptionalAction: 'Eu recuso cookies opcionais',
	aaCookieConsentAcceptAllAction: 'Eu aceito todos os cookies',
	aaCookieConsentCustomizeAction: 'Personalizar cookies',
	aaCookieConsentCustomizeURL: 'https://www.ey.com/en_us/cookie-settings',

	// Cookie Settings
	cookieSettings: {
		title: 'Configurações de cookies',
		explanation: 'Por favor forneça seu consentimento para o uso de cookies em ey.com e na plataforma My EY. Selecione um ou mais dos tipos de cookies listados abaixo e salve sua(s) seleção(ões). Consulte a lista abaixo para obter detalhes sobre os tipos de cookies e sua finalidade.',
		emptyCookieListNotice: 'Cookies desta categoria não são usados neste aplicativo',
		nameTableHeader: 'Nome do cookie',
		providerTableHeader: 'Provedor de cookies',
		purposeTableHeader: 'Finalidade do cookie',
		typeTableHeader: 'Tipo de cookie',
		durationTableHeader: 'Duração do cookie',
		formSubmit: 'Salvar minha seleção',
		requiredCookieListTitle: 'Cookies necessários',
		functionalCookieListTitle: 'Cookies Funcionais',
		functionalCookieAcceptance: 'Aceito os cookies funcionais abaixo',
		functionalCookieExplanation: 'Cookies de funcionalidade, que nos permitem melhorar a sua experiência (por exemplo, lembrando quaisquer configurações que você tenha selecionado).',
		performanceCookieListTitle: 'Cookies de desempenho',
		performanceCookieAcceptance: 'Aceito os cookies de desempenho abaixo',
		performanceCookieExplanation: 'Cookies de desempenho, que nos ajudam a medir o desempenho do site e a melhorar a sua experiência. Ao utilizar cookies de desempenho não armazenamos quaisquer dados pessoais, apenas utilizamos a informação recolhida através destes cookies de forma agregada e anonimizada.',
		advertisingCookieListTitle: 'Cookies de segmentação',
		advertisingCookieAcceptance: 'Aceito os cookies de publicidade/segmentação abaixo',
		advertisingCookieExplanation: 'Cookies de publicidade/segmentação, que utilizamos para rastrear a atividade e as sessões do usuário, para que possamos oferecer um serviço mais personalizado e (no caso de cookies de publicidade) que são definidos por terceiros com quem executamos campanhas publicitárias e nos permitem fornecer anúncios relevantes para você.',
		doNotTrackNotice: 'Detectamos que você ativou a configuração Do Not Track em seu navegador; como resultado, os cookies de publicidade/segmentação são automaticamente desativados.',
	},
	accountFormsMissing: 'Formulários de conta ausentes para {0} conta(s)',
	createAccountForms: 'Criar formulário(s) de conta',
	createAccountFormsDescription: "Clique em 'Confirmar' para criar o documento para os itens relacionados atualmente faltando um documento. ",
	createMissingDocuments: 'Contas relacionadas atualmente sem documentos',
	accountDocumentsCreated: 'Entrega de conteúdo em andamento. Pode levar até dez minutos para que o conteúdo seja entregue. ',
	evidenceMissingPICSignoffs: 'Evidência faltando assinatura(s) de PIC',
	evidenceMissingEQRSignoffs: 'Evidência faltando assinatura(s) de EQR',
	evidenceMissingPICEQRSignoffs: 'Evidência faltando assinatura(s) de PIC e/ou EQR',
	evidenceMissingPICSignoffRequirements: 'Evidência faltando assinatura(s) obrigatória(s) de PIC',
	evidenceMissingEQRSignoffRequirements: 'Evidência faltando assinatura(s) obrigatória(s) de EQR',
	evidenceMissingPICEQRSignoffRequirements: 'Evidência faltando assinatura(s) obrigatória(s) de PIC e/ou EQR',
	evidenceMissingSignoffs: 'Evidência faltando assinatura(s)',

	// Bulk task relate
	bulkTaskRelateFailureMessage: 'Alguns dos documentos selecionados não puderam ser associados à(s) tarefa(s) selecionada(s).',
	/*endoflabels*/
	evidenceMissingPreparerOrReviwerSignoffs: 'Upload de documentos - Faltando assinaturas do Preparador ou Revisor',

	manageITProcess: 'Gerenciar processos de TI',
	manageITRisk: 'Gerenciar risco de tecnologia',
	manageITControl: 'Gerencie o controle de TI',
	manageITSP: 'Gerenciar ITSP',
	manageITApp: 'Gerenciar aplicativos de TI',
	manageSCOT: 'Gerenciar SCOTs',
	addAresCustomDescription: 'Selecione o tipo de conteúdo a ser adicionado a este formulário de fluxo de trabalho guiado, insira os detalhes e clique em salvar.',

	documentImportSuccess: '{0} foi criado com sucesso. Pode levar até dez minutos para que o conteúdo seja entregue.',
	documentImportFailure: 'Falha na criação do documento. Atualize ou tente novamente após algum tempo. Se o problema persistir, entre em contato com o Help Desk.',
	formNotAvailable: 'Nenhum Formulário do Canvas correspondente foi encontrado.',
	selectTask: 'Selecionar Tarefa para relacionar à orientação',
	canvas: 'Canvas',
	selectEngagement: 'Selecione o engagement',

	//Modal Manage sub-scope
	manageSubScopeTitle: 'Gerenciar sub-escopos',
	manageSubScopeDescription: 'Crie novos sub-escopos ou edite ou exclua os sub-escopos existentes abaixo.',
	addSubScope: 'Adicionar um sub-escopo',
	subScopeName: 'Nome do sub-escopo',
	knowledgeScope: 'Escopo de conhecimento',
	subScopeAlreadyExist: 'O nome do sub-escopo que já existe',
	subScopes: 'Sub-escopos',
	notAvailableSubScopes: 'Não há sub-escopos disponíveis.',
	SubScopeNameValidation: 'O comprimento do nome do sub-escopo excede mais de 255 caracteres.',

	//CRA Summary
	manageAccount: 'Gerenciar conta',
	newAccount: 'Nova conta',

	noRelatedObjectITProcessFlow: 'Nenhum objeto relacionado. Relacione um objeto para começar.',

	//Add New Flow Chart Steps
	flowChartNewSteps: {
		newStepTitle: 'Nova etapa',
		placeholderText_1: 'Insira os detalhes da etapa abaixo e selecione',
		placeholderText_2: " 'Salvar e fechar' ",
		placeholderText_3: 'para terminar. Para criar outra etapa, selecione',
		placeholderText_4: " 'Salvar e criar outro'. ",
		columnLabel: 'Coluna (obrigatório)',
		counterOf: 'de',
		counterChar: 'caracteres',
		stepNameLabel: 'Nome da etapa (obrigatório)',
		errorMsgStepNameRequired: 'O nome da etapa é obrigatório',
		stepDescLabel: 'Descrição da etapa (obrigatório)',
		stepDescPlaceholder: 'Insira a descrição da etapa',
		errorMsgStepDescRequired: 'A descrição da etapa é obrigatória',
		required: 'Obrigatório',
		errorMsgStepDescExceedMaxLength: 'A descrição da etapa excede o máximo de caracteres permitidos',
		buttonCancel: 'Cancelar',
		buttonSaveAndClose: 'Salvar e fechar',
		buttonSaveAndCreateAnother: 'Salvar e criar outro',
		errorMsgColumnRequired: 'A coluna é obrigatória',
		headerNameForWCGW: 'Nome do WCGW',
		headerNameForControl: 'Nome do controle',
		headerNameForITApp: 'Nome do aplicativo de TI',
		headerNameForServiceOrganisation: 'Nome da organização de serviço',
		relateLabelForWCGW: 'Relacionar WCGWs',
		relateLabelForControl: 'Relacionar controles',
		relateLabelForITApp: 'Relacionar aplicativos de TI',
		relateLabelForServiceOrganisation: 'Relacionar organizações de serviços',
		designEffectiveness: 'Efetividade do desenho',
		testing: 'Teste',
		lowerRisk: 'Risco mais baixo',
		wcgwNoRowsMessage: 'Nenhum WCGW foi relacionado. Clique em {0} para começar',
		controlNoRowsMessage: 'Nenhum controle foi relacionado. Clique em {0} para começar',
		itAppNoRowsMessage: 'Nenhuma aplicação de TI foi relacionada. Clique em {0} para começar',
		serviceOrganisationNoRowsMessage: 'Nenhuma organização de serviço foi relacionada. Clique em {0} para começar',
		wgcwTabLabel: 'WCGWs',
		controlsTabLabel: 'Controles',
		itAppsTabLabel: 'Aplicativos de TI',
		serviceOrganisationTabLabel: 'Organizações de serviço',
		connectionSuccessMessage: 'Conexão criada com sucesso.',
		connectionFailedMessage: 'Não foi possível estabelecer uma conexão. Por favor, tente novamente.',
		selfConnectFailMessage: 'Origem e Destino não podem ser iguais.',
		connectionDuplicateMessage: 'A conexão já existe.',
		connectionDeleteSuccessMessage: 'Conexão excluída com sucesso.',
		connectionDeleteFailMessage: 'Não foi possível excluir a conexão. Por favor, tente novamente.',
		editStepFailMessage: 'Não foi possível editar a etapa. Por favor, tente novamente.',
		flowchartStepGetByIdFailMessage: 'Etapa inválida. Atualize e tente novamente.',
		flowchartStepGetByIdFailureMessage: 'Esta etapa do fluxograma não está mais disponível. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
		newStepFailureMessage: 'Não foi possível criar uma nova etapa. Por favor, tente novamente.',
		deleteConnector: 'Excluir conector',
		edgeConnectorOptions: 'Opções de conector',
		edgeStartPoint: 'Ponto inicial',
		edgeEndPoint: 'End Point',
		relateDocumentToFlowchartStepError: 'A operação não pode ser concluída neste momento. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
		relateDocumentsOrObjects: 'Relacionar documentos ou objetos',
		thisstep: 'a esta etapa'
	},

	flowChartWCGW: {
		wcgwsCounter: '{0} WCGWs',
		wcgwCounter: '{0} WCGW',
		headerName: 'Relacionar WCGWs',
		showOnlyRelatedText: 'Mostrar apenas relacionados',
		noResultsFound: 'Nenhum resultado encontrado'
	},

	flowchartITAPPSO: {
		showOnlyRelatedText: 'Mostrar apenas relacionados',
		noResultsFound: 'Nenhum resultado encontrado'
	},

	flowChartITApplication: {
		itApplicationsCounter: '{0} aplicativos de TI',
		itApplicationCounter: '{0} aplicativo de TI',
		headerName: 'Relacionar aplicativos de TI',
		columnName: 'Nome do aplicativo de TI',
		noDataFound: 'Nenhum aplicativo de TI encontrado'
	},

	flowChartITSO: {
		itSOsCounter: '{0} organizações de serviço',
		itSOCounter: '{0} organização de serviço',
		headerName: 'Relacionar organizações de serviço',
		columnName: 'Nome da organização de serviço',
		noDataFound: 'Nenhuma organização de serviço encontrada'
	},

	flowChartControl: {
		controlsCounter: '{0} controles',
		headerName: 'Relacionar controles',
		showOnlyRelatedText: 'Mostrar apenas relacionados',
		noResultsFound: 'Nenhum resultado encontrado',
		noWCGWs: 'Nenhum controle foi criado'
	},

	relateSCOT: {
		header: 'Relacionar SCOTs',
		estimate: 'Estimativa',
		scotsCounter: '{0} SCOTs',
		scotCounter: '{0} SCOT',
		headerName: 'Nome da SCOT',
		showOnlyRelated: 'Mostrar apenas relacionados',
		noResultsFound: 'Nenhum resultado encontrado',
		noScotCreated: 'Nenhuma SCOT criada no engaement'
	},

	relatedStepObjects: {
		relatedWCGWs: 'WCGW relacionados',
		relatedControls: 'Controles relacionados',
		relatedDocuments: 'Evidências relacionadas',
		relatedITApplications: 'Aplicativos de TI relacionados',
		relatedSOs: 'Organizações de serviços relacionados'
	},

	flowchartEditSteps: {
		nextStep: 'Próxima etapa',
		previousStep: 'Etapa anterior',
		editStepTitle: 'Editar etapa',
		editPlaceholderText_1: 'Edite os detalhes das etapas e os objetos relacionados abaixo. Clique',
		editPlaceholderText_2: "'Salvar e fechar' ",
		editPlaceholderText_3: 'para salvar e retornar ao fluxograma. Navegar para outras etapas usando as opções abaixo salvará suas atualizações. ',
		draftEditStepFailMessage: 'Não foi possível criar o passo do fluxograma. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	},

	flowChartStepmoreMenu: {
		edit: 'Editar',
		delete: 'Excluir'
	},

	relateEstimate: {
		scot: 'SCOTs',
		strategy: 'Estratégia da SCOT',
		type: 'Tipo',
		noSCOT: 'Cada estimativa deve estar relacionada a pelo menos uma SCOT. Clique',
		noSCOTmsg: ' para começar',
		estimate: 'Estimativa',
		routine: 'Rotineira',
		nonRoutine: 'Não rotineira',
		notSelected: 'Não selecionado',
		relateSCOTs: 'Relacionar SCOTs',
		remove: 'Remover',
		noEstimate: 'Nenhuma estimativa disponível',
	},

	flowChartStepIcons: {
		wcgws: 'WCGWs',
		controls: 'Controles',
		iTApps: 'Aplicativos de TI',
		serviceOrganisations: 'Organizações de serviço'
	},

	flowChartStepIcon: {
		wcgw: 'WCGW',
		control: 'Controle',
		iTApp: 'Aplicativo de TI',
		serviceOrganisation: 'Organização de serviços',
		evidence: 'Evidência'
	},

	flowChartErrorMessage: {
		stepOutsideOfTheColumns: 'Steps cannot be placed outside of the flowchart area',
		stepBetweenTheColumns: 'Steps cannot be placed between the columns',
		stepOnTopOrTooCloseToAnotherStep: 'Steps cannot be placed on top of the other steps'
	},

	//Delete Flow Chart Steps
	flowChartStepsDelete: {
		deletestep: 'Excluir etapa',
		deleteStepModalMessage: 'Tem certeza de que deseja excluir esta etapa? Todos os WCGWs, controles, aplicativos de TI, organizações de serviços e evidências relacionadas à etapa excluída não estarão relacionados.',
		cannotBeUndone: 'Contratos de clientes novos ou renovados',
		deleteStepFailMessage: 'Falha ao excluir a etapa. Por favor, tente novamente',
		deleteDraftStepErrorMessage: 'O passo criado no rascunho não foi excluído. Para excluir este passo, selecione o passo e execute a exclusão novamente.',
	},
	notEntered: 'Não inserido',
	estimateCategory: 'Categoria da estimativa',
	noResultsFoundWithPeriod: 'Nenhum resultado encontrado',
	noEstimateAvailable: 'Nenhuma estimativa disponível',
	noRelatedObject: 'Nenhum objeto relacionado.',
	relateAnObject: 'Relacionar um objeto',
	copyrightMessage: 'Copyright © <ano> todos os direitos reservados',
	leadsheet: 'Folha mestra',
	controlName: 'Nome do controle',
	noControlAvailable: 'Nenhum controle disponível',
	independenceError: 'Todas as respostas incompletas devem ser preenchidas antes de enviar a independência.',
	riskTypeNotAssociated: 'O risco recém-adicionado não corresponde ao(s) tipo(s) de risco permitido(s) e, portanto, não aparece abaixo. Adicione outro risco do tipo permitido ou selecione na lista abaixo',
	accountsAndRelatedEstimates: 'Contas e estimativas relacionadas',
	noEstimatesAssociated: 'Nenhuma estimativa associada',
	noAssertionsAvailable: 'Nenhuma assertiva disponível',
	noAccountsOrDisclosuresAvailable: 'Nenhuma conta ou divulgação disponível',

	relateEstimateToRisk: {
		riskType: 'Tipo de risco',
		risk: 'Risco',
		hasestimate: "Has estimate?",
		accounts: 'Contas',
		isItRelevant: 'É relevante?',
		assertions: 'Assertivas',
		invalidRiskParentRiskErrMsg: 'Registro não encontrado. Atualize a página para continuar.',
		noEstimate: 'Nenhuma estimativa disponível',
		invalidRelateRiskOrEstimateRelationErrMsg: 'O objeto já foi relacionado. Por favor, atualize a página para continuar.',
		invalidUnRelateRiskOrEstimateRelationErrMsg: 'O objeto já foi desvinculado. Por favor, atualize a página para continuar. '
	},

	savingChanges: 'Salvando alterações',
	showEstimateAccountsWithoutEstimates: 'Mostrar contas de estimativa sem estimativas',
	showEstimateSCOTsWithoutEstimates: 'Mostrar SCOTs de estimativa sem estimativas',
	manageSCOTs: 'Gerenciar SCOTs',
	sCOTsAndRelatedEstimates: 'SCOTs e estimativas relacionadas',
	relateEstimateToRiskNoDataMessage: 'Não há registros disponíveis. Relacione pelo menos uma conta e assertiva com um risco associado, se aplicável',
	maps: 'Mapas',
	mapsUpbutSomeLoadingErrorMessage: 'Ocorreu um erro técnico que está impedindo o funcionamento do recurso de mapas. Atualize a página e tente novamente.',
	mapsDownErrorMessage: 'O recurso de Mapas está temporariamente indisponível. Atualize a página e tente novamente. Se essa mensagem persistir, entre em contato com o Help Desk.',
	financialStatements: 'Demonstrações Financeiras',
	serviceGatewayAutomation: 'Serviço de Gateway & Automação',
	priorPeriodCategory: 'Categoria do período anterior',
	relatedAccountWithColon: 'Contas relacionadas: ',
	noRelatedAccount: 'Nenhuma conta relacionada',
	noRetionaleAvailable: 'Nenhuma justificativa disponível',
	leftNavIconApprovals: 'Aprovações',
	editDuplicateSectionHeader: 'Editar os detalhes da Seção e clique em Salvar.',

	relatedEvidences: 'Relacionar evidências',
	relatedEvidencesInstruction: 'Relacionar uma evidência deste engagement.',
	relatedTemporaryFilesInstruction: 'Relacionar um documento temporário deste engagement.',
	noDataLabel: 'Nenhum dado encontrado',
	editDuplicateSection: 'Editar seção',
	showOnlyRelated: 'Mostrar apenas relacionados',
	aiChatbot: 'EYQ Assurance Knowledge',
	StEntityNoRecords: 'Nenhuma conta ou divulgação é mapeada para a entidade selecionada.',
	versionLabel: 'Versão',
	relatedEstimates: 'Estimativas relacionadas',
	viewEvidenceRelatedToBody: 'View evidence related to the body',
	selectHeaderFromRail: 'Select a header from left navigation pane to proceed',
	manageITProcesses: 'Gerenciar processos de TI',
	rationaleForLR: 'Justificativa para conta de risco limitado',
	rationaleForInsignificant: 'Justificativa para conta insignificante',
	rationalIsMissing: 'Está faltando justificativa.',
	craSummaryText1: 'Cada conta ou divulgação significativa deve ter pelo menos uma assertiva relacionada. Clique',
	scotDetails223: {
		relatedAccounts: 'Contas relacionadas',
		scotType: 'Tipo',
		manageScot: 'Gerenciar SCOTs',
		editScot: 'Editar SCOTs',
		scotNotAvailableMessage: 'SCOTs não estão disponíveis para este documento',
		relatedScotNotAvailableMessage: 'Nenhuma SCOT relacionada. Relacione uma SCOT a partir da página de atributos para começar',
		risksDocumented: 'Riscos documentados neste walkthrough',
		risksAvailableHeader: 'Sim',
		risksNotAvailableHeader: 'Não',
		viewRelatedRisks: 'Ver riscos relacionados',
		noRelatedAccountsMessage: 'Nenhuma conta relacionada'
	},

	scotDetails226: {
		noscotsidentified: 'No SCOTs have been identified'
	},

	scotDetails224: {
		riskRelatedWalkthrough: 'Riscos relacionados neste walkthrough',
		relatedToWTDocuments: 'Related to other WT documents',
		riskNotRelatedWalkthrough: 'Riscos não relacionados neste walkthrough',
		substantiveNotSufficient: 'Substantivo não suficiente',
		journalEntry: 'Journal entry',
		noDirectRiskSourcesAreAvailable: 'Sem riscos relacionados',
		scotNotAvailableMessage: 'SCOTs não estão disponíveis para este documento',
		relatedScotNotAvailableMessage: 'Nenhuma SCOT relacionada. Relacione uma SCOT a partir da página de atributos para começar',
		relatedDocuments: 'Related documents',
		risk: "Risk:",
		riskSpecialCircumstances: 'Risk special circumstances',
		relateInstructionText: "This risk has been identified in another SCOT.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		unrelateInstructionText: "This risk has been identified in the critical path of another walkthrough.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		concurrencyErrorMessage: "This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.",
	},
	ipe: 'IPE',
	scotSummary198: {
		noAccountsDisclosureCreated: 'Nenhuma conta ou divulgação significativa foi criada',
		noScotEstimateIdentified: 'Nenhuma SCOT ou estimativa foi identificado.',
		noScotIdentified: 'Nenhuma SCOT identificado',
		scots: 'SCOTs',
		estimates: 'Estimativas',
		errorMessage: 'Esta ação não pôde ser concluída. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
		noResultsFound: 'Nenhum resultado encontrado',
		searchAccounts: 'Pesquisar contas',
		searchScotEstimates: 'Pesquisar SCOTs/estimativas',
		accounts: 'Contas',
		scotsOrEstimate: 'SCOTs/Estimativas',
		accountNotRelatedToScotValidation: 'Cada conta ou divulgação significativa deve estar relacionada a pelo menos uma SCOT. Selecione uma caixa de seleção para relacionar uma SCOT relevante a esta conta significativa ou divulgação significativa.',
		scotNotRelatedToAccountValidation: 'Cada SCOT deve estar relacionada a pelo menos uma conta significativa ou divulgação significativa. Selecione uma caixa de seleção para relacionar esta SCOT a uma conta significativa ou divulgação significativa relevante.',
		showValidations: 'Mostrar validações',
	},
	scotSummary225: {
		relatedScots: 'SCOTs Relacionadas',
		relatedAcconts: 'Contas Relacionadas',
		scotListHeader: 'SCOTs e estimativas',
		noScotsMessage: 'Cada conta ou divulgação significativa deve estar relacionada a pelo menos uma SCOT. Selecione uma SCOT existente para relacionar a esta conta significativa ou divulgação significativa',
		noAccountsMessage: 'No significant accounts or disclosures have been created.',
		noAccountsAvailableOnSearch: 'No results found',
		relateAccounts: 'Relate accounts and disclosures',
		noAccountsCreated: 'No accounts have been created',
		noScotsCreated: 'No SCOTs have been created',
		relateScots: 'Relate SCOTs',
	},
	bodyUnavailableInCCP: 'Este conteúdo não está disponível através do Portal do Cliente do EY Canvas.',
	pyBalance: 'Saldo AA',
	cyBalance: 'Saldo AC',
	designationNotDefined: 'Designação não definida',
	controlRiskAssessment: 'Avaliação de risco de controle',
	first: 'Primeiro',
	noImportedTrialBalance: 'Nenhum saldo de teste importado.',
	placeHolderMessageWhenHelixMappingIsTrue: 'Clique {0} para relacionar um novo analyzer.',
	documentPrintSuccess: 'Document print in progress. It may take up to ten minutes. Once completed, the print will be added to the temporary files.',
	documentPrintError: 'A impressão do documento falhou. Por favor, atualize ou tente novamente após algum tempo. Se o problema persistir, entre em contato com o Help Desk.',
	backToEvidenceWarningMessage: 'This action could not be completed. Please refresh and try again. If issue persists, please contact the Help Desk.',
	rationaleMissingForLR: 'Each limited risk account shall have a rationale provided',
	rationaleMissingForIR: 'Each insignificant account shall have a rationale provided',
	craSummaryText2: ' Each account shall have designation determined. Click',
	contentDrivingEntity: 'Content driving entity',
	contentDrivingEntityPlaceholder: 'Content driving entity has not been selected',
	rationaleForPlaceholder: 'Provide rationale for this account designation',
	contentDrivingEntityRequired: 'Content driving entity (required)',
	refreshContentLayers: 'Refresh content layers',
	noAccessLabel: 'Unauthorized. Contact your administrator and try again.',
	copyForHelpDeskDetails: 'Copy for Help Desk details',
	copyForHelpDeskDetailsSuccess: 'Details copied to clipboard',

	//toast activity as guest user
	sharedGuidedworkflowEvidenceWarning: 'This is a shared Guided Workflow Activity. The objects and evidence exist in the original engagement and will not be added to this engagement upon unlink. See <a style="color: #467cbe" href="https://live.atlas.ey.com/#library/104?pref=20058/9/5" target="_blank">enablement here</a> for further details.',
	sharedGuidedworkflowResponseWarning: "This is a shared Guided Workflow Activity. The responses are being shared with other activities in the same workspace. View relationships by accessing the 'Show relationships' menu item from this activity's summary section."
};

export const groupStructure = {
	createComponent: 'Novo componente',
	deleteComponent: 'Excluir componente',
	manageComponents: 'Gerenciar componentes',
	emptyComponents: 'Nenhum componente foi criado. Crie um {newComponent} para começar.',
	scope: 'Escopo',
	role: 'Função',
	pointOfContact: 'Ponto de contato',
	linkRequest: 'Solicitação do link',
	instructions: 'Instruções',
	instructionsSent: 'Instruções enviadas',
	status: 'Status',
	createComponentInstructionalText: "Insira os detalhes do componente abaixo e selecione <b>'Salvar e fechar'</b> para finalizar. Para criar outro componente, selecione <b>'Salvar e criar outro'</b>. ",
	componentName: 'Nome do componente',
	region: 'Região',
	notUsingCanvas: 'Não usando EY Canvas',
	referenceOnly: 'Apenas referência',
	saveAndCreateAnother: 'Salvar e criar outro',
	dueDate: 'Data de vencimento',
	components: 'Componentes',
	allocation: 'Alocação',
	documents: 'Evidence',
	discussions: 'Discussões',
	EDAP: 'EDAPs',
	siteVisits: 'Visitas ao site',
	reviewWorkComponent: 'Revise o trabalho realizado',
	other: 'Outro',
	significantUpdates: 'Atualizações significativas',
	executionComplete: 'Execução concluída',
	gaRoleTypesLabel: [{
		id: 1,
		displayName: 'Primária'
	},
	{
		id: 2,
		displayName: 'Regional'
	},
	{
		id: 3,
		displayName: 'Componente'
	}
	],
	gaLinkStatusLabel: [{
		id: GALinkStatus.NotSent,
		displayName: 'Enviar'
	},
	{
		id: GALinkStatus.Sent,
		displayName: 'Enviado/Não aceito'
	},
	{
		id: GALinkStatus.ComponentNotUsingCanvas,
		displayName: 'Não está usando o EY Canvas'
	},
	{
		id: GALinkStatus.ReferenceOnly,
		displayName: 'Apenas referência'
	},
	{
		id: GALinkStatus.Accepted,
		displayName: 'Aceito'
	},
	{
		id: GALinkStatus.Rejected,
		displayName: 'Rejeitado'
	},
	{
		id: GALinkStatus.Unlinked,
		displayName: 'Desvinculado'
	},
	{
		id: GALinkStatus.Pending,
		displayName: 'Enviado/Não aceito'
	}
	],
	notAvailable: 'Não disponível',
	search: labels.searchPlaceholder,
	noResultsFound: 'Nenhum resultado encontrado.',
	noComponentsFound: 'Nenhum componente encontrado.',
	contentSwitcher: [{
		id: gaRoleTypes.primary,
		displayName: 'Primária'
	},
	{
		id: gaRoleTypes.component,
		displayName: 'Componente'
	},
	{
		id: gaRoleTypes.regional,
		displayName: 'Região'
	}
	],
	gaRegionTypesLabel: {
		id: gaRegion.notApplicable,
		displayName: 'Não aplicável'
	},
	//TODO: To be removed
	pointOfContactValues: [{
		id: pointOfContactTypes.EYcontact,
		displayName: 'Contato da EY'
	},
	{
		id: pointOfContactTypes.externalContact,
		displayName: 'Contato externo'
	}
	],
	saveAndClose: labels.modalSaveAndClose,
	cancelBtn: labels.modalCancelTitle,
	gaScopesValues: [{
		id: gaScopeType.full,
		displayName: 'Completo'
	},
	{
		id: gaScopeType.specific,
		displayName: 'Específico'
	},
	{
		id: gaScopeType.specifiedAuditProcedures,
		displayName: 'Procedimentos especificos'
	},
	{
		id: gaScopeType.review,
		displayName: 'Revisão'
	}
	],
	edit: labels.edit,
	delete: labels.delete,
	tooltipIcon: 'Selecione esta opção se o componente não estiver usando o EY Canvas para receber instruções de auditoria do Grupo e enviar resultados entre escritórios.',
	tooltipReferenceIcon: 'Os componentes designados como <b>Somente referência</b> são usados ​​apenas para fins organizacionais. Esses engagements de componentes não receberão solicitações de link, instruções ou tarefas e esse engagement de equipe principal também não receberá uma tarefa de grupo.',
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	modalConfirmBtnLabel: labels.confirmLabel,
	clear: 'limpar',
	clearUpper: labels.clear,
	nameOrEmail: 'Insira o e-mail do ponto de contato da EY',
	editComponent: 'Editar componente',
	editComponentInstructionalText: "Edite os detalhes do componente abaixo e selecione <b>\'Salvar\'</b> para finalizar. ",
	linkAlreadyAcceptedInfo: 'Somente o campo de e-mail poderá ser editado, pois o link da solicitação já foi enviada para a equipe Componente.',
	sendAll: 'Enviar todos',
	send: 'Enviar',
	resend: 'Reenviar',
	scopeAndStrategy: 'Escopo & estratégia',
	execution: 'Execução',
	conclusion: 'Conclusão',
	reportingForms: 'Formulários de Reporting',
	manageGroupPermission: 'Você não tem permissão de <b>Gerenciar grupo</b> para realizar esta ação. Solicite a permissão <b>Gerenciar grupo</b> de um Administrador do Engagement.',
	manageComponentModalDesc: 'Crie novos componentes ou edite e exclua os componentes existentes abaixo.',
	editLinkInfo: 'Somente o campo de e-mail poderá ser editado, pois o link da solicitação já foi enviada para a equipe Componente.',
	invalidPointOfContact: 'É necessário um ponto de contato para enviar a solicitação de link. Edite o componente para adicionar um ponto de contato.',
	manageComponentModalActions: 'Ações',
	manageComponentModalComponents: 'Componentes',
	manageComponentModalDelete: 'Excluir',
	noThereAtLeastOneComponentToSendAll: 'Não há componentes com status elegíveis para enviar uma solicitação de link. O status de um componente deve ser <b>Enviar</b> ou <b>Reenviar</b> para enviar uma solicitação de link.',
	showKnowledgeDescription: 'Mostrar título e descrição do Conhecimento',
	hideKnowledgeDescription: 'Mostrar título e descrição do Conhecimento',
	instructionName: 'Digite o nome da instrução',
	instructionDescriptionPlaceholder: 'Insira a descrição da instrução',
	selectDueDate: 'Data de vencimento (obrigatório)',
	show: 'Mostrar',
	allocationHeader: 'Alocação',
	allocationInstructionForKnowledge: 'As instruções de conhecimento só podem ser alocadas por escopo. Selecione o(s) escopo(s) relevante(s) abaixo.',
	allocationInstructionForCustom: 'As instruções personalizadas podem ser alocadas por escopo ou por componente. Selecione a alocação de instrução abaixo e, em seguida, aloque para o(s) escopo(s) ou componente(s) relevante(s).',
	allocateScope: 'Alocar para escopos',
	allocateComponent: 'Alocar para componentes',
	pillScopesPlural: 'escopos',
	pillScopesSingular: 'escopo',
	pillComponentsPlural: 'componentes',
	pillComponentsSingular: 'componente',
	selectScopesPlaceholder: 'Selecionar escopos',
	selectComponentsPlaceholder: 'Selecionar componentes',
	searchNoResultFoundText: labels.searchNoResultFoundText,
	newCustomInstruction: 'Nova instrução personalizada',
	instructionNameNewCustomInstruction: 'Nome da instrução',
	addCustom: 'Adicionar personalização',
	custom: 'Personalizar',
	required: 'Obrigatório',
	remove: 'Remover',
	selectAll: 'Selecionar tudo',
	unselectAll: 'Desmarcar tudo',
	lowerPoC: 'ponto de contato',
	editPoCTooltip: 'Ponto de contato inválido ou sem contato. Edite o ponto de contato para enviar uma solicitação de link.',
	recomendationType: [{
		id: 1,
		label: 'Obrigatório'
	},
		{
			id: 2,
			label: 'Opcional'
		},
		{
			id: 3,
			label: 'Não aplicável'
		}
	],
	confirmLabel: labels.confirmLabel,
	deleteComponentInstructionalText: '<b>Tem certeza de que deseja excluir este componente da estrutura do Grupo?</b><br />Quando o componente for excluído, o link para o componente será removido e os engagements não poderão mais trocar documentação. Além disso, todas as associações entre o componente e suas contas e instruções serão excluídas.',
	noActivitiesAvailable: 'Nenhuma atividade disponível.',
	relatedComponents: 'Componentes relacionados',
	relatedComponentsSingular: 'componente relacionado',
	relatedComponentsPlural: 'Componentes relacionados',
	publish: 'Publicar',
	publishModalHeader: 'Publicar alterações',
	publishChangesInstructional: '<b>Tem certeza de que deseja publicar alterações no resumo das instruções do grupo?</b><br />O conjunto anterior de instruções do grupo será substituído. Assim que as alterações forem publicadas, as instruções atualizadas poderão ser enviadas a partir do resumo de instruções do grupo.',
	publishManageGroupPermission: 'Você deve ter permissão para gerenciar grupo para realizar esta ação. Solicite permissão de um Administrador do Engagement.',
	lastPublished: 'Última publicação: ',
	publishChangesNotAvailable: 'Não disponível ainda',
	noRecordsFound: labels.noRecordsFound,
	deleteInstruction: 'Excluir instrução',
	deleteInstructionInstructionalText: '<b>Tem certeza de que deseja excluir a instrução? </b><br />Esta ação não pode ser desfeita.',
	sendInstructionsTitle: 'Enviar instruções',
	sendInstructionsInstructionalText: "Certifique-se de que as instruções mais recentes foram publicadas clicando em 'Publicar' na página subjacente. Em seguida, revise as instruções do componente abaixo e selecione 'Enviar' para enviar as instruções ao envolvimento do componente. ",
	instructionsAlreadySent: 'A última versão das instruções já foi enviada.',
	missingDueDates: 'Falta a data de vencimento do formulário de relatório.',
	createInstructionsModalButton: 'Criar instruções',
	createInstructionsModalActionToastMessageStart: 'Faltam instruções de avaliação de risco de grupo para',
	createInstructionsModalActionToastMessageEnd: ' componentes.',
	createInstructionsModalDescription: 'Os seguintes componentes de escopo Completo e Específico não têm instruções de avaliação de risco do Grupo que lhes sejam atribuídas. Selecionando <b>Criar</b> criará uma instrução de avaliação de risco do Grupo para cada componente listado abaixo.',
	createInstructionsModalScope: 'Escopo',
	createInstructionsModalHeader: 'Criar instruções',
	createInstructionsModalmodalConfirmBtnLabel: 'Criar ',
	createInstructionsModalmodalCancelBtnLabel: 'Cancelar',
	createInstructionsModalmodalCloseBtnTitletip: 'Fechar',
	createInstructionsModalNewGraInstructionDescription: 'Em anexo está a avaliação de risco para contas relevantes para o seu componente. Revise a avaliação de riscos e garanta que seu engagement tenha essas contas e riscos identificados. Quaisquer riscos adicionais identificados localmente ou com os quais a equipe do componente não concorde devem ser comunicados à equipe principal para que a avaliação de riscos possa ser ajustada adequadamente pela equipe principal e pela equipe componente.',
	createInstructionsModalErrorMessage: 'Falha na criação de instruções de avaliação de risco de Grupo para os seguintes componentes: <b>{0}</b>. Atualize a página e tente novamente.',
	createInstructionsDuplicatedModalErrorMessage: 'Falha na criação da instrução de avaliação de risco de grupo. O nome da instrução não pode ser duplicado.',
	gaLinkActionTooltip: {
		NotUsingCanvasLabel: 'Não está usando o EY Canvas',
		NotUsingCanvas: 'Clicar em <b>Enviar</b> criará as tarefas do <br/> Grupo Primário para este componente, mas <br/> nenhuma instrução será enviada.',
		NotLinkedLabel: 'Não vinculado',
		NotLinked: 'A solicitação de link não foi enviada para <br/> a equipe Componente. Envie o link <br/> da solicitação para enviar instruções.',
		Unlinked: 'Desvinculado'
	},
	viewHistory: 'Ver histórico',
	viewSentInstructionsTitle: 'Ver instruções enviadas',
	save: labels.saveLabel,
	cancel: labels.cancelLabel,
	viewHistoryInstructionalText: 'Selecione a instrução para visualizar as versões anteriores das instruções enviadas à equipe do componente.',
	viewHistorySelectInstruction: 'Selecione a instrução',
	viewHistoryDateSent: 'Data enviada: ',
	viewHistoryStatus: 'Status: ',
	viewHistoryStatusAccepted: 'Aceito',
	viewHistoryStatusPending: 'Pendente',
	viewHistoryStatusRejected: 'Rejeitado',
	viewHistoryStatusSystemError: 'System error',
	viewHistorySelectVersion: 'Selecionar versão',
	noAccountsFound: 'Não foram encontradas contas ou divulgações neste ou em outros engagements. <br />Selecione {link} para criar contas ou divulgações novas ou editar contas existentes.',
	generalCommunications: 'Comunicações gerais',
	reportingDeliverables: 'Relatórios de resultados',
	changesPublishedNotSent: 'Alterações não enviadas',
	changesPublishedBrNotSent: 'Alterações<br/>não enviadas',
	changesPublishedNotSentYes: 'Sim',
	deleteSubScopeInstructionalTextModal: 'Tem certeza de que deseja excluir <br/> o sub-escopo selecionado?',
	deleteSubScopeTitleModal: 'Excluir sub-escopo',
	riskAssessmentModal: {
		headerText: 'Avaliação de risco',
		modalCloseBtnTitletip: labels.close,
		manageAndDisclosures: 'Vincular e gerenciar contas & divulgações',
		next: 'Próximo componente',
		back: 'Componente anterior'
	},
	riskAssessment: 'Avaliação de risco',
	preview: 'Visualização',
	accountsAndDisclosureSummary: 'Conta e divulgação',
	noAccountSnapshotPlaceholder: 'Não há dados de conta para exibir para este componente.',
	createOversightProjectButtonLabel: 'Criar projeto do EY Canvas Oversight',
	createOversightProjectTitle: 'Deseja que um projeto do EY Canvas Oversight seja criado com este engagement principal?',
	createOversightProjectDescription: 'Os engagements regionais e/ou de componentes do EY Canvas identificados nesta estrutura de grupo serão automaticamente populados como parte da configuração do projeto do EY Canvas Oversight.',
	createOversightModalHeader: 'Nome do projeto do EY Canvas Oversight',
	createOversightModalDescription: 'Digite o nome do projeto do EY Canvas Oversight.',
	createOversightModalTextLabel: 'Nome do projeto',
	projectRedirectionButtonLabel: 'Projetos do EY Canvas Oversight',
	projectAssociationTextLabel: 'Existem projetos do EY Canvas Oversight conectados a este engagement.',
	sendLinkDisableTooltip: 'Este engagement foi copiado, incluindo componentes do fluxo de auditoria do Grupo. Os links não podem ser reenviados. Crie um novo componente e envie um link, conforme necessário. ',
	instructionsCannotBeSentUntilPublished: 'Instructions cannot be sent until they are published.'
};

export const groupInvolvement = {
	NoComponentsAvailables: 'Nenhum componente foi criado. <b>Gerencie componentes</b> para começar.',
	GroupInvolvementToastMsgStart: 'O formulário de envolvimento do grupo está faltando para',
	GroupInvolvementToastMsgEnd: ' componente(s).',
	CreateGroupInvolvementHeader: 'Crie formulário(s) de envolvimento',
	GroupInvolvementInstructionalText: 'Os componentes a seguir não possuem formulário(s) de envolvimento do Grupo atribuídos a eles.<br/> Selecionando&#39;<b>Criar</b>&#39; criará um formulário de envolvimento do grupo para cada componente listado abaixo.',
	createGroupInvolvementDocumentErrorMessage: 'Falha na criação do documento de envolvimento do Grupo para os seguintes componentes: <b>{0}</b>. Atualize a página e tente novamente.',
	createGroupInvolvementDocumentSuccessMessage: 'Formulário(s) de envolvimento do Grupo criados com sucesso. Atualize a página em 30 segundos para ver os documentos disponíveis. ',
	involvementTypePlanned: 'Tipo de envolvimento planejado',
	significantUpdatesToPlannedInvolvement: 'Atualizações significativas no envolvimento planejado',
	executionComplete: 'Execução concluída',
	generateGroupInvolvementCommunications: 'Imprimir formulário(s) de envolvimento',
	generateGroupInvolvementInstructionalText: 'Os seguintes componentes têm formulário(s) de envolvimento do Grupo associados a eles. Selecione quais formulários de envolvimento do grupo de componentes serão incluídos em um documento abaixo.<br /><br /> Depois que os componentes forem selecionados, selecionar <b>&#39;Criar&#39;</b> irá crie um documento de envolvimento do grupo com o documento de envolvimento do grupo de cada componente listado abaixo.',
	componentTeams: 'Equipes de componentes',
	noComponentsSelectedErrorMessage: 'Selecione componentes para criar uma comunicação de envolvimento do grupo.',
	documentName: '{taskName} pacote de envolvimento do grupo',
	selectAll: groupStructure.selectAll,
	unselectAll: groupStructure.unselectAll,
	modalConfirmBtnLabel: groupStructure.createInstructionsModalmodalConfirmBtnLabel,
	modalCancelBtnLabel: groupStructure.cancelBtn,
	modalCloseBtnTitletip: groupStructure.modalCloseBtnTitletip
};

export const itPlanning = {
	supportingITColumnsHeaders: {
		applicationTool: {
			name: 'Applications/Tools'
		},
		network: {
			name: 'Redes '
		},
		database: {
			name: 'Bancos de dados '
		},
		operatingSystem: {
			name: 'Sistemas operacionais '
		}
	},
	relatedITProcessesColumnsHeaders: {
		relatedITProcess: 'Related IT processes',
		category: 'Category'
	},
	itPlanningPlaceholders: {
		smartEvidenceSourceEntityId: 'Tecnologia relacionada não disponível para este documento',
		smartEvidenceSourceId: 'Nenhum objeto relacionado. Relacione um objeto para começar.',
	},
	relatedITProcessesPlaceholders: {
		smartEvidenceSourceEntityId: 'Related IT process not available for this document',
		smartEvidenceSourceId: 'Nenhum objeto relacionado. Relacione um objeto para começar.',
		relatedITProcessEmpty: 'No IT process related to the technology'
	},
	noTechnologiesIdentified: 'Nenhuma tecnologia foi identificada',
	supportingITEmpty: 'Nenhum aplicativo/ferramenta de suporte relacionado à tecnologia',
	supportingITNetworkEmpty: 'Sem redes de suporte relacionadas à tecnologia',
	searchPlaceholder: 'Pesquisa',
	newTechnology: 'Nova tecnologia',
	noSupportingDatabases: 'Nenhum banco de dados de suporte relacionado à tecnologia',
	createEntityFormDocument: 'Criar documento',
	noSupportingOperatingSystem: 'No supporting operating systems related to the technology',
	manageTechnology: 'Manage technology'
};

export const itRiskFactors = {
	accepted: 'Aceito',
	rejected: 'Rejeitado',
	accept: 'Aceitar',
	reject: 'Rejeitar',
	rejectionRationale: 'Justificativa de rejeição',
	rejectionCategory: 'Categoria de rejeição',
	rejectionRationaleRequired: 'Rejection rationale (required)',
	rejectionCategoryRequired: 'Rejection category (required)',
	riskName: 'Risk name',
	smartEvidenceValidations: {
		smartEvidenceSourceEntityId: 'Fatores de risco não disponíveis para este documento',
		smartEvidenceSourceId: 'No related object. Relate an object to get started.'
	},
	manageChangePlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'Todos os riscos foram rejeitados '
	},
	manageOperationsPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	},
	manageAccessPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	},
	SDLCPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'Todos os riscos foram rejeitados '
	},
	manageSecuritySettingsPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	}
};

export const rejectionTypeResource = [{
	id: rejectionType.itRiskOther,
	label: 'ITRisk Other'
},
{
	id: rejectionType.itRiskOption2,
	label: 'ITRisk - "Option 2"'
},
{
	id: rejectionType.itRiskOption3,
	label: 'ITRisk - "Option 3"'
}
];

export const sampleList = {
	newSample: 'Nova amostra',
	createSampleModalDescription: "Insira os detalhes do exemplo abaixo e selecione '<b>{0}</b>' para finalizar. Para criar outra amostra, selecione '<b>{1}</b>'. ",
	saveAndCreateAnother: 'Salvar e criar outro',
	saveAndClose: 'Salvar e fechar',
	sampleDescription: 'Descrição da amostra (obrigatório)',
	sampleDate: 'Data da amostra (obrigatório)',
	sampleListId: 'ID da lista de amostra',
	ok: 'Ok',
	addSample: 'Adicionar amostra',
	cancel: 'Cancelar',
	saveAndCloseForHeader: 'Salvar e fechar',
	saveAndCreateAnotherHeader: 'Salvar e criar outro',
	required: 'Obrigatório',
	description: 'Descrição da amostra',
	date: 'Data',
	attributeStatus: 'Atributos',
	tags: 'Tags',
	open: 'Aberto',
	notApplicableLabel: 'Não aplicável',
	notPresent: 'Não presente',
	present: 'Presente',
	pagingShowtext: 'Mostrar',
	placeHolderMessage: 'Nenhuma amostra disponível. Clique em {clickHere} para começar.',
	noSampleListAvailable: 'Nenhuma lista de amostras disponível',
	editSample: 'Editar amostra',
	editSampleDescription: "Edite os detalhes do exemplo abaixo e selecione '<b>{0}</b>' para finalizar. ",
	editSampleSave: 'Salvar',
	sampleCanNotBeCreated: 'Não é possível criar amostras para este documento.',
	noRelatedObject: 'Nenhum objeto relacionado. Relacione um objeto para começar.',
	noResultsFound: 'Nenhum resultado encontrado',
};

export const AdditionDocumentationLabels = {
	addAdditionalDocumentation: 'Adicionar documentação adicional',
	editAdditionalDocTitle: 'Editar documentação adicional',
	removeAdditionalDocumentation: 'Remover documentação adicional',
	cancel: 'Cancelar',
	save: 'Salvar',
	of: 'de',
	additionalDocTitlePlaceholder: 'Documentação adicional (obrigatório)',
	additionalDocTitle: 'Documentação adicional (obrigatório)',
	remove: 'Remover',
	enterAdditionalDocTitle: "Insira a documentação adicional abaixo e selecione <b>'{0}'</b> para finalizar. ",
	editAdditionalDocDesc: "Editar a documentação adicional abaixo e selecione <b>'{0}'</b> para finalizar. ",
	characters: 'Caracteres',
	required: 'Obrigatório',
	descriptionMaxLengthError: 'A resposta excede o máximo permitido.',
	attributeIndexLabel: 'Índice de atributos'
};

export const sampletAttributeConstants = [{
	id: 1,
	label: 'Abrir'
},
{
	id: 3,
	label: 'Apresentar'
},
{
	id: 7,
	label: 'Apresentar com comentários'
},
{
	id: 5,
	label: 'Não apresentado'
},
{
	id: 4,
	label: 'Não aplicável'
},
];

export const groupInstructions = {
	ALRAPackageModalTitle: 'Nome do pacote ALRA',
	ALRAPackageModalInstructionalText: 'Insira o nome do pacote ALRA que você está adicionando à Evidência.',
	ALRAPackageModalNameField: 'Insira o nome',
	ALRAPackageSuccessToastMessage: 'O processo de criação do pacote foi iniciado. Pode levar até dez minutos para que seja concluído.',
	ALRAPackageInProgressToastMessage: 'O processo de criação do pacote está em andamento. Pode levar até dez minutos para ser concluído.',
	delete: labels.delete,
	deleteSectionModalTitle: labels.deleteSection,
	deleteSectionInstructionalText: '<b>Tem certeza de que deseja excluir a seção?</b><br />Esta ação não pode ser desfeita.',
	deleteSectionTooltipText: 'As instruções devem ser excluídas<br />antes que a seção possa ser excluída.',
	modalConfirmBtnLabel: labels.confirmLabel,
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	missing: 'Ausente',
	sendAllModalTriggerButton: 'Enviar todos',
	sendAllModalTooltipText: 'Não há instruções disponíveis para envio às equipes componentes.',
	publishModalTooltipText: 'As instruções do grupo precisam ser publicadas antes de serem enviadas. Quando as instruções são publicadas, quaisquer alterações são salvas como novas instruções, substituindo a versão anterior das instruções. Essas novas instruções podem então ser enviadas para as equipes de componentes.',
	sendAllModalErrorMessage: 'Group instructions for the following Components were not sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk. <br /> <b>{0}</b>',
	sendAllModalHeaderText: 'Envie todas as instruções',
	sendAllModalConfirmBtnLabel: 'Enviar',
	sendAllModalCancelBtnLabel: 'Cancelar',
	sendAllModalCloseBtnTitletip: 'Fechar',
	sendAllModalDescription: 'Selecionar <b>Enviar</b> enviará instruções às seguintes equipes de componentes.',
	generateGroupRiskAssessmentCommunications: 'Gerar grupo ALRA',
	bulkALRAPackageName: '{instructionName} Pacote de avaliação de risco no nível da conta',
	groupInstructionSummaryReport: 'Relatório de resumo de instruções de grupo',
	groupInstructionSummaryReportTitletip: 'Visualize e exporte detalhes de instruções do grupo, histórico de instruções e alterações no mapeamento de componentes/contas.',
	exportGroupRiskAssessment: 'Resumo de exportação',
	reportingDeliverables: groupStructure.reportingDeliverables,
	groupRiskAssessment: 'Avaliação de risco de grupo'
};

export const sectionTitles = [{
	id: KnowledgeSectionIds.GeneralCommunications,
	sectionTitle: groupStructure.generalCommunications
},
{
	id: KnowledgeSectionIds.ScopeOfWork,
	sectionTitle: 'Escopo de trabalho'
},
{
	id: KnowledgeSectionIds.ReportingForms,
	sectionTitle: groupStructure.reportingDeliverables
},
{
	id: KnowledgeSectionIds.ProceduresPerformedCentrally,
	sectionTitle: 'Procedimentos realizados centralizadamente'
},
{
	id: KnowledgeSectionIds.GroupRiskAssessment,
	sectionTitle: groupInstructions.groupRiskAssessment
},
{
	id: KnowledgeSectionIds.OtherCommunications,
	sectionTitle: 'Outras comunicações'
}
];

export const groupAuditToolbar = {
	search: labels.placeholderForSearch
};

export const AccountType = [{
	id: 1,
	accounttypename: 'Conta significativa'
},
{
	id: 2,
	accounttypename: 'Conta de risco limitado'
},
{
	id: 3,
	accounttypename: 'Conta insignificante'
},
{
	id: 4,
	accounttypename: 'Outra conta'
},
{
	id: 5,
	accounttypename: 'Divulgação Significativa'
}
];

export const PriorityType = [{
	value: 1,
	label: 'Baixo'
},
{
	value: 2,
	label: 'Médio'
},
{
	value: 3,
	label: 'Alto'
},
{
	value: 4,
	label: 'Crítico'
}
];

export const AccountSummaryAccountType = [{
	id: '0',
	accounttypename: 'Todas as contas'
},
{
	id: '1',
	accounttypename: 'Contas significativas'
},
{
	id: '2',
	accounttypename: 'Contas de risco limitado'
},
{
	id: '3',
	accounttypename: 'Contas insignificantes'
},
{
	id: '4',
	accounttypename: 'Conta - Outras'
},
{
	id: '5',
	accounttypename: 'Divulgações significativas'
}
];

export const TaskStatus = [{
	id: 1,
	status: 'Aberto'
},
{
	id: 2,
	status: 'Em andamento'
},
{
	id: 3,
	status: 'Em revisão'
},
{
	id: 4,
	status: 'Concluído'
},
{
	id: 5,
	status: 'Removido'
}
];

export const reviewNoteLabels = {
	/*Review Notes*/
	emptyNoteDetailsMessage: 'Selecione uma nota para visualizar detalhes. Para ativar controles em massa, use a tecla controle ou shift e selecione várias notas de revisão. Se você quiser trabalhar em uma nota individual, selecione essa nota na lista.',
	documentReviewNotesLabel: 'Notas de documentos',
	addNewReviewNoteButtonText: 'Adicionar nota',
	noNotesAssociatedWithDocumentLabel: 'Não há notas associadas a este documento',
	allNotesLabel: 'Todas as notas',
	charactersLabel: 'Caracteres',
	myNotesLabel: 'Minhas anotações',
	showClearedLabel: 'Mostrar limpo',
	showClosedLabel: 'Mostrar fechado',
	toLabel: 'para',
	toUserLabel: 'Para',
	ofLabel: 'de',
	textAreaPlaceholder: 'Digite nota',
	addNewNoteModalClose: 'fechar',
	addNewNoteModalTitleLabel: 'Adicionar nova nota',
	editNoteModalTitleLabel: 'Editar nota',
	deleteIconHoverText: 'Apagar',
	deleteIconModalAcceptText: 'Apagar',
	deleteIconModalConfirmMessage: 'Você tem certeza de que deseja excluir sua resposta a esta nota?',
	deleteIconModalConfirmMessageParent: 'Tem certeza de que deseja excluir a nota selecionada?',
	deleteIconModalTitleLabel: 'Excluir nota',
	deleteReplyIconModalTitle: 'Excluir resposta',
	emptyRepliesMessage: 'Ainda sem resposta',
	replyInputPlaceholder: 'Responder a esta nota',
	replyText: 'Texto de resposta',
	editReplyModelTitle: 'Editar resposta',
	noteDueDateLabel: 'Prazo: ',
	fromUserLabel: 'De',
	priorityLabel: 'Prioridade',
	dueDateLabel: 'Data de vencimento',
	dueLabel: 'Vencimento',
	status: 'Status',
	noteModifiedDateLabel: 'Modificado: ',
	cancelLabel: 'Cancelar',
	saveLabel: 'Salvar',
	clearedBy: 'Apagado por',
	closedBy: 'Fechado por',
	reopenedBy: 'Reaberto por',
	reply: 'Resposta',
	editIconHoverTextLabel: 'Editar',
	required: 'Obrigatório',
	closeTitle: 'Fechar',
	otherEngagementNotes: 'Outras notas do engagement',
	closeLabel: 'Fechar',
	showMore: 'Mostrar mais',
	showLess: 'Mostrar menos',
	showMoreEllipsis: 'Mostre mais...',
	showLessEllipsis: 'Mostrar menos...',
	noResultFound: 'Nenhum resultado encontrado',
	engagementNameLabel: 'Nome do engagement: ',
	drag: 'Arrastar',
	formMaxLength: 'O texto não pode exceder {number} caracteres.',
	voiceNoteButtonLabel: 'Nota de voz',
	stopRecordingButtonLabel: 'Parar',
	reopen: 'Reabrir',
	noNotesFound: 'Nenhuma nota encontrada',
	noNotesFoundInstructional: 'Deixe uma nota usando as entradas abaixo. Atribua a nota a um usuário e especifique a prioridade e a data de vencimento.',
	microphoneBlockedMessage: 'Permita que o navegador acesse seu microfone para usar anotações de voz. Se já permitido, atualize e tente novamente.',
	microphoneBlockedOnVideoMessage: 'Permita que o navegador acesse seu microfone para usar voz na gravação de tela. Se já for permitido, atualize e tente novamente.',
	notInMainWindowVoice: 'Gravações de voz não são permitidas dentro da gaveta, abra o documento em uma nova aba para realizar a ação.',
	notInMainWindowScreen: 'Gravações de tela não são permitidas dentro da gaveta, abra o documento em uma nova aba para realizar a ação.',
	voiceNoteNotAvailable: 'Nota de voz e gravação de tela não estão disponíveis na visualização de gaveta. Mude para a visualização em tela inteira para usar esses recursos.',
	playButtonTitle: 'Iniciar',
	deleteButtonTitle: 'Excluir',
	pauseButtonTitle: 'Pausar',
	screenRecord: 'Registro de tela',
	playbackReview: 'Revisão de reprodução '
};

export const IndividualAccountAttributeLabels = {
	attributesNotAvailableForDocument: 'Atributos da conta não estão disponíveis para este documento.',
	noRelatedOnject: 'Nenhum objeto relacionado. Relacione um objeto para começar.',
	noAttributesAvailable: 'Nenhum atributo disponível',
	noRisksAvailable: 'Nenhum risco disponível',
	attributeStandardRomms: 'RDMs padrão de atributo',
	continueButtonTitle: 'Continuar',
	closeButtonTitle: 'Cancelar',
	newAssertionModalPlaceholder: 'Esta seleção resultará na(s) seguinte(s) assertiva(s) sendo identificada(s) como risco inerente mais baixo que não foram anteriormente identificadas como relevantes. Você deseja continuar?',
	assertion: 'Assertiva',
	inherentRiskType: 'Risco inerente',
	assertionModalTitle: 'Nova assertiva',
	riskType: 'Mais baixo '
};

export const entities = [{
	id: 0,
	name: 'Todos'
},
{
	id: 1,
	name: 'Documento'
},
{
	id: 2,
	name: 'Cronograma'
},
{
	id: 3,
	name: 'Contas'
},
{
	id: 4,
	name: 'SCOT'
},
{
	id: 5,
	name: 'Processo de TI'
},
{
	id: 6,
	name: 'Plano de auditoria'
},
{
	id: 7,
	name: 'Risco'
},
{
	id: 8,
	name: 'Tarefa'
},
{
	id: 9,
	name: 'Distorção'
},
{
	id: 10,
	name: 'Deficiência'
},
{
	id: 11,
	name: 'Componente GA'
},
{
	id: 12,
	name: 'Instrução do componente GA'
},
{
	id: 13,
	name: 'Evidência do Componente GA'
},
{
	id: 14,
	name: 'Escopo do Grupo'
},
{
	id: 15,
	name: 'GA Primary Instruction'
},
{
	id: 16,
	name: 'GA Primary'
},
{
	id: 17,
	name: 'Solicitação do cliente'
},
{
	id: 18,
	name: 'WCGW'
},
{
	id: 19,
	name: 'Controle'
},
{
	id: 20,
	name: 'Aplicativo de TI'
},
{
	id: 21,
	name: 'Formuário do Canvas'
},
{
	id: 22,
	name: 'Form Section'
},
{
	id: 23,
	name: 'Corpo do formulário'
},
{
	id: 24,
	name: 'Afirmação'
},
{
	id: 25,
	name: 'Client Engagement'
},
{
	id: 26,
	name: 'Grupo de clientes'
},
{
	id: 27,
	name: 'Tag do engagement'
},
{
	id: 28,
	name: 'Engagement'
},
{
	id: 29,
	name: 'Cabeçalho do formulário'
},
{
	id: 30,
	name: 'Status do formulário'
},
{
	id: 31,
	name: 'Engagement User'
},
{
	id: 32,
	name: 'Usuário do grupo de clientes'
},
{
	id: 33,
	name: 'Índice PSP'
},
{
	id: 34,
	name: 'ITGC'
},
{
	id: 35,
	name: 'Risco de TI'
},
{
	id: 36,
	name: 'Item de linha automatizado'
}
];

export const PaceType = [{
	id: 1,
	paceTypename: 'Baixo'
},
{
	id: 2,
	paceTypename: 'Moderado'
},
{
	id: 3,
	paceTypename: 'Alto'
},
{
	id: 4,
	paceTypename: 'Monitoramento de perto'
}
];

export const DocumentHelper = {
	401: 'Não foi possível concluir a operação. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	413: 'O documento excede o tamanho máximo de arquivo permitido (250 MB) e não pode ser carregado. Reduza o tamanho do arquivo e tente novamente.',
	412: 'Já existe um documento com nome neste engagement',
	414: 'O nome excede o comprimento máximo (120 caracteres).',
	4099: 'Documento com o mesmo nome já existe',
	/*this is a hack as we dont always know why conflict happened.*/
	410: 'Este documento foi excluído e, portanto, não pode ser aberto.',
	411: 'Documentos vazios não são permitidos.'
};

export const Errors = {
	/*Doc Helper Custom Messages */
	0: 'Conexão perdida. Reconecte-se e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	10: 'Um problema foi detectado com o EY Canvas Document Helper. Clique <a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">aqui</a> para instruções sobre como resolver esse problema.',
	101: 'Status de engagement inválido.',
	102: 'Usuário de engagement válido não encontrado.',
	103: 'Conformidade com a independência do usuário do engagement ausente.',
	104: 'Escopos do Azure AD necessários estão pendentes',
	105: 'Ocorreu um erro ao obter as permissões do engagement. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	106: "Unauthorized. Contact your administrator and try again.",
	107: 'Usuário de engagement válido não encontrado. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	108: 'O perfil de engagement está incompleto. Por favor, vá para Landing page e Complete Profile.',
	303: 'Um documento com o mesmo nome está sendo carregado.',
	403: 'Access to this document is not available.  If this document is shared ensure you have access to the source engagement.  Refresh the page and try again.  If the error persists contact the Help Desk.',
	406: 'O documento não pode estar vazio.',
	412: 'Já existe um documento com o mesmo nome neste engagement.',
	414: 'O nome excede o comprimento máximo (120 caracteres).',
	411: 'Documentos vazios não são permitidos.',
	500: 'Conexão perdida. Reconecte-se e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	600: 'A operação não pode ser concluída neste momento. Atualize a página e tente novamente. Se o problema persistir entre em contato com o Help Desk.',
	601: 'Erro de download da captura de tela. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help desk.',
	602: 'O documento já está na colaboração',
	935: 'O usuário não tem permissões suficientes para realizar a operação.',
	zip: 'O arquivo .zip não pode ser carregado porque contém um ou mais tipos de arquivos não suportados, ou contém mais do que a quantidade máxima de arquivos .zip incorporados.',
	401000: 'A alteração de rede é detectada e, por favor, recarregue a página para continuar',

	/*Accounts*/
	1001: 'A chamada Criar conta falhou.',
	1002: 'Nome da conta ausente',
	1003: 'The selected Account has been deleted. Close this modal to see the updated list.',
	1004: 'Nenhum resultado encontrado. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	1005: 'Id de engagament inválido.',
	1006: 'Tipo de conta inválida.',
	1007: 'Tipo de declaração inválida.',
	1008: 'A conta selecionada foi excluída. Feche este modal para ver a lista atualizada.',
	1009: 'Obter conta por chamada de id falhou.',
	1010: 'Obter conta por chamada de id de engagement falhou.',
	1011: 'Obter resumo da matriz de avaliação substantiva (SEM) para uma chamada de ID de conta falhou devido a um pedido inválido.',
	1012: 'Tipo de resumo inválido.',
	1013: 'Falha ao criar chamada de revisão de conta.',
	1014: 'Falha ao excluir chamada de revisão de conta',
	1015: 'Solicitação de criação de revisão de conta inválida.',
	1016: 'ID de revisão de conta inválido.',
	1017: 'A conta não faz parte deste engagements ou foi excluída.',
	1018: 'A revisão da conta foi criada por outro usuário.',
	1019: 'A conta foi excluída por outro usuário. Atualize a página e tente novamente.',
	1020: 'A conta precisa de PSP para atualizar',
	1024: 'O nome da conta tem mais de 500 caracteres.',
	1025: 'Conta de Risco Limitado ou Conta Insignificante não podem ser estimadas',
	1026: 'A conta não pode ter assertivas duplicadas',
	1027: 'ID da assertiva inválido',
	1037: 'Não é possível ter índices PSP duplicados',
	1039: 'A conta selecionada foi excluída. Feche este modal para ver a lista atualizada.',
	1048: 'A conta tem um tipo de execução inválido.',
	1053: 'A conta tem Risco ou Estimativa associada, não pode ser definida como Conta de Risco Limitado ou Conta Insignificante.',
	1054: 'As assertivas a serem excluídas foram associadas a Risco ou Estimativa',
	1065: 'Você não pode fazer alterações em assertiva(s) que tenham um risco significativo, ou risco de fraude, ou risco de distorção relevante ou uma estimativa relacionada. Você precisa remover essas relações primeiro.',
	1070: 'O ID do saldo de teste não pode ser nulo quando incluído no Helix.',
	1072: 'A ação não pode ser concluída. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',

	1266: 'O engagement atingiu o número máximo de documentos em modo de edição de multi-usuário. Verifique alguns documentos e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	1267: 'Document is conflicted. Resolve conflicts and try again.  If the issue persists, contact the Help Desk.',
	1268: 'Document is already in co-edit mode. end co-edit mode and try again.  If the issue persists, contact the Help Desk.',
	1269: 'Document is a shared evidence. Unlink and try again.  If the issue persists, contact the Help Desk.',
	1270: 'As versões do documento não podem ser excluídas ou atualizadas enquanto estiverem em modo de edição de multi-usuário. Por favor, encerre a edição de multi-usuário e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	1271: 'O documento não está em modo de co-edição ou o modo de co-edição foi encerrado. Se o problema persistir, entre em contato com o Help Desk.',

	/*Assertions*/
	2001: 'Solicitação de criação inválida.',
	2002: 'Nome de assertiva ausente.',
	2003: 'Assertiva faltando.',
	2004: 'A chamada de afirmação falhou.',
	2005: 'Id de engagement inválido.',
	2006: 'Falha na obtenção de assertivas por chamada de ID.',
	2007: 'Falha na chamada de obtenção de assertiva dos WCGWs.',

	/*Risks*/
	4001: 'A operação não pôde ser concluída neste momento. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	4002: 'Nome do risco ausente.',
	4003: 'Risco ausente.',
	4004: 'Falha na chamada para obter risco.',
	4005: 'Id de engagement inválido.',
	4006: 'Obter risco por chamada de identificação falhou.',
	4007: 'Solicitação de consulta inválida.',
	4008: 'Esta estimativa não está mais disponível. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	4009: 'Solicitação de atualização inválida.',
	4010: 'WCGW específico já atribuído a este risco',
	4011: 'A lista WCGW não pode ser nula.',
	4012: 'Falhou em obter tipos de risco.',
	4013: 'Solicitação de criação inválida.',
	4014: 'A assertiva relacionada não é válida. Atualize a página e tente novamente. Se esse erro persistir, entre em contato com o Help Desk.',
	4015: 'O WCGW não é válido. Atualize a página e tente novamente. Se esse erro persistir, entre em contato com o Help Desk.',
	4016: 'O Risco/Estimativa selecionado foi excluído. Feche este modal para ver a lista atualizada.',
	4017: 'A assertiva não é válida para o Risco. Atualize a página e tente novamente. Se esse erro persistir, entre em contato com o Help Desk.',
	4018: 'O tipo de id de risco aprovado é inválido.',
	4019: 'O nome do risco não é válido.',
	4020: 'ID de documento inválido.',
	4021: 'O nome do risco não deve ter mais de 500 caracteres.',
	4023: 'A lista de IDs de asserção não pode ser nula.',
	4024: 'O formulário de documentação de risco limitado não pôde ser criado devido a um erro. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	4025: 'ID de conta inválido.',
	4026: 'ID da assertiva inválido.',
	4027: 'O modelo do risco da assertiva não pode estar vazio',
	4031: 'Opção de Risco ou Corpo do Formulário inválida.',
	4035: 'Não é possível editar É Risco Mais Alto para riscos significativos ou de fraude.',
	4036: 'ID  de Conhecimento da Assertiva não pode ficar vazio se nenhum ID da Assertiva for passado. ID  de Conhecimento da Assertiva deve estar em enums',
	4037: 'ID  de Conhecimento da Assertiva já existe para esta conta.',
	4038: 'ID do Tipo de Risco não corresponde às opções permitidas.',
	4062: 'Esta SCOT não está mais disponível. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	4063: 'A relação da SCOT não pode ser editada. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	4076: 'This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	4079: 'This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*TASK*/
	5001: 'Get all tasks failed.',
	5002: 'Esta tarefa não está mais disponível neste engagement',
	5003: 'Obter documentos para a tarefa falha. Parâmetros de solicitação inválidos.',
	5004: 'Obter a tarefa relacionada ao documento falhou. Parâmetros de solicitação inválidos.',
	5005: 'Falha ao obter tarefa por id.',
	5006: 'Obter categorias de tarefas chamada falhou.',
	5007: 'Obter a chamada Subcategorias de tarefas falhou.',
	5008: 'Obter a chamada de descrição da tarefa falhou.',
	5009: 'Obter a chamada de clientes da tarefa falhou.',
	5010: 'Falha ao salvar chamada de solicitações de cliente de tarefa.',
	5011: 'A tarefa a que você está tentando relacionar itens foi excluída ou rejeitada.',
	5012: 'O item que você está tentando relacionar foi excluído.',
	5013: 'A tarefa a que você está tentando relacionar itens foi excluída ou rejeitada.',
	5014: 'O documento que você está tentando relacionar foi excluído.',
	5015: 'Obter a chamada de evidências de tarefa falhou.',
	5016: 'Obter wcgw chamada de tarefa falhou.',
	5017: 'O Id do engagement deve ser maior que zero.',
	5018: 'Não é possível concluir a associação. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	5019: 'A descrição da tarefa está vazia',
	5020: 'A tarefa selecionada foi excluída ou rejeitada. Como tal, esta ação não pode ser concluída neste momento.',
	5021: 'Falta o ID do engagement da fonte.',
	5022: 'Falha na anotação de salvar',
	5023: 'Usuário do engagement não encontrado.',
	5024: 'A chamada de exclusão da tarefa falhou.',
	5025: 'Excluir A chamada Tarefas falhou.',
	5026: 'A lista de tarefas está vazia',
	5027: 'Revisão não encontrada.',
	5028: 'O nome do arquivo é necessário.',
	5029: 'A extensão do arquivo é necessária.',
	5030: 'O nome do arquivo não pode incluir: */:<>\\?|"',
	5031: 'Erro atualizando o nome do documento.',
	5032: 'Documento inválido.',
	5033: 'Tipo de operação não encontrado.',
	5034: 'Falha no status da tarefa de alteração',
	5035: 'A ação que você está tentando realizar não pode ser concluída no momento. Por favor, tente novamente mais tarde. Entre em contato com o Help Desk se esse erro persistir.',
	5036: 'O corpo não pode ser nulo ou vazio na chamada.',
	5037: 'O pedido não pode ser nulo na chamada.',
	5038: 'Digite um nome de arquivo único para prosseguir.',
	5039: 'O nome do arquivo é necessário.',
	5040: 'O nome do arquivo é limitado a 100 caracteres.',
	5041: 'O nome do arquivo não pode incluir: */:<>\\?|"',
	5042: 'A Tarefa selecionada é rejeitada.',
	5043: 'A Tarefa é uma tarefa de construção.',
	5044: 'A Tarefa é um marco da Tarefa.',
	5045: 'A Tarefa não é nem do tipo PSP nem OSP.',
	5046: 'O limite de caracteres é excedido.',
	5047: 'O limite de caracteres é excedido.',
	5048: 'Campo obrigatório.',
	5049: 'O documento selecionado não pode ser removido desta tarefa. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	5050: 'A id do grupo de tarefas não deve ser zero ou inválida',
	5051: 'O id da seção de tarefas não deve ser zero ou inválido',
	5052: 'Erro ao tentar adicionar este documento à tarefa atual. A chamada falhou.',
	5053: 'Erro ao tentar atualizar este documento na tarefa atual. A chamada falhou.',
	5054: 'Erro ao tentar adicionar uma cópia deste documento na tarefa atual. A chamada falhou.',
	5055: 'Erro ao tentar renomear o documento. A chamada falhou.',
	5056: 'Não é possível editar o título da tarefa para conhecimento ou tarefa em grupo',
	5057: 'O tipo de tarefa deve ser Ost Type',
	5058: 'Este documento não pode ser removido da tarefa porque está associado ao sistema.',
	5059: 'Valor da fase de tempo inválido.',
	5060: 'Adicionar ao erro de evidência. Chamada falha',
	5061: 'Outro usuário atualizou as informações apresentadas. Atualize a página e tente novamente. Se o erro persistir, entre em contato com o Help Desk.',
	5062: 'O documento solicitado não está disponível para o canal selecionado do seu contrato no Atlas EY. Por favor, entre em contato com o Help Desk para que as informações possam ser dadas aos autores do conteúdo para futura inclusão.',
	5063: 'Operação de patch inválida.',
	5064: 'A tarefa selecionada foi excluída ou rejeitada. Como tal, esta ação não pode ser concluída neste momento.',
	5065: 'Não é possível atualizar o tipo de origem da tarefa. A solicitação é inválida.',
	5066: 'Obter erro de orientação. A chamada falhou.',
	5067: 'Não é possível atualizar o tipo de natureza da tarefa. A solicitação é inválida.',
	5068: 'Não é possível atualizar o tipo de natureza da tarefa. A solicitação é inválida.',
	5069: 'A chamada De exclusão da atribuição da tarefa falhou.',
	5070: 'Uma ou mais tarefas selecionadas foram excluídas. Por favor, tente novamente ou entre em contato com o Help Desk se o erro persistir.',
	5071: 'Não é possível atualizar a tarefa. A solicitação é inválida.',
	5072: 'Preparador não encontrado. A solicitação é inválida.',
	5073: 'Atribuição não encontrada.',
	5074: 'Salvar atribuição de tarefa falhou',
	5075: 'O mesmo membro da equipe só pode ser designado para uma tarefa que não seja o Preparer. Por favor, tente novamente ou entre em contato com o Help Desk se o erro persistir.',
	5076: 'O membro da equipe selecionado não é um membro ativo deste engagement.. Por favor, tente novamente ou entre em contato com o Help Desk se o erro persistir.',
	5077: 'Uma ou mais tarefas selecionadas foram excluídas. Por favor, tente novamente ou entre em contato com o Help Desk se o erro persistir.',
	5078: 'A tarefa selecionada foi removida. Por favor, tente novamente ou entre em contato com o Help Desk se o erro persistir.',
	5079: 'Uma ou mais tarefas selecionadas foram excluídas. Por favor, tente novamente ou entre em contato com o Help Desk se o erro persistir.',
	5080: 'O registro da versão do documento não existe.',
	5081: 'O usuário que está atualmente designado para esta tarefa não pode ser reatribuído como o Preparador. Por favor, tente novamente ou entre em contato com o Help Desk se o erro persistir.',
	5082: 'A atualização falhou. O nome do documento deve ser único para um engagement.o. Atualize a página para remover esta mensagem.',
	5083: 'O limite de caracteres de detalhes de tarefa é excedido.',
	5084: 'Falha na chamada de criação do documento de tarefa.',
	5085: 'Falha na chamada de exclusão do documento de tarefa.',
	5086: 'Falha na transferência de tarefas para criar chamada.',
	5087: 'A chamada de patch de tarefa falhou.',
	5088: 'Esta tarefa deve conter evidências para a entrega. Tente novamente ou entre em contato com o Help Desk.',
	5089: 'Todas as evidências associadas a esta tarefa (com exceção dos perfis de papel) devem ter pelo menos um preparador e revisor para marcar completas. Tente novamente e entre em contato com o Help Desk se o erro persistir.',
	5091: 'O nome do perfil do papel já existe no engagement..',
	5092: 'O nome não pode conter nenhum dos seguintes caracteres: */:<>\\?|\\',
	5093: 'O nome do paper profile excede o comprimento máximo (100 caracteres).',
	5111: 'A conta selecionada, a assertiva ou a conta de risco limitada foram excluídas. Atualize a página e tente novamente. Se o erro persistir, entre em contato com o Help Desk',
	5116: 'O tipo de documento é inválido.',
	5139: 'Não pode completar associação. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	5131: 'Não é possível completar a associação. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	5146: 'A tarefa não pode ser marcada como concluída.',
	5156: 'A relação da tarefa não pode ser editada. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',

	/*WCGW*/
	6001: 'Falha ao criar chamada WCGW.',
	6002: 'Nome WCGW pendente',
	6003: 'WCGW pendente',
	6004: 'Obter chamada WCGW falhou.',
	6005: 'Engagement Id inválido',
	6006: 'ID da assertiva inválida.',
	6007: 'Obter WCGW por chamada de identidade falhou.',
	6008: 'Pedido inválido.',
	6009: 'WCGW Id inválido.',
	6010: 'A tarefa não poderia estar associada ao WCGW.',
	6011: 'O WCGW selecionado foi excluído.',
	6012: 'A tarefa e o WCGW não estão relacionados com a mesma assertiva.',
	6013: 'A Tarefa selecionada não pertence ao mesmo Engagment.',
	6014: 'A tarefa não poderia ser dissociada do WCGW.',
	6015: 'A tarefa não poderia estar associada ao WCGW.',
	6016: 'A tarefa é rejeitada e não pode ser associada ao WCGW.',
	6017: 'A tarefa não é uma tarefa marcante e não pode ser associada ao WCGW.',
	6018: 'A tarefa não é uma tarefa de etapa de construção e não pode ser associada ao WCGW.',
	6019: 'A tarefa não é um PSP ou OSP e não pode ser associada ao WCGW.',
	6020: 'A tarefa e o WCGW estão associados à mesma assertiva e não podem ser associados ao WCGW.',

	/*Engagement*/
	7001: 'Obter por ID não encontrado.',
	7002: 'Obter engagements por chamada de ID do espaço de trabalho falhou.',
	7003: 'Falha na chamada Obter todas as entidades do engagement.',
	7004: 'Obter engajamento por chamada de ID falhou.',
	7005: 'Falha na chamada de todos os usuários de engagement.',
	7006: 'O sobrenome não deve exceder 250 caracteres.',
	7007: 'Erro de tipo de usuário inválido.',
	7008: 'O primeiro nome não deve exceder 250 caracteres.',
	7009: 'O GUI do usuário não pode ser nulo.',
	7010: 'Erro de status do usuário inválido.',
	7011: 'Criar chamada de usuário de engagement falhou.',
	7012: '{0} {1} não pode ser convidado porque já é um membro ativo ou pendente da equipe.',
	7013: 'As iniciais não devem exceder 3 caracteres.',
	7014: 'A Página de início do EY Canvas não está acessível no momento. Tente novamente e se o problema persistir, entre em contato com o Help Desk.',
	7015: '{0} {1} não pode ser convidado porque o (s) seguinte (s) grupo (s) de acesso: {2} foi excluído do engagament. Atualize e tente novamente.',
	7016: '{0} {1} não pode ser convidado, pois o usuário já é um membro ativo da equipe no seguinte grupo de acesso: {2}',
	7017: 'O domínio não é permitido para grupos selecionados: {0}',
	7018: 'O endereço de e-mail não deve ser nulo.',
	7019: 'O primeiro nome não deve ser nulo.',
	7020: 'Sobrenome não deve ser nulo',
	7021: 'A Inicial do Usuário não deve ser nula.',
	7022: 'O User PrimaryOffice não deve ser nulo.',
	7023: 'O Nome de Login de Usuário não deve ser nulo.',
	7024: 'O EYRole do usuário não deve ser nulo.',
	7025: 'A função do engagement do usuário não deve ser nula.',
	7026: 'Incapaz de completar a operação. Tente novamente e se o problema persistir, entre em contato com o Help Desk.',
	7027: 'Endereço de e-mail inválido',
	7028: 'O patch de chamada do usuário de engagement falhou.',
	7029: 'Usuários de engajgement - ID de status do usuário de engagement inválido.',
	7030: 'Usuários do engagement - ID de função de usuário do engagement inválida.',
	7031: 'Um ou mais usuários do engagement não foram encontrados.',
	7032: 'O e-mail não deve ser maior que 250 caracteres.',
	7033: 'O Usuário solicitado não pode ser Nulo.',
	7034: 'Falha na fila do processador de mensagens universal.',
	7035: 'As iniciais não devem exceder 3 caracteres.',
	7036: 'O primeiro nome não deve exceder 250 caracteres.',
	7037: 'O sobrenome não deve exceder 250 caracteres.',
	7038: 'Criar chamada de usuário externo falha.',
	7039: 'Um ou mais usuários não podem ser convidados porque já são um membro ativo ou pendente da equipe. Atualize a página e tente novamente, se o problema persistir entre em contato com o Help Desk',
	7040: 'O primeiro nome não deve exceder 250 caracteres.',
	7041: 'O sobrenome não deve exceder 250 caracteres.',
	7042: 'As iniciais não devem exceder 3 caracteres.',
	7043: 'GPN não deve ser maior que 250 caracteres.',
	7044: 'Gui não deve ser maior que 250 caracteres.',
	7045: 'Obter usuário externo por ID falhou.',
	7046: 'O usuário não deve ser nulo.',
	7047: 'Não pode salvar mudanças no momento. Tente novamente e se o problema persistir, entre em contato com o Help Desk.',
	7048: 'A Página de início do EY Canvas não está acessível no momento e é necessária para editar. Tente novamente e se o problema persistir, entre em contato com o Help Desk.',
	7049: 'Falha na chamada do usuário do Update Engagement.',
	7050: 'Os membros não podem ser desativados. O engagagement deve ter pelo menos um membro com permissão para administrar o engagement. Atualize sua seleção e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	7051: 'Obter usuário interno por ID falhou.',
	7052: 'Usuário não encontrado.',
	7053: 'Obter por ID não encontrado.',
	7054: 'Obtenha links rápidos por falha na chamada de ID do engagement.',
	7055: 'Direitos insuficientes para adicionar membros anteriores. Fale com um membro da equipe do engagagement com os direitos apropriados para tomar esta ação.',
	7056: 'EY Canvas não poderia salvar mudanças neste momento. Tente novamente e se o problema persistir, entre em contato com o Help Desk.',
	7057: 'Direitos insuficientes para adicionar novos membros. Fale com um membro da equipe do engagement com os direitos apropriados para tomar esta ação.',
	7058: 'O status do usuário mudou. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	7062: 'O EY Canvas Client Portal não está acessível no momento e é necessário atualizar as informações dos membros existentes. Tente novamente e se o problema persistir, entre em contato com o Help Desk.',
	7063: 'Não é possível desativar membros externos. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	7064: 'Operação de patch inválida.',
	7065: '{0} {1} não pode ser ativado em um ou mais grupos de acesso. O domínio não é permitido para grupos selecionados: {2}.',
	7066: '{0} não é um usuário válido.',
	7067: 'A chamada do usuário externo falhou.',
	7068: 'O EY Canvas Client Portal não está acessível no momento e é necessário atualizar as informações dos membros existentes. Tente novamente e se o problema persistir, entre em contato com o Help Desk.',
	7069: 'Os grupos de acesso selecionados não estão ativos: {0}. Remova e tente novamente.',
	7072: 'Ocorreu um erro durante o processo de desvinculação do engagement. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	7074: 'Mudanças não podem ser salvas. O engagement deve ter pelo menos um membro ativo que tenha permissões para administrar o engagement e tenha resolvido a independência. Se o problema persistir, entre em contato com o Help Desk.',
	7079: 'Falha no envio da chamada de independência.',
	7080: 'Identidade do usuário inválido ou o envio de independência não é permitido porque a identidade de usuário não pertence ao usuário conectado',
	7081: "Preencher todas as perguntas antes de clicar em'Enviar'. Use a opção'Mostrar incompleto' para filtrar para perguntas incompletas. Se o problema persistir, ligue para o Help Desk. ",
	7082: 'Nenhum documento de independência encontrado para a solicitação.',
	7083: 'Falha na chamada de Resumo SDM.',
	7084: 'A identidade do usuário não é válida ou a Ação de Independência não é permitida para o usuário logado.',
	7085: 'O comentário de independência deve ser inferior a 4.000 caracteres.',
	7086: 'A chamada da Ação de Independência falhou.',
	7087: 'Sua função deve ser Sócio Responsável; Sócio do Engagement; ou Diretor Executivo para conceder, negar ou substituir acesso para este usuário.',
	7088: 'As alterações de submissão de inpedendência falharam, tente novamente mais tarde.',
	7098: 'ID Pacetype inválido.',
	7099: 'Nenhum modelo de independência disponível, tente novamente e se o problema persistir, ligue para o Help Desk.',
	7154: 'Nenhum usuário encontrado para o formulário do Canvas atual',
	7155: 'O perfil de submtido não é permitido para o engagement restaurado.',
	7156: 'O conteúdo está sendo atualizado. Tente de novo mais tarde. Se o problema persistir durante o tempo prolongado contate o help desk de TI.',
	7158: 'Documento não disponível. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o help desk',

	/*SCOT*/
	8001: 'Criar chamada da SCOT falhou.',
	8002: 'Nome da SCOT pendente',
	8003: 'SCOT pendente',
	8004: 'Criar chamada da SCOT falhou.',
	8005: 'Engagement Id inválido',
	8006: 'ID da assertiva inválida.',
	8007: 'Obter Scot por ID falhou.',
	8008: 'Pedido inválido.',
	8009: 'The selected SCOT has been deleted. Close this modal to see the updated list.',
	8010: 'O ID da SCOT não pode ser nula ou vazia.',
	8011: 'O ID SCOT deve ser maior que zero.',
	8012: 'O ID  do documento não é válido.',
	8013: 'A chamada de atualização da SCOT falhou.',
	8014: 'O nome da SCOT não pode ser nulo ou vazio.',
	8015: 'O nome da SCOT não pode ter mais de 500 caracteres.',
	8016: 'O tipo de estratégia da SCOT é inválido.',
	8017: 'O tipo SCOT é inválido.',
	8018: 'A aplição de IT da SCOT é inválida.',
	8019: 'Scot ITApplication deve estar vazio quando HasNoITApplication for aplicado.',
	8028: 'The selected SCOT has been deleted. Close this modal to see the updated list.',

	/*User*/
	10001: 'Logado nas preferências do Usuário não encontradas.',
	10002: 'Chamada do Usuário GetAll falhou.',
	10003: 'Presença do usuário Receber chamada falhou.',
	10005: 'Incapaz de recuperar detalhes do usuário',

	/*Risk Type*/
	11001: 'Solicitação de criação inválida.',
	11002: 'Nome do RiskType ausente.',
	11003: 'Risco ausenteType.',
	11004: 'Obter riskType chamada falhou.',
	11005: 'Engagement Id inválido',

	/*TaskDocuments*/
	80004: 'Uma ou mais tarefas são inválidas.',
	80005: 'O ID do documento é inválido.',

	/*Edit Control*/
	83001: 'Falha ao obter controles.',
	83002: 'Falha ao obter controle por id.',
	83003: 'O ID do controle é nulo ou vazio.',
	83004: 'Id de controle inválido.',
	83005: 'Id do documento é inválido.',
	83006: 'Faltando nome do controle.',
	83007: 'Comprimento do nome de controle inválido.',
	83008: 'Solicitação inválida.',
	83009: 'Id da frequência de controle inválido.',
	83010: 'Id do tipo de controle inválido.',
	83011: 'Controle de Aplicativo de TI inválido.',
	83012: 'Id do Tipo de Controle com Desenho Efetivo inválido.',
	83013: 'Id do Aplicativo de IT inválido.',
	83014: 'Apenas Aplicativo de TI do tipo SO são permitidos, se o tipo de controle for prevenção manual ou detecção manual.',
	83015: 'Somente o ITDM pode ter o teste manual de IPE selecionado.',
	83016: 'Controles de risco baixo não são permitidos para trabalhos com este perfil.',
	83017: 'Filtros de SCOTs duplicados.',
	83018: 'O nome do controle tem mais de 500 caracteres.',
	83019: 'Ids dos WCGW inválidos.',
	83020: 'Ids dos WCGW duplicados não são permitidos.',
	83021: 'IDs de Aplicativos e TI duplicados não são permitidos.',
	83022: 'Parâmetro {0} não é válido.',
	83023: 'Página atual inválida.',
	83024: 'Tamanho de página inválido.',
	83025: 'Pesquisa string não deve ter mais de 100 caracteres.',
	83026: 'APP de TI sem serviços de organização só pode ser associado a controle do tipo Controle Manual Dependente de TI ou Controle de Aplicativo de Tl',
	83027: 'Filtros de WCGWs duplicados.',
	83028: 'Id de controle de conhecimento inválido.',

	112000: 'Nenhum documento encontrado para documentos de origem e destino.',
	112001: 'Não foi possível realizar a chamada porque a solicitação é nula.',
	112002: 'O corpo da solicitação não pode ser nulo ou vazio na chamada.',
	112003: 'ID do documento de origem e destino não devem ser iguais.',
	112004: 'ID do documento de origem não deve ser nulo ou vazio.',
	112005: 'O ID do documento de destino não deve ser nulo ou vazio.',
	112006: 'A fonte do ID do engagement  não deve ser nulo ou vazio.',
	112007: 'O ID do engagement de destino não deve ser nulo ou vazio.',
	112008: 'O documento de origem não é válido para determinado engagement.',
	112009: 'O documento de destino não é válido para determinado engagement.',
	112010: 'ID do engagement de destino não encontrado.',
	112011: 'Funções insuficientes para vincular formulários para o  engagement de origem. Trabalhe com o administrador do engagement para obter direitos suficientes.',
	112012: 'Funções insuficientes para vincular formulários para o engagement de destino. Trabalhe com um administrador de trabalho para obter direitos suficientes.',
	112013: 'Usuário inválido para o engagement de origem.',
	112014: 'Usuário inválido para o engagement de destino.',
	112015: 'Tipo de documento de origem inválido para compartilhamento.',
	112016: 'Tipo de documento de destino inválido para compartilhamento.',
	112017: 'Os tipos de documentos de origem e de destino não correspondem.',
	112018: 'Ids dos Formulários de conhecimento de origem e de destino não correspondem.',
	112019: 'O link de compartilhamento já existe para documentos de origem e destino.',
	112020: 'O espaço de trabalho dos Engagements de origem e de destino não corresponde.',
	112021: 'O documento de destino não pode ser um destino.',
	112022: 'A atividade selecionada já está compartilhada com outras atividades e não pode mais ser selecionada para compartilhamento.',
	112023: 'O documento de origem não pode ser um destino.',
	112024: 'Não é possível encontrar o ID do engagement do ID do documento de destino.',
	112025: 'Não é possível encontrar o ID do engagement do ID do documento de origem.',
	112026: 'O ID do documento de origem e destino não deve ser iguais.',
	112027: 'Não é possível prosseguir com o envio do perfil, pois o engagement compartilhou as atividades de capacitação do EY Canvas FIT. Desvincule as atividades para prosseguir.',
	112028: 'O ID do Engagement de origem e de destino não deve ser igual.',
	112029: 'O ID do engagement e o ID do documento de origem ou destino devem corresponder à rota',
	112030: 'O documento não é válido para determinado engagement.',
	112031: 'O ID do documento não deve ser nulo ou vazio.',
	112032: 'O ID do engagement não deve ser nulo ou vazio.',
	112033: 'Os IDs dos documentos de destino devem ser exclusivos.',
	112034: 'Documento(s) de destino ou origem já compartilhado(s).',
	112035: 'Para o documento de relação de resposta vinculado existente, só pode haver um único destino.',

	/*MissingDocument*/
	116001: 'Create form failed.',
	116002: 'Nenhum documento de conhecimento encontrado para o dado IDdoTipodeDocumento.',
	116004: 'Falha na criação do documento. Atualize ou tente novamente depois de algum tempo. Se o problema persistir, entre em contato com o Help Desk.',

	/* Annotation Errors*/
	12001: 'Parâmetros de solicitação inválidos.',
	12002: 'Criar chamada de anotação falhou.',
	12003: 'Obter anotação falhou.',
	12004: 'ID de contrato inválido.',
	12005: 'Os IDs devem ser maiores que zero.',
	12006: 'A coleta de id não pode estar vazia.',
	12007: 'ID de tarefa inválido.',
	12008: 'Identificação de documento inválida.',
	12009: 'Deve ter Documentoid válido ou ID de Tarefa.',
	12010: 'As respostas exigem pais.',
	12011: 'ID de status inválido.',
	12012: 'O tipo de documento deve ser de 440GL.',
	12013: 'Tipo de annontação inválida.',
	12014: 'Usuário do engagement inválido.',
	12015: 'Este documento foi excluído por outro usuário.',
	12016: 'A chamada de anotação de atualização falhou.',
	12017: 'A nota que você está respondendo foi excluída por outro membro da equipe. Atualize a página e tente novamente.',
	12018: 'O tipo de ação de alteração de anotação não deve ser nulo.',
	12019: 'Nota de revisão excluída.',
	12020: 'Ação inválida para realizar.',
	12021: 'O usuário não é autor de anotações.',
	12022: 'Usuário de autoria ausente.',
	12023: 'O usuário autor não existe ou pertence ao engagement.',
	12024: 'Usuário atribuído necessário.',
	12025: 'O usuário atribuído não existe ou pertence ao engagement.',
	12026: 'Comentários inválidos.',
	12027: 'Os comentários não devem estar vazios.',
	12028: 'Data de vencimento necessária.',
	12029: 'Prioridade válida necessária.',
	12030: 'O estado desta nota mudou. Você não pode mais editar ou responder à nota. Por favor, feche e reassum a janela para ver os dados atualizados e continue a edição.',
	12031: 'A anotação deve ser de alto nível.',
	12032: 'A anotação não deve ser de alto nível.',
	12033: 'Pelo menos um dos seguintes valores não deve estar vazio: tipo de prioridade, data de vencimento, status ou usuário atribuído.',
	12034: 'O comentário excede o comprimento máximo (4.000 caracteres).',
	12035: 'A sequência de pesquisa excede o comprimento máximo (500 caracteres).',
	12036: 'Apenas TaskId ou DocumentId devem ser aceitos ambos não são permitidos.',
	12037: 'Operação de patch inválida.',
	12038: 'Você está tentando editar uma nota que não existe mais. Atualize a página e tente novamente.',
	12039: 'Você está tentando editar a mesma nota que outro membro da equipe. Atualize a página e tente novamente.',
	12040: 'Obter a chamada dos usuários de Anotação falhou.',
	12041: 'Obter usuários de anotação falhou. Valor de consulta inválido.',
	12042: 'O tipo de documento não é válido para criar uma anotação.',
	12043: 'Você está tentando adicionar uma nota a uma tarefa ou documento que não existe mais. Atualize a página e tente novamente.',
	12044: 'Você está tentando editar uma resposta a uma nota que não existe mais. Atualize a página e tente novamente.',
	12045: 'Nota de revisão não encontrada.',
	12046: 'A nota selecionada já foi excluída por outro usuário.',
	12047: 'Você só pode excluir respostas para notas que estão em status Aberto. Atualize a página e tente novamente.',
	12048: 'Você está tentando mudar o status de uma nota que não existe mais. Atualize a página e tente novamente.',
	12049: 'Você está tentando excluir uma resposta a uma nota que já foi excluída por outro membro da equipe. Atualize a página e tente novamente.',
	12050: 'Você só pode excluir notas que estão em status fechado.',
	12051: 'Anotações do tipo de comentário só podem ser criadas para documentos válidos no Helix.',
	12052: 'A anotação do tipo Comment que você está procurando foi excluída.',
	12060: 'O número de referência é requerido e deve ser maior que 0 e menor do que 1000.',
	12061: 'O número de referência deve ser nulo.',
	12062: 'As anotações só podem ser criadas para um comentário.',
	12066: 'Você está tentando responder a uma nota que não está aberta.',
	12067: 'Você está tentando excluir uma resposta a um comentário que já foi excluído por outro membro da equipe. Atualize a página e tente novamente.',
	12068: 'Você está tentando adicionar uma nota com uma gravação inválida ou que não existe mais. Atualize a página e tente novamente.',
	12069: 'Você está tentando atualizar uma nota com uma gravação inválida ou que não existe mais. Atualize a página e tente novamente.',
	12070: 'Você só pode adicionar um comentário por demonstração financeira. Por favor, edite o existente.',
	12071: 'Falha ao excluir as informações. Por favor, tente novamente. Se o problema persistir, entre em contato com o help desk.',

	/*FlowchartStepControl*/
	123054: 'Control relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',
	123045: 'This control is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepWCGW*/
	123022: 'Este passo do fluxograma não está mais disponível. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	123023: 'Este passo do fluxograma não está mais disponível. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	123055: 'WCGW relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepITApplicationSO*/
	123056: 'IT Application / service organization relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the issue persists.',



	/*FlowchartStepDocument*/
	123048: 'A relação do documento não pode ser editada. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	123033: 'Este passo do fluxograma não está mais disponível. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	123002: 'Este documento não está mais disponível. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',

	/*Configuration*/
	13001: 'Configurações não encontradas.',
	13002: 'Obter configurações Api chamada falhou.',

	/*Documents*/
	14001: 'Não é possível realizar a chamada porque o pedido é nulo.',
	14002: 'Documento não encontrado. A chamada falhou.',
	14003: 'A id do engagement deve ser maior que zero.',
	14004: 'A identificação do documento não deve ser nula ou vazia.',
	14005: 'Erro ao tentar obter tarefas associadas. A chamada falhou.',
	14006: 'O documento selecionado não pode ser excluído. Tente de novo mais tarde. Se o problema persistir, entre em contato com o Help Desk.',
	14007: 'Um erro inesperado ocorreu.',
	14008: 'Ocorreu um erro inesperado durante a aprovação.',
	14009: 'Ocorreu um erro ao adicionar uma aprovação.',
	14010: 'O ID de aprovação deve ser maior que zero.',
	14011: 'Ocorreu um erro ao excluir uma aprovação.',
	14012: 'O corpo não pode estar vazio.',
	14013: 'O documento selecionado não pode ser desvinculado. Tente de novo mais tarde. Se o problema persistir, entre em contato com o Help Desk.',
	14014: 'Obter conta por chamada de identificação de documento falhou.',
	14015: 'Entidade inválida relacionada.',
	14016: 'Obter a chamada de aprovação do documento falhou.',
	14017: 'A chamada GetAll Document Findings falhou.',
	14018: 'A chamada getAll Documents falhou.',
	14019: 'Assinatura não encontrada.',
	14020: 'Valor de ação inválido.',
	14021: 'Tipo de achado inválido.',
	14022: 'O documento não pertence ao engagement.',
	14023: 'O tipo de alteração do documento não é válido.',
	14024: 'A chamada getAll Documents falhou. O parâmetro não é válido.',
	14025: 'Ocorreu um erro ao criar a revisão do documento. A chamada da API falhou.',
	14026: 'Ocorreu um erro ao excluir a revisão do documento. A chamada da API falhou.',
	14027: 'A ID de Contrato não deve ser nula ou vazia.',
	14028: 'O ID do usuário é inválido.',
	14029: 'O Usuário não está autorizado a realizar esta ação.',
	14030: 'Nenhum documento com a identificação passada foi encontrado neste engagement.',
	14031: 'O ID de revisão de documentos não é válido.',
	14032: 'A Revisão de Documentos não foi encontrada.',
	14033: 'O Documento já foi aprovado.',
	14034: 'Um processo de desvinculação está em andamento para outro engagement no espaço de trabalho ou este documento já foi desvinculado. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	14035: 'O documento não é compartilhado com o engagement dado.',
	14036: 'O número da versão deve ser maior que zero.',
	14037: 'A operação não pode ser completa no momento. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk',
	14038: 'Não conseguiu obter razões de alteração de documentos.',
	14039: 'Não conseguiu obter a razão de alteração de documento por identificação.',
	14040: 'Falha ao atualizar a razão da mudança.',
	14041: 'Razão de mudança inválida.',
	14042: 'A razão da mudança de atualização falhou.',
	14043: 'Falhou em criar uma razão de mudança.',
	14044: 'Não foi possível excluir a razão da alteração.',
	14045: 'ID de mudança inválida.',
	14046: 'O documento não está disponível. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	14047: 'O documento já tem uma razão de alteração atribuída.',
	14048: 'O documento tem um conflito, por favor, resolva antes de prosseguir.',
	14049: 'Dados inválidos para verificar se o documento é adicionado à equipe ou ao usuário.',
	14050: 'Erro ao remover documento de referência.',
	14052: 'O texto da pesquisa excede o comprimento máximo permitido (500 caracteres).',
	14053: 'Não é possível realizar a chamada porque o pedido é nulo.',
	14054: 'Operação de patch inválida.',
	14055: 'A chamada de histórico do resolver documento falhou.',
	14056: 'Ocorreu um erro ao tentar fazer fila na mensagem.',
	14057: 'Um documento com o mesmo nome já existe no engagement.',
	14058: 'Foi encontrada mais de uma versão de documento com o mesmo número. Entre em contato com o Help Desk para obter mais assistência.',
	14059: 'A versão do documento selecionado não pôde ser encontrada. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk para obter mais assistência.',
	14060: 'A operação não pôde ser concluída no momento. Por favor, tente novamente mais tarde. Se o problema persistir, entre em contato com o Help Desk para obter mais assistência.',
	14061: 'O documento não está compartilhado, não é possível desvincular o documento. Se o problema persistir, entre em contato com o Help Desk para obter mais assistência.',
	14062: 'O ID do tipo de confidencialidade do documento não é válido.',
	14063: 'Não é possível alterar o tipo de confidencialidade deste tipo de documento.',
	14064: 'O usuário não tem permissão.',
	14065: 'Descrição personalizada inválida.',
	14066: 'Falha ao atualizar a extensão.',
	14067: 'Extensão inválida.',
	14068: 'Falha ao obter as extensões do documento.',
	14069: 'Este documento já foi desvinculado por outro membro da equipe. Atualize a página e entre em contato com o Help Desk se o erro persistir ',
	14070: 'Invalid file type.',
	14071: 'The selected document version is no longer available. Please close and reopen this window to see the latest set of historical versions for the document.',
	14072: 'O ID do documento de origem não deve ser nula ou vazia.',
	14073: 'Os IDs dos formulários do Canvas não devem ser nulos ou vazios.',
	14074: 'Os IDs do formulário do Canvas não devem ser duplicados.',
	14075: 'Falha ao associar o documento aos formulários do Canvas.',
	14076: 'Formulários do Canvas associados ao documento de origem não encontrados.',
	14077: 'Documento de origem não encontrado. Chamada falhou.',
	14078: 'O documento atual já está associado a determinados formulários do Canvas.',
	14079: 'This document has been deleted and therefore it cannot be opened.',
	14080: 'The source approval user id is invalid.',
	14081: 'The source approval user id should valid GUID and must not be empty GUID.',
	14082: 'The modify user id is invalid.',
	14083: 'The modify user id should be a valid GUID and must not be empty GUID.',
	14084: 'File name cannot include: */:<>\\?|""',
	14085: 'The document name exceeded maximum length allowed.',
	14086: 'DocService failed while updating document details.',
	14087: 'The input is not valid.',
	14088: 'A input has duplicate document names.',
	14089: 'The bookmark observation is not valid.',
	14090: 'Request status has changed. Please refresh the page and try again if required. If the issue persists, contact the help desk.',
	14091: 'This request has been deleted. Refresh the page to view updated data. If the issue persists, contact the Help Desk.',
	14092: 'Document not eligible for update. Refresh the page and try again.  If the issue persists, contact the Help Desk.',

	/*SEM*/
	15001: 'Obter RESUMO SEM para uma chamada de id de conta falhou devido a solicitação inválida.',
	15002: 'Id de conta inválida.',
	15003: 'Engagement Id inválido',
	15004: 'Tipo de resumo inválido.',
	15005: 'A conta relacionada não pode ser encontrada. Atualize a página e tente novamente. Entre em contato com o Help Desk se o problema persistir.',

	/*Timephase*/
	16001: 'Obter fases de tempo falhou.',
	16002: 'A id do engagement deve ser maior que zero.',
	16003: 'A fase temporal deve ser maior que zero.',
	16004: 'Valor de ID de tarefa inválido.',
	16005: 'Valor da fase de tempo inválido.',

	/*Validations*/
	17001: 'Identificação de passo de construção ausente ou documento tipo id.',
	17003: 'The document could not be found. Refresh the page and try again. If the issue persists, contact Help Desk.',

	/*TaskGroupSection*/
	18001: 'A chamada da seção getall task group falhou.',

	/*Assignments*/
	19001: 'Criar chamada de atribuição falhou.',
	19002: 'A chamada de atribuição falhou.',

	/*Client Request*/
	21001: 'A chamada de associação de solicitação de clientes falhou.',

	/*Related Components*/
	22001: 'Obter componentes relacionados chamada falha.',

	/*Component Ceation*/
	22022: 'O nome do componente já existe neste engagement',
	22024: 'O componente para o qual você está tentando enviar instruções não está disponível ou já foi excluído.',
	22027: 'Instrução de grupo encontrada sem data de vencimento.',
	22028: 'As instruções de escopo ainda não foram publicadas para o componente.',
	22029: 'Nenhuma nova instrução publicada para o componente enviar.',

	22040: 'Não foi possível enviar instruções. Verifique se o tipo de engagement está correto.',
	22048: 'O componente que você está tentando atualizar é copiado de outro engagement enquanto copia o engagement.',

	/*Send Instruction*/
	22049: 'Group instructions cannot be sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk.',

	/*User Presence*/
	23001: 'Registrado na presença do usuário não encontrado.',
	23002: 'A presença do usuário foi chamada.',
	23003: 'Falha na validação do documento de presença do usuário.',
	23004: 'Falha na chamada de exclusão da presença do usuário.',
	23005: 'O documento não é mais aberto por outros usuários. Atualize a página e tente novamente. Entre em contato com o Help Desk se o problema persistir.',

	/* Forms */
	24001: 'Obter chamada de formulário falhou.',
	24002: 'ID de contrato inválido.',
	24003: 'A identificação do documento não deve ser nula ou vazia.',
	24005: 'Cabeçalho do formulário não encontrado.',
	24004: 'O documento não foi encontrado. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	24006: 'Section is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24007: 'Parâmetros de solicitação inválidos.',
	24008: 'O ID do cabeçalho não deve ser nulo ou vazio.',
	24009: 'O ID da seção não deve ser nulo ou vazio.',
	24010: 'Falha na operação de atualização de resposta do corpo do formulário.',
	24011: 'Solicitação inválida para atualização de resposta do corpo do formulário.',
	24012: 'Body is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24013: 'ID de opção do corpo do formulário inválido.',
	24014: 'ID do tipo de corpo do formulário inválido.',
	24015: 'Corpo de solicitação inválido para o ID de tipo de corpo fornecido.',
	24016: 'Texto livre inválido para o ID do tipo de corpo fornecido.',
	24017: 'Caracteres máximos, inclui tags de formatação de texto ricos. Reduza o comprimento ou remova a formatação desnecessária e tente novamente.',
	24018: 'Para determinado tipo de corpo, a resposta não é permitida.',
	24019: 'O ID do corpo não deve ser nulo ou vazio.',
	24020: 'Corpo de solicitação inválido.',
	24021: 'O corpo é apagado.',
	24022: 'O país não deve ser nulo ou vazio.',
	24023: 'O idioma não deve ser nulo ou vazio.',
	24024: 'A subsericeLine não deve ser nula ou vazia.',
	24025: 'GamLayers não devem ser nulos ou vazios.',
	24026: 'Chamada de criação de cabeçalho falhou.',
	24027: 'Solicitação inválida para criação de cabeçalho.',
	24028: 'Chamada de criação de unidade duplicada falhou.',
	24029: 'Tipo de unidade de formulário inválido.',
	24030: 'A seção foi excluída. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	24031: 'Corpo de texto não é corpo de texto personalizado.',
	24032: 'Pedido de criação de cabeçalho não é válido.',
	24033: 'O ID da entidade do documento fornecido não é válido.',
	24034: 'O ID de entidade fornecido não é válido.',
	24035: 'Ocorreu um erro ao criar o documento relacionado.',
	24036: 'Ocorreu um erro ao excluir o documento relacionado.',
	24037: 'O documento relacionado não poderia ser nulo ou vazio.',
	24038: 'O documento não é válido ou não existe.',
	24039: 'O documento relacionado não é válido ou não existe.',
	24040: 'Falha na criação do corpo personalizado.',
	24041: 'Body creation request is not valid.',
	24042: 'Solicitação de criação de seção não é válida.',
	24043: 'Section get by id call failed.',
	24044: 'Criação de seção falhou.',
	24045: 'Página atual inválida.',
	24046: 'Tamanho de página inválido.',
	24047: 'ID de objeto relacionado ao documento inválido.',
	24048: 'O objeto já está relacionado com o formulário do Canvas.',
	24049: 'Objeto não encontrado.',
	24050: 'O UId da Entidade informado não é válido.',
	24051: 'Se o Id da Entidade foi fornecido, o Uid da Entidade deve ser fornecido e vice-versa.',
	24052: 'Falha na criação do formulário intantâneo do Canvas.',
	24053: 'Cabeçalho por chamada de id falhou.',
	24054: 'Corpo por chamada de id falhou.',
	24055: 'Ocorreu um erro na criação do formulário perfil.',
	24056: 'Documento FomulárioPerfil já existe.',
	24057: 'Falha na validação do documento FomulárioPerfil.',
	24058: 'Documento FomulárioPerfil não existe.',
	24059: 'Seção não personalizada.',
	24060: 'Falha na validação do Formulário de Perfil do Documento. Se o PCAOB-IA for verdadeiro, então o PCAOB-FS deve ser verdadeiro.',
	24061: 'Falha na validação do Formulário de Perfil do Documento. Se o PCAOB-FS é falso, então o PCAOB-IA deve ser falso.',
	24062: "Falha na validação do Formulário de Perfil do Documento. Se'Não-complexo' então'PCAOB - FS'/'PCAOB - IA' deve ser falso. ",
	24063: 'IdPaís é inválido.',
	24064: 'IdIdioma é inválido.',
	24065: 'Cabeçalho não é personalizado.',
	24066: 'A criação do objeto relacionado à seção do formulário falhou.',
	24067: 'Objeto não encontrado.',
	24068: 'Wcgw não está relacionado com a Scot.',
	24069: 'O parâmetro {0} não é válido.',
	24070: 'O Uid informado na entidade-mãe não é válido.',
	24071: 'Falha ao obter o objeto relacionado à seção do formulário.',
	24072: 'A seção não está disponível. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	24073: 'Não é possível recuperar a imagem instantânea. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	24074: 'Capturas instantâneas não disponíveis para o documento selecionado. Atualize a página e tente novamente. Se o problema persistir entre em contato com o Help Desk.',
	24075: 'ID de snapshot é inválido.',
	24076: 'O Id da Entidade não deve ser nulo ou vazio',
	24077: 'A identidade do objeto relacionado à seção do formulário transmitido não é válida.',
	24078: 'A identidade do objeto relacionada da seção do formulário não deve ser nula ou vazia.',
	24079: 'O Id da Entidade Mãe Fornecido não é válido.',
	24080: 'Objeto relacionado à seção do formulário: Registro de entidade mãe não encontrado.',
	24081: 'Objeto não encontrado.',
	24082: 'Objeto não encontrado.',
	24083: 'Um erro ocorreu na atualização do formulário de perfil.',
	24084: 'O documento do Formulário de Perfil não existe.',
	24085: 'O id do idioma deve ser maior que 0.',
	24086: 'O id do país deve ser maior que 0.',
	24087: 'Documentos transmitidos de conhecimento não podem ser atualizados. Atualize o Perfil do Engagement para alterar este perfil de formulários.',
	24088: 'Funções insuficientes para editar conteúdo. Trabalhe com um administrador de engagement para obter direitos suficientes.',
	24089: 'O nome do cabeçalho personalizado excedeu o comprimento máximo (500 caracteres). Ajuste o nome e tente novamente.',
	24090: 'O nome da seção personalizada excedeu o comprimento máximo (500 caracteres). Ajuste o nome e tente novamente.',
	24091: 'O rótulo personalizado da seção excedeu o comprimento máximo (100 caracteres). Ajuste o nome e tente novamente.',
	24092: 'O nome do corpo personalizado excedeu o comprimento máximo (500 caracteres). Ajuste o nome e tente novamente.',
	24093: 'O nome da seção personalizada não deve ser nulo ou vazio.',
	24094: 'O nome do corpo não deve ser nulo ou vazio.',
	24096: 'Nome do cabeçalho personalizado não deve ser nulo ou vazio.',
	24097: 'A substituição não pode ser concluída neste momento.',
	24098: 'O documento de origem ou de destino não é válido.',
	24099: 'O documento de origem e destino do formulário de conhecimento não é o mesmo.',
	24100: 'A identidade do documento de origem não deve ser nula ou vazia.',
	24101: 'A identidade do documento de destino não deve ser nula ou vazia.',
	24103: 'O cabeçalho foi apagado. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	24104: 'O cabeçalho não pode ser editado.',
	24105: 'O corpo não pode ser editado.',
	24106: 'A ação de atualização é inválida.',
	24107: 'Falha na validação do Formulário do Documento. Se o PCAOB-FS é verdadeiro, então o complexo deve ser verdadeiro.',
	24108: 'Falha na validação do Formulário do Documento. Se o PCAOB-IA for verdadeiro, então o PCAOB-FS deve ser verdadeiro.',
	24110: 'Atualização de conteúdo em andamento. Atualize manualmente o conteúdo deste formulário na página de atualização de conteúdo do Canvas assim que a atualização de conteúdo atual for concluída.',
	24111: 'Os valores do Map & Unlink não podem ser os mesmos.',
	24112: 'O documento fonte não deve ser compartilhado.',
	24114: 'Falha em adicionar as evidências. Atualize a página e tente novamente.',
	24115: 'Falha ao remover as evidências. Atualize a página e tente novamente.',
	24116: 'Falha ao enviar o perfil. Atualize a página e tente novamente.',
	24117: 'O formulário tem respostas incompletas. Atualize a página e tente novamente.',
	24118: 'O ID do documento e o ID do documento relacionado não podem ser iguais.',
	24119: 'A edição de objetos relacionados ao corpo falhou.',
	24120: 'O ID do objeto relacionado ao corpo não deve ser nulo ou vazio.',
	24121: 'Objeto não encontrado.',
	24122: 'Token de simultaneidade ausente',
	24124: 'Documentos de destino inválidos encontrados.',
	24125: 'Formulários de tela de destino não encontrados.',
	24126: 'A solicitação não deve ser nula ou vazia.',
	24127: 'A importação de dados da EY Helix não foi bem sucedida. Importe os dados novamente das configurações do EY Helix e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	24155: 'O envio do perfil não é permitido para o engagement restaurado.',
	24164: 'Você não tem as permissões apropriadas para fazer alterações no modelo.',
	24166: 'Salvar perfil não é permitido. O formulário não está disponível para este perfil.',
	24167: 'O tipo de corpo não pode ser atualizado',
	24168: 'Modificar ID do usuário não deve ser nulo ou vazio',
	24169: 'Modificar ID do usuário só pode ter valor quando a Atualização da Resposta Linkada for falsa',
	24170: 'ID de tarefa inválido na solicitação',
	24171: 'IDs de corpo duplicados na solicitação',
	24172: 'Falha na chamada de comparação de seção',
	24173: 'Os IDs dos documentos não podem estar vazios. Também não pode exceder 50 documentos distintos por vez',
	24174: 'O rota do documento deve fazer parte dos documentos do corpo da solicitação',
	24175: 'ID de entidade de evidência inteligente inválido no corpo da solicitação',
	24176: 'Os documentos do corpo devem ter o mesmo ID do formulário de conhecimento',
	24177: 'ID de seção inválido',
	24178: 'Combinação inválida do ID do Documento e ID da Lista Documento',
	24179: 'A lista documentos não pode ser maior que 50',
	24180: 'Não foi possível atualizar a materialidade',
	24181: 'ID do Corpo do Formulário Inválido',
	24182: 'ID do Formulário de Conhecimento ou ID do Corpo do Formulário de Conhecimento Inválido',
	24183: 'Ocorreu um erro ao relacionar o documento a este formulário. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	24184: 'O usuário não está ativo para este engagement do EY Canvas. Convide o usuário por meio da equipe de gerenciamento e tente novamente. Se o problema persistir entre em contato com o Help Desk',
	24188: 'One or more records are no longer available. Please refresh the page',

	/*Document Change Types*/
	25001: 'Obter a chamada Tipos de alteração de documentos falhou.',
	25002: 'Engagement Id inválido',
	25003: 'Pedido inválido.',

	/* Manage team */
	26001: 'ClientUserGroups GetAll chamada falhou.',
	26002: 'Criar nova chamada de domínio do Client Group/ E-mail falhou.',
	26003: 'GroupName não deve ser nulo.',
	26004: 'O domínio de e-mail não deve ser nulo.',
	26005: '{0} rótulo de domínio de e-mail não deve exceder 263 caracteres.',
	26006: '{0} domínio de e-mail não deve exceder 263 caracteres.',
	26007: '{0} Primeira parte do domínio de e-mail pode ser * ou alfanumérico, outros caracteres especiais não são permitidos.',
	26008: '{0} Se a primeira parte é um curinga, então ele deve ter duas ou mais partes a seguir.',
	26009: '{0} Primeira parte pode ser * ou alfanumérica, outras partes de domínio caracteres especiais não são permitidos.',
	26010: 'O domínio de e-mail deve ser único.',
	26011: 'Desde que o ID do grupo de acesso ao cliente não seja válido.',
	26012: 'Ocorreu um erro ao excluir o grupo de acesso. Certifique-se de que não há solicitações/tarefas externas atribuídas a este grupo ou aos membros deste grupo e tente novamente.',
	26013: 'O grupo de acesso já foi excluído, atualize a página para obter os dados mais recentes.',
	26014: 'Pelo menos um domínio de e-mail é necessário.',
	26015: 'O EY Canvas Client Portal não está acessível no momento e é necessário atualizar as informações dos membros existentes. Tente novamente e se o problema persistir, entre em contato com o Help Desk.',
	26016: 'Erro durante a operação de exclusão.',
	26017: 'Obter falha na operação de dados.',
	26018: 'Problema de concorrência, o grupo de acesso não está mais ativo.',
	26019: 'A operação save falhou.',
	26020: 'Os domínios de e-mail não podem ser removidos quando têm usuários ativos atribuídos. As mudanças não foram salvas.',

	/* TimePhaseTypes - Milestones */
	27001: 'Obter detalhes de marcos falhou.',

	/*Client Request Counts*/
	28001: 'Obter solicitações de clientes A chamada de informações não obteve resultados.',

	/*Content updates Error messages*/
	29001: 'Obter por ID não encontrado.',
	29002: 'Acton tipo não encontrado.',
	29003: 'IDs de conteúdo não encontrados.',
	29004: 'A chamada de API de atualização de conteúdo falhou.',
	29005: 'Atualização de conteúdo em andamento. Tente novamente mais tarde e se o problema persistir, entre em contato com o Help Desk.',
	29006: 'Parâmetros de solicitação inválidos.',

	/*IT Process*/
	30001: 'Obter todos os ITProcess falhou.',
	30002: 'Obtenha todo o ITProcess - ID de contrato inválido.',
	30003: 'O nome ITProcess não pode estar vazio.',
	30004: 'ITProcessName não deve ter mais de 500 caracteres.',
	30005: 'Esta ação não pôde ser concluída. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	30006: 'O processo por chamada de id falhou.',
	30007: 'Solicitação inválida',
	30008: 'O processo de TI não está mais disponível. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	30009: 'Documento não encontrado.',
	30010: 'Não é possível concluir a exclusão. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	30012: 'Não é possível concluir a exclusão. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	30017: 'Esta ação não pôde ser concluída. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	30018: 'Esta ação não pôde ser concluída. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	30019: 'Esta ação não pôde ser concluída. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',

	/*Checklist*/
	31001: 'A chamada GetAll CheckLists falhou.',
	31002: "A chamada'Put CheckLists' falhou. ",
	31003: 'Parâmetro de lista de verificação inválido.',
	31004: 'Erro de engagement inválido.',
	31005: 'Erro de parâmetros de solicitação inválido.',
	31006: 'Erro do parâmetro de solicitação ischecked inválido.',

	/*Archive*/
	32001: 'O ID do Status de engagementé inválido.',
	32002: 'O arquivo falhou.',
	32003: 'V1 ARC Call está falhando, atualize a página, resolva as validações e tente o processo de archive novamente. Entre em contato com o Help Desk se o erro persistir.',
	32004: 'Atualização de status do engagement no LDC falhou.',
	32005: 'Erro de cache de engagement inválido.',
	32006: 'Uma atualização de conteúdo está em andamento. Você não pode arquivar este contrato até que a atualização de conteúdo esteja completa. Tente novamente mais tarde e entre em contato com o Help Desk se o erro persistir.',
	32007: 'ArcGUID é nulo ou vazio.',
	32008: 'FileGuid é nulo ou vazio.',
	32009: 'FileStoreHostTcp é nulo ou vazio.',
	32200: 'Há validações não resolvidas neste engagement. Atualize a página, resolva as validações e tente o processo de archivenovamente. Entre em contato com Help Desk se o erro persistir.',
	32300: 'Há validações não resolvidas neste contrato. Atualize a página, resolva as validações e tente o processo de arquivamento novamente. Entre em contato com o Help Desk se o erro persistir.',

	/*RBAC*/
	33001: 'Engagement Id não encontrado',
	33002: 'ID do usuário não encontrado.',

	/*Helix Linked Projects*/
	34001: 'Não é possível realizar a chamada porque o pedido é nulo.',
	34002: 'A ID do engagement não deve ser nula ou vazia.',
	34003: 'O corpo não pode ser nulo ou vazio na chamada.',
	34004: 'A ID do projeto não pode ser nula ou vazia.',
	34005: 'O Nome do Projeto não pode ser nulo ou vazio.',
	34006: 'O projeto já foi vinculado, atualize a página e tente novamente.',
	34007: 'A chamada do GetAll Helix Projects falhou.',
	34008: 'A ID do engagement deve ser maior que zero.',
	34010: 'Salvar não pôde ser concluído. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	34009: 'A ID do projeto mudou. Atualize a página e tente novamente.',
	34011: 'O Tipo de Moeda não pode ser nulo na chamada.',
	34012: 'O Código de Moeda não pode ser nulo na chamada.',
	34013: 'A Unidade de Negócios não pode ser nula ou vazia na chamada.',
	34014: 'O Projeto EY Helix vinculado foi alterado, não pode atualizar as configurações.',
	34017: 'Não foi possível conectar-se ao EY Helix. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	34018: 'A operação não pôde ser concluída. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	34019: 'Importação concluída, mas os dados não são válidos. Atualize a página e clique em Importar para tentar novamente.',
	34027: 'O Projeto Helix está em andamento.',
	34036: 'Não foi possível conectar ao EY Helix. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	34039: 'Este projeto EY Helix não é mais o projeto principal para o seu engagement. Atualize a página e tente novamente.',
	34040: 'Você deve ter permissão de <b>EY Helix Import</b> para realizar esta ação. Fale com um administrador do engagement para obter acesso.',

	/* PAANS */
	35001: 'O Serviço de Autoria, Aprovação e Notificação de Políticas EY não está disponível no momento. Não podemos confirmar se todas as políticas relevantes foram concluídas. Se você não revisou as políticas relevantes, atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk. ',
	/*Engagement Comments*/
	38001: 'Falha ao criar chamada de comentários do engagement',
	38002: 'Falha na chamada de Obter todos os comentários do engagement',
	38003: 'Não é possível concluir esta ação. Atualize a página e tente novamente. Se o erro persistir entre em contato com o Help Desk',
	38004: 'Não é possível concluir esta ação. Atualize a página e tente novamente. Se o erro persistir entre em contato com o Help Desk',
	38005: 'Não é possível concluir esta ação. Atualize a página e tente novamente. Se o erro persistir entre em contato com o Help Desk',
	38006: 'Não é possível concluir esta ação. Atualize a página e tente novamente. Se o erro persistir entre em contato com o Help Desk',
	38007: 'Não é possível concluir esta ação. Atualize a página e tente novamente. Se o erro persistir entre em contato com o Help Desk',
	38008: 'Não é possível concluir esta ação. Atualize a página e tente novamente. Se o erro persistir entre em contato com o Help Desk',
	38009: 'Não é possível concluir esta ação. Atualize a página e tente novamente. Se o erro persistir entre em contato com o Help Desk',
	38010: 'O corpo não deve ser nulo',
	38011: 'O texto do comentário não deve ser nulo ou vazio',
	38012: 'A entidade não existe em determinado engagement',
	38013: 'O ID do status do comentário do engagement deve ser maior que 0',
	38014: 'O ID do comentário do engagement pai deve ser maior que 0',
	38015: 'Não havia nenhum corpo de formulário de tela correspondente aos critérios especificados',
	38016: 'A nota que você está respondendo foi excluída por outro membro da equipe. Atualize a página e tente novamente',
	38017: 'O comentário do engagement pai fornecido foi apagado',
	38018: 'O comentário do engagement fornecido é uma resposta em si',
	38019: 'O texto do comentário fornecido não deve ser menor que 1 e não maior que 4000 caracteres',
	38020: 'O uid da entidade fornecido é inválido',
	38021: 'O uid da entidade pai fornecido é inválido',
	38022: 'Falha ao excluir a chamada de comentários do engagement',
	38023: 'O id do comentário não deve estar vazio',
	38024: 'A ação deve ser uma ação válida',
	38025: 'Você está tentando excluir um comentário que não existe mais. Atualize a página e tente novamente.',
	38026: 'Você não é o dono do comentário',
	38027: 'Não foi encontrado nenhum comentário para atualizar',
	38028: 'Atualizações em comentários limpos não são permitidas',
	38029: 'Somente o autor pode alterar o texto do comentário',
	38030: 'O ID do comentário do engagement é inválido',
	38031: 'O ID do status do comentário não deve estar vazio',
	38032: 'Não houve registro de relacionamento entre o usuário solicitado e o projeto. Atualize a página e tente novamente. Se o erro persistir entre em contato com o Help Desk',
	38033: 'A ação de atualização é inválida',
	38034: 'O ID do comentário solicitado já está sendo usado por outro comentário',
	38035: 'O ID do comentário fornecido é inválido',
	38036: 'O ID do documento não deve ser nulo ou vazio',
	38037: 'O ID do usuário ao qual o comentário será atribuído não deve ser nulo',
	38038: 'O comentário não deve ser reatribuído ao abri-lo ou fechá-lo',
	38039: 'A nota da qual você está excluindo uma resposta já foi excluída. Atualize a página e tente novamente',
	38040: 'A resposta não deve ter data de vencimento',
	38041: 'A resposta não deve ter prioridade',
	38042: 'O comentário deve ter data de vencimento',
	38043: 'Comentário deve ter prioridade',
	38044: 'Você está tentando editar uma resposta a um comentário que não existe mais. Atualize a página e tente novamente',
	38045: 'Não é permitido editar o ID do status do engagement e seu conteúdo atribuído ao usuário ou prioridade',
	38046: 'A chamada de atualização de comentários de engagement falhou',
	38047: 'Você só pode responder a notas abertas',
	39004: 'This tag group is no longer available. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*Risk-Estimate*/
	4064: 'Este risco não está mais disponível. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	4065: 'A relação de risco não pode ser editada. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	4066: 'Esta estimativa não está mais disponível. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',

	/*IT App/SO*/
	81001: 'Falha ao obter todos os aplicativos ITA.',
	81002: 'Obtenha todos os aplicativos ITA - ID de compromisso inválido.',
	81003: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81004: 'Não foi possível concluir a operação. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	81005: 'Não foi possível concluir a operação. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	81006: 'Tipo de estratégia inválida.',
	81007: 'ID de documento inválida.',
	81008: 'Falha ao obter ITApplication por ID.',
	81009: 'Não foi possível concluir a operação. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	81010: 'Não foi possível concluir a operação. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	81011: 'Não foi possível concluir a operação. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	81012: 'ITApplicationID não deve ser nulo ou vazio.',
	81013: 'A relação não pode ser editada. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	81014: 'Falha na exclusão da chamada ITApplicationITProcess.',
	81015: 'ITApplicationITProcessId não deve estar vazio.',
	81016: 'ITApplicationITProcessId inválido.',
	81017: 'A relação não pode ser editada. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	81018: 'Falha ao criar chamada ITApplicationITProcess.',
	81019: 'Organização de serviço aprovada em vez de ITApplication.',
	81020: 'Não é possível concluir a exclusão. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	81039: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81041: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/* ITControl */
	84001: 'Falha ao obter controles de TI.',
	84002: 'Falha ao obter controle de TI por id.',
	84003: 'A ID de controle de TI é nula ou vazia.',
	84004: 'ID de controle de TI inválido.',
	84005: 'A ID do documento é inválida.',
	84006: 'Falta o nome do controle de TI.',
	84007: 'Comprimento do nome do controle de TI inválido.',
	84008: 'FrequencyId de controle de TI inválido.',
	84009: 'ApprochTypeId de controle de TI inválido.',
	84010: 'DesignEffectivenessTypeId de controle de TI inválido.',
	84011: 'Valor de teste de controle de TI inválido.',
	84012: 'Controle de TI inválido OperatingEffectivenessTypeId.',
	84013: 'Frequência de controle de TI ausente.',
	84014: 'Falha ao excluir o controle de TI.',
	84015: 'A string de pesquisa não deve ter mais de 100 caracteres.',
	84016: 'Não foi possível concluir a operação. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	84018: 'Não foi possível concluir a operação. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	84019: 'Não é possível completar a atualização. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',

	/*ITRisk*/
	86001: 'O nome ITRisk não pode estar vazio.',
	86002: 'O nome ITRisk não deve ter mais de 500 caracteres.',
	86003: 'Esta ação não pôde ser concluída. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	86004: 'Obter ITRisk por id de chamada falhou.',
	86005: 'Esta ação não pôde ser concluída. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	86006: 'Não é possível concluir a associação. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	86007: 'Esta ação não pôde ser concluída. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	86008: 'Não é possível concluir a associação. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	86009: 'Esta ação não pôde ser concluída. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	86010: 'Falha ao excluir o risco de tecnologia.',

	/*RiskFactorFormHeaders*/
	89001: 'Relação fator de risco não encontrado.',
	89002: 'Documento não encontrado.',
	89003: 'Identificação de documento faltando.',
	89004: 'O tamanho da justificativa excede mais de 4000 caracteres.',
	89005: 'O risco não pode estar relacionado ao fator de risco selecionado. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	89014: 'A identificação do risco não é válida',
	89020: 'O fator de risco não pode ser salvo.  Atualize a página e tente novamente.  Se o problema persistir, entre em contato com o Help Desk',

	/*Materiality*/
	91001: 'Materialidade não encontrada.',
	91002: 'Os dados não puderam ser salvos. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	91003: 'Solicitação de atualização de materialidade inválida.',
	91004: 'Missing IsAnnualizedAmountPectedToBeReported.',
	91005: 'Ao atualizar IsAnnualizedAmountExpectedToBeReported para true, é obrigatório especificar um BasisForecastAmount.',
	91006: 'O valor da previsão de base não deve ter mais de 15 dígitos ou ter decimais.',
	91007: 'Ao atualizar o IsAnnualizedAmountPectedToBeReported para verdade, o BasisForecastAmountRationale deve ser nulo.',
	91008: 'A base lógica do valor da previsão é muito curta ou muito longa.',
	91009: 'ID de materialidade inválido',
	91010: 'ID de tipo de corpo de materialidade inválido',
	91011: 'O valor nominal não pode exceder o limite superior do intervalo',
	91012: 'O valor da materialidade não deve ter mais de 15 dígitos e 4 casas decimais',

	/*Group Structure - Sub Scopes */
	92013: 'O sub-escopo não pode ser excluído porque há pelo menos uma equipe de região/componente relacionada.',
	92016: 'O nome do sub-escopo que já existe',

	/*Helix Account Mappings */
	94001: 'O mapeamento da conta não pôde ser salvo. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	94004: 'Uma ou mais contas do EY Canvas foram excluídas e não puderam ser mapeadas. Atualize a página e tente novamente.',
	94005: 'Não foi possível retomar o projeto. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',

	/*Group Instructions */
	98001: 'Ocorreu um erro ao recuperar instruções do grupo. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	98002: 'Um ou mais IDs de seção de instruções do Conhecimento não são válidos.',
	98003: 'Ocorreu um erro ao criar instruções de grupo. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	98004: 'O nome da instrução não pode ficar vazio.',
	98005: 'O nome da instrução não pode ter mais de 500 caracteres.',
	98006: 'A descrição da instrução não pode ficar vazia.',
	98007: 'A descrição da instrução não pode ter mais de 30.000 caracteres.',
	98009: 'O nome da instrução não pode ser duplicado.',
	98010: 'A data de vencimento não pode ficar vazia.',
	98011: 'Instrução já excluída.',
	98012: 'Instrução não encontrada.',
	98013: 'O ID da instrução deve ser maior que zero.',
	98014: 'Ocorreu um erro ao salvar as instruções do grupo. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	98015: 'Os escopos obrigatórios da instrução não podem ser excluídos.',
	98016: 'A instrução foi excluída',
	98017: 'Tarefa de grupo já excluída.',
	98018: 'Edite a tarefa do grupo após a exclusão.',
	98019: 'Entidade não encontrada.',

	98020: 'Não foi possível gerar o pacote de avaliação de risco. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',

	98021: 'O nome do documento não pode ficar vazio.',
	98022: 'O nome do documento não pode ter mais de 115 caracteres.',
	98023: 'O nome do documento não pode conter caracteres XML inválidos.',
	98024: 'O processo de criação do pacote está em andamento, pode levar até dez minutos para ser concluído',
	98025: 'Alguma(s) instruçõe(s) não possui(em) componente(s) relacionado(s). Aloque componente(s) às instruções e então gere novamente as comunicações de avaliação de risco do Grupo.',
	98026: 'Algum(ns) componente(s) não possui(em) contas(s) relacionada(s). Aloque conta(s) para o(s) componente(s) e então gere novamente as comunicações de avaliação de risco do Grupo.',
	98027: 'Algum(as) conta(s) não possuem documentos da(s) conta(s). Crie o documento da conta e então gere novamente as comunicações de avaliação de risco do grupo.',
	98028: 'Este Formulário do Canvas não está associado a uma tarefa válida.',
	98029: 'Um ou mais componentes são apenas para referência.',
	98030: 'Um ou mais componentes não são deste engagement primário da equipe.',
	98031: 'Um ou mais escopos não são deste engagement primário da equipe.',
	98032: 'A contagem de documentos excedeu o limite.',
	98033: 'Os escopos necessários não podem ser excluídos da instrução.',

	/*Estimate */
	115017: 'Estimativa não encontrada',

	/* Group Audit */
	106003: 'Erro. Atualize e tente novamente.',

	/* TasksOverview */
	117001: 'Falha na chamada de visão geral de todas as tarefas.',
	117002: 'A solicitação para Obter toda a visão geral da tarefa não pode estar vazia.',
	117003: 'ID do engagement inválido.',
	117004: 'Valor de categoria de tarefa inválido.',
	117005: 'Valor de visualização inválido.',
	117006: 'Valor de pesquisa de categoria de documento inválido.',
	117007: 'IDs de Categoria da Tarefa duplicados.',
	117008: 'IDs Fases de Tempo duplicados.',
	117009: 'IDs de tarefas duplicados.',
	117010: 'IDs de documentos duplicados.',
	117011: 'IDs Atribuído ao Usuário duplicados.',

	/* Multientity */
	114001: 'Falha ao obter todos as Multi-Entidades.',
	114002: 'ST Nome da Entidade não pode ficar vazio.',
	114003: 'ST Nome da Entidade não deve ter mais de 500 caracteres.',
	114004: 'ST Nome Legal não pode ficar vazio.',
	114005: 'ST Nome Legal não deve ter mais de 500 caracteres.',
	114006: 'Multi-Entidade só pode ser criada com engagements MEST.',
	114007: 'Falha na chamada de criação de conta Multi-Entidade.',
	114008: 'O STEntity selecionado foi excluído. Feche este modal para ver a lista atualizada.',
	114009: 'ID de conta inválido.',
	114010: 'ST Nome Curto da Entidade não deve ter mais de 100 caracteres.',
	114011: 'A solicitação de perfil de envio do ST Entidade é inválida.',
	114012: 'O corpo da solicitação de perfil de envio do ST Entidade é inválido.',
	114013: 'O corpo da solicitação de perfil de envio do ST Entidade deve ter ID de ST Entidade distintos.',
	114014: 'IDs da Entidade ST para enviar solicitação de perfil são inválidos.',
	114015: 'O formulário tem respostas incompletas. Atualize a página e tente novamente.',
	114016: 'A atualização de conteúdo está desabilitada para isso.',
	114017: 'O Engagement não possui documento de Perfil Individual Multi-Entidade.',
	114018: 'IDs da Entidade ST ausentes no perfil individual da Multi-Entidade.',
	114019: 'Uma ou mais entidades que você está mapeando não existem mais no engagement. Atualize a página e tente novamente.',
	114020: 'ST Nome Curto da Entidade não pode ficar vazio',
	114021: 'ID do Documeno inválido.',
	114022: 'ID de Engagement inválido.',
	114023: 'O registro STEntityDocument já existe.',
	114024: 'O registro STEntityDocument não existe.',
	114025: 'Registro STEntityDocument IsSystemAssociated.',
	114026: 'Corpo de solicitação inválido.',
	114028: 'Cada entidade não possui um documento de perfil.',
	114035: 'A relação STEntidade já existe.',
	114036: 'Pelo menos um documento de perfil deve ser válido ao solicitar a atualização de todas as entidades.',
	114037: 'A relação entre STEntidade e Conta já foi removida.',
	114038: 'Get all MultiEntity layers failed.',
	114039: 'Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',
	114040: 'Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',

	/* Sample List */
	121101: 'ID de lista de amostras inválido.',
	121008: 'ID de lista de amostras inválido.',
	121011: 'Esta amostra não está mais disponível. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	121013: 'Esta amostra não está mais disponível neste engagement. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	121016: 'Esta amostra não está mais disponível. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	121037: 'Ocorreu um erro ao realizar esta ação. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	121012: 'O status do atributo não pode ser atualizado. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	121025: 'Este documento não está mais disponível. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	121027: 'A relação do documento não pode ser editada. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	121028: 'A relação do documento não pode ser editada. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	121014: 'O status do atributo não pode ser atualizado. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	121029: 'Este atributo não está mais disponível. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
	121021: 'O status do atributo não pode ser atualizado. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',

	/*Control Attributes */
	122018: 'Não foi possível concluir a ação. Atualize a página e tente novamente. Se o problema persistir entre em contato com o Help Desk.',
	122021: 'Ocorreu um erro ao executar esta ação. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',

	/*Flow chart*/
	123031: 'O ID do corpo do formulário não pode ser vinculado a mais de um fluxograma.',

	1034: 'This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	1035: 'This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	/*Group Instructions */
	196033: 'As instruções não podem ter 0 itens de alocação, devem ter pelo menos 1.',

	/*Information Security */
	200001: 'A ação tomada foi proibida pela Segurança da Informação da EY. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk. ',

	/*Tags */
	40007: 'Esta tag não está mais disponível. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	40029: 'A relação de tags não pode ser editada. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.'
};

export const roleForMember = [{
	id: 1,
	role: 'Sócio Responsável',
	roleAbbreviation: 'PIC'
},
{
	id: 2,
	role: 'Sócio do engagement',
	roleAbbreviation: 'EP'
},
{
	id: 3,
	role: 'Diretor Executivo',
	roleAbbreviation: 'ED'
},
{
	id: 4,
	role: 'Princípio',
	roleAbbreviation: 'Princípio'
},
{
	id: 5,
	role: 'Gerente Sênior',
	roleAbbreviation: 'Gerente Senior'
},
{
	id: 6,
	role: 'Gerente',
	roleAbbreviation: 'Gerente'
},
{
	id: 7,
	role: 'Sênior',
	roleAbbreviation: 'Sênior'
},
{
	id: 8,
	role: 'Pessoal',
	roleAbbreviation: 'Staff'
},
{
	id: 9,
	role: 'Internamente',
	roleAbbreviation: 'Intrn'
},
{
	id: 10,
	role: 'Revisor de qualidade do engagement',
	roleAbbreviation: 'EQR'
},
{
	id: 11,
	role: 'Outro Sócio',
	roleAbbreviation: 'Outro Sócio'
},
{
	id: 12,
	role: 'GDS - Pessoal',
	roleAbbreviation: 'GDS Staff'
},
{
	id: 13,
	role: 'Assessoria (ITRA, TAS, Capital Humano ou Outros)',
	roleAbbreviation: 'Adv'
},
{
	id: 14,
	role: 'Imposto',
	roleAbbreviation: 'Imposto'
},
{
	id: 15,
	role: 'Serviços de Suporte Executivo',
	roleAbbreviation: 'Ess'
},
{
	id: 16,
	role: 'Conselheiro Geral',
	roleAbbreviation: 'GCO'
},
{
	id: 17,
	role: 'Revisor de qualidade de auditoria',
	roleAbbreviation: 'Aqr'
},
{
	id: 18,
	role: 'Equipe de componentes ML',
	roleAbbreviation: 'Mct'
},
{
	id: 19,
	role: 'Supervisor de Clientes',
	roleAbbreviation: 'C. Supervisor'
},
{
	id: 20,
	role: 'Funcionários do Cliente',
	roleAbbreviation: 'C. Staff'
},
{
	id: 21,
	role: 'Supervisor de Auditoria Interna',
	roleAbbreviation: 'IA Supervisor'
},
{
	id: 22,
	role: 'Equipe de Auditoria Interna',
	roleAbbreviation: 'IA Staff'
},
{
	id: 23,
	role: 'Regulador',
	roleAbbreviation: 'Regulador'
},
{
	id: 24,
	role: 'Outros (por exemplo, revisão de due diligence)',
	roleAbbreviation: 'Outro'
},
{
	id: 25,
	role: 'Escritório',
	roleAbbreviation: 'Escritório'
},
{
	id: 26,
	role: 'Área',
	roleAbbreviation: 'Área'
},
{
	id: 27,
	role: 'Indústria',
	roleAbbreviation: 'Ind'
},
{
	id: 28,
	role: 'Nacional',
	roleAbbreviation: 'Nat'
},
{
	id: 29,
	role: 'Global',
	roleAbbreviation: 'Gbl'
},
{
	id: 30,
	role: 'GDS - Sênior',
	roleAbbreviation: 'GDS Senior'
},
{
	id: 31,
	role: 'GDS - Gerente',
	roleAbbreviation: 'Gerente de GDS'
}
];

export const accountConclusionTabs = {
	conclusions: 'Conclusões'
};

export const assertions = [{
	id: 1,
	assertionname: 'Completude',
	assertionabbreviation: 'C',
	statementtypeid: 2,
	displayorder: 1
},
{
	id: 2,
	assertionname: 'Existência',
	assertionabbreviation: 'E',
	statementtypeid: 2,
	displayorder: 2
},
{
	id: 3,
	assertionname: 'Valorização',
	assertionabbreviation: 'V',
	statementtypeid: 2,
	displayorder: 3
},
{
	id: 4,
	assertionname: 'Direitos e obrigações',
	assertionabbreviation: 'R&O',
	statementtypeid: 2,
	displayorder: 4
},
{
	id: 5,
	assertionname: 'Apresentação e Divulgação',
	assertionabbreviation: 'P&D',
	statementtypeid: 2,
	displayorder: 5
},
{
	id: 6,
	assertionname: 'Completude',
	assertionabbreviation: 'C',
	statementtypeid: 1,
	displayorder: 6
},
{
	id: 7,
	assertionname: 'Ocorrência',
	assertionabbreviation: 'O',
	statementtypeid: 1,
	displayorder: 7
},
{
	id: 8,
	assertionname: 'Mensuração',
	assertionabbreviation: 'M',
	statementtypeid: 1,
	displayorder: 8
},
{
	id: 9,
	assertionname: 'Apresentação e Divulgação',
	assertionabbreviation: 'P&D',
	statementtypeid: 1,
	displayorder: 9
},
{
	id: 10,
	assertionname: 'Completude',
	assertionabbreviation: 'C',
	statementtypeid: 3,
	displayorder: 10
},
{
	id: 11,
	assertionname: 'Existência/Ocorrência',
	assertionabbreviation: 'E/O',
	statementtypeid: 3,
	displayorder: 11
},
{
	id: 12,
	assertionname: 'Mensuração/Valorização',
	assertionabbreviation: 'M/V',
	statementtypeid: 3,
	displayorder: 12
},
{
	id: 13,
	assertionname: 'Direitos e obrigações',
	assertionabbreviation: 'R&O',
	statementtypeid: 3,
	displayorder: 13
},
{
	id: 14,
	assertionname: 'Apresentação e Divulgação',
	assertionabbreviation: 'P&D',
	statementtypeid: 3,
	displayorder: 14
}
];

export const documentChangeTypesOptions = [{
	value: 1,
	label: 'Mudança não administrativa'
},
{
	value: 2,
	label: 'Aceitar revisões quando a funcionalidade de rastreamento de mudança foi usada'
},
{
	value: 3,
	label: 'Adicionando referências cruzadas adicionais a evidências que já existem'
},
{
	value: 4,
	label: 'Adicionando uma resposta de confirmação original recebida anteriormente via fax ou e-mail'
},
{
	value: 5,
	label: 'Mudança cosmética'
},
{
	value: 6,
	label: 'Preenchimento do Formulário AP e avaliação substancial da função'
},
{
	value: 7,
	label: 'Deletar ou descartar documentação substituída'
},
{
	value: 8,
	label: 'Preparando a carta da administração'
},
{
	value: 9,
	label: 'Assinar checklists de conclusão relacionadas ao processo de arquivamento'
},
{
	value: 10,
	label: 'Classificação, conferência e cruzamento de documentos finais',
},
{
	value: 12,
	label: 'Somente MEST: Modificação de evidências relacionadas a entidades com data de relatório posterior à data do relatório no EY Canvas'
},
{
	value: 11,
	label: 'Quando ajustado ao fuso horário local, a modificação está em ou antes da data do relatório (somente para as Américas)',
}
];

export const KnowledgeFormProfileAnswer = [{
	value: 1,
	label: 'Complexo',
	display: true
},
{
	value: 2,
	label: 'Não complexo',
	display: true
},
{
	value: 3,
	label: 'Listado',
	display: true
},
{
	value: 4,
	label: 'Não listado',
	display: false
},
{
	value: 5,
	label: 'PCAOB - IA',
	display: true
},
{
	value: 6,
	label: 'Non-PCAOB-IA',
	display: false
},
{
	value: 7,
	label: 'PCAOB - FS',
	display: true
},
{
	value: 8,
	label: 'Non-PCAOB-FS',
	display: false
},
{
	value: 9,
	label: 'Auditoria de Grupo',
	display: true
},
{
	value: 10,
	label: 'Não é auditoria de grupo',
	display: false
},
{
	value: 11,
	label: 'Digital',
	display: true
},
{
	value: 12,
	label: 'Core',
	display: true
}
];

export const strategyType = [{
	StrategyTypeId: 1,
	StrategyTypeName: 'Controles'
},
{
	StrategyTypeId: 2,
	StrategyTypeName: 'Substantivo'
}
];

export const controlTypeName = {
	1: 'Aplicativo',
	2: 'ITDM',
	3: 'Prevenção manual',
	4: 'Detecção Manual'
};

export const inCompleteList = [{
	value: 1,
	label: 'Incompleto',
	title: 'Incompleto'
}];

export const scotTypes = [{
	value: 1,
	label: 'Rotineiro',
	title: 'Rotineiro',
	isDisabled: false
},
{
	value: 2,
	label: 'Não rotineiro',
	title: 'Não rotineiro',
	isDisabled: false
},
{
	value: 3,
	label: 'Estimativa',
	title: 'Estimativa',
	isDisabled: false
}
];

export const scotTypesNew = [{
	value: 1,
	label: 'Routineira',
	title: 'Routineira',
	isDisabled: false
},
{
	value: 2,
	label: 'Não rotineira',
	title: 'Não rotineira',
	isDisabled: false
},
{
	value: 3,
	label: 'Estimativa',
	title: 'Estimativa',
	isDisabled: false
},
{
	value: 4,
	label: 'FSCP',
	title: 'FSCP',
	isDisabled: false
}
];

export const estimationTypes = [{
	value: 1,
	label: 'Não',
	title: 'Não',
	isDisabled: false
},
{
	value: 2,
	label: 'Sim',
	title: 'Sim',
	isDisabled: false
}
];

/* IT Control */
export const itApproachType = [{
	ITApproachTypeId: 1,
	ITApproachTypeName: 'Controles'
},
{
	ITApproachTypeId: 2,
	ITApproachTypeName: 'Substantivo'
}
];

/*Controls */
export const controlFrequencyType = [{
	value: 1,
	label: 'Muitas vezes por dia',
	title: 'Muitas vezes por dia'
},
{
	value: 2,
	label: 'Diária',
	title: 'Diária'
},
{
	value: 3,
	label: 'Semanal',
	title: 'Semanal'
},
{
	value: 4,
	label: 'Mensal',
	title: 'Mensal'
},
{
	value: 5,
	label: 'Trimestral',
	title: 'Trimestral'
},
{
	value: 6,
	label: 'Anualmente',
	title: 'Anualmente'
},
{
	value: 8,
	label: 'Outro',
	title: 'Outro'
}
];

export const controlTypes = [{
	value: 1,
	label: 'Controle de aplicativo de TI'
},
{
	value: 2,
	label: 'Controle manual dependente de TI'
},
{
	value: 3,
	label: 'Prevenção manual'
},
{
	value: 4,
	label: 'Detecção manual'
}
];

export const strategyTypeCheck = {
	1: 'Controles',
	2: 'Substantivo'
};

export const designEffectivenessType = [{
	DesignEffectivenessTypeId: 1,
	DesignEffectivenessTypeName: 'Eficaz'
},
{
	DesignEffectivenessTypeId: 2,
	DesignEffectivenessTypeName: 'Ineficaz'
}
];

export const controlEffectivenessType = [{
	ControlEffectivenessTypeId: 1,
	ControlEffectivenessTypeName: 'Eficaz'
},
{
	ControlEffectivenessTypeId: 2,
	ControlEffectivenessTypeName: 'Ineficaz'
}
];

export const testing = [{
	testingId: 1,
	testingDescription: 'Sim'
},
{
	testingId: 2,
	testingDescription: 'Não'
}
];

export const controlType = [{
	value: 1,
	label: 'Controle de aplicativos de TI',
	title: 'Controle de aplicativos de TI'
},
{
	value: 2,
	label: 'Controle manual dependente de TI',
	title: 'Controle manual dependente de TI'
},
{
	value: 3,
	label: 'Prevenção manual',
	title: 'Prevenção manual'
},
{
	value: 4,
	label: 'Detecção Manual',
	title: 'Detecção Manual'
}
];

export const aggregateITEvaluationType = [{
	value: 1,
	label: 'Suporta',
	title: 'Suporta'
},
{
	value: 2,
	label: 'Não suporta',
	title: 'Não suporta'
},
{
	value: 3,
	label: 'Suporte FS e ICFR',
	title: 'Suporte FS e ICFR'
},
{
	value: 4,
	label: 'Suporte somente FS',
	title: 'Suporte somente FS'
}
];

export const KnowledgeFormProfileQuestion = [{
	value: 1,
	label: 'Complexo'
},
{
	value: 2,
	label: 'Listado'
},
{
	value: 3,
	label: 'PCAOB - IA'
},
{
	value: 4,
	label: 'PCAOB - FS'
},
{
	value: 5,
	label: 'Localização'
},
{
	value: 6,
	label: 'Idioma'
},
{
	value: 7,
	label: 'Auditoria de Grupo'
},
{
	value: 8,
	label: 'Digital'
}
];

export const KnowledgeLanguage = [{
	value: 1,
	label: 'English'
},
{
	value: 2,
	label: 'Spanish (Latin America)'
},
{
	value: 3,
	label: 'French (Canada)'
},
{
	value: 4,
	label: 'Dutch'
},
{
	value: 5,
	label: 'Croatian'
},
{
	value: 6,
	label: 'Czech'
},
{
	value: 7,
	label: 'Danish'
},
{
	value: 8,
	label: 'Finnish'
},
{
	value: 9,
	label: 'German (Germany, Austria)',
},
{
	value: 10,
	label: 'Hungarian'
},
{
	value: 11,
	label: 'Italian'
},
{
	value: 12,
	label: 'Japanese (Japan)'
},
{
	value: 13,
	label: 'Norwegian (Norway)'
},
{
	value: 14,
	label: 'Polish'
},
{
	value: 15,
	label: 'Slovak'
},
{
	value: 16,
	label: 'Slovenian'
},
{
	value: 17,
	label: 'Swedish'
},
{
	value: 18,
	label: 'Arabic'
},
{
	value: 19,
	label: 'Simplified Chinese (China)'
},
{
	value: 20,
	label: 'Traditional Chinese (Taiwan)'
},
{
	value: 21,
	label: 'Greek'
},
{
	value: 22,
	label: 'Hebrew (Israel)'
},
{
	value: 23,
	label: 'Indonesian'
},
{
	value: 24,
	label: 'Korean (Republic of Korea)'
},
{
	value: 25,
	label: 'Portuguese (Brazil)'
},
{
	value: 26,
	label: 'Romanian'
},
{
	value: 27,
	label: 'Russian (Russia)'
},
{
	value: 28,
	label: 'Thai'
},
{
	value: 29,
	label: 'Turkish'
},
{
	value: 30,
	label: 'Vietnamese'
},
{
	value: 31,
	label: 'PCAOB - Inglês'
},
{
	value: 32,
	label: 'PCAOB - Espanhol (América Latina)'
},
{
	value: 33,
	label: 'PCAOB - Francês (Canadá)'
},
{
	value: 34,
	label: 'PCAOB - Holandês'
},
{
	value: 35,
	label: 'PCAOB - Croata'
},
{
	value: 36,
	label: 'PCAOB - Tcheco'
},
{
	value: 37,
	label: 'PCAOB - Dinamarquês'
},
{
	value: 38,
	label: 'PCAOB - Finlandês'
},
{
	value: 39,
	label: 'PCAOB - Alemão (Alemanha, Áustria)',
},
{
	value: 40,
	label: 'PCAOB - Húngaro'
},
{
	value: 41,
	label: 'PCAOB - Italiano'
},
{
	value: 42,
	label: 'PCAOB - Japonês (Japão)'
},
{
	value: 43,
	label: 'PCAOB - Norueguês (Noruega)'
},
{
	value: 44,
	label: 'PCAOB - Polonês'
},
{
	value: 45,
	label: 'PCAOB - Eslovaco'
},
{
	value: 46,
	label: 'PCAOB - Esloveno'
},
{
	value: 47,
	label: 'PCAOB - Sueco'
},
{
	value: 48,
	label: 'PCAOB - Árabe'
},
{
	value: 49,
	label: 'PCAOB - Chinês Simplificado (China)'
},
{
	value: 50,
	label: 'PCAOB - Chinês Tradicional (Taiwan)'
},
{
	value: 51,
	label: 'PCAOB - Grego'
},
{
	value: 52,
	label: 'PCAOB - Hebraico (Israel)'
},
{
	value: 53,
	label: 'PCAOB - Indonésio'
},
{
	value: 54,
	label: 'PCAOB - Coreano (República da Coreia)'
},
{
	value: 55,
	label: 'PCAOB - Português (Brasil)'
},
{
	value: 56,
	label: 'PCAOB - Romeno'
},
{
	value: 57,
	label: 'PCAOB - Russo (Rússia)'
},
{
	value: 58,
	label: 'PCAOB - Tailandês'
},
{
	value: 59,
	label: 'PCAOB - Turco'
},
{
	value: 60,
	label: 'PCAOB - Vietnamita'
}
];

export const KnowledgeCountry = [{
	value: 1,
	label: 'Mayotte'
},
{
	value: 2,
	label: 'British Virgin Islands'
},
{
	value: 3,
	label: 'Spain'
},
{
	value: 4,
	label: 'Belize'
},
{
	value: 5,
	label: 'Peru'
},

{
	value: 6,
	label: 'Slovakia'
},
{
	value: 7,
	label: 'Venezuela'
},
{
	value: 8,
	label: 'Norway'
},
{
	value: 9,
	label: 'Falkland Islands (Malvinas)'
},
{
	value: 10,
	label: 'Mozambique'
},

{
	value: 11,
	label: 'China'
},
{
	value: 12,
	label: 'Sudan'
},
{
	value: 13,
	label: 'Israel'
},
{
	value: 14,
	label: 'Belgium'
},
{
	value: 15,
	label: 'Saudi Arabia'
},

{
	value: 16,
	label: 'Gibraltar'
},
{
	value: 17,
	label: 'Guam'
},
{
	value: 18,
	label: 'Norfolk Islands'
},
{
	value: 19,
	label: 'Zambia'
},
{
	value: 20,
	label: 'Reunion'
},

{
	value: 21,
	label: 'Azerbaijan'
},
{
	value: 22,
	label: 'Saint Helena'
},
{
	value: 23,
	label: 'Iran'
},
{
	value: 24,
	label: 'Monaco'
},
{
	value: 25,
	label: 'Saint Pierre and Miquelon'
},

{
	value: 26,
	label: 'New Zealand'
},
{
	value: 27,
	label: 'Cook Islands'
},
{
	value: 28,
	label: 'Saint Lucia'
},
{
	value: 29,
	label: 'Zimbabwe'
},
{
	value: 30,
	label: 'Iraq'
},

{
	value: 31,
	label: 'Tonga'
},
{
	value: 32,
	label: 'American Samoa'
},
{
	value: 33,
	label: 'Maldives'
},
{
	value: 34,
	label: 'Morocco'
},
{
	value: 35,
	label: 'International Standards on Auditing (ISA)'
},

{
	value: 36,
	label: 'Albania'
},
{
	value: 37,
	label: 'Afghanistan'
},
{
	value: 38,
	label: 'Gambia'
},
{
	value: 39,
	label: 'Burkina Faso'
},
{
	value: 40,
	label: 'Tokelau'
},

{
	value: 41,
	label: 'Libya'
},
{
	value: 42,
	label: 'Canada'
},
{
	value: 43,
	label: 'United Arab Emirates'
},
{
	value: 44,
	label: "Korea,Democratic People's Republic of"
},
{
	value: 45,
	label: 'Montserrat'
},

{
	value: 46,
	label: 'Greenland'
},
{
	value: 47,
	label: 'Rwanda'
},
{
	value: 48,
	label: 'Fiji'
},
{
	value: 49,
	label: 'Djibouti'
},
{
	value: 50,
	label: 'Botswana'
},

{
	value: 51,
	label: 'Kuwait'
},
{
	value: 52,
	label: 'Madagascar'
},
{
	value: 53,
	label: 'Isle of Man'
},
{
	value: 54,
	label: 'Hungary'
},
{
	value: 55,
	label: 'Namibia'
},

{
	value: 56,
	label: 'Malta'
},
{
	value: 57,
	label: 'Jersey'
},
{
	value: 58,
	label: 'Thailand'
},
{
	value: 59,
	label: 'Saint Kitts and Nevis'
},
{
	value: 60,
	label: 'Bhutan'
},

{
	value: 61,
	label: 'Panama'
},
{
	value: 62,
	label: 'Somalia'
},
{
	value: 63,
	label: 'Bahrain'
},
{
	value: 64,
	label: 'Bosnia and Herzegovina'
},
{
	value: 65,
	label: 'France'
},

{
	value: 66,
	label: 'Korea,Republic of',
},
{
	value: 67,
	label: 'Iceland'
},
{
	value: 68,
	label: 'Portugal'
},
{
	value: 69,
	label: 'Tunisia'
},
{
	value: 70,
	label: 'Ghana'
},

{
	value: 71,
	label: 'Cameroon'
},
{
	value: 72,
	label: 'Greece'
},
{
	value: 73,
	label: 'French Southern Territories'
},
{
	value: 74,
	label: 'Heard and McDonald Islands'
},
{
	value: 75,
	label: 'Andorra'
},

{
	value: 76,
	label: 'Luxembourg'
},
{
	value: 77,
	label: 'Samoa'
},
{
	value: 78,
	label: 'Anguilla'
},
{
	value: 79,
	label: 'Netherlands'
},
{
	value: 80,
	label: 'Guinea-Bissau'
},

{
	value: 81,
	label: 'Nicaragua'
},
{
	value: 82,
	label: 'Paraguay'
},
{
	value: 83,
	label: 'Antigua and Barbuda'
},
{
	value: 84,
	label: 'International Financial Reporting Standard (IFRS)'
},
{
	value: 85,
	label: 'Niger'
},

{
	value: 86,
	label: 'Egypt'
},
{
	value: 87,
	label: 'Vatican City State'
},
{
	value: 88,
	label: 'Latvia'
},
{
	value: 89,
	label: 'Cyprus'
},
{
	value: 90,
	label: 'US Minor Outlying Islands'
},

{
	value: 91,
	label: 'Russia'
},
{
	value: 92,
	label: 'Saint Vincent and the Grenadines'
},
{
	value: 93,
	label: 'Guernsey'
},
{
	value: 94,
	label: 'Burundi'
},
{
	value: 95,
	label: 'Cuba'
},

{
	value: 96,
	label: 'Equatorial Guinea'
},
{
	value: 97,
	label: 'British Indian Ocean Territory'
},
{
	value: 98,
	label: 'Sweden'
},
{
	value: 99,
	label: 'Uganda'
},
{
	value: 100,
	label: 'Macedonia,the Former Yugoslav Republic of'
},

{
	value: 101,
	label: 'Swaziland'
},
{
	value: 102,
	label: 'El Salvador'
},
{
	value: 103,
	label: 'Kyrgyzstan'
},
{
	value: 104,
	label: 'Ireland'
},
{
	value: 105,
	label: 'Kazakhstan'
},

{
	value: 106,
	label: 'Honduras'
},
{
	value: 107,
	label: 'Uruguay'
},
{
	value: 108,
	label: 'Georgia'
},
{
	value: 109,
	label: 'Trinidad and Tobago'
},
{
	value: 110,
	label: 'Palestinian Authority'
},

{
	value: 111,
	label: 'Martinique'
},
{
	value: 112,
	label: 'Aland Islands'
},
{
	value: 113,
	label: 'French Polynesia'
},
{
	value: 114,
	label: 'Ivory Coast'
},
{
	value: 115,
	label: 'Montenegro'
},

{
	value: 116,
	label: 'South Africa'
},
{
	value: 117,
	label: 'South Georgia and the South Sandwich Islands'
},
{
	value: 118,
	label: 'Yemen'
},
{
	value: 119,
	label: 'Hong Kong China'
},
{
	value: 120,
	label: 'Kenya'
},

{
	value: 121,
	label: 'Chad'
},
{
	value: 122,
	label: 'Colombia'
},
{
	value: 123,
	label: 'Costa Rica'
},
{
	value: 124,
	label: 'Angola'
},
{
	value: 125,
	label: 'Lithuania'
},

{
	value: 126,
	label: 'Syria'
},
{
	value: 127,
	label: 'Malaysia'
},
{
	value: 128,
	label: 'Sierra Leone'
},
{
	value: 129,
	label: 'Serbia'
},
{
	value: 130,
	label: 'Poland'
},

{
	value: 131,
	label: 'Suriname'
},
{
	value: 132,
	label: 'Haiti'
},
{
	value: 133,
	label: 'Nauru'
},
{
	value: 134,
	label: 'Sao Tome and Principe'
},
{
	value: 135,
	label: 'Svalbard and Jan Mayen'
},

{
	value: 136,
	label: 'Singapore'
},
{
	value: 137,
	label: 'Moldova'
},
{
	value: 138,
	label: 'Taiwan'
},
{
	value: 139,
	label: 'Senegal'
},
{
	value: 140,
	label: 'Gabon'
},

{
	value: 141,
	label: 'Mexico'
},
{
	value: 142,
	label: 'Seychelles'
},
{
	value: 143,
	label: 'Micronesia,Federated States of'
},
{
	value: 144,
	label: 'Algeria'
},
{
	value: 145,
	label: 'Italy'
},

{
	value: 146,
	label: 'San Marino'
},
{
	value: 147,
	label: 'Liberia'
},
{
	value: 148,
	label: 'Brazil'
},
{
	value: 149,
	label: 'Croatia'
},
{
	value: 150,
	label: 'Faroe Islands'
},

{
	value: 151,
	label: 'Palau'
},
{
	value: 152,
	label: 'Finland'
},
{
	value: 153,
	label: 'Philippines'
},
{
	value: 154,
	label: 'Jamaica'
},
{
	value: 155,
	label: 'French Guiana'
},

{
	value: 156,
	label: 'Cape Verde'
},
{
	value: 157,
	label: 'Myanmar'
},
{
	value: 158,
	label: 'Lesotho'
},
{
	value: 159,
	label: 'US Virgin Islands'
},
{
	value: 160,
	label: 'Cayman Islands'
},

{
	value: 161,
	label: 'Niue'
},
{
	value: 162,
	label: 'Togo'
},
{
	value: 163,
	label: 'Belarus'
},
{
	value: 164,
	label: 'Dominica'
},
{
	value: 165,
	label: 'Indonesia'
},

{
	value: 166,
	label: 'Uzbekistan'
},
{
	value: 167,
	label: 'Nigeria'
},
{
	value: 168,
	label: 'Wallis and Futuna'
},
{
	value: 169,
	label: 'Barbados'
},
{
	value: 170,
	label: 'Sri Lanka'
},

{
	value: 171,
	label: 'United Kingdom'
},
{
	value: 172,
	label: 'Ecuador'
},
{
	value: 173,
	label: 'Guadeloupe'
},
{
	value: 174,
	label: 'Laos'
},
{
	value: 175,
	label: 'Jordan'
},

{
	value: 176,
	label: 'Solomon Islands'
},
{
	value: 177,
	label: 'East Timor'
},
{
	value: 178,
	label: 'Lebanon'
},
{
	value: 179,
	label: 'Central African Republic'
},
{
	value: 180,
	label: 'India'
},

{
	value: 181,
	label: 'Christmas Island'
},
{
	value: 182,
	label: 'Vanuatu'
},
{
	value: 183,
	label: 'Brunei'
},
{
	value: 184,
	label: 'Bangladesh'
},
{
	value: 185,
	label: 'Antarctica'
},

{
	value: 186,
	label: 'Bolivia'
},
{
	value: 187,
	label: 'Turkey'
},
{
	value: 188,
	label: 'Bahamas'
},
{
	value: 189,
	label: 'Comoros'
},
{
	value: 190,
	label: 'Western Sahara'
},

{
	value: 191,
	label: 'Czech Republic'
},
{
	value: 192,
	label: 'Ukraine'
},
{
	value: 193,
	label: 'Estonia'
},
{
	value: 194,
	label: 'Bulgaria'
},
{
	value: 195,
	label: 'Mauritania'
},

{
	value: 196,
	label: 'Congo,The Democratic Republic of the'
},
{
	value: 197,
	label: 'Liechtenstein'
},
{
	value: 198,
	label: 'Pitcairn'
},
{
	value: 199,
	label: 'Denmark'
},
{
	value: 200,
	label: 'Marshall Islands'
},

{
	value: 201,
	label: 'Japan'
},
{
	value: 202,
	label: 'Austria'
},
{
	value: 203,
	label: 'Oman'
},
{
	value: 204,
	label: 'Mongolia'
},
{
	value: 205,
	label: 'Tajikistan'
},

{
	value: 206,
	label: 'Switzerland'
},
{
	value: 207,
	label: 'Guatemala'
},
{
	value: 208,
	label: 'Eritrea'
},
{
	value: 209,
	label: 'Nepal'
},
{
	value: 210,
	label: 'Mali'
},

{
	value: 211,
	label: 'Slovenia'
},
{
	value: 212,
	label: 'Northern Mariana Islands'
},
{
	value: 213,
	label: '(Not Applicable)'
},
{
	value: 214,
	label: 'Aruba'
},
{
	value: 215,
	label: 'Congo'
},

{
	value: 216,
	label: 'Qatar'
},
{
	value: 217,
	label: 'Guinea'
},
{
	value: 218,
	label: 'United States'
},
{
	value: 219,
	label: 'Ethiopia'
},
{
	value: 220,
	label: 'Other'
},

{
	value: 221,
	label: 'Guyana'
},
{
	value: 222,
	label: 'Germany'
},
{
	value: 223,
	label: 'Bermuda'
},
{
	value: 224,
	label: 'Turks and Caicos Islands'
},
{
	value: 225,
	label: 'Australia'
},

{
	value: 226,
	label: 'Kiribati'
},
{
	value: 227,
	label: 'Puerto Rico'
},
{
	value: 228,
	label: 'Pakistan'
},
{
	value: 229,
	label: 'Mauritius'
},
{
	value: 230,
	label: 'Malawi'
},

{
	value: 231,
	label: 'Turkmenistan'
},
{
	value: 232,
	label: 'Cambodia'
},
{
	value: 233,
	label: 'Chile'
},
{
	value: 234,
	label: 'New Caledonia'
},
{
	value: 235,
	label: 'Papua New Guinea'
},

{
	value: 236,
	label: 'Bouvet Island'
},
{
	value: 237,
	label: 'Tuvalu'
},
{
	value: 238,
	label: 'Curacao'
},
{
	value: 239,
	label: 'Dominican Republic'
},
{
	value: 240,
	label: 'Vietnam'
},

{
	value: 241,
	label: 'Cocos (Keeling) Islands'
},
{
	value: 242,
	label: 'Grenada'
},
{
	value: 243,
	label: 'Tanzania'
},
{
	value: 244,
	label: 'Argentina'
},
{
	value: 245,
	label: 'Macau, China',
},

{
	value: 246,
	label: 'Benin'
},
{
	value: 247,
	label: 'Romania'
},
{
	value: 248,
	label: 'Armenia'
},
{
	value: 249,
	label: 'global'
},
{
	value: 250,
	label: 'IFRS para SMEs'
},

{
	value: 251,
	label: 'US GAAP'
},
{
	value: 252,
	label: 'Estrutura de relatórios financeiros da AICPA para entidades de pequeno e médio porte'
},
{
	value: 253,
	label: 'South Sudan'
}
];

export const pagingSvgHoverText = {
	first: 'Primeira Página',
	previous: 'Página anterior',
	next: 'Próxima página',
	last: 'Última Página'
};

export const priorityTypesForDropdown = [{
	value: 1,
	label: 'Baixo',
	className: 'Low'
},
{
	value: 2,
	label: 'Médio',
	className: 'Medium'
},
{
	value: 3,
	label: 'Alto',
	className: 'High'
},
{
	value: 4,
	label: 'Crítico',
	className: 'Critical'
}
];

export const reviewNoteFilterTypes = [{
	value: 0,
	label: 'Todos'
},
{
	value: 1,
	label: 'Aberto'
},
{
	value: 2,
	label: 'Resolvido'
},
{
	value: 3,
	label: 'Fechado'
}
];

export const reviewStatus = [{
	id: 1,
	name: 'Aberto'
},
{
	id: 2,
	name: 'Resolvido'
},
{
	id: 3,
	name: 'Fechado'
}
];

export const reviewNoteOpenStatusOption = [{
	value: 2,
	label: 'limpar'
},
{
	value: 3,
	label: 'Fechar'
}
];

export const reviewNoteClearedStatusOption = [{
	value: 1,
	label: 'Reabrir'
},
{
	value: 3,
	label: 'Fechar'
}
];

export const reviewNoteBulkClearedStatusOption = [{
	value: 1,
	label: 'Reabrir'
},
{
	value: 2,
	label: 'Resolver'
},
{
	value: 3,
	label: 'Fechar'
}
];

export const reviewNoteClosedStatusOption = [{
	value: 1,
	label: 'Reabrir'
},
{
	value: 4,
	label: 'Excluir'
}
];

export const taskTypeBadge = {
	1: 'OST',
	2: 'PST',
	3: 'WT',
	4: 'TOC',
	5: 'OSP',
	6: 'PSP',
	7: 'RT',
	8: 'GT',
	9: 'PIC',
	10: 'EQR',
	11: 'PIC/EQR',
	22: 'ACT',
	23: 'UDP'
};

export const riskTypes = [{
	id: 1,
	name: 'Riscos significativos',
	abbrev: 'RS',
	label: 'Significativo',
	title: 'Risco significativo'
},
{
	id: 2,
	name: 'Riscos de Fraude',
	abbrev: 'RF',
	label: 'Fraude',
	title: 'Risco de fraude'
},
{
	id: 3,
	name: 'Risco de distorção relevante',
	abbrev: 'R',
	label: 'Risco de distorção relevante',
	title: 'Risco de distorção relevante'
},
{
	id: 4,
	name: 'Estimativa de risco mais baixo',
	abbrev: 'VLRS',
	label: 'Estimativa de risco mais baixo',
	title: 'Estimativa de risco mais baixo'
},
{
	id: 5,
	name: 'Estimativa de risco mais baixo',
	abbrev: 'ERMB',
	label: 'Estimativa de risco mais baixo',
	title: 'Estimativa de risco mais baixo'
},
{
	id: 6,
	name: 'Estimativa de risco mais alto',
	abbrev: 'ERMA',
	label: 'Estimativa de risco mais alto',
	title: 'Estimativa de risco mais alto'
},
{
	id: 7,
	name: 'Estimativa - Não selecionada',
	abbrev: 'ENS',
	label: 'Estimativa - Não selecionada',
	title: 'Estimativa - Não selecionada'
}

];

export const relatedRisksDropdownRiskTypes = [{
	id: 1,
	name: 'Riscos significativos',
	abbrev: 'SR',
	label: 'Significativo',
	title: 'Risco significativo'
},
{
	id: 2,
	name: 'Riscos de fraude',
	abbrev: 'FR',
	label: 'Fraude',
	title: 'Risco de fraude'
},
{
	id: 3,
	name: 'Risco de distorção material',
	abbrev: 'R',
	label: 'Risco de distorção material',
	title: 'Risco de distorção material'
}
];

export const estimateTypes = [{
	id: 4,
	name: 'Estimativa de risco muito baixo',
	abbrev: 'ERMB',
	label: 'Muito baixo',
	title: 'Estimativa de risco muito baixo'
},
{
	id: 5,
	name: 'Estimativa de risco mais baixo',
	abbrev: 'ERB',
	label: 'Mais baixo',
	title: 'Estimativa de risco mais baixo'
},
{
	id: 6,
	name: 'Estimativa de risco mais alto',
	abbrev: 'ERMA',
	label: 'Mais alto',
	title: 'Estimativa de risco mais alto'
},
{
	id: 7,
	name: 'Estimativa - Não selecionada',
	abbrev: 'NA',
	label: 'Não selecionado',
	title: 'Estimativa - Não selecionada'
}
];

export const statementTypes = [{
	id: 1,
	name: 'demonstração de resultados'
},
{
	id: 2,
	name: 'Balanço patrimonial'
},
{
	id: 3,
	name: 'ambos'
}
];

export const RbacErrors = {
	106: 'Permissões insuficientes para editar conteúdo. Trabalhe com um administrador de trabalho para obter direitos suficientes.'
};

export const HelixProjectValidationErrors = {
	800: 'Parece que você não acessou eY Helix antes? Ir para',
	801: 'Você não é um membro autorizado da equipe do projeto vinculado. Entre em contato com o administrador do projeto EY Helix para ter acesso.',
	901: 'O Projeto EY Helix selecionado não está mais disponível. Clique nos projetos do EY Helix para vincular um novo projeto.',
	902: 'O Projeto EY Helix selecionado está marcado para exclusão. Clique nos projetos do EY Helix para vincular um novo projeto.',
	903: 'O Projeto EY Helix selecionado está marcado para armazenamento. Clique nos projetos do EY Helix para vincular um novo projeto.',
	926: 'O analisador selecionado não está disponível no EY Helix. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	927: 'As análises não estão disponíveis no projeto vinculado. Vá para EY Helix e complete as etapas de processamento de dados & análises para continuar.',
	928: 'Analisador inválido ou ausente em EY Helix. Atualize a página e tente novamente. Se o problema persistir, entre em contato com o Help Desk.',
	929: 'Ocorreu um erro relacionado ao projeto EY Helix vinculado. Os dados não podem ser importados.'
};

export const EngagementProfileRequirementErrors = {
	108: 'O Perfil de Engagement não está completo.'
};

export const IndependenceRequirementErrors = {
	103: 'Ausenta do cumprimento da independência do usuário do Engagement'
};

export const strategyTypes = [{
	id: 3,
	name: 'No escopo'
},
{
	id: 4,
	name: 'Fora do escopo'
}
];

export const itAppTypes = [{
	value: 0,
	label: 'Aplicativo de TI'
},
{
	value: 1,
	label: 'Organização de Serviços'
}
];

export const confidentialityLevels = {
	[confidentialityTypes.DEFAULT]: 'Padrão',
	[confidentialityTypes.LOW]: 'Baixo',
	[confidentialityTypes.MODERATE]: 'Moderado',
	[confidentialityTypes.HIGH]: 'Alto'

	// This has been disabled for release 2.5, uncomment if required
	// [confidentialityTypes.CONFIDENTIAL]: 'Confidencial'
};

export const formBodyOptionRiskTypes = [{
	id: 1,
	label: 'Risco significativo'
},
{
	id: 2,
	label: 'Risco de fraude'
},
{
	id: 3,
	label: 'Risco de distorção relevante'
}
];

export const formViewTypes = [{
	value: 0,
	label: 'Forma'
},
{
	value: 1,
	label: 'Mudanças'
},
{
	value: 2,
	label: 'Detalhes'
}
];

export const railFilterValidations = [{
	value: 0,
	label: 'Todo'
},
{
	value: 1,
	label: 'Respostas incompletas'
},
{
	value: 2,
	label: 'Comentários não resolvidos'
}
];

export const aresRiskTypes = [{
	id: 1,
	name: 'Risco significativo'
},
{
	id: 2,
	name: 'Risco de fraude'
},
{
	id: 3,
	name: 'Risco de distorção relevante'
},
{
	id: 4,
	name: 'Estimativa de risco muito baixo'
},
{
	id: 5,
	name: 'Estimativa de risco mais baixo'
},
{
	id: 6,
	name: 'Estimativa de risco mais alto'
},
{
	id: 7,
	name: 'Estimativa - Não selecionada'
}
];

export const materialityTypes = [{
	value: 1,
	label: 'Lucro antes dos impostos'
},
{
	value: 2,
	label: 'LAJIR (Lucro antes juros e impostos)'
},
{
	value: 3,
	label: 'LAJIDA (Lucro antes juros, impostos, depreciação e amortização)',
},
{
	value: 4,
	label: 'Margem bruta'
},
{
	value: 5,
	label: 'Receitas'
},
{
	value: 6,
	label: 'Despesas operacionais'
},
{
	value: 7,
	label: 'Patrimônio líquido'
},
{
	value: 8,
	label: 'Ativos'
},
{
	value: 9,
	label: 'Medidas baseadas em atividades (Outras)'
},
{
	value: 10,
	label: 'Perda antes dos impostos'
},
{
	value: 11,
	label: 'Medidas baseadas em capital (Outras)'
},
{
	value: 12,
	label: 'Medidas baseadas em lucros (Outros)'
}
];

export const helixCurrencyType = {
	[currencyType.Functional]: 'Funcional',
	[currencyType.Reporting]: 'Relatório'
};

export const controlRiskType = [{
	id: 1,
	name: 'Confiar'
},
{
	id: 2,
	name: 'Não Confiar'
},
{
	id: 3,
	name: 'Testar MP'
}
];

export const inherentRiskType = [{
	id: 1,
	name: 'Maior'
},
{
	id: 2,
	name: 'Menor'
},
{
	id: 3,
	name: 'Muito baixo'
}
];

export const AlraInherentRiskType = [{
	id: 3,
	name: 'Não relevante'
},
{
	id: 2,
	name: 'Menor'
},
{
	id: 1,
	name: 'Maior'
}
];

export const scotInherentRiskType = [{
	id: 1,
	name: 'Mais alto'
},
{
	id: 2,
	name: 'Baixo'
},
{
	id: 3,
	name: 'SCOT não rotineira'
}
];

export const CRAStrings = {
	Minimal: 'Mínimo',
	Low: 'Baixo',
	'Low +SC': 'Baixo + SC',
	Moderate: 'Moderado',
	High: 'Alto',
	'High +SC': 'Alto + SC'
};

export const priorityType = [{
	id: 1,
	name: 'Baixo',
	className: 'Low',
	label: 'L'
},
{
	id: 2,
	name: 'Médio',
	className: 'Medium',
	label: 'M'
},
{
	id: 3,
	name: 'Alto',
	className: 'High',
	label: 'H'
},
{
	id: 4,
	name: 'Crítico',
	className: 'Critical',
	label: 'C'
}
];

export const kendoLabels = {
	addComment: 'Adicione comentário',
	addColumnBefore: 'Adicionar coluna à esquerda',
	addColumnAfter: 'Adicionar coluna à direita',
	addInlineComment: 'Adicionar comentário em linha',
	addRowAbove: 'Adicionar linha acima',
	addRowBelow: 'Adicionar linha abaixo',
	alignLeft: 'Justifique à esquerda',
	alignRight: 'Justifique à direita',
	alignCenter: 'Justificar Centro',
	alignFull: ' Justificar completo',
	backgroundColor: 'Cor de fundo',
	bulletList: 'Inserir lista não ordenada',
	bold: 'Audacioso',
	backColor: 'Destacar',
	createLink: 'Insira o hyperlink',
	createTable: 'Criar tabela',
	cleanFormatting: ' Formatação limpa',
	deleteRow: ' Excluir linha',
	deleteColumn: ' Excluir coluna',
	deleteTable: 'Excluir tabela',
	fontSizeInherit: 'Tamanho da fonte',
	foreColor: 'Cor da fonte',
	format: 'Formato',
	fontSize: ' Tamanho da fonte',
	hyperlink: 'Inserir link',
	italic: 'Itálico',
	indent: 'Recuar',
	insertTableHint: 'Crie uma {0} por {1} tabela',
	huge: 'Gigante',
	'hyperlink-dialog-content-address': 'Endereço web',
	'hyperlink-dialog-title': 'Inserir hiperlink',
	'hyperlink-dialog-content-title': 'Título',
	'hyperlink-dialog-content-newwindow': 'Abrir link em nova janela',
	'hyperlink-dialog-cancel': 'Cancelar',
	'hyperlink-dialog-insert': 'Inserir',
	large: 'Grande',
	noDataPlaceholder: 'Inserir texto',
	normal: 'Normal',
	orderedList: 'Inserir lista ordenada',
	outdent: 'Reentrância',
	paragraphSize: 'Tamanho do parágrafo',
	print: 'Imprimir',
	pdf: 'exportação para pdf',
	redo: 'Refazer',
	removeFormatting: 'Remover formatação',
	strikethrough: ' Strikethrough',
	small: 'Pequeno',
	subscript: ' Subscrito',
	superscript: ' Sobrescrito',
	underline: ' Sublinhar',
	undo: 'Desfazer',
	unlink: 'Desvincular'
};

export const kendoFormatOptions = [{
	text: 'Parágrafo',
	value: 'p'
},
{
	text: 'Cabeçallho 1',
	value: 'h1'
},
{
	text: 'Cabeçallho 2',
	value: 'h2'
},
{
	text: 'Cabeçallho 3',
	value: 'h3'
},
{
	text: 'Cabeçallho 4',
	value: 'h4'
},
{
	text: 'Cabeçallho 5',
	value: 'h5'
},
{
	text: 'Cabeçallho 6',
	value: 'h6'
}
];

export const kendoFontSize = [{
	text: '8',
	value: '8px'
},
{
	text: '9',
	value: '9px'
},
{
	text: '10',
	value: '10px'
},
{
	text: '11',
	value: '11px'
},
{
	text: '12',
	value: '12px'
},
{
	text: '14',
	value: '14px'
},
{
	text: '16',
	value: '16px'
},
{
	text: '18',
	value: '18px'
},
{
	text: '20',
	value: '20px'
},
{
	text: '22',
	value: '22px'
},
{
	text: '24',
	value: '24px'
},
{
	text: '26',
	value: '26px'
},
{
	text: '28',
	value: '28px'
},
{
	text: '36',
	value: '36px'
},
{
	text: '48',
	value: '48px'
},
{
	text: '72',
	value: '72px'
}
];

export const ItFlowValidationLabels = {
	ITAppWithoutAtLeastOneRelatedITProcess: 'Aplicativo de TI não relacionado',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: 'ITGC não relacionado',
	ITSPHasNorelatedITRisk: 'ITSP não relacionado',
	ITProcessHasNoRelatedITApplication: 'Processo de TI não relacionado',
	ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication: 'Risco de TI ausente',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: 'Falta de ITGC ou marca nenhuma existe',
	ITDMorITACWithNoRelatedITApplication: 'Controle de App/ITDM sem aplicativo de TI',
	ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'ITSP a ser excluído',
	ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'ITGC a ser excluído',
	ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'Risco de TI a ser excluído',
	ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed: 'ITGC com estratégia de teste inválida',
	ITGCWithoutASelectedDesignEffectiveness: 'Avaliação de design ITGC ausente',
	SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero: 'SCOT não relacionada',
	AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed: 'Resposta inconsistente para teste de controles',
	SCOTWithHasNoITApplicationHasITDMOrAppControls: 'Aplicativo de TI com SCOT ausente'
};

export const ISA315ITFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: ItFlowValidationLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: ItFlowValidationLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: ItFlowValidationLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: ItFlowValidationLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: ItFlowValidationLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed,
	label: ItFlowValidationLabels.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: ItFlowValidationLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
}
];

/* Notes modal labels */

export const reviewNoteModalLabels = {
	/*Review Notes*/
	engagement: 'Engagement',
	emptyReplyErrorMsg: 'Adicione texto para continuar',
	lengthReplyErrorMsg: 'A resposta não pode exceder 4000 caracteres.',
	documentLabel: 'Documento',
	task: 'Tarefa',
	allEngagementFilterLabel: 'Todos os outros engagement',
	otherEngagementComments: 'Notas de outro engagement',
	notesModalInstructionalText: 'Visualize e responda às notas para o {0} selecionado abaixo.',
	commentThread: 'Tópico de nota',
	singleNoteInstructionalText: 'Visualize e responda à nota selecionada abaixo.',
	emptyNoteDetailsMessage: 'Selecione uma nota para ver os detalhes. Para habilitar controles em massa, use a tecla control ou shift e selecione várias notas de revisão. Se você quiser trabalhar em uma nota individual, selecione essa nota na lista.',
	documentReviewNotesLabel: 'Notas do documento',
	addNewReviewNoteButtonText: 'Adicionar nota',
	noNotesAssociatedWithDocumentLabel: 'Deixe uma nota usando as entradas abaixo. Atribua a nota a um usuário e especifique a prioridade e a data de vencimento.',
	noNotesFound: 'Nenhuma nota encontrada',
	noNotesAssociatedWithTaskLabel: 'Não há {0} notas associadas à tarefa.',
	allNotesLabel: 'Todas as notas',
	charactersLabel: 'Caracteres',
	myNotesLabel: 'Minhas anotações',
	showClearedLabel: 'Mostrar limpo',
	showClosedLabel: 'Mostrar fechado',
	assignedToLabel: 'Atribuído a',

	ofLabel: 'do',
	enterNoteText: 'Digite a nota',
	addNewNoteModalClose: 'Fechar',
	addNewNoteModalTitleLabel: 'Adicionar nova nota',
	editNoteModalTitleLabel: 'Editar nota',
	deleteIconHoverText: 'Excluir',
	deleteIconModalAcceptText: 'Excluir',
	deleteIconModalConfirmMessage: 'Tem certeza de que deseja excluir sua resposta a esta nota?',
	deleteIconModalConfirmMessageParent: 'Tem certeza de que deseja excluir a nota selecionada?',
	deleteIconModalTitleLabel: 'Excluir nota',
	deleteReplyIconModalTitle: 'Excluir resposta',
	emptyRepliesMessage: 'Nenhuma resposta ainda',
	replyInputPlaceholder: 'Responda a esta nota',
	replyInputPlaceholderEdit: 'Edite a resposta com uma nota ou uma nota de voz',
	noteInputPlaceholderEdit: 'Edite com uma nota ou uma nota de voz',
	replyText: 'Texto de resposta',
	editReplyModelTitle: 'Editar resposta',
	editReplyPlaceholder: 'Digite a resposta',
	noteDueDateLabel: 'Vencimento',

	priorityLabel: 'Prioridade',
	dueDateLabel: 'Data de Vencimento',
	dueLabel: 'Vencimento',
	status: 'Status',
	noteModifiedDateLabel: 'Modificado',
	cancelLabel: 'Cancelar',
	saveLabel: 'Salvar',
	clearedBy: 'Limpo por',
	closedBy: 'Fechado por',
	reopenedBy: 'Reaberto por',
	reply: 'Responder',
	editIconHoverTextLabel: 'Editar',
	required: 'Requeridos',
	closeTitle: 'Fechar',
	otherEngagementNotes: 'Outras notas do engagement',
	closeLabel: 'Fechar',
	showMore: 'Mostre mais',
	showLess: 'Mostre menos',
	showMoreEllipsis: 'Mostre mais...',
	showLessEllipsis: 'Mostre menos...',
	noResultFound: 'Nenhum resultado encontrado',
	engagementNameLabel: 'Nome do engagement: ',
	taskReviewNotesLabel: 'Notas da tarefa',
	fromUserLabel: 'A partir de',
	toUserLabel: 'Para',
	view: 'Visualizar',
	dueDateRequiredTextError: 'A data de vencimento é obrigatória'
};

export const notesFilterLabels = [{
	id: notesFilter.allNotes,
	label: 'Todas as notas',
	value: notesFilter.allNotes
},
{
	id: notesFilter.myNotes,
	label: 'Minhas notas',
	value: notesFilter.myNotes
},
{
	id: notesFilter.authoredByMeNotes,
	label: 'Atribuído a mim',
	value: notesFilter.authoredByMeNotes
}
];

export const reviewerAssignments = {
	taskLayoutHeaderAssignments: 'atribuições',
	manageAssigmentsStep2: 'Editar atribuição de tarefa',
	editAssignment: 'Editar atribuição',
	deleteAssignment: 'Excluir atribuição',
	manageAssigmentsStep3: 'Concluir tarefa',
	taskAssigmentStatusHeader: 'Status da atribuição',
	taskAssignmentName: 'Atribuição',
	dueDateAssigment: 'Vencimento',
	editDueDate: 'Opcional: editar dias antes da data de término',
	teamMemberAssigmentLabel: 'Membro da equipe',
	currentAssigmentLabel: 'Atual',
	handOffToAssigmentLabel: 'Entregue para: ',
	priorToEndDateLabel: 'antes da data de término',
	noTimePhaseAssigmentLabel: 'Nenhuma fase de tempo atribuída',
	closedByAssigmentLabel: 'Fechado por',
	onAssingmentLabel: 'sobre',
	preparerAssigmentOpenTitleTip: 'Entregue esta tarefa para encerrar sua atribuição de tarefas',
	reviewerAssigmentOpenTitleTip: 'Marcar atribuição de tarefa como fechada',
	reviewerAssigmentClosedTitleTip: 'Marcar atribuição de tarefa como aberta',
	AssigmentLabel: 'Entregue esta tarefa para encerrar sua atribuição de tarefas',
	timePhaseName: 'Fase de tempo: ',
	timePhaseEndDate: 'Data final: ',
	AssignmentType: [{
		id: 1,
		displayName: 'Preparador'
	},
	{
		id: 2,
		displayName: 'Revisor de detalhes'
	},
	{
		id: 3,
		displayName: 'Revisor geral'
	},
	{
		id: 4,
		displayName: 'Parceiro'
	},
	{
		id: 5,
		displayName: 'EQR'
	},
	{
		id: 6,
		displayName: 'Outro'
	}
	],
	AssignmentStatus: [{
		id: 1,
		displayName: 'Abrir'
	},
	{
		id: 2,
		displayName: 'Em andamento'
	},
	{
		id: 3,
		displayName: 'Fechado'
	},
	{
		id: 4,
		displayName: 'Não atribuído'
	}
	],
	assignmentTableColumnHeader: 'Atribuição',
	teamMemberTableColumnHeader: 'Membro da equipe',
	dueDaysTableColumnHeader: 'Vencimento',
	daysPriorToEndDate: 'dias antes da data de término',
	handoffButton: 'Transferir'
};
/* Notes modal labels */

export const handOffModal = {
	title: 'Hand-off',
	description: 'Entregue esta tarefa para o próximo membro da equipe. Para assinar um arquivo de evidência, selecione entre as opções abaixo.',
	dropdownLabel: 'Hand-off to',
	closeTitle: 'Cancelar',
	confirmButton: 'Hand-off',
	evidence: 'Evidência',
	evidenceSignOffTitle: 'Assine em todos como: ',
	existingSignOffs: 'Aprovações existentes',
	noDocumentsAvailable: 'Nenhum documento disponível'
};

// manage scot modal labels
export const manageSCOTModal = {
	title: 'Gerenciar SCOTs',
	description: 'Você pode criar, editar ou excluir SCOTs existentes. As alterações serão aplicadas depois de salvas.',
	addSCOTLink: 'Adicionar SCOT'
};

export const deleteSCOTModal = {
	title: 'Excluir SCOT',
	description: 'A(s) seguinte(s) SCOT(s) serão excluídas. A ação não pode ser desfeita.'
};

export const manageITAppModalLabels = {
	title: 'Gerenciar aplicativos de TI',
	description: 'Crie novos aplicativos de TI ou edite e exclua os aplicativos de TI existentes abaixo.',
	inputNameTitle: 'Nome do aplicativo de TI',
	deleteConfirmMessage: 'Tem certeza de que deseja excluir o aplicativo de TI <b>{0}</b>? Isto não pode ser desfeito.',
	addSuccessMessage: "O aplicativo de TI'{0}' foi criado com sucesso",
	editSuccessMessage: "Edições salvas com sucesso no aplicativo de TI'{0}'",
	deleteSuccessMessage: "'{0}' foi excluído com sucesso"
};

export const manageSOModalLabels = {
	title: 'Gerenciar serviços da organização',
	description: 'Crie novas organizações de serviços ou edite e exclua as organizações de serviços existentes abaixo.',
	inputNameTitle: 'Nome da organização de serviço',
	deleteConfirmMessage: 'Tem certeza de que deseja excluir a organização de serviço <b>{0}</b>? Isto não pode ser desfeito.',
	addSuccessMessage: "A organização de serviço'{0}' foi criada com sucesso",
	editSuccessMessage: "Edições salvas com sucesso na organização de serviço'{0}'",
	deleteSuccessMessage: "'{0}' foi excluído com sucesso",
	addServiceOrganization: 'Adicionar organização de serviço',
	editServiceOrganization: 'Editar organização do serviço',
	deleteServiceOrganization: 'Excluir organização de serviço'
};

export const customNameModal = {
	title: 'Sufixo do nome da fonte JE',
	closeTitle: 'Cancelar',
	save: 'Salvar',
	suffixRequired: 'Sufixo requerido!',
	suffix: 'Sufixo',
	addSuffix: 'Adicionar sufixo',
	editSuffix: 'Editar sufixo'
};

export const GuidedWorkFlowLabels = {
	RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM: 'Eventos e condições não relacionados/risco de distorção',
	RisksUnrelatedToAnAssertionForGuidedWorkflow: 'Riscos não relacionados',
	IncompleteMeasurementBasisForecastAmount: 'Base incompleta',
	IncompleteMeasurementBasisForecastAmountRationale: 'Racional da base necessário',
	IncompleteMeasurementBasisAdjustedAmount: 'Valor ajustado incompleto',
	IncompletePlanningMateriality: 'PM incompleta',
	PlanningMaterialityGreaterThanMaximumAmount: 'PM muito grande',
	IncompletePlanningMaterialityRationale: 'Racional da PM necessário',
	IncompleteTolerableError: 'TE incompleto',
	TENotWithinRangeOfAllowedValues: 'Percentual do TE inválido',
	IncompleteTolerableErrorRationale: 'Racional do TE necessário',
	IncompleteSAD: 'SAD incompleto',
	SADGreaterThanMaximum: 'SAD muito grande',
	IncompleteSADRationale: 'Racional do SAD necessário',
	IncompletePACESelection: 'PACE incompleto',
	AccountWithoutIndividualRiskAssessmentForm: 'Conta {0} documento ausente',
	EstimateWithoutIndividualEstimateForm: 'Estimativa{0} documento ausente',
	AccountWithoutIndividualAnalyticForm: 'Conta {0} documento ausente',
	MultiEntityWithoutIndividualProfileForm: 'Entidade sem documento de Perfil Individual Multi-entidade',
	AccountAccountTypeIDDoesNotMatchAction: 'Seleção inconsistente de designação de conta',
	AccountHasEstimateDoesNotMatchAction: 'Seleção inconsistente de estimativa de conta',
	AccountFormOptionHasRelatedRisksNotAssociatedToAccount: 'Risco sem associação de conta relacionada',
	AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk: 'Assertiva de maior risco inerente sem risco',
	AccountMissingSubstantiveProcedure: 'Conta/Entidades sem procedimento substantivo',
	MultiEntityNotRelatedToALLPSTACTForRelatedAccount: 'Conta/Entidades exigem atualização de conteúdo',
	ComponentWithoutGroupInvolvementForm: 'Componente sem formulário de envolvimento de grupo (excluindo componentes apenas de referência)',
	ComponentWithoutRelatedGroupAssessmentInstruction: 'Componente sem instrução de avaliação de risco de grupo',
	IncompleteAssertionRiskLevel: 'Nível de risco de assertiva incompleta',
	EstimateAccountWithoutEsimatePSPIndex: 'Estimativa de conta sem estimativa de índice PSP',
	AccountExecutedWithoutRelatedComponent: 'Grupo - Contas (executadas em outros engagement) sem um componente de escopo Completo ou Específico relacionado',
	MultiEntityAccountWithoutRelatedToAnyMultiEntity: 'Conta sem entidade relacionada',
	ChangeNotSubmittedMultiEntityFullProfile: 'Alterações não enviadas',
	ChangeNotSubmittedMultiEntityIndividualDocument: 'Alterações não enviadas',
	AccountTypeWithMissingInformation: 'Faltam informações da conta',
	DocumentUploadMissingRequiredPICEQRSignOffs: 'Evidência faltando assinatura(s)',
	DocumentUploadMissingRequiredPICEQRSignOffRequirements: 'Evidência faltando assinatura(s) obrigatória(s)',
	DocumentUploadMissingPreparerOrReviewerSignOffs: 'Upload de documentos - Faltando assinaturas do Preparador ou Revisor',
	ITAppWithoutAtLeastOneRelatedITProcess: 'Aplicativo de TI não relacionado',
	ITProcessHasNoRelatedITApplication: 'Processo de TI não relacionado',
	ITGCWithoutASelectedDesignEffectiveness: 'Falta de avaliação de desenho do ITGC',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: 'ITGC não relacionado',
	ITSPHasNorelatedITRisk: 'ITSP não relacionado',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: 'Falta ITGC ou nenhuma marca existe',
	EstimateWithoutAccountRelated: 'Estimativa sem conta relacionada',
	EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount: 'Estimativa de risco mais alto/mais baixo relacionada a conta não estimada',
	RiskEstimateRiskTypeIDDoesNotMatchAction: "A resposta da categoria da estimativa não está alinhada com a designação em'Editar estimativa'",
	LowerorHigherRiskEstimateWithoutEstimateSCOT: 'Estimativa sem SCOT válida',
	EstimateWithoutIndividualEstimateDocument: 'Estimativa faltando documento individual',
	EstimateAccountWithoutHigherOrLowerRiskEstimate: 'Conta sem estimativa válida',
	EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary: 'Conta de estimativa sem Estimativa de Risco Mais Alto ou Mais Baixo',
	EstimateScotWithoutHigherOrLowerRiskEstimate: 'SCOT de Estimativa sem Estimativa de Risco Mais Alto ou Mais Baixo',
	HigherRiskEstimateWithoutRisk: 'Estimativa de risco mais alta sem risco válido relacionado',
	PICEQRSignOffRequirements: 'PIC or EQR Signoff requirement does not match response',
	AdjustmentsWithoutAnyEvidence: 'Ajustes sem evidências',
	AdjustmentsThatDoNotNet: 'Ajustes que não compensam',
	DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration: 'Documento com check-out ou em processo de check-out para colaboração',
	NonEngagementWideTasksMissingEvidence: 'Tarefas amplas não relacionadas ao Engagement faltando evidências',
	EstimatesMustBeMarkedHigherRisk: 'Risco significativo/fraude relacionado a uma estimativa que não é um risco mais alto',
	SCOTEstimateNoRelatedWalkthroughForm: 'SCOT/Estimativa sem Walkthrough',
	SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2: 'SCOT sem conta relacionada',
	AccountSignificantDisclosureWithNoRelatedSCOTV2: 'Account - Significant Account / Significant Disclosure that is not a CT only Account with no related SCOT or an Estimate when it is an Estimate Account',
	ITApplicationWithoutITAppRiskAssessmentIndividualDocument: 'Technology risk assessment missing document',
	ITApplicationWithoutITAppPlanningIndividualDocument: 'Technology missing document',
	FormContentWithoutHeader: 'Form Content without Header',
	RisksWithoutAnyRelatedAssertions: 'There are risks that have not been related to at least one assertion',
	AssertionsWithIncompleteCRA: 'There are assertions missing an inherent and/or control risk assessment',
	LimitedRiskOrInsignificantAccountMissingRationale: 'All limited risk and insignificant accounts shall have rationale provided',
	ITProcessWithoutWalkthroughDocument: 'ITProcess without IT process - Walkthrough - Individual',
	ITProcessIsUncategorized: 'IT Process - ITProcessTypeID is Uncategorized',
	ITProcessWithNoRelatedITApplication: 'ITProcess - ITProcess with no related IT Application'
};

export const GuidedWorkFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.RiskEstimateRiskTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.RiskEstimateRiskTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.FormContentWithoutHeader,
	label: GuidedWorkFlowLabels.FormContentWithoutHeader
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary
},
{
	validationId: validationTypes.EstimateScotWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateScotWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.HigherRiskEstimateWithoutRisk,
	label: GuidedWorkFlowLabels.HigherRiskEstimateWithoutRisk
},
{
	validationId: validationTypes.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM,
	label: GuidedWorkFlowLabels.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.RisksUnrelatedToAnAssertionForGuidedWorkflow,
	label: GuidedWorkFlowLabels.RisksUnrelatedToAnAssertionForGuidedWorkflow
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmount
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmountRationale,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmountRationale
},
{
	validationId: validationTypes.IncompleteMeasurementBasisAdjustedAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisAdjustedAmount
},
{
	validationId: validationTypes.IncompletePlanningMateriality,
	label: GuidedWorkFlowLabels.IncompletePlanningMateriality
},
{
	validationId: validationTypes.PlanningMaterialityGreaterThanMaximumAmount,
	label: GuidedWorkFlowLabels.PlanningMaterialityGreaterThanMaximumAmount
},
{
	validationId: validationTypes.IncompletePlanningMaterialityRationale,
	label: GuidedWorkFlowLabels.IncompletePlanningMaterialityRationale
},
{
	validationId: validationTypes.IncompleteTolerableError,
	label: GuidedWorkFlowLabels.IncompleteTolerableError
},
{
	validationId: validationTypes.TENotWithinRangeOfAllowedValues,
	label: GuidedWorkFlowLabels.TENotWithinRangeOfAllowedValues
},
{
	validationId: validationTypes.IncompleteTolerableErrorRationale,
	label: GuidedWorkFlowLabels.IncompleteTolerableErrorRationale
},
{
	validationId: validationTypes.IncompleteSAD,
	label: GuidedWorkFlowLabels.IncompleteSAD
},
{
	validationId: validationTypes.SADGreaterThanMaximum,
	label: GuidedWorkFlowLabels.SADGreaterThanMaximum
},
{
	validationId: validationTypes.IncompleteSADRationale,
	label: GuidedWorkFlowLabels.IncompleteSADRationale
},
{
	validationId: validationTypes.IncompletePACESelection,
	label: GuidedWorkFlowLabels.IncompletePACESelection
},
{
	validationId: validationTypes.AccountWithoutIndividualRiskAssessmentForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualRiskAssessmentForm
},
{
	validationId: validationTypes.EstimateWithoutIndividualEstimateForm,
	label: GuidedWorkFlowLabels.EstimateWithoutIndividualEstimateForm
},
{
	validationId: validationTypes.AccountWithoutIndividualAnalyticForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualAnalyticForm
},
{
	validationId: validationTypes.MultiEntityWithoutIndividualProfileForm,
	label: GuidedWorkFlowLabels.MultiEntityWithoutIndividualProfileForm
},
{
	validationId: validationTypes.AccountAccountTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountAccountTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.AccountHasEstimateDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountHasEstimateDoesNotMatchAction
},
{
	validationId: validationTypes.AccountFormOptionHasRelatedRisksNotAssociatedToAccount,
	label: GuidedWorkFlowLabels.AccountFormOptionHasRelatedRisksNotAssociatedToAccount
},
{
	validationId: validationTypes.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk,
	label: GuidedWorkFlowLabels.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk
},
{
	validationId: validationTypes.MultiEntityNotRelatedToALLPSTACTForRelatedAccount,
	label: GuidedWorkFlowLabels.MultiEntityNotRelatedToALLPSTACTForRelatedAccount
},
{
	validationId: validationTypes.AccountMissingSubstantiveProcedure,
	label: GuidedWorkFlowLabels.AccountMissingSubstantiveProcedure
},
{
	validationId: validationTypes.ComponentWithoutGroupInvolvementForm,
	label: GuidedWorkFlowLabels.ComponentWithoutGroupInvolvementForm
},
{
	validationId: validationTypes.ComponentWithoutRelatedGroupAssessmentInstruction,
	label: GuidedWorkFlowLabels.ComponentWithoutRelatedGroupAssessmentInstruction
},
{
	validationId: validationTypes.EstimateAccountWithoutEstimatePSPIndex,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutEsimatePSPIndex
},
{
	validationId: validationTypes.AssertionInherentRiskWithoutRelatedHigherRisk,
	label: GuidedWorkFlowLabels.IncompleteAssertionRiskLevel
},
{
	validationId: validationTypes.AccountGroupWithoutAComponent,
	label: GuidedWorkFlowLabels.AccountExecutedWithoutRelatedComponent
},
{
	validationId: validationTypes.MultiEntityAccountWithoutRelatedToAnyMultiEntity,
	label: GuidedWorkFlowLabels.MultiEntityAccountWithoutRelatedToAnyMultiEntity
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityFullProfile,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityFullProfile
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityIndividualDocument,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityIndividualDocument
},
{
	validationId: validationTypes.AccountWithMissingValues,
	label: GuidedWorkFlowLabels.AccountTypeWithMissingInformation
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffs
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffRequirements
},
{
	validationId: validationTypes.DocumentUploadMissingPreparerOrReviewerSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingPreparerOrReviewerSignOffs
},
{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: GuidedWorkFlowLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: GuidedWorkFlowLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: GuidedWorkFlowLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: GuidedWorkFlowLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: GuidedWorkFlowLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
},
{
	validationId: validationTypes.EstimateWithoutAccountRelated,
	label: GuidedWorkFlowLabels.EstimateWithoutAccountRelated
},
{
	validationId: validationTypes.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount,
	label: GuidedWorkFlowLabels.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount
},
{
	validationId: validationTypes.LowerorHigherRiskEstimateWithoutEstimateSCOT,
	label: GuidedWorkFlowLabels.LowerorHigherRiskEstimateWithoutEstimateSCOT
},
{
	validationId: validationTypes.PICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.PICEQRSignOffRequirements
},
{
	validationId: validationTypes.EstimatesMustBeMarkedHigherRisk,
	label: GuidedWorkFlowLabels.EstimatesMustBeMarkedHigherRisk
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.NonEngagementWideTasksMissingEvidence,
	label: GuidedWorkFlowLabels.NonEngagementWideTasksMissingEvidence
},
{
	validationId: validationTypes.AdjustmentsWithoutAnyEvidence,
	label: GuidedWorkFlowLabels.AdjustmentsWithoutAnyEvidence
},
{
	validationId: validationTypes.AdjustmentsThatDoNotNet,
	label: GuidedWorkFlowLabels.AdjustmentsThatDoNotNet
},
{
	validationId: validationTypes.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration,
	label: GuidedWorkFlowLabels.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration
},
{
	validationId: validationTypes.ITApplicationWithoutITAppRiskAssessmentIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppRiskAssessmentIndividualDocument
},
{
	validationId: validationTypes.SCOTEstimateNoRelatedWalkthroughForm,
	label: GuidedWorkFlowLabels.SCOTEstimateNoRelatedWalkthroughForm
},
{
	validationId: validationTypes.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2,
	label: GuidedWorkFlowLabels.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2
},
{
	validationId: validationTypes.AccountSignificantDisclosureWithNoRelatedSCOTV2,
	label: GuidedWorkFlowLabels.AccountSignificantDisclosureWithNoRelatedSCOTV2
},
{
	validationId: validationTypes.ITApplicationWithoutITAppPlanningIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppPlanningIndividualDocument
},
{
	validationId: validationTypes.RisksWithoutAnyRelatedAssertions,
	label: GuidedWorkFlowLabels.RisksWithoutAnyRelatedAssertions
},
{
	validationId: validationTypes.AssertionsWithIncompleteCRA,
	label: GuidedWorkFlowLabels.AssertionsWithIncompleteCRA
},
{
	validationId: validationTypes.LimitedRiskOrInsignificantAccountMissingRationale,
	label: GuidedWorkFlowLabels.LimitedRiskOrInsignificantAccountMissingRationale
},
{
	validationId: validationTypes.ITProcessWithoutWalkthroughDocument,
	label: GuidedWorkFlowLabels.ITProcessWithoutWalkthroughDocument
},
{
	validationId: validationTypes.ITProcessIsUncategorized,
	label: GuidedWorkFlowLabels.ITProcessIsUncategorized
},
{
	validationId: validationTypes.ITProcessWithNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessWithNoRelatedITApplication
}

];

// Label overrides (redefine here labels / objects that apply for a different part of the application)
export const resourceOverrides = {
	['Ares']: {
		labels: {
			notAROMM: 'Não é um risco de distorção relevante',
			fraudRisk: 'Risco de fraude',
			significantRisk: 'Risco Significativo',
			identifiedRiskFactors:
				'Evento identificado/condição, risco de distorção relevante, riscos significativos e riscos de fraude',
			countUnassociatedRisk:
				"os eventos/condições não estão relacionados/não estão marcados como'Não é um risco de distorção relevante'."
		},
		riskTypes: [{
			id: 1,
			name: 'Risco Significativo',
			abbrev: 'S',
			label: 'Significante',
			title: 'Risco Significativo'
		},
		{
			id: 2,
			name: 'Risco de fraude',
			abbrev: 'F',
			label: 'Fraude',
			title: 'Risco de fraude'
		},
		{
			id: 3,
			name: 'Risco de distorção relevante',
			abbrev: 'R',
			label: 'Risco de distorção relevante',
			title: 'Risco de distorção relevante'
		}
		]
	}
};

export const jeSourceTypes = [{
	value: 1,
	label: 'Sistema gerado'
},
{
	value: 2,
	label: 'Manual'
},
{
	value: 3,
	label: 'Ambos'
}
];

export const hasJournalEntriesOption = [{
	value: 1,
	label: 'Sim'
},
{
	value: 2,
	label: 'Não'
}
];

export const filterReviewNoteStatus = [{
	value: 1,
	label: 'Aberto'
},
{
	value: 2,
	label: 'Resolvido'
},
{
	value: 3,
	label: 'Fechado'
}
];

export const EntitiesLabels = {
	close: 'Fechar',
	cancel: 'Cancelar',
	repNoRecordMessage: 'Nenhum resultado encontrado',
	edit: 'Editar',
	delete: 'Excluir',
	actions: 'Ações',
	show: 'Mostrar',
	first: 'Primeiro',
	last: 'Último',
	prev: 'Página anterior',
	next: 'Próxima página',
	search: 'Procurar',
	primary: 'Primary',
	knowledgeRiskLabel: 'Risks from knowledge cannot be edited or deleted',


	[Entity.Account]: {
		manageEntity: 'Gerenciar contas e divulgações',
		searchEntity: 'Pesquisar contas',
		createEntity: 'Nova conta',
		entityName: 'conta',
		entityNameCaps: 'Conta',
		entityNamePlural: 'Contas',
		placeholderText: 'Crie novas contas e divulgações ou edite e exclua contas e divulgações existentes abaixo.',
		deleteConfirmLabel: 'Tem certeza de que deseja excluir esta conta? Todos os relacionamentos existentes serão removidos. Esta ação não pode ser desfeita.'
	},
	[Entity.Estimate]: {
		manageEntity: 'Gerenciar estimativas',
		searchEntity: 'Estimativas de pesquisa',
		createEntity: 'Nova estimativa',
		entityName: 'estimativa',
		entityNameCaps: 'Estimativa',
		entityNamePlural: 'Estimativas',
		placeholderText: 'Crie novas estimativas ou edite e exclua as estimativas existentes abaixo.',
		deleteConfirmLabel: 'Tem certeza de que deseja excluir esta estimativa? Todos os relacionamentos existentes serão removidos. Esta ação não pode ser desfeita.'
	},
	[Entity.Risk]: {
		manageEntity: 'Gerenciar riscos',
		searchEntity: 'Pesquisar risco',
		createEntity: 'Novo risco',
		entityName: 'risco',
		entityNameCaps: 'Risco',
		entityNamePlural: 'Riscos',
		placeholderText: 'Criar novos riscos ou editar e excluir os riscos existentes abaixo.',
		deleteConfirmLabel: 'Tem certeza de que deseja excluir este risco? Todos os relacionamentos existentes serão removidos. Esta ação não pode ser desfeita.'
	},
	[Entity.STEntity]: {
		manageEntity: 'Gerenciar entidades',
		searchEntity: 'Pesquisar entidades',
		createEntity: 'Nova entidade',
		entityName: 'entidade',
		entityNameCaps: 'Entidade',
		entityNamePlural: 'Entidades',
		placeholderText: 'Crie novas entidades ou edite e exclua as entidades existentes abaixo.',
		deleteEntity: 'Excluir entidade',
		deleteConfirmLabel: 'Tem certeza de que deseja excluir esta entidade? Todos os relacionamentos existentes serão removidos. Esta ação não pode ser desfeita.'
	},
	[Entity.Control]: {
		manageEntity: 'Gerenciar controle',
		searchEntity: 'Controle de pesquisa',
		createEntity: 'Novo controle',
		entityName: 'controle',
		entityNameCaps: 'controle',
		entityNamePlural: 'Controles',
		placeholderText: 'Crie novos controles ou edite e exclua os controles existentes abaixo.',
		deleteEntity: 'Excluir controle',
		deleteConfirmLabel: 'Tem certeza de que deseja excluir este controle? Todos os relacionamentos existentes serão removidos. Esta ação não pode ser desfeita.'
	},
	[Entity.ITProcess]: {
		manageEntity: 'Gerenciar processos de TI',
		searchEntity: 'Pesquisar processo de TI',
		createEntity: 'Novo processo de TI',
		entityName: 'Processo de TI',
		entityNameCaps: 'Processos de TI',
		entityNamePlural: 'Processos de TI',
		placeholderText: "Crie novos processos de TI ou edite e exclua os processos de TI existentes abaixo.",
		deleteEntity: 'Excluir processo de TI',
		deleteConfirmLabel: 'Tem certeza de que deseja excluir este processo de TI? Todos os relacionamentos existentes serão removidos. Esta ação não pode ser desfeita.'
	},
	[Entity.ITRisk]: {
		manageEntity: 'Gerenciar riscos de tecnologia',
		searchEntity: 'Pesquisar riscos de tecnologia',
		createEntity: 'Novo risco de tecnologia',
		entityName: 'Risco de tecnologia',
		entityNameCaps: 'Riscos de tecnologia',
		entityNamePlural: 'Riscos de tecnologia',
		placeholderText: 'Criar novos riscos de tecnologia ou editar e excluir riscos de tecnologia existentes abaixo.',
		deleteEntity: 'Excluir risco de tecnologia',
		deleteConfirmLabel: 'Você tem certeza de que deseja excluir este risco de tecnologia? Todos os relacionamentos existentes serão removidos. Esta ação não pode ser desfeita.'
	},
	[Entity.ITControl]: {
		ITGC: {
			manageEntity: 'Gerenciar ITGCs',
			searchEntity: 'Pesquisar ITGCs',
			createEntity: 'Novo ITGC',
			editEntity: 'Editar ITGC',
			viewEntity: 'Ver ITGC',
			entityName: 'ITGC',
			entityNameCaps: 'ITGC',
			entityNamePlural: 'ITGCs',
			placeholderText: 'Crie um novo ITGC ou edite e exclua ITGCs existentes abaixo.',
			deleteEntity: 'Excluir ITGC',
			close: 'Fechar',
			cancel: 'Cancelar',
			processIdRequired: 'O processo de TI é obrigatório',
			save: 'Salvar',
			confirm: 'Confirmar',
			iTProcesslabel: 'Processo de TI',
			saveAndCloseLabel: 'Salvar e fechar',
			saveAndCreateLabel: 'Salvar e criar outro',
			deleteConfirmLabel: 'Tem certeza de que deseja excluir este ITGC? Todos os relacionamentos existentes serão removidos. Esta ação não pode ser desfeita.',
			operationEffectiveness: 'Efetividade operacional',
			itDesignEffectivenessHeader: 'Efetividade do desenho',
			itTestingColumnHeader: 'Teste',
			testingTitle: 'Teste',
			frequency: 'Frequência',
			controlOpertaingEffectiveness: 'Efetividade operacional',
			designEffectiveness: 'Efetividade do desenho',
			frequencyITGC: 'Selecionar frequência',
			nameITGC: 'Nome do ITGC (obrigatório)',
			itspNameRequired: 'Nome do ITSP (obrigatório)',
			noResultsFound: 'Nenhum resultado encontrado',
			selectITRisk: 'Selecione o risco de tecnologia (obrigatório)',
			itRiskRequired: 'Risco de tecnologia (obrigatório)',
			itRiskName: 'Risco de tecnologia',
			inputInvaildCharacters: 'A entrada não pode incluir a seguinte sequência de caracteres: */:<>\\?|"',
			itControlNameRequired: 'O nome do ITGC é obrigatório',
			itgcTaskDescription: 'Realize nossos testes de ITGCs desenhados para obter evidências de auditoria adequadas e suficientes de sua efetividade operacional durante todo o período de confiança.',
			selectITProcess: 'Selecionar processo de TI (obrigatório)',
			itProcessRequired: 'Processo de TI (obrigatório)',
			riskNameRequired: 'O risco de tecnologia é obrigatório',
			addModalDescription: 'Insira a descrição do ITGC.',
			editModalDescription: 'Edite o ITGC e seus atributos associados.',
			controlDesignEffectiveness: {
				[0]: {
					description: 'Não selecionado'
				},
				[1]: {
					description: 'Efetividade'
				},
				[2]: {
					description: 'Inefetivo'
				}
			},
			controlTesting: {
				[0]: {
					description: 'Não selecionado'
				},
				[1]: {
					description: 'Sim'
				},
				[2]: {
					description: 'Não'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: 'Não selecionado'
				},
				[1]: {
					description: 'Efetividade'
				},
				[2]: {
					description: 'Inefetivo'
				}
			}
		},
		ITSP: {
			manageEntity: 'Gerenciar ITSPs',
			searchEntity: 'Pesquisar ITSPs',
			createEntity: 'Novo ITSP',
			editEntity: 'Editar ITSP',
			viewEntity: 'Ver ITSP',
			inputInvaildCharacters: 'A entrada não pode incluir a seguinte sequência de caracteres: */:<>\\?|"',
			addModalDescription: 'Insira a descrição do ITSP.',
			editModalDescription: 'Edite o ITSP e seus atributos associados.',
			entityName: 'ITSP',
			selectITProcess: 'Selecionar processo de TI (obrigatório)',
			entityNameCaps: 'ITSP',
			processIdRequired: 'O processo de TI é obrigatório',
			entityNamePlural: 'ITSPs',
			itspRequired: 'O nome do ITSP é obrigatório',
			close: 'Fechar',
			cancel: 'Cancelar',
			iTProcesslabel: 'Processo de TI',
			save: 'Salvar',
			confirm: 'Confirmar',
			saveAndCloseLabel: 'Salvar e fechar',
			riskNameRequired: 'O risco de tecnologia é obrigatório',
			saveAndCreateLabel: 'Salvar e criar outro',
			placeholderText: 'Crie novo ITSP ou edite e exclua ITSPs existentes abaixo.',
			deleteEntity: 'Excluir ITGC',
			deleteConfirmLabel: 'Tem certeza de que deseja excluir este ITSP? Todos os relacionamentos existentes serão removidos. Esta ação não pode ser desfeita.',
			itspTaskDescription: 'Personalize esta descrição da tarefa para projetar a natureza, a época e a extensão dos procedimentos substantivos de TI para obter evidências de auditoria suficientes e apropriadas de que os riscos de tecnologia estão sendo abordados de forma eficaz durante o período de confiança.<br />Quando o procedimento substantivo de TI for realizado em uma data intermediária, projete e execute procedimentos para obter evidências adicionais de que os riscos de tecnologia estão sendo abordados para o período coberto por nossos procedimentos intermediários até o final do período.<br />Concluímos sobre os resultados de nossos procedimentos substantivos de TI.',
			operationEffectiveness: 'Efetividade operacional',
			itDesignEffectivenessHeader: 'Efetividade do desenho',
			itTestingColumnHeader: 'Teste',
			testingTitle: 'Teste',
			frequency: 'Frequência',
			controlOpertaingEffectiveness: 'Efetividade operacional',
			designEffectiveness: 'Efetividade do desenho',
			frequencyITGC: 'Selecionar frequência',
			nameITGC: 'Nome do ITGC (obrigatório)',
			itspNameRequired: 'Nome do ITSP (obrigatório)',
			noResultsFound: 'Nenhum resultado encontrado',
			selectITRisk: 'Selecione o risco de tecnologia (obrigatório)',
			itRiskRequired: 'Risco de tecnologia (obrigatório)',
			itRiskName: 'Risco de tecnologia',
			itProcessRequired: 'Processo de TI (obrigatório)',
			controlDesignEffectiveness: {
				[0]: {
					description: 'Não selecionado'
				},
				[1]: {
					description: 'Efetividade'
				},
				[2]: {
					description: 'Inefetivo'
				}
			},
			controlTesting: {
				[0]: {
					description: 'Não selecionado'
				},
				[1]: {
					description: 'Sim'
				},
				[2]: {
					description: 'Não'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: 'Não selecionado'
				},
				[1]: {
					description: 'Efetividade'
				},
				[2]: {
					description: 'Inefetivo'
				}
			},
		}
	},
	[Entity.ITSOApplication]: {
		manageEntity: 'Gerenciar aplicativo de TI',
		searchEntity: 'Pesquisar aplicativo de TI',
		createEntity: 'Novo aplicativo de TI',
		entityName: 'Aplicativo de TI',
		entityNameCaps: 'Aplicativos de TI',
		entityNamePlural: 'Aplicativos de TI',
		placeholderText: 'Crie novos aplicativos de TI ou edite e exclua os aplicativos de TI existentes abaixo.',
		deleteEntity: 'Excluir aplicativo de TI',
		deleteConfirmLabel: 'Tem certeza de que deseja excluir este aplicativo de TI? Todos os relacionamentos existentes serão removidos. Esta ação não pode ser desfeita.'
	},
	[Entity.SCOT]: {
		manageEntity: 'Gerenciar SCOTs',
		searchEntity: 'Pesquisar SCOTs',
		createEntity: 'Nova SCOT',
		entityName: 'SCOT',
		entityNameCaps: 'SCOTs',
		entityNamePlural: 'SCOTs',
		placeholderText: 'Criar novas SCOTs ou editar e excluir SCOTs existentes abaixo.',
		deleteEntity: 'Excluir SCOT',
		deleteConfirmLabel: 'Tem certeza de que deseja excluir esta SCOT? Todos os relacionamentos existentes serão removidos. Esta ação não pode ser desfeita.'
	},
	[Entity.SampleItem]: {
		manageEntity: 'Manage sample tags',
		searchEntity: 'Tags de pesquisa',
		createEntity: 'Nova tag',
		createManageTagEntity: 'Gerenciar grupos de tags',
		entityName: 'tag de amostra',
		entityNamePlural: 'Tags',
		entityNameForTagGroupPlural: 'Grupo de tags',
		placeholderText: "Crie novas tags ou edite e exclua as tags existentes abaixo. Se você precisar criar novos grupos de tags, clique em <b>'Gerenciar grupos de tags'</b>",
		deleteEntity: 'Excluir tag de amostra',
		deleteConfirmLabel: 'Tem certeza de que deseja excluir a tag selecionada? Ele será removido de todas as amostras às quais está relacionado. Esta ação não pode ser desfeita.'
	},
	[Entity.SampleTagGroups]: {
		manageEntity: 'Gerenciar grupos de tags de amostra',
		searchEntity: 'Search tag groups',
		createEntity: 'Novo grupo de tags',
		entityName: 'sample tag group',
		entityNameCaps: 'Grupo de tags',
		entityNamePlural: 'Grupos de tags',
		placeholderText: 'Crie novos grupos de tags ou edite e exclua exemplos de grupos de tags existentes abaixo.',
		deleteConfirmLabel: 'Tem certeza de que deseja excluir a tag selecionada? Ele será removido de todas as amostras às quais está relacionado. Esta ação não pode ser desfeita.'
	},
};

export const inherentRiskFactorTypes = [{
	id: 1,
	label: 'Complexidade',
	displayOrder: 1
},
{
	id: 2,
	label: 'Incerteza da subjetividade',
	displayOrder: 2
},
{
	id: 3,
	label: 'Fraude ou erro',
	displayOrder: 3
},
{
	id: 4,
	label: 'Mudar',
	displayOrder: 4
},
{
	id: 5,
	label: 'Natureza da conta',
	displayOrder: 5
},
{
	id: 6,
	label: 'Partes envolvidas',
	displayOrder: 6
}
];

export const executionType = [{
	id: 1,
	label: 'PT',
	toolTip: 'Procedimentos neste engagement [apenas TP]',
	value: 'Neste engagement [apenas TP]'
},
{
	id: 2,
	label: 'CT',
	toolTip: 'Procedimentos em outros engagements [apenas TC]',
	value: 'Em outros engagements [apenas TC]'
},
{
	id: 3,
	label: 'PT/CT',
	toolTip: 'Procedimentos neste e em outros engagements [TP/TC]',
	value: 'Neste e em outros engagements [TP/TC]'
}
];

export const createEditAccountModalLabels = {
	createModalDescription: "Insira os detalhes da nova conta abaixo e selecione'<b>{0}</b>' para finalizar. Para criar outra conta, selecione'<b>{1}</b>'.",
	editModalDescription: "Edite os detalhes da conta abaixo e selecione'<b>{0}</b>' para finalizar.",
	close: 'Fechar',
	cancel: 'Cancelar',
	createAccount: 'Nova conta',
	editAccount: 'Editar conta',
	newSignificantDisclosure: 'Nova divulgação significativa',
	save: 'Salvar',
	confirm: 'Confirmar',
	saveAndCloseLabel: 'Salvar e fechar',
	saveAndCreateLabel: 'Salvar e criar outro',
	accountNameLabel: 'Nome da conta (requerido)',
	accountDesignationLabel: 'Designação',
	accountExecutionTypeLabel: 'Em qual engagement do Canvas os procedimentos para esta conta serão realizados e documentados?',
	accountEstimateLabel: 'Esta conta foi afetada por uma estimativa?',
	yes: 'Sim',
	no: 'Não',
	accountStatementTypeLabel: 'Tipo de divulgação',
	pspIndexDropdownLabel: 'Índice PSP (obrigatório, selecione até 5)',
	removePSPIndexLabel: 'Remover índice PSP',
	assertionsLabel: 'Selecionar as assertivas relevantes',
	accountTypeOptions: AccountType,
	assertionOptions: assertions,
	executionTypeOptions: executionType,
	statementTypeOptions: statementTypes,
	noOptionsMessage: 'Nenhum resultado encontrado',
	accountNameErrorMsg: 'Obrigatório',
	pspIndexErrorMsg: 'Obrigatório',
	assertionWarningMessage: 'Você não pode fazer alterações em assertiva(s) que tenham um risco significativo, ou risco de fraude, ou risco de distorção relevante ou uma estimativa relacionada. Você precisa remover essas relações primeiro.',
	confirmChanges: 'Confirmar alterações',
	executionTypeWarningMessage: 'As alterações que você salvar nesta conta afetarão as Assertivas e PSPs existentes, os links serão removidos. Tem certeza de que deseja prosseguir? Essa ação não pode ser desfeita.',
	contentUpdateToastMessage: 'A atualização de conteúdo está disponível para {0}. Inicie a atualização de conteúdo na página Atualização de conteúdo.',
	assertionsRequired: 'Pelo menos uma assertiva deve ser selecionada',
	pspIndexDisabledLabel: 'Selecione até cinco índices PSP. Desmarque uma ou mais opções para continuar.'
};

export const createEditRiskModalLabels = {
	createModalDescription: "Insira os novos detalhes do risco abaixo e selecione'<b>{0}</b>' para finalizar. Para criar outro risco, selecione'<b>{1}</b>'.",
	editModalDescription: "Edite os detalhes do risco abaixo e selecione'<b>{0}</b>' para finalizar.",
	close: 'Fechar',
	cancel: 'Cancelar',
	createRisk: 'Novo risco',
	editRisk: 'Editar risco',
	riskType: 'Tipo de risco (obrigatório)',
	riskTypeOptions: [{
		value: 1,
		label: 'Risco Significativo'
	},
	{
		value: 2,
		label: 'Risco de fraude'
	},
	{
		value: 3,
		label: 'Risco de distorção relevante'
	}
	],
	save: 'Salvar',
	saveAndCloseLabel: 'Salvar e fechar',
	saveAndCreateLabel: 'Salvar e criar outro',
	riskNameLabel: 'Nome do risco (obrigatório)',
	relatedAccountsAssertionsLabel: 'Contas e assertivas relacionadas (opcional)',
	relateAccounts: 'Relacionar contas',
	assertionsLabel: 'Selecionar assertivas relevantes',
	riskNameErrorMsg: 'Obrigatório',
	riskTypeErrorMsg: 'Obrigatório',
	assertionOptions: assertions,
	removeAccountLabel: 'Remover conta',
	required: 'Obrigatório',
	assertionsRequired: 'Pelo menos uma assertiva deve ser selecionada'
};

export const CreateEditMestLabels = {
	createModalTitle: 'Nova entidade',
	createModalDescription: "Insira os detalhes da nova entidade abaixo e selecione' <b> {0}</b>' para finalizar. Para criar outra entidade, selecione' <b> {1}</b>'.",
	close: 'Fechar',
	cancel: 'Cancelar',
	save: 'Salvar',
	confirm: 'Confirm',
	primary: 'Primary',
	saveAndCloseLabel: 'Salvar e fechar',
	saveAndCreateLabel: 'Salvar e criar outro',
	entityNameLabel: 'Nome da entidade (obrigatório)',
	entityStandardIndexLabel: 'Índice padrão da entidade (obrigatório)',
	entityDescriptionLabel: 'Descrição da entidade',
	entityNameErrorMsg: 'Obrigatório',
	entityStandardIndexErrorMsg: 'Obrigatório',
	editModalTitle: 'Editar Entidade',
	editModalDescription: "Edite os detalhes da entidade abaixo e selecione' <b> {0}</b>' para finalizar.",
	primaryEntitySelectionLabel: 'Select as the primary entity',
	primaryEntitySelectionMsg: 'Only one entity in the engagement can be selected as the primary entity, which will be the determinant for the content delivered to the engagement. An entity will need to be selected as the primary to be able to submit the engagement profile. \'Update content\' permission is required to make or edit the primary entity selection.',
	primaryEntityDisableSelectionLabel: 'To change the primary entity designation, select from the \'Edit\' of the entity you wish to designate as primary',
	noAccessLabel: 'Unauthorized. Contact your administrator and try again.',
	primaryEntityConfirmationLabel: 'Primary entity confirmation',
	primaryEntityConfirmationDisplay: '{0} is currently selected as the primary entity. Are you sure you want to change the primary entity?',
	profileV2ChangeNotSubmittedBannerMessage: 'Changes have been made to the profile that will result in content updates. Submit the profile to receive the new content or revert the answers to the previous state.',
};

export const CreateEditITProcessLabels = {
	close: 'Fechar',
	cancel: 'Cancelar',
	yes: 'Sim',
	no: 'Não',
	delete: 'excluir',
	save: 'Salvar',
	saveAndCloseLabel: 'Salvar e fechar',
	saveAndCreateLabel: 'Salvar e criar outro',
	newITProcessLabel: 'Novo processo de TI',
	editITProcessLabel: 'Editar processo de TI',
	viewITProcessLabel: 'Visualizar o processo de TI',
	addDescriptionLabel: "Insira os detalhes do novo processo de TI abaixo e selecione' <b> {0}</b>' para finalizar. Para criar outro processo de TI, selecione' <b> {1}</b>'.",
	editDescriptionLabel: "Edite os detalhes do processo de TI abaixo e selecione' <b> {0}</b>' para finalizar.",
	iTProcessNameLabel: 'Nome do processo de TI (obrigatório)',
	confirm: 'Confirmar',
	confirmChanges: 'Confirmar',
	iTProcessNameErrorMsg: 'Obrigatório',
	inputInvaildCharacters: 'A entrada não pode incluir a seguinte sequência de caracteres: */:<>\\?|"',
	remove: 'Remover'
};

export const CreateEditITRiskLabels = {
	close: 'Fechar',
	cancel: 'Cancelar',
	yes: 'Sim',
	no: 'Não',
	delete: 'excluir',
	save: 'Salvar',
	saveAndCloseLabel: 'Salvar e fechar',
	saveAndCreateLabel: 'Salvar e criar outro',
	newITRiskLabel: 'Novo risco de tecnologia',
	editITRiskLabel: 'Editar risco de tecnologia',
	itRiskNameLabel: 'Risco de tecnologia (obrigatório)',
	confirm: 'Confirmar',
	confirmChanges: 'Confirmar',
	itRiskNameErrorMsg: 'Obrigatório',
	itProcessNotSelectedErrorMsg: 'Obrigatório',
	hasNoITGCLabel: 'Não há ITGCs que abordem o risco de tecnologia',
	editModalDescription: 'Editar a descrição do risco de tecnologia.',
	createModalDescription: 'Insira a descrição do risco de tecnologia.',
	selectITProcess: 'Selecione processo de TI (obrigatório)',
	noITProcessAvailable: 'Nenhum processo de TI criado',
	relatedITProcessLabel: 'Processo de TI relacionado',
	inputInvaildCharacters: 'A entrada não pode incluir a seguinte sequência de caracteres: */:<>\\?|"',
	remove: 'Remover'
};

export const CreateEditEstimateLabels = {
	createModalDescription: "Insira os detalhes da nova estimativa abaixo e selecione'<b>{0}</b>' para finalizar. Para criar outra estimativa, selecione'<b>{1}</b>'.",
	editModalDescription: "Edite os detalhes da estimativa abaixo e selecione'<b>{0}</b>' para finalizar.",
	close: 'Fechar',
	cancel: 'Cancelar',
	save: 'Salvar',
	saveAndCloseLabel: 'Salvar e fechar',
	saveAndCreateLabel: 'Salvar e criar outro',
	createModalTitle: 'Nova estimativa',
	editEstimateLabel: 'Editar estimativa',
	estimateNameLabel: 'Nome da estimativa (obrigatório)',
	analysisPeriodBalance: 'Saldo da data de análise (obrigatório)',
	analysisPeriodDate: 'Data da análise (obrigatório)',
	comparativePeriodBalance: 'Saldo de data comparativo (obrigatório)',
	comparativePeriodDate: 'Data comparativa (obrigatório)',
	estimateCategory: 'Categoria da estimativa (obrigatório)',
	confirm: 'Confirmar',
	estimateNameErrorMsg: 'Obrigatório',
	analysisPeriodBalanceErrorMsg: 'Requerido',
	analysisPeriodDateErrorMsg: 'Requerido',
	comparativePeriodBalanceErrorMsg: 'Requerido',
	comparativePeriodDateErrorMsg: 'Requerido',
	estimateCategoryErrorMsg: 'Obrigatório',
	remove: 'Excluir',
	balanceNOTApplicable: 'Saldos não aplicáveis',
	wtDetailPrefixForEstimate: '<p>Complete the estimate walkthrough task.</p>',

	riskLevelOptions: [{
		value: 4,
		label: 'Risco muito baixo'
	},
	{
		value: 5,
		label: 'Risco mais baixo'
	},
	{
		value: 6,
		label: 'Risco mais alto'
	},
	{
		value: 7,
		label: 'Não selecionado'
	}
	]
};

export const CreateEditControlLabels = {
	createModalTitle: 'Novo controle',
	editModalTitle: 'Editar controle',
	viewModalTitle: 'Visualizar o controle',
	close: 'Fechar',
	cancel: 'Cancelar',
	save: 'Salvar',
	saveAndCloseLabel: 'Salvar e fechar',
	saveAndCreateLabel: 'Salvar e criar outro',
	controlNameLabel: 'Nome do controle (obrigatório)',
	frequency: 'Frequência',
	controlType: 'Tipo de controle',
	frequencyTypeOptions: controlFrequencyType,
	controlTypeOptions: controlTypes,
	designEffectiveness: 'Efetividade do desenho',
	operatingEffectiveness: 'Efetividade operacional',
	testingLabel: 'Teste',
	lowerRiskLabel: 'Is control lower risk?',
	effective: 'Efetivo',
	ineffective: 'Inefetivo',
	yes: 'Sim',
	no: 'Não',
	required: 'Obrigatório',
	remove: 'Remover',
	noOptionsMessage: 'Nenhum resultado encontrado',
	disabledTabTooltipMessage: "Selecionar'Tipo de controle' como'Controle de aplicativos de TI' ou'Controle manual dependente de TI' para relacionar aplicativos de TI",
	itAppLabels: {
		tabLabel: 'aplicativos de TI',
		dropdownLabel: 'Relacionar aplicativos de TI',
		noRelatedItems: 'Nenhum aplicativo de TI relacionado',
		itApplications: 'Aplicativos de TI'
	},
	soLabels: {
		tabLabel: 'SOs',
		dropdownLabel: 'Relacionar serviços de organização',
		noRelatedItems: 'Nenhuma serviços de organização relacionados',
		serviceOrganizations: 'Serviços de organização'
	},
	controlNameErrorMsg: 'Obrigatório',
	createModalDescriptionLabel: "Insira os detalhes do novo controle abaixo e selecione'<b>{0}</b>' para finalizar. Para criar outro controle, selecione'<b>{1}</b>'.",
	editModalDescriptionLabel: "Edite os detalhes do controle abaixo e selecione'<b>{0}</b>' para finalizar.",
	viewModalDescriptionLabel: 'Visualizar o controle e os aplicativos de TI e serviços da organização relacionados.',
	wcgwLabel: 'WCGW'
};

export const ITApplicationTypeLabels = [{
	value: 1,
	label: 'Aplicativo/Ferramenta'
},
{
	value: 2,
	label: 'Banco de dados'
},
{
	value: 3,
	label: 'Sistema operacional'
},
{
	value: 4,
	label: 'Network'
},
{
	value: 6,
	label: 'Sem categoria'
}
];

export const CreateEditITApplicationLabels = {
	close: 'Fechar',
	cancel: 'Cancelar',
	yes: 'Sim',
	no: 'Não',
	delete: 'Excluir',
	save: 'Salvar',
	saveAndCloseLabel: 'Salvar e fechar',
	saveAndCreateLabel: 'Salvar e criar outro',
	newITApplicationLabel: 'Novo aplicativo de TI',
	editITApplicationLabel: 'Editar aplicativo de TI',
	iTApplicationNameLabel: 'Nome do aplicativo de TI',
	confirm: 'Confirmar',
	confirmChanges: 'Confirmar alterações',
	noOptionsMessage: 'Nenhum resultado encontrado',
	iTAppNameErrorMsg: 'Obrigatório',
	controls: 'Controles',
	substantive: 'Substantivo',
	remove: 'Excluir',
	iTApplicationStrategyLabel: 'Estratégia do aplicativo de TI',
	SCOTsLabel: 'Nomes das SCOT',
	StrategyLabel: 'Estratégia',
	ControlsLabel: 'Controles',
	ControlTypeLabel: 'Tipo',
	addDescriptionLabel: "Insira os novos detalhes do aplicativo de TI abaixo e selecione'<b>{0}</b>' para finalizar. Para criar outro aplicativo de TI, selecione'<b>{1}</b>'.",
	editDescriptionLabel: "Edite os detalhes do aplicativo de TI abaixo e selecione'<b>{0}</b>' para finalizar.",
	scotErrorMessage: 'A SCOT pode não estar não relacionada ao aplicativo de TI, uma vez que existem controles relacionados.',
	SCOTsLabels: {
		tabLabel: 'SCOTs',
		dropdownLabel: 'Relacionar SCOTs',
		noRelatedItems: 'Nenhuma SCOT relacionada'
	},
	ControlsLabels: {
		tabLabel: 'Controles',
		dropdownLabel: 'Relacionar controles',
		noRelatedItems: 'Nenhum controle relacionado'
	},
	strategyType: {
		1: 'Controles',
		2: 'Substantivo',
		3: 'Confiança',
		4: 'Não confiança'
	},
	controlType: {
		1: 'Aplicativo de TI',
		2: 'Manual Dependente de TI',
		3: 'Prevenção manual',
		4: 'Detecção manual'
	},
	technologyTypeOptions: ITApplicationTypeLabels,
	technologyType: 'Select technology type'
};

export const CreateEditSCOTLabels = {
	createModalTitle: 'Nova SCOT',
	editModalTitle: 'Editar SCOT',
	viewModalTitle: 'Ver SCOT',
	createModalDescription: "Insira os novos detalhes da SCOT abaixo e selecione'<b>{0}</b>' para finalizar. Para criar outra SCOT, selecione'<b>{1}</b>'.",
	editModalDescription: "Edite os detalhes da SCOT abaixo e selecione'<b>{0}</b>' para finalizar.",
	close: 'Fechar',
	cancel: 'Cancelar',
	save: 'Salvar',
	saveAndCloseLabel: 'Salvar e fechar',
	saveAndCreateLabel: 'Salvar e criar outro',
	scotNameLabel: 'Nome da SCOT (obrigatório)',
	scotStrategyLabel: 'Estratégia da SCOT',
	scotTypeLabel: 'Tipo da SCOT',
	hasEstimateLabel: 'Esta SCOT foi impactado por uma estimativa?',
	itAPPUsedLabel: 'Algum aplicativo de TI é usado?',
	routine: 'Rotineira',
	nonRoutine: 'Não rotineira',
	controls: 'Controles',
	substantive: 'Substantivo',
	yes: 'Sim',
	scotNameErrorMsg: 'Obrigatório',
	remove: 'Excluir',
	noOptionsMessage: 'Nenhum resultado encontrado',
	disabledTabTooltipMessage: "Selecione'Algum aplicativo de TI é usado?' para relacionar aplicações de TI",
	itAppLabels: {
		itApplications: 'Aplicativos de TI relacionados',
		tabLabel: 'Aplicativos de TI',
		dropdownLabel: 'Relacionar aplicativos de TI',
		noRelatedItems: 'Nenhum aplicativo de TI relacionado'
	},
	soLabels: {
		serviceOrganizations: 'Organizações de serviços relacionados',
		tabLabel: 'SOs',
		dropdownLabel: 'Relacionar organizações de serviços',
		noRelatedItems: 'Nenhuma organização de serviços relacionada'
	},
	wtDetailPrefix: '<p>Para todas as SCOTs rotineiras e não rotineiras e processos de divulgação significativos, confirmamos nossa compreensão a cada período realizando procedimentos de walkthrough. Além disso, para auditorias PCAOB, realizamos procedimentos de walkthrough das SCOTs de estimativa.<br/>Para todas as SCOTs, quando adotamos uma estratégia de confiança em controles, e para controles que abordam riscos significativos, confirmamos que os controles relevantes foram adequadamente desenhados e implementados. Confirmamos que nossa decisão de adotar uma estratégia de confiança em controles ainda é apropriada.<br/><br/>Concluímos que nossa documentação descreve com precisão a operação da SCOT e identificamos todos os WCGWs apropriados, incluindo riscos que surgem do uso de TI, e controles relevantes (quando aplicável).<br/><br/> Para SCOTs de estimativa, quando usamos uma estratégia apenas substantiva, determinamos se nossa compreensão da SCOT de estimativa é apropriada com base em nossos procedimentos substantivos.</p>',
};

export const ViewSampleItemLabels = {
	previous: 'Anterior',
	next: 'Próximo',
	sampleDateLabel: 'Data da amostra',
	attributesHeader: 'Atributos',
	statusHeader: 'Status',
	noAttributesLabel: 'Nenhum atributo disponível.',
	present: 'Presente',
	presentWithComments: 'Apresentar com comentários',
	notPresent: 'Não presente',
	notApplicatable: 'Não aplicável',
	naLabel: 'N/A',
	additionDocumentation: 'Documentação adicional',
	deleteSampleHeader: 'Excluir amostra',
	deleteSmapleDescription: 'Tem certeza de que deseja excluir a amostra selecionada? Esta ação não pode ser desfeita.',
	deleteSamplePreText: 'Descrição da amostra',
	relateTagModalTitle: 'Relacionar tags à amostra',
	relateTagModalDescription: "Relacione uma ou mais tags à amostra. Para adicionar uma nova tag, clique em <b>'Gerenciar tags'</b>. As associações de tags não serão arquivadas, mas as tags em si serão arquivadas para que estejam disponíveis para uso em rollforward.",
	relateTagTableHeader: 'Nome da tag',
	relateTagTableSubHeader: 'Grupo de tags',
	tagsCounter: '{0} Tags',
	tagCounter: '{0} Tag',
	relateTagGroupLabel: 'Grupo de tags',
	relateTagSearchPlaceholder: 'Pesquisar',
	relateTagClearSearch: 'Limpar',
	relateTagShowSelectedOnly: 'Mostrar apenas relacionados',
	manageTagsLabel: 'Gerenciar tags',
	addTag: 'Adicionar etiqueta',
	supportingDocumentationTitle: 'Documentação suporte',
	dropdownAll: 'Todos',
	noResultsLabel: 'Nenhum resultado encontrado',
	noDataLabel: 'Nenhum dado encontrado',
	attributeStatusModalTitle: 'Mark all as present',
	attributeStatusModalCancelButton: 'Cancelar',
	attributeStatusModalConfirmButton: 'Salvar',
	attributeStatusModalDescription: 'Tem certeza de que deseja marcar os atributos como presentes? Somente atributos que não possuem status selecionado serão marcados como presentes.',
	attributeModalDeleteErrorMessage: 'O status do atributo não pode ser atualizado. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.',
};

export const ShortRiskTypeForAres = {
	1: 'Significativo',
	2: 'Fraude',
	3: 'Inerente',
	4: 'Risco muito baixo',
	5: 'Risco mais baixo',
	6: 'Risco mais alto',
	7: 'Não selecionado'
};

export const RelateEstimateToAssertionLabels = {
	relateAccountsAndAssertions: 'Relacionar contas e assertivas',
	relateAccountsToEstimate: 'Relacionar contas à estimativa',
	accounts: 'Contas',
	designation: 'Designação',
	relatedAssertions: 'Assertivas relacionadas',
	accountNameField: 'Nomedaconta',
	accountTypeIdField: 'Iddotipodeconta',
	assertionsField: 'assertivas',
	executionTypeIdField: 'Iddotipodeexecução',
	notSelected: 'Não selecionado',
	pathField: 'caminho',
	noAccountsAvailable: 'Nenhuma conta disponível',
	noRelatedAccounts: 'Nenhuma conta relacionada à estimativa',
	accountId: 'ID da conta',
	remove: 'Remover'
};

//Send instructions switcher
export const sendIntructionsSwitcherLabels = {

	[sendInstructionsSwitcherIds.groupInstructions]: 'Instruções de grupo',
	[sendInstructionsSwitcherIds.groupRiskAssessment]: 'Avaliação de risco de grupo'
};

export const RelateEstimateToAccountLabels = {
	relatEstimatesToAccount: 'Relacionar estimativas à conta',
	showOnlyRelatedEstimates: 'Mostrar apenas estimativas relacionadas',
	noEstimatesResult: labels.noResultsFound,
	noEstimatesLabel: 'Nenhuma estimativa criada no engagement',
	estimateNameHeader: 'Nome da estimativa',
	relatedEstimateCounter: '{0} estimativa',
	relatedEstimatesCounter: '{0} estimativas',
	relatedAccount: 'Conta/divulgação',
	close: labels.close
};

export const RelateAccountsToEstimateLabels = {
	relateAccountsToEstimate: 'Relacionar contas à estimativa',
	showOnlyRelatedAccounts: 'Mostrar apenas contas relacionadas',
	noAccountsResult: labels.noResultsFound,
	noAccountsLabel: 'Nenhuma conta criada no engajamento',
	accountNameHeader: 'Nome da conta',
	relatedAccountCounter: '{0} conta',
	relatedAccountsCounter: '{0} contas',
	relatedEstimate: labels.estimate,
	close: labels.close
};

export const SupportingDocumentationLabels = {
	evidence: 'Evidência',
	priorPeriod: 'Período anterior',
	temporaryFiles: 'Arquivos temporários',
	externalDocuments: 'Documentos externos',
	addEvidenceBtn: 'Adicionar evidência',
	addTemporaryFilesBtn: 'Adicionar arquivo temporário',
	notes: 'Notas',
	signOffs: 'Assinaturas',
	name: 'Nome',
	supportingDocumentationTitle: 'Documentação suporte',
	temporaryFilesEmptyPlaceholder1: 'Nenhum documento temporário relacionado.',
	temporaryFilesEmptyPlaceholder2: 'Para relacionar um documento temporário clique em {addTemporaryFiles}.',
	evidencePlaceholderLine1: 'Nenhuma evidência relacionada.',
	evidencePlaceholderLine2: 'Para relacionar uma evidência, clique em {addEvidenceBtn}.',
	removeFromSample: 'Remover da amostra',
	unlink: 'Desvincular',
	retailControlEvidenceLabel: 'Mantivemos evidências de controle para apoiar nossos testes do(s) atributo(s) para este item de amostra específico?',
	removeEvidenceModalTitle: 'Remover evidências da amostra',
	removeEvidenceModalDesc: 'Tem certeza de que deseja remover todas as evidências desta amostra?',
	removeEvidenceErrorMessage: 'Esses documentos não estão mais disponíveis nesta amostra. Atualize a página e tente novamente. Entre em contato com o Help Desk se o erro persistir.'
}

export const deleteSampleItemAttributeModal = {
	modalDescription: 'Tem certeza de que deseja remover a seleção para este atributo? Documentação adicional será excluída.',
	modalTitle: 'Remover seleção',
	modalConfirmButton: 'Remover',
	modalCancelButton: 'Cancelar',
	additionalDocumentationLabel: 'Documentação adicional'
}
export const accountsFilterLabels = [{
	id: accountsFilter.allAccounts,
	label: 'Todas as contas',
	value: accountsFilter.allAccounts
},
{
	id: accountsFilter.accountsWithRelatedEstimates,
	label: 'Contas com estimativa relacionada',
	value: accountsFilter.accountsWithRelatedEstimates
},
{
	id: accountsFilter.accountsWithoutRelatedEstimates,
	label: 'Contas sem estimativa relacionada',
	value: accountsFilter.accountsWithoutRelatedEstimates
}
];

export const changeSampleItemAttributeModal = {
	modalDescription: 'Tem certeza de que deseja alterar a seleção deste atributo? Documentação adicional será excluída.',
	modalTitle: 'Alterar seleção',
	modalConfirmButton: 'Mudar',
	modalCancelButton: 'Cancelar'
}
export const scotsFilterLabels = [{
	id: scotsFilter.allScots,
	label: 'Todas as SCOTs',
	value: scotsFilter.allScots
},
{
	id: scotsFilter.scotsWithRelatedEstimates,
	label: 'SCOTs com estimativa relacionada',
	value: scotsFilter.scotsWithRelatedEstimates
},
{
	id: scotsFilter.scotsWithoutRelatedEstimates,
	label: 'SCOTs sem estimativa relacionada',
	value: scotsFilter.scotsWithoutRelatedEstimates
}
];

export const CreateEditTagGroupLabels = {
	createModalTitle: 'Novo grupo de tags de amostra',
	editModalTitle: 'Editar grupo de tags de amostra',
	createModalDescription: "Insira os detalhes do grupo de tags abaixo e selecione <b>\'Salvar e fechar\'</b> para finalizar. Para criar outro grupo de tags, selecione <b>\'Salvar e criar outro\'</b>.",
	editModalDescription: "Edite os detalhes do grupo de tags abaixo e selecione'<b>{0}</b>' para finalizar.",
	close: 'Fechar',
	cancel: 'Cancelar',
	save: 'Salvar',
	saveAndCloseLabel: 'Salvar e fechar',
	saveAndCreateLabel: 'Salve e crie outro',
	tagGroupNameLabel: 'Nome do grupo de tags (obrigatório)',
	required: 'Obrigatório'
};

export const CreateEditTagLabels = {
	createModalTitle: 'Nova tag de amostra',
	editModalTitle: 'Edit sample tag',
	createModalDescription: "Insira os detalhes da tag abaixo e selecione'<b>Salvar e fechar</b>' para finalizar. Para criar outra tag, selecione'<b>Salvar e criar outra</b>'.",
	editModalDescription: `Edit the tag details below and select'<b>{0}</b>' to finish.`,
	tagNameLabel: 'Nome da tag (obrigatório)',
	tagGroupNameLabel: 'Nome do grupo de tags (obrigatório)',
	tagColorLabel: 'Cor (obrigatório)',
	saveAndCloseLabel: 'Salvar e fechar',
	saveAndCreateLabel: 'Salve e crie outro',
	cancelLabel: 'Cancelar',
	required: 'Obrigatório',
	save: 'Salvar',
	noresultsLabel: 'Nenhum grupo de tags disponível',
	tagColors: [{
		text: 'Vermelho',
		color: 'vermelho'
	},
	{
		text: 'Laranja',
		color: 'laranja'
	},
	{
		text: 'Verde-água',
		color: 'verde-água'
	},
	{
		text: 'Azul',
		color: 'azul'
	},
	{
		text: 'Roxo',
		color: 'roxo'
	},
	{
		text: 'Verde',
		color: 'verde'
	}
	]
};

export const ITProcessFlowLabels = {
	itProcess: 'Processo de TI',
	technology: 'Tecnologia',
	technologies: 'Tecnologias',
	technologyName: 'Nome da tecnologia',
	supportingTechnologyName: 'Nome do suporte de tecnologia',
	technologyType: 'Tipo de tecnologia',
	showOnlyRelated: 'Mostrar apenas relacionados',
	technologiesCounter: '{0} Tecnologias',
	technologyCounter: '{0} Tecnologia',
	supportingTechnologyLabel: 'Tecnologia de suporte',
	relatedITAppNoDataPlaceholder: 'Nenhuma tecnologia relacionada ao processo de TI',
	relateTechnology: 'Relacionar tecnologia',
	supportingITPAppNoDataPlaceholder: 'Nenhuma tecnologia de suporte ao processo de TI',
	itRiskHeader: 'Riscos de TI',
	itgcHeader: 'ITGCs',
	itspHeader: 'ITSPs',
	noDataPlaceholderITGC: 'Pelo menos um ITGC deve ser identificado ou uma designação de que o risco de TI não possui ITGCs. Crie {createNewITGC}, {relateITGC} ou indique que não há {noITGC} que abordem o risco de TI.',
	noDataPlaceholderITSP: 'Se avaliarmos os ITGCs como ineficazes ou determinarmos que não existem ITGCs para abordar o risco de TI, podemos realizar procedimentos de teste substantivo de TI (ITSPs) para obter uma garantia razoável de que o risco de TI dentro do processo de TI associado ao ITGC ineficaz não foi explorado. Crie {createNewITSP} ou {relateITSP}.',
	noRecordsFound: 'Nenhum risco de TI identificado para este processo de TI',
	noITGCPlaceholder: 'Não há ITGCs que abordem o risco de TI.',
	relateSupportingTechnology: 'Relacionar tecnologia de suporte',
	relatedTechnologiesNotAvailable: 'Tecnologias relacionadas não disponíveis para este documento',
	supportingTechNotAvailable: 'Tecnologias de suporte não disponíveis para este documento',
	relatedITRisksNotAvailable: 'Riscos de TI relacionados não disponíveis para este documento',
	relateITGC: 'Relate ITGCs',
	itRisk: 'Risco de TI',
	itgcCounter: '{0} ITGC',
	itgcsCounter: '{0} ITGCs',
	itgcName: 'Nome do ITGC',

};

export const ITRisksFlowLabels = {
	itRisk: 'Risco de TI',
	relatedITRiskNoDataPlaceholder: 'Nenhum risco de TI relacionado ao processo de TI',
	newITRisk: 'Novo risco de TI',
	relatedITRisksNotAvailable: 'Riscos de TI relacionados não disponíveis para este documento',
	deleteConfirmLabel: 'Você tem certeza de que deseja excluir o risco de TI selecionado? Esta ação não pode ser desfeita.',
	deleteITRisk: 'Excluir risco de TI',
	CreateITFlowITRiskLabels: {
		close: 'Fechar',
		cancel: 'Cancelar',
		yes: 'Sim',
		no: 'Não',
		delete: 'excluir',
		save: 'Salvar',
		saveAndCloseLabel: 'Salvar e fechar',
		saveAndCreateLabel: 'Salvar e criar outro',
		newITRiskLabel: 'Novo risco de TI',
		itRiskNameLabel: 'Nome do risco de TI (obrigatório)',
		itRiskCharactersLabel: 'caracteres',
		itRiskOfLabel: 'de',
		confirm: 'Confirmar',
		confirmChanges: 'Confirmar',
		itRiskNameErrorMsg: 'Obrigatório',
		itProcessNotSelectedErrorMsg: 'Obrigatório',
		hasNoITGCLabel: 'Não há ITGCs que abordem o risco de TI',
		createModalDescription: "Insirir os detalhes do risco de TI abaixo e selecione'<b>Salvar e fechar</b>' para finalizar. Para criar outro risco de TI, selecione'<b>Salvar e criar outro</b>'.",
		relatedITProcessLabel: 'Processo de TI',
		inputInvaildCharacters: 'A entrada não pode incluir a seguinte sequência de caracteres: */:<>\?|"',
		remove: 'Remove',
		editModalDescription: "Edite os detalhes do risco de TI abaixo e selecione'<b>Salvar</b>' para finalizar.",
		editITRiskLabel: 'Edit IT risk'
	}
};

export const ITProcessListingLabels = {
	all: 'Todos',
	manageChange: 'Gerenciar mudanças',
	manageAccess: 'Gerenciar acesso',
	manageSecuritySettings: 'Gerenciar configurações de segurança',
	itOperations: 'Operações de TI',
	systemImplementation: 'Implementação de sistema',
	category: 'Categoria',
	uncategorized: 'Sem categoria',
	technologies: 'Tecnologias',
};

export const ITProcessQuickFilterOptions = {
	0: ITProcessListingLabels.all,
	1: ITProcessListingLabels.manageChange,
	2: ITProcessListingLabels.manageAccess,
	3: ITProcessListingLabels.manageSecuritySettings,
	4: ITProcessListingLabels.itOperations,
	5: ITProcessListingLabels.systemImplementation
}

// Relate Technology Modal
export const RelateTechnologyModalLabels = {
	relateTechnology: 'Relacionar tecnologia',
	relatedTechnologiesDescription: 'Nome da tecnologia',
	supportingTechnologyName: 'Nome do suporte de tecnologia',
	technology: '{0} technology',
	technologies: '{0} technologies'
};

export const AccountStandardROMMListingLabels = {
	accountRisksNotAvailableForDocument: 'Os riscos da conta não estão disponíveis para este documento.',
	noRelatedObject: 'Nenhum objeto relacionado. Relacione um objeto para começar.',
	noResultsFound: 'Nenhum risco disponível.',
	acceptedText: 'Aceito',
	rejectedText: 'Rejeitado',
	allRisksRejected: 'Todos os riscos foram rejeitados',
	relevantAssertions: 'Assertivas relevantes',
	rejectLabel: 'Rejeitar',
	acceptLabel: 'Aceitar',
	rejectionRationaleLabel: 'Justificativa de rejeição',
	rejectionCategoryText: 'Categoria de rejeição',
	editRejectionRationaleText: 'Editar justificativa de rejeição',
	rejectionRationalePlaceholder: "Are you sure you want to reject the selected risk? Enter the details below and select <strong>\'Reject\'</strong>.",
	cancel: 'Cancel',
	rejectionRationaleTextAreaPlaceholder: 'Rationale (required)',
	rejectionCategoryDropdownPlaceholder: 'Rejection category (required)',
	required: 'Required',
	preRejectedText: 'Pre-rejected',
	additionalContextLabel: 'Additional context',
	additionalContextwhyRiskShouldBeRejected: 'Click {0} to add additional context specific to this client for why this risk should be rejected.',
	hereLink: 'here',
	editAdditionalContextText: 'Edit additional context'
}

export const RejectionCategory = [{
	id: 1,
	label: 'Editar justificativa de rejeição'
},
{
	id: 2,
	label: 'Editar justificativa de rejeição'
},
{
	id: 3,
	label: 'Editar justificativa de rejeição'
},
{
	id: 4,
	label: 'Editar justificativa de rejeição'
},
]

export const formLabels = {
	required: labels.required,
	maxLength: labels.maxLength
};

export const paginationLabels = {
	show: labels.pagingShowtext,
	first: 'Primeira página',
	last: 'Última página',
	prev: 'Página anterior',
	next: 'Próxima página'
};
