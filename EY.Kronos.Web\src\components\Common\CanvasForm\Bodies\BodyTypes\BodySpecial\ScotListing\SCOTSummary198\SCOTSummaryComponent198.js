import React, {useState, useRef, useLayoutEffect} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import styled from 'styled-components';
import {useEngagementId} from '../../../../../../../../util/customHooks';
import SCOTSummaryDetailComponent198 from './SCOTSummaryDetailComponent198';
import Loader from '../../../../../../LoadingIndicator/LoadingIndicator';
import {clientSideEventNames} from '../../../../../../../../util/uiconstants';
import {sortScots, sortAccounts, getAllScotsData, getAllAccounts, getScotSummaryData} from './SCOTSummaryUtils198';
import SCOTSummaryManageEntity198 from './SCOTSummaryManageEntity198';

export default function SCOTSummaryComponent198() {
	const dispatch = useDispatch();
	const allScotsEstimates = useSelector((state) => state.scots.byId);
	const allAccounts = useSelector((state) => state.accounts.byId);
	const engagementId = useEngagementId();
	const isDataRefreshed = useRef(false);

	const [isLoading, setIsLoading] = useState(false);
	const [scotSummaryData, setScotSummaryData] = useState(null);
	const [isApiCallsComplete, setIsApiCallsComplete] = useState(false);

	useLayoutEffect(() => {
		getAllScotsAndAccounts();
	}, []);

	useLayoutEffect(() => {
		if (isApiCallsComplete) {
			setIsApiCallsComplete(false);
			const scotAccountData = getScotSummaryData(sortScots(allScotsEstimates), sortAccounts(allAccounts));
			setScotSummaryData(scotAccountData);
		}
	}, [isApiCallsComplete]);

	async function getAllScotsAndAccounts(isSilentRefresh = false) {
		{
			!isSilentRefresh && setIsLoading(true);
		}
		const promiseArray = [getAllScotsData(engagementId, dispatch), getAllAccounts(engagementId, dispatch)];

		await Promise.allSettled(promiseArray)
			.then(() => {
				setIsApiCallsComplete(true);
			})
			.finally(() => {
				window.dispatchEvent(new CustomEvent(clientSideEventNames.getValidations));
				{
					!isSilentRefresh && setIsLoading(false);
				}
			});
	}

	function refreshPage() {
		isDataRefreshed.current = true;
		getAllScotsAndAccounts(true);
	}

	return (
		<Wrapper>
			<SCOTSummaryManageEntity198 reloadContent={refreshPage} />
			{isLoading && (
				<StyledLoader className="loader">
					<Loader view="inlineView" hideLabel />
				</StyledLoader>
			)}
			{scotSummaryData && (
				<SCOTSummaryDetailComponent198
					scotAccountSummaryData={scotSummaryData}
					reloadContent={isDataRefreshed.current}
				/>
			)}
		</Wrapper>
	);
}

const Wrapper = styled.section`
	.placeholder {
		width: 100%;
		display: flex;
		border: var(--border-width-small) solid var(--neutrals-200);
		border-radius: var(--px-8);
		background: var(--neutrals-00white);
		padding: var(--px-80) 0;
		justify-content: center;
		color: var(--neutrals-700);
	}
`;

const StyledLoader = styled.section`
	background: var(--neutrals-00white);
	width: 100%;
	height: var(--px-500);
	position: relative;
	z-index: 999;
	opacity: 0.5;
	top: var(--px-0);
	pointer-events: none;
	justify-content: center;
	align-items: center;
	display: flex;
`;
