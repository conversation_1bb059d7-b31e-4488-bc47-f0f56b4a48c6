import _ from 'lodash';
import * as types from '../../../actions/actiontypes';
import initialstate from '../../initialstate';
import {
	isApiResponseOk,
	normalize,
	normalizedDelete,
	normalizedUpdate,
	truthyWinsMergeStrategy
} from '../../../util/utils';
import {HttpStatusCodes} from '../../../util/uiconstants';

const deleteEntityFromStore = (entityId, state) => {
	let newState = {...state};

	// Remove the entity from both manageEntities and root slices:
	if (newState.manageEntities) {
		newState.manageEntities = normalizedDelete(newState.manageEntities, Number(entityId));
	}

	newState = normalizedDelete(newState, Number(entityId));

	return newState;
};

export function stEntitiesReducer(state = initialstate.stEntities, action) {
	let newState;

	switch (action.type) {
		case types.GET_STENTITY_BY_ID: {
			if (action.error?.response?.status === HttpStatusCodes.NotFound) {
				return deleteEntityFromStore(action.multiEntityId, state);
			}
			if (isApiResponseOk(action)) {
				newState = {...state};

				if (action.isManageEntities) {
					newState.manageEntities = normalize([action.response], undefined, newState.manageEntities);
				} else {
					newState = normalize([action.response], undefined, newState);
				}

				return newState;
			}

			return state;
		}
		case types.CREATE_STENTITY: {
			if (isApiResponseOk(action)) {
				const newEntity = action.response;

				newState = { ...state };
				if (action.isManageEntities) {
					newState.manageEntities = normalize([newEntity], undefined, newState.manageEntities);
				} else {
					newState = normalize([newEntity], undefined, newState);
				}

				if (newEntity.isPrimary) {
					newState.primaryEntity = newEntity;
				}
				return newState;
			}
			return state;
		}

		case types.UPDATE_STENTITY: {
			if (action.error && action.error.response.status === HttpStatusCodes.NotFound) {
				return deleteEntityFromStore(action.meta.api.data.id, state);
			}
			if (isApiResponseOk(action)) {
				const responseEntity = action.response;
				newState = {...state};

				// Update entity in both manageEntities and root slices
				if (newState.manageEntities) {
					newState.manageEntities = normalizedUpdate(responseEntity.id, responseEntity, newState.manageEntities);
				}

				newState = normalizedUpdate(responseEntity.id, responseEntity, newState);
				if (responseEntity.isPrimary) {
					newState.primaryEntity = responseEntity;
				}

				return newState;
			}

			return state;
		}
		case types.GET_ALL_MULTI_ENTITY:
		case types.GET_ALL_ENGAGEMENT_ENTITIES: {
			if (isApiResponseOk(action)) {
				newState = { ...state };
				let primaryEntity = null;
				if (action?.meta?.api?.url?.includes('pageSize=-1') || action?.meta?.api?.url?.includes('page=1')) {
					primaryEntity = action?.response?.data?.find(entity => entity?.isPrimary);
				}
				if (action.isManageEntities) {
					newState.manageEntities = normalize(
						action.response.data,
						undefined,
						newState.manageEntities,
						undefined,
						true
					);
				} else {
					newState = {
						...normalize(action.response.data, undefined, newState, undefined, true),
						pagingOptions: {
							totalEntityCount: action.response.totalEntityCount,
							pages: action.response.pages,
							page: action.response.page
						},
						primaryEntity: primaryEntity || newState.primaryEntity
					};

					const mergeSTEntities = (newById, currentById) => {
						const byId = { ...currentById };
						for (const key in newById) {
							byId[key] = { ...currentById?.[key], ...newById[key] };
						}
						return byId;
					};

					const currentState = {...state};
					if (currentState && typeof currentState === 'object' && Object.keys(currentState).length > 0) {
						newState = {
							...newState,
							byId: mergeSTEntities(newState.byId || {}, currentState.byId || {}),
						}
					}
				}

				return newState;
			}
			return state;
		}
		case types.ARES_GET_DOCUMENT_BY_ID: {
			if (isApiResponseOk(action)) {
				if (action.response.entities?.length > 0) {
					return {
						...state,
						...normalize(action.response.entities, undefined, state)
					};
				}
			}
			return state;
		}
		case types.GET_ALL_DOCUMENTS: {
			if (isApiResponseOk(action)) {
				const documentsResponse = action.response.data;
				let entities = {};

				// Get all unique entities related to each document
				for (const doc of documentsResponse) {
					const documentEntities = doc?.entities || [];

					for (const entity of documentEntities) {
						if (!entities[entity.id]) {
							entities[entity.id] = entity;
						}
					}
				}

				if (!_.isEmpty(entities)) {
					return {
						...state,
						...normalize(Object.values(entities), undefined, state)
					};
				}
			}
			return state;
		}
		case types.DELETE_MULTI_ENTITY: {
			if (isApiResponseOk(action) || action.error?.response?.status === HttpStatusCodes.NotFound) {
				return deleteEntityFromStore(action.stEntityId, state);
			}

			return state;
		}
		case types.GET_MULTI_ENTITY_BY_ID: {
			if (action.error?.response?.status === HttpStatusCodes.NotFound) {
				return deleteEntityFromStore(action.multiEntityId, state);
			}
			return state;
		}
		default: {
			return state;
		}
	}
}

export function multiEntityReducer(state = initialstate.multiEntity, action) {
	switch (action.type) {
		case types.GET_MULTI_ENTITY_BY_ID: {
			if (isApiResponseOk(action)) {
				return action.response || state;
			}
			return state;
		}
		case types.CLEAR_MULTI_ENTITY:
			return {};
		default: {
			return state;
		}
	}
}

export function entityLayersReducer(state = initialstate.entityLayers, action) {
	switch (action.type) {
		case types.GET_ENTITY_LAYERS: {
			if (!action) {
				return state;
			}
			if (action.response) {
				return action.response;
			}
			return state;
		}
		case types.CLEAR_ENTITY_LAYERS: {
			return {};
		}
		default:
			return state;
	}
}
