/* eslint-disable prettier/prettier,max-len */

import {
	Entity,
	GALinkStatus,
	confidentialityTypes,
	currencyType,
	gaRegion,
	gaRoleTypes,
	gaScopeType,
	notesFilter,
	pointOfContactTypes,
	validationTypes,
	KnowledgeSectionIds,
	sendInstructionsSwitcherIds,
	accountsFilter,
	scotsFilter,
	rejectionType
} from '../util/uiconstants';

/**
 * Created by calhosh on 4/14/2017.
 * KO resource file
 */
export const labels = {
	addEvidenceBtn: '증거 추가',
	multipleDocuments: '복수 문서',
	invalidEngagementId: 'Engagement ID가 유효하지 않습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	newComponent: '신규 부문',
	workingOffline: '오프라인 작업',
	syncInProgress: '동기화 진행 중',
	prepareOffline: '오프라인 데이터 준비',
	connectionAvailable: '연결 가능',
	training: '교육',
	clickForOptions: '옵션을 더 보려면 클릭하십시오',
	navReviewNotes: '리뷰노트',
	navHeaderManageTeam: '팀 관리',
	navManageGroup: '그룹 관리',
	manageObjects: 'Object 관리',
	navCRASummary: 'CRA 요약',
	navAuditPlan: '감사 계획',
	navWorkPlan: '작업 계획',
	navSEM: '실증절차평가표',
	navFindings: '발견사항',
	navContentUpdates: '내용 업데이트',
	navCanvasFormUpdates: 'Canvas 양식 업데이트',
	navCopyHub: '복사 허브',
	navCopyHubNew: '복사 허브 NEW',
	navArchiveChecklist: 'Archive 체크리스트',
	navExportHub: '내보내기 허브',
	navReporting: '보고',
	navHelp: '일반 도움말',
	validationNavHelp: '유효성 검증 도움말',
	leaveUsFeedback: '피드백을 남겨 주십시오',
	navDashboard: '대시보드',
	tasksPage: 'Task',
	documentsPage: '문서',
	collaborationPage: '공동작업',
	automationPage: '자동화',
	documentHelperConnectionIssue: 'EY Canvas Document Helper에 문제가 발견됐습니다. 문제 해결 지침을 보려면 <a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">here</a>을(를) 클릭하십시오.',
	noContentAvailable: '사용 가능한 내용이 없습니다',
	noSectionsAvailable: '사용 가능한 섹션이 없습니다',
	noInformationAvailable: '사용 가능한 정보 없음',
	collapse: '축소',
	expand: '확장',
	duplicate: '복제',
	duplicateSection: '섹션 복제',
	duplicateSectionHeader: '선택한 섹션을 복제하시겠습니까?',
	deleteSection: '섹션 삭제',
	deleteSectionHeader: '선택한 섹션을 삭제하시겠습니까?',
	deleteHeader: '머리글 삭제',
	deleteHeaderTitle: '선택한 머리글을 삭제하시겠습니까?',
	confirmLabel: '확인',
	custom: '사용자지정',
	selectHeader: '머리글 선택',
	selectSection: '섹션 선택',
	noResultsFound: '결과를 찾을 수 없습니다',
	scot: 'SCOT',
	scotTypes: 'SCOT 유형',
	frequency: '빈도',
	SelectFrequency: '빈도 선택',
	SelectControlType: '통제 유형 선택',
	itBadge: 'IT',
	soBadge: 'SO',
	noRecordsAvailable: '사용 가능한 기록이 없습니다',
	noIncompleteResponseSummaryView: '미완료 응답 없음',
	noUnresolvedCommentsSummaryView: '미해결 comment 없음',
	edit: '편집',
	editForm: '양식 편집',
	editControl: '통제 편집',
	delete: '삭제',
	remove: '제거',
	noBodies: '사용 가능한 본문이 없습니다',
	relateDocuments: '문서 연결',
	relatedDocuments: '연결된 문서',
	deleteBody: '본문 삭제',
	bodyDescription: '선택한 본문을 삭제하시겠습니까?',
	description: '설명',
	maxLengthForEditResponse: '본문 텍스트가 최대 허용 길이를 초과했습니다',
	maxLengthForEditResponseWithCount: '응답에 {#}자가 포함되어 최대치 {##}자를 초과합니다. 텍스트 또는 서식을 줄여 답변을 조정한 후 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	saveResponse: '변경사항을 폐기하면 응답 텍스트 편집 내용은 저장되지 않습니다. 닫히거나 삭제된 comment는 강조된 채로 남습니다. 변경사항을 모두 폐기할지 확인하십시오.',
	discardChangesModalText: '변경사항 폐기',
	seeBodyDescriptionText: '설명 보기',
	hideBodyDescriptionText: '설명 숨기기',
	showBodyDescriptionText: '설명 표시',
	okLabel: '확인',
	addButtonLabel: '추가',
	addEvidence: '증거 추가',
	addTemporaryFiles: '임시 파일 추가',
	notemporaryDocs: '사용 가능한 임시 파일 없음',
	relateFiles: '파일 연결',
	uploadFiles: '파일 업로드',
	cancelButtonLabel: '취소',
	clickEditResponseToContinue: '계속하려면 편집 아이콘을 클릭하십시오',
	editResponse: '응답 편집',
	save: '저장',
	numericValuePlaceholder: '수치 입력',
	saveLabel: '저장',
	cancelLabel: '취소',
	closeLabel: '닫기',
	editText: '편집',
	select: '선택',
	selectScot: 'SCOT 선택',
	clearHoverText: '처리',
	optional: '(선택 사항)',
	nodocumentsAdded: '사용 가능한 문서가 없습니다',
	errorBanner: '{0}개 오류',
	NetworkErrorMessage: '응용프로그램 네트워크에 오류가 발생했습니다. 페이지를 새로 고침하고 나중에 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	of: 'of',
	characters: 'characters',
	show: '표시: ',
	views: '보기: ',
	primaryRelated: 'Primary 관련 Canvas 양식',
	secondaryRelated: 'Secondary 관련 Canvas 양식',
	singleLineValuePlaceholder: '텍스트 입력',
	paceInputPlaceholder: 'PACE ID',
	multiLineValuePlaceholder: '텍스트 입력',
	riskFactorDescribe: '설명',
	riskFactorLabel: '관련 사건 및 상황/왜곡표시위험',
	riskFactorEmptyWarning: '사건 및 상황/왜곡표시위험 설명 누락',
	riskFactorNoDescription: '신규 관련 사건 및 상황/왜곡표시위험을 생성하거나 기존 관련 사건 및 상황/왜곡표시 위험을 선택하십시오',
	fraudRiskTagMessage: '이 관련 사건 및 상황/왜곡표시위험은 항상 부정위험을 초래하거나, 해당하는 경우, 중요왜곡표시위험 아님으로 지정되어야 합니다',
	significantRiskTagMessage: '이 관련 사건 및 상황/왜곡표시위험은 항상 유의적인 위험, 부정위험을 초래하거나, 해당하는 경우, 중요왜곡표시위험 아님으로 지정되어야 합니다',
	on: 'on',
	sliderDeSelectMessage: '동그라미를 끌어다 값을 배정하십시오',
	yearPlaceholder: 'YYYY',
	dayPlaceholder: 'DD',
	monthPlaceholder: 'MM',
	amLabel: 'AM',
	pmLabel: 'PM',
	relatedEntities: '연결된 기업',
	eyServiceGateway: 'EY Service Gateway',
	eyAutomation: 'EY Automation',
	eyserviceGatewayAutomation: 'EY Service Gateway & Automation',
	creating: 'Creating...',
	cannotCreateUdp: 'Cannot create UDP. Time Phase cannot be empty',

	// 440GL
	rrdReminderTitle: 'RAS reminder',
	rrdReminderMessage: '보고서 발행일은 {rrdDescription}입니다. {rrdPendingDays} RAS에 서명하는 것을 잊지 마십시오..',
	rrdReminderPendingDays: '{0}일 후',

	//Create or Associate risks
	createAssociateRisksLabel: '신규 위험 생성 또는 위험 연결',
	relatedRiskIsMissingWarning: '연결된 위험이 없습니다',
	associateRiskDescription: '하나 이상의 위험을 선택하거나 신규 위험을 생성하여 질문 응답에 연결하십시오',
	createNewRiskLabel: '신규 위험 생성',
	noRiskIdentifiedLabel: '식별된 위험 없음',

	// GuidanceModal
	eyAtlasLink: 'EY Atlas',
	guidanceHeaderMessage: 'This module contains',
	guidanceModalHeader: 'Guidance',
	guidanceModalLabelText: 'Entry',
	guidanceFooter: 'for more information.',
	guidanceSeveralEntriesText: 'several entries: ',
	guidanceVisitText: 'Visit',
	guidanceClickText: 'Click',
	guidanceHereText: 'here',
	guidanceFooterText: 'for more information.',
	analyticsInconsistencies: '분석을 검토할 때, 기대치와 모순되는 변동이나 활동이 있는지 고려하십시오. 이러한 상황은 신규 위험, 별도의 거래유형, SCOT의 변동 또는 경영진에 의한 통제무력화 위험 징후일 수 있습니다.',
	analyticsInconsistenciesOSJE: 'OJSE는 분석 중인 선택 계정의 상대계정을 나타냅니다. 우리는 새롭거나 비경상적인 계정 조합을 찾습니다.',
	analyticsInconsistenciesActivityBySource: '원천별 활동은 선택한 계정의 총 월간 활동 및 관련 원천을 나타냅니다. 우리는 비경상적인 활동, 원천의 변동 또는 특정 원천에서 변동된 활동량에 집중합니다.',
	analyticsInconsistenciesPreparerAnalysis: '작성자 분석에는 선택한 계정에 기록한 작성자별 총 활동이 전기와 비교하여 요약됩니다. 우리는 작성자의 변동 또는 그들의 월권행위에 집중합니다.',
	analyticsInconsistenciesAccountMetrics: '계정 지표에는 계정 지정에 도움이 되는 주요 계정 정보가 요약됩니다',
	analyticsLoadingIsInProgress: '요청된 분석이 아직 로드 중입니다. 완료 후 탭에 접근할 수 있습니다.',

	aboutDescriptioin: 'GAM 단계, 기준 또는 언어를 편집합니다. 편집하면 내용이 업데이트됩니다.',
	aboutContentDescription: 'Edit the content layers, standards, language, or content driving entity. Edits will trigger a content update.',
	about: 'About',
	formType: '양식 유형',
	gamLayer: 'GAM 단계',
	contentLayer: '내용 단계',
	standard: '기준',
	language: '언어',
	createdBy: '생성한 사람',
	createdOn: '생성일',
	contentLastUpdatedBy: '최근 내용 업데이트한 사람',
	contentLastUpdatedOn: '최근 내용 업데이트일',
	notModified: '수정되지 않음',

	rolesInsufficientTooltip: '내용을 편집할 권한이 없습니다. Engagement 관리자와 협의하여 충분한 권한을 얻으십시오.',
	knowledgeFormToolTip: "Knowledge delivered documents cannot be updated. Update the Engagement Profile to change this form's profile.",
	selectTeamMember: '이름 또는 이메일',

	// SeeMore component
	showMore: '더 표시',
	showLess: '덜 표시',
	showMoreEllipsis: '더 표시…',
	showLessEllipsis: '덜 표시…',

	relatedITapplicationSOs: '연결된 IT application/SO',
	aggregateITevaluations: 'Aggregate IT Evaluation',
	lowerRisk: '낮은 위험',
	controlLowerRisk: '통제는 위험이 낮습니다',
	relatedITApplication: '연결된 IT Application',
	relatedITSO: '연결된 SO',
	noITApplicationUsed: '사용된 IT application 없음',

	notSel: '선택되지 않음',
	internal: '내부',
	external: '외부',
	notSelected: '미선택',
	noOptionSelected: '미선택',
	tod: 'TOD',
	sap: 'SAP',
	int: 'INT',
	ext: 'EXT',
	toc: 'TOC',

	placeholderForSearch: '검색',
	source: '원천',
	nature: '성격',
	testOfDetail: '세부 테스트',
	testOfControl: '통제테스트',
	substantiveAnalyticalProcedure: '실증적 분석절차',
	expandScot: '1. SCOT을 확장합니다',
	selectWCGWToDisplayTheResponsiveTask: '2. 중요왜곡표시위험을 선택하여 대응하는 task를 표시합니다',
	tasksWithNoRelatedWCGW: '중요왜곡표시위험이 연결되지 않은 task',
	noTasksAvailable: '사용 가능한 task가 없습니다',
	noWCGWAvailableForTask: '사용 가능한 ROMM이 없습니다',
	noSubstantiveTasksAvailable: '연결된 실증 task가 없습니다',
	selectAssertionToRelateWCGW: '위험을 WCGW과 연결하려면 경영진주장을 선택하십시오',
	significantAccounts: '유의적인 계정',
	riskName: '위험: ',
	accountName: '계정: ',
	control: '통제',
	controls: '통제',
	noScotsFound: '연결된 SCOT이 없습니다',
	relatedwcgw: '연결된 WCGW',
	relatedRisks: '연결된 위험: ',
	boltIconTitle: '연결된 위험',
	relatedITApp: '연결된 IT App/SO',
	instructions: '지침: ',
	expandRisk: '1. 위험을 확장합니다',
	selectAssertion: '2. 경영진주장을 선택합니다',
	identifyRelatedWCGW: '3. 위험에 연결된 WCGW을 식별합니다',
	clickAccount: '1. 계정을 클릭합니다',
	selectWCGW: '2. WCGW을 선택합니다',
	identifyRelatedTask: '3. WCGW에 대응하는 task를 식별합니다',
	information: '정보',
	requiredAssertions: '필수 경영진주장',
	wcgwWithoutTasks: 'Task 없는 WCGW',
	rommAssociatedWNotRelyAssertion: 'ROMM은 계정에 추정이 있는 경우 의존하지 않는 통제위험이 있는 경영진주장이나 높은 고유위험이 있는 경영진주장에 연결됩니다',
	hasRiskAssociated: '위험 연결됨',
	clearSelections: '모두 선택 해제',
	romm: 'ROMM',
	riskOfMaterialMisstatementsWithoutRelatedTask: 'Task가 연결되지 않은 중요왜곡표시위험',
	selectOneOrMoreTasksToSeeTheRelatedROMM: '연결된 ROMM을 보려면 task를 하나 이상 선택하십시오',
	invalidRelatedEntity: '연결된 계정을 찾을 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	noResultsAvailable: '결과를 찾을 수 없습니다',
	riskOfMaterialMisstatement: '중요왜곡표시위험',
	AccountConclusion: '계정 결론',
	CanvasForm: 'Canvas 양식',
	IndependenceForm: '독립성 양식',
	Profile: '프로필',
	AccountDetails: '세부정보',
	Conclusions: '결론',
	accountDetailsTab: '세부정보',
	conclusionsTab: '결론',
	formsNoContentText: '사용 가능한 내용이 없습니다',
	formsDocumentNoRelatedObjects: '문서와 연결된 object가 없습니다',
	formsNoRelatedObjects: '연결된 object가 없습니다',
	formsBodyHeaderControl: '통제',
	formsBodyDesignEffectiveness: '설계 효과성',
	formsScotsAndWcgws: 'SCOT & WCGW',
	wcgWAndRelatedControls: 'WCGW 및 관련 통제',
	controlAndRelatedItSO: '통제 및 관련 IT app/SO',
	type: '유형',
	designEffectiveness: '설계 효과성',
	approch: '접근법',
	controlOpertaingEffectiveness: '운영 효과성',
	iTAppSO: 'IT App/SO',
	iTProcess: 'IT 프로세스',
	iTControl: 'IT 통제',
	iTRisk: 'IT 위험',
	aggregateITEvaluation: 'Aggregate IT Evaluation',
	relatedCanvasForm: '관련 Canvas 양식',
	relatedSections: '연결된 섹션',
	validations: '유효성 검증',
	profileV2Validation: '변경사항 제출되지 않음',
	profileV2ValidationModalDescription: '내용 업데이트를 초래할 변경사항이 있지만 제출되지 않았습니다. 의도한 변경사항이라면 이 모달을 닫고 신규 프로필 답변을 제출하십시오. 의도하지 않은 변경사항이라면 변경사항 보기를 검토하고 수동으로 답변을 이전 선택으로 되돌리십시오.',
	profileV2ValidationCount: '1',
	itProcessWithoutRelatedTechnology: 'IT process without related technology',
	reviewNote: '리뷰노트',
	editAssociations: '연결 편집',
	editAssociationsLower: '연결 편집',
	riskWCGW: '위험:WCGW 관계',
	wcgwTask: 'WCGW:Task 관계',
	noAssertionFound: '경영진주장이 연결되지 않았습니다. {here}를 클릭하여 경영진주장을 연결하십시오',
	limitedRiskAccountIdentifier: 'Limited risk 계정',
	insignificantAccountIdentifier: '비유의적인 계정',
	noWCGWFound: '이 위험에 연결된 WCGW이 없습니다. 연결 편집을 클릭하여 WCGW을 하나 이상 연결합니다.',
	noRelatedWCGWs: '연결된 WCGW이 없습니다',
	noWCGWAvailable: '선택된 경영진주장에 사용 가능한 ROMM이 없습니다',
	expandCollapse: '확장/축소하려면 여기를 클릭하십시오',
	requiredAssertionsInfo: '의존하지 않는 통제위험이 있는 경영진주장과 위험이 높은 추정치 관련 경영진주장만 표시',
	wCGWwithoutTasksInfo: '의존하지 않는 통제위험이 있는 경영진주장과 위험이 높은 추정치 관련 경영진주장에 관련되며, 이에 대응하는 실증 task가 연결되지 않은 WCGW만 표시',
	noBuildStepsAvailable: '표시할 수 있는 작성 단계가 없습니다',
	risk: '위험',
	wcgw: 'WCGW',
	riskWcgw: '위험: WCGW',
	wcgwTasks: 'WCGW: Task',
	riskWcgwLabel: '위험을 WCGW과 연결',
	wcgwTasksLabel: 'WCGW을 Task와 연결',
	noRiskTypes: '위험 유형을 찾을 수 없습니다',
	saveRisk: '저장',
	noRisksFound: '위험을 찾을 수 없습니다',
	haveBeenIdentified: '이(가) 식별됐습니다',
	noAccountsFound: '기록을 찾을 수 없습니다',
	noResponseAvailable: '사용 가능한 응답 없음',
	noDocumentsAvailable: '사용 가능한 문서 없음',
	noValue: '값 없음',
	showValidation: '유효성 검증',
	noAccountsIdentified: '계정이 식별되지 않았습니다',
	noAssertionsIdentified: '경영진주장이 식별되지 않았습니다',
	noWcgwsIdentified: 'WCGW이 식별되지 않았습니다',
	pastingImagesNotAllowed: '이미지 붙여넣기는 허용되지 않습니다. 이미지가 필요한 경우, 증거로 업로드하고 참조 표시하십시오.',
	incompleteResponse: '미완료 응답',
	unresolvedComments: '미해결 Comment',
	inconsistentForms: '모순된 양식',
	limitedRiskAccount: 'Limited Risk 계정',
	inherentRiskAssessment: '고유 위험 평가',
	task: 'Task',
	selected: '선택됨',
	displaytoc: 'TOC 표시',
	workingoffline: '오프라인 작업',
	syncinprogress: '동기화 진행 중',
	prepareoffline: '오프라인 데이터 준비',
	connectionavilable: '연결 가능',
	softwareUpdate: '소프트웨어 업데이트',
	updateLater: '나중에 업데이트',
	updateNow: '지금 업데이트',
	updateMsg: 'EY Canvas 소프트웨어 업데이트가 가능합니다. 업데이트를 다운로드하여 설치하려면, 지금 업데이트를 선택하십시오. 페이지가 새로 고쳐집니다',
	searchPlaceholder: '검색',
	filter: '필터',
	leftNavSearchPlaceholder: '머리글 및 섹션 검색',
	back: '뒤로',
	updateAvailable: '업데이트 가능',
	contentUpdateAvailableTooltip: "내용 업데이트 가능. 업데이트를 개시하려면 여기를 클릭하여'Canvas 양식 업데이트' 화면으로 이동하십시오. ",
	moreMenu: '추가 메뉴',
	signoffPreparer: '작성자로 Sign-off',
	signoffReviewer: '검토자로 Sign-off',
	pagingShowtext: '표시',
	searchDocuments: '문서 검색',
	noRelatedDocuments: '연결된 문서가 없습니다',
	noRelatedObjects: '연결된 object 없음',
	documentName: '문서 이름',
	formDetails: '양식 세부정보',
	questionsAndResponses: '질문 및 응답',
	details: '세부정보',
	trackChanges: '변경사항 추적',
	goToTrackChanges: '변경사항 추적으로 이동',
	attributes: '정보',
	relatedActions: '연결된 작업',
	createCustom: '사용자지정 생성',
	createCustomButtonLabel: 'Create custom header, section, or body',
	overwriteForm: '양식 덮어쓰기',
	decimalNaN: 'NaN - 숫자 아님',
	noRelatedObjectsApplicable: 'Object는 이 Canvas 양식에 연결될 필요가 없습니다',
	objects: 'Object',
	objectName: 'Object 이름',
	addCustomDescription: '이 Canvas 양식에 추가할 내용 유형을 선택하고 세부정보를 입력한 후 저장을 클릭합니다',
	headerTitle: '머리글 제목',
	sectionTitle: '섹션 제목 (필수)',
	aresSectionTitle: '섹션 제목',
	customLabel: '사용자지정 레이블 (선택사항)',
	customBodyDescription: '본문 설명',
	header: '머리글',
	section: '섹션',
	body: '본문',
	requiredWCGW: '필수',
	headerTitleRequired: '머리글 제목 필요',
	bodyDescriptionRequired: '본문 설명은 필수입니다',
	bodySectionRequired: '섹션은 필수입니다',
	bodyHeaderRequired: '머리글은 필수입니다',
	sectionTitleRequired: '섹션 제목은 필수입니다',
	headerRequiredMessage: '머리글은 필수입니다',
	enterDecimalAmount: '십진수 입력',
	enterPercentage: '백분율 입력',
	completeRiskFactorAssessment: '식별된 사건 및 상황 평가 완료',
	noScotsEstimatesIdentified: 'SCOT 또는 추정치가 식별되지 않았습니다',
	// Track changes
	trackChangesResponseLabel: '변경사항 추적 버전 응답',
	trackChangesVersionLabel: '변경사항 버전 추적',
	noResponseIdentified: '식별된 응답 없음',

	// Compare responses
	compareResponsesLabel: '응답 비교',
	compareResponsesTitle: '기업 응답 비교',
	compareResponseNoDataPlaceholder: 'Engagement에 동일한 유형의 문서가 하나 뿐이므로 사용 가능한 데이터가 없습니다',
	labelFor: 'for',
	questions: '질문',
	answers: '답변',
	countOfResponses: '응답 개수',
	openNotes: '처리 전 노트',
	clearedNotes: '처리된 노트',
	click: 'Click',
	clickToViewAnswer: '답변 보기',
	clickToViewQuestionAnswer: '질문과 답변 보기',
	selectDocuments: '문서 선택',
	selectedDocumentsCount: '{0} 문서 선택됨',
	selectedDocumentCount: '{0} 문서 선택됨',
	associatedDocuments: '연결된 문서',
	noAnswerProvided: '제공된 답변 없음',

	// Workspace Engagement
	thisEngagement: '이 engagement',
	documentLocation: '문서 위치',
	otherEngagementsInWorkspace: 'Workspace 내 다른 engagement',
	added: '추가됨',
	documentIneligibleForSharingMessage: '기밀 문서는 공유할 수 없습니다',
	fitDocumentCannotbeSelected: 'FIT 문서는 engagement 간에 공유할 수 없습니다',

	//Helix Configuration
	helixConfigurationTitle: 'EY Helix 데이터 통합하기',
	helixConfigurationPageDescription: '링크된 EY Helix 프로젝트를 검증하고 EY Canvas로 데이터를 불러오십시오. 데이터를 불러온 후 아래에서 EY Helix 설정이나 EY Helix 데이터를 변경했다면, 업데이트할 데이터를 다시 불러와야 합니다.',
	linkedEYHelixProjects: '링크된 EY Helix 프로젝트: ',
	client: '고객: ',
	engagement: 'Engagement: ',
	analysisDate: '분석일: ',
	eyHelixProjects: 'EY Helix 프로젝트',
	noPrimaryEYHelixproject: 'Primary EY Helix 프로젝트가 식별되지 않았습니다',
	here: '여기',
	identifyEyHelixProjects: '를 클릭하여 EY Helix를 식별하고 업무흐름을 시작하십시오',
	eyHelix: 'EY Helix',
	primary: 'Primary',
	helixSettingsDescription: '편집을 클릭하여 EY Helix Analyzer를 로드할 때 적용될 설정을 선택하십시오',
	editButton: '편집',
	helixSettingsModalTitle: 'EY Helix 설정',
	currencyType: '통화 유형',
	currencyTypeError: 'EY Helix에서 통화 유형을 검색할 수 없습니다. 데이터가 EY Helix에 올바르게 설정되어 있는지 확인하고 다시 시도하십시오.',
	shortNumberFormat: '숫자 축약 형식',
	shortNumberFormatFooter: 'EY Helix 표에 표시된 숫자 값에 적용될 반올림',
	eyHelixAnalyzerFilterMetadataError: 'EY Helix에 연결할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	functional: '기능',
	reporting: '보고',
	currencyCode: '통화 코드',
	businessUnit: '사업부',
	roundingNumberFormat: '반올림 숫자 형식',
	eyHelixProjectChangedLine1: 'EY Helix 설정이 마지막으로 저장된 이후로 링크된 EY Helix 프로젝트가 변경됐습니다',
	eyHelixProjectChangedLine2: 'EY Helix에서 데이터를 불러오거나 다시 불러오기 전에 편집을 클릭하여 설정을 업데이트하십시오',
	helixSettingsTimeline: 'EY Helix 설정',
	helixMapEntitiesToBU: '기업을 사업부에 매핑',
	helixNumberOfMapEntities: '매핑된 사업부 수',
	importEYHelixDataTimeline: 'EY Helix 데이터 불러오기',
	mapAccountsHelixTimeline: '계정 매핑',
	setEYHelixSettings: '아래에서 EY Helix 설정을 편집하십시오. 저장되고 나면, 선택한 비교일 1 및 비교일 2 날짜는 OAR 활동에서 지정된 분석일과 비교하는 데 사용됩니다. 단 하나의 비교일과 비교하려면, 비교일 2 선택에서 "없음"을 선택하십시오.',
	eyHelixDataHasChangedLine1: '설정이 마지막으로 저장된 이후로 데이터가 변경됐습니다. 아래에서 신규 항목을 선택하고',
	eyHelixDataHasChangedLine2: '을(를) 클릭하여 EY Helix 설정을 업데이트하십시오.',
	all: '모두',
	multiple: '복수',
	notApplicableAbbreviation: 'N/A',
	importEyHelixData: 'EY Helix 데이터 불러오기',
	editScreenshot: '편집',
	deleteNote: '삭제',
	removeAnnotation: '주석 제거',
	addAnnotation: '주석 추가',
	addingBoundingBox: '스크린샷에서 주석을 달 영역을 선택하고 체크표시를 클릭하여 저장하십시오',
	cancelBoundingBox: '취소',
	deleteScreenshot: '스크린샷 삭제',
	openInFullscreen: '전체 화면으로 열기',
	helixURLErrorMessage1: '매핑된 EY Helix 프로젝트 구성이 완료되지 않았습니다',
	helixURLErrorMessage2: '{0} 페이지로 이동하여 업데이트하십시오',
	helixIsNotEnabledMessage: '귀하의 engagement에 EY Helix가 활성화되어 있지 않습니다',
	helixSetup: 'EY Helix 설정',
	openAnalysisInHelix: 'Open analysis in EY Helix',
	helixInvaliddate: '유효하지 않은 날짜. 분석일 이전 날짜를 선택하십시오.',
	helixcomparativedateoptional: 'EY Helix 비교일 2 (선택사항)',
	helixpriorperioddate: 'EY Helix 비교일 1',
	helixanalysisperiod: 'EY Helix 분석일',
	helixfiscalDropDownLabel: '기간 {0} - {1}',

	helixSettingsEditButtonTitle: '편집',
	helixImportDataEditButtonTitle: '불러오기',
	helixImportInsufficientPermissionsMessage: 'Helix 데이터 불러오기를 수행할 권한이 없습니다. Engagement 관리자에게 문의하여 Helix 데이터 불러오기를 개시할 권한을 요청하십시오.',
	helixImportNotAllowed: 'Engagement 프로필은 Helix 데이터 불러오기를 허용하지 않습니다.',
	helixDeleteImportInsufficientPermissionsMessage: 'EY Helix 데이터 불러오기 삭제 권한 미비. Engagement 관리자에게 문의하여 EY Helix 데이터 삭제 권한을 요청하십시오.',

	EYAccountMappingStepLabel: '계정 매핑 관리',

	EYAccountMappingOptional: '선택사항',
	EYAccountMappingStepTitleSettingsCompleted: 'EY 계정 매핑을 수행하십시오',
	EYAccountMappingStepTitleSettingsIncomplete: 'EY Helix 매핑 모듈에 접근하려면 EY Helix 설정을 완료하십시오',
	EYAccountMappingInstructions: '고객 계정을 EY 계정에 매핑하고 변경사항을 처리하십시오. 처리가 완료되고 나면 아래 데이터를 불러오십시오.',
	manageAccountMappingButtonLabel: '계정 매핑 관리',

	//EY Helix Setup Card
	EYHelixSetupTitle: 'EY Helix',
	EYHelixSetupSubTitle: 'EY Canvas에 데이터 구성 및 불러오기',
	LastImported: '최근 불러옴',
	EYHelixSettings: 'EY Helix 연결',
	NoEYHelixProjectLinkedLabel1: '이 engagement는 주 EY Helix 프로젝트에 링크되어 있지 않습니다. 링크를 설정하려면',
	NoEYHelixProjectLinkedLabel2: '페이지를 방문하십시오',
	NoEYHelixProjectLinkedHperlink: 'EY Helix 프로젝트',
	NoDataImportedLabel: 'EY Helix에서 데이터를 불러오지 못했습니다. 프로세스를 개시하려면 EY Helix 설정을 클릭하십시오.',
	noHelixConnections: "\'EY Helix 연결\'을 클릭하여 연결을 생성하십시오 ",
	helixConnectionExists: '연결이 있습니다',
	helixConnectionsExist: '연결이 있습니다',
	helixTrialBalanceImported: '범위 및 전략 시산표 불러옴',
	helixTrialBalancesImported: '범위 및 전략 시산표 불러옴',
	helixNoTrialBalanceImported: "\'EY Helix 연결\'을 클릭하여 범위 및 전략 시산표를 불러오십시오 ",

	// EY Helix - Map Accounts
	mapAccountsHelixCanvas: 'EY Canvas 계정을 EY Helix에서 불러온 계정에 매핑하려면 편집을 클릭하십시오',
	mapAccountsHelixCanvasSubtitle: 'EY Canvas 계정에 매핑하려면 각 EY Helix 계정을 끌어서 놓으십시오. EY Canvas 계정을 생성하거나 편집하려면 계정 관리를 클릭하십시오.',
	mapAccountsHelixHeaderLabel: 'EY Helix 계정',
	mapAccountsCanvasHeaderLabel: 'EY Canvas 계정',
	mapAccountsConnectedLabel: '계정 연결됨',
	mapAccountsShowMappedLabel: '매핑된 계정 표시',
	mapAccountsHideMappedLabel: '매핑된 계정 숨기기',
	mapAccountsManageLabel: '계정 관리',
	mapAccountsAndDisclosuresManageLabel: '계정 및 공시 관리',
	mapAccountsNoCanvasAccounts: '계정이 식별되지 않았습니다',
	mapAccountsNoCanvasAccountsClick: '시작하려면',
	mapAccountsNoCanvasAccountGetStarted: '을(를) 클릭하십시오',
	mapAccountsRemoveAccount: '제거',
	mapAccountsReImportHelixAccounts: 'EY Helix에서 데이터 불러오기에 실패했습니다. 데이터를 다시 불러와 다시 시도하십시오.',
	mapAccountsReImportHelixAccountsHelpDesk: '문제가 지속되면, 헬프 데스크에 문의하십시오',
	mapAccountsNoHelixAccountHasBeenImported: 'EY Helix 계정을 불러오지 않았습니다',
	mapAccountsNoHelixAccountHasBeenImportedCheckData: 'EY Helix에서 데이터를 확인하고 다시 시도하십시오',

	//Helix Analyzer
	accountNotRelatedToDocumentOnPhaseTwo: '이 문서에 연결된 계정 없음',

	//PM TE SAD Widget
	materialityWidgetLabel: 'PM/TE/SAD',

	// TE labels
	planningmateriality: 'Planning Materiality',
	requiredTePercentage: '필수 TE',
	suggestedtepercentage: '권장 TE',
	currentperiodte: '당기 TE',
	priorperiodte: '전기 TE',
	pmPriorPeriod: '전기 PM',
	tepercentage: 'TE 백분율',
	teamount: 'TE 금액',
	teLabel: 'Tolerable Error',
	sugestedSAD: '권장 SAD 백분율: ',
	priorSAD: '전기 SAD',
	currentPeriodSAD: '당기 SAD',
	sadPercentage: 'SAD 백분율',
	sadAmount: 'SAD 금액',
	rationaleLabel: '근거',
	suggestedTEPercentageInfo: 'Engagement 관련 요소로 인해 다른 백분율을 선택한 경우, 아래에 해당 요소를 문서화하라는 메시지가 표시됩니다.',
	rationaleTEDescription: '위에서 선택한 속성을 고려하여 TE로 PM의 백분율을 정한 근거를 입력하십시오',
	teAmmountInvalid: '유효한 금액을 입력하거나 50% 또는 75% 중 선택하십시오',
	highRiskTEAmmountInvalid: '유효한 금액을 입력하거나 50%를 선택하십시오',
	highRiskTERequired: '위 고려사항에 대한 응답을 바탕으로 식별된 TE 백분율은 필수이며 변경할 수 없습니다',

	// EY Helix Map Entities to Business Units Modal
	mapEntitiesModalTitle: '기업 관리',
	mapEntitiesModalLeyendDescription: 'EY Canvas 기업과 아래 EY Helix 프로젝트의 사업부 간 매핑 관리',
	mapEntitiesModalLeyendNote: 'Note: 사업부는 하나 또는 여러 EY Canvas 기업에 연결될 수 있습니다. 저장하고 데이터를 불러오면, EY Helix 내 사업부에 연결된 데이터가 관련 EY Canvas 기업에 매핑되어 표시됩니다.',
	mapEntitiesModalEntityCodeLabel: '기업 코드',
	mapEntitiesModalEmptyEntitiesList: '기업이 생성되지 않았습니다',
	mapEntitiesRelatedBusinessUnitDropdownPlaceholder: '연결된 사업부',
	mapEntitiesSelectedBusinessUnitsCount: '{0} 사업부 선택됨',

	//AdjustedBasis
	enterAmount: '수치 입력',
	basisAmount: '기준 금액',
	lowEndOfRange: '범위 하한',
	highEndOfRange: '범위 상한',
	suggestedRange: 'Based on the above factors, use of a percentage towards the {0} of the range {1} to {2} may be appropriate. If an amount is selected outside of this range based on engagement specific factors, you will be prompted to document those factors below.',
	suggestedRangeLowPMBracket: '위 요소에 기초하면, {0}의 백분율이 적합할 수 있습니다. 특정 engagement 요소를 기초로 이 백분율을 벗어난 금액을 선택한 경우, 그러한 요소를 문서화하라는 메시지가 표시됩니다.',
	middle: '중간',
	lowerEnd: '하한',
	higherEnd: '상한',
	lowEnd: '하한',
	priorPeriodPm: '전기 PM: ',
	suggestedRangeSummary: '권장 범위',
	loadingMateriality: '중요성 로드 중…',
	pmBasisPercentage: 'PM 기준 백분율',
	pmAmount: 'PM 금액',
	currentPmAmount: '당기 PM 금액',
	pmAmountPlaceholder: 'PM 금액 입력',
	currentPeriodPm: '당기 PM: ',
	enterRationale: '근거 입력',
	rationaleDescription: '위에서 선택한 정보를 고려하여 PM 기준 백분율에 대한 근거를 입력하십시오',
	pmValidationMessage: 'PM은 범위 상한을 초과할 수 없습니다',
	sadValidationMessage: 'Nominal amount는 범위 상한을 초과할 수 없습니다',
	sadRationaleDiscription: '위에서 선택한 속성을 고려하여 SAD 비율에 대한 근거를 입력하십시오',
	nopriorperiodDocs: '사용 가능한 전기 문서 없음',
	addPriorPeriodEvidence: '전기 증거 추가',
	addToEvidenceLabel: '증거에 추가',
	moveToEvidenceLabel: '증거로 이동',
	addToEvidenceModalDescription: '선택한 문서의 이름을 새로 생성하거나 기존 이름을 유지하십시오',
	GoToSource: '원천으로 이동',
	//ITRiskITControls
	createNewITGC: '신규 ITGC',
	relateITGC: 'Relate ITGCs',
	createNewITSP: '신규 ITSP',
	relateITSP: 'ITSP 연결',
	noITGC: 'ITGC 없음',
	itRiskForITGCITSP: 'IT 위험 이름 (필수)',
	createITGCModalDescription: "아래에서 ITGC 세부정보를 입력하고 ‘<b>{0}</b>’을(를) 선택하여 마칩니다. 다른 ITGC를 생성하려면, '<b>{1}</b>'을(를) 선택하십시오. ",
	createITSPModalDescription: "아래에서 ITSP 세부정보를 입력하고 ‘<b>{0}</b>’을(를) 선택하여 마칩니다. 다른 ITSP를 생성하려면, '<b>{1}</b>'을(를) 선택하십시오. ",
	controlDesignEffectiveness: {
		[0]: {
			description: '미선택'
		},
		[1]: {
			description: '효과적'
		},
		[2]: {
			description: '비효과적'
		}
	},
	controlOperationEffectiveness: {
		[0]: {
			description: '미선택'
		},
		[1]: {
			description: '효과적'
		},
		[2]: {
			description: '비효과적'
		}
	},
	controlTesting: {
		[0]: {
			description: '미선택'
		},
		[1]: {
			description: '예'
		},
		[2]: {
			description: '아니오'
		}
	},
	itAppTypes: {
		[0]: {
			label: 'IT App'
		},
		[1]: {
			label: 'SO'
		}
	},
	controlType: {
		[0]: {
			controlTypeName: '',
			shortName: '선택되지 않음'
		},
		[1]: {
			controlTypeName: 'IT Application 통제',
			shortName: 'Application'
		},
		[2]: {
			controlTypeName: 'IT 의존 수동 통제',
			shortName: 'ITDM'
		},
		[3]: {
			controlTypeName: '수동 예방',
			shortName: '수동 예방'
		},
		[4]: {
			controlTypeName: '수동 적발',
			shortName: '수동 적발'
		}
	},
	controlTypeEnumLabel: {
		[0]: {
			controlTypeName: '미선택'
		},
		[1]: {
			controlTypeName: 'IT Application 통제'
		},
		[2]: {
			controlTypeName: 'IT 의존 수동 통제'
		},
		[3]: {
			controlTypeName: '수동 예방'
		},
		[4]: {
			controlTypeName: '수동 적발'
		}
	},
	controlFrequencyType: {
		[0]: {
			controlFrequencyTypeName: '미선택'
		},
		[1]: {
			controlFrequencyTypeName: '하루에 여러 번'
		},
		[2]: {
			controlFrequencyTypeName: '매일'
		},
		[3]: {
			controlFrequencyTypeName: '매주'
		},
		[4]: {
			controlFrequencyTypeName: '매월'
		},
		[5]: {
			controlFrequencyTypeName: '매분기'
		},
		[6]: {
			controlFrequencyTypeName: '매년'
		},
		[7]: {
			controlFrequencyTypeName: 'IT Application 통제'
		},
		[8]: {
			controlFrequencyTypeName: 'Other'
		}
	},
	strategyType: {
		[0]: {
			strategyTypeName: '선택되지 않음'
		},
		[1]: {
			strategyTypeName: '통제'
		},
		[2]: {
			strategyTypeName: '실증'
		},
		[3]: {
			strategyTypeName: 'Rely'
		},
		[4]: {
			strategyTypeName: 'Not Rely'
		}
	},
	aggregateITEvaluationType: {
		[0]: {
			aggregateITEvaluationTypeName: '선택되지 않음'
		},
		[1]: {
			aggregateITEvaluationTypeName: 'Support'
		},
		[2]: {
			aggregateITEvaluationTypeName: 'Not Support'
		},
		[3]: {
			aggregateITEvaluationTypeName: 'FS & ICFR Support'
		},
		[4]: {
			aggregateITEvaluationTypeName: 'FS Only Support'
		}
	},

	sampleItemFilterLabels: {
		filterTypeOfTags: '태그',
		noFiltersAvailable: '사용 가능한 필터 없음',
		filterToolTip: '필터',
		clearAll: '모두 처리',
		showMore: '기타',
		filters: '필터',
		noResults: '결과를 찾을 수 없습니다'
	},

	stratergyTypeLabels: {
		[0]: {
			label: '선택되지 않음'
		},
		[1]: {
			label: 'In Scope'
		},
		[2]: {
			label: 'Out of Scope'
		}
	},
	noChangeReasonCommentAvailable: '문서 톱니바퀴 옵션에서 사유 편집을 클릭하여 변경 사유를 입력하십시오',
	changeReasonModalTitle: '변경 사유 편집',
	changeReasonModalText: '보고서일 후 문서를 변경한 사유를 선택하십시오. 관리적 변경이 여러 번 이루어진 경우, 아래 드롭다운에서 가장 유의한 변경을 선택하십시오. 관리적 변경과 비관리적 변경이 모두 이루어진 경우, 아래에서 비관리적 변경을 선택하십시오.',
	changeReasonUploadModalTitle: '문서 업로드 사유',
	changeReasonUploadModalText: '보고서일 후 문서를 변경한 사유를 선택하십시오. 관리적 변경이 여러 번 이루어진 경우, 아래 드롭다운에서 가장 유의한 변경을 선택하십시오. 관리적 변경과 비관리적 변경이 모두 이루어진 경우, 아래에서 비관리적 변경을 선택하십시오.',
	changeReasonModalComboPlaceholder: '선택',
	changeReasonModalAnnotationText: '발생한 상황과 정보를 추가한 사유; 새롭게 또는 추가로 수행한 감사절차, 입수한 감사증거와 도출한 결론; 그리고 감사보고서에 미치는 영향을 문서화합니다',
	changeReasonUploadModalAnnotationText: '발생한 상황과 정보를 추가한 사유; 새롭게 또는 추가로 수행한 감사절차, 입수한 감사증거와 도출한 결론; 그리고 감사보고서에 미치는 영향을 문서화합니다',
	changeReasonModalAnnotationPlaceHolder: '변경 사유 입력',
	changeReasonModalChangeReasonRequired: '저장할 사유 변경',
	reasonColumnTitle: '사유',
	shared: '공유됨',
	shareStatusOwned: '이 engagement에서 보유함',
	shareStatusShared: '이 engagement에 공유됨',
	lastModifiedBy: '최근 수정한 사람',
	fileSize: ' | {1} KB',
	openedLabelText: '열림',
	currentlyBeingModifiedBy: '현재 수정 중인 사람',
	OpenGuidedWorkflowDocument: 'EY Canvas FIT enablement을 통해 이 문서를 여십시오',
	submitProfile: '프로필 제출',
	submitProfileFit: '프로필 제출',
	contentUpdateUnAuthorizedTooltipMessage: '내용 업데이트를 수행할 권한이 없습니다. 내용 업데이트를 개시할 권한을 받으려면 engagement 관리자에게 문의하십시오.',
	submitProfileValidationErrorMessage: '질문에 모두 답해야 프로필을 제출할 수 있습니다. 미완료 질문을 필터링하여 완료한 후 다시 제출하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	pickADate: '날짜 선택',

	/* Sign Offs */
	preparerHoverText: '작성자로 Sign-off',
	reviewerHoverText: '검토자로 Sign-off',
	preparerTitle: '작성자 Sign-off',
	reviewerTitle: '검토자 Sign-off',
	deleteHoverText: 'Sign-off 제거',
	preparer: '작성자',
	reviewer: '검토자',
	preparerLabel: 'P',
	reviewerLabel: 'R',
	noSignOffsAvailable: '사용 가능한 sign-off 없음',
	none: '없음',
	partnerInChargeLabel: 'PIC',
	eqrLabel: 'EQR',
	documentSignoffRequiredLabel: 'Sign-off 필요: ',

	relatedDocumentsTitle: '연결된 문서',
	relatedTasksCount: '{0} 연결된 task',
	relatedTasksTitle: '연결된 task',
	relateTemporaryFiles: '임시 파일 연결',
	bodyRelatedDocumentsTitle: '본문에 연결된 문서',
	relatedObjectsTitle: '연결된 object',
	relateDocumentsTitle: '연결된 문서 관리',
	relateDocumentsToBodyTitle: '증거 추가',
	relateDocumentsDesc: 'Canvas 양식에 연결할 문서를 선택하십시오',
	relateDocumentsToBodyDesc: '이 engagement 또는 이 workspace 내 다른 engagement에 있는 문서를 연결합니다',
	relateDocumentsToTheBody: 'Relate a document from this engagement.',
	priorPeriodEvidencesToTheBody: '본문에 연결된 전기 증거',
	relatedDocunentEngdisabed: '문서가 이 engagement와 공유되지 않았습니다',
	showOnlyRelatedDocuments: '연결된 문서만 표시',
	manageDocuments: '문서 관리',
	documentCount: '{0} 문서',
	documentsCount: '{0} 문서',
	relateDocumentsSearchPlaceholder: '문서 검색',
	overwriteFormDesc: '현재 Canvas 양식에서 응답을 덮어쓸 양식을 선택합니다. 현재 양식은 임시 파일로 옮겨집니다.',
	searchFormPlaceholder: '양식 검색',
	overwriteLabel: '덮어쓰기',
	confirmOverwriteLabel: '덮어쓰기 확인',
	confirmOverwriteDesc: "{0}' 양식 내용을'{1}' 양식으로 복사하시겠습니까? 응답은'{2}' 양식으로 덮어써지지만, 연결된 증거와 object는 그렇지 않습니다. 해당 증거와 object는 덮어쓰기 완료 후'{3}' 양식에 다시 연결해야 합니다.'{4}' 양식은 기존 프로필/GAM 단계를 유지하므로 프로필이'{5}' 양식과 다른 경우, 복사된 내용을 검토하고 정리하십시오. \n 덮어쓰기 프로세스가 진행되는 동안에는 브라우저를 닫거나 이동하지 마십시오. 덮어쓰기 프로세스가 완료되면, '{6}' 양식은 임시파일로 이동되고 귀하는'{7}' 양식으로 이동합니다. 이 작업은 실행 취소할 수 없습니다. ",
	formSelectionRequired: '재지정할 양식 선택',

	open: '열기',
	startCoeditMode: 'Multi-user 편집 시작',
	endCoeditMode: 'Multi-user 편집 종료',
	openReadOnly: '읽기 전용으로 열기',
	copyLink: '링크 복사',
	rename: '이름 바꾸기',
	viewHistory: '기록 보기',
	documentOpenModelLabel: '현재 수정 중인 문서',
	modelUserOpenedTheDocumentText: '이 사용자가 문서를 열었습니다',
	modelDocumentOpenedText: '이 문서를 현재 수정 중인 사람',
	modelOpenedDocumentConflictText: '문서를 열면 충돌을 초래할 수 있기 때문에 읽기 전용으로 여는 것을 권장합니다. 이 문서의 편집자가 되고 싶다면,',
	clickHereEnabledText: '여기를 클릭하십시오.',
	documentOptions: '문서 옵션',
	accountDetails: '계정 세부정보',

	// DAAS labels
	coEditModeIsEnding: 'Multi-user 편집이 종료됩니다',
	coEditMode: 'Multi-user 편집 중',
	checkInInProgressMessage: '체크인 진행 중. 문서가 체크인되는 데 20분까지 소요될 수 있습니다. 업데이트하려면 새로 고침하십시오',
	checkInInErrorLabel: '체크인 실패',
	checkOutInProgressMessage: '체크아웃 진행 중. 문서가 체크아웃 되는 데 20분까지 소요될 수 있습니다. 업데이트하려면 새로 고침하십시오',
	checkOutInProgressLabel: '체크아웃 진행 중',
	checkInInProgressLabel: '체크인 진행 중',
	checkOutInErrorLabel: '체크아웃 실패',
	daasErrorMessage: '지금은 작업을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	coEditModeIsStarting: 'Multi-user 편집이 시작됩니다',
	daasOpenDocumentWarning: 'Multi-user editing may have been ended by another user. Refresh the page and try again.',
	beingEditedInCoeditMode: 'Being edited in multi-user edit. Edit started by {0}',
	beingEditedInCoeditModeOn: 'on {0}.',
	beingEditedInCoeditModeError: 'Being edited in multi-user edit',
	coEditModeAutomaticallyEnds: '문서가 multi-user 편집 모드이며, {0}일 후 자동으로 종료됩니다',
	coEditModeAutomaticallyEndsToday: '문서가 multi-user 편집 모드이며, 오늘 종료됩니다',
	daasStartCollaborationModeWarning: 'Collaboration mode may have been started by another user. Refresh the page and try again.',
	documentCurrentlyBeingModifiedTitle: 'Document currently being modified',
	documentCurrentlyBeingModifiedHeader: 'This document is currently being modified by {0}. This user opened the document',
	documentCurrentlyBeingModifiedBody: 'Starting multi-user edit mode could cause conflicts, so we recommend discussing with {0} before proceeding. Select {1} to start the multi-user edit mode or {2} to return without starting multi-user edit mode.',
	documentEndMultiUserEditingTitle: '​Multi-user 편집 종료',
	documentEndMultiUserEditingHeader: 'Warning: Other users may be actively editing this document.  This can be checked by opening the document and seeing if other users are currently in the file. Please confirm that all changes are complete before ending multi-user mode. File changes in multi-user mode may take up to 1 minute to be processed. Therefore, please wait at least 1 minute after exiting the file before ending multi-user mode.',
	documentEndMultiUserEditingBody: 'Select {0} to end the multi-user edit mode or {1} to return without ending multi-user edit mode.',
	startMultiuserEditing: 'Start',

	/* Engagement Comments */
	clear: '처리',
	close: '닫기',
	reOpen: '다시 열기',
	reply: '회신 추가',
	replyLabel: '회신',
	unselectComment: 'Comment 선택 해제',
	commentText: '텍스트 입력',
	replyText: '회신 텍스트',
	openStatus: '처리 전',
	clearedStatus: '처리됨',
	closedStatus: '닫힘',
	chartCommentsTitle: '리뷰노트',
	showComments: 'Comment 표시',
	noRecordsFound: '기록을 찾을 수 없습니다',
	noCommentsFound: '아래에 입력하여 comment를 남기십시오. Comment를 사용자에게 배정하고 우선순위와 만료일을 지정하십시오.',
	newComment: 'Comment 추가',
	addNoteTitle: '노트 추가',
	editComment: 'Comment 편집',
	newReply: '회신 추가',
	editReply: '회신 편집',
	commentTextRequired: 'Comment 텍스트 필요',
	replyTextRequired: '회신 텍스트 필요',
	myComments: '내 comment',
	assignTo: '배정 대상',
	theCommentMustBeAssigned: '담당자 필요',
	priorityRequired: '우선순위 필요',
	dueDateRequired: '만료일 필요',
	assignedTo: '담당자',
	allComments: '모든 Comment',
	assignedToMe: '내 담당',
	unassigned: '배정되지 않음',
	draggableCommentsPlaceholder: '신규 comment를 추가하려면 텍스트를 입력하십시오',
	draggableNotesPlaceholder: '텍스트를 입력하여 신규 노트 추가',
	enterReply: '회신 입력',
	dueDate: '만료일',
	commentsAmmount: '{count} comment',
	singleCommentAmmount: '{count} comment',
	eyInternal: 'EY',
	noneAvailable: '사용 가능한 항목 없음',

	navHelixProjects: 'EY Helix 연결',

	evidence: '증거',
	priorPeriod: '전기',
	temporaryFiles: '임시 파일',
	priorPeriodEvidence: '전기',
	closed: '모든 배정 닫힘',

	/*Delete*/
	deleteFileTitle: '문서 삭제',
	deleteFileCloseBtnTitle: '취소',
	deleteFileConfirmBtnTitle: '삭제',
	deleteFileCloseTitle: '닫기',
	deleteFileModalMessage: '선택한 문서를 삭제하시겠습니까?',
	/*Rename*/
	editCanvasformObjects: '정보를 편집하고 <b>저장</b>을 클릭하십시오',
	renameFileModalMessage: '문서 이름을 바꾸고 저장을 클릭하십시오',
	renameScreenshotModalMessage: '스크린샷 이름을 바꾸고 확인을 클릭하십시오',

	renameFileTitle: '문서 이름 바꾸기',
	fileNameRequired: '파일 이름은 필수입니다',
	invalidCharacters: '파일 이름에 다음을 포함할 수 없습니다: */:<>\\?|"',
	existingFileName: '파일 이름이 고유하지 않습니다. 이 메시지를 제거하려면 페이지를 새로 고침하거나 파일 이름을 변경하십시오.',
	maxLengthExceeded: '문서 이름은 115자를 초과할 수 없습니다',

	STEntityProfileBannerMessage: '기업 프로필에 내용 업데이트를 초래할 변경사항이 하나 이상 있습니다. 프로필 기업 페이지로 되돌아가서 “내용 불러오기”를 클릭하여 기업 프로필에 적용 가능한 신규 내용을 수신하거나 답변을 이전 상태로 되돌리십시오.',
	independenceValidationForOwnForm: '독립성 응답에 변경사항이 있지만 제출되지 않았습니다. 의도한 변경사항이라면 응답이 제출됐는지 확인하십시오. 의도하지 않은 변경사항이라면 변경사항 보기를 검토하고 수동으로 답변을 이전 선택으로 되돌리십시오.',
	independenceValidationForOthersForm: '독립성 응답에 변경사항이 있지만 팀 멤버가 이를 제출하지 않았습니다. 의도한 변경사항이라면 팀 멤버가 변경사항을 검토하고 동일하게 제출했는지 확인하십시오.',
	insufficientRightsForIndependenceSubmission: '내용을 편집할 권한이 없습니다. Engagement 관리자에게 문의하여 내용 편집 권한을 요청하십시오.',
	submitIndependenceProfileV2Message: '프로필을 검토하고 응답이 정확한지 확인하십시오. 확인 후, sign-off하고 engagement를 진행하십시오.',
	submitIndependenceProfileV2EditMessage: '프로필에 engagement 내용 변경을 초래할 변경사항이 없습니다. 필요한 경우, Engagement 내용 업데이트 페이지에서 내용 업데이트를 수행하십시오.',
	insufficientRightsForProfileV2Submission: '프로필을 편집할 권한이 없습니다. Engagement 관리자에게 문의하여 프로필을 편집할 권한을 요청하십시오.',
	returnToDashboard: '대시보드로 돌아가기',
	returnToDashboardFit: '대시보드로 돌아가기',
	profileV2ChangeNotSubmittedBannerMessage: 'Changes have been made to the Profile that will result in content updates. Submit the Profile to receive the new content or revert the answers to the previous state.',
	independenceChangeNotSubmittedBannerMessage: '독립성 양식에 재제출이 필요한 변경사항이 있습니다.',
	multiEntityIndividualProfileBannerMessage: '프로필을 편집할 권한이 없습니다. Engagement 관리자에게 문의하여 프로필을 편집할 권한을 요청하십시오.',
	scotStrategy: 'SCOT 전략',
	wcgwStrategy: 'WCGW 전략',
	itProcessStrategy: 'IT 프로세스 전략',

	/*Edit Wcgw*/
	editWcgw: 'WCGW 편집',
	viewWcgw: 'WCGW 보기',
	editScot: 'SCOT 편집',
	viewScot: 'SCOT 보기',
	showIncomplete: '미완료 표시',
	forms: '양식',
	form: '양식',
	comments: '노트',
	changes: '변경사항',
	editHeader: '머리글 편집',
	editSection: '섹션 편집',
	editBody: '본문 편집',
	editSectionDescription: '섹션 세부정보를 편집하고 ‘저장’을 클릭하십시오',
	editHeaderDescription: '머리글 세부정보를 편집하고 ‘저장’을 클릭하십시오',
	editBodyDescription: "본문 세부정보를 편집하고'저장'을 클릭하십시오 ",
	manageObject: 'Object 관리',
	relatedObjects: '연결된 Object',

	/* Manage body objects */
	bro_manage_WCGWTask_title: 'WCGW 연결',
	bro_manage_WCGWTask_instructions: '해당 WCGW 관리',
	bro_manage_WCGWTask_noDataLabel: '결과를 찾을 수 없습니다',

	/*Add/Edit ITGC*/
	addITGC: 'ITGC 추가',
	addNewITGC: '신규 ITGC 추가',
	addExistingITGC: '기존 ITGC 추가',
	addITGCDescription: 'ITGC 설명 입력',
	itControlNameRequired: 'ITGC 이름은 필수입니다',
	frequencyRequired: '빈도는 필수입니다',
	frequencyITGC: '빈도 선택',
	nameITGC: 'ITGC 이름 (필수)',
	iTProcesslabel: 'IT 프로세스',
	editITGC: 'ITGC 편집',
	editITSP: 'ITSP 편집',
	editITGCDescription: 'ITGC 및 관련 정보 편집',
	editITSPDescription: 'ITSP 및 관련 정보 편집',
	viewITGC: 'ITGC 보기',
	viewITSP: 'ITSP 보기',
	itgcTaskDescription: '의존대상기간 동안의 운영 효과성에 대한 충분하고 적합한 감사증거를 입수하기 위해 설계된 ITGC 테스트를 수행합니다',
	/**
	 * Add Edit ITGC
	 */
	addITSPDescription: 'ITSP 설명 입력',
	selectITRisk: 'IT 위험 선택 (필수)',
	itRiskRequired: 'IT 위험 (필수)',
	itspNameRequired: 'ITSP 이름 (필수)',
	itspTaskDescription: '이 task 설명을 수정하여, 의존 기간에 걸쳐 IT 위험에 효과적으로 대응했다는 충분하고 적합한 감사증거를 입수하도록 IT 실증절차의 성격, 시기, 범위를 설계합니다.<br>중간일에 IT 실증절차를 수행하는 경우, 중간절차 대상 기간부터 기말까지 IT 위험에 대응한 추가 증거를 입수하기 위해 절차를 설계하고 수행합니다.<br />IT 실증절차의 결과에 대해 결론을 내립니다.',
	itspRequired: 'ITSP 이름은 필수입니다',
	selectTestingStrategy: '통제테스트의 성격, 시기, 범위를 설계하여, 경영진주장 수준에서 중요왜곡표시를 예방하거나 적발하고 수정하기 위해 통제가 의존 기간에 걸쳐 설계된 대로 효과적으로 운영된다는 충분하고 적합한 감사증거를 입수합니다. <br /> 표본 규모를 확대하거나 보완 통제를 테스트한 경우를 비롯하여 통제테스트의 결과를 평가함으로써, 통제의 운영 효과성에 대해 결론을 내립니다.',
	itControlNameTest: '{0} 테스트',

	/*Edit ITControl*/
	editITControl: 'ITGC/ITSP 편집',
	viewITControl: 'ITGC/ITSP 보기',

	/*Add/Edit ITRisk*/
	editITRisk: 'IT 위험 편집',
	editITRiskDescription: 'IT 위험 편집',
	viewITRisk: 'IT 위험 보기',
	addITRisk: 'IT 위험 추가',
	addITRiskDescription: 'IT 위험 설명 입력',
	selectITProcess: 'IT 프로세스 (필수) 선택',
	itRiskName: 'IT 위험',
	itRiskNameRequired: 'IT 위험 (필수)',
	riskNameRequired: 'IT 위험은 필수입니다',
	processIdRequired: 'IT 프로세스는 필수입니다',
	itProcessRequired: 'IT 프로세스 (필수)',
	hasNoITGC: 'IT 위험에 대응하는 ITGC가 없습니다',

	/*Edit Risk*/
	editRisk: '위험 편집',
	viewRisk: '위험 보기',

	/*Edit Control*/
	editControl: '통제 편집',
	viewControl: '통제 보기',
	scotRelatedControls: '다음에 연결된 통제',
	applicationControl: 'Application 통제',
	iTDependentManualControl: 'IT 의존 수동 통제',
	noAapplicationControlAvailable: 'Application 통제 없음',
	noITDependentManualControlAvailable: 'ITDM 통제 없음',
	isIPEManuallyTested: '이 ITDM 통제의 자동화 부분은 시스템 생성 보고서의 사용 뿐이며 실증 테스트됩니다',

	/*Edit ITProcess*/
	editITSOProcess: 'IT/SO 프로세스 편집',
	viewITSOProcess: 'IT/SO 프로세스 보기',

	/*Edit ITApplication*/
	viewITAppSO: 'IT App/SO 보기',
	editITAppSO: 'IT App/SO 편집',
	strategy: '전략',
	nameRequired: '이름 필요',
	name: '이름',

	/*Snap shot*/
	currentVersion: '현재 버전',
	compareVersion: '비교할 버전을 선택하십시오',
	snapshotVersionNotAvailable: '비교 가능한 버전이 없습니다',
	snapshots: '스냅샷',
	sharedFormWarning: "이는 공유된 Canvas 양식입니다. Object와 증거는 원본 engagement에 존재하며 링크해제 시에 이 engagement에 추가되지 않습니다. 세부 정보는 <a style='color: #467cbe' href='https://live.atlas.ey.com/#library/104/p/SL33184174-396647/C_33404446/C_38129691' target='_blank'>enablement here</a>를 참조하십시오. ",
	fullView: '전체 보기',
	defaultView: '기본 보기',
	print: '프린트',
	version: '버전',
	navigationUnavailable: "변경사항 추적 & 정보 보기에서 내비게이션을 사용할 수 없습니다. 내비게이션을 다시 활성화하려면'질문 및 응답'을 보십시오. ",
	snapshotUpdate: '업데이트됨',
	snapshotNew: '신규',
	snapshotRemoved: '제거됨',
	snapshotRollforward: 'Roll-forward 시 생성됨',
	snapshotRestore: '복원 시 생성됨',
	snapshotCopy: '복사 시 생성됨',

	/*Special Body*/
	priorPeriodAmount: '전기 금액',

	// Helix special body:
	helixScreenshotListLoading: '스크린샷 로드 중…',
	helixScreenshotLoading: '스크린샷 이미지 로드 중…',
	helixScreenshotDeleting: '스크린샷 삭제 중…',
	helixNotesLoading: 'Tickmark 로드 중',
	helixNotesBoundingBoxShow: '주석 표시',
	helixNotesBoundingBoxHide: '주석 숨기기',
	helixNoteReferenceNumber: '#',
	helixNoteReferenceNumberPlaceholder: '참조 번호 입력',
	helixNoteText: '노트',
	helixNoteTextPlaceholder: 'Tickmark 텍스트 입력',
	helixNoteAnnotate: '주석 달기',
	helixNoteAnnotateMessage: '스크린샷에서 주석을 달 영역을 선택하고, 주석을 확인한 후, 체크표시를 클릭하여 저장하십시오',
	helixRemoveAnnotation: '주석 삭제',

	/* User lookup body */
	userLookupInstructionalText: '결과를 보려면 이름이나 이메일을 입력하고 Enter 키를 누르십시오',
	userLookupShortInstructionalText: '이름 또는 이메일을 입력하고 enter 키를 누르십시오',

	/*Guidance*/
	guidance: 'Guidance',
	noIncompleteBodies: '내용을 보려면 내비게이션 메뉴에서 머리글이나 섹션을 선택하십시오',
	noUnresolvedComments: '내용을 보려면 내비게이션 메뉴에서 머리글이나 섹션을 선택하십시오',
	addComment: 'Comment 추가',

	/*Independence*/
	otherFormIndependenceMessage: '이 독립성 양식 내용이 업데이트되었으며 그 이후로 사용자가 다시 로그인하지 않았습니다. 그 결과, 일부 응답이 미완료일 수 있습니다. 이전 독립성 상태는 참고 목적으로 유지되어 있습니다.',
	override: '무시',
	grantAccess: '접근 부여',
	denyAccess: '접근권한 거부',
	overrideSmall: '무시',
	grantAccessSmall: '접근 부여',
	denyAccessSmall: '접근 거부',
	status: '상태',
	undefined: '미확정',
	incomplete: '완료되지 않음',
	noMattersIdentified: '식별한 사항 없음',
	matterIdentifiedPendingAction: '식별한 사항 - 보류 중인 조치',
	matterResolvedDeniedAccess: '해결한 사항 - 거부된 접근',
	matterResolvedGrantedAccess: '해결한 사항 - 부여된 접근',
	notApplicable: '해당 없음',
	restored: '복원됨',
	overridden: 'Overridden',
	priorNoMattersIdentified: '이전 - 식별한 사항 없음',
	priorMatterIdentifiedPendingAction: '이전 - 식별한 사항 - 보류 중인 조치',
	priorMatterResolvedGrantedAccess: '이전 - 해결한 사항 - 부여된 접근',
	priorMatterResolvedDeniedAccess: '이전 - 해결한 사항 - 거부된 접근',
	byOn: 'by {0} on',
	byLabel: 'by',
	onLabel: 'on',
	modifiedBy: '수정한 사람',
	reason: '사유',
	submit: '제출',
	submitTemplate: '템플릿 제출',
	independenceHoverText: '이 사용자의 접근을 부여, 거부, 재지정하려면 귀하가 Partner in Charge, Engagement Partner 또는 Executive Director이어야 합니다',
	enterRationaleText: '근거 입력',
	enterRationalePlaceholderText: '근거 텍스트 입력',
	requiredRationaleText: '근거 (필수)',
	rationaleTextRequired: '근거는 필수입니다',

	sharedExternalWarning: '이 양식은 Canvas Client Portal을 통해 공유되어 외부 팀 멤버가 접근할 수 있습니다. 외부 팀 멤버에게 공유해야 하는 응답과 comment만 입력하십시오.',
	independenceViewTemplateMessage: '이 양식은 각 팀 멤버의 개별 독립성 질문서 역할을 합니다. <br /> 독립성 질문을 완료할 때 각 팀 멤버는 감사 대상 기업에 해당되는 독립성 요구사항과 관련된 여러 질문에 응답해야 합니다. 이러한 질문에 적합한 응답을 선택하십시오. 응답은 각 팀 멤버의 독립성 질문서에 동기화됩니다. 팀 멤버가 다른 응답을 선택하면 engagement에 다시 들어올 때 독립성을 재확인해야 합니다. Engagement에 다시 들어오지 않는다면, 이전 독립성 상태와 응답이 유지됩니다. <br /> 권한이 있는 사용자만 독립성 양식을 변경할 수 있습니다. Engagement 관리자와 얘기하십시오. Archive 전에 수동으로 실행 취소한 경우라도 변경사항은 제출해야 합니다.',

	/**
	 * FORM OBJECTS: SCOT-WCGW-CONTROL
	 */
	fo_instructionalText: 'Canvas 양식이 문서화하는 object 선택',
	fsro_instructionalText: '이 섹션에 연결된 object를 관리합니다',
	relObj_title_risk: '위험',
	relObj_title_riskType: '위험 유형',
	fo_showOnlyRelated: '관련 object만 표시',
	scotsCount: '{0} SCOT',
	wcgwsCount: '{0} WCGW',
	itsoCount: '{0} IT application / 서비스조직',
	controlsCount: '{0} 통제',
	itControlsCount: '{0} IT 통제',
	itGcCount: '{0} ITGC',
	itSpCount: '{0} ITSP',
	itProcessesCount: '{0} IT 프로세스',
	risksCount: '{0} 위험',
	accountsCount: '{0} 계정',

	stEntitiesCount: '{0} 기업',

	componentsCount: '{0} 부문',
	view: '보기',
	searchByScotName: 'SCOT 이름으로 검색',
	searchByWcgwName: 'WCGW 이름으로 검색',
	searchByITSOAppName: 'IT/SO Application 이름으로 검색',
	searchByControlName: '통제 이름으로 검색',
	searchByItControlName: 'IT 통제 이름으로 검색',
	searchByItProcessName: 'IT 프로세스 이름으로 검색',
	searchByRiskName: '위험 이름으로 검색',
	searchByAccountName: '계정 이름으로 검색',
	searchBySTEntityName: '기업 이름별 검색',
	searchByEstimateName: '추정치 이름별 검색',
	searchByComponentName: '부문 이름별 검색',
	noScotsAvailable: '이 Engagement에 사용 가능한 SCOT이 없습니다',
	noRisksAvailable: '이 engagement에서 사용 가능한 위험이 없습니다',
	noControlsAvailable: '이 Engagement에 사용 가능한 통제가 없습니다',
	noItControlsAvailable: '이 Engagement에 사용 가능한 IT 통제가 없습니다',
	noItProcessesAvailable: '이 Engagement에 사용 가능한 IT 프로세스가 없습니다',
	noItApplicationsAvailable: '이 Engagement에 사용 가능한 IT Application이 없습니다',
	noAccountsAvailableLabel: '이 engagement에 사용 가능한 계정이 없습니다.',
	noObjectsRelatedToForm: '이 Canvas 양식에 연결된 object가 없습니다',
	noDocumentControlsAvailable: '통제가 이 문서에 연결되지 않았습니다',
	noDocumentScotsAvailable: '이 문서에 연결된 SCOT이 없습니다',
	noSTEntitiesAvailable: '이 engagement에 사용 가능한 기업이 없습니다',
	noComponentsAvailable: '이 engagement에 사용 가능한 부문이 없습니다',
	editObjectDescription: 'Object 연결을 이 양식에 맞게 편집하십시오',
	editObjectsLabel: 'Object 편집',
	noITGCsOrITSPsHaveBeenIdentified: 'ITGC 또는 ITSP가 식별되지 않았습니다',
	noItProcessIdentified: 'IT 프로세스가 식별되지 않았습니다',
	noControlsIdentified: '통제가 식별되지 않았습니다',
	noRelatedRisksIdentified: '관련 유의적인 위험 또는 부정 위험이 식별되지 않았습니다',
	noItApplicationsIdentified: 'IT Application이 식별되지 않았습니다',
	noSCOTIdentified: 'SCOT이 식별되지 않았습니다',
	noWCGWIdentified: 'WCGW이 식별되지 않았습니다',
	maxLimitLabel: '최대 object 수가 선택됐습니다',
	minLimitLabel: '최소 object 수가 선택됐습니다',

	relatedITAppsTitle: 'IT 프로세스 및 관련 IT application',
	relatedWCGWTasksTitle: 'WCGW 및 연결된 Task',
	noRelatedTasks: '연결된 task가 없습니다',
	noRelatedWcgw: '연결된 WCGW이 없습니다',
	noRelatedControls: '연결된 통제가 없습니다',
	controlRelatedRisksTitle: '통제 및 관련 위험',
	sCOTRelatedRisksTitle: 'SCOT 및 연결된 위험',
	scotRelatedItApp: 'SCOT 관련 IT app',
	relatedItApps: '연결된 IT apps',
	relatedRisksTitle: '연결된 위험',
	relatedItRisksItProcessesTitle: 'ITGC, 관련 IT 프로세스 및 IT 위험',
	testingTitle: '테스트',
	strategyTitle: '전략',
	yes: '예',
	no: '아니오',
	noRelatedRisks: '연결된 유의적인 또는 부정 위험이 없습니다',
	closeAllComments: '모든 comment 닫기',
	closeComments: 'Comment 닫기',
	closeCommentsDescription: '처리 전 및 처리된 comment가 모두 닫힙니다. 이 {0}에 대한 comment를 모두 닫으시겠습니까?',
	addCanvasFormDigital: 'Digital',
	addCanvasFormCore: 'Core',
	addCanvasFormNonComplex: '비복잡',
	addCanvasFormComplex: '복잡',
	addCanvasFormListed: '상장',
	addCanvasFormGroupAudit: '그룹 감사',
	addCanvasFormPCAOBFS: 'PCAOB-FS',
	addCanvasFormPCAOBIA: 'PCAOB-IA',
	addCanvasFormStandards: '기준',
	addCanvasFormLanguage: '언어',
	addCanvasFormNoResultFound: '결과를 찾을 수 없습니다',
	addCanvasFormStandardsNotSelectedMessage: '기준은 필수 필드입니다',
	addCanvasFormLanguageNotSelectedMessage: '언어는 필수 필드입니다',

	/* Confidentiality */
	confidentialityPlaceholder: '기밀 선택',
	confidentiality: '기밀',
	confidentialityTitle: '문서 기밀성',
	confidentialityText: '이 문서를 여는 데 필요한 접근 수준을 설정합니다. 접근 수준은 팀 관리 페이지에서 Engafgement 관리자가 설정합니다. 이 문서에 이미 기밀성이 설정된 경우, 문서를 열 수 있는 사람만 문서를 변경할 수 있습니다.',
	confidentialityNotOpenable: 'Engagement 권한이 충분하지 않아 문서를 열 수 없습니다. 접근 수준은 Engagement 관리자가 팀 관리에서 설정합니다.',
	confidentialityTargetNotOpenable: '기밀 문서는 원천 engagement에서만 열 수 있습니다',
	backToCCP: 'EY Canvas Client Portal로 돌아가기',
	guidanceMessageBackToCCP: '이 양식을 작성한 후, EY Canvas Client Portal로 돌아가 EY에 요청을 제출하십시오',
	noProfileInformationFound: '프로필 정보를 찾을 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	confirmUpdate: '업데이트 확인',
	keepVersion: '이 버전 유지',
	conflictDescription: '이 텍스트 {1}이(가) 열린 이후 {0}이(가) 이를 편집했습니다. 유지해야 하는 버전을 선택하십시오.',
	currentConflictVersion: '현재 버전',
	serverConflictVersion: '서버 버전',
	conflictShowChanges: '변경사항 추적 표시',
	sectionViewTrackChangesDropdownPlaceholder: '버전 선택',
	verifyingIndependence: '독립성 상태 확인 중, 기다려 주십시오.',
	creatingIndependenceForm: '독립성 양식 생성',
	meCallFailed: '사용자 정보를 검색하지 못했습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	getUserByIdFailed: '사용자 독립성 상태를 검색하지 못했습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	independenceFormCreationFailed: '사용자 독립성 양식을 생성하지 못했습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	gettingProfile: '프로필 정보 가져오는 중. 기다려 주십시오.',
	invalidDocumentId: '문서 ID가 유효하지 않습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	returnToEditMode: '편집 모드로 돌아가기',
	saveAndCloseButtonTitle: '저장 & 닫기',
	formCreationFailed: '양식을 생성하지 못했습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',

	/*Sign-off requirements*/
	signOffRequirements: 'Sign-off 요건',
	signoffRequirementsModalTitle: 'Sign-off 요건',
	signoffRequirementsModalDescription1: '아래에서 이 문서의 executive sign-off 요건을 조정하십시오',
	signoffRequirementsModalDescription2: 'EY Canvas에서 필요로 하는 일부 sign-off 요건은 조정할 수 없습니다',
	signoffRequirementsModalSaveLabel: '저장',
	signoffRequirementsModalCancelLabel: '취소',
	signoffRequirementsModalCloseLabel: '닫기',
	signoffRequirementsModalPICLabel: 'PIC',
	signoffRequirementsModalEQRLabel: 'EQR',

	/*<Ares>*/
	/* View changes */
	viewChanges: '변경사항 보기',
	viewChangesModalTitle: '변경사항 보기',
	documentModificationAlert: '최근에 이 활동을 수정한 사람',
	dismiss: '해제',

	/*Task List*/
	aresPageTitle: 'EY Canvas FIT enablement',
	aresPageSubtitle: '요청한 감사 정보를 가지고 아래 단계를 완료하십시오',
	summary: '요약',
	aresNoDocumentFound: '선택한 활동에 사용 가능한 기타 정보 없음',
	taskSubTitleNoValue: '사용 가능한 설명 없음',
	mainActivities: '주요 활동',
	unmarkComplete: '완료 표시 해제',
	markCompleteTitleTip: '완료 표시',
	disableMarkCompleteTitleTip: '이 활동에 완료 표시하려면, 연결된 모든 문서에 하나 이상의 작성자와 검토자가 sign-off했는지 확인하십시오.',
	/*Activity Summary*/
	activitySummary: '활동 요약',
	selectedAnswers: '선택된 답변',
	allAnswers: '모든 답변',
	incompleteResponses: '미완료 응답',
	previous: '이전',
	next: '다음',
	viewAsLabel: '다음으로 보기',
	rolePreparerLabel: '작성자',
	roleDetailedReviewerLabel: '세부 검토자',
	roleGeneralReviewerLabel: '일반 검토자',
	roleEQRLabel: 'EQR',
	/*Simple Helix*/
	helixPlaceholder: 'Helix 스크린샷에 guidance URL이 있는 guidance 유형 7은 필수입니다',
	noNotesAvailable: '생성된 tickmark 없음',
	addScreenshot: '스크린샷 추가',
	replaceScreenshot: '스크린샷 교체',
	replaceFrameDescription: '아래 분석을 검토하고 기존 스크린샷을 교체하려면 교체를 클릭하십시오',
	addNote: 'Tickmark 추가',
	notes: 'Tickmark',
	noScreenshotsAvailable: '시작하려면 {viewDataAnalytic}를 클릭하십시오',
	viewDataAnalytic: '데이터 분석 보기',
	/* Delete modal Helix screenshot*/
	modalTitle: '스크린샷 삭제',
	sureDeleteBeforeName: '스크린샷을 삭제하시겠습니까?',
	sureDeleteAfterName: '스크린샷을 삭제하면 연결된 tickmark도 모두 삭제되며 실행 취소할 수 없습니다',

	/*uploadDocument body type */
	relateExistingDocuments: 'Engagement에서 기존 문서를 연결하거나',
	fromEngagementOr: '이/다른 engagement에서 또는',
	browse: '업로드할',
	toUpload: '을(를) 찾으십시오',
	signoffs: 'Sign-off',
	addDocument: '문서 추가',
	uploadDocument: '문서 업로드',
	relateDocument: '기존 문서 연결',
	generateAccountRiskAssessmentPackage: '그룹 ALRA 생성',
	relateDocumentsToBodyAresTitle: '문서 연결',
	discardLabel: '폐기',
	uploadDocumentLabel: '문서 업로드',
	confirm: '확인',
	duplicateDocumentHeader: '이 engagement에 이름이 동일한 문서가 (증거나 임시파일로서) 이미 하나 이상 존재합니다.',
	duplicateDocumentInstruction: "'덮어쓰기'를 선택하여 문서를 업로드하고 기존 파일을 교체하거나'폐기'를 선택하여 취소합니다. 기존 파일이 증거에 있다면 문서는 증거로 업로드됩니다. 기존 파일이 임시파일에 있다면 문서는 임시파일로 업로드됩니다. ",
	maxUploadFilesError: '시스템에서 문서를 최대 10개까지 동시에 업로드할 수 있습니다',
	/*</Ares>*/
	noTaskRelatedToThisDocument: '이 문서에 연결된 task 없음',
	uncheckTrackChangesToSave: 'Unselect the track changes option to save',
	reviewRoleCloseCommentsTitle: '미해결 Comment',
	reviewRoleCloseCommentsDesc: '처리해야 할 미해결 comment가 있습니다. 필터를 사용하면 미해결 comment를 쉽게 식별할 수 있습니다.',

	/*Document Upload - PIC/EQR Required Body type*/
	requirementDetails: '요구사항 세부정보',

	//Risk Factors
	riskFactor: '관련 사건 및 상황/왜곡표시위험',
	manageRisk: '위험 관리',
	noRiskFactors: '관련 사건 및 상황/왜곡표시위험이 식별되지 않았습니다',
	relateRiskFactorsToRisks: '사건 및 상황의 유의성 결정',
	riskType: '위험 유형',
	relateToRisk: '위험에 연결',
	noRisksIdentified: '식별된 위험 없음',
	notDefined: '정의되지 않음',
	selectValidRiskType: '유효한 위험 유형을 선택하십시오',
	newRisk: '신규 위험 추가',
	notAROMM: '중요왜곡표시위험 아님',
	describeRationale: '근거 설명',
	noRisksIdentifiedForTheSpecificRiskType: '{0}이(가) 식별되지 않았습니다',
	addAnAccount: '추가 계정 연결',
	selectAnAccount: '계정 선택',
	noAccountsHaveBeenIdentified: '계정이 식별되지 않았습니다',
	accountSelected: '계정',
	statementType: '재무제표 유형',
	selectAssertions: '경영진주장을 하나 이상 선택하십시오',
	noAssertionsIdentifiedForAccount: '이 계정에 경영진주장이 식별되지 않았습니다',
	relatedAssertions: '연결된 경영진주장',
	editAccount: '계정 & 공시 편집',
	addNewDescription: '신규 설명 추가',
	editRiskFactorDescription: '설명 편집',
	enterRiskFactorDescription: '관련 사건 및 상황/왜곡표시위험에 대한 설명 입력',
	riskFactorDescriptionRequired: '관련 사건 및 상황/왜곡표시위험 설명 필요',
	riskFactorDescription: '관련 사건 및 상황/왜곡표시위험 설명',
	createNewAccount: '신규 계정 생성',
	createAccountLabel: '계정 {0}을(를) 생성했습니다',
	updateAccountLabel: '계정 {0}에 대한 편집을 저장했습니다',
	deleteAccountLabel: '이(가) 삭제됐습니다',
	significanceLabel: '유의성',
	provideRationale: '선택을 저장하려면 근거를 제공하십시오',
	clearRiskSignificance: '위험 유의성 및 설명 지우기',
	clearSignificance: '유의성 및 설명 지우기',

	// Account Summary
	unavailable: '사용 불가능',
	notAvailable: '사용 불가능',
	fraudRisk: '부정 위험',
	significantRisk: '유의적인 위험',
	significantRiskIndicator: 'SR',
	fraudRiskIndicator: 'FR',
	inherentRisk: 'ROMM',
	inherentRiskIndicator: 'ROMM',
	prioryearbalance: '전기 잔액: ',
	accounts: '계정',
	accountsOther: '계정 - 기타',
	accountsSignDis: '유의적인 공시',
	xMateriality: 'PM의 배수',
	xTEMateriality: 'x TE',
	estimateAssociated: '추정 연결됨',
	designation: '지정',
	noAccountshasbeenIdentified: '계정이 식별되지 않았습니다',
	noAccountsOtherhasbeenIdentified: '식별된 다른 계정 없음',
	noAccountsSigfhasbeenIdentified: '식별된 유의적인 공시 없음',
	addOtherAccounts: '계정 추가 - 기타',
	addSignificantDisclosure: '유의적인 공시 추가',
	pydesignation: '이전 지정: ',
	notapplicable: 'N/A',
	noApplicable: '해당 없음',
	changeDesignationMessage: '계정 지정을 변경합니다',
	changeDesignationTitle: '계정 지정 변경',
	continue: '계속',
	currentYearBalance: '당기',
	currentPeriodBalance: '당기',
	priorYear: '전기',
	priorYearDesignation: '전기 지정',
	priorYearEstimation: '전기 추정',
	priorPeriodChange: '% 변동',
	analytics: '분석',
	notROMMHeader: '중요왜곡표시위험 아님',
	manageEyCanvasAccounts: 'EY Canvas 계정 관리',
	manageAccountMapping: '계정 매핑 관리',
	manageAccountMappingCloseButton: '페이지 하단의 버튼을 이용하여 닫으십시오',
	manageAccountMappingToasterMessage: 'EY Helix에 연결할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	inherentRiskTypeChangeMessage: '계정 경영진주장을 관련있음에서 관련없음으로 변경하면, 경영진주장에 대한 WCGW 등 Canvas에 있는 특정 연결이 제거되고 PSP는 OSP로 강등됩니다. 계속하려면 “확인”을 클릭하십시오. 변경 없이 되돌아가려면 “취소”를 클릭하십시오.',

	analyticsIconDisabled: '귀하의 engagement에 EY Helix가 활성화되어 있지 않습니다',
	//Estimate
	estimatesTitle: '추정치',
	relatedAccount: '연결된 계정',
	relatedAccounts: '연결된 계정',
	currentBalance: '당기 잔액',
	priorBalance: '전기 잔액',
	unrelateEstimates: '연결되지 않은 추정치 계정',
	manageEstimates: '추정치 관리',
	noEstimatesCreated: '추정치가 생성되지 않았습니다',
	createEstimates: 'Create estimate',
	estimateStarted: 'to get started',
	createEstimateDocument: '추정 문서 생성',
	noEstimatesFound: '추정치가 식별되지 않았습니다',
	relateEstimateLink: '추정치 연결',

	//Journal Source
	noJournalEntrySourcesAreAvailable: '사용 가능한 분개 원천이 없습니다',
	jeSourceName: '분개 원천',
	jeSourceNameTooltip: '분개 원천',
	changeInUse: '사용 중 변경',
	grossValue: '관련 거래 총액',
	relatedTransactions: '관련 거래 수',
	relevantTransactions: 'SCOT 관련',
	expandAll: '모두 확장',
	collapseAll: '모두 축소',
	descriptionLabel: '이 원천의 목적을 간략히 설명하십시오',
	jeSourceTypesLabel: '이 원천을 통해 기록된 분개는 시스템 생성입니까 아니면 수동입니까?',
	journalEntries: '이 원천이 비표준 분개(즉, 비경상적, 이상 거래, 조정사항)를 기록하는 데 사용됩니까?',
	identifySCOTsLabel: '분개 원천에 연결된 SCOT을 식별하십시오 (해당 항목 모두 선택)',
	systemGeneratedLabel: '시스템 생성',
	manualLabel: '수동',
	bothLabel: '둘 다',
	accountEstimateLabel: 'Est',
	addSCOTLabel: 'SCOT 추가',
	newSCOTLabel: '신규 SCOT',
	addSCOTModalDescription: 'SCOT을 생성할 수 있습니다. 저장되고 나면 변경사항이 적용됩니다.',
	scotnameRequired: 'SCOT 이름은 필수입니다',
	scotCategoryRequired: 'SOCT 범주는 필수입니다',
	estimateRequired: '추정치는 필수입니다',

	jeSourcesLabel: '분개 원천',
	jeSourcesToSCOTs: 'SCOT에 대한 분개 원천',
	scotsToSignificantAccounts: '유의적인 계정에 대한 SCOT',

	//Modal common labels.
	modalCloseTitle: '닫기',
	modalConfirmButton: '변경사항 저장',
	modalCancelTitle: '취소',
	modalSave: '저장',
	modalSaveAndClose: '저장 및 닫기',
	modalSaveAndAdd: '저장 및 추가',
	modalSaveAndCreateAnother: '저장 및 추가 생성',

	//Add & Manage Risks
	addNewRiskModalTitle: '신규 위험 추가',
	manageRisksListModalTitle: '위험 관리',
	riskInfoMessage: '저장되고 나면 변경사항이 적용됩니다',
	risksListInstructionalText: '기존 위험을 편집하거나 삭제할 수 있습니다. 필요한 경우, 신규 위험을 추가할 수도 있습니다.',
	risksListEmptyArray: '사용 가능한 위험 없음. 시작하려면 신규 위험을 추가하십시오.',
	addNewRiskButtonLabel: '신규 위험 추가',
	labelRiskName: '위험 이름',
	riskDescriptionLabel: '위험 설명',
	selectRiskType: '위험 유형',
	requiredRiskName: '위험 이름은 필수입니다',
	requiredRiskType: '위험 유형은 필수입니다',
	deleteRiskTrashLabel: '위험 삭제',
	undoDeleteRiskTrashLabel: '삭제 취소',
	notARommLabel: '중요왜곡표시위험 없음',
	identifiedRiskFactors: '식별된 사건/상황/왜곡표시위험, 중요왜곡표시위험, 유의적인 위험 및 부정 위험',
	noneIdentified: '식별되지 않음',
	countUnassociatedRisk: '사건/상황/왜곡표시위험이 연결되지 않았습니다/’중요왜곡표시위험 아님/중요왜곡표시위험’으로 표시되지 않았습니다',

	// Bar Chart / Account Summary
	accountsTotal: '총 {0} 계정',
	accountSummary: '계정 요약',
	allAccounts: '모든 계정',
	significantAccountsBarChart: '유의적인 계정',
	limitedAccounts: 'Limited risk 계정',
	insignificantAccounts: '비유의적인 계정',
	noAccountsHasBeenIdentifiedBarChart: '{0}이(가) 식별되지 않았습니다',
	selectedTotalAccountsCounter: '{0}/{1} 계정',
	identifyInsignificantAccounts: '비유의적인 계정 식별',
	identifySignificantAccounts: '유의적인 계정 식별',
	identifyLimitedAccounts: 'Limited risk 계정 식별',
	preInsigniAccounts: '당기에 더 이상 TE보다 작지 않은 전기 비유의적인 계정',
	nonDesignatedInsignificant: '이 계정은 비유의적으로 지정될 수 없습니다. 지정 드롭다운을 클릭하여 이 계정에 대한 지정을 변경하십시오.',
	tolerableError: 'TE를 사용할 수 없습니다. 중요성을 업데이트하고 다시 시도하십시오.',
	documentContainerLabel: '문서',
	clickcreateformtogenerate: '{0}을(를) 클릭하여 Limited risk 계정 조서를 생성하십시오',
	createform: '양식 생성',
	createLimitedRiskAccountDocumentation: 'Limited risk 계정 조서 생성',
	limitedAccountDocumentName: 'Limited risk 계정 문서',

	//Modal Confirm Switch account
	modalSwitchTitle: '저장되지 않은 변경사항',
	modalConfirmSwitch: '확인',
	modalConfirmSwitchDescription: '계속하면 변경사항이 저장되지 않고 지워집니다. 계속하시겠습니까?',

	//Modal Edit Account
	manageAccountsModalTitle: 'EY Canvas 계정 관리',
	editAccountLinkLabel: '계정 편집',
	editAccountInstructionalText: '기존 EY Canvas 계정을 편집 또는 삭제하거나 신규 계정을 생성할 수 있습니다. 저장되고 나면 변경사항이 적용됩니다.',
	selectAnAccountLabel: '계정 선택',
	accountNameLabel: '계정 이름',
	accountLabel: '계정',
	accountDesignationLabel: '계정 지정',
	accountStatementTypeLabel: '재무제표 유형',
	accountRelatedAssertionsLabel: '연결된 경영진주장',
	accountHasEstimateLabel: '추정이 계정에 영향을 미칩니까?',
	requiredAccountName: '계정 이름은 필수입니다',
	requiredAccountDesignation: '계정 지정은 필수입니다',
	requiredStatementType: '재무제표 유형은 필수입니다',
	requiredRelatedAssertions: '경영진주장 선택',
	pspIndexDropdownLabel: 'PSP index 선택',
	removePSPIndexLabel: 'PSP index 제거',
	addPSPIndexLink: 'PSP Index 추가',
	pspIsRequired: 'PSP Index는 필수입니다',

	//Delete account modal
	deleteAccount: '계정 삭제',
	deleteAccountModalMessage: '선택한 계정을 삭제하시겠습니까?',
	cannotBeUndone: '이는 실행 취소할 수 없습니다',
	guidedWorkflow: 'EY Canvas FIT enablement',
	scotSummary: 'SCOT 요약',
	scopeAndStrategy: '범위 및 전략',
	ToggleSwitch: {
		Inquire: '질문',
		Completed: '완료됨',
		isOn: '예',
		isOff: '아니오'
	},
	navExecution: '실행',
	navCanvasEconomics: 'EY Canvas Economics',
	navOversight: 'EY Canvas Oversight',
	navConclusion: '결론',
	navTeamMemberIndependence: '팀 멤버 독립성',
	navGroupAudit: '그룹 관리',
	navGroupActivityFeed: '그룹 활동 피드',
	navPrimaryStatus: 'Primary 상태',
	navComponentStatus: '부문 상태',
	navGroupStatus: '그룹 상태',
	navEngagementManagement: 'Engagement 관리',
	navProfile: '프로필',
	navItSummary: 'IT 요약',
	nav440GL: '보고서일 후 변경사항',
	navGroupStructureSummary: '그룹 구조',
	navGroupInstructionSummary: '그룹 지침',
	navGroupInvolvement: '그룹 관여',
	navNotApplicable: '해당 없음',
	cropScreenshot: '스크린샷 자르기',
	cropScreenshotModalDescription: '관련 부분만 포함하도록 스크린샷을 자릅니다. 자르면 기존 주석은 제거되고 tickmark는 유지되며 주석을 다시 달 수 있습니다. 자르기는 실행 취소할 수 없습니다.',
	crop: '자르기',
	replace: '교체',
	nameTheScreenshot: '스크린샷 이름',
	nameLabel: '이름',
	takeScreenshot: '스크린샷 추가',
	measurementBasis: '측정 기준',
	MeasurementBasisMessage: '원천 EY Helix 데이터 매핑에 따르면, 선택한 측정 기준이 예상 위치(예, 차변 위치에 있는 세전이익)에 없는 것으로 보입니다. 다음을 고려하십시오: ',
	measurementBasisProjectMappedCorrectly: 'EY Helix 프로젝트 데이터가 올바르게 매핑되었는지',
	measurementBasisAppropriateValue: '다른 측정 기준이 적합한지',
	measurementBasisAdjustValue: '아레 제시된 것처럼 측정 기준 값을 조정하는 것이 적합한지',
	basisValueFromHelix: '시산표 값',
	rationaleDeterminationLabel: '이 금액을 설정한 근거',
	loginInstructions: '로그인하고 지침에 따라 계정을 설정하십시오',
	gotoText: 'Go to,',
	asOfDate: '기준일',
	annualizedBasisValue: '연환산 기준 값',
	basisValue: '기준 값',
	isAnnualizedAmountRepresentative: '연환산 기준 값은 기말 현재 보고될 것으로 예상되는 금액을 나타냅니까?',
	isAnnualizedAmountRepresentativeForAssetsOrEquity: '값이 감사기간말에 보고될 예상 금액을 나타냅니까?',

	enterExpectedFinancialPerioadAmount: '감사기간 종료시 예상 금액 입력',
	enterRationaleAmountDetermined: '이 금액 결정 근거 입력',
	printNotAvailable: '{0}은(는) 내용이 없어 정보가 표시되지 않습니다',
	canvasFormPrintNotAvailable: 'Canvas 양식 인쇄를 현재 사용할 수 없습니다. 다시 시도하거나 오류가 지속되면 헬프 데스크에 문의하십시오.',
	saving: '저장 중…',
	removing: '제거 중…',
	activitySummaryTitle: '활동 요약',
	currentLabel: '현재',
	PMLabel: 'PM',
	planningMaterialityLabel: 'Planning Materiality',
	TELabel: 'TE',
	tolerableErrorLabel: 'Tolerable Error',
	SADLabel: 'SAD',
	SADNominalAmountLabel: 'SAD Nominal Amount',
	PriorLabel: '전기',
	editRichText: '텍스트 편집',
	noTypeTwoResponseAvailable: '사용 가능한 응답 없음. <br /> 응답하려면 {clickHere}을(를) 클릭하십시오.',
	clickHere: '여기',
	helixNavigationTitle: 'EY Helix 구성 페이지로 이동하여 EY Helix 프로젝트를 링크하거나 구성하십시오',
	helixNavigationLink: 'EY Helix로 이동',
	measurementBasisValue: '측정 기준 값',
	inlineSaveUnsavedChanges: '저장되지 않은 변경사항이 있습니다. 계속하시겠습니까?',
	rationaleIncomplete: '근거 미완료',
	projectMismatchDisplayMessageOnDataImport: '귀하의 주 EY Helix 프로젝트가 변경됐습니다. EY Helix 설정을 확인하고 신규 프로젝트에서 데이터를 불러오십시오.',
	importSuccess: 'EY Helix 데이터 불러옴',
	importHelix: '불러오기를 클릭하여 EY Helix에서 총계정원장 데이터를 불러오십시오',
	importLabel: '불러오기',
	reImportLabel: '다시 불러오기',
	lastImportedBy: '최근 불러온 사람',
	lastImportedOn: '최근 불러온 날짜',
	dataImportedFromProjec: '프로젝트에서 불러온 데이터',
	reImportConfirmationTitle: 'EY Helix에서 데이터 다시 불러오기',
	reImportConfirmationMessagePart1: 'EY Helix에서 데이터를 다시 불러오면 관련 활동 내에서 기존 데이터가 변경되거나 신규 데이터가 추가되며, 이는 실행 취소할 수 없습니다',
	reImportConfirmationMessagePart2: '데이터 다시 불러오기 프로세스가 EY Canvas FIT enablement에 미치는 영향 요약과 더 많은 정보를 보려면 EY Atlas를 참조하십시오',
	defineRisksTitle: '위험 정의',
	assessAndDefineRisksTitle: '위험 평가 및 정의',
	identifiedRiskFactorsTitle: '식별된 사건/상황 및 관련 위험: ',
	descriptionIncompleteLabel: '설명 미완료',
	noRiskHasBeenRelatedMsg: '위험이 연결되지 않았습니다',
	rationaleIncompleteMsg: '근거 미완료',
	loading: '로드 중…',
	importInProgressLabel: '불러오기 진행 중. 수 분이 소요될 수 있습니다. 업데이트 상태를 보려면 페이지를 새로 고침하십시오.',
	importInProgressMessage: 'EY Helix 데이터 불러오기 진행 중. 불러오기 상태를 보려면 페이지를 새로 고침하십시오.',
	importHelixProjectError: 'EY Helix에서 데이터를 불러오면서 오류가 발생했습니다. EY Helix 프로젝트의 상태가 분석 사용 가능으로 표시되는지 확인하고 페이지를 새로 고침한 후 불러오기 또는 다시 불러오기를 클릭하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	importDeletionConfirmationMsg: 'EY Helix 데이터 불러오기를 삭제하시겠습니까? 불러오기를 삭제하면 연결된 활동 내 기존 데이터가 삭제되며 이는 실행 취소할 수 없습니다.',
	deletePreviousImport: 'EY Helix 불러오기 삭제',
	//Assess Risks - Summary Page
	assessRisksAccordionLabel: '위험 연결',
	assessRisksNoItemsFound: '위험이 식별되지 않았습니다',
	assessRiskAccountsAndRelatedAssertions: '계정 및 관련 경영진주장',
	assessRiskNoAccountsLinked: '연결된 계정 없음',
	accountRiskAssessmentSummary: '계정 및 공시',
	// Flow chart
	flowchartTitle: 'Flowchart',
	launchFlowchart: 'Flowchart 실행',
	clickherelink: 'Click here',
	orderToCashPlacement: 'Order to cash placement',
	orderToCashPlacementMessage: '프로그램 부서에서는 신규 고객과 계약을 협상하고 기존 고객과 계약 갱신 및/또는 계약 수정을 협상합니다. 계약에는 보수, 조건, 보증과 같은 세부정보가 포함됩니다.',
	saveFlowChart: '저장',
	newstep: '신규 단계',
	zoomIn: '확대',
	zoomOut: '축소',
	resetZoom: '확대/축소 재설정',
	toogleInteractivity: '상호작용성 전환',
	fitView: 'Fit 보기',
	numberOfSteps: '단계 수',
	flowchartSuccessfullyCreated: 'Flowchart가 생성됐습니다',
	flowchartLinkedEvidenceMessage: '이 flowchart는 다른 engagement에서 생성됐습니다. 증거에 대한 링크를 해제하면 이 engagement에서 flowchart 접근권한이 제거됩니다.',
	flowchartSmartEvidenceSourceIdNullMessage: '사용 가능한 SCOT 없음',
	noTaskDocumentAvailableFlowchart: '이 문서는 임시파일입니다. Flowchart 세부정보에 접근하려면 task에 증거로 연결하십시오.',
	// Control Attributes
	controlAttributes: '통제 속성',
	noControlAttributes: '사용 가능한 통제 없음',
	flowchartStepMoremenu: '추가 메뉴',
	createControlAttributes: '사용 가능한 통제 속성 없음.<br />신규 통제 속성을 생성하려면 {clickHere}을(를) 클릭하십시오.',
	createNewControlAttribute: '신규 속성',
	editControlAttribute: '속성 편집',
	createControlAtttributeInstructions: '아래에서 속성 세부정보를 편집하고 <b>\’저장 및 닫기 \’</b>를 선택하여 마칩니다. 다른 속성을 더 생성하려면 <b>\저장 및 추가 생성\’</b>을 선택하십시오.',
	editControlAttributeInstructions: '아래에서 속성 세부정보를 편집하고 <b>\’저장\’</b>을 선택하여 마칩니다. 속성은 속성 index 기준으로 정렬됩니다.',
	editAttributeButtonLabel: '속성 편집',
	deleteAttributeButtonLabel: '속성 삭제',
	deleteControlAttributeInstructions: '선택한 속성을 삭제하시겠습니까? 이 작업은 실행 취소할 수 없습니다.',
	// Control Attributes Form
	requiredAttributeIndexLabel: '속성 index (필수)',
	requiredAttributeDescriptionLabel: '속성 설명 (필수)',
	errorMessageAttributeIndexRequired: '필수',
	errorMessageAttributeDescriptionRequired: '필수',
	errorMessageAttributeDescriptionMaxLength: '응답에 {#}자가 포함되어 최대치 {##}자를 초과합니다. 텍스트 또는 서식을 줄여 설명을 조정한 후 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	errorMessageAttributeTestingTypesRequired: '필수',
	proceduresLabel: '수행할 절차',
	modalRequiredProceduresLabel: '수행할 절차 (필수)',
	attributeTestingTypesLabel: {
		inquiry: '질문',
		observation: '관찰',
		inspection: '조사',
		reperformance: '재수행/재계산',
	},

	/*CRA Badge*/
	ir: 'IR',
	cr: 'CR',
	cra: 'CRA',
	incompleteCra: '미완료 CRA',
	incomplete: '완료되지 않음',

	//Progess bar labels
	savingProgress: '저장 중…',
	discardChangesLabel: '변경사항 폐기',
	removeFromBody: '본문에서 제거',
	uploading: '업로드 중…',
	uploadComplete: '업로드 완료',
	downloadComplete: '다운로드 완료',
	processing: '처리 중…',

	/* ISA BODIES */
	/* Common */
	deleteEntityConfirmation: '<b>{0}</b>을(를) 삭제하시겠습니까? 이 작업은 실행 취소할 수 없습니다.',
	/* ITAPP-SCOT */
	searchScot: 'SCOT 검색',
	addITApp: 'IT application 추가',
	relatedItApp: '연결된 IT App',
	itApplicationHeader: 'IT Application',
	scotNoDataPlaceholder: '사용 가능한 정보 없음',
	noScotsOrControlsPlaceholder: '연결된 SCOT 또는 통제 없음; {noScotsOrControlsPlaceholderEditAssoc}.',
	noScotsOrControlsPlaceholderEditAssoc: '연결 편집',
	noScotsOrControlsPlaceholderTarget: '연결된 SCOT 또는 통제 없음',
	scotHeader: 'SCOT',
	controlsHeader: '통제',
	controlsApplicationHeader: 'Application',
	controlsITDMHeader: 'ITDM',
	itAppScotNoDataPlaceholderLabel: 'IT application이 추가되지 않았습니다',
	itAppScotNoDataPlaceholder: 'IT application이 추가되지 않았습니다.<br /> 시작하려면, {itAppScotNoDataPlaceholderAddItApp}를 클릭하십시오.',
	itAppScotNoDataPlaceholderAddItApp: 'IT application 추가',
	editItAppOption: 'IT Application 편집',
	removeItAppOption: 'IT Application 삭제',
	viewItAppOption: 'IT application 보기',
	editScotsISA: 'SCOT 편집',
	viewScotISA: 'SCOT 보기',
	viewControlISA: '통제 보기',
	scotAndRelatedControls: 'SCOT 및 통제',
	otherControls: '기타 통제',
	controlTypeLabel: '통제 유형',

	/*SCOT-ITAPP*/
	addOrRelateItAppPlaceholder: '{identifyRelatedItApps} 또는 {documentScotHasNoItApps}을 문서화합니다',
	identifyRelatedItApps: '관련 IT application 식별',
	documentScotHasNoItApps: 'SCOT에 연결된 IT application 없음',
	correctScotDocumentationPlaceholder: 'SCOT이 IT application으로 지원되지 않는 것으로 지정됐습니다. Application 및/또는 ITDM 통제가 이 SCOT, {correctScotDocumentation}에 연결됐습니다.',
	correctScotDocumentation: '이 SCOT을 지원하는 IT application이 없다고 지정한 것이 맞는지 다시 확인하십시오',
	controlsWithoutRelatedItApps: 'IT application이 연결되지 않은 통제',
	controlsWithRelatedItApps: 'IT application이 연결된 통제',

	/*AddEditITApplication */
	saveAndCreateNewButtonTitle: '저장 & 신규 생성',
	instructionalMessage: 'IT application을 추가하고 관련 SCOT을 선택하십시오. 해당되는 경우, IT application에 관련된 통제를 연결하십시오.',
	ITApplicationNamePlaceholder: 'IT application 이름 (필수)',
	scotDropdownPlaceholderText: 'IT application에 연결될 SCOT 선택',
	selectScotPlaceholderText: 'IT application에 연결될 SCOT 선택',
	selectControlPlaceholderText: 'IT application에 연결될 통제 선택',
	noRelatedScotsPlaceholderText: '연결된 SCOT 없음',
	noRelatedControlsPlaceholderText: '연결된 통제 없음',
	CreateSOModelTitle: '서비스조직 추가',
	CreateSOInstructionalMessage: "아래에 신규 서비스조직 세부정보를 편집하고 ‘<b>{0}</b>’을(를) 선택하여 마칩니다. 다른 서비스조직을을 생성하려면, '<b>{1}</b>'을(를) 선택하십시오.`, //'신규 서비스조직을 생성하고 관련 SCOT과 통제를 연결하십시오. ",
	saveAndCloseLabel: '저장 및 닫기',
	saveAndCreateLabel: '저장 및 추가 생성',
	SONamePlaceholder: '서비스조직 이름 (필수)',
	SOSelectScotPlaceholderText: '서비스조직 관련 SCOT 선택',
	SOSelectControlPlaceholderText: '서비스조직 관련 통제 선택',
	CreatedSOLabel: 'SO 추가됨',
	createdITAppLabel: 'IT application 추가됨',
	searchNoResultFoundText: '결과를 찾을 수 없습니다',
	searchNoResultsFoundText: '결과를 찾을 수 없습니다',
	iTApplicationNameRequired: 'IT Application 이름은 필수입니다',
	soNameRequired: '서비스조직 이름은 필수입니다',
	editITAppDesctiptionLabel: 'IT application 및 관련 SCOT과 통제 편집',
	editSODescriptionLabel: '아래에서 서비스조직 세부정보를 편집하고 <b>’저장’</b>을 선택하여 마칩니다',
	viewITApplication: 'IT application 보기',
	itApplicationName: 'IT Application 이름',
	serviceOrganizationName: '서비스조직 이름',
	newItApplication: '신규 IT application',

	/*Add/Edit ITProcess*/
	itProcessName: 'IT 프로세스 이름',
	addItProcessDescription: 'IT 프로세스 생성',
	addItProcess: 'IT 프로세스 추가',
	itProcessNameRequired: 'IT 프로세스 이름은 필수입니다',
	editItProcess: 'IT 프로세스 편집',
	editItProcessDescription: 'IT 프로세스 편집',
	viewItProcess: 'IT 프로세스 보기',
	taskTitle: 'IT 프로세스 이해 및 문서화: {0}',
	taskDescription: 'IT 프로세스에 대한 이해를 문서화합니다. IT 프로세스에 대한 이해를 뒷받침하기 위해 관련 양식을 증거로 첨부합니다. <br /> Task 정보 섹션에 ITGC가 나타나면, walkthrough 절차를 수행하여 ITGC에 대한 이해를 확인하고 그 설계와 구축을 평가합니다. 절차의 증거를 첨부합니다.',
	newItProcess: '신규 IT 프로세스',
	noITProcessesFound: 'IT 프로세스가 식별되지 않았습니다',

	/* IT Process - Task */
	itProcessSearchPlaceholder: 'IT 프로세스 검색',
	itProcessHeader: 'IT 프로세스',
	itProcessTasksHeader: 'Task',
	itProcessAddUPD: 'UDP 추가',
	itProcessEditItProcess: 'IT 프로세스 편집',
	itProcessRelateUDP: 'UDP 연결',
	itProcessViewProcess: 'IT 프로세스 보기',
	itProcessNoDataPlaceholder: 'IT 프로세스가 추가되지 않았습니다.<br/> 시작하려면, {addItProcess}를 클릭하십시오.',
	itProcessSourceInstructionalText: '하나 이상의 UDP가 IT 프로세스에 연결되어야 합니다. {itProcessSourceInstructionalTextCreateUdp} 또는 {itProcessSourceInstructionalTextRelateUdp}',
	itProcessSourceInstructionalTextCreateUdp: '신규 UDP 생성',
	itProcessSourceInstructionalTextRelateUdp: '기존 UDP 연결',
	itProcessTargetInstructionalText: 'IT 프로세스에 연결된 UDP가 없습니다',

	/* IT APP IT PROCESSES RELATION*/
	itApplicationHeaderRelate: 'IT Application',
	itProcessesHeaderRelate: 'IT 프로세스',
	itAppNoDataPlaceHolderLabel: 'IT application이 식별되지 않았습니다',
	itAppNoDataPlaceHolder: 'IT application이 식별되지 않았습니다<br /> {identifyItApp}',
	identifyItApp: 'IT application 식별',
	itProcessNoDataPlaceHolderRelationLabel: 'IT 프로세스가 식별되지 않았습니다',
	itProcessNoDataPlaceHolderRelation: 'IT 프로세스가 식별되지 않았습니다<br /> {identifyItProcess}',
	identifyItProcess: 'IT 프로세스 식별',
	editItApp: 'IT Application 편집',
	deleteItApp: 'IT Application 삭제',
	drag: 'IT 프로세스를 관련 IT application으로 끌어 당깁니다',
	// editItProcess: 'Edit IT process',
	deleteItProcess: 'IT 프로세스 삭제',
	unassociatedProcess: '{0} 연결되지 않은 프로세스',
	unassociatedItApplications: '{0} 연결되지 않은 IT application',
	showOnlyUnrelated: '연결되지 않은 항목만 표시 - {0}',
	searchItProcess: 'IT 프로세스 검색',
	searchItApplication: 'IT application 검색',
	itProcessesLabel: 'IT 프로세스',
	itApplicationsLabel: 'IT Application',
	showAllItAplications: '모든 IT application 표시',
	showAllItProcesses: '모든 IT 프로세스 표시',
	relatedToLabel: "List of all <span class='child-entity-count'>{count}</span> <span class='child-entity-name'>{child}</span> related to <span class='parent-entity-object-name'>{parent}</span> ",

	/* IT Process > IT Risk */
	itProcessItRiskNoDataPlaceholder: 'IT 프로세스가 식별되지 않았습니다<br/>{identifyItProcess}',
	itProcessItRiskNoDataPlaceholderTarget: 'IT 프로세스가 식별되지 않았습니다',
	itApplication: 'IT App',
	itGC: 'ITGC',
	addItRiskBtnTitle: 'IT 위험 추가',
	itProcessItRiskUnrelatedITGC: '연결되지 않은 ITGC',
	itProcessItRiskUnrelatedITGCUppercase: '연결되지 않은 ITGC',
	itProcessItRiskNoRisksNoControlsPlaceholder: '이 IT 프로세스에 연결된 IT application과 관련하여 설계 평가가 효과적인 application 통제나 ITDM 통제가 없기 때문에, 이 IT 프로세스에 IT 위험이나 ITGC를 포함할 필요가 없습니다',
	itProcessItRiskNoRisksControlsPlaceholder: '식별된 application 통제와 ITDM 통제에 따라, 이 IT 프로세스에 대해 {itRiskIdentify}',
	itRiskIdentify: 'IT 위험을 식별해야 합니다',
	itProcessItRiskItProcessContentTitle: 'IT 위험 및 ITGC',
	itProcessItRiskItRiskNoItgcRequiredPlaceholder: 'IT 위험에 대응하는 ITGC가 없습니다',
	itProcessItRiskItRiskItgcRequiredPlaceholder: '식별된 모든 위험에는 식별된 ITGC가 하나 이상 있거나 IT 위험에 ITGC가 없다는 지정이 있어야 합니다.<br/>IT 위험에 대응하는 {newITGC} 또는 {existingITGC}를 식별하거나 IT 위험에 대응하는 {noItRisksIdentified}을 지정하십시오.',
	noItRisksIdentified: 'ITGC 없음',
	newITGC: '신규 ITGC',
	existingITGC: '기존 ITGC',
	unrelatedItGCModalMessage: '더 이상 필요하지 않고 연결되지 않은 ITGC를 삭제합니다',
	unrelatedItGCModalNoDataPlaceholder: '연결되지 않은 ITGC 없음',
	removeItRisk: 'IT 위험 제거',
	deleteItRiskConfirmation: 'IT 위험 <b>{0}</b>을(를) 제거하시겠습니까? 이 작업은 IT 위험을 삭제하며 실행 취소할 수 없습니다.',
	relateItGcTitle: 'ITGC 연결',
	relateItGcEntityTitle: 'IT 위험',
	relateItGcDescription: 'IT 위험에 관련된 ITGC를 선택하십시오',
	relateItGcSearchPlaceholder: 'ITGC 검색',
	relateItGcShowSelectedOnlyText: '연결된 ITGC만 표시',
	relateItGcNoDataPlaceholder: '사용 가능한 ITGC 없음. 계속하려면 신규 ITGC를 생성하십시오.',
	relateITSPTitle: 'ITSP 연결',
	relateITSPDescription: 'IT 위험에 관련된 ITSP를 선택하십시오',
	relateITSPSearchPlaceholder: 'ITSP 이름으로 검색',
	relateITSPShowSelectedOnlyText: '연결된 ITSP만 표시',
	relateITSPNoDataPlaceholder: '사용 가능한 ITSP 없음. 계속하려면 신규 ITSP를 생성하십시오.',

	/* IT Process Task Relationship */
	relateUDP: 'UDP 연결',
	relateUDPDescription: 'IT 프로세스에 관련된 UDP task를 선택하십시오',
	relateUDPListHeaderItemName: 'Task 이름',
	relateUDPSearchPlaceholder: 'Task 이름으로 검색',
	relateUDPNoResultsFoundPlaceholder: '결과를 찾을 수 없습니다',
	relateUDPCountLabel: '{0} task',
	relateUDPClose: '닫기',
	relateUDPShowOnlyRelatedTasks: '연결된 task만 표시',
	relateUDPNoDataFoundPlaceholder: '사용 가능한 task 없음',
	relateUDPNoDataPlaceHolder: 'IT 프로세스가 식별되지 않았습니다',

	/* ITGC test strategy */
	itProcessItRiskItGcWithoutDesignEffectiveness: '설계 효과성이 없는 ITGC',
	searchItGC: 'IT 프로세스 검색',
	itGCNoDataPlaceHolder: 'IT 프로세스가 식별되지 않았습니다',
	addItRisks: 'IT 위험 추가',
	itDMHeader: 'ITDM',
	itAppHeader: 'IT App',
	itTestHeader: '테스트',
	itTestingHeader: '테스트',
	itgcHeader: 'ITGCs',
	controlsSelectedHeader: '테스트를 위해 선택한 통제',
	iTRisksAndITGCs: 'IT 위험 및 ITGC',
	NoITGCForITRiskPlaceholder: '이 IT 위험에 대응할 수 있는 ITGC가 IT 환경에 없습니다',
	ITGCsNotIdentifiedRiskNoITGCs: '이 위험에 대한 ITGC가 식별되지 않았습니다. {identifyAnITGC} 또는 {itRiskHasNoITGCs}을 지정합니다.',
	identifyAnITGC: 'ITGC 식별',
	itRiskHasNoITGCs: 'IT위험에 ITGC 없음',

	/**
	 * IT SO > SCOT
	 */
	searchItSO: '서비스조직 검색',
	addItSOBtnTitle: '서비스조직 추가',
	itSoNoDataPlaceHolder: '서비스조직이 식별되지 않았습니다<br/><a>{identifyAnSo}<a/>',
	noItSoDataPlaceHolder: '서비스조직이 식별되지 않았습니다',
	identifyAnSo: '서비스조직 식별',
	soHeader: '서비스조직',
	editSO: '서비스조직 편집',
	deleteSO: '서비스조직 삭제',
	viewSO: '서비스조직 보기',
	controlRelatedToSO: 'SO에 연결된 통제',

	/**
	 * Manage IT SP
	 */
	addITSP: 'ITSP 추가',
	searchPlaceholderManageITSP: 'IT 프로세스 검색',
	noManageITSPDataPlaceholder: 'IT 프로세스가 식별되지 않았습니다',
	itRiskColumnHeader: 'IT 위험',
	itDesignEffectivenessHeader: '설계 효과성',
	itTestingColumnHeader: '테스트',
	itGCColumnHeader: 'ITGC',
	itSPColumnHeader: 'ITSP',
	searchClearButtonTitle: '처리',
	itProcessItRiskUnrelatedITSP: '연결되지 않은 ITSP',
	manageITSPUnrelatedITSPUppercase: '연결되지 않은 ITSP',
	unrelatedITSPModalMessage: '연결되지 않고 더 이상 필요하지 않은 ITSP를 삭제하십시오',
	unrelatedITSPModalNoDataPlaceholder: '연결되지 않은 ITSP 없음',
	noITGCPlaceholderMessageFragment1: '식별된 모든 위험에는 식별된 ITGC가 하나 이상 있거나 위험에 ITGC가 없다는 것을 지정해야 합니다: ',
	noITGCPlaceholderMessageFragment2: 'Identify a',
	noITGCPlaceholderMessageFragment3: 'new ITGC ',
	noITGCPlaceholderMessageFragment4: 'or',
	noITGCPlaceholderMessageFragment5: 'existing ITGC',
	noITGCPlaceholderMessageFragment6: 'that addresses the IT risk or indicate that there are',
	noITGCPlaceholderMessageFragment7: 'no ITGCs',
	noITGCPlaceholderMessageFragment8: 'that address the IT risk',
	addNewITSP: '신규 ITSP 추가',
	addExistingITSP: '기존 ITSP 추가',
	noITSPPlaceholderMessageFragment1: 'ITGC를 비효과적으로 평가했거나 IT 위험에 대응하는 ITGC가 없는 것으로 확인했다면, IT 실증 테스트 절차(ITSP)를 수행하여 비효과적인 ITGC와 연결된 IT 프로세스 내 IT 위험이 악용되지 않았다는 합리적인 확신을 얻을 수 있습니다.',
	noITSPPlaceholderMessageFragment2: '신규 ITSP를 식별하십시오',
	noITSPPlaceholderMessageFragment3: 'or',
	noITSPPlaceholderMessageFragment4: 'relate an existing ITSP',
	noITSPPlaceholderMessageFragment5: '.',
	noITGCsExitForITRisk: 'IT 위험에 ITGC가 없습니다',
	noITSPExitForITRisk: 'IT 위험에 ITSP가 식별되지 않았습니다',
	manageITSPItemExpansionMessage: 'IT 위험',
	noITGCExists: 'IT 위험에 대응하는 ITGC가 없습니다',
	iTGCName: 'ITGC 이름',
	itSPName: 'ITSP 이름',
	operationEffectiveness: '운영 효과성',
	savingLabel: '저장 중',
	deletingLabel: '삭제 중',
	removingLabel: '제거 중',
	itFlowModalDescription: '{itSummaryLink}으로 이동하여 engagement에 더 이상 해당되지 않는 이 object를 편집/제거하십시오.',
	itSummaryLink: 'IT 요약 화면',
	manageITSPYes: '예',
	manageITSPNo: '아니오',

	understandITProcess: 'IT 프로세스 이해',
	activity: '활동',
	unsavedPageChangesMessage: '계속하면 저장되지 않은 변경사항이 지워집니다. 이 페이지를 나가시겠습니까?',
	unsavedChangesTitle: '저장되지 않은 변경사항',
	unsavedChangesLeave: '이 페이지 나가기',
	unsavedChangesStay: '이 페이지 머물기',

	notificationDownErrorMessage: '알림 기능을 잠시 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	notificationUpbutSomeLoadingErrorMessage: '기술적 오류가 발생하여 알림 기능이 작동되지 않습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	markCompleteError: '표시된 모든 문서는 최소 한 명의 작성자와 한 명의 검토자가 sign-off 해야 합니다',
	markCompleteDescription: '활동에 완료 표시하려면 모든 문서는 최소 한 명의 작성자와 한 명의 검토자가 sign-off 해야 합니다',
	lessthan: 'Less than',
	openingFitGuidedWorkflowFormError: 'EY Canvas FIT enablement 양식을 열 수 없습니다',
	timeTrackerErrorFallBackMessage: '시간 추적 기능을 잠시 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	timeTrackerLoadingFallbackMessage: '시간 추적 기능을 잠시 사용할 수 없습니다. 잠시 후 사용할 수 있습니다.',
	priorPeriodRelateDocument: '전기 증거 연결',
	selectedValue: '선택된 값',
	serviceGateway: '서비스 게이트웨이',
	docNameRequired: '이름은 비워둘 수 없습니다',
	docInvalidCharacters: '이름에 다음을 포함할 수 없습니다: */:<>\\?|"',
	invalidComment: 'Comment를 추가할 수 없습니다. 목록에서 여러 항목을 선택했다면 단일 항목만 선택하여 다시 시도하십시오. 문제가 지속되면 페이지를 새로 고침하여 다시 시도하거나 헬프 데스크에 문의하십시오.',
	inputInvaildCharacters: '입력에 다음 문자열을 포함할 수 없습니다: */:<>\\?|',

	// FIT Navigation panel
	relatedActivities: '연결된 활동',
	backToRelatedActivities: '연결된 활동으로 돌아가기',
	backToMainActivities: '주요 활동으로 돌아가기',
	formOptions: '양식 옵션',

	// FIT Sharing
	shareActivity: '활동 공유',
	shareLabel: '공유',
	shareInProgress: '공유 진행 중',
	manageSharing: "이 활동을 공유하려면'EY Canvas FIT enablement 공유 관리' 사용자 권한이 필요합니다. 권한을 관리하려면'팀 관리' 페이지로 이동하거나 다른 팀 멤버에게 문의하십시오.  ",
	dropdownPlaceholderSA: '공유 대상 engagement를 선택하십시오',
	fitSharingModalInfo: '이 활동을 동일한 engagement 또는 동일한 workspace 내 다른 engagement에 있는 활동 하나 이상과 공유하십시오. 선택된 활동이 아직 공유되지 않았다면, 아래 선택된 활동의 응답이 덮어써집니다. 활동이 공유된 상태라면, 하나만 선택할 수 있으며 이 활동의 응답이 덮어써집니다.',
	lastModifiedDate: '최근 수정일: ',
	noActivityToShare: '공유 가능한 활동 없음',
	activityNotShared: '{0}이(가) 공유되지 않았습니다',
	activityShareSuccessfull: '{0}이(가) 공유됐습니다',
	sharedWithAnotherFITActivity: '이 활동이 다른 활동과 공유되어 있습니다',
	sharedActivityWithAnotherCanvas: '활동을 다른 EY Canvas FIT enablement와 공유하십시오',
	shareActivityModalTitle: '활동 공유',
	showRelationshipsTitle: '관계 표시',
	shareActivityEngagement: 'Engagement',
	shareActivityRelationshipsModalTitle: '공유된 활동 관계',
	shareActivityWorkspaceHeading: '이 활동은 동일한 workspace에 있는 아래 engagement와 관련 활동에 공유되고 있습니다',
	shareModalOkTitle: '공유',
	shareModalContinueLabel: '계속',
	selectedActivityInfoLabel: '선택한 engagement에서 최근 수정된 날짜: ',
	noSharedActivityInfoLabel: '이 engagement에는 공유할 동일한 유형의 다른 문서가 없습니다',
	alreadyHasSharedActivityInfoLabel: '선택된 활동이 이미 다른 활동과 공유됐습니다. 현재 활동을 공유하면 선택한 활동에서 현재 활동으로 응답이 동기화됩니다.',
	selectActivityResponsesForSharingLabel: '어떤 문서의 응답이 다른 문서를 대체할지 선택하십시오: ',
	selectActivityResponsesForCurrentRadioLabel: '현재 문서에서 위 선택 문서로 응답을 공유하십시오',
	selectActivityResponsesForSelectedRadioLabel: '위 선택 문서에서 현재 문서로 응답을 공유하십시오',
	selectActivityResponsesWarningEarlierTimeLabel: "The current activity was modified at an earlier time compared to the selected engagement's activity. Please consider this before confirming the sharing option's below the table.",
	selectActivityResponsesWarningModifiedMoreRecentlyLabel: '현재 활동이 선택한 문서 활동보다 최근에 수정됐습니다. 위 공유 옵션을 확인하기 전에 이를 고려하십시오.',
	selectActivityUnsuccessfulMessage: '공유하지 못했습니다. 문제가 지속되면 IT 헬프 데스크에 문의하십시오.',
	otherEngagemntDropdownlabel: 'Workspace 내 다른 engagement: ',
	documentSearchPlaceholder: '문서 검색',
	showOnlySelected: '선택 항목만 보기',

	//FIT Copy
	copyLabel: '복사',
	copyActivity: '복사 활동',
	copyInProgress: '복사 진행 중',
	fitCopyModalInfo: '이 활동 응답을 동일 engagement 또는 동일 workspace 내 다른 engagement에 있는 하나 이상의 활동에 복사합니다',
	dropdownPlaceholderCA: '복사 대상 engagement를 선택하십시오',
	noCopyActivityInfoLabel: '이 engagement에는 복사할 동일한 유형의 문서가 없습니다',
	copyActivityHoverLabel: '이 활동은 이미 다른 활동과 공유되어 있어 복사할 수 없습니다',
	copyActivityWarningEarlierTimeLabel: '현재 활동이 선택한 engagement 활동보다 이른 시간에 수정됐습니다. 복사 옵션을 확인하기 전에 이를 고려하십시오.',

	//Unlink
	unlinkModalTitle: '활동 링크 해제',
	unlinkModalDescription: '선택한 활동 링크를 해제하시겠습니까?',
	unlinkLabel: '링크 해제',
	insufficientPermissionsLabel: '권한 미비',
	unlinkFailMessage: '링크를 해제하지 못했습니다. 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	unlinkSuccessfulMessage: '링크 해제 성공',
	unlinkInProgressLabel: '링크 해제 진행 중',
	unlinkError: '링크 해제 오류',
	unlinkInProgressInfo: '링크 해제 진행 중. 완료하는 데 최대 15분까지 소요될 수 있습니다. 링크 해제가 완료된 후, 이 양식을 닫고 다시 열어야 합니다.',

	/** Manage scot modal labels */
	scotName: 'SCOT 이름',
	scotCategory: 'SCOT 범주',
	estimate: '추정치',
	noScotsAvailablePlaceHolder: '사용 가능한 SCOT 없음. 시작하려면 신규 SCOT을 추가하십시오.',
	addScotDisableTitle: '신규 SCOT을 추가하려면 SCOT 세부정보를 모두 작성하십시오',
	deleteScotTrashLabel: 'SCOT 삭제',
	undoDeleteScotTrashLabel: '삭제 취소',
	scotNameValidationMessage: 'SCOT 이름은 필수입니다',
	scotCategoryValidationMessage: 'SOCT 범주는 필수입니다',
	scotWTTaskDescription:
		'<p>매 기간마다 walkthrough 절차를 수행하여 모든 일상적 및 비일상적 SCOT과 유의적인 공시 프로세스에 대한 이해를 확인합니다. 또한, PCAOB 감사인 경우, 추정 SCOT에 대해 walkthrough 절차를 수행합니다.<br/>통제 의존 전략을 취하는 경우 모든 SCOT과 유의적인 위험에 대응하는 통제에 대해, 관련 통제가 적합하게 설계되고 구축되었는지 확인합니다. 통제 의존 전략이 여전히 적합한 결정인지 확인합니다.<br/><br/>SCOT의 운영이 조서에 정확하게 설명되어 있고, IT 사용으로 발생하는 위험 등 적합한 WCGW 및 (해당하는 경우) 관련 통제를 모두 식별했는지 결론 내립니다.<br/><br/> 실증 단독 전략을 사용하는 추정 SCOT인 경우 실증절차에 기초하여 추정 SCOT에 대한 이해가 적합한지 확인합니다.</p>',

	relate: '연결',
	unrelate: '연결 해제',
	related: '연결됨',
	relatedSCOTs: '연결된 SCOT',
	thereAreNoSCOTsIdentified: '식별된 SCOT이 없습니다',
	selectSCOTsToBeRelated: '연결될 SCOT 선택',

	//OAR Tables
	OARBalanceSheet: '재무상태표',
	OARIncomeStatement: '손익계산서',
	OARCurrentPeriod: '분석일',
	OARAmountChangeFrom: '변동',
	OARPercentageChangeFrom: '% 변동',
	OARNoDataAvailable: '사용 가능한 데이터 없음. 더 진행하려면 {0} 페이지를 검토하고 데이터를 불러오십시오.',
	OARAnnotationLabel: '예상하지 못한 변동 또는 예상한 변동의 결여 사유를 검토하려면 클릭하십시오.',
	OARAnnotationSelectedIcon: '예상하지 못한 변동 또는 예상한 변동의 결여 사유를 문서화하십시오',
	OARAnnotationModalTitle: '주석',
	OARAnnotationModalPlaceholder: '비경상적이거나 예상하지 못한 변동, 또는 예상한 변동의 결여가 나타난 항목을 문서화하십시오',
	OARWithAnnotationLabel: '예상하지 못한 변동 문서화',
	OARAnnotation: '주석',
	OARAccTypeWithAnnotationCountLabel: '계정 유형 내 주석 {0}개',
	OARSubAccTypeWithAnnotationCountLabel: '계정 하위 유형 내 주석 {0}개',
	OARColumnA: 'A',
	OARColumnB: 'B',
	OARColumnC: 'C',
	OARComparative1Period: '비교일 1',
	OARComparative2Period: '비교일 2',
	OARExpand: '계정 분류 확장',
	OARCollapse: '계정 분류 축소',
	OARHelixNavigationLink: '추가 정보를 보려면 EY Helix로 가십시오',
	OARPrintNoDataAvailable: '사용 가능한 데이터 없음',
	OARAdjustedBalance: '조정 잔액',
	OARLegendLabel: '별표(*)가 있는 값은 조정사항이 포함되어 있다는 표시입니다. 추가 세부정보를 보려면 조정사항 모듈로 이동하십시오.',
	OARAccountType: '계정 유형',
	astrixLabel: '*',

	//OAR Helix integration
	helixIntegrationModalDescription: '정의 보류 중인 텍스트입니다',
	OSJETabText: '상대 분개',
	activityAnalysisTabText: '활동 분석',
	preparerAnalysisTabText: '작성자 분석',
	accountMetricsTabText: '계정 지표',
	noAnalyticsData: '표시 가능한 분석 없음',

	printActivitiesTitle: '활동 인쇄',
	printActivitiesModalInfo: '포함하고 싶은 활동을 선택하십시오',
	printActivitiesModalConfirmButton: 'PDF 편집',
	printActivitiesDropdownLabel: 'FIT 활동',
	printActivitiesAll: '모두',
	oarSetupText: '{0} 페이지로 이동하여 EY Helix 프로젝트를 링크하거나 구성하십시오',
	helixNotAvailable: '귀하의 engagement에 사용 가능한 EY Helix가 없습니다',
	dragDropUploadPlaceholder: '문서를 하나 이상 끌어서 놓기 또는 <span>{addDocument}</span> 클릭',

	noTaskAssociatedToastMessage: 'Canvas 양식이 임시 파일에 있으므로 추가된 문서도 임시 파일에 추가됐습니다',

	// chart labels.
	assets: '자산',
	liabilities: '부채',
	equity: '자본',
	revenues: '수익',
	expenses: '비용',
	noAccountsAvailable: '사용 가능한 계정 없음',

	// ALRA
	ALRAFilterByAccount: '계정별 필터링',
	ALRANoRecords: '결과를 찾을 수 없습니다',
	ALRAAssertions: '경영진주장',
	ALRAInherent: '고유 위험요소',
	ALRAHigher: '높은 위험요소',
	ALRAAccountDisclosure: '계정/공시',
	ALRAType: '유형',
	ALRAName: '이름',
	ALRARisks: '위험',
	ALRAC: 'C',
	ALRAEO: 'E/O',
	ALRAMV: 'M/V',
	ALRARO: 'R&O',
	ALRAPD: 'P&D',
	ALRAR: 'R',
	ALRANoRisksAssociated: '이 계정에 연결된 위험 없음',
	ALRAAccountsDisclosureName: '계정/공시 이름',
	ALRAHigherRisk: '높은 위험',
	ALRAHigherInherentRisk: '높은 고유위험',
	ALRAHigherRiskCode: 'H',
	ALRALowerRisk: '낮은 위험',
	ALRALowerInherentRisk: '낮은 고유위험',
	ALRALowerRiskCode: 'L',
	ALRALimitedRiskAccount: '이 계정은 limited risk로 식별됐습니다',
	ALRAInsignificantRiskAccount: '이 계정은 비유의적으로 식별됐습니다',
	ALRADesignations: '지정',
	ALRABalances: '잔액',
	ALRADesignation: '지정',
	ALRAAnalysisPeriod: '분석일',
	ALRAxTE: 'xTE',
	ALRAPercentageChangeFrom: '% 변동',
	ALRAPriorPeriodDesignation: '전기 지정',
	ALRAPriorPeriodEstimate: '전기 추정치',
	ALRAComparativePeriod1: '비교일 1',
	ALRAComparativePeriod2: '비교일 2',
	ALRASelectUpToThreeOptions: '최대 3개 옵션 선택',
	ALRASelectUpToTwoOptions: '옵션을 2개까지 선택합니다',
	ALRAValidations: '유효성 검증',
	ALRANoSignOffs: 'Sign-off 없음',
	ALRAIncompleteInherentRisk: '미완료 고유위험',
	ALRARelatedDocuments: '연결된 문서',
	ALRAGreaterExtent: '더 큰 범위',
	ALRALesserExtent: '더 작은 범위',
	ALRARiskRelatedToAssertion: '위험 연결됨',
	ALRAContributesToHigherInherentRisk: '높은 고유 위험에 연결되고 기여하는 위험',

	// Assess inherent risk
	HigherRiskAssertionWithoutRisksThatContributesToTheHigherInherentRisk: '높은 고유위험에 기여하는 위험이 하나도 없는데 경영진주장 고유위험이 higher로 식별됐습니다. 위험을 연결하고 어떤 위험이 경영진주장의 높은 고유위험에 기여하는지 식별하십시오.',

	//MEST - Multi-entity account Execution Type selection listing
	account: '계정',
	taskByEntity: '기업별 task',
	bodyInformation: '변경사항을 저장하려면 아래에서 내용 불러오기를 클릭해야 합니다',

	/*user search component*/
	seachInputRequired: '검색 입력 필요',
	nameOrEmail: '이름 또는 이메일',
	emailForExternal: '이메일',
	noRecord: '결과를 찾을 수 없습니다',
	userSearchPlaceholder: '이름 또는 이메일을 입력하고 Enter키를 눌러 결과를 확인하십시오',
	userSearchPlaceholderForExternal: '이메일을 입력하고 Enter키를 눌러 결과를 확인하십시오',
	clearAllValues: '모든 값 처리',
	inValidEmail: '유효한 이메일을 입력하십시오',

	//reactive frame
	maxTabsLocked: '최대 허용 탭 수에 도달했습니다. 새 탭을 열려면 탭 하나를 고정 해지하고 닫으십시오.',
	openInNewTab: '신규 탭에서 열기',
	unPinTab: '탭 고정 해지',
	pinTab: '탭 고정',
	closeDrawer: '드로어 닫기',
	minimize: '최소화',

	accountHeader: '계정',
	sCOTSummaryAccountNoDataLabel: '각 SCOT에는 유의적인 계정 또는 공시를 하나 이상 연결해야 합니다. 기존 유의적인 계정 또는 공시를 선택하여 이 SCOT에 연결하십시오',
	sCOTSummaryNoDataLabel: 'SCOT이 생성되지 않았습니다',
	scotSearchNoResultsFound: 'No results found',
	scotSummary225TabsName: {
		[0]: {
			label: '계정별 표시'
		},
		[1]: {
			label: 'SCOT별 표시'
		}
	},

	// Display Account Balances
	currentPeriodAccountBalance: '당기 계정 잔액: ',
	priorPeriodAccountBalance: '전기 계정 잔액: ',

	ALRANoResults: '결과를 찾을 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	associatedRomsCount: '연결된 위험 합계: {0}',
	alraMessage: '계정 & 공시 편집’에서 지정한 사항과 일치하지 않는 계정 지정 응답',
	estimateCategoryResponseNotAlignedToDesignation: '‘추정치 편집’에서 지정한 사항과 일치하지 않는 추정치 범주 응답',


	// Analytics Overview
	analyticsOverviewTitle: '분석 개요',
	noSignificantAccountRecords: '유의적인 계정이 생성되지 않았습니다',
	noSignificantAccountMapped: '선택한 기업에 유의적인 계정이 매핑되지 않았습니다',
	noLimitedAccountMapped: '선택한 기업에 limited 계정이 매핑되지 않았습니다',
	openAnalyticDocumentation: 'Open 분석 문서',
	openLimitedRiskAccountDocumentation: 'Limited risk 계정 문서 열기',
	associatedSCOTs: '연결된 SCOT: ',
	analysisPeriodLabel: '분석일 {0}',
	analysisPeriodChangeLabel: '{0} 대비 % 변동',
	xTELabel: 'xTE',
	risksLabel: '위험',
	comparativePeriod1: '비교일 1 {0}',
	analysisPeriodTitle: '분석일',
	analysisPeriodChangeTitle: '% 변동',
	comparativePeriodTitle: '비교일 1',
	noAccountAvailable: '사용 가능한 계정 없음',

	// Estimates
	titleEstimateCategory: '추정치 범주',
	titleRisks: '위험',

	voiceNoteNotAvailable: '드로어 보기에서 음성 노트와 화면 기록을 사용할 수 없습니다. 이러한 기능을 사용하려면 전체 화면 보기로 바꾸십시오.',

	financialStatementType: {
		[1]: {
			label: '자산'
		},
		[2]: {
			label: '유동자산'
		},
		[3]: {
			label: '비유동자산'
		},
		[4]: {
			label: '부채'
		},
		[5]: {
			label: '유동부채'
		},
		[6]: {
			label: '비유동부채'
		},
		[7]: {
			label: '자본 '
		},
		[8]: {
			label: '수익'
		},
		[9]: {
			label: '비용'
		},
		[10]: {
			label: '영업외수익/(비용)'
		},
		[11]: {
			label: '기타포괄이익(OCI)'
		},
		[12]: {
			label: '기타'
		},
		[13]: {
			label: '계정 유형 '
		},
		[14]: {
			label: '계정 하위 유형'
		},
		[15]: {
			label: '계정 분류'
		},
		[16]: {
			label: '계정 하위 분류'
		},
		[17]: {
			label: '순(이익)/손실'
		}
	},
	accountTypes: {
		[1]: {
			label: '유의적인 계정'
		},
		[2]: {
			label: 'Limited risk 계정 '
		},
		[3]: {
			label: '비유의적인 계정'
		},
		[4]: {
			label: '기타 계정'
		},
		[5]: {
			label: '유의적인 공시 '
		}
	},
	noClientDataAvailable: '사용 가능한 데이터 없음',

	analysisPeriod: '분석일',
	comparativePeriod: '비교일',
	perchangeLabel: '% 변동',

	entityCreateAccountLabel: '계정 & 공시 생성',
	insignificantAccount: '비유의적인 계정',
	noAccountRecords: '계정이 식별되지 않았습니다',
	noAccountsForEntity: '선택된 기업에 매핑된 계정 혹은 공시 없음',
	noLimitedRiskAccountRecords: '사용 가능한 Limited Risk 계정 없음',
	createAccount: '계정 생성',
	createDocument: '문서 생성',
	noAccountResults: '식별된 계정 없음',
	createGroupInvolvementDocument: '관여 양식 생성',
	chooseVersionsToCompare: '비교할 버전을 선택하십시오',
	noTrackChangesOption: '사용 가능한 변경사항 추적 버전 없음',
	trackChangesDefaultMessage: '진행하려면 ‘비교할 버전을 선택하십시오’ 드롭다운에서 버전을 선택하십시오',
	whichRiskContributeToHigherRisk: '어느 위험이 위험이 높은 경영진주장에 기여했습니까?',

	//multi-entity Entity List
	createMultiEntity: '신규 기업',
	editMultiEntity: '기업 편집',
	noEntitiesAvailableCreateNewLink: 'Click here',
	noEntitiesAvailable: '생성된 기업이 없습니다. 시작하려면 {noEntitiesAvailableCreateNewLink}',
	noEntitiesFound: '결과를 찾을 수 없습니다',
	createMultiEntityProfile: '기업 프로필 생성',

	createEntity: '기업 생성',
	includeEntities: 'Multi-entity 목록에는 기업이 하나 이상 포함되어야 합니다. 시작하려면 {createEntity}하십시오.',
	//multi-entity table
	multiEntityCode: '기업 표준 Index',
	multiEntityName: '기업 이름',
	multiEntityGroup: '기업 그룹',
	multiEntityActions: '작업',
	relateMultiEntityUngrouped: '그룹화되지 않음',
	selectAll: '모두 선택',
	entitiesSelected: '기업 선택됨',
	entitySelected: '기업 선택됨',
	meNoEntitiesAvailable: '사용 가능한 기업 없음',
	meSwitchEntities: '기업 변환',
	meSelectEntity: '기업 선택',
	allEntities: '모든 기업',
	noEntitiesIdentified: '식별된 기업 없음',
	contentDeliveryInProcessMessage: '내용 전달 진행 중. 내용이 전달되는 데 10분까지 소요될 수 있습니다.',
	importContent: '내용 불러오기',
	profileSubmit: '프로필 제출',
	importPSPs: 'PSP 불러오기',
	contentUpdateInsufficienRolesLabel: '내용을 업데이트할 권한이 없습니다. Engagement 관리자와 협의하여 충분한 권한을 부여받으십시오.',
	// MEST Switcher
	meEntitySwitcher: '기업 변환기',
	//Error Boundary
	errorBoundaryMessage: '오류가 발생했습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	sectionName: '섹션 이름',
	maxLength: '텍스트는 {number}자를 초과할 수 없습니다',
	required: '필수',
	yearAgo: '년 전',
	yearsAgo: '년 전',
	monthAgo: '개월 전',
	monthsAgo: '개월 전',
	weekAgo: '주 전',
	weeksAgo: '주 전',
	daysAgo: '일 전',
	dayAgo: '일 전',
	today: '오늘',
	todayLowercase: '오늘',
	yesterday: '어제',
	approaching: '임박',

	associatedToInherentRiskFactor: '고유 위험요소에 연결됨',

	createMissingDocument: '누락 문서 생성',
	createMissingDocumentBody: "현재 문서가 누락된 관련 항목에 문서를 생성하려면'확인'을 클릭하십시오 ",
	documentCreationSuccessMsg: '문서 생성 진행 중. 페이지를 새로 고침하여 업데이트하십시오.',

	noRisksRelatedToAssertion: '이 계정 경영진주장에 연결된 위험이 없습니다',

	noAssertionsRelatedToAccount: '이 계정에 연결된 경영진주장 없음',

	sharing: '공유',

	risksUnrelatedToAccountAssertion: '계정 경영진주장에 연결되지 않은 위험',
	cantCompleteTask: '관련 task에 연결된 문서에 sign-off가 누락됐습니다. Task를 열고 누락된 문서를 완료한 후, 다시 완료 표시하십시오.',
	cantCompleteTasksTitle: '완료 표시할 수 없습니다',
	ok: '확인',
	documentsEngagementShareLabel: 'Engagement 내 공유 대상 문서를 선택하십시오',
	documentsEngagementCopyLabel: 'Engagement 내 복제 대상 문서를 선택하십시오',
	lastModifiedon: '최근 수정된 날짜',
	newAccountAndDisclosure: '신규 계정 & 공시',
	newAccountORDisclosure: '신규 계정 또는 공시',

	externalDocuments: '외부 문서',
	noExternalDocumentsAvailable: '사용 가능한 외부 문서 없음',
	addExternalDocuments: '외부 문서 추가',
	relateExternalDocuments: '외부 문서 연결',

	helixNotMappedToAccount: 'EY Helix 데이터가 이 계정에 매핑되지 않았습니다. 계속하려면 매핑을 업데이트하고 데이터를 다시 불러오십시오.',
	trackChangesNotAvailableForSpecialBodyDisplayMessage: '변경사항 추적 기능은 아래 응답에 사용할 수 없습니다',
	noDocumentRelatedObjectsApplicable: 'Object를 이 가이드 업무흐름 양식에 연결할 필요는 없습니다',
	helixViewerLoader: 'Helix 뷰어 로드 중...',
	trackChangesViewDefaultMessage: '특정 응답에는 변경사항 추적 기능이 포함되지 않습니다. 이 사실은 원천 활동 세부사항에서 다음과 같은 메시지로 표시됩니다: “변경사항 추적 기능은 아래 응답에서 사용할 수 없습니다.” 따라서 아래에서 변경사항 알림이 없다는 것이 변경사항의 발생 유무를 나타내는 징후가 될 수 없습니다.',

	//Relate task modal
	relateTasksTitle: 'Task 연결',
	taskLocationLabel: 'Task 위치',
	relateTaskInstructionalText: '문서를 연결해야 하는 task를 추가하거나 제거하십시오. 문서가 증거인 경우, 마지막 task에서 이를 제거하면 임시 파일로 이동합니다.',
	noResultFound: '결과를 찾을 수 없습니다',
	relatedTaskCounter: '{0} task',
	relatedTasksCounter: '{0} task',
	onlyShowRelatedTasks: '연결된 task만 표시',
	relateTaskName: 'Task 이름',
	relateTaskType: '유형',

	/*Relate Entities*/
	relateEntitiesTitle: '기업 연결',
	relateEntitiesSearchPlaceholder: 'Enter키를 눌러 이름별로 검색하십시오',
	relateEntitiesName: '기업 이름',
	relateEntitiesIndex: '기업 표준 Index',
	relatedEntitiesCounter: '{0} 기업',
	relatedEntityCounter: '{0} 기업',
	onlyShowRelatedEntities: '연결된 기업만 표시',
	entity: '기업',

	step01: '단계 01',
	step02: '단계 02',
	shareActivityStep1Description: 'Engagement 및 문서 선택',
	shareActivityStep2Description: '어떤 문서 응답이 다른 것을 대체할지 선택하십시오',
	documentsShareLabel: '아래 선택된 문서의 응답을 나머지 문서와 공유하십시오',
	selectedActivity: '선택된 활동',
	sharedHoverLabel: '이 활동은 이미 다른 활동과 공유되어 있습니다. 이 활동을 공유하면 이 활동의 응답이 모든 공유된 활동으로 동기화됩니다.',
	noAssertionsRelatedLabel: '연결된 경영진주장 없음',

	// Bulk mark complete:
	bulkMarkCompleteInstructionalText: '활동에 완료 표시하려면 모든 문서는 최소 한 명의 작성자와 한 명의 검토자가 sign-off 해야 합니다.',
	bulkMarkCompleteEngagementColumn: 'Engagement',
	bulkMarkCompleteDocumentsMissingSignOffs: 'Sign-off 누락. {bulkMarkCompleteMissingSignOffsClickableText}을(를) 클릭하여 sign-off하십시오.',
	bulkMarkCompleteMissingSignOffsClickableText: '여기',
	bulkMarkCompleteNoAccessToEngagement: '귀하는 이 task가 있는 engagement에 접근 권한이 없습니다',
	bulkMarkCompleteInProgressMessage: '프로세스가 진행 중입니다. 최대 10분이 소요될 수 있습니다. 업데이트하려면 새로 고침하십시오',
	bulkMarkCompleteRelatedDocumentsModalTitle: '문서 sign-off',
	bulkMarkCompleteFilterUnreadyTasks: '문서 sign-off가 누락된 task만 표시',
	bulkMarkCompleteNotAllowedModalTitle: '완료 표시할 수 없습니다',
	bulkMarkCompleteNotAllowedModalDescription: '완료 표시할 task를 하나 이상 선택해야 합니다',
	bulkMarkCompleteRelatedDocumentsModalDescription: '선택한 task에 완료 표시하려면 모든 문서는 최소 한 명의 작성자와 한 명의 검토자가 sign-off 해야 합니다',
	bulkMarkCompleteRelatedDocumentsModalRefreshSignoffs: 'Sign-off 및 노트 새로 고침',
	selectedTaskCounter: '({0}) 선택된 task',
	selectedTasksCounter: '({0}) 선택된 task',

	// Mark complete (old):
	markCompleteNotAllowedModalDescription: '관련 task와 연결된 문서에 sign-off가 누락됐습니다. Task를 열고 누락된 문서를 작성한 후, 완료 표시를 다시 시도하십시오.',
	markCompleteInstructionalText: '활동에 완료 표시하려면 모든 문서에 최소 한 명의 작성자와 한 명의 검토자가 sign-off 해야 합니다.',

	// Adobe Analytics
	aaCookieConsentTitle: 'Welcome to',
	aaCookieContentPrompt: '쿠키를 허용하시겠습니까?',
	aaCookieConsentExplanation: '<p>이 웹사이트를 운영하는 데 반드시 필요한 쿠키 외에도 귀하의 사용자 경험과 당사의 서비스를 개선하기 위해 다음 유형의 쿠키를 사용합니다: 사용자 경험을 향상시키기 위한 <strong>기능 쿠키</strong>(예: 설정 기억), 웹사이트 성능을 측정하고 사용자 경험을 개선하기 위한 <strong>성능 쿠키</strong>, 당사가 광고 캠페인을 실행하고 귀하와 관련된 광고를 제공하도록 제3자가 설정한 <strong>광고/대상선정 쿠키</strong>.</p><p>추가 정보는 <a target="_blank" href="https://www.ey.com/en_us/cookie-policy">쿠키 정책</a>을 참고하십시오.</p>',
	aaCookieConsentExplanationWithDoNotTrack: '<p>이 웹사이트를 운영하는 데 반드시 필요한 쿠키 외에도 귀하의 사용자 경험과 당사의 서비스를 개선하기 위해 다음 유형의 쿠키를 사용합니다: 사용자 경험을 향상시키기 위한 <strong>기능 쿠키</strong>(예: 설정 기억), 웹사이트 성능을 측정하고 사용자 경험을 개선하기 위한 <strong>성능 쿠키</strong>, 당사가 광고 캠페인을 실행하고 귀하와 관련된 광고를 제공하도록 제3자가 설정한 <strong>광고/대상선정 쿠키</strong>.</p><p>귀하의 브라우저에서 Do Not Track 설정을 활성화한 것이 감지됐습니다; 따라서, 광고/대상선정 쿠키가 자동으로 비활성화됩니다.</p><p> 추가 정보는 <a target="_blank" href="https://www.ey.com/en_us/cookie-policy">쿠키 정책</a>을 참고하십시오.</p>',
	aaCookieConsentDeclineOptionalAction: '선택 쿠키를 거부합니다',
	aaCookieConsentAcceptAllAction: '모든 쿠키를 수락합니다',
	aaCookieConsentCustomizeAction: '쿠키 수정',
	aaCookieConsentCustomizeURL: 'https://www.ey.com/en_us/cookie-settings',

	// Cookie Settings
	cookieSettings: {
		title: '쿠키 설정',
		explanation: 'ey.com과 My EY 플랫폼에서 쿠키 사용에 동의하십시오. 아래에 열거된 쿠키 유형을 하나 이상 선택한 후 저장하십시오. 쿠키 유형과 그 목적에 관한 세부정보는 아래 목록을 참조하십시오.',
		emptyCookieListNotice: '이 범주의 쿠키는 이 앱에서 사용되지 않습니다',
		nameTableHeader: '쿠키 이름',
		providerTableHeader: '쿠키 제공자',
		purposeTableHeader: '쿠키 목적',
		typeTableHeader: '쿠키 유형',
		durationTableHeader: '쿠키 기간',
		formSubmit: '내 선택 저장',
		requiredCookieListTitle: '필수 쿠키',
		functionalCookieListTitle: '기능 쿠키',
		functionalCookieAcceptance: '아래 기능 쿠키를 수락합니다',
		functionalCookieExplanation: '기능 쿠키, (귀하가 선택했던 설정을 기억하는 등) 사용자 경험을 향상시킵니다',
		performanceCookieListTitle: '성능 쿠키',
		performanceCookieAcceptance: '아래 성능 쿠키를 수락합니다',
		performanceCookieExplanation: '성능 쿠키, 웹사이트 성능을 측정하고 사용자 경험을 개선하는 데 도움을 줍니다. 성능 쿠키를 사용할 때, 개인 데이터를 저장하지 않으며, 이러한 쿠키를 통해 수집된 정보를 익명으로 집계한 형태로만 사용합니다.',
		advertisingCookieListTitle: '대상선정 쿠키',
		advertisingCookieAcceptance: '아래 광고/대상선정 쿠키를 수락합니다',
		advertisingCookieExplanation: '광고/대상선정 쿠키는 사용자 활동과 세션을 추적하는 데 사용하며 이로써 더욱 개인화된 서비스를 제공할 수 있습니다. (광고 쿠키의 경우) 당사가 광고 캠페인을 실행하고 귀하와 관련된 광고를 제공하도록 제3자가 설정합니다.',
		doNotTrackNotice: '귀하의 브라우저에서 Do Not Track 설정을 활성화한 것이 감지됐습니다; 따라서, 광고/대상선정 쿠키가 자동으로 비활성화됩니다.',
	},
	accountFormsMissing: '{0} 계정에 대한 계정 양식 누락',
	createAccountForms: '계정 양식 생성',
	createAccountFormsDescription: "현재 문서가 누락된 관련 항목에 문서를 생성하려면'확인'을 클릭하십시오 ",
	createMissingDocuments: '현재 관련 계정 문서 누락',
	accountDocumentsCreated: '내용 전달 진행 중. 내용이 전달되는 데 10분까지 소요될 수 있습니다.',

	evidenceMissingPICSignoffs: 'PIC sign-off가 없는 증거',
	evidenceMissingEQRSignoffs: 'EQR sign-off가 없는 증거',
	evidenceMissingPICEQRSignoffs: 'PIC 및/또는 EQR sign-off가 없는 증거',
	evidenceMissingPICSignoffRequirements: 'PIC sign-off 요건이 없는 증거',
	evidenceMissingEQRSignoffRequirements: 'EQR sign-off 요건이 없는 증거',
	evidenceMissingPICEQRSignoffRequirements: 'PIC 및/또는 EQR sign-off 요건이 없는 증거',
	evidenceMissingSignoffs: 'Sign-off가 없는 증거',

	// Bulk task relate
	bulkTaskRelateFailureMessage: '선택한 문서 중 일부를 선택한 task에 연결할 수 없었습니다',
	/*endoflabels*/
	evidenceMissingPreparerOrReviwerSignoffs: '문서 업로드 - 작성자 또는 검토자 Sign-off 누락',

	manageITProcess: 'IT 프로세스 관리',
	manageITRisk: '기술 위험 관리',
	manageITControl: 'IT 통제 관리',
	manageITSP: 'ITSP 관리',
	manageITApp: 'IT application 관리',
	manageSCOT: 'SCOT 관리',
	addAresCustomDescription: '이 가이드 업무흐름 양식에 추가할 내용 유형을 선택하고, 세부정보를 입력한 후, 저장을 클릭합니다',

	documentImportSuccess: '{0}이(가) 생성됐습니다. 내용이 전달되는 데 10분까지 소요될 수 있습니다.',
	documentImportFailure: '문서 생성 실패. 잠시 후 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	formNotAvailable: '일치하는 Canvas 양식을 찾을 수 없습니다',
	selectTask: 'Task를 선택하여 guidance에 연결하십시오',
	canvas: 'Canvas',
	selectEngagement: 'Engagement 선택',

	//Modal Manage sub-scope
	manageSubScopeTitle: '하위 범위 관리',
	manageSubScopeDescription: '하위 범위를 생성하거나, 아래 기존 하위 범위를 편집하거나 삭제하십시오',
	addSubScope: '하위 범위 추가',
	subScopeName: '하위 범위 이름',
	knowledgeScope: 'Knowledge 범위',
	subScopeAlreadyExist: '하위 범위 이름이 이미 있습니다',
	subScopes: '하위 범위',
	notAvailableSubScopes: '사용 가능한 하위 범위가 없습니다',
	SubScopeNameValidation: '하위 범위 이름 길이가 255자를 초과합니다',

	//CRA Summary
	manageAccount: '계정 관리',
	newAccount: '신규 계정',

	noRelatedObjectITProcessFlow: '연결된 object 없음. 시작하려면 object를 연결하십시오.',

	//Add New Flow Chart Steps
	flowChartNewSteps: {
		newStepTitle: '신규 단계',
		placeholderText_1: '아래에 단계 세부정보를 입력하고',
		placeholderText_2: ' 저장 및 닫기’를 선택하여',
		placeholderText_3: ' 마치십시오. 추가 단계를 더 생성하려면',
		placeholderText_4: ' 저장 및 추가 생성’을 선택하십시오',
		columnLabel: '열 (필수)',
		counterOf: 'of',
		counterChar: 'characters',
		stepNameLabel: '단계 이름 (필수)',
		errorMsgStepNameRequired: '단계 이름은 필수입니다',
		stepDescLabel: '단계 설명 (필수)',
		stepDescPlaceholder: '단계 설명 입력',
		errorMsgStepDescRequired: '단계 설명은 필수입니다',
		required: '필수',
		errorMsgStepDescExceedMaxLength: '단계 설명이 최대 허용 글자수를 초과합니다',
		buttonCancel: '취소',
		buttonSaveAndClose: '저장 및 닫기',
		buttonSaveAndCreateAnother: '저장 및 추가 생성',
		errorMsgColumnRequired: '열은 필수입니다',
		headerNameForWCGW: 'WCGW 이름',
		headerNameForControl: '통제 이름',
		headerNameForITApp: 'IT application 이름',
		headerNameForServiceOrganisation: '서비스조직 이름',
		relateLabelForWCGW: 'WCGW 연결',
		relateLabelForControl: '통제 연결',
		relateLabelForITApp: 'IT application 연결',
		relateLabelForServiceOrganisation: '서비스조직 연결',
		designEffectiveness: '설계 효과성',
		testing: '테스트 ',
		lowerRisk: 'Lower risk',
		wcgwNoRowsMessage: 'WCGW이 연결되지 않았습니다. 시작하려면 {0}을(를) 클릭하십시오.',
		controlNoRowsMessage: '통제가 연결되지 않았습니다. 시작하려면 {0}을(를) 클릭하십시오.',
		itAppNoRowsMessage: 'IT application이 연결되지 않았습니다. 시작하려면 {0}을(를) 클릭하십시오.',
		serviceOrganisationNoRowsMessage: '서비스조직이 연결되지 않았습니다. 시작하려면 {0}을(를) 클릭하십시오.',
		wgcwTabLabel: 'WCGW',
		controlsTabLabel: '통제',
		itAppsTabLabel: 'IT application',
		serviceOrganisationTabLabel: '서비스조직',
		connectionSuccessMessage: '연결이 생성됐습니다',
		connectionFailedMessage: '연결을 설정할 수 없습니다. 다시 시도하십시오.',
		selfConnectFailMessage: '원천과 대상은 동일할 수 없습니다',
		connectionDuplicateMessage: '연결이 이미 존재합니다',
		connectionDeleteSuccessMessage: '연결이 삭제됐습니다',
		connectionDeleteFailMessage: '연결을 삭제할 수 없습니다. 다시 시도하십시오.',
		editStepFailMessage: '단계를 편집할 수 없습니다. 다시 시도하십시오.',
		flowchartStepGetByIdFailMessage: '유효하지 않은 단계, 새로 고침하여 다시 시도하십시오.',
		flowchartStepGetByIdFailureMessage: '이 flowchart 단계를 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
		newStepFailureMessage: '신규 단계를 생성할 수 없습니다. 다시 시도하십시오.',
		deleteConnector: '커넥터 삭제',
		edgeConnectorOptions: '커넥터 옵션',
		edgeStartPoint: '시작 포인트',
		edgeEndPoint: 'End Point',
		relateDocumentToFlowchartStepError: '지금은 작업을 완료할 수 없습니다 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
		relateDocumentsOrObjects: '문서 또는 object 연결',
		thisstep: '이 단계에'
	},

	flowChartWCGW: {
		wcgwsCounter: '{0} WCGW',
		wcgwCounter: '{0} WCGW',
		headerName: 'WCGW 연결',
		showOnlyRelatedText: '연결된 항목만 표시',
		noResultsFound: '결과를 찾을 수 없습니다'
	},

	flowchartITAPPSO: {
		showOnlyRelatedText: '연결된 항목만 표시',
		noResultsFound: '결과를 찾을 수 없습니다'
	},

	flowChartITApplication: {
		itApplicationsCounter: '{0} IT application',
		itApplicationCounter: '{0} IT application',
		headerName: 'IT application 연결',
		columnName: 'IT application 이름',
		noDataFound: 'IT application을 찾을 수 없습니다 '
	},

	flowChartITSO: {
		itSOsCounter: '{0} 서비스조직',
		itSOCounter: '{0} 서비스조직',
		headerName: '서비스조직 연결',
		columnName: '서비스조직 이름',
		noDataFound: '서비스조직을 찾을 수 없습니다 '
	},

	flowChartControl: {
		controlsCounter: '통제 연결 {0}',
		headerName: '통제 연결',
		showOnlyRelatedText: '연결된 항목만 표시',
		noResultsFound: '결과를 찾을 수 없습니다',
		noWCGWs: '통제가 생성되지 않았습니다'
	},

	relateSCOT: {
		header: 'SCOT 연결',
		estimate: '추정치',
		scotsCounter: '{0} SCOT',
		scotCounter: '{0} SCOT',
		headerName: 'SCOT 이름',
		showOnlyRelated: '연결된 항목만 표시',
		noResultsFound: '결과를 찾을 수 없습니다',
		noScotCreated: '이 engagement에 생성된 SCOT 없음'
	},

	relatedStepObjects: {
		relatedWCGWs: '연결된 WCGW',
		relatedControls: '연결된 통제',
		relatedDocuments: '연결된 증거',
		relatedITApplications: '연결된 IT application',
		relatedSOs: '연결된 서비스 조직'
	},

	flowchartEditSteps: {
		nextStep: '다음 단계',
		previousStep: '이전 단계',
		editStepTitle: '단계 편집',
		editPlaceholderText_1: '아래에서 단계 세부정보 및 연결된 object를 편집하십시오.',
		editPlaceholderText_2: "'저장 및 닫기’를 클릭하여' ",
		editPlaceholderText_3: '저장하고 flowchart로 되돌아가십시오. 아래 옵션을 사용하여 다른 단계로 이동하면 업데이트사항이 저장됩니다.',
		draftEditStepFailMessage: 'Flowchart를 생성할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
	},

	flowChartStepmoreMenu: {
		edit: '편집',
		delete: '삭제'
	},

	relateEstimate: {
		scot: 'SCOT',
		strategy: 'SCOT 전략',
		type: '유형',
		noSCOT: '각 추정치에 SCOT을 하나 이상 연결해야 합니다.',
		noSCOTmsg: '을(를) 클릭하여 시작하십시오',
		estimate: '추정치',
		routine: '일상적',
		nonRoutine: '비일상적',
		notSelected: '미선택',
		relateSCOTs: 'SCOT 연결',
		remove: '제거',
		noEstimate: '사용 가능한 추정치 없음',
	},

	flowChartStepIcons: {
		wcgws: 'WCGW',
		controls: '통제',
		iTApps: 'IT application',
		serviceOrganisations: '서비스조직'
	},

	flowChartStepIcon: {
		wcgw: 'WCGW',
		control: '통제',
		iTApp: 'IT Application',
		serviceOrganisation: '서비스조직',
		evidence: '증거'
	},

	flowChartErrorMessage: {
		stepOutsideOfTheColumns: 'Steps cannot be placed outside of the flowchart area',
		stepBetweenTheColumns: 'Steps cannot be placed between the columns',
		stepOnTopOrTooCloseToAnotherStep: 'Steps cannot be placed on top of the other steps'
	},

	//Delete Flow Chart Steps
	flowChartStepsDelete: {
		deletestep: '단계 삭제',
		deleteStepModalMessage: '이 단계를 삭제하시겠습니까? 삭제되는 단계에 연결된 WCGW, 통제, IT application, 서비스조직, 증거는 모두 연결이 해제됩니다.',
		cannotBeUndone: '신규 또는 갱신된 고객 연락처',
		deleteStepFailMessage: '단계를 삭제하지 못했습니다. 다시 시도하십시오.',
		deleteDraftStepErrorMessage: '초안으로 생성된 단계가 삭제되지 않았습니다. 이 단계를 삭제하려면, 단계를 선택하고 삭제를 다시 실행하십시오.',
	},
	notEntered: '입력되지 않음',
	estimateCategory: '추정치 범주',
	noResultsFoundWithPeriod: '결과를 찾을 수 없습니다',
	noEstimateAvailable: '사용 가능한 추정치 없음',
	noRelatedObject: '연결된 object 없음',
	relateAnObject: 'Object 연결',
	copyrightMessage: 'Copyright © <year> all rights reserved',
	leadsheet: '리드 시트',
	controlName: '통제 이름',
	noControlAvailable: '사용 가능한 통제 없음',
	independenceError: '미완료 응답은 독립성 제출 전에 모두 완료해야 합니다',
	riskTypeNotAssociated: '새로 추가된 위험이 허용된 위험 유형과 일치하지 않아 아래에 나타나지 않습니다. 허용된 유형의 다른 위험을 더 추가하거나 아래 목록에서 선택하십시오.',
	accountsAndRelatedEstimates: '계정 및 연결된 추정치',
	noEstimatesAssociated: '연결된 추정치 없음',
	noAssertionsAvailable: '사용 가능한 경영진주장 없음',
	noAccountsOrDisclosuresAvailable: '사용 가능한 계정 또는 공시 없음',

	relateEstimateToRisk: {
		riskType: '위험 유형',
		risk: '위험',
		hasestimate: "Has estimate?",
		accounts: '계정',
		isItRelevant: '관련 있습니까?',
		assertions: '경영진주장',
		invalidRiskParentRiskErrMsg: '기록을 찾을 수 없습니다. 계속하려면 페이지를 새로 고침하십시오.',
		noEstimate: '사용 가능한 추정치 없음',
		invalidRelateRiskOrEstimateRelationErrMsg: 'Object가 이미 연결되어 있습니다. 계속하려면 페이지를 새로 고침하십시오.',
		invalidUnRelateRiskOrEstimateRelationErrMsg: 'Object가 이미 연결 해제되어 있습니다. 계속하려면 페이지를 새로 고침하십시오.'
	},

	savingChanges: '변경사항 저장 중',
	showEstimateAccountsWithoutEstimates: '추정치가 없는 추정치 계정 표시',
	showEstimateSCOTsWithoutEstimates: '추정치가 없는 추정 SCOT 표시',
	manageSCOTs: 'SCOT 관리',
	sCOTsAndRelatedEstimates: 'SCOT 및 연결된 추정치',
	relateEstimateToRiskNoDataMessage: '사용 가능한 기록 없음, 해당하는 경우 계정 및 경영진주장을 하나 이상 관련 위험에 연결하십시오',
	maps: '맵',
	mapsUpbutSomeLoadingErrorMessage: '기술적인 오류가 발생하여 맵 기능이 작동하지 않습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	mapsDownErrorMessage: '맵 기능을 잠시 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 이 메시지가 지속되면, 헬프 데스크에 문의하십시오.',
	financialStatements: '재무제표',
	serviceGatewayAutomation: '서비스 게이트웨이 & 자동화',
	priorPeriodCategory: '전기 범주',
	relatedAccountWithColon: '연결된 계정: ',
	noRelatedAccount: '연결된 계정 없음',
	noRetionaleAvailable: '사용 가능한 근거 없음',
	leftNavIconApprovals: '승인',
	editDuplicateSectionHeader: '섹션 세부정보를 편집하고 저장을 클릭하십시오',

	relatedEvidences: '증거 연결',
	relatedEvidencesInstruction: '이 engagement에서 증거 연결',
	relatedTemporaryFilesInstruction: '이 engagement에서 임시 문서 연결',
	noDataLabel: '데이터를 찾을 수 없습니다',
	editDuplicateSection: '섹션 편집',
	showOnlyRelated: '연결된 항목만 표시',
	aiChatbot: 'EYQ Assurance Knowledge',
	StEntityNoRecords: '선택한 기업에 계정 또는 공시가 매핑되지 않았습니다',
	versionLabel: '버전',
	relatedEstimates: '연결된 추정치',
	viewEvidenceRelatedToBody: 'View evidence related to the body',
	selectHeaderFromRail: 'Select a header from left navigation pane to proceed',
	manageITProcesses: 'IT 프로세스 관리',
	rationaleForLR: 'Limited risk 계정 근거',
	rationaleForInsignificant: '비유의적인 계정 근거',
	rationalIsMissing: '근거가 없습니다',
	craSummaryText1: '각 유의적인 계정 또는 공시에는 경영진주장을 하나 이상 연결해야 합니다. Click',
	scotDetails223: {
		relatedAccounts: '연결된 계정',
		scotType: '유형',
		manageScot: 'SCOT 관리',
		editScot: 'SCOT 편집',
		scotNotAvailableMessage: '이 문서에 사용 가능한 SCOT이 없습니다',
		relatedScotNotAvailableMessage: '연결된 SCOT 없음. 시작하려면 정보 페이지에서 SCOT을 연결하십시오',
		risksDocumented: '이 walkthrough에서 문서화된 위험',
		risksAvailableHeader: '예',
		risksNotAvailableHeader: '아니오',
		viewRelatedRisks: '연결된 위험 보기',
		noRelatedAccountsMessage: '연결된 계정 없음'
	},

	scotDetails226: {
		noscotsidentified: 'No SCOTs have been identified'
	},

	scotDetails224: {
		riskRelatedWalkthrough: '이 walkthrough에서 연결된 위험',
		relatedToWTDocuments: 'Related to other WT documents',
		riskNotRelatedWalkthrough: '이 walkthrough에서 연결되지 않은 위험',
		substantiveNotSufficient: '실증 충분하지 않음',
		journalEntry: '분개',
		noDirectRiskSourcesAreAvailable: '연결된 위험 없음',
		scotNotAvailableMessage: '이 문서에 사용 가능한 SCOT이 없습니다',
		relatedScotNotAvailableMessage: '연결된 SCOT 없음. 시작하려면 정보 페이지에서 SCOT을 연결하십시오',
		relatedDocuments: 'Related documents',
		risk: "Risk:",
		riskSpecialCircumstances: 'Risk special circumstances',
		relateInstructionText: "This risk has been identified in another SCOT.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		unrelateInstructionText: "This risk has been identified in the critical path of another walkthrough.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		concurrencyErrorMessage: "This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.",
	},
	ipe: 'IPE',
	scotSummary198: {
		noAccountsDisclosureCreated: '유의적인 계정 또는 공시가 생성되지 않았습니다',
		noScotEstimateIdentified: 'SCOT 또는 추정치가 식별되지 않았습니다',
		noScotIdentified: '식별된 SCOT 없음',
		scots: 'SCOT',
		estimates: '추정치',
		errorMessage: '작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
		noResultsFound: '결과를 찾을 수 없습니다',
		searchAccounts: '계정 검색',
		searchScotEstimates: 'SCOT/추정치 검색',
		accounts: '계정',
		scotsOrEstimate: 'SCOT/추정치',
		accountNotRelatedToScotValidation: '각 유의적인 계정 또는 공시에는 SCOT을 하나 이상 연결해야 합니다. 체크박스를 선택하여 관련 SCOT을 이 유의적인 계정 또는 공시에 연결하십시오.',
		scotNotRelatedToAccountValidation: '각 SCOT에는 유의적인 계정 또는 공시를 하나 이상 연결해야 합니다. 체크박스를 선택하여 이 SCOT을 관련 유의적인 계정 또는 유의적인 공시에 연결하십시오.',
		showValidations: '유효성 검증 표시',
	},
	scotSummary225: {
		relatedScots: '연결된 SCOT',
		relatedAcconts: '연결된 계정',
		scotListHeader: 'SCOT 및 추정치',
		noScotsMessage: '각 유의적인 계정 또는 공시에는 SCOT을 하나 이상 연결해야 합니다. 기존 SCOT을 선택하여 이 유의적인 계정 또는 공시에 연결하십시오.',
		noAccountsMessage: 'No significant accounts or disclosures have been created.',
		noAccountsAvailableOnSearch: 'No results found',
		relateAccounts: 'Relate accounts and disclosures',
		noAccountsCreated: 'No accounts have been created',
		noScotsCreated: 'No SCOTs have been created',
		relateScots: 'Relate SCOTs',
	},
	bodyUnavailableInCCP: '이 내용은 EY Canvas Client Portal를 통해 사용할 수 없습니다',
	pyBalance: '전기 잔액',
	cyBalance: '당기 잔액',
	designationNotDefined: '지정 정의되지 않음',
	controlRiskAssessment: '통제위험 평가',
	first: '처음',
	noImportedTrialBalance: '불러온 시산표 없음',
	placeHolderMessageWhenHelixMappingIsTrue: '{0}을(를) 클릭하여 신규 analyzer를 연결하십시오',
	documentPrintSuccess: 'Document print in progress. It may take up to ten minutes. Once completed, the print will be added to the temporary files.',
	documentPrintError: '문서 인쇄 실패. 잠시 후 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	backToEvidenceWarningMessage: 'This action could not be completed. Please refresh and try again. If issue persists, please contact the Help Desk.',
	rationaleMissingForLR: 'Each limited risk account shall have a rationale provided',
	rationaleMissingForIR: 'Each insignificant account shall have a rationale provided',
	craSummaryText2: ' Each account shall have designation determined. Click',
	contentDrivingEntity: 'Content driving entity',
	contentDrivingEntityPlaceholder: 'Content driving entity has not been selected',
	rationaleForPlaceholder: 'Provide rationale for this account designation',
	contentDrivingEntityRequired: 'Content driving entity (required)',
	refreshContentLayers: 'Refresh content layers',
	noAccessLabel: 'Unauthorized. Contact your administrator and try again.',
	copyForHelpDeskDetails: 'Copy for Help Desk details',
	copyForHelpDeskDetailsSuccess: 'Details copied to clipboard',

	//toast activity as guest user
	sharedGuidedworkflowEvidenceWarning: 'This is a shared Guided Workflow Activity. The objects and evidence exist in the original engagement and will not be added to this engagement upon unlink. See <a style="color: #467cbe" href="https://live.atlas.ey.com/#library/104?pref=20058/9/5" target="_blank">enablement here</a> for further details.',
	sharedGuidedworkflowResponseWarning: "This is a shared Guided Workflow Activity. The responses are being shared with other activities in the same workspace. View relationships by accessing the'Show relationships' menu item from this activity's summary section."
};

export const groupStructure = {
	createComponent: '신규 부문',
	deleteComponent: '부문 삭제',
	manageComponents: '부문 관리',
	emptyComponents: '부문이 생성되지 않았습니다. 시작하려면 {newComponent}을(를) 생성하십시오.',
	scope: '범위',
	role: '역할',
	pointOfContact: '연락처',
	linkRequest: '링크 요청',
	instructions: '지침',
	instructionsSent: '지침 전송됨',
	status: '상태',
	createComponentInstructionalText: '아래에 부문 세부정보를 입력하고 <b>’저장 및 닫기’</b>를 선택하여 마칩니다. 다른 부문을 생성하려면 <b>‘저장 및 추가 생성’</b>을 선택하십시오.',
	componentName: '부문 이름',
	region: 'Region',
	notUsingCanvas: 'EY Canvas 미사용',
	referenceOnly: '참조 전용',
	saveAndCreateAnother: '저장 및 다른 부문 생성',
	dueDate: '만료일',
	components: '부문',
	allocation: '배정',
	documents: 'Evidence',
	discussions: '토의',
	EDAP: 'EDAP',
	siteVisits: '현장 방문',
	reviewWorkComponent: '수행된 작업 검토',
	other: '기타',
	significantUpdates: '유의적인 업데이트',
	executionComplete: '실행 완료',
	gaRoleTypesLabel: [{
		id: 1,
		displayName: 'Primary'
	},
	{
		id: 2,
		displayName: 'Regional'
	},
	{
		id: 3,
		displayName: '부문'
	}
	],
	gaLinkStatusLabel: [{
		id: GALinkStatus.NotSent,
		displayName: '보내기'
	},
	{
		id: GALinkStatus.Sent,
		displayName: '전송됨/수락되지 않음'
	},
	{
		id: GALinkStatus.ComponentNotUsingCanvas,
		displayName: 'EY Canvas 미사용'
	},
	{
		id: GALinkStatus.ReferenceOnly,
		displayName: '참조 전용'
	},
	{
		id: GALinkStatus.Accepted,
		displayName: '수락됨 '
	},
	{
		id: GALinkStatus.Rejected,
		displayName: '거절됨'
	},
	{
		id: GALinkStatus.Unlinked,
		displayName: '링크 해제됨'
	},
	{
		id: GALinkStatus.Pending,
		displayName: '전송됨/수락되지 않음'
	}
	],
	notAvailable: '사용 불가능',
	search: labels.searchPlaceholder,
	noResultsFound: '결과를 찾을 수 없습니다',
	noComponentsFound: '부문을 찾을 수 없습니다',
	contentSwitcher: [{
		id: gaRoleTypes.primary,
		displayName: 'Primary'
	},
	{
		id: gaRoleTypes.component,
		displayName: '부문'
	},
	{
		id: gaRoleTypes.regional,
		displayName: 'Region'
	}
	],
	gaRegionTypesLabel: {
		id: gaRegion.notApplicable,
		displayName: '해당 없음'
	},
	//TODO: To be removed
	pointOfContactValues: [{
		id: pointOfContactTypes.EYcontact,
		displayName: 'EY 연락처'
	},
	{
		id: pointOfContactTypes.externalContact,
		displayName: '외부 연락처'
	}
	],
	saveAndClose: labels.modalSaveAndClose,
	cancelBtn: labels.modalCancelTitle,
	gaScopesValues: [{
		id: gaScopeType.full,
		displayName: 'Full'
	},
	{
		id: gaScopeType.specific,
		displayName: 'Specific'
	},
	{
		id: gaScopeType.specifiedAuditProcedures,
		displayName: 'Specified procedures'
	},
	{
		id: gaScopeType.review,
		displayName: 'Review'
	}
	],
	edit: labels.edit,
	delete: labels.delete,
	tooltipIcon: '부문이 그룹감사 지침 수신 및 interoffice 산출물 제출에 EY Canvas를 사용하지 않을 때 이 옵션을 선택하십시오',
	tooltipReferenceIcon: '<b>참조 전용</b>으로 지정된 부문은 조직 목적으로만 사용됩니다. 이 부문 enagement는 링크 요청, 지침, task를 수신하지 않으며 이 primary 팀 engagement도 그룹 task를 수신하지 않습니다.',
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	modalConfirmBtnLabel: labels.confirmLabel,
	clear: '처리',
	clearUpper: labels.clear,
	nameOrEmail: 'EY 연락처 이메일 입력',
	editComponent: '부문 편집',
	editComponentInstructionalText: '아래에서 부문 세부정보를 편집하고 <b>\저장\</b>을 선택하여 마칩니다',
	linkAlreadyAcceptedInfo: '링크 요청이 이미 부문 팀에 전송되었기 때문에 이메일 필드만 편집할 수 있습니다.',
	sendAll: '모두 보내기',
	send: '보내기',
	resend: '재전송',
	scopeAndStrategy: '범위 및 전략',
	execution: '실행',
	conclusion: '결론',
	reportingForms: '보고 양식',
	manageGroupPermission: '이 작업을 수행할 <b>그룹 관리</b> 권한이 없습니다. Engagement 관리자에게 <b>그룹 관리</b> 권한을 요청하십십시오.',
	manageComponentModalDesc: '신규 부문을 생성하거나 아래 기존 부문을 편집하거나 삭제하십시오',
	editLinkInfo: '링크 요청이 이미 부문 팀에 전송되었기 때문에 이메일 필드만 편집할 수 있습니다.',
	invalidPointOfContact: '링크 요청을 보내려면 연락처가 필요합니다. 부문을 편집하여 연락처를 추가하십시오.',
	manageComponentModalActions: '작업',
	manageComponentModalComponents: '부문',
	manageComponentModalDelete: '삭제',
	noThereAtLeastOneComponentToSendAll: '링크 요청을 보내기에 적합한 상태인 부문이 없습니다. 링크 요청을 보내기 위해서는 부문 상태가 <b>보내기</b> 또는 <b>재전송</b>이어야 합니다.',
	showKnowledgeDescription: 'Knowledge의 제목 및 설명 표시',
	hideKnowledgeDescription: 'Knowledge의 제목 및 설명 숨기기',
	instructionName: '지침 이름 입력',
	instructionDescriptionPlaceholder: '지침 설명 입력',
	selectDueDate: '만료일 (필수)',
	show: '표시',
	allocationHeader: '배정',
	allocationInstructionForKnowledge: 'Knowledge 지침은 범위별로만 배정할 수 있습니다. 아래에서 관련 범위를 선택하십시오.',
	allocationInstructionForCustom: '사용자지정 지침은 범위별 또는 부문별로 배정할 수 있니다. 아래에서 지침 배정을 선택하고, 관련 범위 또는 부문에 배정하십시오.',
	allocateScope: '범위에 배정',
	allocateComponent: '부문에 배정',
	pillScopesPlural: '범위',
	pillScopesSingular: '범위',
	pillComponentsPlural: '부문',
	pillComponentsSingular: '부문',
	selectScopesPlaceholder: '범위 선택',
	selectComponentsPlaceholder: '부문 선택',
	searchNoResultFoundText: labels.searchNoResultFoundText,
	newCustomInstruction: '신규 사용자지정 지침',
	instructionNameNewCustomInstruction: '지침 이름',
	addCustom: '사용자지정 추가',
	custom: '사용자지정',
	required: '필수',
	remove: '제거',
	selectAll: '모두 선택',
	unselectAll: '모두 선택 해제',
	lowerPoC: '연락처',
	editPoCTooltip: '유효하지 않거나 없는 연락처. 링크 요청을 전송하려면 연락처를 편집하십시오.',
	recomendationType: [{
		id: 1,
		label: '필수 '
	},
	{
		id: 2,
		label: '선택사항'
	},
	{
		id: 3,
		label: '해당 없음'
	}
	],
	confirmLabel: labels.confirmLabel,
	deleteComponentInstructionalText: '<b>그룹 구조에서 이 부문을 삭제하시겠습니까?</b><br /> 부문을 삭제하면 부문과 링크가 제거되고 engagement 간에 더 이상 문서를 교환할 수 없습니다. 또한, 부문, 계정, 지침 간 연결이 모두 삭제됩니다.',
	noActivitiesAvailable: '사용 가능한 활동 없음',
	relatedComponents: '연결된 부문',
	relatedComponentsSingular: '연결된 부문',
	relatedComponentsPlural: '연결된 부문',
	publish: '발간',
	publishModalHeader: '변경사항 발간',
	publishChangesInstructional: '<b>그룹 지침 요약에 변경사항을 발간하시겠습니까?</b><br />이전에 발간한 그룹 지침 세트는 덮어써집니다. 변경사항이 발간되고 나면, 그룹 지침 요약에서 업데이트된 지침을 보낼 수 있습니다.',
	publishManageGroupPermission: '이 작업을 하려면 그룹 관리 권한이 있어야 합니다. Engagement 관리자에게 권한을 요청하십십시오.',
	lastPublished: '최근 발간됨: ',
	publishChangesNotAvailable: '아직 사용할 수 없음',
	noRecordsFound: labels.noRecordsFound,
	deleteInstruction: '지침 삭제',
	deleteInstructionInstructionalText: '<b>지침을 삭제하시겠습니까?</b><br />이 작업은 실행 취소할 수 없습니다.',
	sendInstructionsTitle: '지침 보내기',
	sendInstructionsInstructionalText: '원래 페이지에서 ‘발간’을 클릭하여 최신 지침이 발간되도록 하십시오. 그러고 나서, 아래 부문 지침을 검토하고 ’보내기’를 선택하여 부문 engagement에 지침을 전송하십시오.',
	instructionsAlreadySent: '최신 지침 버전이 이미 전송됐습니다',
	missingDueDates: '보고 양식 만료일 누락',
	createInstructionsModalButton: '지침 생성',
	createInstructionsModalActionToastMessageStart: 'Group risk assessment instructions are missing for',
	createInstructionsModalActionToastMessageEnd: ' 부문에 대한 그룹 위험평가 지침이 누락됐습니다',
	createInstructionsModalDescription: '다음 Full scope 및 Specific scope 부문에 배정된 그룹 위험평가 지침이 없습니다. <b>생성</b>을 선택하면 아래 열거된 각 부문에 대한 그룹 위험평가 지침이 생성됩니다.',
	createInstructionsModalScope: '범위',
	createInstructionsModalHeader: '지침 생성',
	createInstructionsModalmodalConfirmBtnLabel: '생성',
	createInstructionsModalmodalCancelBtnLabel: '취소',
	createInstructionsModalmodalCloseBtnTitletip: '닫기',
	createInstructionsModalNewGraInstructionDescription: '귀하의 부문과 관련된 계정 위험평가가 포함되어 있습니다. 위험평가를 검토하고 engagement에 식별된 계정과 위험이 있는지 확인하십시오. 현지에서 식별한 추가 위험이 있거나 부문 팀이 동의하지 않는 경우 primary team과 커뮤니케이션하여 primary team과 부문  팀 둘 다 위험평가를 조정하도록 해야 합니다.',
	createInstructionsModalErrorMessage: '다음 부문에 대한 그룹 위험평가 지침 생성 실패: <b>{0}</b>. 페이지를 새로 고침하여 다시 시도하십시오.',
	createInstructionsDuplicatedModalErrorMessage: '그룹 위험평가 지침 생성 실패. 지침 이름은 중복될 수 없습니다',
	gaLinkActionTooltip: {
		NotUsingCanvasLabel: 'EY Canvas 미사용',
		NotUsingCanvas: '<b>보내기</b>를 클릭하면 이 부문에 대한 Primary <br/> 그룹 task가 생성되지만 <br/> 지침은 전송되지 않습니다.',
		NotLinkedLabel: '링크되지 않음',
		NotLinked: '링크 요청이 <br/>부문 팀으로 전송되지 않았습니다. 지침을 전송하려면 링크 <br/>요청을 전송하십시오.',
		Unlinked: '링크 해제됨'
	},
	viewHistory: '기록 보기',
	viewSentInstructionsTitle: '전송된 지침 보기',
	save: labels.saveLabel,
	cancel: labels.cancelLabel,
	viewHistoryInstructionalText: '부문 팀에게 전송된 이전 버전의 지침을 보려면 지침을 선택하십시오',
	viewHistorySelectInstruction: '지침 선택',
	viewHistoryDateSent: '전송 날짜: ',
	viewHistoryStatus: '상태: ',
	viewHistoryStatusAccepted: '수락됨',
	viewHistoryStatusPending: '보류 중',
	viewHistoryStatusRejected: '거절됨',
	viewHistoryStatusSystemError: 'System error',
	viewHistorySelectVersion: '버전 선택',
	noAccountsFound: '이 engagement 또는 다른 engagement에서 계정이나 공시를 찾을 수 없습니다. <br />{link}을(를) 선택하여 신규나 기존 계정 또는 공시를 생성하거나 편집하십시오.',
	generalCommunications: '일반 커뮤니케이션',
	reportingDeliverables: '보고 산출물',
	changesPublishedNotSent: '변경사항 미전송',
	changesPublishedBrNotSent: '변경사항<br/> 미전송',
	changesPublishedNotSentYes: '예',
	deleteSubScopeInstructionalTextModal: '<br/>선택한 하위 범위를 삭제하시겠습니까?',
	deleteSubScopeTitleModal: '하위 범위 삭제',
	riskAssessmentModal: {
		headerText: '위험평가',
		modalCloseBtnTitletip: labels.close,
		manageAndDisclosures: '계정 & 공시 링크 관리',
		next: '다음 부문',
		back: '이전 부문'
	},
	riskAssessment: '위험평가',
	preview: '미리보기',
	accountsAndDisclosureSummary: '계정 및 공시',
	noAccountSnapshotPlaceholder: '이 부문에 표시할 계정 데이터가 없습니다',
	createOversightProjectButtonLabel: 'EY Canvas Oversight 프로젝트 생성',
	createOversightProjectTitle: '이 primary engagement와 함께 EY Canvas Oversight 프로젝트를 생성하시겠습니까?',
	createOversightProjectDescription: '이 그룹 구조에서 식별된 지역 및/또는 부문 EY Canvas engagement는 EY Oversight 프로젝트 설정의 일부로 자동으로 채워집니다',
	createOversightModalHeader: 'EY Canvas Oversight 프로젝트 이름',
	createOversightModalDescription: 'EY Canvas Oversight 프로젝트 이름을 입력하십시오',
	createOversightModalTextLabel: '프로젝트 이름',
	projectRedirectionButtonLabel: 'EY Canvas Oversight 프로젝트',
	projectAssociationTextLabel: '이 engagement에 연결된 EY Canvas Oversignt 프로젝트가 있습니다',
	sendLinkDisableTooltip: '이 engagement는 그룹 감사 흐름에 있는 부문을 포함하여 복사됐습니다. 링크를 다시 전송할 수 없습니다. 필요한 경우, 신규 부문을 생성한 후 링크를 전송하십시오.',
	instructionsCannotBeSentUntilPublished: 'Instructions cannot be sent until they are published.'
};

export const groupInvolvement = {
	NoComponentsAvailables: '부문이 생성되지 않았습니다. <b>부문 관리</b>로 시작하십시오.',
	GroupInvolvementToastMsgStart: ' 부문에 대한 그룹 관여 양식이 누락됐습니다',
	GroupInvolvementToastMsgEnd: '.',
	CreateGroupInvolvementHeader: '관여 양식 생성',
	GroupInvolvementInstructionalText: '다음 부문에 배정된 관여 양식이 없습니다.<br/> &#39;<b>생성</b>&#39;을 선택하면 아래 열거된 각 부문에 대한 그룹 관여 양식이 생성됩니다.',
	createGroupInvolvementDocumentErrorMessage: '다음 부문에 대한 그룹 관여 문서 생성 실패: <b>{0}</b>. 페이지를 새로 고침하여 다시 시도하십시오.',
	createGroupInvolvementDocumentSuccessMessage: '그룹 관여 양식이 생성됐습니다. 사용 가능한 문서를 보려면 30초 후에 페이지를 새로 고침하십시오.',
	involvementTypePlanned: '관여 유형 계획됨',
	significantUpdatesToPlannedInvolvement: '계획된 관여에 대한 유의적인 업데이트',
	executionComplete: '실행 완료',
	generateGroupInvolvementCommunications: '관여 양식 인쇄',
	generateGroupInvolvementInstructionalText: '다음 부문에 연결된 그룹 관여 양식이 있습니다. 아래에서 하나의 문서에 어느 부문 그룹 관여 양식을 포함할지 선택하십시오.<br /><br /> 부문을 선택하고 나서 <b>생성</b>을 선택하면 아래 열거된 각 부문 그룹 관여 문서로 그룹 관여 문서가 생성됩니다.',
	componentTeams: '부문 팀',
	noComponentsSelectedErrorMessage: '그룹 관여 커뮤니케이션을 생성할 부문을 선택하십시오',
	documentName: '{taskName} 그룹 관여 패키지',
	selectAll: groupStructure.selectAll,
	unselectAll: groupStructure.unselectAll,
	modalConfirmBtnLabel: groupStructure.createInstructionsModalmodalConfirmBtnLabel,
	modalCancelBtnLabel: groupStructure.cancelBtn,
	modalCloseBtnTitletip: groupStructure.modalCloseBtnTitletip
};

export const itPlanning = {
	supportingITColumnsHeaders: {
		applicationTool: {
			name: 'Applications/Tools'
		},
		network: {
			name: '네트워크'
		},
		database: {
			name: '데이터베이스'
		},
		operatingSystem: {
			name: '운영 시스템'
		}
	},
	relatedITProcessesColumnsHeaders: {
		relatedITProcess: 'Related IT processes',
		category: 'Category'
	},
	itPlanningPlaceholders: {
		smartEvidenceSourceEntityId: '이 문서에 연결된 기술을 사용할 수 없습니다',
		smartEvidenceSourceId: '연결된 object 없음. 시작하려면 object를 연결하십시오.',
	},
	relatedITProcessesPlaceholders: {
		smartEvidenceSourceEntityId: 'Related IT process not available for this document',
		smartEvidenceSourceId: '연결된 object 없음. 시작하려면 object를 연결하십시오.',
		relatedITProcessEmpty: 'No IT process related to the technology'
	},
	noTechnologiesIdentified: '기술이 식별되지 않았습니다',
	supportingITEmpty: '기술에 연결된 지원 application/도구 없음',
	supportingITNetworkEmpty: '기술에 연결된 지원 네트워크 없음',
	searchPlaceholder: '검색',
	newTechnology: '신규 기술',
	noSupportingDatabases: '기술에 연결된 지원 데이터베이스 없음',
	createEntityFormDocument: '문서 생성',
	noSupportingOperatingSystem: 'No supporting operating systems related to the technology',
	manageTechnology: 'Manage technology'
};

export const itRiskFactors = {
	accepted: '수락됨',
	rejected: '거절됨',
	accept: '수락',
	reject: '거절',
	rejectionRationale: '거절 근거',
	rejectionCategory: '거절 범주',
	rejectionRationaleRequired: 'Rejection rationale (required)',
	rejectionCategoryRequired: 'Rejection category (required)',
	riskName: 'Risk name',
	smartEvidenceValidations: {
		smartEvidenceSourceEntityId: '이 문서에 위험요소를 사용할 수 없습니다',
		smartEvidenceSourceId: 'No related object. Relate an object to get started.'
	},
	manageChangePlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: '모든 위험이 거절됐습니다'
	},
	manageOperationsPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	},
	manageAccessPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	},
	SDLCPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: '모든 위험이 거절됐습니다'
	},
	manageSecuritySettingsPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	}
};

export const rejectionTypeResource = [{
	id: rejectionType.itRiskOther,
	label: 'ITRisk Other'
},
{
	id: rejectionType.itRiskOption2,
	label: 'ITRisk - "Option 2"'
},
{
	id: rejectionType.itRiskOption3,
	label: 'ITRisk - "Option 3"'
}
];

export const sampleList = {
	newSample: '신규 표본',
	createSampleModalDescription: '아래에 표본 세부정보를 입력하고 <b>{0}</b>’을(를) 선택하여 마칩니다. 다른 표본을 생성하려면, <b>{1}</b>을(를) 선택하십시오.',
	saveAndCreateAnother: '저장 및 다른 부문 생성',
	saveAndClose: '저장 및 닫기',
	sampleDescription: '표본 설명 (필수)',
	sampleDate: '표본 날짜 (필수)',
	sampleListId: '표본 목록 ID',
	ok: '확인',
	addSample: '표본 추가',
	cancel: '취소',
	saveAndCloseForHeader: '저장 및 닫기',
	saveAndCreateAnotherHeader: '저장 및 추가 생성',
	required: '필수',
	description: '표본 설명',
	date: '날짜',
	attributeStatus: '정보',
	tags: '태그',
	open: '열기',
	notApplicableLabel: '해당 없음',
	notPresent: '표시되지 않음',
	present: '표시됨',
	pagingShowtext: '표시',
	placeHolderMessage: '사용 가능한 표본 없음. 시작하려면 {clickHere}을(를) 클릭하십시오.',
	noSampleListAvailable: '사용 가능한 표본 목록 없음',
	editSample: '표본 편집',
	editSampleDescription: '아래에서 표본 세부정보를 편집하고 <b>{0}</b>’을(를) 선택하여 마칩니다',
	editSampleSave: '저장',
	sampleCanNotBeCreated: '이 문서에 대한 표본이 생성될 수 없습니다',
	noRelatedObject: '연결된 object 없음. 시작하려면 object를 연결하십시오.',
	noResultsFound: '결과를 찾을 수 없습니다',
};

export const AdditionDocumentationLabels = {
	addAdditionalDocumentation: '추가 문서 추가',
	editAdditionalDocTitle: '추가 문서 편집',
	removeAdditionalDocumentation: '추가 문서 제거',
	cancel: '취소',
	save: '저장',
	of: 'of',
	additionalDocTitlePlaceholder: '추가 문서 (필수)',
	additionalDocTitle: '추가 문서 (필수)',
	remove: '제거',
	enterAdditionalDocTitle: '아래에 추가 문서를 입력하고 <b>’{0}’</b>을(를) 선택하여 마칩니다',
	editAdditionalDocDesc: '아래에서 추가 문서를 편집하고 <b>’{0}’</b>을(를) 선택하여 마칩니다',
	characters: 'characters',
	required: '필수',
	descriptionMaxLengthError: '응답이 최대 허용치를 초과합니다',
	attributeIndexLabel: '속성 index',
};

export const sampletAttributeConstants = [{
	id: 1,
	label: '처리 전 '
},
{
	id: 3,
	label: '표시됨'
},
{
	id: 7,
	label: 'Comment와 함께 표시됨'
},
{
	id: 5,
	label: '표시되지 않음'
},
{
	id: 4,
	label: '해당 없음'
},
];

export const groupInstructions = {
	ALRAPackageModalTitle: 'ALRA 패키지 이름',
	ALRAPackageModalInstructionalText: '증거에 추가하려는 ALRA 패키지 이름을 입력하십시오',
	ALRAPackageModalNameField: '이름 입력',
	ALRAPackageSuccessToastMessage: '패키지 생성 프로세스가 시작됐습니다. 완료까지 최대 10분이 소요될 수 있습니다.',
	ALRAPackageInProgressToastMessage: '패키지 생성 과정이 진행 중입니다, 완료까지 최대 10분이 소요될 수 있습니다.',
	delete: labels.delete,
	deleteSectionModalTitle: labels.deleteSection,
	deleteSectionInstructionalText: '<b>섹션을 삭제하시겠습니까?</b><br />이 작업은 실행 취소할 수 없습니다.',
	deleteSectionTooltipText: '지침을 삭제해야<br /> 섹션을 삭제할 수 있습니다',
	modalConfirmBtnLabel: labels.confirmLabel,
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	missing: '누락',
	sendAllModalTriggerButton: '모두 보내기',
	sendAllModalTooltipText: '부문 팀에게 전송할 수 있는 지침이 없습니다',
	publishModalTooltipText: '그룹 지침은 전송하기 전에 발간해야 합니다. 지침이 발간되면, 변경사항은 신규 지침으로 저장되어 이전 지침 버전을 덮어씁니다. 그 후, 이러한 신규 지침을 부문 팀에 전송할 수 있습니다.',
	sendAllModalErrorMessage: 'Group instructions for the following Components were not sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk. <br /> <b>{0}</b>',
	sendAllModalHeaderText: '지침 모두 보내기',
	sendAllModalConfirmBtnLabel: '보내기',
	sendAllModalCancelBtnLabel: '취소',
	sendAllModalCloseBtnTitletip: '닫기',
	sendAllModalDescription: '<b>보내기</b>를 선택하여 다음 부문 팀에게 지침을 전송합니다',
	generateGroupRiskAssessmentCommunications: '그룹 ALRA 생성',
	bulkALRAPackageName: '{instructionName} 계정 수준 위험평가 패키지',
	groupInstructionSummaryReport: '그룹 지침 요약 보고서',
	groupInstructionSummaryReportTitletip: '그룹 지침 세부정보, 지침 기록 및 부문/계정 매핑 변경사항 보기 및 내보내기',
	exportGroupRiskAssessment: '요약 내보내기',
	reportingDeliverables: groupStructure.reportingDeliverables,
	groupRiskAssessment: '그룹 위험평가'
};

export const sectionTitles = [{
	id: KnowledgeSectionIds.GeneralCommunications,
	sectionTitle: groupStructure.generalCommunications
},
{
	id: KnowledgeSectionIds.ScopeOfWork,
	sectionTitle: '업무 범위'
},
{
	id: KnowledgeSectionIds.ReportingForms,
	sectionTitle: groupStructure.reportingDeliverables
},
{
	id: KnowledgeSectionIds.ProceduresPerformedCentrally,
	sectionTitle: '중앙에서 수행된 절차'
},
{
	id: KnowledgeSectionIds.GroupRiskAssessment,
	sectionTitle: groupInstructions.groupRiskAssessment
},
{
	id: KnowledgeSectionIds.OtherCommunications,
	sectionTitle: '기타 커뮤니케이션'
}
];

export const groupAuditToolbar = {
	search: labels.placeholderForSearch
};

export const AccountType = [{
	id: 1,
	accounttypename: '유의적인 계정 '
},
{
	id: 2,
	accounttypename: 'Limited risk 계정'
},
{
	id: 3,
	accounttypename: '비유의적인 계정 '
},
{
	id: 4,
	accounttypename: '기타 계정'
},
{
	id: 5,
	accounttypename: '유의적인 공시 '
}
];

export const PriorityType = [{
	value: 1,
	label: 'Low'
},
{
	value: 2,
	label: 'Medium'
},
{
	value: 3,
	label: 'High'
},
{
	value: 4,
	label: 'Critical'
}
];

export const AccountSummaryAccountType = [{
	id: '0',
	accounttypename: '모든 계정'
},
{
	id: '1',
	accounttypename: '유의적인 계정'
},
{
	id: '2',
	accounttypename: 'Limited risk 계정'
},
{
	id: '3',
	accounttypename: '비유의적인 계정'
},
{
	id: '4',
	accounttypename: '계정 - 기타 '
},
{
	id: '5',
	accounttypename: '유의적인 공시 '
}
];

export const TaskStatus = [{
	id: 1,
	status: '열기'
},
{
	id: 2,
	status: '진행 중'
},
{
	id: 3,
	status: '리뷰 중'
},
{
	id: 4,
	status: '완료됨'
},
{
	id: 5,
	status: '제거됨'
}
];

export const reviewNoteLabels = {
	/*Review Notes*/
	emptyNoteDetailsMessage: '세부정보를 보려면 노트를 선택하십시오. 한꺼번에 제어하려면, control 키나 shift 키를 이용하여 여러 리뷰노트를 선택하십시오. 개별 노트에서 작업하려면 목록에서 해당 노트를 선택하십시오.',
	documentReviewNotesLabel: '문서 노트',
	addNewReviewNoteButtonText: '노트 추가',
	noNotesAssociatedWithDocumentLabel: '이 문서에 연결된 노트가 없습니다',
	allNotesLabel: '모든 노트',
	charactersLabel: 'characters',
	myNotesLabel: '내 노트',
	showClearedLabel: '처리된 항목 표시',
	showClosedLabel: '닫힌 항목 표시',
	toLabel: 'to',
	toUserLabel: 'To',
	ofLabel: 'of',
	textAreaPlaceholder: '노트 입력',
	addNewNoteModalClose: '닫기',
	addNewNoteModalTitleLabel: '신규 노트 추가',
	editNoteModalTitleLabel: '노트 편집',
	deleteIconHoverText: '삭제',
	deleteIconModalAcceptText: '삭제',
	deleteIconModalConfirmMessage: '이 노트에 대한 회신을 삭제하시겠습니까?',
	deleteIconModalConfirmMessageParent: '선택한 노트를 삭제하시겠습니까?',
	deleteIconModalTitleLabel: '노트 삭제',
	deleteReplyIconModalTitle: '회신 삭제',
	emptyRepliesMessage: '아직 회신이 없습니다',
	replyInputPlaceholder: '이 노트에 회신',
	replyText: '회신 텍스트',
	editReplyModelTitle: '회신 편집',
	noteDueDateLabel: '만료: ',
	fromUserLabel: 'From',
	priorityLabel: '우선순위',
	dueDateLabel: '만료일',
	dueLabel: '만료',
	status: '상태',
	noteModifiedDateLabel: '수정됨: ',
	cancelLabel: '취소',
	saveLabel: '저장',
	clearedBy: '처리한 사람',
	closedBy: '닫은 사람',
	reopenedBy: '다시 연 사람',
	reply: '회신',
	editIconHoverTextLabel: '편집',
	required: '필수',
	closeTitle: '닫기',
	otherEngagementNotes: '다른 engagement 노트',
	closeLabel: '닫기',
	showMore: '더 표시',
	showLess: '덜 표시',
	showMoreEllipsis: '더 표시…',
	showLessEllipsis: '덜 표시…',
	noResultFound: '결과를 찾을 수 없습니다',
	engagementNameLabel: 'Engagement 이름: ',
	drag: 'Drag',
	formMaxLength: '텍스트는 {number}자를 초과할 수 없습니다',
	voiceNoteButtonLabel: '음성 노트',
	stopRecordingButtonLabel: '정지',
	reopen: '다시 열기',
	noNotesFound: '노트를 찾을 수 없습니다',
	noNotesFoundInstructional: '아래에 입력하여 노트를 남기십시오. 노트를 사용자에게 배정하고 우선순위와 만료일을 지정하십시오.',
	microphoneBlockedMessage: '음성 노트를 사용하려면 마이크에 접근할 수 있도록 브라우저를 허용하십시오. 이미 허용됐다면, 새로 고침하여 다시 시도하십시오',
	microphoneBlockedOnVideoMessage: '화면 기록에 음성을 사용하려면 브라우저에 마이크 접근 권한을 허용하십시오. 이미 허용했다면, 새로 고침하여 다시 시도하십시오.',
	notInMainWindowVoice: '드로어 안에서 음성 기록은 허용되지 않습니다. 작업을 수행하려면 문서를 새 탭에서 여십시오',
	notInMainWindowScreen: '드로어 안에서 화면 기록은 허용되지 않습니다. 작업을 수행하려면 문서를 새 탭에서 여십시오',
	voiceNoteNotAvailable: '드로어 보기에서 음성 노트와 화면 기록을 사용할 수 없습니다. 이러한 기능을 사용하려면 전체 화면 보기로 바꾸십시오.',
	playButtonTitle: '재생',
	deleteButtonTitle: '삭제',
	pauseButtonTitle: '일시 정지',
	screenRecord: '화면 기록',
	playbackReview: '재생 검토'
};

export const IndividualAccountAttributeLabels = {
	attributesNotAvailableForDocument: '이 문서에 계정 정보를 사용할 수 없습니다',
	noRelatedOnject: '연결된 object 없음. 시작하려면 object를 연결하십시오.',
	noAttributesAvailable: '사용 가능한 정보 없음',
	noRisksAvailable: '사용 가능한 위험 없음',
	attributeStandardRomms: '정보 표준 ROMM',
	continueButtonTitle: '계속',
	closeButtonTitle: '취소',
	newAssertionModalPlaceholder: '이렇게 선택하면 이전에는 관련된 것으로 식별되지 않았던 다음 경영진주장이 낮은 고유위험으로 식별됩니다. 계속하시겠습니까?',
	assertion: '경영진주장',
	inherentRiskType: '고유 위험',
	assertionModalTitle: '신규 경영진주장',
	riskType: 'Lower'
};

export const entities = [{
	id: 0,
	name: '모두'
},
{
	id: 1,
	name: '문서'
},
{
	id: 2,
	name: '리드 스케줄'
},
{
	id: 3,
	name: '계정'
},
{
	id: 4,
	name: 'SCOT'
},
{
	id: 5,
	name: 'IT 프로세스'
},
{
	id: 6,
	name: '감사 계획'
},
{
	id: 7,
	name: '위험'
},
{
	id: 8,
	name: 'Task'
},
{
	id: 9,
	name: '왜곡표시'
},
{
	id: 10,
	name: '미비점'
},
{
	id: 11,
	name: 'GA 부문'
},
{
	id: 12,
	name: 'GA 부문 지침'
},
{
	id: 13,
	name: 'GA 부문 증거'
},
{
	id: 14,
	name: 'GA 범위'
},
{
	id: 15,
	name: 'GA Primary 지침'
},
{
	id: 16,
	name: 'GA Primary'
},
{
	id: 17,
	name: '고객 요청'
},
{
	id: 18,
	name: 'WCGW'
},
{
	id: 19,
	name: '통제'
},
{
	id: 20,
	name: 'IT Application'
},
{
	id: 21,
	name: 'Canvas 양식'
},
{
	id: 22,
	name: '양식 섹션'
},
{
	id: 23,
	name: '양식 본문'
},
{
	id: 24,
	name: '경영진주장'
},
{
	id: 25,
	name: '고객 Engagement'
},
{
	id: 26,
	name: '고객 그룹'
},
{
	id: 27,
	name: 'Engagement 태그'
},
{
	id: 28,
	name: 'Engagement'
},
{
	id: 29,
	name: '양식 머리글'
},
{
	id: 30,
	name: '양식 상태'
},
{
	id: 31,
	name: 'Engagement 사용자'
},
{
	id: 32,
	name: '고객 그룹 사용자'
},
{
	id: 33,
	name: 'PSP Index'
},
{
	id: 34,
	name: 'ITGC'
},
{
	id: 35,
	name: 'IT 위험'
},
{
	id: 36,
	name: '자동화 개별 항목'
}
];

export const PaceType = [{
	id: 1,
	paceTypename: 'Low'
},
{
	id: 2,
	paceTypename: 'Moderate'
},
{
	id: 3,
	paceTypename: 'High'
},
{
	id: 4,
	paceTypename: 'Close monitoring'
}
];

export const DocumentHelper = {
	401: '작업을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	413: '문서가 최대 허용 가능 파일 크기(250mb)를 초과하여 업로드되지 않았습니다. 파일 크기를 줄여 다시 시도하십시오.',
	412: '이 engagement에 이름이 있는 문서가 이미 있습니다',
	414: '이름이 최대 길이(120자)를 초과합니다',
	4099: '이름이 동일한 문서가 이미 있습니다',
	/*this is a hack as we dont always know why conflict happened.*/
	410: '이 문서는 삭제되어 열 수 없습니다',
	411: '빈 문서는 허용되지 않습니다'
};

export const Errors = {
	/*Doc Helper Custom Messages */
	0: '연결이 끊어졌습니다. 재연결 후 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	10: 'EY Canvas Document Helper에 문제가 발견됐습니다. 문제 해결 지침을 보려면 <a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">here</a>을(를) 클릭하십시오.',
	101: '유효하지 않은 Engagement 상태',
	102: '유효한 engagement 사용자를 찾을 수 없습니다',
	103: 'Engagement 사용자 독립성 준수 누락',
	104: '필수 Azure AD 범위 누락',
	105: 'Engagement 권한을 얻는 동안 오류가 발생했습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	106: "Unauthorized. Contact your administrator and try again.",
	107: '유효한 engagement 사용자를 찾을 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	108: 'Engagement 프로필이 완료되지 않았습니다. 랜딩 페이지로 이동하여 프로필을 완료하십시오.',
	303: '이름이 동일한 문서를 현재 업로드 중입니다',
	403: 'Access to this document is not available.  If this document is shared ensure you have access to the source engagement.  Refresh the page and try again.  If the error persists contact the Help Desk.',
	406: '문서는 비워둘 수 없습니다',
	412: '이 engagement에 이름이 동일한 문서가 이미 있습니다',
	414: '이름이 최대 길이(120자)를 초과합니다',
	411: '빈 문서는 허용되지 않습니다',
	500: '연결이 끊어졌습니다. 재연결 후 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	600: '지금은 작업을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	601: '스크린샷 다운로드 오류. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	602: '문서가 이미 공동작업에 있습니다',
	935: '사용자에게 이 작업을 수행할 권한이 없습니다',
	zip: 'Zip 파일에 지원되지 않는 파일 유형이 하나 이상 있거나 최대 용량을 초과하여 업로드할 수 없습니다.',
	401000: '네트워크 변경이 감지됐습니다. 계속하려면 페이지를 다시 로드하십시오.',

	/*Accounts*/
	1001: '계정 생성 호출 실패',
	1002: '계정 이름 누락',
	1003: 'The selected Account has been deleted. Close this modal to see the updated list.',
	1004: '결과를 찾을 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	1005: '유효하지 않은 Engagement ID',
	1006: '유효하지 않은 계정 유형',
	1007: '유효하지 않은 재무제표 유형',
	1008: '선택한 계정이 삭제됐습니다. 업데이트된 목록을 보려면 이 모달을 닫으십시오.',
	1009: 'ID별 계정 가져오기 호출 실패',
	1010: 'Engagement ID별 계정 가져오기 호출 실패',
	1011: '유효하지 않은 요청으로 인해 계정 ID에 대한 SEM 요약 가져오기 호출 실패',
	1012: '유효하지 않은 요약 유형',
	1013: '계정 검토 생성 호출 실패',
	1014: '계정 검토 삭제 호출 실패',
	1015: '유효하지 않은 계정 검토 생성 요청',
	1016: '유효하지 않은 계정 검토 ID',
	1017: '계정이 이 Engagement에 속하지 않거나 삭제됐습니다',
	1018: '다른 사용자가 계정 검토를 생성했습니다',
	1019: '다른 사용자가 계정을 삭제했습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	1020: '업데이트하려면 계정에 PSP가 필요합니다',
	1024: '계정 이름이 500자를 초과합니다',
	1025: 'Limited Risk 또는 비유의적인 계정은 추정할 수 없습니다',
	1026: '계정은 중복 경영진주장을 가질 수 없습니다',
	1027: '유효하지 않은 경영진주장 ID',
	1037: '중복 PSP index를 가질 수 없습니다',
	1039: '선택한 계정이 삭제됐습니다. 업데이트된 목록을 보려면 이 모달을 닫으십시오.',
	1048: '계정이 유효하지 않은 실행 유형입니다',
	1053: '위험이나 추정치가 연결된 계정은 Limited Risk 또는 비유의적인 계정으로 설정할 수 없습니다',
	1054: '삭제될 경영진주장에 위험 또는 추정치가 연결되어 있습니다',
	1065: '유의적인 위험, 부정위험, 중요왜곡표시위험 또는 추정치가 연결된 경영진주장은 변경할 수 없습니다. 이러한 연결을 먼저 제거해야 합니다.',
	1070: '포함된 Helix에서 시산표 ID는 null일 수 없습니다',
	1072: '작업을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',

	1266: 'Engagement가 최대 multi-user 편집 모드 문서 수에 도달했습니다. 일부 문서를 다시 확인하고 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	1267: 'Document is conflicted. Resolve conflicts and try again.  If the issue persists, contact the Help Desk.',
	1268: 'Document is already in co-edit mode. end co-edit mode and try again.  If the issue persists, contact the Help Desk.',
	1269: 'Document is a shared evidence. Unlink and try again.  If the issue persists, contact the Help Desk.',
	1270: '문서 버전이 Multi-user 편집 모드일 때는 문서 버전을 삭제하거나 업데이트할 수 없습니다. Multi-user 편집 모드를 종료하고 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	1271: '문서가 공동편집 모드 상태에 해당하지 않거나 공통편집 모드 종료가 진행 중입니다. 문제가 지속되면, 헬프 데스크에 문의하십시오.',

	/*Assertions*/
	2001: '유효하지 않은 생성 요청',
	2002: '경영진주장 이름 누락',
	2003: '경영진주장 누락',
	2004: '경영진주장 가져오기 호출 실패',
	2005: '유효하지 않은 Engagement ID',
	2006: 'ID별 경영진주장 가져오기 호출 실패',
	2007: '경영진주장 WCGW 가져오기 호출 실패',

	/*Risks*/
	4001: '작업을 지금 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	4002: '위험 이름 누락',
	4003: '위험 누락',
	4004: '위험 가져오기 호출 실패',
	4005: '유효하지 않은 Engagement ID',
	4006: 'ID별 위험 가져오기 호출 실패',
	4007: '유효하지 않은 쿼리 요청',
	4008: '이 추정치를 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
	4009: '유효하지 않은 업데이트 요청',
	4010: '이 위험에 이미 배정된 특정 WCGW',
	4011: 'WCGW 목록은 null일 수 없습니다',
	4012: '위험 유형 가져오기 실패',
	4013: '유효하지 않은 생성 요청',
	4014: '연결된 경영진주장이 유효하지 않습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	4015: 'WCGW이 유효하지 않습니다. 페이지를 새로 고침하여 다시 시도하십시오. 이 오류가 지속되면 헬프 데스크에 문의하십시오.',
	4016: '선택한 위험/추정치가 삭제됐습니다. 업데이트된 목록을 보려면 이 모달을 닫으십시오.',
	4017: '경영진주장이 위험에 유효하지 않습니다. 페이지를 새로 고침하여 다시 시도하십시오. 이 오류가 지속되면 헬프 데스크에 문의하십시오.',
	4018: '전달된 위험 유형 ID가 유효하지 않습니다',
	4019: '위험 이름이 유효하지 않습니다',
	4020: '유효하지 않은 문서 ID',
	4021: '위험 이름은 500자를 초과할 수 없습니다',
	4023: '경영진주장 ID는 null일 수 없습니다',
	4024: '오류로 인해 limited risk 조서 양식을 생성할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	4025: '유효하지 않은 계정 ID',
	4026: '유효하지 않은 경영진주장 ID',
	4027: '경영진주장 위험 모델은 비워둘 수 없습니다',
	4031: '유효하지 않은 위험 또는 양식 본문 옵션',
	4035: '유의적인 위험 또는 부정위험에 대해서는 높은 위험을 편집할 수 없습니다',
	4036: '전달된 경영진주장 ID가 없다면 Knowledge 경영진주장 ID는 비워둘 수 없습니다.Knowledge 경영진주장 ID는 반드시 enum 단위로 표시되어야 합니다',
	4037: 'Knowledge 경영진주장이 이미 이 계정에 있습니다',
	4038: '위험 유형 ID가 허용되는 옵션과 일치하지 않습니다',
	4062: '이 SCOT을 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
	4063: 'SCOT 연결을 편집할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	4076: 'This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	4079: 'This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*TASK*/
	5001: 'Get all tasks failed.',
	5002: '이 engagement에서 이 task를 더 이상 사용할 수 없습니다',
	5003: 'Task에 대한 문서 가져오기 실패. 유효하지 않은 요청 파라미터.',
	5004: '문서 관련 task 가져오기 실패. 유효하지 않은 요청 파라미터.',
	5005: 'ID별 task 가져오기 실패',
	5006: 'Task 범주 가져오기 호출 실패',
	5007: 'Task 하위 범주 가져오기 호출 실패',
	5008: 'Task 설명 가져오기 호출 실패',
	5009: 'Task 고객 요청사항 가져오기 호출 실패',
	5010: 'Task 고객 요청사항 저장 호출 실패',
	5011: '항목을 연결하려는 task가 삭제됐거나 거절됐습니다',
	5012: '연결하려는 항목이 삭제됐습니다',
	5013: '항목을 연결하려는 task가 삭제됐거나 거절됐습니다',
	5014: '연결하려는 문서가 삭제됐습니다',
	5015: 'Task 증거 가져오기 호출 실패',
	5016: 'WCGW Task 가져오기 호출 실패',
	5017: 'Engagement ID는 0보다 커야 합니다',
	5018: '연결을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	5019: 'Task 설명이 비었습니다',
	5020: '선택한 task가 삭제됐거나 거절됐습니다. 따라서, 이 작업은 지금 완료할 수 없습니다.',
	5021: '원천 engagement ID가 없습니다',
	5022: '주석 저장 실패',
	5023: 'Engagement 사용자를 찾을 수 없습니다',
	5024: 'Task 삭제 호출 실패',
	5025: 'Task 삭제 호출 실패',
	5026: 'Task 목록이 비었습니다',
	5027: '리뷰노트를 찾을 수 없습니다',
	5028: '파일 이름은 필수입니다',
	5029: '파일 확장자는 필수입니다',
	5030: '파일 이름에 다음을 포함할 수 없습니다: */:<>\\?|"',
	5031: '문서 이름 업데이트 오류',
	5032: '유효하지 않은 문서 ID',
	5033: '작업 유형을 찾을 수 없습니다',
	5034: 'Task 상태 변경 실패',
	5035: '수행하려는 작업을 지금 완료할 수 없습니다. 나중에 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	5036: '호출에서 본문은 null이거나 비워둘 수 없습니다',
	5037: '호출에서 요청은 null일 수 없습니다',
	5038: '계속하려면 고유한 파일 이름을 입력하십시오',
	5039: '파일 이름은 필수입니다',
	5040: '파일 이름은 100자로 제한됩니다',
	5041: '파일 이름에 다음을 포함할 수 없습니다: */:<>\\?|"',
	5042: '선택한 task가 거절됐습니다',
	5043: '이 task는 작성 단계 task입니다',
	5044: '이 task는 milestone task입니다',
	5045: '이 task는 PSP 또는 OSP 유형이 아닙니다',
	5046: '문자 한도를 초과했습니다',
	5047: '문자 한도를 초과했습니다',
	5048: '필수 필드',
	5049: '선택한 문서를 이 task에서 제거할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	5050: 'Task 그룹 ID는 유효하지 않거나 0일 수 없습니다',
	5051: 'Task 섹션 ID는 유효하지 않거나 0일 수 없습니다',
	5052: '현재 task에 이 문서 추가 시도 중 오류. 호출 실패.',
	5053: '현재 task 내 이 문서 업데이트 시도 중 오류. 호출 실패.',
	5054: '현재 task 내 이 문서 복사본 추가 시도 중 오류. 호출 실패.',
	5055: '문서 이름 바꾸기 시도 중 오류. 호출 실패.',
	5056: 'Knowledge 또는 그룹 task에 대한 task 제목을 편집할 수 없습니다',
	5057: 'Task 유형이 OST 유형이어야 합니다',
	5058: '시스템과 관련되어 있으므로 이 문서를 task에서 제거할 수 없습니다',
	5059: '유효하지 않은 시기 값',
	5060: '증거 추가 오류. 호출 실패.',
	5061: '다른 사용자가 표시된 정보를 업데이트했습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	5062: '귀하의 engagement에서 선택된 EY Atlas 채널에는 요청된 문서가 없습니다. 추후 포함할 정보를 내용 작성자에게 제공하도록 헬프 데스크에 연락하십시오.',
	5063: '유효하지 않은 패치 작업',
	5064: '선택한 task가 삭제됐거나 거절됐습니다. 따라서, 이 작업은 지금 완료할 수 없습니다.',
	5065: 'Task 원천 유형을 업데이트할 수 없습니다. 요청이 유효하지 않습니다.',
	5066: 'Guidance 가져오기 오류. 호출 실패.',
	5067: 'Task 성격 유형을 업데이트할 수 없습니다. 요청이 유효하지 않습니다.',
	5068: 'Task 성격 유형을 업데이트할 수 없습니다. 요청이 유효하지 않습니다.',
	5069: 'Task 배정 삭제 호출 실패',
	5070: '선택한 task가 하나 이상 삭제됐습니다. 다시 시도하거나 오류가 지속되면 헬프 데스크에 문의하십시오.',
	5071: '배정을 업데이트할 수 없습니다. 요청이 유효하지 않습니다.',
	5072: '작성자를 찾을 수 없습니다. 요청이 유효하지 않습니다.',
	5073: '배정을 찾을 수 없습니다.',
	5074: 'Task 배정 저장 실패',
	5075: '동일한 팀 멤버는 작성자 외 task 배정 하나에만 배정될 수 있습니다. 다시 시도하거나 오류가 지속되면 헬프 데스크에 문의하십시오.',
	5076: '선택한 팀 멤버가 이 engagement의 활동 멤버가 아닙니다. 다시 시도하거나 오류가 지속되면 헬프 데스크에 문의하십시오.',
	5077: '선택한 task가 하나 이상 삭제됐습니다. 다시 시도하거나 오류가 지속되면 헬프 데스크에 문의하십시오.',
	5078: '선택한 task 배정이 제거됐습니다. 다시 시도하거나 오류가 지속되면 헬프 데스크에 문의하십시오.',
	5079: '선택한 task가 하나 이상 삭제됐습니다. 다시 시도하거나 오류가 지속되면 헬프 데스크에 문의하십시오.',
	5080: '문서 버전 기록이 없습니다',
	5081: '현재 이 task에 배정된 사용자는 작성자로 재배정할 수 없습니다. 다시 시도하거나 오류가 지속되면 헬프 데스크에 문의하십시오.',
	5082: '업데이트 실패. 문서 이름은 engagement 내에서 고유해야 합니다. 페이지를 새로 고침하여 이 메시지를 제거하십시오.',
	5083: 'Task 세부정보 문자 한도를 초과했습니다',
	5084: 'Task 문서 생성 호출 실패',
	5085: 'Task 문서 삭제 호출 실패',
	5086: 'Task hand-off 생성 호출 실패',
	5087: 'Task 패치 호출 실패',
	5088: 'Hand-off하려면 이 task에 증거가 있어야 합니다. 다시 시도하거나 헬프 데스크에 문의하십시오.',
	5089: '이 task에 연결된 모든 증거(paper profile 제외)에는 작성자와 완료 표시할 검토자가 하나 이상 있어야 합니다. 다시 시도하고 오류가 지속되면 헬프 데스크에 문의하십시오.',
	5091: 'Engagement에 Paper profile 이름이 이미 있습니다.',
	5092: '이름에 다음 문자를 포함할 수 없습니다: */:<>\\?|\\',
	5093: 'Paper profile 이름이 최대 길이(100자)를 초과합니다',
	5111: '선택한 계정, 경영진주장 또는 limited risk 계정이 삭제됐습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	5116: '문서 유형이 유효하지 않습니다',
	5139: '연결을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	5131: '연결을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	5146: 'Task에 완료 표시할 수 없습니다',
	5156: 'Task 연결을 편집할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',

	/*WCGW*/
	6001: 'WCGW 생성 호출 실패',
	6002: 'WCGW 이름 누락',
	6003: 'WCGW 누락',
	6004: 'WCGW 가져오기 호출 실패',
	6005: '유효하지 않은 Engagement ID',
	6006: '유효하지 않은 경영진주장 ID',
	6007: 'ID별 WCGW 가져오기 호출 실패',
	6008: '유효하지 않은 요청',
	6009: '유효하지 않은 WCGW ID',
	6010: 'Task를 WCGW과 연결할 수 없었습니다',
	6011: '선택한 WCGW이 삭제됐습니다',
	6012: 'Task와 WCGW이 동일한 경영진주장에 연결되지 않았습니다',
	6013: '선택한 task가 동일한 engagement에 속하지 않습니다',
	6014: 'Task를 WCGW에서 분리할 수 없습니다',
	6015: 'Task를 WCGW과 연결할 수 없었습니다',
	6016: 'Task가 거절되어 WCGW과 연결할 수 없습니다',
	6017: 'Task는 milestone task가 아니며 WCGW과 연결할 수 없습니다',
	6018: 'Task가 작성 단계 task가 아니므로 WCGW과 연결할 수 없습니다',
	6019: 'Task는 PSP나 OSP가 아니며 WCGW과 연결할 수 없습니다',
	6020: 'Task와 WCGW이 동일한 경영진주장에 연결되어 있고 이 WCGW과 연결할 수 없습니다',

	/*Engagement*/
	7001: 'ID별 가져오기를 찾을 수 없습니다',
	7002: 'Workspace ID별 engagement 가져오기 호출 실패',
	7003: '모든 engagement 엔터티 가져오기 호출 실패',
	7004: 'ID별 engagement 가져오기 호출 실패',
	7005: '모든 engagement 사용자 가져오기 호출 실패',
	7006: '성은 250자를 초과할 수 없습니다',
	7007: '유효하지 않은 사용자 유형 오류',
	7008: '이름은 250자를 초과할 수 없습니다',
	7009: '사용자 GUI는 null일 수 없습니다',
	7010: '유효하지 않은 사용자 상태 오류',
	7011: 'Engagement 사용자 생성 호출 실패',
	7012: '{0} {1}은(는) 이미 활동 또는 보류 중인 팀 멤버이기 때문에 초대할 수 없습니다',
	7013: '이니셜은 3자를 초과할 수 없습니다',
	7014: '지금 EY Canvas 랜딩 페이지에 접근할 수 없습니다. 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	7015: '{0} {1}은(는) 다음 접근 그룹으로 초대할 수 없습니다: {2}이(가) engagement에서 삭제됐습니다. 새로 고침하여 다시 시도하십시오.',
	7016: '사용자가 이미 다음 접근 그룹의 활동 팀 멤버이므로 {0} {1}은(는) 초대할 수 없습니다: {2}',
	7017: '선택한 다음 그룹에 도메인이 허용되지 않습니다: {0}',
	7018: '이메일 주소는 null일 수 없습니다',
	7019: '이름은 null일 수 없습니다',
	7020: '성은 null일 수 없습니다',
	7021: '사용자 이니셜은 null일 수 없습니다',
	7022: '사용자 Primary Office는 null일 수 없습니다',
	7023: '사용자 로그인 이름은 null일 수 없습니다',
	7024: '사용자 EY 역할은 null일 수 없습니다',
	7025: '사용자 Engagement 역할은 null일 수 없습니다',
	7026: '작업을 완료할 수 없습니다. 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	7027: '유효하지 않은 이메일 주소',
	7028: 'Engagement 사용자 패치 호출 실패',
	7029: 'Engagement 사용자 - 유효하지 않은 Engagement 사용자 상태 ID',
	7030: 'Engagement 사용자 - 유효하지 않은 Engagement 사용자 역할 ID',
	7031: '하나 이상의 engagement 사용자 ID를 찾을 수 없습니다',
	7032: '이메일은 250자를 초과할 수 없습니다',
	7033: '요청된 사용자는 Null일 수 없습니다',
	7034: 'Universal 메시지 프로세서 대기 실패',
	7035: '이니셜은 3자를 초과할 수 없습니다',
	7036: '이름은 250자를 초과할 수 없습니다',
	7037: '성은 250자를 초과할 수 없습니다',
	7038: '외부 사용자 생성 호출 실패',
	7039: '하나 이상의 사용자가 이미 활동 또는 보류 중인 팀 멤버이기 때문에 초대할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	7040: '이름은 250자를 초과할 수 없습니다',
	7041: '성은 250자를 초과할 수 없습니다',
	7042: '이니셜은 3자를 초과할 수 없습니다',
	7043: 'GPN은 250자를 초과할 수 없습니다',
	7044: 'GUI는 250자를 초과할 수 없습니다',
	7045: 'ID별 외부 사용자 가져오기 실패',
	7046: '사용자는 null일 수 없습니다',
	7047: '지금은 변경사항을 저장할 수 없습니다. 다시 시도하고 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	7048: '지금은 EY Canvas 랜딩 페이지에 접근할 수 없으며 편집이 필요합니다. 다시 시도하고 문제가 지속되면 헬프 데스크에 문의하십시오.',
	7049: 'Engagement 사용자 업데이트 호출 실패',
	7050: '멤버를 비활성화할 수 없습니다. Engagement에는 engagement를 관리할 권한을 가진 멤버가 하나 이상 있어야 합니다. 선택을 업데이트하고 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	7051: 'ID별 내부 사용자 가져오기 실패',
	7052: '사용자를 찾을 수 없습니다',
	7053: 'ID별 가져오기를 찾을 수 없습니다',
	7054: 'Engagement ID별 빠른 링크 가져오기 호출 실패',
	7055: '이전 멤버를 추가할 권한이 없습니다. 이 작업을 하려면 적합한 권한이 있는 engagement 팀 멤버와 얘기하십시오.',
	7056: '지금은 EY Canvas에서 변경사항을 저장할 수 없습니다. 다시 시도하고 문제가 지속되면 헬프 데스크에 문의하십시오.',
	7057: '신규 멤버를 추가할 권한이 없습니다. 이 작업을 하려면 적합한 권한이 있는 engagement 팀 멤버와 얘기하십시오.',
	7058: '사용자 상태가 변경됐습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	7062: '지금 EY Canvas Client Portal에 접근할 수 없으며 기존 멤버 정보를 업데이트해야 합니다. 다시 시도하고 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	7063: '외부 멤버를 비활성화할 수 없습니다. 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	7064: '유효하지 않은 패치 작업',
	7065: '{0} {1}을(를) 하나 이상의 접근 그룹에서 활성화할 수 없습니다. 선택한 다음 그룹에서 도메인이 허용되지 않습니다: {2}.',
	7066: '{0}은(는) 유효한 사용자가 아닙니다',
	7067: '외부 사용자 입력 호출 실패',
	7068: '지금 EY Canvas Client Portal에 접근할 수 없으며 기존 멤버 정보를 업데이트해야 합니다. 다시 시도하고 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	7069: '선택한 다음 접근 그룹이 활성 상태가 아닙니다: {0}. 제거하고 다시 시도하십시오.',
	7072: 'Engagement 링크 해제 과정에서 오류가 발생했습니다. 페이지를 새로 고침하고 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	7074: '변경사항을 저장할 수 없습니다. Engagement에는 engagement를 관리할 권한을 가지고 독립성이 해결된 활동 멤버가 하나 이상 있어야 합니다. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	7079: '독립성 제출 호출 실패',
	7080: '사용자 ID가 로그인한 사용자와 다르므로 유효하지 않은 사용자 ID이거나 독립성 제출이 허용되지 않습니다',
	7081: "'제출'을 클릭하기 전에 모든 질문을 완료합니다.'미완료 표시' 옵션을 사용하여 미완료 질문을 필터링합니다. 문제가 지속되면 헬프 데스크에 문의하십시오. ",
	7082: '요청에 대한 독립성 문서를 찾을 수 없습니다',
	7083: 'SDM 요약 호출 실패',
	7084: '사용자 ID가 유효하지 않거나 로그인한 사용자에게 독립성 작업이 허용되지 않습니다',
	7085: '독립성 comment는 4,000자 미만이어야 합니다',
	7086: '독립성 작업 호출 실패',
	7087: '이 사용자의 접근을 부여, 거부, 재지정하려면 귀하가 Partner in Charge, Engagement Partner 또는 Executive Director이어야 합니다',
	7088: '독립성 제출 변경 실패, 나중에 다시 시도하십시오.',
	7098: '유효하지 않은 PACE 유형 ID',
	7099: '사용 가능한 독립성 템플릿이 없습니다. 다시 시도하고 문제가 지속되면 헬프 데스크에 문의하십시오.',
	7154: '현재 canvas 양식에 대해 사용자를 찾을 수 없습니다',
	7155: '복원된 engagement는 프로필 제출이 허용되지 않습니다',
	7156: '내용을 새로 고치고 있습니다. 나중에 다시 시도하십시오. 문제가 오래 지속되면 IT 헬프 데스크에 문의하십시오.',
	7158: '문서를 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',

	/*SCOT*/
	8001: 'SCOT 생성 호출 실패',
	8002: 'SCOT 이름 누락',
	8003: 'SCOT 누락',
	8004: 'SCOT 가져오기 호출 실패',
	8005: '유효하지 않은 Engagement ID',
	8006: '유효하지 않은 경영진주장 ID',
	8007: 'ID별 SCOT 가져오기 호출 실패',
	8008: '유효하지 않은 요청',
	8009: 'The selected SCOT has been deleted. Close this modal to see the updated list.',
	8010: 'SCOT ID는 null이거나 비워둘 수 없습니다',
	8011: 'SCOT ID는 0보다 커야 합니다',
	8012: '문서 ID가 유효하지 않습니다',
	8013: 'SCOT 업데이트 호출 실패',
	8014: 'SCOT 이름은 null이거나 비워둘 수 없습니다',
	8015: 'SCOT 이름은 500자를 초과할 수 없습니다',
	8016: 'SCOT 전략 유형이 유효하지 않습니다',
	8017: 'SCOT 유형이 유효하지 않습니다',
	8018: 'SCOT IT Application이 유효하지 않습니다',
	8019: 'HasNoITApplication이 적용될 때 SCOT ITApplication은 비어 있어야 합니다.',
	8028: 'The selected SCOT has been deleted. Close this modal to see the updated list.',

	/*User*/
	10001: '로그인한 사용자 기본 설정을 찾을 수 없습니다',
	10002: '사용자 모두 가져오기 호출 실패',
	10003: '사용자 기본 설정 가져오기 호출 실패',
	10005: '사용자 세부정보를 검색할 수 없습니다',

	/*Risk Type*/
	11001: '유효하지 않은 생성 요청',
	11002: '위험 유형 이름 누락',
	11003: '위험 유형 누락',
	11004: '위험 유형 가져오기 호출 실패',
	11005: '유효하지 않은 Engagement ID',

	/*TaskDocuments*/
	80004: '하나 이상의 task가 유효하지 않습니다',
	80005: '문서 ID가 유효하지 않습니다',

	/*Edit Control*/
	83001: '통제를 가져오지 못했습니다',
	83002: 'ID별 통제를 가져오지 못했습니다',
	83003: '통제 ID는 null이거나 비워둘 수 없습니다',
	83004: '유효하지 않은 통제 ID',
	83005: '문서 ID가 유효하지 않습니다',
	83006: '통제 이름 누락',
	83007: '유효하지 않은 통제 이름 길이',
	83008: '유효하지 않은 요청',
	83009: '유효하지 않은 통제 빈도 ID',
	83010: '유효하지 않은 통제 유형 ID',
	83011: '유효하지 않은 통제 IT application',
	83012: '유효하지 않은 통제 설계 효과성 유형 ID',
	83013: '유효하지 않은 IT Applicatin ID',
	83014: '통제 유형이 수동 예방 또는 수동 적발 유형인 경우 SO 유형 IT Application만 허용됩니다',
	83015: 'ITDM만 수동 IPE 테스트를 선택할 수 있습니다',
	83016: '이 프로필이 있는 engagement에는 위험이 낮은 통제가 허용되지 않습니다',
	83017: '중복 필터 SCOT',
	83018: '통제 이름이 500자를 초과합니다',
	83019: '유효하지 않은 WCGW ID',
	83020: '중복 WCGW ID가 허용되지 않습니다',
	83021: '중복 IT Application ID가 허용되지 않습니다',
	83022: '파라미터 {0}이(가) 유효하지 않습니다',
	83023: '유효하지 않은 현재 페이지',
	83024: '유효하지 않은 페이지 크기',
	83025: '검색 문자열은 100자를 초과할 수 없습니다',
	83026: '서비스 조직이 없는 IT Application은 IT 의존 수동 통제 또는 IT Application 통제 유형에만 연결될 수 있습니다',
	83027: '중복 필터 WCGW',
	83028: '유효하지 않은 Knowledge 통제 ID',

	112000: '원천 및 대상 문서를 찾을 수 없습니다',
	112001: '요청이 null이므로 호출을 수행할 수 없습니다',
	112002: '호출에서 요청 본문은 null이거나 비워둘 수 없습니다',
	112003: '원천 및 대상 문서 ID는 같을 수 없습니다',
	112004: '원천 문서 ID는 null이거나 비워둘 수 없습니다',
	112005: '대상 문서 ID는 null이거나 비워둘 수 없습니다',
	112006: '원천 engagement ID는 null이거나 비워둘 수 없습니다',
	112007: '대상 engagement ID는 null이거나 비워둘 수 없습니다',
	112008: '원천 문서가 주어진 engagement에 유효하지 않습니다',
	112009: '대상 문서가 주어진 engagement에 유효하지 않습니다',
	112010: '대상 문서 ID를 찾을 수 없습니다',
	112011: '원천 engagement에 양식을 링크할 권한이 없습니다. Engagement 관리자와 협의하여 충분한 권한을 부여받으십시오.',
	112012: '대상 engagement에 양식을 연결할 권한이 없습니다. Engagement 관리자와 협의하여 충분한 권한을 부여받으십시오.',
	112013: '원천 engagement에 유효하지 않은 사용자',
	112014: '대상 engagement에 유효하지 않은 사용자',
	112015: '공유에 유효하지 않은 원천 문서 유형',
	112016: '공유에 유효하지 않은 대상 문서 유형',
	112017: '원천과 대상 문서 유형이 일치하지 않습니다',
	112018: '원천과 대상 knowledge 양식 ID가 일치하지 않습니다',
	112019: '원천 및 대상 문서에 대한 공유 링크가 이미 있습니다',
	112020: '원천과 대상 engagement workspace가 일치하지 않습니다',
	112021: '대상 문서는 대상이 될 수 없습니다',
	112022: '선택된 활동은 이미 다른 활동과 공유되어 추가 공유를 위해 선택할 수 없습니다',
	112023: '원천 문서는 대상이 될 수 없습니다',
	112024: '대상 문서 ID의 engagement ID를 찾을 수 없습니다',
	112025: '원천 문서 ID의 engagement ID를 찾을 수 없습니다',
	112026: '원천 문서와 대상 문서 ID는 같을 수 없습니다',
	112027: 'Engagement에 공유된 EY Canvas FIT enablement 활동이 있기 때문에 프로필 제출을 진행할 수 없습니다. 계속하려면 활동 링크를 해제하십시오.',
	112028: '원천과 대상 Engagement ID는 같을 수 없습니다',
	112029: '원천 또는 대상 Engagement ID와 문서 ID는 경로 Engagement ID 및 문서 ID가 일치해야 합니다',
	112030: '문서가 주어진 engagement에 유효하지 않습니다',
	112031: '문서 ID는 null이거나 비워둘 수 없습니다',
	112032: 'Engagement ID는 null이거나 비워둘 수 없습니다',
	112033: '대상 문서 ID는 고유해야 합니다',
	112034: '이미 공유된 대상 또는 원천 문서',
	112035: '기존 링크된 응답 관련 문서에는 단일 대상만 있을 수 있습니다.',

	/*MissingDocument*/
	116001: 'Create form failed.',
	116002: '주어진 문서 유형 ID에서 knowledge 문서를 찾을 수 없습니다',
	116004: '문서 생성 실패. 페이지를 새로 고침하거나 잠시 후 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',

	/* Annotation Errors*/
	12001: '유효하지 않은 요청 파라미터',
	12002: '주석 생성 호출 실패',
	12003: '주석 가져오기 실패',
	12004: '유효하지 않은 Engagement ID',
	12005: '컬렉션 내 ID는 0보다 커야 합니다',
	12006: 'ID 컬렉션은 비워둘 수 없습니다',
	12007: '유효하지 않은 task ID',
	12008: '유효하지 않은 문서 ID',
	12009: '유효한 문서 ID 또는 Task ID가 있어야 합니다',
	12010: '회신에는 parent가 필요합니다',
	12011: '유효하지 않은 상태 ID',
	12012: '문서 유형은 440GL이어야 합니다',
	12013: '유효하지 않은 주석 유형',
	12014: '유효하지 않은 engagement 사용자',
	12015: '다른 사용자가 이 문서를 삭제했습니다',
	12016: '주석 업데이트 호출 실패',
	12017: '회신하려는 노트를 다른 팀 멤버가 삭제했습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	12018: '주석 변경 작업 유형은 null일 수 없습니다',
	12019: '리뷰노트 삭제됨',
	12020: '유효하지 않은 수행 작업',
	12021: '사용자가 주석을 작성하지 않았습니다',
	12022: '작성자 누락',
	12023: '작성자가 engagement에 존재하지 않거나 속하지 않습니다',
	12024: '배정된 사용자 필요',
	12025: '배정된 사용자가 engagement에 존재하지 않거나 속하지 않습니다',
	12026: '유효하지 않은 comment',
	12027: 'Comment는 비워둘 수 없습니다',
	12028: '만료일 필요',
	12029: '유효한 우선순위 필요',
	12030: '이 노트의 상태가 변경됐습니다. 더 이상 편집하거나 노트에 응답할 수 없습니다. 업데이트된 데이터를 보고 계속 편집하려면 창을 닫았다가 다시 여십시오.',
	12031: '주석은 최상위 수준이어야 합니다',
	12032: '주석은 최상위 수준이 아니어야 합니다',
	12033: '다음 값 중 적어도 하나는 비어 있으면 안됩니다: 우선순위 유형, 만료일, 상태, 배정된 사용자',
	12034: 'Comment가 최대 길이(4,000자)를 초과합니다',
	12035: '검색 문자열이 최대 길이(500자)를 초과합니다',
	12036: 'Task ID 또는 문자 ID만 수락되어야 합니다. 둘 다는 허용되지 않습니다.',
	12037: '유효하지 않은 패치 작업',
	12038: '더 이상 존재하지 않는 노트를 편집하려고 합니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	12039: '다른 팀 멤버와 동일한 노트를 편집하려고 합니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	12040: '주석 사용자 가져오기 호출 실패',
	12041: '주석 사용자 가져오기 호출 실패. 유효하지 않은 쿼리 값.',
	12042: '주석을 생성하기에 문서 유형이 유효하지 않습니다',
	12043: '더 이상 존재하지 않는 task 또는 문서에 노트를 추가하려고 합니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	12044: '더 이상 존재하지 않는 노트에 대한 회신을 편집하려고 합니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	12045: '리뷰노트를 찾을 수 없습니다',
	12046: '다른 사용자가 선택한 노트를 이미 삭제했습니다',
	12047: '처리 전 상태인 노트에 대한 회신만 삭제할 수 있습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	12048: '더 이상 존재하지 않는 노트의 상태를 변경하려고 합니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	12049: '다른 팀 멤버가 이미 삭제한 노트에 대한 회신을 삭제하려고 합니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	12050: '닫힌 상태인 노트만 삭제할 수 있습니다',
	12051: 'Comment 유형 주석은 유효한 Helix 문서에만 생성할 수 있습니다',
	12052: '귀하가 찾고 있는 comment 유형 주석은 삭제됐습니다',
	12060: '참조 번호는 필수이며 0보다 크고 1000보다 작아야 합니다',
	12061: '참조 번호는 null이어야 합니다.',
	12062: '주석은 comment에만 생성할 수 있습니다',
	12066: '열리지 않은 노트에 회신하려고 합니다.',
	12067: '다른 팀 멤버가 이미 삭제한 comment에 대한 회신을 삭제하려고 합니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	12068: '유효하지 않거나 더 이상 존재하지 않는 기록이 있는 노트를 추가하려고 합니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	12069: '유효하지 않거나 더 이상 존재하지 않는 기록이 있는 노트를 업데이트하려고 합니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	12070: '재무제표별로 comment는 하나만 추가할 수 있습니다. 기존 comment를 편집하십시오.',
	12071: '정보를 삭제하지 못했습니다. 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',

	/*FlowchartStepControl*/
	123054: 'Control relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',
	123045: 'This control is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepWCGW*/
	123022: '이 flowchart 단계를 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
	123023: '이 flowchart 단계를 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
	123055: 'WCGW relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepITApplicationSO*/
	123056: 'IT Application / service organization relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the issue persists.',



	/*FlowchartStepDocument*/
	123048: '문서 연결을 편집할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
	123033: '이 flowchart 단계를 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
	123002: '이 문서를 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',

	/*Configuration*/
	13001: '설정을 찾을 수 없습니다',
	13002: '설정 API 가져오기 호출 실패',

	/*Documents*/
	14001: '요청이 null이므로 호출을 수행할 수 없습니다',
	14002: '문서를 찾을 수 없습니다. 호출 실패.',
	14003: 'Engagement ID는 0보다 커야 합니다',
	14004: '문서 ID는 null이거나 비워둘 수 없습니다',
	14005: '연결된 task를 가져오려는 동안 오류가 발생했습니다. 호출 실패.',
	14006: '선택한 문서를 삭제할 수 없습니다. 나중에 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	14007: '예상치 못한 오류가 발생했습니다',
	14008: 'Sign-off 중에 예상치 못한 오류가 발생했습니다',
	14009: 'Sign-off를 추가하는 동안 오류가 발생했습니다',
	14010: '승인 ID는 0보다 커야 합니다',
	14011: 'Sign-off를 삭제하는 동안 오류가 발생했습니다',
	14012: '본문은 비워둘 수 없습니다',
	14013: '선택한 문서는 링크 해체할 수 없습니다. 나중에 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	14014: '문서 ID별 계정 가져오기 호출 실패',
	14015: '유효하지 않은 관련 엔터티',
	14016: '문서 승인 가져오기 호출 실패',
	14017: '문서 발견사항 모두 가져오기 호출 실패',
	14018: '문서 모두 가져오기 호출 실패',
	14019: 'Sign-off를 찾을 수 없습니다',
	14020: '유효하지 않은 작업 값',
	14021: '유효하지 않은 발견사항 유형',
	14022: '문서가 engagement에 속하지 않습니다',
	14023: '문서 변경 유형이 유효하지 않습니다',
	14024: '모든 문서 가져오기 호출 실패. 파라미터가 유효하지 않습니다.',
	14025: '문서 검토를 생성하는 동안 오류가 발생했습니다. API 호출이 실패했습니다.',
	14026: '문서 검토를 삭제하는 동안 오류가 발생했습니다. API 호출이 실패했습니다.',
	14027: 'Engagement ID는 null이거나 비워둘 수 없습니다',
	14028: '사용자 ID가 유효하지 않습니다',
	14029: '사용자는 이 작업을 수행할 권한이 없습니다',
	14030: '이 engagement에서 통과된 ID가 있는 문서를 찾을 수 없습니다',
	14031: '문서 검토 ID가 유효하지 않습니다',
	14032: '문서 검토를 찾을 수 없습니다',
	14033: '문서가 이미 승인됐습니다',
	14034: 'Workspace 내 다른 engagement에서 링크 해제 진행 중이거나 이 문서가 이미 링크 해제됐습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	14035: '문서가 주어진 engagement와 공유되지 않았습니다',
	14036: '버전 번호는 0보다 커야 합니다',
	14037: '지금은 작업을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	14038: '문서 변경 사유를 가져오지 못했습니다',
	14039: 'ID별 문서 변경 사유를 가져오지 못했습니다',
	14040: '변경 사유를 업데이트하지 못했습니다',
	14041: '유효하지 않은 변경 사유',
	14042: '변경 사유 업데이트 실패',
	14043: '변경 사유를 생성하지 못했습니다',
	14044: '변경 사유를 삭제하지 못했습니다',
	14045: '유효하지 않은 변경 사유 ID',
	14046: '문서를 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	14047: '문서에 배정된 변경 사유가 이미 있습니다',
	14048: '문서에 충돌이 있습니다. 계속하기 전에 해결하십시오.',
	14049: '문서가 팀이나 사용자에 추가됐는지 확인할 데이터가 유효하지 않습니다',
	14050: '참조 문서를 제거하는 동안 오류가 발생했습니다',
	14052: '검색 텍스트가 최대 허용 길이(500자)를 초과합니다',
	14053: '요청이 null이므로 호출을 수행할 수 없습니다',
	14054: '유효하지 않은 패치 작업',
	14055: '문서 기록 해결 호출 실패',
	14056: '메시지를 대기 행렬에 넣는 동안 오류가 발생했습니다',
	14057: '이름이 동일한 문서가 engagement에 이미 있습니다',
	14058: '동일한 숫자를 가진 하나 이상의 문서 버전이 발견됐습니다. 추가 지원을 위해 헬프 데스크에 문의하십시오.',
	14059: '선택한 문서 버전을 찾을 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 추가 지원을 위해 헬프 데스크에 문의하십시오.',
	14060: '지금은 작업을 완료할 수 없습니다. 나중에 다시 시도하십시오. 문제가 지속되면 추가 지원을 위해 헬프 데스크에 문의하십시오.',
	14061: '문서가 공유되지 않아 이 문서를 링크 해제할 수 없습니다.  문제가 지속되면 추가 지원을 위해 헬프 데스크에 문의하십시오.',
	14062: '문서 기밀 유형이 유효하지 않습니다',
	14063: '이 문서 유형의 기밀 유형을 변경할 수 없습니다',
	14064: '사용자에게 권한이 없습니다',
	14065: '유효하지 않은 사용자지정 설명',
	14066: '확장자를 업데이트 하지 못했습니다',
	14067: '유효하지 않은 확장자',
	14068: '문서 확장자를 가져오지 못했습니다',
	14069: '이 문서는 다른 팀 멤버가 이미 링크 해제했습니다. 페이지를 새로 고침하고 오류가 지속되면 헬프 데스크에 문의하십시오.',
	14070: 'Invalid file type.',
	14071: 'The selected document version is no longer available. Please close and reopen this window to see the latest set of historical versions for the document.',
	14072: '원천 문서 ID는 null이거나 비워둘 수 없습니다',
	14073: 'Canvas 양식 ID는 null이거나 비워둘 수 없습니다',
	14074: 'Canvas 양식 ID는 중복될 수 없습니다',
	14075: '문서를 Canvas 양식과 연결하지 못했습니다',
	14076: '원천 문서와 연결된 Canvas 양식을 찾을 수 없습니다',
	14077: '원천 문서를 찾을 수 없습니다. 호출 실패.',
	14078: '현재 문서가 지정된 Canvas 양식과 이미 연결되었습니다',
	14079: 'This document has been deleted and therefore it cannot be opened.',
	14080: 'The source approval user id is invalid.',
	14081: 'The source approval user id should valid GUID and must not be empty GUID.',
	14082: 'The modify user id is invalid.',
	14083: 'The modify user id should be a valid GUID and must not be empty GUID.',
	14084: 'File name cannot include: */:<>\\?|""',
	14085: 'The document name exceeded maximum length allowed.',
	14086: 'DocService failed while updating document details.',
	14087: 'The input is not valid.',
	14088: 'A input has duplicate document names.',
	14089: 'The bookmark observation is not valid.',
	14090: 'Request status has changed. Please refresh the page and try again if required. If the issue persists, contact the help desk.',
	14091: 'This request has been deleted. Refresh the page to view updated data. If the issue persists, contact the Help Desk.',
	14092: 'Document not eligible for update. Refresh the page and try again.  If the issue persists, contact the Help Desk.',

	/*SEM*/
	15001: '유효하지 않은 요청으로 인해 계정 ID에 대한 SEM 요약 가져오기 호출 실패',
	15002: '유효하지 않은 계정 ID',
	15003: '유효하지 않은 Engagement ID',
	15004: '유효하지 않은 요약 유형',
	15005: '연결된 계정을 찾을 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',

	/*Timephase*/
	16001: '시기 가져오기 실패',
	16002: 'Engagement는 0보다 커야 합니다',
	16003: '시기는 0보다 커야 합니다',
	16004: '유효하지 않은 task ID 값',
	16005: '유효하지 않은 시기 값',

	/*Validations*/
	17001: '작성 단계 ID 또는 문서 유형 ID 누락',
	17003: 'The document could not be found. Refresh the page and try again. If the issue persists, contact Help Desk.',

	/*TaskGroupSection*/
	18001: '모든 task 그룹 섹션 가져오기 호출 실패',

	/*Assignments*/
	19001: '배정 생성 호출 실패',
	19002: '배정 가져오기 호출 실패',

	/*Client Request*/
	21001: '고객 요청 연결 호출에 실패했습니다',

	/*Related Components*/
	22001: '연결된 부문 가져오기 호출 실패',

	/*Component Ceation*/
	22022: '이 engagement에 부문 이름이 이미 있습니다',
	22024: '지침을 보내려는 부문이 사용 가능하지 않거나 이미 삭제됐습니다',
	22027: '만료일 없는 그룹 지침이 있습니다',
	22028: '부문에 대한 범위 지침이 아직 발간되지 않았습니다',
	22029: '부문에 전송하기 위해 발간된 신규 지침이 없습니다',

	22040: '지침을 전송할 수 없습니다. Engagement 유형이 올바른지 확인하십시오.',
	22048: '업데이트하려는 부문은 다른 engagement에서 engaement 복사를 통해 복사됐습니다',

	/*Send Instruction*/
	22049: 'Group instructions cannot be sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk.',

	/*User Presence*/
	23001: '로그인한 사용자 기본 설정을 찾을 수 없습니다',
	23002: '사용자 기본 설정 가져오기 호출 실패',
	23003: '사용자 기본 설정 문서 유효성 검증 실패',
	23004: '사용자 기본 설정 삭제 호출 실패',
	23005: '다른 사용자가 더 이상 문서를 열고 있지 않습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',

	/* Forms */
	24001: '양식 가져오기 호출 실패',
	24002: '유효하지 않은 Engagement ID',
	24003: '문서 ID는 null이거나 비워둘 수 없습니다',
	24005: '양식 머리글을 찾을 수 없습니다',
	24004: '문서를 찾을 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	24006: 'Section is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24007: '유효하지 않은 요청 파라미터',
	24008: '머리글 ID는 null이거나 비워둘 수 없습니다',
	24009: '섹션 ID는 null이거나 비워둘 수 없습니다',
	24010: '양식 본문 응답 업데이트 작업 실패',
	24011: '유효하지 않은 양식 본문 응답 업데이트 요청',
	24012: 'Body is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24013: '유효하지 않은 양식 본문 옵션 ID',
	24014: '유효하지 않은 양식 본문 유형 ID',
	24015: '지정된 본문 유형 ID에 유효하지 않은 요청 본문',
	24016: '지정된 본문 유형 ID에 유효하지 않은 자유 텍스트',
	24017: 'Rich text 서식 태그를 포함한 최대 문자 수. 길이를 줄이거나 불필요한 서식을 제거하고 다시 시도하십시오.',
	24018: '지정된 본문 유형 ID의 경우, 응답이 허용되지 않습니다',
	24019: '본문 ID는 null이거나 비워둘 수 없습니다',
	24020: '유효하지 않은 요청 본문',
	24021: '본문이 삭제됐습니다',
	24022: '국가는 null이거나 비워둘 수 없습니다',
	24023: '언어는 null이거나 비워둘 수 없습니다',
	24024: '서브서비스라인은 null이거나 비워둘 수 없습니다',
	24025: 'GAM 단계는 null이거나 비워둘 수 없습니다',
	24026: '머리글 생성 호출 실패',
	24027: '유효하지 않은 머리글 생성 요청',
	24028: '중복 유닛 생성 호출 실패',
	24029: '유효하지 않은 양식 유닛 유형',
	24030: '섹션이 삭제됐습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	24031: '본문은 사용자지정 본문이 아닙니다',
	24032: '머리글 생성 요청이 유효하지 않습니다',
	24033: '제공된 문서 엔터티 ID가 유효하지 않습니다',
	24034: '제공된 엔터티 ID가 유효하지 않습니다',
	24035: '연결된 문서를 생성하는 동안 오류가 발생했습니다',
	24036: '연결된 문서를 삭제하는 동안 오류가 발생했습니다',
	24037: '연결된 문서는 null이거나 비워둘 수 없습니다',
	24038: '문서가 유효하지 않거나 존재하지 않습니다',
	24039: '연결된 문서가 유효하지 않거나 존재하지 않습니다',
	24040: '사용자지정 본문 생성 실패',
	24041: '본문 생성 요청이 유효하지 않습니다',
	24042: '섹션 생성 요청이 유효하지 않습니다',
	24043: 'ID별 섹션 가져오기 호출 실패',
	24044: '섹션 생성 실패',
	24045: '유효하지 않은 현재 페이지',
	24046: '유효하지 않은 페이지 크기',
	24047: '유효하지 않은 문서 관련 object ID',
	24048: 'Object가 Canvas 양식에 이미 연결되어 있습니다',
	24049: 'Object를 찾을 수 없습니다',
	24050: '전달된 엔터티 UID가 유효하지 않습니다',
	24051: '엔터티 ID가 제공되면 엔터티 UID도 제공되어야 하며 그 반대도 마찬가지입니다',
	24052: 'Canvas 양식 스냅샷 생성 실패',
	24053: 'ID별 머리글 가져오기 호출 실패',
	24054: 'ID별 본문 가져오기 호출 실패',
	24055: '양식 프로필 생성에 오류가 발생했습니다',
	24056: '문서 양식 프로필이 이미 있습니다',
	24057: '문서 양식 프로필 유효성 검증 실패',
	24058: '문서 양식 프로필이 없습니다',
	24059: '섹션이 사용자지정이 아닙니다',
	24060: '문서 양식 프로필 유효성 검증 실패. PCAOB-IA가 참이면 PCAOB-FS도 참이어야 합니다.',
	24061: '문서 양식 프로필 유효성 검증 실패. PCAOB-FS가 거짓이면 PCAOB-IA도 거짓이어야 합니다.',
	24062: "문서 양식 프로필 유효성 검증 실패. 비복잡이면'PCAOB-FS'/'PCAOB-IA'가 거짓이어야 합니다. ",
	24063: '국가 ID가 유효하지 않습니다',
	24064: '언어 ID가 유효하지 않습니다',
	24065: '머리글이 사용자지정이 아닙니다',
	24066: '양식 섹션 관련 object 생성 실패',
	24067: 'Object를 찾을 수 없습니다',
	24068: 'WCGW이 SCOT에 연결되어 있지 않습니다',
	24069: '파라미터 {0}이(가) 유효하지 않습니다',
	24070: '전달된 parent 엔터티 UID가 유효하지 않습니다',
	24071: '양식 섹션 관련 object 가져오기 실패',
	24072: '섹션을 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	24073: '스냅샷을 검색할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	24074: '선택한 문서에 스냅샷을 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	24075: '유효하지 않은 스냅샷 ID',
	24076: '엔터티 ID는 null이거나 비워둘 수 없습니다',
	24077: '전달된 양식 섹션 관련 object ID가 유효하지 않습니다',
	24078: '양식 섹션 관련 Object ID는 null이거나 비워둘 수 없습니다',
	24079: '제공된 Parent 엔터티 ID가 유효하지 않습니다',
	24080: '양식 섹션 관련 Object : Parent 엔터티 기록을 찾을 수 없습니다',
	24081: 'Object를 찾을 수 없습니다',
	24082: 'Object를 찾을 수 없습니다',
	24083: '양식 프로필 업데이트에서 오류가 발생했습니다',
	24084: '문서 양식 프로필이 없습니다',
	24085: '언어 ID는 0보다 커야 합니다',
	24086: '국가 ID는 0보다 커야 합니다',
	24087: 'Knowledge 제공 문서는 업데이트할 수 없습니다. 이 양식 프로필을 변경하려면 Engagement 프로필을 업데이트하십시오.',
	24088: '내용을 편집할 권한이 없습니다. Engagement 관리자와 협의하여 충분한 권한을 얻으십시오.',
	24089: '사용자지정 머리글 이름이 최대 길이(500자)를 초과했습니다. 이름을 조정하고 다시 시도하십시오.',
	24090: '사용자지정 섹션 이름이 최대 길이(500자)를 초과했습니다. 이름을 조정하고 다시 시도하십시오.',
	24091: '섹션 사용자지정 레이블이 최대 길이(100자)를 초과했습니다. 이름을 조정하고 다시 시도하십시오.',
	24092: '사용자지정 본문 이름이 최대 길이(500자)를 초과했습니다. 이름을 조정하고 다시 시도하십시오.',
	24093: '사용자지정 섹션 이름은 null이거나 비워둘 수 없습니다',
	24094: '본문 이름은 null이거나 비워둘 수 없습니다',
	24096: '사용자지정 머리글 이름은 null이거나 비워둘 수 없습니다',
	24097: '지금은 덮어쓰기를 완료할 수 없습니다',
	24098: '원천 또는 대상 문서 유형 ID가 유효하지 않습니다',
	24099: '원천 및 대상 문서 knowledge 양식 ID가 동일하지 않습니다',
	24100: '원천 문서 ID는 null이거나 비워둘 수 없습니다',
	24101: '대상 문서 ID는 null이거나 비워둘 수 없습니다',
	24103: '머리글이 삭제됐습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	24104: '머리글은 편집할 수 없습니다',
	24105: '본문은 편집할 수 없습니다',
	24106: '업데이트 작업이 유효하지 않습니다',
	24107: '문서 양식 프로필 유효성 검증 실패. PCAOB-FS가 참이면 복잡도 참이어야 합니다.',
	24108: '문서 양식 프로필 유효성 검증 실패. PCAOB-IA가 참이면 PCAOB-FS도 참이어야 합니다.',
	24110: '현재 내용 업데이트 진행 중. 내용 업데이트가 완료되면 Canvas 양식 내용 업데이트 페이지에서 이 양식 내용을 수동 업데이트하십시오.',
	24111: '매핑 & 링크 해제 값은 동일할 수 없습니다',
	24112: '원천 문서는 공유할 수 없습니다',
	24114: '증거를 추가하지 못했습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	24115: '증거를 제거하지 못했습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	24116: '프로필을 제출하지 못했습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	24117: '양식에 미완료 응답이 있습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	24118: '문서 ID와 연결된 문서 ID는 동일할 수 없습니다',
	24119: '본문 관련 object를 편집하지 못했습니다',
	24120: '본문 관련 object ID는 null이거나 비워둘 수 없습니다',
	24121: 'Object를 찾을 수 없습니다',
	24122: '동시성 토큰 누락',
	24124: '유효하지 않은 대상 문서를 찾았습니다',
	24125: '대상 Canvas 양식을 찾을 수 없습니다',
	24126: '요청은 null이거나 비워둘 수 없습니다',
	24127: 'EY Helix에서 데이터를 불러올 수 없었습니다. EY Helix 설정에서 데이터를 다시 불러오고 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	24155: '복원된 engagement는 프로필 제출이 허용되지 않습니다',
	24164: '템플릿을 변경할 수 있는 적합한 권한이 없습니다',
	24166: '프로필 저장이 허용되지 않습니다. 이 프로필에 사용 가능한 양식이 없습니다.',
	24167: '본문 유형을 업데이트할 수 없습니다',
	24168: '사용자 수정 ID는 null이거나 비워둘 수 없습니다',
	24169: '사용자 수정 ID는 링크된 응답 업데이트가 거짓인 경우에만 값을 가질 수 있습니다',
	24170: '요청에 유효하지 않은 task ID',
	24171: '요청에 중복된 본문 ID',
	24172: '섹션 비교 호출 실패',
	24173: '문서 ID는 비워둘 수 없습니다. 또한 구별되는 문서가 한 번에 50개를 초과할 수 없습니다.',
	24174: '경로 문서는 요청 본문 문서 ID의 일부여야 합니다',
	24175: '요청 본문에 유효하지 않은 스마트 증거 기업 ID',
	24176: '본문 문서 ID에 동일한 knowledge 양식 ID가 있어야 합니다',
	24177: '유효하지 않은 섹션 ID',
	24178: '유효하지 않은 문서 ID와 문서 ID 목록 조합',
	24179: '문서 ID 목록은 50개를 초과할 수 없습니다',
	24180: '중요성을 업데이트할 수 없습니다',
	24181: '유효하지 않은 양식 본문 ID',
	24182: '유효하지 않은 Knowledge 양식 ID 또는 Knowledge 양식 본문 ID',
	24183: '이 양식에 문서를 연결하면서 오류가 발생하였습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	24184: '사용자가 이 EY Canvas engagement에서 활동 상태가 아닙니다. 팀 관리를 통해 사용자를 초대한 뒤 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	24188: 'One or more records are no longer available. Please refresh the page',

	/*Document Change Types*/
	25001: '문서 변경 유형 가져오기 호출 실패',
	25002: '유효하지 않은 Engagement ID',
	25003: '유효하지 않은 요청',

	/* Manage team */
	26001: '고객 사용자 그룹 모두 가져오기 호출 실패',
	26002: '신규 고객 그룹 / 이메일 도메인 생성 호출 실패',
	26003: '그룹 이름은 null일 수 없습니다',
	26004: '이메일 도메인은 null일 수 없습니다',
	26005: '{0} 이메일 도메인 레이블은 263을 초과할 수 없습니다',
	26006: '{0} 이메일 도메인은 263자를 초과할 수 없습니다',
	26007: '{0} 이메일 도메일 첫 부분은 * 또는 문자나 숫자이며 기타 특수문자는 허용되지 않습니다',
	26008: '{0} 첫 부분에 와일드카드가 있으면 그 다음 부분은 둘 이상이어야 합니다',
	26009: '{0} 첫 부분은 * 또는 문자나 숫자이며 기타 도메인 부분에 특수문자는 허용되지 않습니다',
	26010: '이메일 도메인은 고유해야 합니다',
	26011: '제공된 고객 접근 그룹 ID가 유효하지 않습니다',
	26012: '접근 그룹을 삭제하는 동안 오류가 발생했습니다. 이 그룹 또는 이 그룹의 멤버에게 배정된 요청사항/외부 Task가 없는지 확인하고 다시 시도하십시오.',
	26013: '접근 그룹이 이미 삭제됐습니다. 최신 데이터를 가져오려면 페이지를 새로 고침하십시오.',
	26014: '하나 이상의 이메일 도메인은 필수입니다',
	26015: '지금 EY Canvas Client Portal에 접근할 수 없으며 기존 멤버 정보를 업데이트해야 합니다. 다시 시도하고 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	26016: '삭제 작업 중 오류',
	26017: '데이터 가져오기 작업 실패',
	26018: '동시성 문제. 접근 그룹이 더 이상 활성 상태가 아닙니다.',
	26019: '저장 작업 실패',
	26020: '이메일 도메인에 활동 사용자가 배정되어 있으므로 이를 제거할 수 없습니다. 변경사항은 저장되지 않습니다.',

	/* TimePhaseTypes - Milestones */
	27001: 'Milestone 세부정보 가져오기 실패',

	/*Client Request Counts*/
	28001: '고객 요청사항 정보 가져오기 호출이 결과를 가져오지 못했습니다',

	/*Content updates Error messages*/
	29001: 'ID별 가져오기를 찾을 수 없습니다',
	29002: '작업 유형을 찾을 수 없습니다',
	29003: '내용 ID를 찾을 수 없습니다',
	29004: '내용 업데이트 API 호출 실패',
	29005: '내용 업데이트 진행 중. 나중에 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	29006: '유효하지 않은 요청 파라미터',

	/*IT Process*/
	30001: '모든 IT 프로세스 가져오기 실패',
	30002: '모든 IT 프로세스 가져오기 - 유효하지 않은 engagement ID',
	30003: 'IT 프로세스 이름은 비워둘 수 없습니다',
	30004: 'IT 프로세스 이름은 500자를 초과할 수 없습니다',
	30005: '작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	30006: 'ID별 IT 프로세스 가져오기 호출 실패',
	30007: '유효하지 않은 요청',
	30008: 'IT 프로세스를 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	30009: '문서를 찾을 수 없습니다',
	30010: '삭제를 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	30012: '삭제를 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	30017: '작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	30018: '작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	30019: '작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',

	/*Checklist*/
	31001: '모든 체크리스트 가져오기 호출 실패',
	31002: '체크리스트 입력 호출 실패',
	31003: '유효하지 않은 체크리스트 파라미터',
	31004: '유효하지 않은 Engagement 오류',
	31005: '유효하지 않은 요청 파라미터 오류',
	31006: '유효하지 않은 확인 요청 파라미터 오류',

	/*Archive*/
	32001: 'Engagement 상태 ID가 유효하지 않습니다',
	32002: 'Archive 실패',
	32003: 'V1 ARC 호출 실패. 페이지를 새로 고침하여 유효성 검증을 해결하고 archive 프로세스를 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	32004: 'LDC에 Engagement 상태 업데이트 실패',
	32005: 'Engagement 캐시 오류를 무효화합니다',
	32006: '내용 업데이트가 진행 중입니다. 내용 업데이트가 완료될 때까지 이 engagement를 archive할 수 없습니다. 나중에 다시 시도하고 오류가 지속되면 헬프 데스크에 문의하십시오.',
	32007: 'ARC GUID가 null이거나 비어 있습니다',
	32008: '파일 GUID가 null이거나 비어 있습니다',
	32009: '파일 저장소 호스트 TCP가 null이거나 비어 있습니다',
	32200: '이 engagement에 해결되지 않은 유효성 검증이 있습니다. 페이지를 새로 고침하여 유효성 검증을 해결하고 archive 프로세스를 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	32300: '이 engagement에 해결되지 않은 유효성 검증이 있습니다. 페이지를 새로 고침하여 유효성 검증을 해결하고 archive 프로세스를 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',

	/*RBAC*/
	33001: 'Engagement ID를 찾을 수 없습니다',
	33002: '사용자 ID를 찾을 수 없습니다',

	/*Helix Linked Projects*/
	34001: '요청이 null이므로 호출을 수행할 수 없습니다',
	34002: 'Engagement ID는 null이거나 비워둘 수 없습니다',
	34003: '호출에서 본문은 null이거나 비워둘 수 없습니다',
	34004: '프로젝트 ID는 null이거나 비워둘 수 없습니다',
	34005: '프로젝트 이름은 null이거나 비워둘 수 없습니다',
	34006: '프로젝트가 이미 링크됐습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	34007: '모든 Helix 프로젝트 가져오기 호출 실패',
	34008: 'Engagement ID는 0보다 커야 합니다',
	34010: '저장할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	34009: '프로젝트 ID가 변경됐습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	34011: '통화 유형은 호출에서 null일 수 없습니다',
	34012: '통화 코드는 호출에서 null일 수 없습니다',
	34013: '사업부는 호출에서 null이거나 비워 둘 수 없습니다',
	34014: '링크된 EY Helix 프로젝트가 변경되어 설정을 업데이트할 수 없습니다',
	34017: 'EY Helix에 연결할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	34018: '작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	34019: '불러오기를 완료했으나 데이터가 유효하지 않습니다. 페이지를 새로 고침하고 불러오기를 클릭하여 다시 시도하십시오.',
	34027: 'Helix 프로젝트가 진행 중입니다',
	34036: 'EY Helix에 연결할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	34039: '이 EY Helix 프로젝트는 더이상 귀하의 engagement에 대한 primary 프로젝트가 아닙니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	34040: '이 작업을 하려면 <b>EY Helix 불러오기</b> 권한이 있어야 합니다. 접근권한을 얻으려면 engagement 관리자와 얘기하십시오.',

	/* PAANS */
	35001: '지금은 EY 정책 작성, 승인 및 알림 서비스를 사용할 수 없습니다. 따라서 관련 정책이 모두 완료됐는지 확인할 수 없습니다. 관련 정책을 검토하지 않았다면, 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',

	/*Engagement Comments*/
	38001: 'Engageement comment 생성 호출 실패',
	38002: '모든 engagement comment 가져오기 호출 실패',
	38003: '이 작업을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	38004: '이 작업을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	38005: '이 작업을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	38006: '이 작업을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	38007: '이 작업을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	38008: '이 작업을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	38009: '이 작업을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	38010: '본문은 null일 수 없습니다',
	38011: 'Comment 텍스트는 null이거나 비워둘 수 없습니다',
	38012: '주어진 engagement에 기업이 존재하지 않습니다',
	38013: 'Engagement comment 상태 ID는 0보다 커야 합니다',
	38014: 'Parent engagement comment ID는 0보다 커야 합니다',
	38015: '지정 기준과 일치하는 Canvas 양식 본문이 없습니다',
	38016: '회신하려는 노트를 다른 팀 멤버가 삭제했습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	38017: '제공된 parent engagement comment가 처리됐습니다',
	38018: '제공된 parent engagement comment는 회신 그 자체입니다',
	38019: '제공된 comment 텍스트는 1자보다 짧으면 안 되며 4,000자보다 길어도 안 됩니다',
	38020: '제공된 기업 UID가 유효하지 않습니다',
	38021: '제공된 parent 기업 UID가 유효하지 않습니다',
	38022: 'Engagememt comment 삭제 호출 실패',
	38023: 'Comment ID는 비워둘 수 없습니다',
	38024: '작업은 유효한 작업이어야 합니다',
	38025: '더 이상 존재하지 않는 comment를 삭제하려고 합니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	38026: '귀하는 comment의 소유자가 아닙니다',
	38027: '업데이트할 comment가 없습니다',
	38028: '처리된 comment에 대한 업데이트는 허용되지 않습니다',
	38029: '작성자만 comment 텍스트를 변경할 수 있습니다',
	38030: 'Engagement comment ID가 유효하지 않습니다',
	38031: 'Comment 상태 ID는 비워둘 수 없습니다',
	38032: '요청된 사용자와 engagement 간 관계 기록이 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	38033: '업데이트 작업이 유효하지 않습니다',
	38034: '요청된 comment ID가 이미 다른 comment에 사용되고 있습니다',
	38035: '제공된 comment ID가 유효하지 않습니다',
	38036: '문서 ID는 null이거나 비워둘 수 없습니다',
	38037: 'Comment가 배정될 사용자 ID는 null일 수 없습니다',
	38038: 'Comment를 열거나 닫는 동안 재배정하면 안 됩니다',
	38039: '삭제하려는 회신이 있는 노트가 이미 삭제됐습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	38040: '회신에 만료일이 없어야 합니다',
	38041: '회신에 우선순위가 없어야 합니다',
	38042: 'Comment는 만료일이 있어야 합니다',
	38043: 'Comment는 우선순위가 있어야 합니다',
	38044: '더 이상 존재하지 않는 comment에 대한 회신을 편집하려고 합니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	38045: '사용자에게 배정된 Engagement 상태 ID와 그 내용, 또는 우선순위를 모두 편집하는 것은 허용되지 않습니다',
	38046: 'Engagement comment 업데이트 호출 실패',
	38047: '처리 전 노트에만 회신할 수 있습니다',
	39004: 'This tag group is no longer available. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*Risk-Estimate*/
	4064: '이 위험을 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
	4065: '위험 연결을 편집할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	4066: '이 추정치를 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',

	/*IT App/SO*/
	81001: '모든 IT Application 가져오기 실패',
	81002: '모든 IT Application 가져오기 - 유효하지 않은 engagement ID',
	81003: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81004: '작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	81005: '작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	81006: '유효하지 않은 전략 유형',
	81007: '유효하지 않은 문서 ID',
	81008: 'ID별 IT Application 가져오기 실패',
	81009: '작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	81010: '작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	81011: '작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	81012: 'IT Application ID는 null이거나 비워둘 수 없습니다',
	81013: '연결을 편집할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
	81014: 'IT Application IT Process 삭제 호출 실패',
	81015: 'IT Application IT Process ID는 비워둘 수 없습니다',
	81016: '유효하지 않은 IT Application IT Process ID',
	81017: '연결을 편집할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
	81018: 'IT Application IT Process 생성 호출 실패',
	81019: 'IT Application 대신 서비스조직을 통과했습니다',
	81020: '삭제를 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	81039: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81041: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/* ITControl */
	84001: 'IT 통제를 가져오지 못했습니다',
	84002: 'ID별 IT 통제를 가져오지 못했습니다',
	84003: 'IT 통제 ID가 null이거나 비어 있습니다',
	84004: '유효하지 않은 IT 통제 ID',
	84005: '문서 ID가 유효하지 않습니다',
	84006: 'IT 통제 이름 누락',
	84007: '유효하지 않은 IT 통제 이름 길이',
	84008: '유효하지 않은 IT 통제 빈도 ID',
	84009: '유효하지 않은 IT 통제 접근법 유형 ID',
	84010: '유효하지 않은 IT 통제 설계 효과성 유형 ID',
	84011: '유효하지 않은 IT 통제 테스트 값',
	84012: '유효하지 않은 IT 통제 운영 효과성 유형 ID',
	84013: 'IT 통제 빈도 누락',
	84014: 'IT 통제 삭제 실패',
	84015: '검색 문자열은 100자를 초과할 수 없습니다',
	84016: '작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	84018: '작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	84019: '업데이트를 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',

	/*ITRisk*/
	86001: 'IT 위험 이름은 비워둘 수 없습니다',
	86002: 'IT 위험 이름은 500자를 초과할 수 없습니다',
	86003: '작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	86004: 'ID별 IT 위험 가져오기 호출 실패',
	86005: '이 작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	86006: '연결을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	86007: '이 작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	86008: '연결을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	86009: '이 작업을 완료할 수 없었습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	86010: '기술 위험 삭제 실패',

	/*RiskFactorFormHeaders*/
	89001: '위험요소 연결을 찾을 수 없습니다',
	89002: '문서를 찾을 수 없습니다',
	89003: '문서 ID 누락',
	89004: '근거 길이가 4000자를 초과합니다',
	89005: '선택한 위험요소에 위험을 연결할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	89014: '위험 ID가 유효하지 않습니다',
	89020: '위험요소를 저장할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',

	/*Materiality*/
	91001: '중요성을 찾을 수 없습니다',
	91002: '데이터를 저장할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	91003: '유효하지 않은 중요성 업데이트 요청',
	91004: '보고될 것으로 예상되는 연환산 금액 누락',
	91005: '보고될 것으로 예상되는 연환산 금액을 참으로 업데이트할 때, 기준 예측 금액을 지정해야 합니다',
	91006: '기준 예측 금액은 15자리를 초과하거나 소수점 이하 자릿수를 포함할 수 없습니다',
	91007: '보고될 것으로 예상되는 연환산 금액을 참으로 업데이트할 때, 기준 예측 금액은 null이어야 합니다',
	91008: '예측 기준금액 근거가 너무 짧거나 너무 깁니다.',
	91009: '유효하지 않은 중요성 ID',
	91010: '유효하지 않은 중요성 본문 유형 ID',
	91011: 'Nominal amount는 범위 상한을 초과할 수 없습니다',
	91012: '중요성 금액은 15자리와 소수점 이하 4자리를 초과할 수 없습니다',

	/*Group Structure - Sub Scopes */
	92013: '연결된 지역/부문 팀이 하나 이상 있으므로 하위 범위를 삭제할 수 없습니다',
	92016: '하위 범위 이름이 이미 있습니다',

	/*Helix Account Mappings */
	94001: '계정 매핑을 저장할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	94004: 'EY Canvas 계정이 하나 이상 삭제되어 매핑할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	94005: '프로젝트를 재개할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',

	/*Group Instructions */
	98001: '그룹 지침을 검색하면서 오류가 발생했습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	98002: '하나 이상의 Knowledge 지침 섹션 ID가 유효하지 않습니다',
	98003: '그룹 지침을 생성하면서 오류가 발생했습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	98004: '지침 이름은 비워둘 수 없습니다',
	98005: '지침 이름은 500자를 초과할 수 없습니다',
	98006: '지침 설명은 비워둘 수 없습니다',
	98007: '지침 설명은 30,000자를 초과할 수 없습니다',
	98009: '지침 이름은 중복될 수 없습니다',
	98010: '만료일은 비워둘 수 없습니다',
	98011: '지침이 이미 삭제됐습니다',
	98012: '지침을 찾을 수 없습니다',
	98013: '지침 ID는 0보다 커야 합니다',
	98014: '그룹 지침을 저장하면서 오류가 발생했습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	98015: '지침 필수 범위는 삭제될 수 없습니다',
	98016: '지침이 삭제됐습니다',
	98017: '그룹 task가 이미 삭제됐습니다',
	98018: '삭제 후 그룹 task를 편집하십시오',
	98019: '기업을 찾을 수 없습니다',

	98020: '위험평가 패키지를 생성할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',

	98021: '문서 이름은 비워둘 수 없습니다',
	98022: '문서 이름은 115자를 초과할 수 없습니다',
	98023: '문서 이름은 유효하지 않은 XML 문자를 포함할 수 없습니다',
	98024: '패키지 생성 프로세스가 진행 중입니다, 완료까지 최대 10분이 소요될 수 있습니다',
	98025: '일부 지침에 연결된 부문이 없습니다. 지침에 부문을 배정하고 나서, 그룹 위험평가 커뮤니케이션을 다시 생성하십시오.',
	98026: '일부 지침에 연결된 계정이 없습니다. 부문에 계정을 연결하고 나서 그룹 위험평가 커뮤니케이션을 다시 생성하십시오.',
	98027: '일부 계정에 계정 문서가 없습니다. 계정 문서를 생성하고 나서 그룹 위험평가 커뮤니케이션을 다시 생성하십시오.',
	98028: '이 Canvas 양식이 유효한 task에 연결되어 있지 않습니다',
	98029: '하나 이상의 부문이 참조 전용입니다',
	98030: '하나 이상의 부문이 이 primary team engagement에서 오지 않았습니다',
	98031: '하나 이상의 범위가 이 primary team engagement에서 오지 않았습니다',
	98032: '문서 개수가 한도를 초과했습니다',
	98033: '필수 범위는 지침에서 삭제될 수 없습니다',

	/*Estimate */
	115017: '추정치를 찾을 수 없습니다',

	/* Group Audit */
	106003: '오류. 새로 고침하여 다시 시도하십시오.',

	/* TasksOverview */
	117001: '모든 task 개요 가져오기 호출 실패',
	117002: '모든 task 개요 가져오기 요청은 비워둘 수 없습니다',
	117003: '유효하지 않은 Engagement ID',
	117004: '유효하지 않은 task 범주 값',
	117005: '유효하지 않은 보기 값',
	117006: '유효하지 않은 문서 범주 검색 값',
	117007: '중복 Task 범주 ID',
	117008: '중복 시기 ID',
	117009: '중복 Task ID',
	117010: '중복 문서 ID',
	117011: '중복 담당 사용자 ID',

	/* Multientity */
	114001: '모든 Multi-entity 가져오기 실패',
	114002: 'ST 기업 이름은 비워둘 수 없습니다',
	114003: 'ST 기업 이름은 500자를 초과할 수 없습니다',
	114004: 'ST 법인 이름은 비워둘 수 없습니다',
	114005: 'ST 법인 이름은 500자를 초과할 수 없습니다',
	114006: 'Multi-entity는 MEST Engagement에 대해서면 생성될 수 있습니다',
	114007: 'Multi-entity 계정 생성 호출 실패',
	114008: '선택한 ST 기업이 삭제됐습니다. 업데이트된 목록을 보려면 이 모달을 닫으십시오.',
	114009: '유효하지 않은 계정 ID',
	114010: 'ST 기업 이름은 100자를 초과할 수 없습니다',
	114011: 'ST 기업 프로필 제출 요청이 유효하지 않습니다',
	114012: 'ST 기업 프로필 제출 요청 본문이 유효하지 않습니다',
	114013: 'ST 기업 프로필 제출 요청 본문에 구별되는 ST 기업 ID가 있어야 합니다',
	114014: '프로필 요청을 제출하기 위한 ST 기업 ID가 유효하지 않습니다',
	114015: '양식에 미완료 응답이 있습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	114016: '이를 위해 내용을 업데이트할 수 없습니다',
	114017: 'Engagement에 Multi-entity 개별 프로필 문서가 없습니다',
	114018: 'Multi-entity 개별 프로필이 누락된 ST 기업 ID',
	114019: '매핑하려는 기업 중 하나 이상이 이 engagement에 더이상 존재하지 않습니다. 페이지를 새로 고침하여 다시 시도하십시오.',
	114020: 'ST 기업 이름은 비워둘 수 없습니다',
	114021: '유효하지 않은 문서 ID',
	114022: '유효하지 않은 Engagement ID',
	114023: 'ST 기업 문서 기록이 이미 있습니다',
	114024: 'ST 기업 문서 기록이 없습니다',
	114025: 'ST 기업 문서 기록은 시스템에 연결되어 있습니다',
	114026: '유효하지 않은 요청 본문',
	114028: '각 기업에 하나의 프로필 문서가 없습니다',
	114035: 'ST기업 연결이 이미 있습니다',
	114036: '모든 기업 업데이트를 요청할 때 프로필 문서가 하나 이상 유효해야 합니다',
	114037: 'ST기업과 계정 연결이 이미 제거됐습니다',
	114038: 'Get all MultiEntity layers failed.',
	114039: 'Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',
	114040: 'Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',

	/* Sample List */
	121101: '유효하지 않은 표본 목록 ID',
	121008: '유효하지 않은 표본 목록 ID',
	121011: '이 표본을 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
	121013: '이 engagement에서 이 표본을 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',
	121016: '이 표본을 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
	121037: '이 작업을 수행하는 동안 오류가 발생했습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
	121012: '속성 상태를 업데이트할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	121025: '이 문서를 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
	121027: '문서 연결을 편집할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	121028: '문서 연결을 편집할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	121014: '속성 상태를 업데이트할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	121029: '이 정보를 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.',
	121021: '속성 상태를 업데이트할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',

	/*Control Attributes */
	122018: '작업을 완료할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	122021: '이 작업을 수행하는 동안 오류가 발생하였습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면 헬프 데스크에 문의하십시오.',

	/*Flow chart*/
	123031: '양식 본문 ID는 여러 flowchart에 링크할 수 없습니다',

	1034: 'This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	1035: 'This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	/*Group Instructions */
	196033: '지침에 배정 항목이 0개일 수 없으며 최소 1개 이상 있어야 합니다',

	/*Information Security */
	200001: '수행한 작업은 EY 정보 보안에 의해 금지됐습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',

	/*Tags */
	40007: '이 태그를 더 이상 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
	40029: '태그 연결을 편집할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면, 헬프 데스크에 문의하십시오.',
};

export const roleForMember = [{
	id: 1,
	role: 'Partner in Charge',
	roleAbbreviation: 'PIC'
},
{
	id: 2,
	role: 'Engagement Partner',
	roleAbbreviation: 'EP'
},
{
	id: 3,
	role: 'Executive Director',
	roleAbbreviation: 'ED'
},
{
	id: 4,
	role: 'Principal',
	roleAbbreviation: 'Principle'
},
{
	id: 5,
	role: 'Senior Manager',
	roleAbbreviation: 'Sr. Manager'
},
{
	id: 6,
	role: 'Manager',
	roleAbbreviation: 'Manager'
},
{
	id: 7,
	role: 'Senior',
	roleAbbreviation: 'Senior'
},
{
	id: 8,
	role: 'Staff',
	roleAbbreviation: 'Staff'
},
{
	id: 9,
	role: 'Intern',
	roleAbbreviation: 'Intrn'
},
{
	id: 10,
	role: 'Engagement Quality Reviewer',
	roleAbbreviation: 'EQR'
},
{
	id: 11,
	role: 'Other Partner',
	roleAbbreviation: 'Other Partner'
},
{
	id: 12,
	role: 'GDS - Staff',
	roleAbbreviation: 'GDS Staff'
},
{
	id: 13,
	role: 'Advisory (ITRA,TAS,Human Capital or Other)',
	roleAbbreviation: 'ADV'
},
{
	id: 14,
	role: 'Tax',
	roleAbbreviation: 'Tax'
},
{
	id: 15,
	role: 'Executive Support Services',
	roleAbbreviation: 'ESS'
},
{
	id: 16,
	role: 'General Counsel',
	roleAbbreviation: 'GCO'
},
{
	id: 17,
	role: 'Audit Quality Reviewer',
	roleAbbreviation: 'AQR'
},
{
	id: 18,
	role: 'ML Component Team',
	roleAbbreviation: 'MCT'
},
{
	id: 19,
	role: 'Client Supervisor',
	roleAbbreviation: 'C. Supervisor'
},
{
	id: 20,
	role: 'Client Staff',
	roleAbbreviation: 'C. Staff'
},
{
	id: 21,
	role: 'Internal Audit Supervisor',
	roleAbbreviation: 'IA Supervisor'
},
{
	id: 22,
	role: 'Internal Audit Staff',
	roleAbbreviation: 'IA Staff'
},
{
	id: 23,
	role: 'Regulator',
	roleAbbreviation: 'Regulator'
},
{
	id: 24,
	role: 'Other (e.g. due diligence review)',
	roleAbbreviation: 'Other'
},
{
	id: 25,
	role: 'Office',
	roleAbbreviation: 'Office'
},
{
	id: 26,
	role: 'Area',
	roleAbbreviation: 'Area'
},
{
	id: 27,
	role: 'Industry',
	roleAbbreviation: 'IND'
},
{
	id: 28,
	role: 'National',
	roleAbbreviation: 'NAT'
},
{
	id: 29,
	role: 'Global',
	roleAbbreviation: 'GBL'
},
{
	id: 30,
	role: 'GDS - Senior',
	roleAbbreviation: 'GDS Senior'
},
{
	id: 31,
	role: 'GDS - Manager',
	roleAbbreviation: 'GDS Manager'
}
];

export const accountConclusionTabs = {
	conclusions: '결론'
};

export const assertions = [{
	id: 1,
	assertionname: '완전성',
	assertionabbreviation: 'C',
	statementtypeid: 2,
	displayorder: 1
},
{
	id: 2,
	assertionname: '실재성',
	assertionabbreviation: 'E',
	statementtypeid: 2,
	displayorder: 2
},
{
	id: 3,
	assertionname: '평가',
	assertionabbreviation: 'V',
	statementtypeid: 2,
	displayorder: 3
},
{
	id: 4,
	assertionname: '권리와 의무',
	assertionabbreviation: 'R&O',
	statementtypeid: 2,
	displayorder: 4
},
{
	id: 5,
	assertionname: '표시와 공시',
	assertionabbreviation: 'P&D',
	statementtypeid: 2,
	displayorder: 5
},
{
	id: 6,
	assertionname: '완전성',
	assertionabbreviation: 'C',
	statementtypeid: 1,
	displayorder: 6
},
{
	id: 7,
	assertionname: '발생사실',
	assertionabbreviation: 'O',
	statementtypeid: 1,
	displayorder: 7
},
{
	id: 8,
	assertionname: '측정',
	assertionabbreviation: 'M',
	statementtypeid: 1,
	displayorder: 8
},
{
	id: 9,
	assertionname: '표시와 공시',
	assertionabbreviation: 'P&D',
	statementtypeid: 1,
	displayorder: 9
},
{
	id: 10,
	assertionname: '완전성',
	assertionabbreviation: 'C',
	statementtypeid: 3,
	displayorder: 10
},
{
	id: 11,
	assertionname: '실재성/발생사실',
	assertionabbreviation: 'E/O',
	statementtypeid: 3,
	displayorder: 11
},
{
	id: 12,
	assertionname: '측정/평가',
	assertionabbreviation: 'M/V',
	statementtypeid: 3,
	displayorder: 12
},
{
	id: 13,
	assertionname: '권리와 의무',
	assertionabbreviation: 'R&O',
	statementtypeid: 3,
	displayorder: 13
},
{
	id: 14,
	assertionname: '표시와 공시',
	assertionabbreviation: 'P&D',
	statementtypeid: 3,
	displayorder: 14
}
];

export const documentChangeTypesOptions = [{
	value: 1,
	label: '비관리적 변경사항'
},
{
	value: 2,
	label: '변경사항 추적 기능이 사용된 경우 수정사항 수락'
},
{
	value: 3,
	label: '이미 존재하는 증거에 상호참조 추가'
},
{
	value: 4,
	label: '이전에 팩스 또는 이메일로 수신한 조회서 회신 원본 추가'
},
{
	value: 5,
	label: '미관 변경'
},
{
	value: 6,
	label: 'Form AP 및 substantial role 평가 완료'
},
{
	value: 7,
	label: '대체된 문서 삭제 또는 폐기'
},
{
	value: 8,
	label: 'Management letter 작성'
},
{
	value: 9,
	label: 'Archive 프로세스 관련 체크리스트 완료 시 sign-off'
},
{
	value: 10,
	label: '최종 문서 정렬, 대조 및 상호참조',
},
{
	value: 12,
	label: 'MEST 전용: EY Canvas에 있는 보고서일보다 보고서일이 늦은 기업과 관련된 증거 수정'
},
{
	value: 11,
	label: '현지 시간대로 조정하면, 보고서일 이전에 수정됐습니다 (Americas 전용)',
}
];

export const KnowledgeFormProfileAnswer = [{
	value: 1,
	label: '복잡',
	display: true
},
{
	value: 2,
	label: '비복잡',
	display: true
},
{
	value: 3,
	label: '상장',
	display: true
},
{
	value: 4,
	label: '비상장',
	display: false
},
{
	value: 5,
	label: 'PCAOB - IA',
	display: true
},
{
	value: 6,
	label: 'Non-PCAOB-IA',
	display: false
},
{
	value: 7,
	label: 'PCAOB - FS',
	display: true
},
{
	value: 8,
	label: 'Non-PCAOB-FS',
	display: false
},
{
	value: 9,
	label: '그룹 감사',
	display: true
},
{
	value: 10,
	label: '비그룹 감사',
	display: false
},
{
	value: 11,
	label: 'Digital',
	display: true
},
{
	value: 12,
	label: 'Core',
	display: true
}
];

export const strategyType = [{
	StrategyTypeId: 1,
	StrategyTypeName: '통제'
},
{
	StrategyTypeId: 2,
	StrategyTypeName: '실증'
}
];

export const controlTypeName = {
	1: 'Application',
	2: 'ITDM',
	3: '수동 예방',
	4: '수동 적발'
};

export const inCompleteList = [{
	value: 1,
	label: '미완료',
	title: '미완료'
}];

export const scotTypes = [{
	value: 1,
	label: '일상적',
	title: '일상적',
	isDisabled: false
},
{
	value: 2,
	label: '비일상적',
	title: '비일상적',
	isDisabled: false
},
{
	value: 3,
	label: '추정',
	title: '추정',
	isDisabled: false
}
];

export const scotTypesNew = [{
	value: 1,
	label: '일상적',
	title: '일상적',
	isDisabled: false
},
{
	value: 2,
	label: '비일상적',
	title: '비일상적',
	isDisabled: false
},
{
	value: 3,
	label: '추정',
	title: '추정',
	isDisabled: false
},
{
	value: 4,
	label: 'FSCP',
	title: 'FSCP',
	isDisabled: false
}
];

export const estimationTypes = [{
	value: 1,
	label: '아니오',
	title: '아니오',
	isDisabled: false
},
{
	value: 2,
	label: '예',
	title: '예',
	isDisabled: false
}
];

/* IT Control */
export const itApproachType = [{
	ITApproachTypeId: 1,
	ITApproachTypeName: '통제'
},
{
	ITApproachTypeId: 2,
	ITApproachTypeName: '실증'
}
];

/*Controls */
export const controlFrequencyType = [{
	value: 1,
	label: '하루에 여러 번',
	title: '하루에 여러 번'
},
{
	value: 2,
	label: '매일',
	title: '매일'
},
{
	value: 3,
	label: '매주',
	title: '매주'
},
{
	value: 4,
	label: '매월',
	title: '매월'
},
{
	value: 5,
	label: '매분기',
	title: '매분기'
},
{
	value: 6,
	label: '매년',
	title: '매년'
},
{
	value: 8,
	label: '기타',
	title: '기타'
}
];

export const controlTypes = [{
	value: 1,
	label: 'IT Application 통제'
},
{
	value: 2,
	label: 'IT 의존 수동 통제'
},
{
	value: 3,
	label: '수동 예방'
},
{
	value: 4,
	label: '수동 적발'
}
];

export const strategyTypeCheck = {
	1: '통제',
	2: '실증'
};

export const designEffectivenessType = [{
	DesignEffectivenessTypeId: 1,
	DesignEffectivenessTypeName: '효과적'
},
{
	DesignEffectivenessTypeId: 2,
	DesignEffectivenessTypeName: '비효과적'
}
];

export const controlEffectivenessType = [{
	ControlEffectivenessTypeId: 1,
	ControlEffectivenessTypeName: '효과적'
},
{
	ControlEffectivenessTypeId: 2,
	ControlEffectivenessTypeName: '비효과적'
}
];

export const testing = [{
	testingId: 1,
	testingDescription: '예'
},
{
	testingId: 2,
	testingDescription: '아니오'
}
];

export const controlType = [{
	value: 1,
	label: 'IT Application 통제',
	title: 'IT Application 통제'
},
{
	value: 2,
	label: 'IT 의존 수동 통제',
	title: 'IT 의존 수동 통제'
},
{
	value: 3,
	label: '수동 예방',
	title: '수동 예방'
},
{
	value: 4,
	label: '수동 적발',
	title: '수동 적발'
}
];

export const aggregateITEvaluationType = [{
	value: 1,
	label: 'Support',
	title: 'Support'
},
{
	value: 2,
	label: 'Not Support',
	title: 'Not Support'
},
{
	value: 3,
	label: 'FS & ICFR Support',
	title: 'FS & ICFR Support'
},
{
	value: 4,
	label: 'FS Only Support',
	title: 'FS Only Support'
}
];

export const KnowledgeFormProfileQuestion = [{
	value: 1,
	label: '복잡'
},
{
	value: 2,
	label: '상장'
},
{
	value: 3,
	label: 'PCAOB - IA'
},
{
	value: 4,
	label: 'PCAOB - FS'
},
{
	value: 5,
	label: '위치'
},
{
	value: 6,
	label: '언어'
},
{
	value: 7,
	label: '그룹 감사'
},
{
	value: 8,
	label: 'Digital'
}
];

export const KnowledgeLanguage = [{
	value: 1,
	label: 'English'
},
{
	value: 2,
	label: 'Spanish (Latin America)'
},
{
	value: 3,
	label: 'French (Canada)'
},
{
	value: 4,
	label: 'Dutch'
},
{
	value: 5,
	label: 'Croatian'
},
{
	value: 6,
	label: 'Czech'
},
{
	value: 7,
	label: 'Danish'
},
{
	value: 8,
	label: 'Finnish'
},
{
	value: 9,
	label: 'German (Germany, Austria)',
},
{
	value: 10,
	label: 'Hungarian'
},
{
	value: 11,
	label: 'Italian'
},
{
	value: 12,
	label: 'Japanese (Japan)'
},
{
	value: 13,
	label: 'Norwegian (Norway)'
},
{
	value: 14,
	label: 'Polish'
},
{
	value: 15,
	label: 'Slovak'
},
{
	value: 16,
	label: 'Slovenian'
},
{
	value: 17,
	label: 'Swedish'
},
{
	value: 18,
	label: 'Arabic'
},
{
	value: 19,
	label: 'Simplified Chinese (China)'
},
{
	value: 20,
	label: 'Traditional Chinese (Taiwan)'
},
{
	value: 21,
	label: 'Greek'
},
{
	value: 22,
	label: 'Hebrew (Israel)'
},
{
	value: 23,
	label: 'Indonesian'
},
{
	value: 24,
	label: 'Korean (Republic of Korea)'
},
{
	value: 25,
	label: 'Portuguese (Brazil)'
},
{
	value: 26,
	label: 'Romanian'
},
{
	value: 27,
	label: 'Russian (Russia)'
},
{
	value: 28,
	label: 'Thai'
},
{
	value: 29,
	label: 'Turkish'
},
{
	value: 30,
	label: 'Vietnamese'
},
{
	value: 31,
	label: 'PCAOB - English'
},
{
	value: 32,
	label: 'PCAOB - Spanish (Latin America)'
},
{
	value: 33,
	label: 'PCAOB - French (Canada)'
},
{
	value: 34,
	label: 'PCAOB - Dutch'
},
{
	value: 35,
	label: 'PCAOB - Croatian'
},
{
	value: 36,
	label: 'PCAOB - Czech'
},
{
	value: 37,
	label: 'PCAOB - Danish'
},
{
	value: 38,
	label: 'PCAOB - Finnish'
},
{
	value: 39,
	label: 'PCAOB - German (Germany, Austria)',
},
{
	value: 40,
	label: 'PCAOB - Hungarian'
},
{
	value: 41,
	label: 'PCAOB - Italian'
},
{
	value: 42,
	label: 'PCAOB - Japanese (Japan)'
},
{
	value: 43,
	label: 'PCAOB - Norwegian (Norway)'
},
{
	value: 44,
	label: 'PCAOB - Polish'
},
{
	value: 45,
	label: 'PCAOB - Slovak'
},
{
	value: 46,
	label: 'PCAOB - Slovenian'
},
{
	value: 47,
	label: 'PCAOB - Swedish'
},
{
	value: 48,
	label: 'PCAOB - Arabic'
},
{
	value: 49,
	label: 'PCAOB - Simplified Chinese (China)'
},
{
	value: 50,
	label: 'PCAOB - Traditional Chinese (Taiwan)'
},
{
	value: 51,
	label: 'PCAOB - Greek'
},
{
	value: 52,
	label: 'PCAOB - Hebrew (Israel)'
},
{
	value: 53,
	label: 'PCAOB - Indonesian'
},
{
	value: 54,
	label: 'PCAOB - Korean (Republic of Korea)'
},
{
	value: 55,
	label: 'PCAOB - Portuguese (Brazil)'
},
{
	value: 56,
	label: 'PCAOB - Romanian'
},
{
	value: 57,
	label: 'PCAOB - Russian (Russia)'
},
{
	value: 58,
	label: 'PCAOB - Thai'
},
{
	value: 59,
	label: 'PCAOB - Turkish'
},
{
	value: 60,
	label: 'PCAOB - Vietnamese'
}
];

export const KnowledgeCountry = [{
	value: 1,
	label: 'Mayotte'
},
{
	value: 2,
	label: 'British Virgin Islands'
},
{
	value: 3,
	label: 'Spain'
},
{
	value: 4,
	label: 'Belize'
},
{
	value: 5,
	label: 'Peru'
},

{
	value: 6,
	label: 'Slovakia'
},
{
	value: 7,
	label: 'Venezuela'
},
{
	value: 8,
	label: 'Norway'
},
{
	value: 9,
	label: 'Falkland Islands (Malvinas)'
},
{
	value: 10,
	label: 'Mozambique'
},

{
	value: 11,
	label: 'China'
},
{
	value: 12,
	label: 'Sudan'
},
{
	value: 13,
	label: 'Israel'
},
{
	value: 14,
	label: 'Belgium'
},
{
	value: 15,
	label: 'Saudi Arabia'
},

{
	value: 16,
	label: 'Gibraltar'
},
{
	value: 17,
	label: 'Guam'
},
{
	value: 18,
	label: 'Norfolk Islands'
},
{
	value: 19,
	label: 'Zambia'
},
{
	value: 20,
	label: 'Reunion'
},

{
	value: 21,
	label: 'Azerbaijan'
},
{
	value: 22,
	label: 'Saint Helena'
},
{
	value: 23,
	label: 'Iran'
},
{
	value: 24,
	label: 'Monaco'
},
{
	value: 25,
	label: 'Saint Pierre and Miquelon'
},

{
	value: 26,
	label: 'New Zealand'
},
{
	value: 27,
	label: 'Cook Islands'
},
{
	value: 28,
	label: 'Saint Lucia'
},
{
	value: 29,
	label: 'Zimbabwe'
},
{
	value: 30,
	label: 'Iraq'
},

{
	value: 31,
	label: 'Tonga'
},
{
	value: 32,
	label: 'American Samoa'
},
{
	value: 33,
	label: 'Maldives'
},
{
	value: 34,
	label: 'Morocco'
},
{
	value: 35,
	label: '국제감사기준 (ISA)'
},

{
	value: 36,
	label: 'Albania'
},
{
	value: 37,
	label: 'Afghanistan'
},
{
	value: 38,
	label: 'Gambia'
},
{
	value: 39,
	label: 'Burkina Faso'
},
{
	value: 40,
	label: 'Tokelau'
},

{
	value: 41,
	label: 'Libya'
},
{
	value: 42,
	label: 'Canada'
},
{
	value: 43,
	label: 'United Arab Emirates'
},
{
	value: 44,
	label: 'Korea,Democratic Peoples Republic of',
},
{
	value: 45,
	label: 'Montserrat'
},

{
	value: 46,
	label: 'Greenland'
},
{
	value: 47,
	label: 'Rwanda'
},
{
	value: 48,
	label: 'Fiji'
},
{
	value: 49,
	label: 'Djibouti'
},
{
	value: 50,
	label: 'Botswana'
},

{
	value: 51,
	label: 'Kuwait'
},
{
	value: 52,
	label: 'Madagascar'
},
{
	value: 53,
	label: 'Isle of Man'
},
{
	value: 54,
	label: 'Hungary'
},
{
	value: 55,
	label: 'Namibia'
},

{
	value: 56,
	label: 'Malta'
},
{
	value: 57,
	label: 'Jersey'
},
{
	value: 58,
	label: 'Thailand'
},
{
	value: 59,
	label: 'Saint Kitts and Nevis'
},
{
	value: 60,
	label: 'Bhutan'
},

{
	value: 61,
	label: 'Panama'
},
{
	value: 62,
	label: 'Somalia'
},
{
	value: 63,
	label: 'Bahrain'
},
{
	value: 64,
	label: 'Bosnia and Herzegovina'
},
{
	value: 65,
	label: 'France'
},

{
	value: 66,
	label: 'Korea,Republic of',
},
{
	value: 67,
	label: 'Iceland'
},
{
	value: 68,
	label: 'Portugal'
},
{
	value: 69,
	label: 'Tunisia'
},
{
	value: 70,
	label: 'Ghana'
},

{
	value: 71,
	label: 'Cameroon'
},
{
	value: 72,
	label: 'Greece'
},
{
	value: 73,
	label: 'French Southern Territories'
},
{
	value: 74,
	label: 'Heard and McDonald Islands'
},
{
	value: 75,
	label: 'Andorra'
},

{
	value: 76,
	label: 'Luxembourg'
},
{
	value: 77,
	label: 'Samoa'
},
{
	value: 78,
	label: 'Anguilla'
},
{
	value: 79,
	label: 'Netherlands'
},
{
	value: 80,
	label: 'Guinea-Bissau'
},

{
	value: 81,
	label: 'Nicaragua'
},
{
	value: 82,
	label: 'Paraguay'
},
{
	value: 83,
	label: 'Antigua and Barbuda'
},
{
	value: 84,
	label: '국제회계기준 (IFRS)'
},
{
	value: 85,
	label: 'Niger'
},

{
	value: 86,
	label: 'Egypt'
},
{
	value: 87,
	label: 'Vatican City State'
},
{
	value: 88,
	label: 'Latvia'
},
{
	value: 89,
	label: 'Cyprus'
},
{
	value: 90,
	label: 'US Minor Outlying Islands'
},

{
	value: 91,
	label: 'Russia'
},
{
	value: 92,
	label: 'Saint Vincent and the Grenadines'
},
{
	value: 93,
	label: 'Guernsey'
},
{
	value: 94,
	label: 'Burundi'
},
{
	value: 95,
	label: 'Cuba'
},

{
	value: 96,
	label: 'Equatorial Guinea'
},
{
	value: 97,
	label: 'British Indian Ocean Territory'
},
{
	value: 98,
	label: 'Sweden'
},
{
	value: 99,
	label: 'Uganda'
},
{
	value: 100,
	label: 'Macedonia,the Former Yugoslav Republic of',
},

{
	value: 101,
	label: 'Swaziland'
},
{
	value: 102,
	label: 'El Salvador'
},
{
	value: 103,
	label: 'Kyrgyzstan'
},
{
	value: 104,
	label: 'Ireland'
},
{
	value: 105,
	label: 'Kazakhstan'
},

{
	value: 106,
	label: 'Honduras'
},
{
	value: 107,
	label: 'Uruguay'
},
{
	value: 108,
	label: 'Georgia'
},
{
	value: 109,
	label: 'Trinidad and Tobago'
},
{
	value: 110,
	label: 'Palestinian Authority'
},

{
	value: 111,
	label: 'Martinique'
},
{
	value: 112,
	label: 'Aland Islands'
},
{
	value: 113,
	label: 'French Polynesia'
},
{
	value: 114,
	label: 'Ivory Coast'
},
{
	value: 115,
	label: 'Montenegro'
},

{
	value: 116,
	label: 'South Africa'
},
{
	value: 117,
	label: 'South Georgia and the South Sandwich Islands'
},
{
	value: 118,
	label: 'Yemen'
},
{
	value: 119,
	label: 'Hong Kong China'
},
{
	value: 120,
	label: 'Kenya'
},

{
	value: 121,
	label: 'Chad'
},
{
	value: 122,
	label: 'Colombia'
},
{
	value: 123,
	label: 'Costa Rica'
},
{
	value: 124,
	label: 'Angola'
},
{
	value: 125,
	label: 'Lithuania'
},

{
	value: 126,
	label: 'Syria'
},
{
	value: 127,
	label: 'Malaysia'
},
{
	value: 128,
	label: 'Sierra Leone'
},
{
	value: 129,
	label: 'Serbia'
},
{
	value: 130,
	label: 'Poland'
},

{
	value: 131,
	label: 'Suriname'
},
{
	value: 132,
	label: 'Haiti'
},
{
	value: 133,
	label: 'Nauru'
},
{
	value: 134,
	label: 'Sao Tome and Principe'
},
{
	value: 135,
	label: 'Svalbard and Jan Mayen'
},

{
	value: 136,
	label: 'Singapore'
},
{
	value: 137,
	label: 'Moldova'
},
{
	value: 138,
	label: 'Taiwan'
},
{
	value: 139,
	label: 'Senegal'
},
{
	value: 140,
	label: 'Gabon'
},

{
	value: 141,
	label: 'Mexico'
},
{
	value: 142,
	label: 'Seychelles'
},
{
	value: 143,
	label: 'Micronesia,Federated States of',
},
{
	value: 144,
	label: 'Algeria'
},
{
	value: 145,
	label: 'Italy'
},

{
	value: 146,
	label: 'San Marino'
},
{
	value: 147,
	label: 'Liberia'
},
{
	value: 148,
	label: 'Brazil'
},
{
	value: 149,
	label: 'Croatia'
},
{
	value: 150,
	label: 'Faroe Islands'
},

{
	value: 151,
	label: 'Palau'
},
{
	value: 152,
	label: 'Finland'
},
{
	value: 153,
	label: 'Philippines'
},
{
	value: 154,
	label: 'Jamaica'
},
{
	value: 155,
	label: 'French Guiana'
},

{
	value: 156,
	label: 'Cape Verde'
},
{
	value: 157,
	label: 'Myanmar'
},
{
	value: 158,
	label: 'Lesotho'
},
{
	value: 159,
	label: 'US Virgin Islands'
},
{
	value: 160,
	label: 'Cayman Islands'
},

{
	value: 161,
	label: 'Niue'
},
{
	value: 162,
	label: 'Togo'
},
{
	value: 163,
	label: 'Belarus'
},
{
	value: 164,
	label: 'Dominica'
},
{
	value: 165,
	label: 'Indonesia'
},

{
	value: 166,
	label: 'Uzbekistan'
},
{
	value: 167,
	label: 'Nigeria'
},
{
	value: 168,
	label: 'Wallis and Futuna'
},
{
	value: 169,
	label: 'Barbados'
},
{
	value: 170,
	label: 'Sri Lanka'
},

{
	value: 171,
	label: 'United Kingdom'
},
{
	value: 172,
	label: 'Ecuador'
},
{
	value: 173,
	label: 'Guadeloupe'
},
{
	value: 174,
	label: 'Laos'
},
{
	value: 175,
	label: 'Jordan'
},

{
	value: 176,
	label: 'Solomon Islands'
},
{
	value: 177,
	label: 'East Timor'
},
{
	value: 178,
	label: 'Lebanon'
},
{
	value: 179,
	label: 'Central African Republic'
},
{
	value: 180,
	label: 'India'
},

{
	value: 181,
	label: 'Christmas Island'
},
{
	value: 182,
	label: 'Vanuatu'
},
{
	value: 183,
	label: 'Brunei'
},
{
	value: 184,
	label: 'Bangladesh'
},
{
	value: 185,
	label: 'Antarctica'
},

{
	value: 186,
	label: 'Bolivia'
},
{
	value: 187,
	label: 'Turkey'
},
{
	value: 188,
	label: 'Bahamas'
},
{
	value: 189,
	label: 'Comoros'
},
{
	value: 190,
	label: 'Western Sahara'
},

{
	value: 191,
	label: 'Czech Republic'
},
{
	value: 192,
	label: 'Ukraine'
},
{
	value: 193,
	label: 'Estonia'
},
{
	value: 194,
	label: 'Bulgaria'
},
{
	value: 195,
	label: 'Mauritania'
},

{
	value: 196,
	label: 'Congo,The Democratic Republic of the',
},
{
	value: 197,
	label: 'Liechtenstein'
},
{
	value: 198,
	label: 'Pitcairn'
},
{
	value: 199,
	label: 'Denmark'
},
{
	value: 200,
	label: 'Marshall Islands'
},

{
	value: 201,
	label: 'Japan'
},
{
	value: 202,
	label: 'Austria'
},
{
	value: 203,
	label: 'Oman'
},
{
	value: 204,
	label: 'Mongolia'
},
{
	value: 205,
	label: 'Tajikistan'
},

{
	value: 206,
	label: 'Switzerland'
},
{
	value: 207,
	label: 'Guatemala'
},
{
	value: 208,
	label: 'Eritrea'
},
{
	value: 209,
	label: 'Nepal'
},
{
	value: 210,
	label: 'Mali'
},

{
	value: 211,
	label: 'Slovenia'
},
{
	value: 212,
	label: 'Northern Mariana Islands'
},
{
	value: 213,
	label: '(해당 없음)'
},
{
	value: 214,
	label: 'Aruba'
},
{
	value: 215,
	label: 'Congo'
},

{
	value: 216,
	label: 'Qatar'
},
{
	value: 217,
	label: 'Guinea'
},
{
	value: 218,
	label: 'United States'
},
{
	value: 219,
	label: 'Ethiopia'
},
{
	value: 220,
	label: '기타'
},

{
	value: 221,
	label: 'Guyana'
},
{
	value: 222,
	label: 'Germany'
},
{
	value: 223,
	label: 'Bermuda'
},
{
	value: 224,
	label: 'Turks and Caicos Islands'
},
{
	value: 225,
	label: 'Australia'
},

{
	value: 226,
	label: 'Kiribati'
},
{
	value: 227,
	label: 'Puerto Rico'
},
{
	value: 228,
	label: 'Pakistan'
},
{
	value: 229,
	label: 'Mauritius'
},
{
	value: 230,
	label: 'Malawi'
},

{
	value: 231,
	label: 'Turkmenistan'
},
{
	value: 232,
	label: 'Cambodia'
},
{
	value: 233,
	label: 'Chile'
},
{
	value: 234,
	label: 'New Caledonia'
},
{
	value: 235,
	label: 'Papua New Guinea'
},

{
	value: 236,
	label: 'Bouvet Island'
},
{
	value: 237,
	label: 'Tuvalu'
},
{
	value: 238,
	label: 'Curacao'
},
{
	value: 239,
	label: 'Dominican Republic'
},
{
	value: 240,
	label: 'Vietnam'
},

{
	value: 241,
	label: 'Cocos (Keeling) Islands'
},
{
	value: 242,
	label: 'Grenada'
},
{
	value: 243,
	label: 'Tanzania'
},
{
	value: 244,
	label: 'Argentina'
},
{
	value: 245,
	label: 'Macau, China',
},

{
	value: 246,
	label: 'Benin'
},
{
	value: 247,
	label: 'Romania'
},
{
	value: 248,
	label: 'Armenia'
},
{
	value: 249,
	label: 'global'
},
{
	value: 250,
	label: '중소기업을 위한 IFRS'
},

{
	value: 251,
	label: 'US GAAP'
},
{
	value: 252,
	label: '중소기업을 위한 AICPA 재무보고체계'
},
{
	value: 253,
	label: 'South Sudan'
}
];

export const pagingSvgHoverText = {
	first: '첫 페이지',
	previous: '이전 페이지',
	next: '다음 페이지',
	last: '마지막 페이지'
};

export const priorityTypesForDropdown = [{
	value: 1,
	label: 'Low',
	className: 'Low'
},
{
	value: 2,
	label: 'Medium',
	className: 'Medium'
},
{
	value: 3,
	label: 'High',
	className: 'High'
},
{
	value: 4,
	label: 'Critical',
	className: 'Critical'
}
];

export const reviewNoteFilterTypes = [{
	value: 0,
	label: '모두'
},
{
	value: 1,
	label: '처리 전'
},
{
	value: 2,
	label: '처리됨'
},
{
	value: 3,
	label: '닫힘'
}
];

export const reviewStatus = [{
	id: 1,
	name: '처리 전'
},
{
	id: 2,
	name: '처리됨'
},
{
	id: 3,
	name: '닫힘'
}
];

export const reviewNoteOpenStatusOption = [{
	value: 2,
	label: '처리'
},
{
	value: 3,
	label: '닫기'
}
];

export const reviewNoteClearedStatusOption = [{
	value: 1,
	label: '다시 열기'
},
{
	value: 3,
	label: '닫기'
}
];

export const reviewNoteBulkClearedStatusOption = [{
	value: 1,
	label: '다시 열기'
},
{
	value: 2,
	label: '처리'
},
{
	value: 3,
	label: '닫기'
}
];

export const reviewNoteClosedStatusOption = [{
	value: 1,
	label: '다시 열기'
},
{
	value: 4,
	label: '삭제'
}
];

export const taskTypeBadge = {
	1: 'OST',
	2: 'PST',
	3: 'WT',
	4: 'TOC',
	5: 'OSP',
	6: 'PSP',
	7: 'RT',
	8: 'GT',
	9: 'PIC',
	10: 'EQR',
	11: 'PIC/EQR',
	22: 'ACT',
	23: 'UDP'
};

export const riskTypes = [{
	id: 1,
	name: '유의적인 위험',
	abbrev: 'SR',
	label: '유의적',
	title: '유의적인 위험'
},
{
	id: 2,
	name: '부정 위험',
	abbrev: 'FR',
	label: '부정',
	title: '부정 위험'
},
{
	id: 3,
	name: '중요왜곡표시위험',
	abbrev: 'R',
	label: '중요왜곡표시위험',
	title: '중요왜곡표시위험'
},
{
	id: 4,
	name: 'Very low risk estimate',
	abbrev: 'VLRS',
	label: 'Very low risk estimate',
	title: 'Very low risk estimate'
},
{
	id: 5,
	name: 'Lower risk estimate',
	abbrev: 'LRE',
	label: 'Lower risk estimate',
	title: 'Lower risk estimate'
},
{
	id: 6,
	name: 'Higher risk estimate',
	abbrev: 'HRE',
	label: 'Higher risk estimate',
	title: 'Higher risk estimate'
},
{
	id: 7,
	name: 'Estimate - Not selected',
	abbrev: 'ENS',
	label: 'Estimate - Not selected',
	title: 'Estimate - Not selected'
}

];

export const relatedRisksDropdownRiskTypes = [{
	id: 1,
	name: '유의적인 위험',
	abbrev: 'SR',
	label: '유의적',
	title: '유의적인 위험'
},
{
	id: 2,
	name: '부정 위험',
	abbrev: 'FR',
	label: '부정',
	title: '부정 위험'
},
{
	id: 3,
	name: '중요왜곡표시위험',
	abbrev: 'R',
	label: '중요왜곡표시위험',
	title: '중요왜곡표시위험'
}
];

export const estimateTypes = [{
	id: 4,
	name: 'Very low risk estimate',
	abbrev: 'VLRE',
	label: 'Very Low',
	title: 'Very low risk estimate'
},
{
	id: 5,
	name: 'Lower risk estimate',
	abbrev: 'LRE',
	label: 'Lower',
	title: 'Lower risk estimate'
},
{
	id: 6,
	name: 'Higher risk estimate',
	abbrev: 'HRE',
	label: 'Higher',
	title: 'Higher risk estimate'
},
{
	id: 7,
	name: 'Estimate - Not selected',
	abbrev: 'NA',
	label: '미선택',
	title: 'Estimate - Not selected'
}
];

export const statementTypes = [{
	id: 1,
	name: '손익계산서'
},
{
	id: 2,
	name: '재무상태표'
},
{
	id: 3,
	name: '둘 다'
}
];

export const RbacErrors = {
	106: '내용 편집 권한 미비. Engagement 관리자와 협의하여 충분한 권한을 부여받으십시오.'
};

export const HelixProjectValidationErrors = {
	800: '전에 EY Helix에 접속한 적이 없는 것 같습니까? Go to',
	801: '귀하는 이 프로젝트의 승인받은 팀 멤버가 아닙니다. 접근권한을 얻으려면 EY Helix 프로젝트 관리자에게 문의하십시오.',
	901: '선택한 EY Helix 프로젝트를 더 이상 사용할 수 없습니다. 신규 프로젝트를 링크하려면 EY Helix 프로젝트를 클릭하십시오.',
	902: '선택한 EY Helix 프로젝트는 삭제 표시됐습니다. 신규 프로젝트를 링크하려면 EY Helix 프로젝트를 클릭하십시오.',
	903: '선택한 EY Helix 프로젝트는 저장 표시됐습니다. 신규 프로젝트를 링크하려면 EY Helix 프로젝트를 클릭하십시오.',
	926: 'EY Helix에서 선택한 analyzer를 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	927: '링크된 프로젝트에서 분석을 사용할 수 없습니다. 계속하려면 EY Helix로 이동하여 데이터 & 분석 처리 단계를 완료하십시오.',
	928: 'EY Helix에서 analyzer가 유효하지 않거나 누락됐습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
	929: '링크된 EY Helix 프로젝트와 관련하여 오류가 발생했습니다. 데이터를 불러올 수 없습니다.'
};

export const EngagementProfileRequirementErrors = {
	108: 'Engagement 프로필이 완료되지 않았습니다'
};

export const IndependenceRequirementErrors = {
	103: 'Engagement 사용자 독립성 준수 누락'
};

export const strategyTypes = [{
	id: 3,
	name: 'In Scope'
},
{
	id: 4,
	name: 'Out of Scope'
}
];

export const itAppTypes = [{
	value: 0,
	label: 'IT Application'
},
{
	value: 1,
	label: '서비스조직'
}
];

export const confidentialityLevels = {
	[confidentialityTypes.DEFAULT]: '기본',
	[confidentialityTypes.LOW]: 'Low',
	[confidentialityTypes.MODERATE]: 'Moderate',
	[confidentialityTypes.HIGH]: 'High'

	// This has been disabled for release 2.5, uncomment if required
	// [confidentialityTypes.CONFIDENTIAL]: '기밀'
};

export const formBodyOptionRiskTypes = [{
	id: 1,
	label: '유의적인 위험'
},
{
	id: 2,
	label: '부정 위험'
},
{
	id: 3,
	label: '중요왜곡표시위험'
}
];

export const formViewTypes = [{
	value: 0,
	label: '양식'
},
{
	value: 1,
	label: '변경사항'
},
{
	value: 2,
	label: '세부정보'
}
];

export const railFilterValidations = [{
	value: 0,
	label: '모두'
},
{
	value: 1,
	label: '미완료 응답'
},
{
	value: 2,
	label: '미해결 comment'
}
];

export const aresRiskTypes = [{
	id: 1,
	name: '유의적인 위험'
},
{
	id: 2,
	name: '부정 위험'
},
{
	id: 3,
	name: '중요왜곡표시위험'
},
{
	id: 4,
	name: 'Very low risk estimate'
},
{
	id: 5,
	name: 'Lower risk estimate'
},
{
	id: 6,
	name: 'Higher risk estimate'
},
{
	id: 7,
	name: 'Estimate - Not selected'
}
];

export const materialityTypes = [{
	value: 1,
	label: '세전이익'
},
{
	value: 2,
	label: 'Earnings before interest and taxes (EBIT)'
},
{
	value: 3,
	label: 'Earnings before interest, taxes, depreciation, and amortization (EBITDA)',
},
{
	value: 4,
	label: '매출총이익'
},
{
	value: 5,
	label: '수익'
},
{
	value: 6,
	label: '영업비용'
},
{
	value: 7,
	label: '자본'
},
{
	value: 8,
	label: '자산'
},
{
	value: 9,
	label: '활동 기준 측정 (기타)'
},
{
	value: 10,
	label: '세전 손실'
},
{
	value: 11,
	label: '자본 기준 측정 (기타)'
},
{
	value: 12,
	label: '이익 기준 측정 (기타)'
}
];

export const helixCurrencyType = {
	[currencyType.Functional]: '기능',
	[currencyType.Reporting]: '보고'
};

export const controlRiskType = [{
	id: 1,
	name: 'Rely'
},
{
	id: 2,
	name: 'Not Rely'
},
{
	id: 3,
	name: 'MP 테스트'
}
];

export const inherentRiskType = [{
	id: 1,
	name: 'Higher'
},
{
	id: 2,
	name: 'Lower'
},
{
	id: 3,
	name: 'Very Low'
}
];

export const AlraInherentRiskType = [{
	id: 3,
	name: '관련되지 않음'
},
{
	id: 2,
	name: 'Lower'
},
{
	id: 1,
	name: 'Higher'
}
];

export const scotInherentRiskType = [{
	id: 1,
	name: 'Higher'
},
{
	id: 2,
	name: 'Lower'
},
{
	id: 3,
	name: '비일상적 SCOT'
}
];

export const CRAStrings = {
	Minimal: 'Minimal',
	Low: 'Low',
	'Low +SC': "Low+SC",
	Moderate: 'Moderate',
	High: 'High',
	'High +SC': "High + SC"
};

export const priorityType = [{
	id: 1,
	name: 'Low',
	className: 'Low',
	label: 'L'
},
{
	id: 2,
	name: 'Medium',
	className: 'Medium',
	label: 'M'
},
{
	id: 3,
	name: 'High',
	className: 'High',
	label: 'H'
},
{
	id: 4,
	name: 'Critical',
	className: 'Critical',
	label: 'C'
}
];

export const kendoLabels = {
	addComment: 'Comment 추가',
	addColumnBefore: '왼쪽에 열 추가',
	addColumnAfter: '오른쪽에 열 추가',
	addInlineComment: 'Inline comment 추가',
	addRowAbove: '위쪽 행 추가',
	addRowBelow: '아래쪽 행 추가',
	alignLeft: '왼쪽 맞춤',
	alignRight: '오른쪽 맞춤',
	alignCenter: '가운데 맞춤',
	alignFull: '전체 맞춤',
	backgroundColor: '배경 색상',
	bulletList: '비순서 리스트 삽입',
	bold: '굵게',
	backColor: '하이라이트',
	createLink: '하이퍼링크 삽입',
	createTable: '표 생성',
	cleanFormatting: '서식 정리',
	deleteRow: '행 삭제',
	deleteColumn: '열 삭제',
	deleteTable: '표 삭제',
	fontSizeInherit: '글꼴 크기',
	foreColor: '글자 색상',
	format: '형식',
	fontSize: '글꼴 크기',
	hyperlink: '링크 삽입',
	italic: '기울임꼴',
	indent: '들여쓰기',
	insertTableHint: '{0} x {1} 표 생성',
	huge: 'Huge',
	'hyperlink-dialog-content-address': "웹 주소",
	'hyperlink-dialog-title': "하이퍼링크 삽입",
	'hyperlink-dialog-content-title': "제목",
	'hyperlink-dialog-content-newwindow': "새 창에서 링크 열기",
	'hyperlink-dialog-cancel': "취소",
	'hyperlink-dialog-insert': "삽입",
	large: 'Large',
	noDataPlaceholder: '텍스트 입력',
	normal: 'Normal',
	orderedList: '순서 리스트 삽입',
	outdent: '내어쓰기',
	paragraphSize: '문단 크기',
	print: '인쇄',
	pdf: 'pdf로 내보내기',
	redo: '재실행',
	removeFormatting: '서식 제거',
	strikethrough: '취소선',
	small: 'Small',
	subscript: '위첨자',
	superscript: '아래첨자',
	underline: '밑줄',
	undo: '실행 취소',
	unlink: '링크 해제'
};

export const kendoFormatOptions = [{
	text: '문단',
	value: 'p'
},
{
	text: '제목 1',
	value: 'h1'
},
{
	text: '제목 2',
	value: 'h2'
},
{
	text: '제목 3',
	value: 'h3'
},
{
	text: '제목 4',
	value: 'h4'
},
{
	text: '제목 5',
	value: 'h5'
},
{
	text: '제목 6',
	value: 'h6'
}
];

export const kendoFontSize = [{
	text: '8',
	value: '8px'
},
{
	text: '9',
	value: '9px'
},
{
	text: '10',
	value: '10px'
},
{
	text: '11',
	value: '11px'
},
{
	text: '12',
	value: '12px'
},
{
	text: '14',
	value: '14px'
},
{
	text: '16',
	value: '16px'
},
{
	text: '18',
	value: '18px'
},
{
	text: '20',
	value: '20px'
},
{
	text: '22',
	value: '22px'
},
{
	text: '24',
	value: '24px'
},
{
	text: '26',
	value: '26px'
},
{
	text: '28',
	value: '28px'
},
{
	text: '36',
	value: '36px'
},
{
	text: '48',
	value: '48px'
},
{
	text: '72',
	value: '72px'
}
];

export const ItFlowValidationLabels = {
	ITAppWithoutAtLeastOneRelatedITProcess: '연결되지 않은 IT application',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: '연결되지 않은 ITGC',
	ITSPHasNorelatedITRisk: '연결되지 않은 ITSP',
	ITProcessHasNoRelatedITApplication: '연결되지 않은 IT 프로세스',
	ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication: 'IT 위험 누락',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: 'ITGC 또는 미존재 표시 누락',
	ITDMorITACWithNoRelatedITApplication: 'IT app이 없는 App/ITDM 통제',
	ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'ITSP 삭제 예정',
	ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'ITGC 삭제 예정',
	ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'IT 위험 삭제 예정',
	ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed: '유효하지 않은 테스트 전략이 있는 ITGC',
	ITGCWithoutASelectedDesignEffectiveness: 'ITGC 설계 평가 누락',
	SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero: '연결되지 않은 SCOT',
	AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed: '모순된 통제테스트 응답',
	SCOTWithHasNoITApplicationHasITDMOrAppControls: 'SCOT 내 IT application 누락'
};

export const ISA315ITFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: ItFlowValidationLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: ItFlowValidationLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: ItFlowValidationLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: ItFlowValidationLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: ItFlowValidationLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed,
	label: ItFlowValidationLabels.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: ItFlowValidationLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
}
];

/* Notes modal labels */

export const reviewNoteModalLabels = {
	/*Review Notes*/
	engagement: 'Engagement',
	emptyReplyErrorMsg: '계속하려면 텍스트를 추가하십시오',
	lengthReplyErrorMsg: '회신은 4,000자를 초과할 수 없습니다',
	documentLabel: '문서',
	task: 'Task',
	allEngagementFilterLabel: '다른 모든 engagement',
	otherEngagementComments: '다른 engagement 노트',
	notesModalInstructionalText: '아래에서 선택된 {0}에 대한 노트를 보고 응답하십시오',
	commentThread: '노트 thread',
	singleNoteInstructionalText: '아래에서 선택된 노트를 보고 응답하십시오',
	emptyNoteDetailsMessage: '세부정보를 보려면 노트를 선택하십시오. 한꺼번에 제어하려면, control 키나 shift 키를 이용하여 여러 리뷰노트를 선택하십시오. 개별 노트에서 작업하려면 목록에서 해당 노트를 선택하십시오.',
	documentReviewNotesLabel: '문서 노트',
	addNewReviewNoteButtonText: '노트 추가',
	noNotesAssociatedWithDocumentLabel: '아래에 입력하여 노트를 남기십시오. 노트를 사용자에게 배정하고 우선순위와 만료일을 지정하십시오.',
	noNotesFound: '노트를 찾을 수 없습니다',
	noNotesAssociatedWithTaskLabel: 'Task에 연결된 {0} 노트가 없습니다',
	allNotesLabel: '모든 노트',
	charactersLabel: 'characters',
	myNotesLabel: '내 노트',
	showClearedLabel: '처리된 항목 표시',
	showClosedLabel: '닫힌 항목 표시',
	assignedToLabel: '담당자',

	ofLabel: 'of',
	enterNoteText: '노트 입력',
	addNewNoteModalClose: '닫기',
	addNewNoteModalTitleLabel: '신규 노트 추가',
	editNoteModalTitleLabel: '노트 편집',
	deleteIconHoverText: '삭제',
	deleteIconModalAcceptText: '삭제',
	deleteIconModalConfirmMessage: '이 노트에 대한 회신을 삭제하시겠습니까?',
	deleteIconModalConfirmMessageParent: '선택한 노트를 삭제하시겠습니까?',
	deleteIconModalTitleLabel: '노트 삭제',
	deleteReplyIconModalTitle: '회신 삭제',
	emptyRepliesMessage: '아직 회신이 없습니다',
	replyInputPlaceholder: '이 노트에 회신',
	replyInputPlaceholderEdit: '노트 또는 음성 노트로 회신 편집',
	noteInputPlaceholderEdit: '노트 또는 음성 노트로 편집',
	replyText: '회신 텍스트',
	editReplyModelTitle: '회신 편집',
	editReplyPlaceholder: '회신 입력',
	noteDueDateLabel: '만료',

	priorityLabel: '우선순위',
	dueDateLabel: '만료일',
	dueLabel: '만료',
	status: '상태',
	noteModifiedDateLabel: '수정됨',
	cancelLabel: '취소',
	saveLabel: '저장',
	clearedBy: '처리한 사람',
	closedBy: '닫은 사람',
	reopenedBy: '다시 연 사람',
	reply: '회신',
	editIconHoverTextLabel: '편집',
	required: '필수',
	closeTitle: '닫기',
	otherEngagementNotes: '다른 engagement 노트',
	closeLabel: '닫기',
	showMore: '더 표시',
	showLess: '덜 표시',
	showMoreEllipsis: '더 표시…',
	showLessEllipsis: '덜 표시…',
	noResultFound: '결과를 찾을 수 없습니다',
	engagementNameLabel: 'Engagement 이름: ',
	taskReviewNotesLabel: 'Task 노트',
	fromUserLabel: 'From',
	toUserLabel: 'to',
	view: '보기',
	dueDateRequiredTextError: '만료일은 필수입니다'
};

export const notesFilterLabels = [{
	id: notesFilter.allNotes,
	label: '모든 노트',
	value: notesFilter.allNotes
},
{
	id: notesFilter.myNotes,
	label: '내 노트',
	value: notesFilter.myNotes
},
{
	id: notesFilter.authoredByMeNotes,
	label: '내 담당',
	value: notesFilter.authoredByMeNotes
}
];

export const reviewerAssignments = {
	taskLayoutHeaderAssignments: '배정',
	manageAssigmentsStep2: 'Task 배정 편집',
	editAssignment: '배정 편집',
	deleteAssignment: '배정 삭제',
	manageAssigmentsStep3: '배정 완료',
	taskAssigmentStatusHeader: '배정 상태',
	taskAssignmentName: '배정',
	dueDateAssigment: '만료',
	editDueDate: '선택사항: 종료일까지 남은 날짜 편집',
	teamMemberAssigmentLabel: '팀 멤버',
	currentAssigmentLabel: '현재',
	handOffToAssigmentLabel: 'Hand-off 대상: ',
	priorToEndDateLabel: 'prior to end date',
	noTimePhaseAssigmentLabel: '배정된 시기 없음',
	closedByAssigmentLabel: '닫은 사람',
	onAssingmentLabel: 'on',
	preparerAssigmentOpenTitleTip: 'Task 배정을 닫으려면 이 task를 hand-off하십시오',
	reviewerAssigmentOpenTitleTip: 'Task 배정 닫힘 표시',
	reviewerAssigmentClosedTitleTip: 'Task 배정 처리 전 표시',
	AssigmentLabel: 'Task 배정을 닫으려면 이 task를 hand-off하십시오',
	timePhaseName: '시기: ',
	timePhaseEndDate: '종료일: ',
	AssignmentType: [{
		id: 1,
		displayName: '작성자'
	},
	{
		id: 2,
		displayName: '세부 검토자'
	},
	{
		id: 3,
		displayName: '일반 검토자'
	},
	{
		id: 4,
		displayName: 'Partner'
	},
	{
		id: 5,
		displayName: 'EQR'
	},
	{
		id: 6,
		displayName: 'Other'
	}
	],
	AssignmentStatus: [{
		id: 1,
		displayName: '열기'
	},
	{
		id: 2,
		displayName: '진행 중'
	},
	{
		id: 3,
		displayName: '닫힘'
	},
	{
		id: 4,
		displayName: '배정되지 않음'
	}
	],
	assignmentTableColumnHeader: '배정',
	teamMemberTableColumnHeader: '팀 멤버',
	dueDaysTableColumnHeader: '만료',
	daysPriorToEndDate: '종료일까지 남은 날짜',
	handoffButton: 'Hand-off'
};
/* Notes modal labels */

export const handOffModal = {
	title: 'Hand-off',
	description: '이 task를 다음 팀 멤버에게 hand-off합니다. 증거 파일에 sign-off하려면 아래 옵션에서 선택하십시오.',
	dropdownLabel: 'Hand-off 대상',
	closeTitle: '취소',
	confirmButton: 'Hand-off',
	evidence: '증거',
	evidenceSignOffTitle: '모두 sign-off: ',
	existingSignOffs: '기존 sign-off',
	noDocumentsAvailable: '사용 가능한 문서 없음'
};

// manage scot modal labels
export const manageSCOTModal = {
	title: 'SCOT 관리',
	description: '기존 SCOT을 생성, 편집, 또는 삭제할 수 있습니다. 저장되면 변경사항이 적용됩니다.',
	addSCOTLink: 'SCOT 추가'
};

export const deleteSCOTModal = {
	title: 'SCOT 삭제',
	description: '다음 SCOT이 삭제됩니다. 이 작업은 실행 취소할 수 없습니다.'
};

export const manageITAppModalLabels = {
	title: 'IT application 관리',
	description: '신규 IT application을 생성하거나, 아래 기존 IT application을 편집하거나 삭제하십시오',
	inputNameTitle: 'IT application 이름',
	deleteConfirmMessage: 'IT application <b>{0}</b>을(를) 삭제하시겠습니까? 이는 실행 취소할 수 없습니다.',
	addSuccessMessage: "IT application'{0}'이(가) 생성됐습니다 ",
	editSuccessMessage: "IT application'{0}'에 대한 편집이 저장됐습니다 ",
	deleteSuccessMessage: "'{0}'이(가) 삭제됐습니다  "
};

export const manageSOModalLabels = {
	title: '서비스조직 관리',
	description: '신규 서비스조직을 생성하거나, 아래 기존 서비스조직을 편집하거나 삭제하십시오',
	inputNameTitle: '서비스조직 이름',
	deleteConfirmMessage: '서비스조직 <b>{0}</b>을(를) 삭제하시겠습니까? 이는 실행 취소할 수 없습니다.',
	addSuccessMessage: "서비스조직'{0}'이(가) 생성됐습니다",
	editSuccessMessage: "서비스조직'{0}'에 대한 편집이 저장됐습니다 ",
	deleteSuccessMessage: "'{0}'이(가) 삭제됐습니다",
	addServiceOrganization: '서비스조직 추가',
	editServiceOrganization: '서비스조직 편집',
	deleteServiceOrganization: '서비스조직 삭제'
};

export const customNameModal = {
	title: '분개 원천 이름 suffix',
	closeTitle: '취소',
	save: '저장',
	suffixRequired: 'Suffix는 필수입니다!',
	suffix: 'Suffix',
	addSuffix: 'Suffix 추가',
	editSuffix: 'Suffix 편집'
};

export const GuidedWorkFlowLabels = {
	RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM: '연결되지 않은 사건 및 상황/왜곡표시위험',
	RisksUnrelatedToAnAssertionForGuidedWorkflow: '연결되지 않은 위험',
	IncompleteMeasurementBasisForecastAmount: '미완료 기준',
	IncompleteMeasurementBasisForecastAmountRationale: '기준 근거 필요',
	IncompleteMeasurementBasisAdjustedAmount: '미완료 조정 금액',
	IncompletePlanningMateriality: '미완료 PM',
	PlanningMaterialityGreaterThanMaximumAmount: 'PM이 너무 큼',
	IncompletePlanningMaterialityRationale: 'PM 근거 필요',
	IncompleteTolerableError: '미완료 TE',
	TENotWithinRangeOfAllowedValues: '유효하지 않은 TE 비율',
	IncompleteTolerableErrorRationale: 'TE 근거 필요',
	IncompleteSAD: '미완료 SAD',
	SADGreaterThanMaximum: 'SAD가 너무 큼',
	IncompleteSADRationale: 'SAD 근거 필요',
	IncompletePACESelection: '미완료 PACE',
	AccountWithoutIndividualRiskAssessmentForm: '계정{0} 문서 누락',
	EstimateWithoutIndividualEstimateForm: '추정치{0} 문서 누락',
	AccountWithoutIndividualAnalyticForm: '계정{0} 문서 누락',
	MultiEntityWithoutIndividualProfileForm: 'Multi-entity 개별 프로필 문서가 없는 기업',
	AccountAccountTypeIDDoesNotMatchAction: '모순되는 계정 지정 선택',
	AccountHasEstimateDoesNotMatchAction: '모순되는 계정 추정치 선택',
	AccountFormOptionHasRelatedRisksNotAssociatedToAccount: '관련 계정이 연결되지 않은 위험',
	AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk: '위험 없이 고유위험이 높은 경영진주장',
	AccountMissingSubstantiveProcedure: '실증절차가 없는 계정/기업',
	MultiEntityNotRelatedToALLPSTACTForRelatedAccount: '계정/기업에 내용 업데이트가 필요합니다',
	ComponentWithoutGroupInvolvementForm: '그룹 관여 양식이 없는 부문 (참조 전용 부문 제외)',
	ComponentWithoutRelatedGroupAssessmentInstruction: '그룹 위험평가 지침이 없는 부문',
	IncompleteAssertionRiskLevel: '미완료 경영진주장 위험 수준',
	EstimateAccountWithoutEsimatePSPIndex: '추정치 PSP index가 없는 추정치 계정',
	AccountExecutedWithoutRelatedComponent: '그룹 - 연결된 Full 또는 Specific 범위 부문이 없는 계정 (다른 engagement에서 실행됨)',
	MultiEntityAccountWithoutRelatedToAnyMultiEntity: '연결된 기업이 없는 계정',
	ChangeNotSubmittedMultiEntityFullProfile: '변경사항 미제출',
	ChangeNotSubmittedMultiEntityIndividualDocument: '변경사항 미제출',
	AccountTypeWithMissingInformation: '계정 정보 누락',
	DocumentUploadMissingRequiredPICEQRSignOffs: 'Sign-off가 없는 증거',
	DocumentUploadMissingRequiredPICEQRSignOffRequirements: 'Sign-off 요건이 없는 증거',
	DocumentUploadMissingPreparerOrReviewerSignOffs: '문서 업로드 - 작성자 또는 검토자 Sign-off 누락',
	ITAppWithoutAtLeastOneRelatedITProcess: '연결되지 않은 IT application',
	ITProcessHasNoRelatedITApplication: '연결되지 않은 IT 프로세스',
	ITGCWithoutASelectedDesignEffectiveness: 'ITGC 설계 평가 누락',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: '연결되지 않은 ITGC',
	ITSPHasNorelatedITRisk: '연결되지 않은 ITSP',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: 'ITGC 또는 미존재 표시 누락',
	EstimateWithoutAccountRelated: '계정이 연결되지 않은 추정치',
	EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount: '비추정치 계정에 연결된 높은/낮은 위험 추정치',
	RiskEstimateRiskTypeIDDoesNotMatchAction: '‘추정치 편집’에서 지정한 사항과 일치하지 않는 추정치 범주 응답',
	LowerorHigherRiskEstimateWithoutEstimateSCOT: '유효한 SCOT이 없는 추정치',
	EstimateWithoutIndividualEstimateDocument: '개별 문서가 누락된 추정치',
	EstimateAccountWithoutHigherOrLowerRiskEstimate: '유효한 추정치가 없는 계정',
	EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary: '높은 또는 낮은 위험 추정치가 없는 추정치 계정',
	EstimateScotWithoutHigherOrLowerRiskEstimate: '높은 또는 낮은 위험 추정치가 없는 추정 SCOT',
	HigherRiskEstimateWithoutRisk: '유효한 위험이 연결되지 않은 높은 위험 추정치',
	PICEQRSignOffRequirements: 'PIC or EQR Signoff requirement does not match response',
	AdjustmentsWithoutAnyEvidence: '증거가 없는 조정사항',
	AdjustmentsThatDoNotNet: '상계되지 않는 조정사항',
	DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration: '공동작업을 위해 체크아웃됐거나 체크아웃 진행 중인 문서',
	NonEngagementWideTasksMissingEvidence: '증거가 누락된 비 engagement 차원 task',
	EstimatesMustBeMarkedHigherRisk: '높은 위험이 아닌 추정치에 연결된 유의적인/부정 위험',
	SCOTEstimateNoRelatedWalkthroughForm: 'Walkthrough가 없는 SCOT/추정치',
	SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2: '계정이 연결되지 않은 SCOT',
	AccountSignificantDisclosureWithNoRelatedSCOTV2: 'Account - Significant Account / Significant Disclosure that is not a CT only Account with no related SCOT or an Estimate when it is an Estimate Account',
	ITApplicationWithoutITAppRiskAssessmentIndividualDocument: 'Technology risk assessment missing document',
	ITApplicationWithoutITAppPlanningIndividualDocument: 'Technology missing document',
	FormContentWithoutHeader: 'Form Content without Header',
	RisksWithoutAnyRelatedAssertions: 'There are risks that have not been related to at least one assertion',
	AssertionsWithIncompleteCRA: 'There are assertions missing an inherent and/or control risk assessment',
	LimitedRiskOrInsignificantAccountMissingRationale: 'All limited risk and insignificant accounts shall have rationale provided',
	ITProcessWithoutWalkthroughDocument: 'ITProcess without IT process - Walkthrough - Individual',
	ITProcessIsUncategorized: 'IT Process - ITProcessTypeID is Uncategorized',
	ITProcessWithNoRelatedITApplication: 'ITProcess - ITProcess with no related IT Application'
};

export const GuidedWorkFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.RiskEstimateRiskTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.RiskEstimateRiskTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.FormContentWithoutHeader,
	label: GuidedWorkFlowLabels.FormContentWithoutHeader
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary
},
{
	validationId: validationTypes.EstimateScotWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateScotWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.HigherRiskEstimateWithoutRisk,
	label: GuidedWorkFlowLabels.HigherRiskEstimateWithoutRisk
},
{
	validationId: validationTypes.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM,
	label: GuidedWorkFlowLabels.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.RisksUnrelatedToAnAssertionForGuidedWorkflow,
	label: GuidedWorkFlowLabels.RisksUnrelatedToAnAssertionForGuidedWorkflow
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmount
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmountRationale,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmountRationale
},
{
	validationId: validationTypes.IncompleteMeasurementBasisAdjustedAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisAdjustedAmount
},
{
	validationId: validationTypes.IncompletePlanningMateriality,
	label: GuidedWorkFlowLabels.IncompletePlanningMateriality
},
{
	validationId: validationTypes.PlanningMaterialityGreaterThanMaximumAmount,
	label: GuidedWorkFlowLabels.PlanningMaterialityGreaterThanMaximumAmount
},
{
	validationId: validationTypes.IncompletePlanningMaterialityRationale,
	label: GuidedWorkFlowLabels.IncompletePlanningMaterialityRationale
},
{
	validationId: validationTypes.IncompleteTolerableError,
	label: GuidedWorkFlowLabels.IncompleteTolerableError
},
{
	validationId: validationTypes.TENotWithinRangeOfAllowedValues,
	label: GuidedWorkFlowLabels.TENotWithinRangeOfAllowedValues
},
{
	validationId: validationTypes.IncompleteTolerableErrorRationale,
	label: GuidedWorkFlowLabels.IncompleteTolerableErrorRationale
},
{
	validationId: validationTypes.IncompleteSAD,
	label: GuidedWorkFlowLabels.IncompleteSAD
},
{
	validationId: validationTypes.SADGreaterThanMaximum,
	label: GuidedWorkFlowLabels.SADGreaterThanMaximum
},
{
	validationId: validationTypes.IncompleteSADRationale,
	label: GuidedWorkFlowLabels.IncompleteSADRationale
},
{
	validationId: validationTypes.IncompletePACESelection,
	label: GuidedWorkFlowLabels.IncompletePACESelection
},
{
	validationId: validationTypes.AccountWithoutIndividualRiskAssessmentForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualRiskAssessmentForm
},
{
	validationId: validationTypes.EstimateWithoutIndividualEstimateForm,
	label: GuidedWorkFlowLabels.EstimateWithoutIndividualEstimateForm
},
{
	validationId: validationTypes.AccountWithoutIndividualAnalyticForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualAnalyticForm
},
{
	validationId: validationTypes.MultiEntityWithoutIndividualProfileForm,
	label: GuidedWorkFlowLabels.MultiEntityWithoutIndividualProfileForm
},
{
	validationId: validationTypes.AccountAccountTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountAccountTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.AccountHasEstimateDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountHasEstimateDoesNotMatchAction
},
{
	validationId: validationTypes.AccountFormOptionHasRelatedRisksNotAssociatedToAccount,
	label: GuidedWorkFlowLabels.AccountFormOptionHasRelatedRisksNotAssociatedToAccount
},
{
	validationId: validationTypes.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk,
	label: GuidedWorkFlowLabels.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk
},
{
	validationId: validationTypes.MultiEntityNotRelatedToALLPSTACTForRelatedAccount,
	label: GuidedWorkFlowLabels.MultiEntityNotRelatedToALLPSTACTForRelatedAccount
},
{
	validationId: validationTypes.AccountMissingSubstantiveProcedure,
	label: GuidedWorkFlowLabels.AccountMissingSubstantiveProcedure
},
{
	validationId: validationTypes.ComponentWithoutGroupInvolvementForm,
	label: GuidedWorkFlowLabels.ComponentWithoutGroupInvolvementForm
},
{
	validationId: validationTypes.ComponentWithoutRelatedGroupAssessmentInstruction,
	label: GuidedWorkFlowLabels.ComponentWithoutRelatedGroupAssessmentInstruction
},
{
	validationId: validationTypes.EstimateAccountWithoutEstimatePSPIndex,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutEsimatePSPIndex
},
{
	validationId: validationTypes.AssertionInherentRiskWithoutRelatedHigherRisk,
	label: GuidedWorkFlowLabels.IncompleteAssertionRiskLevel
},
{
	validationId: validationTypes.AccountGroupWithoutAComponent,
	label: GuidedWorkFlowLabels.AccountExecutedWithoutRelatedComponent
},
{
	validationId: validationTypes.MultiEntityAccountWithoutRelatedToAnyMultiEntity,
	label: GuidedWorkFlowLabels.MultiEntityAccountWithoutRelatedToAnyMultiEntity
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityFullProfile,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityFullProfile
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityIndividualDocument,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityIndividualDocument
},
{
	validationId: validationTypes.AccountWithMissingValues,
	label: GuidedWorkFlowLabels.AccountTypeWithMissingInformation
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffs
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffRequirements
},
{
	validationId: validationTypes.DocumentUploadMissingPreparerOrReviewerSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingPreparerOrReviewerSignOffs
},
{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: GuidedWorkFlowLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: GuidedWorkFlowLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: GuidedWorkFlowLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: GuidedWorkFlowLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: GuidedWorkFlowLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
},
{
	validationId: validationTypes.EstimateWithoutAccountRelated,
	label: GuidedWorkFlowLabels.EstimateWithoutAccountRelated
},
{
	validationId: validationTypes.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount,
	label: GuidedWorkFlowLabels.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount
},
{
	validationId: validationTypes.LowerorHigherRiskEstimateWithoutEstimateSCOT,
	label: GuidedWorkFlowLabels.LowerorHigherRiskEstimateWithoutEstimateSCOT
},
{
	validationId: validationTypes.PICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.PICEQRSignOffRequirements
},
{
	validationId: validationTypes.EstimatesMustBeMarkedHigherRisk,
	label: GuidedWorkFlowLabels.EstimatesMustBeMarkedHigherRisk
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.NonEngagementWideTasksMissingEvidence,
	label: GuidedWorkFlowLabels.NonEngagementWideTasksMissingEvidence
},
{
	validationId: validationTypes.AdjustmentsWithoutAnyEvidence,
	label: GuidedWorkFlowLabels.AdjustmentsWithoutAnyEvidence
},
{
	validationId: validationTypes.AdjustmentsThatDoNotNet,
	label: GuidedWorkFlowLabels.AdjustmentsThatDoNotNet
},
{
	validationId: validationTypes.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration,
	label: GuidedWorkFlowLabels.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration
},
{
	validationId: validationTypes.ITApplicationWithoutITAppRiskAssessmentIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppRiskAssessmentIndividualDocument
},
{
	validationId: validationTypes.SCOTEstimateNoRelatedWalkthroughForm,
	label: GuidedWorkFlowLabels.SCOTEstimateNoRelatedWalkthroughForm
},
{
	validationId: validationTypes.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2,
	label: GuidedWorkFlowLabels.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2
},
{
	validationId: validationTypes.AccountSignificantDisclosureWithNoRelatedSCOTV2,
	label: GuidedWorkFlowLabels.AccountSignificantDisclosureWithNoRelatedSCOTV2
},
{
	validationId: validationTypes.ITApplicationWithoutITAppPlanningIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppPlanningIndividualDocument
},
{
	validationId: validationTypes.RisksWithoutAnyRelatedAssertions,
	label: GuidedWorkFlowLabels.RisksWithoutAnyRelatedAssertions
},
{
	validationId: validationTypes.AssertionsWithIncompleteCRA,
	label: GuidedWorkFlowLabels.AssertionsWithIncompleteCRA
},
{
	validationId: validationTypes.LimitedRiskOrInsignificantAccountMissingRationale,
	label: GuidedWorkFlowLabels.LimitedRiskOrInsignificantAccountMissingRationale
},
{
	validationId: validationTypes.ITProcessWithoutWalkthroughDocument,
	label: GuidedWorkFlowLabels.ITProcessWithoutWalkthroughDocument
},
{
	validationId: validationTypes.ITProcessIsUncategorized,
	label: GuidedWorkFlowLabels.ITProcessIsUncategorized
},
{
	validationId: validationTypes.ITProcessWithNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessWithNoRelatedITApplication
}

];

// Label overrides (redefine here labels / objects that apply for a different part of the application)
export const resourceOverrides = {
	['Ares']: {
		labels: {
			notAROMM: '중요왜곡표시위험 아님',
			fraudRisk: '부정 위험',
			significantRisk: '유의적인 위험',
			identifiedRiskFactors: '식별된 사건/상황, 중요왜곡표시위험, 유의적인 위험, 부정위험',
			countUnassociatedRisk: "사건/상황이'중요왜곡표시위험 아님'으로 연결/표시되지 않았습니다"
		},
		riskTypes: [{
			id: 1,
			name: '유의적인 위험',
			abbrev: 'S',
			label: '유의적',
			title: '유의적인 위험'
		},
		{
			id: 2,
			name: '부정 위험',
			abbrev: 'F',
			label: '부정',
			title: '부정 위험'
		},
		{
			id: 3,
			name: '중요왜곡표시위험',
			abbrev: 'R',
			label: '중요왜곡표시위험',
			title: '중요왜곡표시위험'
		}
		]
	}
};

export const jeSourceTypes = [{
	value: 1,
	label: '시스템 생성'
},
{
	value: 2,
	label: '수동'
},
{
	value: 3,
	label: '둘 다'
}
];

export const hasJournalEntriesOption = [{
	value: 1,
	label: '예'
},
{
	value: 2,
	label: '아니오'
}
];

export const filterReviewNoteStatus = [{
	value: 1,
	label: '처리 전'
},
{
	value: 2,
	label: '처리됨'
},
{
	value: 3,
	label: '닫힘'
}
];

export const EntitiesLabels = {
	close: '닫기',
	cancel: '취소',
	repNoRecordMessage: '결과를 찾을 수 없습니다',
	edit: '편집',
	delete: '삭제',
	actions: '작업',
	show: '표시',
	first: '처음',
	last: '마지막',
	prev: '이전 페이지',
	next: '다음 페이지',
	search: '검색',
	primary: 'Primary',
	knowledgeRiskLabel: 'Risks from knowledge cannot be edited or deleted',


	[Entity.Account]: {
		manageEntity: '계정 및 공시 관리',
		searchEntity: '계정 검색',
		createEntity: '신규 계정',
		entityName: '계정',
		entityNameCaps: '계정',
		entityNamePlural: '계정',
		placeholderText: '신규 계정 및 공시를 생성하거나, 아니면 아래 기존 계정 및 공시를 편집하거나 삭제하십시오',
		deleteConfirmLabel: '이 계정을 삭제하시겠습니까? 기존 연결은 모두 제거됩니다. 이 작업은 실행 취소할 수 없습니다.'
	},
	[Entity.Estimate]: {
		manageEntity: '추정치 관리',
		searchEntity: '추정치 검색',
		createEntity: '신규 추정치',
		entityName: '추정치',
		entityNameCaps: '추정치',
		entityNamePlural: '추정치',
		placeholderText: '신규 추정치를 생성하거나 아래 기존 추정치를 편집하거나 삭제하십시오',
		deleteConfirmLabel: '이 추정치를 삭제하시겠습니까? 기존 연결은 모두 제거됩니다. 이 작업은 실행 취소할 수 없습니다.'
	},
	[Entity.Risk]: {
		manageEntity: '위험 관리',
		searchEntity: '위험 검색',
		createEntity: '신규 위험',
		entityName: '위험',
		entityNameCaps: '위험',
		entityNamePlural: '위험',
		placeholderText: '신규 위험을 생성하거나 아래 기존 위험을 편집하거나 삭제하십시오',
		deleteConfirmLabel: '이 위험을 삭제하시겠습니까? 기존 연결은 모두 제거됩니다. 이 작업은 실행 취소할 수 없습니다.'
	},
	[Entity.STEntity]: {
		manageEntity: '기업 관리',
		searchEntity: '기업 검색',
		createEntity: '신규 기업',
		entityName: '기업',
		entityNameCaps: '기업',
		entityNamePlural: '기업',
		placeholderText: '신규 기업을 생성하거나 아래 기존 기업을 편집하거나 삭제하십시오',
		deleteEntity: '기업 삭제',
		deleteConfirmLabel: '이 기업을 삭제하시겠습니까? 기존 연결은 모두 제거됩니다. 이 작업은 실행 취소할 수 없습니다.'
	},
	[Entity.Control]: {
		manageEntity: '통제 관리',
		searchEntity: '통제 검색',
		createEntity: '신규 통제',
		entityName: '통제',
		entityNameCaps: '통제',
		entityNamePlural: '통제',
		placeholderText: '신규 통제를 생성하거나, 아래 기존 통제를 편집하거나 삭제하십시오',
		deleteEntity: '통제 삭제',
		deleteConfirmLabel: '이 통제를 삭제하시겠습니까? 기존 연결은 모두 제거됩니다. 이 작업은 실행 취소할 수 없습니다.'
	},
	[Entity.ITProcess]: {
		manageEntity: 'IT 프로세스 관리',
		searchEntity: 'IT 프로세스 검색',
		createEntity: '신규 IT 프로세스',
		entityName: 'IT 프로세스',
		entityNameCaps: 'IT 프로세스',
		entityNamePlural: 'IT 프로세스',
		placeholderText: "신규 IT 프로세스를 생성하거나, 아래 기존 IT 프로세스를 편집하거나 삭제하십시오",
		deleteEntity: 'IT 프로세스 삭제',
		deleteConfirmLabel: '이 IT 프로세스를 삭제하시겠습니까? 기존 연결은 모두 제거됩니다. 이 작업은 실행 취소할 수 없습니다.'
	},
	[Entity.ITRisk]: {
		manageEntity: '기술 위험 관리',
		searchEntity: '기술 위험 검색',
		createEntity: '신규 기술 위험',
		entityName: '기술 위험',
		entityNameCaps: '기술 위험',
		entityNamePlural: '기술 위험',
		placeholderText: '신규 기술 위험을 생성하거나, 아래에서 기존 기술 위험을 편집하거나 삭제하십시오',
		deleteEntity: '기술 위험 삭제',
		deleteConfirmLabel: '이 기술 위험을 삭제하시겠습니까? 기존 연결이 모두 제거됩니다. 이 작업은 실행 취소할 수 없습니다.'
	},
	[Entity.ITControl]: {
		ITGC: {
			manageEntity: 'ITGC 관리',
			searchEntity: 'ITGC 검색',
			createEntity: '신규 ITGC',
			editEntity: 'ITGC 편집',
			viewEntity: 'ITGC 보기',
			entityName: 'ITGC',
			entityNameCaps: 'ITGC',
			entityNamePlural: 'ITGC',
			placeholderText: '신규 ITGC를 생성하거나, 아니면 아래 기존 ITGC를 편집하거나 삭제하십시오',
			deleteEntity: 'ITGC 삭제 ',
			close: '닫기',
			cancel: '취소',
			processIdRequired: 'IT 프로세스는 필수입니다',
			save: '저장',
			confirm: '확인',
			iTProcesslabel: 'IT 프로세스',
			saveAndCloseLabel: '저장 및 닫기',
			saveAndCreateLabel: '저장 및 추가 생성',
			deleteConfirmLabel: '이 ITGC를 삭제하시겠습니까? 기존 연결은 모두 제거됩니다. 이 작업은 실행 취소할 수 없습니다.',
			operationEffectiveness: '운영 효과성',
			itDesignEffectivenessHeader: '설계 효과성',
			itTestingColumnHeader: '테스트 ',
			testingTitle: '테스트 ',
			frequency: '빈도',
			controlOpertaingEffectiveness: '운영 효과성',
			designEffectiveness: '설계 효과성',
			frequencyITGC: '빈도 선택',
			nameITGC: 'ITGC 이름 (필수)',
			itspNameRequired: 'ITSP 이름 (필수)',
			noResultsFound: '결과를 찾을 수 없습니다',
			selectITRisk: '기술 위험 선택 (필수)',
			itRiskRequired: '기술 위험 (필수)',
			itRiskName: '기술 위험',
			inputInvaildCharacters: '입력에 다음 문자열을 포함할 수 없습니다: */:<>\\?|”',
			itControlNameRequired: 'ITGC 이름은 필수입니다',
			itgcTaskDescription: 'ITGC 의존 대상 기간에 걸쳐 그 운영 효과성에 대해 충분하고 적합한 감사증거를 입수하기 위해 설계된 ITGC 테스트를 수행합니다',
			selectITProcess: 'IT 프로세스 선택 (필수)',
			itProcessRequired: 'IT 프로세스 (필수)',
			riskNameRequired: '기술 위험은 필수입니다',
			addModalDescription: 'ITGC 설명 입력',
			editModalDescription: 'ITGC 및 관련 정보 편집',
			controlDesignEffectiveness: {
				[0]: {
					description: '미선택'
				},
				[1]: {
					description: '효과적'
				},
				[2]: {
					description: '비효과적'
				}
			},
			controlTesting: {
				[0]: {
					description: '미선택'
				},
				[1]: {
					description: '예'
				},
				[2]: {
					description: '아니오'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: '미선택'
				},
				[1]: {
					description: '효과적'
				},
				[2]: {
					description: '비효과적'
				}
			}
		},
		ITSP: {
			manageEntity: 'ITSP 관리',
			searchEntity: 'ITSP 검색',
			createEntity: '신규 ITSP',
			editEntity: 'ITSP 편집',
			viewEntity: 'ITSP 보기',
			inputInvaildCharacters: '입력에 다음 문자열을 포함할 수 없습니다: */:<>\\?|”',
			addModalDescription: 'ITSP 설명 입력',
			editModalDescription: 'ITSP 및 관련 정보 편집',
			entityName: 'ITSP',
			selectITProcess: 'IT 프로세스 선택 (필수)',
			entityNameCaps: 'ITSP',
			processIdRequired: 'IT 프로세스는 필수입니다',
			entityNamePlural: 'ITSP',
			itspRequired: 'ITSP 이름은 필수입니다',
			close: '닫기',
			cancel: '취소',
			iTProcesslabel: 'IT 프로세스',
			save: '저장',
			confirm: '확인',
			saveAndCloseLabel: '저장 및 닫기',
			riskNameRequired: '기술 위험은 필수입니다',
			saveAndCreateLabel: '저장 및 추가 생성',
			placeholderText: '신규 ITSP를 생성하거나, 아니면 아래 기존 ITSP를 편집하거나 삭제하십시오',
			deleteEntity: 'ITGC 삭제 ',
			deleteConfirmLabel: '이 ITSP를 삭제하시겠습니까? 기존 연결은 모두 제거됩니다. 이 작업은 실행 취소할 수 없습니다.',
			itspTaskDescription: '이 task 설명을 수정하여, 의존 기간에 걸쳐 기술 위험에 효과적으로 대응했다는 충분하고 적합한 감사증거를 입수하도록 IT 실증절차의 성격, 시기, 범위를 설계합니다.<br>중간일에 IT 실증절차를 수행하는 경우, 중간절차 대상 기간부터 기말까지 기술 위험에 대응한 추가 증거를 입수하기 위해 절차를 설계하고 수행합니다.<br />IT 실증절차의 결과에 대해 결론을 내립니다.',
			operationEffectiveness: '운영 효과성',
			itDesignEffectivenessHeader: '설계 효과성',
			itTestingColumnHeader: '테스트 ',
			testingTitle: '테스트 ',
			frequency: '빈도',
			controlOpertaingEffectiveness: '운영 효과성',
			designEffectiveness: '설계 효과성',
			frequencyITGC: '빈도 선택',
			nameITGC: 'ITGC 이름 (필수)',
			itspNameRequired: 'ITSP 이름 (필수)',
			noResultsFound: '결과를 찾을 수 없습니다',
			selectITRisk: '기술 위험 선택 (필수)',
			itRiskRequired: '기술 위험 (필수)',
			itRiskName: '기술 위험',
			itProcessRequired: 'IT 프로세스 (필수)',
			controlDesignEffectiveness: {
				[0]: {
					description: '미선택'
				},
				[1]: {
					description: '효과적'
				},
				[2]: {
					description: '비효과적'
				}
			},
			controlTesting: {
				[0]: {
					description: '미선택'
				},
				[1]: {
					description: '예'
				},
				[2]: {
					description: '아니오'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: '미선택'
				},
				[1]: {
					description: '효과적'
				},
				[2]: {
					description: '비효과적'
				}
			},
		}
	},
	[Entity.ITSOApplication]: {
		manageEntity: 'IT application 관리',
		searchEntity: 'IT application 검색',
		createEntity: '신규 IT application',
		entityName: 'IT application',
		entityNameCaps: 'IT application',
		entityNamePlural: 'IT application',
		placeholderText: '신규 IT application을 생성하거나, 아래 기존 IT application을 편집하거나 삭제하십시오',
		deleteEntity: 'IT application 삭제',
		deleteConfirmLabel: '이 IT application을 삭제하시겠습니까? 기존 연결은 모두 제거됩니다. 이 작업은 실행 취소할 수 없습니다.'
	},
	[Entity.SCOT]: {
		manageEntity: 'SCOT 관리',
		searchEntity: 'SCOT 검색',
		createEntity: '신규 SCOT',
		entityName: 'SCOT',
		entityNameCaps: 'SCOT',
		entityNamePlural: 'SCOT',
		placeholderText: '신규 SCOT을 생성하거나, 아래 기존 SCOT을 편집하거나 삭제하십시오',
		deleteEntity: 'SCOT 삭제',
		deleteConfirmLabel: '이 SCOT을 삭제하시겠습니까? 기존 연결은 모두 제거됩니다. 이 작업은 실행 취소할 수 없습니다.'
	},
	[Entity.SampleItem]: {
		manageEntity: 'Manage sample tags',
		searchEntity: '태그 검색',
		createEntity: '신규 태그',
		createManageTagEntity: '태그 그룹 관리',
		entityName: '표본 태그',
		entityNamePlural: '태그',
		entityNameForTagGroupPlural: '태그 그룹',
		placeholderText: '신규 태그를 생성하거나, 아래 기존 태그를 편집하거나 삭제하십시오. 신규 태그 그룹을 생성해야 한다면, <b>”태그 그룹 관리”</b>를 클릭하십시오.',
		deleteEntity: '표본 태그 삭제',
		deleteConfirmLabel: '선택한 태그를 삭제하시겠습니까? 이는 연결된 모든 표본에서 삭제됩니다. 이 작업은 실행 취소할 수 없습니다.'
	},
	[Entity.SampleTagGroups]: {
		manageEntity: '표본 태그 그룹 관리',
		searchEntity: 'Search tag groups',
		createEntity: '신규 태그 그룹',
		entityName: 'sample tag group',
		entityNameCaps: '태그 그룹',
		entityNamePlural: '태그 그룹',
		placeholderText: '신규 태그 그룹을 생성하거나, 아래 기존 태그 그룹을 편집하거나 삭제하십시오',
		deleteConfirmLabel: '선택한 태그를 삭제하시겠습니까? 이는 연결된 모든 표본에서 삭제됩니다. 이 작업은 실행 취소할 수 없습니다.'
	},
};

export const inherentRiskFactorTypes = [{
	id: 1,
	label: '복잡성',
	displayOrder: 1
},
{
	id: 2,
	label: '주관성 불확실성',
	displayOrder: 2
},
{
	id: 3,
	label: '부정 또는 오류',
	displayOrder: 3
},
{
	id: 4,
	label: '변동',
	displayOrder: 4
},
{
	id: 5,
	label: '계정 성격',
	displayOrder: 5
},
{
	id: 6,
	label: '특수관계자',
	displayOrder: 6
}
];

export const executionType = [{
	id: 1,
	label: 'PT',
	toolTip: '이 engagement에 있는 절차 [PT 전용]',
	value: '이 engagement [PT 전용]'
},
{
	id: 2,
	label: 'CT',
	toolTip: '다른 engagement에 있는 절차 [CT 전용]',
	value: '다른 engagement [CT 전용]'
},
{
	id: 3,
	label: 'PT/CT',
	toolTip: '이 engagement 및 다른 engagement에 있는 절차 [PT/CT]',
	value: '이 enagement 및 다른 engagement [PT/CT]'
}
];

export const createEditAccountModalLabels = {
	createModalDescription: "아래에 신규 계정 세부정보를 입력하고'<b>{0}</b>'을(를) 선택하여 마칩니다. 다른 계정을 생성하려면,'<b>{1}</b>'을(를) 선택하십시오.",
	editModalDescription: "아래에서 계정 세부정보를 편집하고'<b>{0}</b>'을(를) 선택하여 마칩니다",
	close: '닫기',
	cancel: '취소',
	createAccount: '신규 계정',
	editAccount: '계정 편집',
	newSignificantDisclosure: '신규 유의적인 공시',
	save: '저장',
	confirm: '확인',
	saveAndCloseLabel: '저장 및 닫기',
	saveAndCreateLabel: '저장 및 추가 생성',
	accountNameLabel: '계정 이름 (필수)',
	accountDesignationLabel: '지정',
	accountExecutionTypeLabel: '이 계정에 대한 절차는 어떤 Canvas engagement에서 수행되고 문서화됩니까?',
	accountEstimateLabel: '추정치가 이 계정에 영향을 미칩니까?',
	yes: '예',
	no: '아니오',
	accountStatementTypeLabel: '재무제표 유형',
	pspIndexDropdownLabel: 'PSP index (필수, 5개까지 선택)',
	removePSPIndexLabel: 'PSP index 제거',
	assertionsLabel: '관련 경영진주장을 선택하십시오',
	accountTypeOptions: AccountType,
	assertionOptions: assertions,
	executionTypeOptions: executionType,
	statementTypeOptions: statementTypes,
	noOptionsMessage: '결과를 찾을 수 없습니다',
	accountNameErrorMsg: '필수',
	pspIndexErrorMsg: '필수',
	assertionWarningMessage: '유의적인 위험, 부정위험, 중요왜곡표시위험 또는 추정치가 연결된 경영진주장은 변경할 수 없습니다. 이러한 연결을 먼저 제거해야 합니다.',
	confirmChanges: '변경사항 확인',
	executionTypeWarningMessage: '이 계정에 저장하려는 변경사항은 기존 경영진주장 및 PSP에 영향을 미쳐 링크가 끊어질 것입니다. 계속 진행하시겠습니까? 이 작업은 실행 취소할 수 없습니다.',
	contentUpdateToastMessage: '{0}에 대해 내용을 업데이트할 수 있습니다. 내용 업데이트 페이지에서 내용 업데이트를 개시하십시오.',
	assertionsRequired: '경영진주장을 하나 이상 선택해야 합니다',
	pspIndexDisabledLabel: 'PSP index을 5개까지 선택하십시오. 계속하려면 옵션을 하나 이상 선택 해제하십시오.'
};

export const createEditRiskModalLabels = {
	createModalDescription: "아래에 신규 위험 세부정보를 입력하고 ‘<b>{0}</b>’을(를) 선택하여 마칩니다. 다른 위험을 생성하려면,'<b>{1}</b>'을(를) 선택하십시오.",
	editModalDescription: "아래에서 위험 세부정보를 편집하고'<b>{0}</b>'을(를) 선택하여 마칩니다.",
	close: '닫기',
	cancel: '취소',
	createRisk: '신규 위험',
	editRisk: '위험 편집',
	riskType: '위험 유형 (필수)',
	riskTypeOptions: [{
		value: 1,
		label: '유의적인 위험'
	},
	{
		value: 2,
		label: '부정 위험'
	},
	{
		value: 3,
		label: '중요왜곡표시위험'
	}
	],
	save: '저장',
	saveAndCloseLabel: '저장 및 닫기',
	saveAndCreateLabel: '저장 및 추가 생성',
	riskNameLabel: '위험 이름 (필수)',
	relatedAccountsAssertionsLabel: '연결된 계정 및 경영진주장 (선택사항)',
	relateAccounts: '계정 연결',
	assertionsLabel: '관련 경영진주장 선택',
	riskNameErrorMsg: '필수',
	riskTypeErrorMsg: '필수',
	assertionOptions: assertions,
	removeAccountLabel: '계정 제거',
	required: '필수',
	assertionsRequired: '경영진주장을 하나 이상 선택해야 합니다'
};

export const CreateEditMestLabels = {
	createModalTitle: '신규 기업',
	createModalDescription: "아래에 신규 기업 세부정보를 입력하고 ‘<b>{0}</b>’을(를) 선택하여 마칩니다. 다른 기업을 생성하려면,'<b>{1}</b>'을(를) 선택하십시오.",
	close: '닫기',
	cancel: '취소',
	save: '저장',
	confirm: 'Confirm',
	primary: 'Primary',
	saveAndCloseLabel: '저장 및 닫기',
	saveAndCreateLabel: '저장 및 추가 생성',
	entityNameLabel: '기업 이름 (필수)',
	entityStandardIndexLabel: '기업 표준 index (필수)',
	entityDescriptionLabel: '기업 설명',
	entityNameErrorMsg: '필수',
	entityStandardIndexErrorMsg: '필수',
	editModalTitle: '기업 편집',
	editModalDescription: '아래에서 기업 세부정보를 편집하고 ‘<b>{0}</b>’을(를) 선택하여 마칩니다',
	primaryEntitySelectionLabel: 'Select as the primary entity',
	primaryEntitySelectionMsg: "Only one entity in the engagement can be selected as the primary entity, which will be the determinant for the content delivered to the engagement. An entity will need to be selected as the primary to be able to submit the engagement profile. \'Update content\' permission is required to make or edit the primary entity selection.",
	primaryEntityDisableSelectionLabel: "To change the primary entity designation, select from the \'Edit\' of the entity you wish to designate as primary",
	noAccessLabel: 'Unauthorized. Contact your administrator and try again.',
	primaryEntityConfirmationLabel: 'Primary entity confirmation',
	primaryEntityConfirmationDisplay: '{0} is currently selected as the primary entity. Are you sure you want to change the primary entity?',
	profileV2ChangeNotSubmittedBannerMessage: 'Changes have been made to the profile that will result in content updates. Submit the profile to receive the new content or revert the answers to the previous state.',
};

export const CreateEditITProcessLabels = {
	close: '닫기',
	cancel: '취소',
	yes: '예',
	no: '아니오',
	delete: '삭제',
	save: '저장',
	saveAndCloseLabel: '저장 및 닫기',
	saveAndCreateLabel: '저장 및 추가 생성',
	newITProcessLabel: '신규 IT 프로세스',
	editITProcessLabel: 'IT 프로세스 편집',
	viewITProcessLabel: 'IT 프로세스 보기',
	addDescriptionLabel: "아래에 신규 IT 프로세스 세부정보를 입력하고'<b>{0}</b>'을(를) 선택하여 마칩니다. 다른 IT 프로세스를 생성하려면,'<b>{1}</b>'을(를) 선택하십시오.",
	editDescriptionLabel: '아래에서 IT 프로세스 세부정보를 편집하고 ‘<b>{0}</b>’을(를) 선택하여 마칩니다',
	iTProcessNameLabel: 'IT 프로세스 이름 (필수)',
	confirm: '확인',
	confirmChanges: '확인',
	iTProcessNameErrorMsg: '필수',
	inputInvaildCharacters: '입력에 다음 문자열을 포함할 수 없습니다: */:<>\\?|”',
	remove: '제거'
};

export const CreateEditITRiskLabels = {
	close: '닫기',
	cancel: '취소',
	yes: '예',
	no: '아니오',
	delete: '삭제',
	save: '저장',
	saveAndCloseLabel: '저장 및 닫기',
	saveAndCreateLabel: '저장 및 추가 생성',
	newITRiskLabel: '신규 기술 위험',
	editITRiskLabel: '기술 위험 편집',
	itRiskNameLabel: '기술 위험 (필수)',
	confirm: '확인',
	confirmChanges: '확인',
	itRiskNameErrorMsg: '필수',
	itProcessNotSelectedErrorMsg: '필수',
	hasNoITGCLabel: '기술 위험에 대응하는 ITGC가 없습니다',
	editModalDescription: '기술 위험 설명을 편집하십시오',
	createModalDescription: '기술 위험 설명을 입력하십시오',
	selectITProcess: 'IT 프로세스 선택 (필수)',
	noITProcessAvailable: '생성된 IT 프로세스 없음',
	relatedITProcessLabel: '연결된 IT 프로세스',
	inputInvaildCharacters: '입력에 다음 문자열을 포함할 수 없습니다: */:<>\\?|”',
	remove: '제거'
};

export const CreateEditEstimateLabels = {
	createModalDescription: "아래에 신규 추정치 세부정보를 입력하고 ‘<b>{0}</b>’을(를) 선택하여 마칩니다. 다른 추정치를 생성하려면,'<b>{1}</b>'을(를) 선택하십시오.",
	editModalDescription: '아래에 추정치 세부정보를 편집하고 <b>{0}</b>’을(를) 선택하여 마칩니다',
	close: '닫기',
	cancel: '취소',
	save: '저장',
	saveAndCloseLabel: '저장 및 닫기',
	saveAndCreateLabel: '저장 및 추가 생성',
	createModalTitle: '신규 추정치',
	editEstimateLabel: '추정치 편집',
	estimateNameLabel: '추정치 이름 (필수)',
	analysisPeriodBalance: '분석일 잔액 (필수)',
	analysisPeriodDate: '분석일 (필수)',
	comparativePeriodBalance: '비교일 잔액 (필수)',
	comparativePeriodDate: '비교일 (필수)',
	estimateCategory: '추정치 범주 (필수)',
	confirm: '확인',
	estimateNameErrorMsg: '필수',
	analysisPeriodBalanceErrorMsg: '필수',
	analysisPeriodDateErrorMsg: '필수',
	comparativePeriodBalanceErrorMsg: '필수',
	comparativePeriodDateErrorMsg: '필수',
	estimateCategoryErrorMsg: '필수',
	remove: '제거',
	balanceNOTApplicable: '잔액 해당 없음',
	wtDetailPrefixForEstimate: '<p>Complete the estimate walkthrough task.</p>',

	riskLevelOptions: [{
		value: 4,
		label: 'Very low risk'
	},
	{
		value: 5,
		label: 'Lower risk'
	},
	{
		value: 6,
		label: 'Higher risk'
	},
	{
		value: 7,
		label: 'Not selected'
	}
	]
};

export const CreateEditControlLabels = {
	createModalTitle: '신규 통제',
	editModalTitle: '통제 편집',
	viewModalTitle: '통제 보기',
	close: '닫기',
	cancel: '취소',
	save: '저장',
	saveAndCloseLabel: '저장 및 닫기',
	saveAndCreateLabel: '저장 및 추가 생성',
	controlNameLabel: '통제 이름 (필수)',
	frequency: '빈도',
	controlType: '통제 유형',
	frequencyTypeOptions: controlFrequencyType,
	controlTypeOptions: controlTypes,
	designEffectiveness: '설계 효과성',
	operatingEffectiveness: '운영 효과성',
	testingLabel: '테스트',
	lowerRiskLabel: 'Is control lower risk?',
	effective: '효과적',
	ineffective: '비효과적',
	yes: '예',
	no: '아니오',
	required: '필수',
	remove: '제거',
	noOptionsMessage: '결과를 찾을 수 없습니다',
	disabledTabTooltipMessage: "IT application을 연결하려면'통제 유형'을'IT application 통제' 또는'IT 의존 수동 통제'로 선택하십시오 ",
	itAppLabels: {
		tabLabel: 'IT App',
		dropdownLabel: 'IT application 연결',
		noRelatedItems: '연결된 IT Application 없음',
		itApplications: 'IT application'
	},
	soLabels: {
		tabLabel: 'SO',
		dropdownLabel: '서비스조직 연결',
		noRelatedItems: '연결된 서비스조직 없음',
		serviceOrganizations: '서비스조직'
	},
	controlNameErrorMsg: '필수',
	createModalDescriptionLabel: "아래에 신규 통제 세부정보를 입력하고 ‘<b>{0}</b>’을(를) 선택하여 마칩니다. 다른 통제를 생성하려면,'<b>{1}</b>'을(를) 선택하십시오.",
	editModalDescriptionLabel: '아래에서 통제 세부정보를 편집하고 ‘<b>{0}</b>’을(를) 선택하여 마칩니다',
	viewModalDescriptionLabel: '통제 및 관련 IT application과 서비스조직 보기',
	wcgwLabel: 'WCGW'
};

export const ITApplicationTypeLabels = [{
	value: 1,
	label: 'Application/도구'
},
{
	value: 2,
	label: '데이터베이스'
},
{
	value: 3,
	label: '운영 시스템'
},
{
	value: 4,
	label: '네트워크'
},
{
	value: 6,
	label: '범주화되지 않음'
}
];

export const CreateEditITApplicationLabels = {
	close: '닫기',
	cancel: '취소',
	yes: '예',
	no: '아니오',
	delete: '삭제',
	save: '저장',
	saveAndCloseLabel: '저장 및 닫기',
	saveAndCreateLabel: '저장 및 추가 생성',
	newITApplicationLabel: '신규 IT application',
	editITApplicationLabel: 'IT application 편집',
	iTApplicationNameLabel: 'IT application 이름',
	confirm: '확인',
	confirmChanges: '변경사항 확인',
	noOptionsMessage: '결과를 찾을 수 없습니다',
	iTAppNameErrorMsg: '필수',
	controls: '통제',
	substantive: '실증',
	remove: '제거',
	iTApplicationStrategyLabel: 'IT application 전략',
	SCOTsLabel: 'SCOT 이름',
	StrategyLabel: '전략',
	ControlsLabel: '통제',
	ControlTypeLabel: '유형',
	addDescriptionLabel: "아래에서 IT application 세부정보를 편집하고'<b>{0}</b>'을(를) 선택하여 마칩니다. 다른 IT application을 생성하려면,'<b>{1}</b>을(를) 선택하십시오.",
	editDescriptionLabel: '아래에서 IT application 세부정보를 편집하고 ‘<b>{0}</b>’을(를) 선택하여 마칩니다',
	scotErrorMessage: '통제가 연결되어 있기 때문에 IT application에서 SCOT 연결을 해제할 수 없습니다',
	SCOTsLabels: {
		tabLabel: 'SCOT',
		dropdownLabel: 'SCOT 연결',
		noRelatedItems: '연결된 SCOT 없음'
	},
	ControlsLabels: {
		tabLabel: '통제',
		dropdownLabel: '통제 연결',
		noRelatedItems: '연결된 통제 없음'
	},
	strategyType: {
		1: '통제',
		2: '실증',
		3: 'Rely',
		4: 'Not Rely'
	},
	controlType: {
		1: 'IT Application',
		2: 'IT 의존 수동',
		3: '수동 예방',
		4: '수동 적발'
	},
	technologyTypeOptions: ITApplicationTypeLabels,
	technologyType: 'Select technology type'
};

export const CreateEditSCOTLabels = {
	createModalTitle: '신규 SCOT',
	editModalTitle: 'SCOT 편집',
	viewModalTitle: 'SCOT 보기',
	createModalDescription: "아래에 신규 SCOT 세부정보를 입력하고'<b>{0}</b>'을(를) 선택하여 마칩니다. 다른 SCOT을 생성하려면,'<b>{1}</b>'을(를) 선택하십시오.",
	editModalDescription: '아래에서 SCOT 세부정보를 편집하고 ‘<b>{0}</b>’을(를) 선택하여 마칩니다',
	close: '닫기',
	cancel: '취소',
	save: '저장',
	saveAndCloseLabel: '저장 및 닫기',
	saveAndCreateLabel: '저장 및 추가 생성',
	scotNameLabel: 'SCOT 이름 (필수)',
	scotStrategyLabel: 'SCOT 전략',
	scotTypeLabel: 'SCOT 유형',
	hasEstimateLabel: '추정치가 이 SCOT에 영향을 미칩니까?',
	itAPPUsedLabel: '사용된 IT application이 있습니까?',
	routine: '일상적',
	nonRoutine: '비일상적',
	controls: '통제',
	substantive: '실증',
	yes: '예',
	scotNameErrorMsg: '필수',
	remove: '제거',
	noOptionsMessage: '결과를 찾을 수 없습니다',
	disabledTabTooltipMessage: 'IT application을 연결하려면 “사용된 IT application이 있습니까?”를 선택하십시오',
	itAppLabels: {
		itApplications: '연결된 IT application',
		tabLabel: 'IT App',
		dropdownLabel: 'IT application 연결',
		noRelatedItems: '연결된 IT application 없음'
	},
	soLabels: {
		serviceOrganizations: '연결된 서비스 조직 없음',
		tabLabel: 'SO',
		dropdownLabel: '서비스 조직 연결',
		noRelatedItems: '연결된 서비스 조직 없음'
	},
	wtDetailPrefix: '<p>매 기간마다 walkthrough 절차를 수행하여 모든 일상적 및 비일상적 SCOT과 유의적인 공시 프로세스에 대한 이해를 확인합니다. 또한, PCAOB 감사인 경우, 추정 SCOT에 대해 walkthrough 절차를 수행합니다.<br/>통제 의존 전략을 취하는 경우, 모든 SCOT과 유의적인 위험에 대응하는 통제에 대해, 관련 통제가 적합하게 설계되고 구축되었는지 확인합니다. 통제 의존 전략이 여전히 적합한 결정인지 확인합니다.<br/><br/>SCOT의 운영이 조서에 정확하게 설명되어 있고, IT 사용으로 발생하는 위험 등 적합한 WCGW 및 (해당하는 경우) 관련 통제를 모두 식별했는지 결론 내립니다.<br/><br/> 실증 단독 전략을 사용하는 추정 SCOT인 경우 실증절차에 기초하여 추정 SCOT에 대한 이해가 적합한지 확인합니다.</p>',
};

export const ViewSampleItemLabels = {
	previous: '이전',
	next: '다음',
	sampleDateLabel: '표본 날짜',
	attributesHeader: '정보',
	statusHeader: '상태',
	noAttributesLabel: '사용 가능한 정보 없음',
	present: '표시됨',
	presentWithComments: 'Comment와 함께 표시됨',
	notPresent: '표시되지 않음',
	notApplicatable: '해당 없음',
	naLabel: 'N/A',
	additionDocumentation: '추가 문서화',
	deleteSampleHeader: '표본 삭제',
	deleteSmapleDescription: '선택한 표본을 삭제하시겠습니까? 이 작업은 실행 취소할 수 없습니다.',
	deleteSamplePreText: '표본 설명',
	relateTagModalTitle: '표본에 태그 연결',
	relateTagModalDescription: '표본에 태그를 하나 이상 연결하십시오. 신규 태그를 추가하려면, <b>’태그 관리’</b>를 클릭하십시오. 태그 연결은 archive되지 않지만 태그 자체는 archive되므로 rollforward하여 사용할 수 있습니다.',
	relateTagTableHeader: '태그 이름',
	relateTagTableSubHeader: '태그 그룹',
	tagsCounter: '{0} 태그',
	tagCounter: '{0} 태그',
	relateTagGroupLabel: '태그 그룹',
	relateTagSearchPlaceholder: '검색',
	relateTagClearSearch: '처리',
	relateTagShowSelectedOnly: '연결된 항목만 표시',
	manageTagsLabel: '태그 관리',
	addTag: '태그 추가',
	supportingDocumentationTitle: '지원 문서',
	dropdownAll: '모두',
	noResultsLabel: '결과를 찾을 수 없습니다',
	noDataLabel: '데이터를 찾을 수 없습니다',
	attributeStatusModalTitle: 'Mark all as present',
	attributeStatusModalCancelButton: '취소',
	attributeStatusModalConfirmButton: '저장',
	attributeStatusModalDescription: '속성을 현재로 표시하시겠습니까? 상태가 선택되지 않은 속성만 현재로 표시됩니다.',
	attributeModalDeleteErrorMessage: '속성 상태를 업데이트할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 문제가 지속되면 헬프 데스크에 문의하십시오.',
};

export const ShortRiskTypeForAres = {
	1: 'Significant',
	2: 'Fraud',
	3: 'Inherent',
	4: 'Very low risk',
	5: 'Lower risk',
	6: 'Higher risk',
	7: 'Not selected'
};

export const RelateEstimateToAssertionLabels = {
	relateAccountsAndAssertions: '계정 및 경영진주장 연결',
	relateAccountsToEstimate: '추정치에 계정 연결',
	accounts: '계정',
	designation: '지정',
	relatedAssertions: '연결된 경영진주장',
	accountNameField: '계정 이름',
	accountTypeIdField: '계정 유형 ID',
	assertionsField: '경영진주장',
	executionTypeIdField: '실행 유형 ID',
	notSelected: '미선택',
	pathField: '경로',
	noAccountsAvailable: '사용 가능한 계정 없음',
	noRelatedAccounts: '추정치에 연결된 계정 없음',
	accountId: '계정 ID',
	remove: '제거'
};

//Send instructions switcher
export const sendIntructionsSwitcherLabels = {

	[sendInstructionsSwitcherIds.groupInstructions]: '그룹 지침',
	[sendInstructionsSwitcherIds.groupRiskAssessment]: '그룹 위험평가'
};

export const RelateEstimateToAccountLabels = {
	relatEstimatesToAccount: '계정에 추정치 연결',
	showOnlyRelatedEstimates: '연결된 추정치만 표시',
	noEstimatesResult: labels.noResultsFound,
	noEstimatesLabel: '이 engagement에서 생성된 추정치 없음',
	estimateNameHeader: '추정치 이름',
	relatedEstimateCounter: '{0} 추정치',
	relatedEstimatesCounter: '{0} 추정치',
	relatedAccount: '계정/공시',
	close: labels.close
};

export const RelateAccountsToEstimateLabels = {
	relateAccountsToEstimate: '추정치에 계정 연결',
	showOnlyRelatedAccounts: '연결된 계정만 표시',
	noAccountsResult: labels.noResultsFound,
	noAccountsLabel: 'Engagement에 생성된 계정 없음',
	accountNameHeader: '계정 이름',
	relatedAccountCounter: '{0} 계정',
	relatedAccountsCounter: '{0} 계정',
	relatedEstimate: labels.estimate,
	close: labels.close
};

export const SupportingDocumentationLabels = {
	evidence: '증거',
	priorPeriod: '전기',
	temporaryFiles: '임시 파일',
	externalDocuments: '외부 문서',
	addEvidenceBtn: '증거 추가',
	addTemporaryFilesBtn: '임시 파일 추가',
	notes: '노트',
	signOffs: 'Sign-off',
	name: '이름',
	supportingDocumentationTitle: '지원 문서',
	temporaryFilesEmptyPlaceholder1: '연결된 임시 문서 없음',
	temporaryFilesEmptyPlaceholder2: '임시 문서를 연결하려면 {addTemporaryFiles}을(를) 클릭하십시오',
	evidencePlaceholderLine1: '연결된 증거 없음',
	evidencePlaceholderLine2: '증거를 연결하려면 {addEvidenceBtn}을(를) 클릭하십시오',
	removeFromSample: '표본에서 제거',
	unlink: '링크 해제',
	retailControlEvidenceLabel: '이 특정 표본 항목에 대한 속성 테스트를 뒷받침하는 통제 증거를 보관했습니까?',
	removeEvidenceModalTitle: '표본에서 증거 제거',
	removeEvidenceModalDesc: '이 표본에서 모든 증거를 제거하시겠습니까?',
	removeEvidenceErrorMessage: '이 문서는 더 이상 이 표본에서 사용할 수 없습니다. 페이지를 새로 고침하여 다시 시도하십시오. 오류가 지속되면, 헬프 데스크에 문의하십시오.'
}

export const deleteSampleItemAttributeModal = {
	modalDescription: '이 정보에 대한 선택을 제거하시겠습니까? 추가 문서가 삭제됩니다.',
	modalTitle: '선택 제거',
	modalConfirmButton: '제거',
	modalCancelButton: '취소',
	additionalDocumentationLabel: '추가 문서'
}
export const accountsFilterLabels = [{
	id: accountsFilter.allAccounts,
	label: '모든 계정',
	value: accountsFilter.allAccounts
},
{
	id: accountsFilter.accountsWithRelatedEstimates,
	label: '추정치가 연결된 계정',
	value: accountsFilter.accountsWithRelatedEstimates
},
{
	id: accountsFilter.accountsWithoutRelatedEstimates,
	label: '추정치가 연결되지 않은 계정',
	value: accountsFilter.accountsWithoutRelatedEstimates
}
];

export const changeSampleItemAttributeModal = {
	modalDescription: '이 속성에 대한 선택을 변경하시겠습니까? 추가 문서를 삭제합니다.',
	modalTitle: '선택 변경',
	modalConfirmButton: '변경',
	modalCancelButton: '취소'
}
export const scotsFilterLabels = [{
	id: scotsFilter.allScots,
	label: '모든 SCOT',
	value: scotsFilter.allScots
},
{
	id: scotsFilter.scotsWithRelatedEstimates,
	label: '추정치가 연결된 SCOT',
	value: scotsFilter.scotsWithRelatedEstimates
},
{
	id: scotsFilter.scotsWithoutRelatedEstimates,
	label: '추정치가 연결되지 않은 SCOT',
	value: scotsFilter.scotsWithoutRelatedEstimates
}
];

export const CreateEditTagGroupLabels = {
	createModalTitle: '신규 표본 태그 그룹',
	editModalTitle: '표본 태그 그룹 편집',
	createModalDescription: "아래에 태그 그룹 세부정보를 입력하고 <b>\’저장 및 닫기\’</b>를 선택하여 마칩니다. 다른 태그 그룹을 생성하려면, <b>\'저장 및 추가 생성\'</b>을 선택하십시오.",
	editModalDescription: '아래에서 task 그룹 세부정보를 편집하고 ‘<b>{0}</b>’을(를) 선택하여 마칩니다',
	close: '닫기',
	cancel: '취소',
	save: '저장',
	saveAndCloseLabel: '저장 및 닫기',
	saveAndCreateLabel: '저장 및 추가 생성',
	tagGroupNameLabel: '태그 그룹 이름 (필수)',
	required: '필수'
};

export const CreateEditTagLabels = {
	createModalTitle: '신규 표본 태그',
	editModalTitle: 'Edit sample tag',
	createModalDescription: '아래에 태그 세부정보를 입력하고 ‘<b>저장 및 닫기</b>’를 선택하여 마칩니다. 다른 태그를 생성하려면, ‘<b>저징 및 추가 생성</b>’을 선택하십시오.',
	editModalDescription: `Edit the tag details below and select'<b>{0}</b>' to finish.`,
	tagNameLabel: '태그 이름 (필수)',
	tagGroupNameLabel: '태그 그룹 이름 (필수)',
	tagColorLabel: '색상 (필수)',
	saveAndCloseLabel: '저장 및 닫기',
	saveAndCreateLabel: '저장 및 추가 생성',
	cancelLabel: '취소',
	required: '필수',
	save: '저장',
	noresultsLabel: '사용 가능한 태그 그룹 없음',
	tagColors: [{
		text: '빨간색',
		color: '빨간색'
	},
	{
		text: '귤색',
		color: '귤색'
	},
	{
		text: '청록색',
		color: '청록색'
	},
	{
		text: '파란색',
		color: '파란색'
	},
	{
		text: '보라색',
		color: '보라색'
	},
	{
		text: '녹색',
		color: '녹색'
	}
	]
};

export const ITProcessFlowLabels = {
	itProcess: 'IT 프로세스',
	technology: '기술',
	technologies: '기술',
	technologyName: '기술 이름',
	supportingTechnologyName: '지원 기술 이름',
	technologyType: '기술 유형',
	showOnlyRelated: '연결된 항목만 표시',
	technologiesCounter: '{0} 기술',
	technologyCounter: '{0} 기술',
	supportingTechnologyLabel: '지원 기술',
	relatedITAppNoDataPlaceholder: 'IT 프로세스에 연결된 기술 없음',
	relateTechnology: '기술 연결',
	supportingITPAppNoDataPlaceholder: 'IT 프로세스를 지원하는 기술 없음',
	itRiskHeader: 'IT 위험',
	itgcHeader: 'ITGC',
	itspHeader: 'ITSP',
	noDataPlaceholderITGC: 'ITGC가 하나 이상 식별되거나, IT 위험에 ITGC가 없다는 것을 지정해야 합니다. {createNewITGC} 또는 {relateITGC}을(를) 생성하거나 IT 위험에 대응하는 {noITGC}을(를) 표시하십시오.',
	noDataPlaceholderITSP: 'ITGC를 비효과적으로 평가했거나 IT 위험에 대응하는 ITGC가 없는 것으로 확인했다면, IT 실증 테스트 절차(ITSP)를 수행하여 비효과적인 ITGC와 연결된 IT 프로세스 내 IT 위험이 악용되지 않았다는 합리적인 확신을 얻을 수 있습니다. {createNewITSP} 또는 {relateITSP}을(를) 생성하십시오.',
	noRecordsFound: '이 IT 프로세스에 식별된 IT 위험 없음',
	noITGCPlaceholder: 'IT 위험에 대응하는 ITGC가 없습니다',
	relateSupportingTechnology: '지원 기술 연결',
	relatedTechnologiesNotAvailable: '이 문서에 연결된 기술을 사용할 수 없습니다',
	supportingTechNotAvailable: '이 문서에 지원 기술을 사용할 수 없습니다',
	relatedITRisksNotAvailable: '이 문서에 연결된 IT 위험을 사용할 수 없습니다',
	relateITGC: 'Relate ITGCs',
	itRisk: 'IT 위험',
	itgcCounter: '{0} ITGC',
	itgcsCounter: '{0} ITGC',
	itgcName: 'ITGC 이름',

};

export const ITRisksFlowLabels = {
	itRisk: 'IT 위험',
	relatedITRiskNoDataPlaceholder: 'IT 프로세스에 연결된 IT 위험 없음',
	newITRisk: '신규 IT 위험',
	relatedITRisksNotAvailable: '이 문서에 연결된 IT 위험을 사용할 수 없습니다',
	deleteConfirmLabel: '선택한 IT 위험을 삭제하시겠습니까? 이 작업은 실행 취소할 수 없습니다.',
	deleteITRisk: 'IT 위험 삭제',
	CreateITFlowITRiskLabels: {
		close: '닫기',
		cancel: '취소',
		yes: '예',
		no: '아니오',
		delete: '삭제',
		save: '저장',
		saveAndCloseLabel: '저장 및 닫기',
		saveAndCreateLabel: '저장 및 추가 생성',
		newITRiskLabel: '신규 IT 위험',
		itRiskNameLabel: 'IT 위험 이름 (필수)',
		itRiskCharactersLabel: 'characters',
		itRiskOfLabel: 'of',
		confirm: '확인',
		confirmChanges: '확인',
		itRiskNameErrorMsg: '필수',
		itProcessNotSelectedErrorMsg: '필수',
		hasNoITGCLabel: 'IT 위험에 대응하는 ITGC가 없습니다',
		createModalDescription: "아래에 IT 위험 세부정보를 입력하고'<b>저장 및 닫기</b>’를 선택하여 마칩니다. 다른 기술을 생성하려면,'<b>저장 및 추가 생성</b>'을 선택하십시오.",
		relatedITProcessLabel: 'IT 프로세스',
		inputInvaildCharacters: '입력에 다음 문자열을 포함할 수 없습니다: */:<>\\?|”',
		remove: 'Remove',
		editModalDescription: '아래에서 IT 위험 세부정보를 편집하고 <b>저장</b>을 선택하여 마칩니다',
		editITRiskLabel: 'Edit IT risk'
	}
};

export const ITProcessListingLabels = {
	all: '모두',
	manageChange: '변경사항 관리',
	manageAccess: '접근 관리',
	manageSecuritySettings: '보안 설정 관리',
	itOperations: 'IT 작업',
	systemImplementation: '시스템 구축',
	category: '범주',
	uncategorized: '범주화되지 않음',
	technologies: '기술',
};

export const ITProcessQuickFilterOptions = {
	0: ITProcessListingLabels.all,
	1: ITProcessListingLabels.manageChange,
	2: ITProcessListingLabels.manageAccess,
	3: ITProcessListingLabels.manageSecuritySettings,
	4: ITProcessListingLabels.itOperations,
	5: ITProcessListingLabels.systemImplementation
}

// Relate Technology Modal
export const RelateTechnologyModalLabels = {
	relateTechnology: '기술 연결',
	relatedTechnologiesDescription: '기술 이름',
	supportingTechnologyName: '지원 기술 이름',
	technology: '{0} technology',
	technologies: '{0} technologies'
};

export const AccountStandardROMMListingLabels = {
	accountRisksNotAvailableForDocument: '이 문서에 사용 가능한 계정 위험이 없습니다',
	noRelatedObject: '연결된 object 없음. 시작하려면 object를 연결하십시오.',
	noResultsFound: '사용 가능한 위험 없음',
	acceptedText: '수락됨',
	rejectedText: '거절됨',
	allRisksRejected: '모든 위험이 거절됐습니다',
	relevantAssertions: '관련 경영진주장',
	rejectLabel: '거절',
	acceptLabel: '수락',
	rejectionRationaleLabel: '거절 근거',
	rejectionCategoryText: '거절 범주',
	editRejectionRationaleText: '거절 근거 편집',
	rejectionRationalePlaceholder: "Are you sure you want to reject the selected risk? Enter the details below and select <strong>\'Reject\'</strong>.",
	cancel: 'Cancel',
	rejectionRationaleTextAreaPlaceholder: 'Rationale (required)',
	rejectionCategoryDropdownPlaceholder: 'Rejection category (required)',
	required: 'Required',
	preRejectedText: 'Pre-rejected',
	additionalContextLabel: 'Additional context',
	additionalContextwhyRiskShouldBeRejected: 'Click {0} to add additional context specific to this client for why this risk should be rejected.',
	hereLink: 'here',
	editAdditionalContextText: 'Edit additional context'
}

export const RejectionCategory = [{
	id: 1,
	label: '발생 가능성 - (통제 고려 없이) 합리적인 발생 가능성 없음'
},
{
	id: 2,
	label: '규모 - 잠재적인 왜곡표시가 중요하지 않습니다'
},
{
	id: 3,
	label: '이 engagement에 대한 위험이 중요하지 않습니다'
},
{
	id: 4,
	label: '다른 engagement에서 ROMM에 대응했습니다 (그룹 감사 전용)'
},
]

export const formLabels = {
	required: labels.required,
	maxLength: labels.maxLength
};

export const paginationLabels = {
	show: labels.pagingShowtext,
	first: '첫 페이지',
	last: '마지막 페이지',
	prev: '이전 페이지',
	next: '다음 페이지'
};
