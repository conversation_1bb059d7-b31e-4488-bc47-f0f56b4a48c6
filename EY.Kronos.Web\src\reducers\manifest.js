// DO NOT edit or delete this file.
// This file is auto-generated using: npm run compile:reducer-manifest
// Run that command after any changes to reducers.
import { accountAssertionsReducer as accountAssertionsReducer1, accountDetailsReducer as accountDetailsReducer1, accountReducer as accountReducer1, accountsEditReducer as accountsEditReducer1, accountsReducer as accountsReducer1, accountsWithCombinedScotsAndRisksReducer as accountsWithCombinedScotsAndRisksReducer1, addAccountReviewReducer as addAccountReviewReducer1, areAllCanvasAccountsMappedToHelixAccountsReducer as areAllCanvasAccountsMappedToHelixAccountsReducer1, currentAccountReducerSEM as currentAccountReducerSEM1, removeAccountReviewReducer as removeAccountReviewReducer1 } from "./Accounts/accountsreducer.js";
import { manageAccountReducer as manageAccountReducer2, manageAccountsReducer as manageAccountsReducer2 } from "./Accounts/manageaccountsreducer.js";
import default3 from "./ActiveAnnotation/activeAnnotationReducer.js";
import { annotationsReducer as annotationsReducer4 } from "./Annotations/annotationsreducer.js";
import default5 from "./apponlinestatereducer.js";
import { aresCompareBodiesReducer as aresCompareBodiesReducer6 } from "./Ares/Compare/aresCompareBodiesReducer.js";
import { aresFormDataReducer as aresFormDataReducer7 } from "./Ares/FormData/aresFormDataReducer.js";
import { aresFormDataByBodyTypeIdReducer as aresFormDataByBodyTypeIdReducer8 } from "./Ares/FormDataByBodyType/aresFormDataByBodyTypeReducer.js";
import { aresFormHeadersReducer as aresFormHeadersReducer9 } from "./Ares/formHeadersReducer.js";
import { aresLinkedDocumentsReducer as aresLinkedDocumentsReducer10 } from "./Ares/LinkedDocuments/linkedDocumentsReducer.js";
import { entityLayersReducer as entityLayersReducer11, multiEntityReducer as multiEntityReducer11, stEntitiesReducer as stEntitiesReducer11 } from "./Ares/MultiEntities/stEntitiesreducer.js";
import { aresMyRiskFactorDescriptionReducer as aresMyRiskFactorDescriptionReducer12 } from "./Ares/RiskFactors/aresMyRiskFactorDescriptionReducer.js";
import { aresSnapshotsReducer as aresSnapshotsReducer13 } from "./Ares/Snapshots/aresSnapshotsReducer.js";
import { stEntityIdReducer as stEntityIdReducer14 } from "./Ares/stEntityIdReducer.js";
import { aresTaskDocumentsReducer as aresTaskDocumentsReducer15, navigationPanelReducer as navigationPanelReducer15 } from "./Ares/taskDocumentsReducer.js";
import { aresTaskDetailsReducer as aresTaskDetailsReducer16 } from "./Ares/TaskList/taskDetailsReducer.js";
import { aresTaskListReducer as aresTaskListReducer17 } from "./Ares/TaskList/tasklistreducer.js";
import default18 from "./Ares/taskRelatedDocumentsReducer.js";
import default19 from "./Assertions/_assertionreducer.js";
import default20 from "./Assertions/accountAssertionsreducer.js";
import { assertionsReducer as assertionsReducer21, engagementAssertionsReducer as engagementAssertionsReducer21 } from "./Assertions/assertionsreducer.js";
import default22 from "./AssessRiskSummary/assessRiskSummaryReducer.js";
import { controlAttributesReducer as controlAttributesReducer23 } from "./Attributes/controlAttributesReducer.js";
import default24 from "./BodiesSpecial/bodiesSpecialReducer.js";
import default25 from "./CaptureAnnotations/captureAnnotationReducer.js";
import default26 from "./Configurations/configurationsreducer.js";
import default27 from "./connectivitystatereducer.js";
import { contentUpdatesReducer as contentUpdatesReducer28 } from "./ContentUpdates/contentupdatesreducer.js";
import { controlsDetailsReducer as controlsDetailsReducer29 } from "./Control/controlsReducer.js";
import default30 from "./Document/bulkRelateReducer.js";
import default31 from "./Document/docHelperDocumentsReducer.js";
import default32 from "./Document/docHelperErrorReducer.js";
import default33 from "./Document/documentextensionreducer.js";
import { documentsReducer as documentsReducer34, setDocumentIdReducer as setDocumentIdReducer34 } from "./Document/documentsreducer.js";
import default35 from "./Document/documenttypesreducer.js";
import default36 from "./Document/duplicateDocumentsReducer.js";
import default37 from "./Document/entityDocumentReducer.js";
import default38 from "./Document/fourFortyGlDocumentsReducer.js";
import default39 from "./Document/knowledgeFormsReducer.js";
import default40 from "./Document/limitedRiskDocumentsReducer.js";
import default41 from "./Document/uploaddocumentreducer.js";
import { documentLastModifiedDateReducer as documentLastModifiedDateReducer42 } from "./documentLastModifiedDateReducer.js";
import default43 from "./EngagementComment/engagementcommentreducer.js";
import { countryStatusReducer as countryStatusReducer44, engagementMetaDataReducer as engagementMetaDataReducer44, engagementReducer as engagementReducer44, engagementTeamMembers as engagementTeamMembers44, engagementUserRolesReducer as engagementUserRolesReducer44, engagementUsersWithRoles as engagementUsersWithRoles44, getCountriesReducer as getCountriesReducer44, getLanguagesReducer as getLanguagesReducer44, paansReducer as paansReducer44, rbacUserRolesReducer as rbacUserRolesReducer44, setEngagementIdReducer as setEngagementIdReducer44, setHasBodyIdFilterReducer as setHasBodyIdFilterReducer44, setIsAresViewReducer as setIsAresViewReducer44, setIsCompareResponsesViewReducer as setIsCompareResponsesViewReducer44, setIsDrawerModeReducer as setIsDrawerModeReducer44, setIsGuidedWorkFlowV2Reducer as setIsGuidedWorkFlowV2Reducer44, setIsTrackChangesViewReducer as setIsTrackChangesViewReducer44, setNavigationFilterReducer as setNavigationFilterReducer44, setServerPrintDataReducer as setServerPrintDataReducer44, workspaceReducer as workspaceReducer44 } from "./Engagements/engagementreducer.js";
import { quickLinkReducer as quickLinkReducer45 } from "./Engagements/quicklinksreducer.js";
import { entityFormsReducers as entityFormsReducers46 } from "./EntityForms/entityformsreducer.js";
import default47 from "./errorreducer.js";
import { estimatesReducer as estimatesReducer48 } from "./Estimates/estimatesReducer.js";
import default49 from "./Eyq/AIChatBotIntegrationParametersReducer.js";
import default50 from "./Filter/filterdatareducer.js";
import default51 from "./Filter/filterreducer.js";
import { financialStatementReducer as financialStatementReducer52 } from "./FinancialStatement/financialstatementreducer.js";
import { flowChartReducer as flowChartReducer53, flowChartsReducer as flowChartsReducer53 } from "./FlowChart/flowchartreducer.js";
import { flowchartstepEditModalReducer as flowchartstepEditModalReducer54, flowchartstepReducer as flowchartstepReducer54 } from "./FlowChart/flowchartstepreducer.js";
import default55 from "./Form/formActionsReducer.js";
import default56 from "./Form/formBodyReducer.js";
import default57 from "./Form/formreducer.js";
import default58 from "./FormBodyRelatedDocuments/formBodyRelatedDocumentsReducer.js";
import { IndividualIndependenceDocumentReducer as IndividualIndependenceDocumentReducer59, SubmitIndependenceActionReducer as SubmitIndependenceActionReducer59, SubmitIndependenceDataReducer as SubmitIndependenceDataReducer59, SubmitProfileDataReducer as SubmitProfileDataReducer59, formBodyDataReducer as formBodyDataReducer59, formBodyDisplayDataForUpdateReducer as formBodyDisplayDataForUpdateReducer59, formBodyDisplayDataNoStoreUpdateReducer as formBodyDisplayDataNoStoreUpdateReducer59, formBodyDisplayDataReducer as formBodyDisplayDataReducer59, formOverwriteDataReducer as formOverwriteDataReducer59, formProfileDataReducer as formProfileDataReducer59, formProfileUpdateDataReducer as formProfileUpdateDataReducer59 } from "./FormData/formdatareducer.js";
import default60 from "./FormUsers/formusersreducer.js";
import default61 from "./GroupAudit/AssignedScopes/index.js";
import default62 from "./GroupAudit/GroupAccounts/index.js";
import default63 from "./GroupAudit/GroupComponents/index.js";
import default64 from "./GroupAudit/GroupInstructions/index.js";
import default65 from "./GroupAudit/GroupScopes/index.js";
import default66 from "./GroupAudit/GroupSnapshot/index.js";
import default67 from "./GroupAudit/GroupSnapshots/index.js";
import default68 from "./GroupAudit/index.js";
import default69 from "./HelixIntegration/engagementhelixreducer.js";
import default70 from "./HelixIntegration/engagementhelixsummaryreducer.js";
import default71 from "./HelixIntegration/helixreducer.js";
import default72 from "./HelixSnapshot/helixsnapshotreducer.js";
import default73 from "./InternalUsers/internalusersreducer.js";
import { itApplicationDataReducer as itApplicationDataReducer74, itApplicationDetailsReducer as itApplicationDetailsReducer74, itApplicationReducer as itApplicationReducer74 } from "./ITApplication/itapplicationreducer.js";
import { itcontrolDetailsReducer as itcontrolDetailsReducer75 } from "./ITControl/itcontrolReducer.js";
import default76 from "./ITPlanning/index.js";
import { itProcessReducer as itProcessReducer77 } from "./ITProcess/itprocessreducer.js";
import { itRiskReducer as itRiskReducer78 } from "./ITRisk/itriskreducer.js";
import default79 from "./JournalSource/journalsourcereducer.js";
import default80 from "./loadingReducer.js";
import default81 from "./ManageObjects/manageobjectsreducer.js";
import default82 from "./MarkComplete/markCompleteTasksReducer.js";
import default83 from "./Materiality/materialityreducer.js";
import default84 from "./MediaPlaying/mediaplayingreducer.js";
import { adcModalOpenStateReducer as adcModalOpenStateReducer85, modalLevelStateReducer as modalLevelStateReducer85, modalOpenStateReducer as modalOpenStateReducer85 } from "./modalopenstatereducer.js";
import default86 from "./NotesModal/rnhnotedetailreducer.js";
import default87 from "./offlineprepreducer.js";
import default88 from "./Projects/projectReducer.js";
import default89 from "./PSPIndexes/pspIndexesReducer.js";
import default90 from "./RelatedDocuments/relatedDocumentsReducer.js";
import { relatedObjectsReducer as relatedObjectsReducer91 } from "./RelatedObjects/relatedobjectsreducer.js";
import default92 from "./RelateTask/relatetaskreducer.js";
import default93 from "./ReviewNotes/reviewnotesreducer.js";
import default94 from "./Risk/_riskReducer.js";
import default95 from "./Risk/relatedwcgwsreducer.js";
import { newRiskReducer as newRiskReducer96, relateRiskReducer as relateRiskReducer96, relatedRisksReducer as relatedRisksReducer96, riskReducer as riskReducer96, risksReducer as risksReducer96, wcgwRelatedRisksReducer as wcgwRelatedRisksReducer96 } from "./Risk/riskreducer.js";
import default97 from "./RiskFactor/riskFactorReducer.js";
import default98 from "./RiskType/riskTypeReducer.js";
import default99 from "./SampleItemDocuments/sampleItemDocumentsReducer.js";
import default100 from "./SampleItems/sampleItemReducer.js";
import default101 from "./SampleList/sampleListReducer.js";
import { scotReducer as scotReducer102, scotsByIdDetailsReducer as scotsByIdDetailsReducer102, scotsDetailsReducer as scotsDetailsReducer102, scotsReducer as scotsReducer102 } from "./Scots/scotsreducer.js";
import default103 from "./Scots/wcgwTaskScotsreducer.js";
import { sectionRelatedObjectsReducer as sectionRelatedObjectsReducer104 } from "./SectionRelatedObjects/sectionrelatedobjectsreducer.js";
import default105 from "./signoffsreducer.js";
import { clearRightSideSnapshotViewReducer as clearRightSideSnapshotViewReducer106, formPrintReducer as formPrintReducer106, showHideLeftSideSnapshotViewReducer as showHideLeftSideSnapshotViewReducer106, showHideRightSideSnapshotViewReducer as showHideRightSideSnapshotViewReducer106, snapshotCompareDetailsReducer as snapshotCompareDetailsReducer106, snapshotDetailsReducer as snapshotDetailsReducer106 } from "./Snapshot/snapshotdetailsreducer.js";
import default107 from "./Snapshot/snapshotsreducer.js";
import { standaloneViewReducer as standaloneViewReducer108 } from "./standaloneViewReducer.js";
import default109 from "./Tags/tagsreducer.js";
import { taskGroupReducer as taskGroupReducer110 } from "./Task/taskgroupreducer.js";
import { buildTasksReducer as buildTasksReducer111, taskListCountReducer as taskListCountReducer111, taskListReducer as taskListReducer111, taskListReducerForWCGW as taskListReducerForWCGW111 } from "./Task/tasklistreducer.js";
import { timephaseReducer as timephaseReducer112 } from "./Timephase/timephasereducer.js";
import { setFormV2ToasterVisibilityReducer as setFormV2ToasterVisibilityReducer113 } from "./toastreducer.js";
import default114 from "./TopHeader/topHeaderReducer.js";
import { trialBalanceByIdReducer as trialBalanceByIdReducer115, trialBalancesReducer as trialBalancesReducer115 } from "./TrialBalance/trialbalancereducer.js";
import default116 from "./User/userreducer.js";
import { internalUserReducer as internalUserReducer117, userPreferencesReducer as userPreferencesReducer117 } from "./userpreferences/userpreferencesreducer.js";
import default118 from "./UserPresence/userpresencereducer.js";
import default119 from "./UserSearch/usersearchreducer.js";
import default120 from "./UserSearch/usersearchreducerGA.js";
import { validationCountsReducer as validationCountsReducer121, validationDetailsReducer as validationDetailsReducer121, withoutApprovalvalidationCountsReducer as withoutApprovalvalidationCountsReducer121 } from "./ValidationControl/validationcontrolreducer.js";
import default122 from "./versionreducer.js";
import { wcgwReducer as wcgwReducer123, wcgwsReducer as wcgwsReducer123 } from "./WCGW/wcgwreducer.js";
import default124 from "./WorkspaceEngagement/workspaceengagementsreducer.js";
export default [
[accountAssertionsReducer1, ["GET_ASSERTIONS_FOR_ACCOUNTID"]],
[accountDetailsReducer1, ["GET_REVIEWS_FOR_ACCOUNTID"]],
[accountReducer1, ["CREATE_MANAGE_ACCOUNT","CREATE_ACCOUNT","GET_ACCOUNT_BY_ID","CLEAN_ACCOUNT","UPDATE_ACCOUNT","UPDATE_MANAGE_ACCOUNT","UPDATE_STORE_ACCOUNT_ASSERTION","DELETE_ACCOUNT","GET_TOP_ENTITY_METADATA","PATCH_ACCOUNT_ATTRIBUTES"]],
[accountsEditReducer1, ["GET_ACCOUNTS_EDIT"]],
[accountsReducer1, ["GET_ACCOUNTS","DELETE_ACCOUNT","CREATE_MANAGE_ACCOUNT","CREATE_ACCOUNT","UPDATE_ACCOUNT","UPDATE_MANAGE_ACCOUNT","GET_ACCOUNT_BY_ID","SAVE_BATCH_HELIX_CANVAS_ACCOUNT_RELATIONS","SAVE_SENT_ASSOCIATED_HELIX_ACCOUNTS","DELETE_DOCUMENT_BY_ID","RELATE_ENTITY_ACCOUNT","UNRELATE_ENTITY_ACCOUNT","CREATE_ENTITY_FORM","GET_TOP_ENTITY_METADATA","RELATE_ACCOUNT_TO_SCOT","UNRELATE_ACCOUNT_TO_SCOT"]],
[accountsWithCombinedScotsAndRisksReducer1, ["GET_ACCOUNTS"]],
[addAccountReviewReducer1, ["ADD_REVIEWS_FOR_ACCOUNTID"]],
[areAllCanvasAccountsMappedToHelixAccountsReducer1, ["GET_IS_HELIX_CANVAS_ACCOUNT_MAPPINGS_SUCCESSFULL"]],
[currentAccountReducerSEM1, ["GET_ASSERTIONS_FOR_ACCOUNTID_SEM"]],
[removeAccountReviewReducer1, ["DELETE_REVIEWS_FOR_ACCOUNTID"]],
[manageAccountReducer2, ["DELETE_MANAGE_ACCOUNT","CREATE_MANAGE_ACCOUNT","UPDATE_MANAGE_ACCOUNT","GET_MANAGE_ACCOUNT_BY_ID","CLEAN_MANAGE_ACCOUNT"]],
[manageAccountsReducer2, ["GET_MANAGE_ACCOUNTS","GET_MANAGE_ACCOUNT_BY_ID","CREATE_MANAGE_ACCOUNT","UPDATE_MANAGE_ACCOUNT","DELETE_MANAGE_ACCOUNT"]],
[default3, ["SET_ACTIVE_ANNOTATION","UNSET_ACTIVE_ANNOTATION"]],
[annotationsReducer4, ["GET_TRIAL_BALANCE_BY_ID","CREATE_ENTITY_ANNOTATION","UPDATE_ENTITY_ANNOTATION","DELETE_ENTITY_ANNOTATION"]],
[default5, ["UPDATE_APP_ONLINE_STATE"]],
[aresCompareBodiesReducer6, ["GET_COMPARE_BODIES_BY_SECTION_ID"]],
[aresFormDataReducer7, ["GET_FORM_BY_SNAPSHOT_ID"]],
[aresFormDataByBodyTypeIdReducer8, ["GET_FORM_BODIES_BY_BODYTYPEID"]],
[aresFormHeadersReducer9, ["GET_ALL_TASKS_OVERVIEW","GET_FORM_DATA","ARES_SET_SELECTED_HEADER","ARES_CLEAR_FORM_HEADERS"]],
[aresLinkedDocumentsReducer10, ["GET_LINKED_DOCUMENTS"]],
[entityLayersReducer11, ["GET_ENTITY_LAYERS","CLEAR_ENTITY_LAYERS"]],
[multiEntityReducer11, ["GET_MULTI_ENTITY_BY_ID","CLEAR_MULTI_ENTITY"]],
[stEntitiesReducer11, ["GET_STENTITY_BY_ID","CREATE_STENTITY","UPDATE_STENTITY","GET_ALL_MULTI_ENTITY","GET_ALL_ENGAGEMENT_ENTITIES","ARES_GET_DOCUMENT_BY_ID","GET_ALL_DOCUMENTS","DELETE_MULTI_ENTITY","GET_MULTI_ENTITY_BY_ID"]],
[aresMyRiskFactorDescriptionReducer12, ["GET_FORM_BODIES_BY_SECTIONID","GET_RISKS_FACTORS","RELATE_RISK_FACTOR_TO_DESCRIPTION","UNRELATE_RISK_FACTOR_FROM_DESCRIPTION","EDIT_RISK_FACTOR_DESCRIPTION","CREATE_RISK_FACTOR_RELATION","UPDATE_RISK_FACTOR_RELATION_DESCRIPTION","REMOVE_RISK_FACTOR_RELATION"]],
[aresSnapshotsReducer13, ["GET_FORM_SNAPSHOTS"]],
[stEntityIdReducer14, ["SET_SELECTED_STENTITY_ID","CLEAR_SELECTED_STENTITY_ID"]],
[aresTaskDocumentsReducer15, ["GET_ALL_TASKS_OVERVIEW","GET_ALL_DOCUMENTS","ARES_GET_DOCUMENT_BY_ID","GET_FORM_DATA","UPDATE_STORYBOOK","ARES_UPDATE_TASK_DOCUMENT_ON_SIGN_OFF","ARES_UPDATE_TASK_DOCUMENT_ON_SIGN_OFF_REMOVAL","ARES_UPDATE_TASK_DOCUMENT_ON_CONFIRM_CHANGE_REASON","KRONOS_DOCHELPER_UPLOAD_DOCUMENT","SET_IS_SHARED","UPDATE_DOCUMENT_STATUS","SET_UNLINK_INITIATED","ADD_APPROVAL","DELETE_APPROVAL","DELETE_SECTION","DELETE_HEADER","EDIT_HEADER","CREATE_CUSTOM","header","section","CREATE_DUPLICATE_SECTION","GET_LINKED_DOCUMENTS","GET_DOCUMENT_SIGN_OFFS"]],
[navigationPanelReducer15, ["SET_LEFT_NAVIGATION_ACTIVE_PANEL"]],
[aresTaskDetailsReducer16, ["ARES_GET_TASK_BY_ID","GET_TASK_ASSIGNMENTS"]],
[aresTaskListReducer17, ["GET_ALL_TASKS_BY_GROUP_ID","GET_ALL_DOCUMENTS","GET_ALL_TASKS_OVERVIEW","ARES_GET_DOCUMENT_BY_ID","GET_VALIDATIONCOUNTS","GET_TASK_ASSIGNMENTS","ARES_GET_TASK_BY_ID","HANDOFF_TASK"]],
[default18, ["GET_TASK_DOCUMENTS","UPDATE_DOCUMENT_ON_ADDING_SIGNOFF","UPDATE_DOCUMENT_ON_SIGNOFF_REMOVAL"]],
[default19, ["GET_ACCOUNTS"]],
[default20, ["GET_ASSERTIONS_FOR_ACCOUNTID"]],
[assertionsReducer21, ["GET_ASSERTIONS_FOR_RISKID"]],
[engagementAssertionsReducer21, ["GET_ASSERTIONS_FOR_ENGAGEMENTID"]],
[default22, ["GET_ALL_ASSOCIATED_RISKS_SUMMARY"]],
[controlAttributesReducer23, ["CREATE_CONTROL_ATTRIBUTES","GET_CONTROL_ATTRIBUTES","UPDATE_CONTROL_ATTRIBUTES","DELETE_CONTROL_ATTRIBUTES"]],
[default24, ["GET_ALL_SCOTS","GET_ENGAGEMENT_ALL_SCOTS","GET_ALL_ITPROCESSES","GET_ITPROCESS_BY_ID","REFRESH_UNRELATED_ITCONTROLS","BODIES_SPECIAL_CLIENTSIDE_FILTERS","GET_IT_APPLICATION","GET_ITAPPSO_BY_ID","GET_ALL_IT_APPLICATIONS","DELETE_ITAPPLICATION","DELETE_ITAPPLICATION_ITPROCESS_RELATION","DELETE_ITPROCESS","DELETE_ITCONTROL","DELETE_ITRISK","DELETE_RISK","GET_ENGAGEMENT_ALL_ITCONTROLS","GET_ALL_ITCONTROLS","GET_ITCONTROL_BY_ID","RELATE_ITRISK_ITCONTROL","UNRELATE_ITRISK_ITCONTROL","GET_ALL_TASKS","ADD_RELATED_TASK","GET_RISKS_FACTORS","UPDATE_RISK_FACTOR_RELATION","CREATE_RISK","UPDATE_RISK","PUT_WCGW","GET_RISK_TYPES","GET_RISKS","RELATE_TASK_ITPROCESS","UNRELATE_TASK_ITPROCESS","CREATE_ITAPPLICATION_ITPROCESS_RELATIONSHIP","CREATE_ITPROCESS_SUPPORTING_ITAPPLICATION_RELATIONSHIP","DELETE_ITPROCESS_SUPPORTING_ITAPPLICATION_RELATIONSHIP","IT_PROCESS_SELECTED","IT_APPLICATION_SELECTED","CREATE_ITPROCESS","UPDATE_ITPROCESS","CREATE_ITRISK","CREATE_ITAPPLICATION_SO","UPDATE_ITRISK","CREATE_ITCONTROL","UPDATE_ITCONTROL","UPDATE_SCOT","UPDATE_CONTROL","UPDATE_ITAPPLICATION","GET_ALL_ITPROCESSES_WITHOUT_PAGINATION","GET_FORM_BODIES_BY_SECTIONID","UPDATE_FORM_BODY_RELATEOBJECTS_AFTER_RESPONSE","GET_CONTROL_BY_ID","GET_ALL_CRA_ACCOUNTS","GET_ALL_IT_APPS","DELETE_IT_APPS_BY_ID","UPDATE_MANAGE_ACCOUNT","UPDATE_ASSERTION_IR_CR","PATCH_ACCOUNT_RATIONALE"]],
[default25, ["ADD_CAPTURE_IMAGE","SET_CAPTURE_ANNOTATIONS","REQUEST_CAPTURE_IMAGE_METADATA","UPLOAD_CAPTURE_IMAGE","COMPLETE_CAPTURE_IMAGE_UPLOAD","SET_CAPTURE_ERROR","SET_CAPTURE_ANNOTATION_BOX"]],
[default26, ["GET_CONFIGURATIONS"]],
[default27, ["SET_CONNECTIVITY_STATE"]],
[contentUpdatesReducer28, ["GET_CONTENT_UPDATES"]],
[controlsDetailsReducer29, ["GET_CONTROL_BY_ID","GET_ALL_CONTROLS"]],
[default30, ["SAVE_UPLOAD_DOCUMENT_ID","CLEAR_UPLOAD_DOCUMENT_ID","SAVE_UPLOAD_DOCUMENTS","UPDATE_UPLOAD_DOCUMENT","CLEAR_UPLOAD_DOCUMENTS","REMOVE_TASK_DOCUMENT","REMOVE_UPLOAD_DOCUMENT"]],
[default31, ["KRONOS_DOCUMENT_DOWNLOAD_PROGRESS","KRONOS_DOCUMENT_DOWNLOAD_COMPLETE","KRONOS_DOCUMENT_UPLOAD_PROGRESS","KRONOS_DOCUMENT_UPLOAD_COMPLETE","KRONOS_DOCHELPER_DOWNLOAD_DOCUMENT","KRONOS_DOCHELPER_UPLOAD_DOCUMENT","DOCHELPER_ERROR","DOCUMENT_UPLOAD_ERROR","ASSOCIATE_TASK_DOCUMENTS","REMOVE_TASK_DOCUMENT","KRONOS_DOCHELPER_UPLOAD_COMPLETE"]],
[default32, ["DOCHELPER_ERROR"]],
[default33, ["GET_ALL_DOCUMENT_EXTENSION"]],
[documentsReducer34, ["GET_ALL_DOCUMENTS"]],
[setDocumentIdReducer34, ["SET_DOCUMENT_ID"]],
[default35, ["GET_ALL_DOCUMENT_TYPES"]],
[default36, ["ADD_DUPLICATE_DOCUMENT","CLEAR_DUPLICATE_DOCUMENTS","SELECT_DOCUMENT_OVERWRITE"]],
[default37, ["GET_ENTITY_DOCUMENTS","UPDATE_DOCUMENT","DELETE_DOCUMENT_BY_ID","SET_ENTITY_DOCUMENT_NAME","ADD_ENTITY_DOCUMENT_SIGNOFF","REMOVE_ENTITY_DOCUMENT_SIGNOFF","CREATE_ENTITY_FORM","UPDATE_ENTITY_DOCUMENT_RELATE","DELETE_ENTITY_DOCUMENT_RELATE","POST_RNH_NEW_NOTE","UPDATE_RNH_NOTE_DETAIL","CLEAR_ENTITY_DOCUMENTS"]],
[default38, ["KRONOS_DOCHELPER_DOWNLOAD_DOCUMENT","SET_CHANGE_REASON_REQUIRED","SHOW_CHANGE_REASON_MODAL","DOCHELPER_UPLOAD_DOCUMENT","DOCUMENT_UPLOAD_PROGRESS","DOCUMENT_UPLOAD_COMPLETE","DOCUMENT_DOWNLOAD_PROGRESS","DOCUMENT_DOWNLOAD_COMPLETE","API_ERROR"]],
[default39, ["GET_ALL_KNOWLEDGE_FORM"]],
[default40, ["GET_LIMITED_RISK_DOCUMENTS","UPDATE_DOCUMENT","ADD_LIMITED_RISK_DOCUMENT_SIGNOFF","REMOVE_LIMITED_RISK_DOCUMENT_SIGNOFF"]],
[default41, ["KRONOS_UPCHUNK_CREATE_DOCUMENT_METADATA","KRONOS_UPCHUNK_DOCUMENT_UPLOAD_COMPLETE","KRONOS_UPCHUNK_DOCUMENT_UPLOAD_PROGRESS","KRONOS_UPCHUNK_UPLOAD_COMPLETE","KRONOS_UPCHUNK_DOCUMENT_UPLOAD_TASKS_COMPLETE","KRONOS_DOCUMENT_ERRORS","KRONOS_UPCHUNK_DOCUMENT_UPLOAD_CANCELLED","KRONOS_UPCHUNK_UPLOAD_ERROR","REMOVE_TASK_DOCUMENT"]],
[documentLastModifiedDateReducer42, ["UPDATE_DOCUMENT_LAST_MODIFIED_DATE"]],
[default43, ["SET_ENGAGEMENT_COMMENTS_CONFIG_DATA","CLEAR_ENGAGEMENT_COMMENTS","SET_SELECTED_ENGAGEMENT_COMMENT_GUID","GET_ALL_ENGAGEMENT_COMMENTS","SET_ENGAGEMENT_COMMENTS_FILTER","REMOVE_ENGAGEMENT_COMMENTS_FILTER","DISPLAY_ENGAGEMENT_COMMENT_INPUT_MODAL","CREATE_ENGAGEMENT_COMMENT","CREATE_ENGAGEMENT_COMMENT_REPLY","UPDATE_ENGAGEMENT_COMMENT","UPDATE_ENGAGEMENT_COMMENT_STATUS","DELETE_ENGAGEMENT_COMMENT_PARENT","DELETE_ENGAGEMENT_COMMENT_CHILD","UPDATE_COMMENT_COUNT_AFTER_CLOSING_COMMENTS","ADD_TO_ENGAGEMENT_COMMENT_SLICE"]],
[countryStatusReducer44, ["GET_COUNTRY_STATUS"]],
[engagementMetaDataReducer44, ["LOAD_ENGAGEMENTMETADATA"]],
[engagementReducer44, ["GET_ENGAGEMENT","UPDATE_ENGAGEMENT"]],
[engagementTeamMembers44, ["GET_ENGAGEMENT_TEAM_MEMBERS"]],
[engagementUserRolesReducer44, ["GET_ALL_ENGAGEMENT_USER_ROLES"]],
[engagementUsersWithRoles44, ["GET_ENGAGEMENT_USERS_WITHROLES"]],
[getCountriesReducer44, ["GET_COUNTRIES"]],
[getLanguagesReducer44, ["GET_LANGUAGES"]],
[paansReducer44, ["CHECK_USER_ACCEPTED_PAANS_POLICY"]],
[rbacUserRolesReducer44, ["GET_ALL_RBAC_USER_ROLES"]],
[setEngagementIdReducer44, ["SET_ENGAGEMENT_ID"]],
[setHasBodyIdFilterReducer44, ["SET_HAS_BODY_ID_FILTER"]],
[setIsAresViewReducer44, ["SET_IS_ARESVIEW"]],
[setIsCompareResponsesViewReducer44, ["SET_IS_COMPARE_RESPONSES_VIEW"]],
[setIsDrawerModeReducer44, ["SET_IS_DRAWER_MODE"]],
[setIsGuidedWorkFlowV2Reducer44, ["SET_IS_GUIDEDWORKFLOWV2"]],
[setIsTrackChangesViewReducer44, ["SET_IS_TRACK_CHANGES_VIEW"]],
[setNavigationFilterReducer44, ["SET_NAVIGATION_FILTER"]],
[setServerPrintDataReducer44, ["SET_SERVER_PRINT_DATA"]],
[workspaceReducer44, ["GET_WORKSPACE_METADATA"]],
[quickLinkReducer45, ["GET_QUICKLINKS_BY_ENGAGEMENTID"]],
[entityFormsReducers46, ["CREATE_ENTITY_FORM","DELETE_DOCUMENT_BY_ID","UPDATE_DOCUMENT"]],
[default47, ["API_ERRORS","CLIENT_VALIDATION_ERROR","SUCCESS_MESSAGE","WARNING_MESSAGE","INFO_MESSAGE","CLEAR_ERRORS","DOCHELPER_ERROR","KRONOS_DOCHELPER_ERROR","KRONOS_UPCHUNK_UPLOAD_ERROR","PreConditionFailed","RequestEntityTooLarge","Unauthorized","LengthRequired","Forbidden","INVALID_RELATED_ENTITY"]],
[estimatesReducer48, ["GET_ESTIMATES","GET_ESTIMATE_BY_ID","DELETE_ESTIMATE_DOCUMENT_BY_ID"]],
[default49, ["GET_AICHATBOT_INTEGRATION_PARAMETERS"]],
[default50, ["GET_FILTER_ENTITIES"]],
[default51, ["SET_FILTERS","CLEAR_FILTERS","SET_FILTER"]],
[financialStatementReducer52, ["GET_TRIAL_BALANCE_BY_ID","OPEN_ANNOTATION_MODAL","CLEAR_ANNOTATION_MODAL","CREATE_ENTITY_ANNOTATION","DELETE_ENTITY_ANNOTATION"]],
[flowChartReducer53, ["GET_FLOWCHART","CLEAR_FLOWCHART","SAVE_FLOWCHART_CONNECTION","DELETE_FLOWCHART_CONNECTION","CREATE_FLOWCHART_NEW_STEP","SET_FLOWCHART_DOCUMENT_UPLOAD"]],
[flowChartsReducer53, ["GET_FLOWCHARTS"]],
[flowchartstepEditModalReducer54, ["SET_IS_FLOWCHART_STEP_EDIT_MODAL_OPEN","SET_EDIT_FLOWCHART_STEP_ID"]],
[flowchartstepReducer54, ["GET_FLOWCHART_STEP_BY_ID","EDIT_FLOWCHART_STEP","DELETE_FLOWCHART_STEP","RELATE_WCGW_TO_FLOWCHART_STEP","RELATE_ITAPP_TO_FLOWCHART_STEP","UNRELATE_IT_APPLICATION_FROM_FLOWCHART_STEP","RELATE_CONTROL_TO_FLOWCHART_STEP","RELATE_DOCUMENT_TO_FLOWCHART_STEP","UNRELATE_WCGW_FROM_FLOWCHART_STEP","UNRELATE_CONTROL_FROM_FLOWCHART_STEP","UNRELATE_DOCUMENT_TO_FLOWCHART_STEP","UPDATE_STORE_UNRELATED_WCGW_FROM_FLOWCHARTSTEPWCGW","UPDATE_STORE_UNRELATED_CONTROL_FROM_FLOWCHARTSTEP_CONTROL","UPDATE_STORE_RELATED_FLOWCHARTSTEPWCGW","UPDATE_STORE_RELATED_FLOWCHARTSTEP_CONTROL","UPDATE_STORE_RELATED_FLOWCHARTSTEP_IT_APPLICATION_SO","UPDATE_STORE_UNRELATED_IT_APPLICATION_FROM_FLOWCHARTSTEP_IT_APPLICATION_SO","CLEAR_FLOWCHART_STEP"]],
[default55, ["GET_ENGAGEMENT_FORM_ACTIONS","CLEAN_COMPONENTS_AND_FORMACTIONS"]],
[default56, ["SET_FORM_BODY_SAVE_STATE","CLEAR_FORM_BODY_SAVE_STATE","CLEAR_FORM_BODY_SAVE_STATE_BY_BODY_ID","RETURN_TO_EDITOR_ON_CONFLICT"]],
[default57, ["POST_FORM"]],
[default58, ["GET_ALL_RELATED_DOCUMENTS_FOR_BODIES","GET_RELATED_DOCUMENT_FOR_BODY","RESET_BODY_RELATED_DOCUMENTS","DELETE_DOCUMENT_BY_ID","ASSOCIATE_TASK_DOCUMENTS","UPDATE_DOCUMENT","ADD_UPLOADING_DOCUMENT","DOCHELPER_ERROR","POST_RNH_NEW_NOTE","UPDATE_RNH_NOTE_DETAIL","KRONOS_DOCUMENT_UPLOAD_COMPLETE","REMOVE_TASK_DOCUMENT","UPDATE_DOCUMENT_ON_ADDING_SIGNOFF","UPDATE_DOCUMENT_ON_SIGNOFF_REMOVAL","UNLINK_SHARED_DOCUMENT","CHECK_IN_CHECK_OUT_DOCUMENT"]],
[IndividualIndependenceDocumentReducer59, ["CREATE_INDEPENDENCE_FORM"]],
[SubmitIndependenceActionReducer59, ["SUBMIT_INDEPENDENCE_ACTION"]],
[SubmitIndependenceDataReducer59, ["SUBMIT_INDEPENDENCE"]],
[SubmitProfileDataReducer59, ["SUBMIT_PROFILE","CLEAR_PROFILE_DATA"]],
[formBodyDataReducer59, ["CREATE_RELATED_DOCUMENT","evidenceName","priorPeriodName","DELETE_DOCUMENT_BY_ID","evidenceName","priorPeriodName","temporaryName","GET_ALL_RELATED_DOCUMENTS_FOR_BODIES","evidence","canvasFormBodyEvidence","priorPeriod","temporary","external","GET_FORM_BODIES_BY_SECTIONID","ADD_CUSTOM_BODY","GET_FORM_BODIES_AFTER_RESPONSE","UPDATE_FORM_BODY_RELATEOBJECTS_AFTER_RESPONSE","DELETE_FORM_BODY","EDIT_BODY","UPDATE_COMMENT_COUNT_AFTER_CLOSING_COMMENTS","UPDATE_BODY_COMMENT_COUNT_AFTER_CLOSING_COMMENTS","UPDATE_BODY_RELATED_COUNT","UPDATE_RISK_FACTOR_RELATION_DESCRIPTION","CREATE_RISK_FACTOR_RELATION","REMOVE_RISK_FACTOR_RELATION","CREATE_RISK_FORMBODYOPTION","DELETE_RISK_FORMBODYOPTION","UPDATE_FORM_BODY_DATA","UPDATE_RICH_TEXT_BODY","UPDATE_FORMBODY_RISKFACTORS","UPDATE_DOCUMENT_COUNT_ON_TEMPORARY_FILE_UPLOAD","temporaryName","GET_BODY_GUIDANCES"]],
[formBodyDisplayDataForUpdateReducer59, ["UPDATE_FORM_BODY_RESPONSE_FOR_UPDATE","UPDATE_BODY_FREE_TEXT_IN_FORM_BODY_DISPLAY_DATA_FOR_UPDATING"]],
[formBodyDisplayDataNoStoreUpdateReducer59, ["UPDATE_FORM_BODY_RESPONSE_NO_STORE_UPDATE"]],
[formBodyDisplayDataReducer59, ["UPDATE_FORM_BODY_RESPONSE","UPDATE_FORM_BODY_DISPLAY_DATA","UPDATE_BODY_FREE_TEXT","UPDATE_RICH_TEXT_BODY","UPDATE_BODY_ON_CONFLICT","CLEAR_FORMBODYDISPLAYDATA"]],
[formOverwriteDataReducer59, ["OVERWRITE_FORM"]],
[formProfileDataReducer59, ["GET_FORM_PROFILE_DATA_BY_ID"]],
[formProfileUpdateDataReducer59, ["UPDATE_FORM_PROFILE_RESPONSE"]],
[default60, ["GET_FORM_USERS"]],
[default61, ["GET_ASSIGNED_SCOPES","DELETE_SUB_SCOPE"]],
[default62, ["GET_ALL_ACCOUNTS","SET_ACCOUNTS_PARAMETERS","POST_GACOMPONENT_ACCOUNT","DELETE_GACOMPONENT_ACCOUNT"]],
[default63, ["GET_ALL_COMPONENTS","POST_NEW_COMPONENT","PUT_COMPONENT","DELETE_COMPONENT","SEND_LINK_REQUEST","POST_SEND_INSTRUCTIONS","CREATE_ENTITY_FORM","CLEAN_COMPONENTS_AND_FORMACTIONS"]],
[default64, ["GET_INSTRUCTIONS","POST_INSTRUCTIONS","PATCH_SECTIONS","DELETE_DOCUMENT_BY_ID","UPDATE_INSTRUCTIONS","DELETE_INSTRUCTION","POST_NEW_SECTION","DELETE_CUSTOM_SECTION","GET_ALL_RELATED_DOCUMENTS_FOR_BODIES"]],
[default65, ["GET_ALL_SCOPES","POST_SUB_SCOPE","PATCH_SUB_SCOPE","DELETE_SUB_SCOPE"]],
[default66, ["GET_GROUP_SNAPSHOT_BY_ID","CLEAN_GROUP_SNAPSHOT"]],
[default67, ["GET_ALL_SNAPSHOTS","POST_SNAPSHOTS"]],
[default68, ["GET_ALL_ACCOUNTS","SET_ACCOUNTS_PARAMETERS","POST_GACOMPONENT_ACCOUNT","DELETE_GACOMPONENT_ACCOUNT","POST_NEW_COMPONENT","SEND_LINK_REQUEST","GET_ALL_COMPONENTS","DELETE_COMPONENT","PUT_COMPONENT","POST_SEND_INSTRUCTIONS","CREATE_ENTITY_FORM","CLEAN_COMPONENTS_AND_FORMACTIONS","GET_ALL_SCOPES","POST_SUB_SCOPE","PATCH_SUB_SCOPE","DELETE_SUB_SCOPE","GET_ASSIGNED_SCOPES","DELETE_DOCUMENT_BY_ID","PATCH_SECTIONS","POST_NEW_SECTION","DELETE_CUSTOM_SECTION","POST_INSTRUCTIONS","UPDATE_INSTRUCTIONS","DELETE_INSTRUCTION","GET_ALL_RELATED_DOCUMENTS_FOR_BODIES","GET_INSTRUCTIONS","GET_GROUP_SNAPSHOT_BY_ID","CLEAN_GROUP_SNAPSHOT","POST_SNAPSHOTS","GET_ALL_SNAPSHOTS","SET_GROUP_AUDIT_DOCUMENT_UPLOAD"]],
[default69, ["GET_AVAILABLE_PROJECT_FROM_CANAVS_ENGAGEMENT"]],
[default70, ["GET_ENGAGEMENT_HELIX_SUMMARY"]],
[default71, ["GET_HELIX_PROJECT"]],
[default72, ["SIMPLE_HELIX_BOUNDING_BOXES_SHOW","SET_SIMPLE_HELIX_LOADING_STATE","GET_ALL_SIMPLE_HELIX_DOCUMENTS","GET_SIMPLE_HELIX_DOCUMENT_BLOB","GET_CAPTURE_ANNOTATIONS","UPLOAD_CAPTURE_IMAGE","COMPLETE_CAPTURE_IMAGE_UPLOAD","CLEAR_UPLOADED_SCREENSHOT_DOCUMENT_ID","SET_ANNOTATION_MODE","ADD_CAPTURE_ANNOTATION","GET_ALL_SIMPLE_HELIX_NOTES","REMOVE_NEW_ANNOTATION","NEW_ANNOTATION_SAVE","ADD_BOUNDINGBOX_ANNOTATION","SET_NEW_ANNOTATION_BOUNDING_BOX","SET_REFERENCE_NUMBER_NEW_ANNOTATION","EDIT_ANNOTATION","EDIT_BOUNDINGBOX","CANCEL_BOUNDING_BOX","UPDATE_DOCUMENT"]],
[default73, ["GET_INTERNAL_USERS"]],
[itApplicationDataReducer74, ["GET_IT_APPLICATION","UPDATE_ITAPPLICATION","DELETE_ITAPPLICATION","GET_CONTROL_BY_ID","CREATE_ITAPPLICATION_SO"]],
[itApplicationDetailsReducer74, ["GET_ITAPPSO_BY_ID"]],
[itApplicationReducer74, ["UPDATE_ITAPPLICATION"]],
[itcontrolDetailsReducer75, ["GET_ITCONTROL_BY_ID"]],
[default76, ["GET_TECHNOLOGIES","GET_SUPPORTING_IT","RELATE_SUPPORTING_IT","UNRELATE_SUPPORTING_IT","GET_IT_PLANNING_MODAL_DATA","GET_IT_RISK_FACTORS","REJECT_RISK_FACTOR","ACCEPT_RISK_FACTOR","GET_RELATED_IT_PROCESSES"]],
[itProcessReducer77, ["UPDATE_ITPROCESS","GET_ALL_ITPROCESSES","DELETE_ITPROCESS"]],
[itRiskReducer78, ["UPDATE_ITRISK","GET_ALL_ITRISKS"]],
[default79, ["GET_JOURNAL_SOURCE","UPDATE_JOURNAL_SOURCE","UPDATE_JOURNAL_SOURCE_RELATED_SCOT","UPDATE_JOURNAL_SOURCE_CUSTOM_NAME","POST_SCOT_JOURNAL_SOURCE","DELETE_SCOT_JOURNAL_SOURCE"]],
[default80, ["IS_LOADING"]],
[default81, ["GET_All_SCOTS","GET_ALL_ITSOAPPLICATIONS"]],
[default82, ["RESET_BULK_MARK_COMPLETE","MARK_COMPLETE_GET_LINKED_FORMS","GET_TASK_DOCUMENTS","MARK_COMPLETE_GET_DOCUMENTS","ARES_GET_DOCUMENT_BY_ID","MARK_COMPLETE_GET_DOCUMENT_BY_ID","ADD_APPROVAL","DELETE_APPROVAL","HANDOFF_TASK"]],
[default83, ["GET_MATERIALITY","UPDATE_MATERIALITY","CREATE_MATERIALITY","UPDATE_MATERIALITY_ADJUSTED_BASIS","UPDATE_MATERIALITY_STATE_BY_ID"]],
[default84, ["SET_IS_MEDIA_PLAYING"]],
[adcModalOpenStateReducer85, ["SET_IS_ADC_MODAL_OPEN"]],
[modalLevelStateReducer85, ["SET_MODAL_LEVEL"]],
[modalOpenStateReducer85, ["SET_IS_MODAL_OPEN"]],
[default86, ["GET_RNH_NOTE_DETAIL","OPEN_RNH_NOTE_DETAIL_MODAL","CLEAR_RNH_NOTE_DETAIL","SET_RNH_SELECTED_NOTE_ID_RESET_CONTEXT","GET_RNH_NOTE_REPLIES","POST_RNH_NEW_NOTE","POST_RNH_NOTE_REPLY","UPDATE_RNH_NOTE_INFO","UPDATE_RNH_NOTE_DETAIL","DELETE_RNH_NOTE_DETAIL","DELETE_RNH_PARENT_NOTE","GET_FORM_BODY_COMMENT","ARES_GET_DOCUMENT_BY_ID"]],
[default87, ["SET_OFFLINE_PREP"]],
[default88, ["GET_OVERSIGHT_PROJECTS_BY_ENGAGEMENT","GET_OVERSIGHT_OTHER_PROJECTS_BY_ENGAGEMENT","CREATE_GROUP_OVERSIGHT_PROJECT"]],
[default89, ["GET_AVAILABLE_PSP_INDEXES"]],
[default90, ["GET_ALL_RELATED_DOCUMENTS","GET_ALL_RELATED_DOCUMENTS_FOR_BODIES","DELETE_DOCUMENT_BY_ID","UPDATE_DOCUMENT","POST_RNH_NEW_NOTE","UPDATE_RNH_NOTE_DETAIL","CREATE_RELATED_DOCUMENT","DELETE_RELATED_DOCUMENT","CHECK_IN_CHECK_OUT_DOCUMENT"]],
[relatedObjectsReducer91, ["GET_RELATED_OBJECTS","DELETE_RELATED_OBJECTS","CREATE_RELATED_OBJECTS","UPDATE_RELATEOBJECTS_AFTER_RESPONSE"]],
[default92, ["GET_DOCUMENT_AVAILABLE_TASKS","GET_DOCUMENT_RELATED_TASKS","RELATE_TASK_TO_DOCUMENT","UNRELATE_TASK_FROM_DOCUMENT"]],
[default93, ["GET_REVIEW_NOTES","CLEAR_REVIEW_NOTES","DELETE_NOTE","ADD_NOTE","UPDATE_NOTE_INFO","UPDATE_NOTE_DETAIL","Open","Closed","Cleared","POST_NOTE_REPLY","DELETE_NOTE_DETAIL","EXPAND_COLLAPSE_NOTE"]],
[default94, ["GET_ACCOUNTS"]],
[default95, ["GET_RELATED_WCGWS"]],
[newRiskReducer96, ["CREATE_RISK","CLEAR_NEW_RISK"]],
[relateRiskReducer96, ["GET_RISKS_TO_RELATE"]],
[relatedRisksReducer96, ["GET_RELATED_RISKS_FOR_WCGWID"]],
[riskReducer96, ["CREATE_RISK","GET_RISK_BY_ID","GET_TOP_ENTITY_METADATA","CLEAR_RISK","DISPLAY_ESTIMATETYPE_MISMATCH_CLIENT_VALIDATION_MESSAGE","UPDATE_STORE_RELATE_SCOT_TO_RISK","UPDATE_RISK","UPDATE_STORE_UNRELATE_SCOT_TO_RISK","RELATE_ESTIMATE_TO_RISK","UNRELATE_ESTIMATE_TO_RISK","UPDATE_STORE_UNRELATE_ACCOUNT_TO_RISK","PATCH_RISK"]],
[risksReducer96, ["GET_RISKS","GET_RISK_BY_ID","UPDATE_RISK","CREATE_RISK","DELETE_RISK","RELATE_SCOT_TO_RISK","UNRELATE_SCOT_TO_RISK","RELATE_ESTIMATE_TO_RISK","UNRELATE_ESTIMATE_TO_RISK","RELATE_SPECIAL_CIRCUMSTANCE_TO_ROMM","UNRELATE_SPECIAL_CIRCUMSTANCE_TO_ROMM"]],
[wcgwRelatedRisksReducer96, ["GET_RISKS_FOR_WCGWID"]],
[default97, ["GET_RISKS_FACTORS","UPDATE_RISK_FACTOR_RELATION"]],
[default98, ["GET_RISK_TYPES","CREATE_ASSERTION_RISK_RELATION","DELETE_ASSERTION_RISK_RELATION"]],
[default99, ["GET_ALL_RELATED_DOCUMENTS_FOR_SAMPLE","CLEAR_SAMPLE_RELATED_DOCUMENTS","DELETE_DOCUMENT_FROM_SAMPLE_ITEM","RELATE_SAMPLE_ITEM_DOCUMENT","UPDATE_DOCUMENT","DELETE_DOCUMENT_BY_ID","POST_RNH_NEW_NOTE","UPDATE_RNH_NOTE_DETAIL"]],
[default100, ["GET_SAMPLEITEMS_BY_ID","CREATE_SAMPLE","GET_SAMPLE_ITEMS","EDIT_SAMPLE_ITEM","DELETE_SAMPLE_ITEM","CLEAR_SAMPLE_ATTRIBUTES","ADD_SAMPLE_ATTRIBUTES_STATUS","UPDATE_SAMPLE_ATTRIBUTES_STATUS","DELETE_ATTRIBUTE_FROM_SAMPLE_ITEM","RELATE_TAG_SAMPLE_ITEM","UNRELATE_TAG_SAMPLE_ITEM","EDIT_SAMPLE_TAG","CREATE_CONTROL_ATTRIBUTES","DELETE_CONTROL_ATTRIBUTES"]],
[default101, ["GET_ALL_SAMPLE_LIST","CREATE_SAMPLE_LIST","UPDATE_SAMPLE_LIST"]],
[scotReducer102, ["CREATE_SCOT"]],
[scotsByIdDetailsReducer102, ["GET_SCOT_BY_ID_DETAILS"]],
[scotsDetailsReducer102, ["GET_SCOT_BY_ID","CLEAN_SCOT"]],
[scotsReducer102, ["GET_SCOTS_FOR_ASSERTIONID","SAVE_RISK_WCGW_SELECTION","GET_ENGAGEMENT_ALL_SCOTS","CREATE_SCOT","GET_SCOT_BY_ID","UPDATE_SCOT","GET_SCOTS_BY_ENG","DELETE_SCOT"]],
[default103, ["GET_SCOTS_FOR_ASSERTIONIDS"]],
[sectionRelatedObjectsReducer104, ["GET_SECTION_RELATED_OBJECTS","UPDATE_SECTION_RELATEDOBJECTS_AFTER_RESPONSE","DELETE_SECTION_RELATED_OBJECTS","CREATE_SECTION_RELATED_OBJECTS","UPDATE_ITAPPLICATION","UPDATE_CONTROL"]],
[default105, ["SET_APPROVALS","ADD_APPROVAL","CREATE_SNAPSHOT","DELETE_APPROVAL","GET_DOCUMENT_SIGN_OFFS","GET_ALL_DOCUMENTS","MARK_COMPLETE_GET_DOCUMENTS","ARES_GET_DOCUMENT_BY_ID"]],
[clearRightSideSnapshotViewReducer106, ["CLEAR_RIGHT_SIDE_SNAPSHOT_VIEW"]],
[formPrintReducer106, ["GET_SNAPSHOT_BY_ID"]],
[showHideLeftSideSnapshotViewReducer106, ["SHOW_HIDE_LEFT_SIDE_SNAPSHOT_VIEW"]],
[showHideRightSideSnapshotViewReducer106, ["SHOW_HIDE_RIGHT_SIDE_SNAPSHOT_VIEW"]],
[snapshotCompareDetailsReducer106, ["GET_SNAPSHOT_COMPARE_BY_ID","CLEAR_SNAPSHOT_COMPARE_VIEW"]],
[snapshotDetailsReducer106, ["GET_SNAPSHOT_BY_ID"]],
[default107, ["GET_SNAPSHOTS"]],
[standaloneViewReducer108, ["SET_IS_STANDALONE_VIEW"]],
[default109, ["GET_SAMPLE_ITEM_TAGS","GET_SAMPLE_ITEM_TAG_GROUPS","CREATE_TAG","DELETE_SAMPLE_TAG","DELETE_SAMPLE_TAG_GROUP","UPDATE_TAG_GROUP"]],
[taskGroupReducer110, ["GET_TASKGROUP_BY_ID"]],
[buildTasksReducer111, ["GET_ALL_BUILD_TASKS"]],
[taskListCountReducer111, ["GET_ALL_TASKS_COUNT","RESET_TASK_LIST","SELECT_ENGAGEMENT_USERROLE_ITEM"]],
[taskListReducer111, ["GET_ALL_TASKS","UPDATE_COMPLETED_TASK","CLEAR_TASK_LIST","RESET_TASK_LIST","SELECT_ENGAGEMENT_USERROLE_ITEM","PUT_WCGW_TASK","DELETE_WCGW_TASK","UPDATE_DUE_DATE","UPDATE_EP_EQR_SIGNOFF_INFO_TASK_LIST"]],
[taskListReducerForWCGW111, ["GET_ALL_TASKS_FOR_WCGW"]],
[timephaseReducer112, ["GET_ALL_TIMEPHASES"]],
[setFormV2ToasterVisibilityReducer113, ["SET_TOASTER_VISIBILITY"]],
[default114, ["GET_TOP_ENTITY_METADATA","UPDATE_ACCOUNT","UPDATE_MANAGE_ACCOUNT","UPDATE_SCOT"]],
[trialBalanceByIdReducer115, ["GET_TRIAL_BALANCE_BY_ID"]],
[trialBalancesReducer115, ["GET_TRIAL_BALANCE"]],
[default116, ["GET_USER_BY_GUI"]],
[internalUserReducer117, ["GET_INTERNAL_USER_BY_ID"]],
[userPreferencesReducer117, ["GET_USER_PREFERENCES"]],
[default118, ["GET_USER_PRESENCE","RESET_USER_PRESENCE"]],
[default119, ["GET_USER_SEARCH"]],
[default120, ["GET_USER_SEARCH_GA"]],
[validationCountsReducer121, ["UNLOAD_VALIDATION_COUNTS","GET_VALIDATIONCOUNTS","REFRESH_VALIDATION_COUNTS"]],
[validationDetailsReducer121, ["UNLOAD_VALIDATION_DETAILS","GET_VALIDATIONDETAILS"]],
[withoutApprovalvalidationCountsReducer121, ["GET_WITHOUT_APPROVAL_VALIDATION_COUNTS"]],
[default122, ["VERSION_UPDATE"]],
[wcgwReducer123, ["PUT_WCGW"]],
[wcgwsReducer123, ["GET_WCGWS"]],
[default124, ["GET_WORKSPACE_ENGAGEMENTS","CLEAN_WORKSPACE_ENGAGEMENTS"]]
];