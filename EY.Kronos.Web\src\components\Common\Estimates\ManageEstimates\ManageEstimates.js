import React, {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import Button from '../../BaseComponentWrappers/MotifButton';
import {labels} from '../../../../util/utils';
import EntitiesModalWrapper from '../../EntityCrud/EntitiesModalWrapper';
import {Entity} from '../../../../util/uiconstants';
import styled from 'styled-components';
import {getEstimates} from '../../../../actions/Estimates/estimatesactions';
import {useEngagementId} from '../../../../util/customHooks';
import {getRisks, deleteRisk} from '../../../../actions/Risk/riskactions';
import {riskCategory, paging, clientSideEventNames} from '../../../../util/uiconstants';

export default function ManageEstimates(props) {
	const {onClose, isEstimateAssertionRelationRequired} = props;
	const [showEstimatesModal, setShowEstimatesModal] = useState(false);
	const estimates = useSelector((state) => state.risks?.manageEntities);
	const [formattedEstimates, setFormattedEstimates] = useState([]);
	const engagementId = useEngagementId();

	const handlerOnModalOpen = () => {
		setShowEstimatesModal(true);
	};

	const handlerOnModalClose = () => {
		setShowEstimatesModal(false);
		window.dispatchEvent(new CustomEvent(clientSideEventNames.getValidations));
		onClose && onClose();
	};

	useEffect(() => {
		if (estimates && estimates?.data) {
			let formattedEstimates = [];
			estimates.data.map((estimate) => {
				formattedEstimates.push({id: estimate.id, name: estimate.riskName});
			});
			setFormattedEstimates(formattedEstimates);
		}
	}, [estimates]);
	return (
		<StyledManageEstimate className="StyledManageEstimate">
			<Button
				variant="text-alt"
				className="manageEstimate"
				onClick={handlerOnModalOpen}
				showLabel
				isEllipsed
				buttonLabel={labels.manageEstimates}
				noOfLines={1}
			/>
			{showEstimatesModal && (
				<EntitiesModalWrapper
					engagementId={engagementId}
					show={showEstimatesModal}
					entityType={Entity.Estimate}
					setShow={setShowEstimatesModal}
					getAllAction={() => {
						return getRisks(
							engagementId,
							{
								riskCategory: riskCategory.estimates,
								orderBy: 'RiskType'
							},
							{isManageEntities: true}
						);
					}}
					entities={formattedEstimates}
					onClose={handlerOnModalClose}
					deleteAction={(id) => {
						return deleteRisk(engagementId, id, {isManageEntities: true});
					}}
					isEstimateAssertionRelationRequired={isEstimateAssertionRelationRequired}
				/>
			)}
		</StyledManageEstimate>
	);
}

const StyledManageEstimate = styled.section`
	/* margin-bottom: var(--px-25); */
	display: flex;
	justify-content: flex-end;
	.manageEstimate {
		&:hover,
		&:focus {
			box-shadow: none;
		}
	}
`;
