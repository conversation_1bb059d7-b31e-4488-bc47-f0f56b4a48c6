/* eslint-disable prettier/prettier,max-len */

import {
	Entity,
	GALinkStatus,
	confidentialityTypes,
	currencyType,
	gaRegion,
	gaRoleTypes,
	gaScopeType,
	notesFilter,
	pointOfContactTypes,
	validationTypes,
	KnowledgeSectionIds,
	sendInstructionsSwitcherIds,
	accountsFilter,
	scotsFilter,
	rejectionType
} from '../util/uiconstants';

/**
 * Created by calhosh on 4/14/2017.
 * HE resource file
 */
export const labels = {
	addEvidenceBtn: 'הוסף ראיות',
	multipleDocuments: 'מספר מסמכים',
	invalidEngagementId: 'מספר זיהוי תיק הביקורת אינו חוקי. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית (Help Desk) אם השגיאה נמשכת.',
	newComponent: 'ישות מוחזקת חדשה',
	workingOffline: 'עבודה במצב לא מקוון',
	syncInProgress: 'הסנכרון מתבצע',
	prepareOffline: 'הכנת נתונים למצב לא מקווןן',
	connectionAvailable: 'חיבור זמין',
	training: 'הדרכה',
	clickForOptions: 'לחץ לקבלת אפשרויות נוספות',
	navReviewNotes: 'סקירת הערות',
	navHeaderManageTeam: 'ניהול צוות',
	navManageGroup: 'ניהול ביקורת קבוצה',
	manageObjects: 'נהל פריטים',
	navCRASummary: 'סיכום הערכת הסיכון המשולב (CRA)',
	navAuditPlan: 'תוכנית ביקורת',
	navWorkPlan: 'תוכנית עבודה',
	navSEM: 'Substantive Evaluation Matrix',
	navFindings: 'ממצאים',
	navContentUpdates: 'עדכון תוכן',
	navCanvasFormUpdates: 'עדכוני טפסים ב- Canvas',
	navCopyHub: 'Copy hub',
	navCopyHubNew: 'Copy hub - חדש',
	navArchiveChecklist: 'שאלון לביצוע ארכיב',
	navExportHub: 'ייצוא כונן HUB',
	navReporting: 'דוחות',
	navHelp: 'עזרה כללית',
	validationNavHelp: 'עזרה באימות',
	leaveUsFeedback: 'השאר לנו משוב',
	navDashboard: 'Dashboard',
	tasksPage: 'משימות',
	documentsPage: 'מסמכים',
	collaborationPage: 'שיתוף פעולה',
	automationPage: 'אוטומציה',
	documentHelperConnectionIssue: 'זוהתה בעיה ב-EY Canvas Document Helper. לחץ <a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">כאן</a> להנחיות כיצד לפתור בעיה זו.',
	noContentAvailable: 'אין תוכן זמין',
	noSectionsAvailable: 'אין פסקאות זמינות',
	noInformationAvailable: 'אין מידע זמין',
	collapse: 'צמצם',
	expand: 'הרחב',
	duplicate: 'העתק',
	duplicateSection: 'מקטע כפול',
	duplicateSectionHeader: 'האם אתה בטוח שברצונך לשכפל את המקטע הנבחר?',
	deleteSection: 'מחק קטע',
	deleteSectionHeader: 'האם אתה בטוח שברצונך למחוק את המקטע שנבחר?',
	deleteHeader: 'מחק כותרת עליונה',
	deleteHeaderTitle: 'האם אתה בטוח שברצונך למחוק את הכותרת שנבחרה?',
	confirmLabel: 'לְאַשֵׁר',
	custom: 'מותאם אישית',
	selectHeader: 'בחר כותרת',
	selectSection: 'בחר פסקה',
	noResultsFound: 'לא נמצאו תוצאות',
	scot: 'תת התהליך המהותי (SCOT)',
	scotTypes: 'סוג תת התהליך המהותי  (SCOT)',
	frequency: 'תדירות',
	SelectFrequency: 'בחר תדירות',
	SelectControlType: 'בחר סוג בקרה',
	itBadge: 'IT',
	soBadge: 'SO',
	noRecordsAvailable: 'אין רשומות זמינות',
	noIncompleteResponseSummaryView: 'לא קיימות תשובות שטרם נענו',
	noUnresolvedCommentsSummaryView: 'לא קיימות הערות לא פתורות',
	edit: 'ערוך',
	editForm: 'ערוך טופס',
	editControl: 'ערוך בקרה',
	delete: 'מחק',
	remove: 'הסר',
	noBodies: 'אין תוכן זמין',
	relateDocuments: 'קשר מסמכים',
	relatedDocuments: 'מסמכים קשורים',
	deleteBody: 'מחק גוף',
	bodyDescription: 'האם אתה בטוח שברצונך למחוק את הגוף שנבחר?',
	description: 'תיאור',
	maxLengthForEditResponse: 'טקסט גוף חורג מהאורך המרבי המותר',
	maxLengthForEditResponseWithCount: 'התגובה מכילה {#} תווים אשר מהווים חריגה מהמקסימום של {##} תווים. התאם את התשובה על ידי הפחתת טקסט או עיצוב ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	saveResponse: 'ביטול השינויים לא ישמור עריכות שבוצעו בטקסט התגובה. הערות סגורות או שנמחקו יישארו מסומנות. אנא אשר אם ברצונך לבטל את כל השינויים.',
	discardChangesModalText: 'התעלם משינויים',
	seeBodyDescriptionText: 'ראה תיאור',
	hideBodyDescriptionText: 'הסתר תיאור',
	showBodyDescriptionText: 'הצג תיאור',
	okLabel: 'אשר',
	addButtonLabel: 'הוסף',
	addEvidence: 'הוסף ראיות',
	addTemporaryFiles: 'הוסף קבצים זמניים',
	notemporaryDocs: 'אין קבצים זמניים זמינים',
	relateFiles: 'קשר קבצים',
	uploadFiles: 'העלאת קבצים',
	cancelButtonLabel: 'בטל',
	clickEditResponseToContinue: 'לחץ על אייקון העריכה בכדי להמשיך',
	editResponse: 'ערוך תגובה',
	save: 'שמור',
	numericValuePlaceholder: 'הזן סכום',
	saveLabel: 'שמור',
	cancelLabel: 'בטל',
	closeLabel: 'Close',
	editText: 'ערוך',
	select: 'בחר',
	selectScot: 'בחר תתי תהליכים מהותיים (SCOT)',
	clearHoverText: 'Clear',
	optional: '(אופציונלי)',
	nodocumentsAdded: 'אין מסמכים זמינים',
	errorBanner: '{0} שגיאה/ות',
	NetworkErrorMessage: 'רשת היישומים חווה שגיאה. רענן את הדף ונסה שוב מאוחר יותר. פנה לתמיכה הטכנית (Help Desk) אם השגיאה נמשכת.',
	of: 'שֶׁל',
	characters: 'תווים',
	show: 'הצג: ',
	views: 'תצוגה',
	primaryRelated: 'טפסי canvas שקושרו באופן ראשוני',
	secondaryRelated: 'טפסי canvas שקושרו באופן משני',
	singleLineValuePlaceholder: 'הזן טקסט',
	paceInputPlaceholder: 'מזהה PACE',
	multiLineValuePlaceholder: 'הזן טקסט',
	riskFactorDescribe: 'תאר',
	riskFactorLabel: 'אירוע ותנאי רלוונטיים/סיכון להצגה מוטעית',
	riskFactorEmptyWarning: 'חסר תיאור לאירוע ותנאי (Event and condition) / סיכון של הצגה מוטעית (ROMM)',
	riskFactorNoDescription: 'צור חדש או בחר אירוע קיים או תנאי רלוונטי / סיכון להצגה מוטעית',
	fraudRiskTagMessage: 'אירוע ותנאי/סיכון רלוונטיים אלה להצגה מוטעית גורמים תמיד לסיכון הונאה או, אם רלוונטי, צריך להיות מוגדר כלא מהווה סיכון להצגה מוטעית מהותית',
	significantRiskTagMessage: 'אירוע ותנאי/סיכון רלוונטיים אלה להצגה מוטעית גורמים תמיד לסיכון משמעותי, סיכון הונאה או, אם רלוונטי, צריך להיות מוגדר כלא מהווה לסיכון להצגה מוטעית מהותית',
	on: 'על',
	sliderDeSelectMessage: 'גרור את העיגול כדי להקצות ערך',
	yearPlaceholder: 'YYYY',
	dayPlaceholder: 'DD',
	monthPlaceholder: 'MM',
	amLabel: 'AM',
	pmLabel: 'PM',
	relatedEntities: 'ישויות קשורות',
	eyServiceGateway: 'EY Service Gateway',
	eyAutomation: 'EY Automation',
	eyserviceGatewayAutomation: 'EY Service Gateway & Automation',
	creating: 'Creating...',
	cannotCreateUdp: 'Cannot create UDP. Time Phase cannot be empty',

	// 440GL
	rrdReminderTitle: 'תזכורת ל- RAS',
	rrdReminderMessage: 'תאריך פרסום הדוח הוא {rrdDescription}. זכור לחתום על טופס ה RAS {rrdPendingDays}.',
	rrdReminderPendingDays: 'בעוד {0} יום/ימים',

	//Create or Associate risks
	createAssociateRisksLabel: 'צור סיכונים חדשים או קשר סיכונים קיימים',
	relatedRiskIsMissingWarning: 'חסר סיכון קשור',
	associateRiskDescription: 'בחר סיכון אחד או יותר, או צור סיכון חדש, לשיוך לתגובה לשאלה.',
	createNewRiskLabel: 'צור סיכון חדש',
	noRiskIdentifiedLabel: 'לא זוהו סיכונים',

	// GuidanceModal
	eyAtlasLink: 'EY Atlas',
	guidanceHeaderMessage: 'מודול זה מכיל',
	guidanceModalHeader: 'הנחיות',
	guidanceModalLabelText: 'רישום',
	guidanceFooter: 'למידע נוסף.',
	guidanceSeveralEntriesText: 'מספר ערכים: ',
	guidanceVisitText: 'לבקר',
	guidanceClickText: 'לחץ',
	guidanceHereText: 'כאן',
	guidanceFooterText: 'למידע נוסף.',
	analyticsInconsistencies: 'בעת סקירת ניתוח נתונים, שקול אם יש שינויים או פעילות שאינם עולים בקנה אחד עם הציפיות שלנו. מצבים אלו עשויים להעיד על סיכונים חדשים, סוגים נפרדים של עסקאות, שינויים בתתי התהליכים המהותיים (SCOTs) או סיכונים של עקיפת בקרות על ידי ההנהלה.',
	analyticsInconsistenciesOSJE: 'OSJE מתאר את החשבונאות הכפולה עבור הסעיף המנותח הנבחר. אנו מחפשים התאמות חדשות או יוצאות דופן לסעיף.',
	analyticsInconsistenciesActivityBySource: 'פעילות לפי מקור מתארת את הפעילות החודשית ברוטו ואת המקורות הקשורים לסעיף שנבחר. אנו מתמקדים בפעילות חריגה או שינויים במקורות או בנפח הפעילות למקור.',
	analyticsInconsistenciesPreparerAnalysis: 'ניתוח מכינים מסכם את הפעילות הברוטו של תקופה על פני תקופה שנרשמה על ידי מכינים עבור הסעיף שנבחר. אנו מתמקדים בשינויים במכינים או בפעילות בסעיפים מחוץ למסגרת תפקידם.',
	analyticsInconsistenciesAccountMetrics: 'מדדי (metrics) חשבון מסכמים מידע מרכזי על הסעיף המסייע בהגדרתו.',
	analyticsLoadingIsInProgress: 'הניתוח המבוקש עדיין נטען. בסיומו, תוכל לגשת ללשונית.',

	aboutDescriptioin: 'ערוך את שכבות ה- GAM התקנים או שפה. עריכות אלו יגרמו לעדכון תוכן.',
	aboutContentDescription: 'Edit the content layers, standards, language, or content driving entity. Edits will trigger a content update.',
	about: 'על',
	formType: 'סוג טופס',
	gamLayer: 'שכבות GAM (GAM layers)',
	contentLayer: 'שכבות תוכן',
	standard: 'תֶקֶן',
	language: 'שפה',
	createdBy: 'נוצר על ידי',
	createdOn: 'נוצר ב',
	contentLastUpdatedBy: 'התוכן עודכן לאחרונה על ידי',
	contentLastUpdatedOn: 'התוכן עודכן לאחרונה ב-',
	notModified: 'לא השתנה',

	rolesInsufficientTooltip: 'אין די תפקידים לעריכת תוכן. עבוד עם מנהל תיק הביקורת כדי לקבל הרשאות מתאימות.',
	knowledgeFormToolTip: "Knowledge delivered documents cannot be updated. Update the Engagement Profile to change this form's profile.",
	selectTeamMember: 'שם או דואל',

	// SeeMore component
	showMore: 'הצג עוד',
	showLess: 'הצג פחות',
	showMoreEllipsis: 'הצג עוד ...',
	showLessEllipsis: 'הצג פחות ...',

	relatedITapplicationSOs: 'יישומי IT/SOs קשורים',
	aggregateITevaluations: 'הערכות IT מצטברות',
	lowerRisk: 'בסיכון נמוך',
	controlLowerRisk: 'הבקרה בסיכון נמוך',
	relatedITApplication: 'יישומי IT קשורים',
	relatedITSO: 'SOs קשורים',
	noITApplicationUsed: 'לא נעשה שימוש ביישום IT',

	notSel: 'לא נבחר',
	internal: 'פנימי',
	external: 'חיצוני',
	notSelected: 'לא נבחר',
	noOptionSelected: 'לא נבחר',
	tod: 'TOD',
	sap: 'SAP',
	int: 'INT',
	ext: 'שלוחה',
	toc: 'TOC',

	placeholderForSearch: 'חפש',
	source: 'מקור',
	nature: 'אופי',
	testOfDetail: 'בדיקת פריטים (Test of details)',
	testOfControl: 'טסט לבקרה (TOC)',
	substantiveAnalyticalProcedure: 'נוהל אנליטי מבסס',
	expandScot: '1. הרחב את תת התהליך המהותי (ה- SCOT)',
	selectWCGWToDisplayTheResponsiveTask: '2. בחר את הסיכון להצגה מוטעית מהותית בכדי להציג את משימות המענה לסיכון בהתאם',
	tasksWithNoRelatedWCGW: 'משימות ללא קישור להצגה מוטעית מהותית',
	noTasksAvailable: 'אין משימות זמינות',
	noWCGWAvailableForTask: 'אין ROMM זמין',
	noSubstantiveTasksAvailable: 'אין משימות מבססות קשורות',
	selectAssertionToRelateWCGW: 'בחר מצג הנהלה כדי לקשר סיכון ל WCGW',
	significantAccounts: 'סעיפים מהותיים',
	riskName: 'סיכון: ',
	accountName: 'סעיף: ',
	control: 'בקרה',
	controls: 'בקרות',
	noScotsFound: 'אין תתי תהליכים מהותיים (SCOTs) קשורים',
	relatedwcgw: 'WCGWs מקושרים',
	relatedRisks: 'סיכונים קשורים: ',
	boltIconTitle: 'סיכונים קשורים',
	relatedITApp: 'אפליקציית IT / SO קשורה',
	instructions: 'הוראות: ',
	expandRisk: '1. הרחב את הסיכון',
	selectAssertion: '2. בחר מצג הנהלה (assertion)',
	identifyRelatedWCGW: '3. זהה את ה- WCGW המקושר לסיכון',
	clickAccount: '1. לחץ על סעיף',
	selectWCGW: '2. בחר WCGW',
	identifyRelatedTask: '3. זהה את המשימות כמענה ל- WCGW',
	information: 'מידע',
	requiredAssertions: 'מצגי הנהלה נדרשים',
	wcgwWithoutTasks: 'WCGW ללא משימות',
	rommAssociatedWNotRelyAssertion: 'ROMM משויך ליעד הביקורת עם סיכון בקרה של אי הסתמכות (CR=Not Rely) או יעד ביקורת עם סיכון שבמהות גבוה (IR=High) כאשר לסעיף משויך אומדן.',
	hasRiskAssociated: 'סיכון קשור',
	clearSelections: 'בטל את כל הסינונים',
	romm: 'ROMM',
	riskOfMaterialMisstatementsWithoutRelatedTask: 'סיכון להצגה מוטעית מהותית ללא משימה קשורה',
	selectOneOrMoreTasksToSeeTheRelatedROMM: 'בחר משימה אחת או יותר כדי לראות את ה- ROMM הקשור',
	invalidRelatedEntity: 'לא ניתן למצוא סעיף קשור. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית (Help Desk) אם הבעיה נמשכת.',
	noResultsAvailable: 'לא נמצאו תוצאות',
	riskOfMaterialMisstatement: 'סיכון להצגה מוטעית מהותית',
	AccountConclusion: 'סיכום סעיף',
	CanvasForm: 'Canvas form',
	IndependenceForm: 'טופס אי תלות',
	Profile: 'פרופיל',
	AccountDetails: 'פרטים',
	Conclusions: 'מסקנות',
	accountDetailsTab: 'פרטים',
	conclusionsTab: 'מסקנות',
	formsNoContentText: 'אין תוכן זמין.',
	formsDocumentNoRelatedObjects: 'אין אובייקטים שקשורים למסמך',
	formsNoRelatedObjects: 'אין אובייקטים קשורים',
	formsBodyHeaderControl: 'בקרות',
	formsBodyDesignEffectiveness: 'יעילות עיצוב',
	formsScotsAndWcgws: 'SCOTs & WCGWs',
	wcgWAndRelatedControls: 'WCGWs ובקרות קשורות',
	controlAndRelatedItSO: 'בקרות ואפליקציות IT/SOs קשורות',
	type: 'סוג',
	designEffectiveness: 'Design Effectiveness',
	approch: 'גישה',
	controlOpertaingEffectiveness: 'אפקטיביות תפעולית',
	iTAppSO: 'אפליקציית IT/SO',
	iTProcess: 'תהליך IT',
	iTControl: 'בקרת IT',
	iTRisk: 'סיכוני IT',
	aggregateITEvaluation: 'הערכת IT מצטברת',
	relatedCanvasForm: 'Related Canvas Form',
	relatedSections: 'מקטעים קשורים',
	validations: 'תיקוף',
	profileV2Validation: 'השינויים לא הועברו',
	profileV2ValidationModalDescription: 'בוצעו שינויים שיביאו לעדכון תוכן, אך טרם נשלחו. אם השינויים בוצעו בטעות, סגרו מודל זה ושלחו את התשובות לשאלות הפרופיל החדשות. אם השינויים בוצעו שלא בטעות, עיין בתצוגת השינויים והחזירו ידנית את התשובות לבחירות הקודמות.',
	profileV2ValidationCount: '1',
	itProcessWithoutRelatedTechnology: 'IT process without related technology',
	reviewNote: 'סקירת הערות',
	editAssociations: 'ערוך קישורים',
	editAssociationsLower: 'ערוך קישורים',
	riskWCGW: 'שיוך של סיכון:WCGW',
	wcgwTask: 'קישורים של משימה:WCGW',
	noAssertionFound: 'לא קושרו מצגי הנהלה. לחץ {here} כדי לשייך מצגי הנהלה',
	limitedRiskAccountIdentifier: 'חשבון בסיכון מוגבל (LRA)',
	insignificantAccountIdentifier: 'חשבון לא משמעותי',
	noWCGWFound: 'אין WCGWs הקשורים לסיכון זה. לחץ על ערוך שיוכים כדי לקשר WCGWs אחד או יותר.',
	noRelatedWCGWs: 'אין WCGWs קשורים',
	noWCGWAvailable: 'אין ROMM זמין עבור מצג(י) ההנהלה (Assertion(s שנבחרו',
	expandCollapse: 'לחץ כאן כדי להרחיב/לצמצם',
	requiredAssertionsInfo: 'הצג רק יעדי ביקורת עם הערכת סיכוני בקרה של אי הסתמכות (CR=Not Rely) ויעדי ביקורת הקשורים לאומדנים בסיכון גבוה (higher risk estimates)',
	wCGWwithoutTasksInfo: 'הצג רק WCGWs שקשורים ליעד ביקורת עם הערכת סיכון בקרה של אי הסתמכות (CR=Not Rely) ויעדי ביקורת הקשורים לאומדנים בסיכון גבוה (higher risk estimates) ואשר אין להם משימות מבססות למתן מענה',
	noBuildStepsAvailable: 'אין שלבים זמינים להצגה.',
	risk: 'סיכונים',
	wcgw: 'WCGW',
	riskWcgw: 'סיכון: WCGW',
	wcgwTasks: 'WCGW: משימה',
	riskWcgwLabel: 'קישור הסיכון ל- WCGW',
	wcgwTasksLabel: 'קישור ה- WCGW למשימה',
	noRiskTypes: 'לא נמצאו סוגי סיכונים',
	saveRisk: 'שמור',
	noRisksFound: 'לא נמצאו סיכונים',
	haveBeenIdentified: 'זוהה',
	noAccountsFound: 'לא נמצאו רשומות',
	noResponseAvailable: 'אין תגובה זמינה',
	noDocumentsAvailable: 'אין מסמכים זמינים',
	noValue: 'אין ערך',
	showValidation: 'תיקוף',
	noAccountsIdentified: 'לא זוהו סעיפים.',
	noAssertionsIdentified: 'מצגי ההנהלה לא זוהו',
	noWcgwsIdentified: 'לא זוהו WCGW',
	pastingImagesNotAllowed: 'אין להדביק תמונות. אם נדרשות תמונות העלו אותן כראיות והתייחסו אליהן.',
	incompleteResponse: 'מענה שלא שולם',
	unresolvedComments: 'הערות (comments) לא פתורות',
	inconsistentForms: 'צורות לא עקביות',
	limitedRiskAccount: 'סעיף בסיכון מוגבל (limited risk account)',
	inherentRiskAssessment: 'הערכת הסיכון שבמהות (IR)',
	task: 'משימה',
	selected: 'המשתמש שנבחר',
	displaytoc: 'הצג טסטים על הבקרות (TOC)',
	workingoffline: 'עבודה במצב לא מקוון',
	syncinprogress: 'הסנכרון מתבצע',
	prepareoffline: 'הכנת נתונים למצב לא מקווןן',
	connectionavilable: 'חיבור זמין',
	softwareUpdate: 'עדכוני תוכנה',
	updateLater: 'עדכן מאוחר יותר',
	updateNow: 'עדכן כעת',
	updateMsg: 'עדכוני תוכנה זמינים עבור EY Canvas. בחר באפשרות עדכן כעת כדי להוריד ולהתקין את העדכונים. הדף יתרענן לאחד מכן.',
	searchPlaceholder: 'חפש',
	filter: 'סנן',
	leftNavSearchPlaceholder: 'חיפוש כותרות ומדורים',
	back: 'חזור',
	updateAvailable: 'עדכון זמין',
	contentUpdateAvailableTooltip: "קיים עדכון תוכן זמין. לחץ כאן כדי לנווט למסך'עדכוני טופס Canvas' בכדי להתחיל את העדכון.",
	moreMenu: 'תפריט נוסף',
	signoffPreparer: 'חתום כמכין',
	signoffReviewer: 'חתום כסוקר',
	pagingShowtext: 'הצג',
	searchDocuments: 'חיפוש מסמכים',
	noRelatedDocuments: 'אין מסמכים קשורים.',
	noRelatedObjects: 'אין אובייקטים קשורים',
	documentName: 'שם המסמך',
	formDetails: 'פרטי טופס',
	questionsAndResponses: 'שאלות ותשובות',
	details: 'פרטים',
	trackChanges: 'עקוב אחר שינויים',
	goToTrackChanges: 'עבור אל עקוב אחר שינויים',
	attributes: 'מאפיינים',
	relatedActions: 'פעולות קשורות',
	createCustom: 'צור מותאם אישית',
	createCustomButtonLabel: 'Create custom header, section, or body',
	overwriteForm: 'החלף טופס',
	decimalNaN: 'NaN - אינו מהווה מספר',
	noRelatedObjectsApplicable: 'אובייקטים אינם נדרשים להיות משויכים לטופס הקאנבס הזה',
	objects: 'אובייקטים',
	objectName: 'שם הפריט',
	addCustomDescription: 'בחר את סוג התוכן שיש להוסיף לטופס Canvas זה הזן פרטי קלט ולחץ על שמור',
	headerTitle: 'כותרת Header',
	sectionTitle: 'כותרת לפיסקה (חובה)',
	aresSectionTitle: 'כותרת הסעיף',
	customLabel: 'תווית מותאמת אישית (אופציונלי)',
	customBodyDescription: 'תיאור גוף',
	header: 'כּוֹתֶרֶת',
	section: 'פיסקה',
	body: 'גוּף',
	requiredWCGW: 'נדרש',
	headerTitleRequired: 'כותרת Header נדרשת.',
	bodyDescriptionRequired: 'נדרש תיאור תוכן.',
	bodySectionRequired: 'נדרשת פיסקה.',
	bodyHeaderRequired: 'כותרת נדרשת.',
	sectionTitleRequired: 'נדרש לתת כותרת לפיסקה.',
	headerRequiredMessage: 'כותרת נדרשת.',
	enterDecimalAmount: 'הזן סכום עשרוני',
	enterPercentage: 'הזן אחוז',
	completeRiskFactorAssessment: 'השלם את ההערכה של אירועים ותנאים שזוהו.',
	noScotsEstimatesIdentified: 'לא זוהו תתי תהליכים מהותיים SCOTs או אומדנים',
	// Track changes
	trackChangesResponseLabel: 'מענה בגרסת עקוב אחר שינויים',
	trackChangesVersionLabel: 'עקוב אחר שינויים בגרסה',
	noResponseIdentified: 'לא זוהתה תגובה',

	// Compare responses
	compareResponsesLabel: 'השווה תגובות',
	compareResponsesTitle: 'השווה בין תגובות הישות',
	compareResponseNoDataPlaceholder: 'אין נתונים זמינים, הואיל ולהתקשרות יש רק מסמך אחד מאותו סוג.',
	labelFor: 'עבור',
	questions: 'שאלות',
	answers: 'תשובות',
	countOfResponses: 'ספירת התגובות',
	openNotes: 'הערות פתוחות',
	clearedNotes: 'הערות שנסגרו',
	click: 'לחץ',
	clickToViewAnswer: 'לצפייה בתשובה',
	clickToViewQuestionAnswer: 'לצפייה בשאלה ובתשובה',
	selectDocuments: 'בחר מסמכים',
	selectedDocumentsCount: 'נבחרו {0} מסמכים',
	selectedDocumentCount: 'נבחר מסמך {0}',
	associatedDocuments: 'מסמכים קשורים',
	noAnswerProvided: 'לא ניתנה תשובה',

	// Workspace Engagement
	thisEngagement: 'התקשרות זו',
	documentLocation: 'מיקום המסמך',
	otherEngagementsInWorkspace: 'התקשרויות אחרות בסביבת העבודה',
	added: 'נוסף',
	documentIneligibleForSharingMessage: 'לא ניתן לשתף מסמכים סודיים.',
	fitDocumentCannotbeSelected: 'לא ניתן לשתף מסמכי FIT בין תיקי ביקורת.',

	//Helix Configuration
	helixConfigurationTitle: 'שלב נתוני EY Helix',
	helixConfigurationPageDescription: 'אימות פרויקט EY Helix מקושר ויבוא נתונים ל-EY Canvas. אם שינית הגדרות כלשהן של EY Helix להלן או ביצעת שינויים בנתוני EY Helix שלך לאחר ייבוא הנתונים, תצטרך לייבא מחדש את הנתונים כדי שהעדכון יתבצע.',
	linkedEYHelixProjects: 'פרויקטים מקושרים של EY Helix: ',
	client: 'לקוח: ',
	engagement: 'תיק הביקורת: ',
	analysisDate: 'תאריך ניתוח: ',
	eyHelixProjects: 'פרויקטים של EY Helix',
	noPrimaryEYHelixproject: 'לא זוהה פרויקט EY Helix ראשי.',
	here: 'כאן',
	identifyEyHelixProjects: 'לזהות פרויקט EY Helix ולהתחיל את תהליך העבודה.',
	eyHelix: 'EY Helix',
	primary: 'Primary',
	helixSettingsDescription: 'לחץ על ערוך כדי לבחור את ההגדרות שיופעלו בעת טעינת EY Helix Analyzers.',
	editButton: 'ערוך',
	helixSettingsModalTitle: 'הגדרות EY Helix',
	currencyType: 'סוג המטבע',
	currencyTypeError: 'לא ניתן היה לאחזר סוג מטבע מ- EY Helix. ודא שהנתונים הוגדרו כראוי ב- EY Helix ונסה שוב.',
	shortNumberFormat: 'פורמט למספר קצר',
	shortNumberFormatFooter: 'החלת עיגול ספרות על ערכים מספריים המוצגים בטבלאות EY Helix.',
	eyHelixAnalyzerFilterMetadataError: 'לא ניתן היה להתחבר ל- EY Helix. רענן את הדף ונסה שוב. אם הבעיה ממשיכה אנא פנה לתמיכה הטכנית.',
	functional: 'פוּנקצִיוֹנָלִי',
	reporting: 'דוחות',
	currencyCode: 'קוד מטבע',
	businessUnit: 'יחידה עסקית',
	roundingNumberFormat: 'פורמט לעיגול ספרות',
	eyHelixProjectChangedLine1: 'פרויקט EY Helix המקושר השתנה מאז הפעם האחרונה בה ההגדרות של ה- EY Helix נשמרו.',
	eyHelixProjectChangedLine2: 'לחץ על ערוך כדי לעדכן הגדרות לפני שניתן יהיה לייבא או לייבא מחדש נתונים מה- EY Helix.',
	helixSettingsTimeline: 'הגדרות EY Helix',
	helixMapEntitiesToBU: 'מפה ישויות ליחידות עסקיות',
	helixNumberOfMapEntities: 'מספר היחידות העסקיות הממופות',
	importEYHelixDataTimeline: 'ייבא נתוני EY Helix',
	mapAccountsHelixTimeline: 'מיפוי סעיפים',
	setEYHelixSettings: "ערוך את הגדרות EY Helix שלך להלן. לאחר השמירה וייבוא הנתונים, התאריכים שנבחרו תאריך השוואתי 1 ותאריך השוואתי 2 ישמשו כהשוואות מול תאריך הניתוח המיועד בפעילות ה- OAR. כדי להשוות לתאריך השוואתי אחד בלבד, בחר'ללא' בבחירת 2 התאריכים ההשוואתיים.",
	eyHelixDataHasChangedLine1: 'הנתונים השתנו מאז הפעם האחרונה שבה נשמרו ההגדרות. בצע בחירות חדשות למטה ולחץ',
	eyHelixDataHasChangedLine2: 'לעדכון הגדרות EY Helix.',
	all: 'הכול',
	multiple: 'Multiple',
	notApplicableAbbreviation: 'לא רלוונטי',
	importEyHelixData: 'ייבא נתוני EY Helix',
	editScreenshot: 'ערוך',
	deleteNote: 'מחק',
	removeAnnotation: 'הסר הערה',
	addAnnotation: 'הוסף הערה',
	addingBoundingBox: 'בחר אזור בצילום המסך להערה, אשר את ההערה ולחץ על סימן הביקורת (checkmark) לשמירה.',
	cancelBoundingBox: 'בטל',
	deleteScreenshot: 'מחק צילום מסך',
	openInFullscreen: 'פתח במסך מלא',
	helixURLErrorMessage1: 'התצורה של פרויקט EY Helix הממופה אינה מלאה.',
	helixURLErrorMessage2: 'נא עבור לדף {0} כדי לעדכן.',
	helixIsNotEnabledMessage: 'EY Helix אינו מופעל עבור תיק הביקורת שלך.',
	helixSetup: 'הגדרת EY Helix',
	openAnalysisInHelix: 'ניתוח פתוח ב-EY Helix',
	helixInvaliddate: 'תאריך לא חוקי. בחר תאריך לפני תאריך הניתוח.',
	helixcomparativedateoptional: 'תאריך השוואתי EY Helix 2 (אופציונלי)',
	helixpriorperioddate: 'תאריך השוואתי EY Helix 1',
	helixanalysisperiod: 'תאריך ניתוח EY Helix',
	helixfiscalDropDownLabel: 'תקופה {0} - {1}',

	helixSettingsEditButtonTitle: 'ערוך',
	helixImportDataEditButtonTitle: 'בוא',
	helixImportInsufficientPermissionsMessage: 'אין הרשאות מספיקות לביצוע ייבוא נתוני Helix. צור קשר עם מנהל ההתקשרות שלך ובקש הרשאה ליזום ייבוא נתוני Helix.',
	helixImportNotAllowed: 'פרופיל תיק הביקורת אינו מאפשר ייבוא של נתוני Helix',
	helixDeleteImportInsufficientPermissionsMessage: 'אין הרשאות מספיקות למחיקת ייבוא ​​הנתונים של ה- EY Helix. פנה למנהל ההתקשרות שלך ובקש רשות למחוק את נתוני EY Helix.',

	EYAccountMappingStepLabel: 'ניהול מיפוי הסעיפים (Account mapping)',

	EYAccountMappingOptional: 'אופציונלי',
	EYAccountMappingStepTitleSettingsCompleted: 'בצע מיפוי חשבון EY',
	EYAccountMappingStepTitleSettingsIncomplete: 'השלם את הגדרות EY Helix כדי לגשת למודול EY Helix Mapping Module',
	EYAccountMappingInstructions: 'מיפוי סעיפי לקוחות לחשבונות EY ותהליכים שעברו שינויים. לאחר השלמת העיבוד, ייבא את הנתונים להלן.',
	manageAccountMappingButtonLabel: 'ניהול מיפוי הסעיפים (Account mapping)',

	//EY Helix Setup Card
	EYHelixSetupTitle: 'EY Helix',
	EYHelixSetupSubTitle: 'הגדר וייבא נתונים ל-EY Canvas',
	LastImported: 'יבוא אחרון',
	EYHelixSettings: 'חיבורי EY Helix',
	NoEYHelixProjectLinkedLabel1: 'התקשרות זו אינה מקושרת לפרויקט Primary EY Helix. אנא בקר ב',
	NoEYHelixProjectLinkedLabel2: 'עמוד ליצירת הקישור.',
	NoEYHelixProjectLinkedHperlink: 'פרויקטים של EY Helix',
	NoDataImportedLabel: 'נתונים לא יובאו מ-EY Helix. לחץ על הגדרות EY Helix כדי להתחיל את התהליך.',
	noHelixConnections: "לחץ על \'חיבורי EY Helix\' כדי ליצור חיבור.",
	helixConnectionExists: 'קיים חיבור',
	helixConnectionsExist: 'קיימים חיבורים',
	helixTrialBalanceImported: 'ייבוא מאזן בוחן היקף ואסטרטגיה',
	helixTrialBalancesImported: 'ייבוא מאזני בוחן של היקף ואסטרטגיה',
	helixNoTrialBalanceImported: "לחץ על \'חיבורי EY Helix\' כדי לייבא מאזן בוחן של היקף ואסטרטגיה.",

	// EY Helix - Map Accounts
	mapAccountsHelixCanvas: "לחץ על'ערוך' כדי למפות סעיפים ב -  EY Canvas מסעיפים שיובאו ל- EY Helix",
	mapAccountsHelixCanvasSubtitle: "גרור ושחרר כל סעיף ב -  EY Helix כדי למפות אותו לסעיף ב -  EY Canvas. לחץ על'נהל סעיפים' כדי ליצור או לערוך סעיפים EY Canvas.",
	mapAccountsHelixHeaderLabel: 'סעיפים ב - EY Helix',
	mapAccountsCanvasHeaderLabel: 'סעיפים ב- EY Canvas',
	mapAccountsConnectedLabel: 'סעיפים מחוברים',
	mapAccountsShowMappedLabel: 'הצג סעיפים שמופו',
	mapAccountsHideMappedLabel: 'הסתר חשבונות שמופו',
	mapAccountsManageLabel: 'נהל סעיפים',
	mapAccountsAndDisclosuresManageLabel: 'ניהול חשבונות וגילויים',
	mapAccountsNoCanvasAccounts: 'לא זוהו סעיפים.',
	mapAccountsNoCanvasAccountsClick: 'לחץ',
	mapAccountsNoCanvasAccountGetStarted: ' בכדי להתחיל.',
	mapAccountsRemoveAccount: 'הסר',
	mapAccountsReImportHelixAccounts: 'ייבוא ​​הנתונים מה-EY Helix לא הצליח. אנא ייבא מחדש את הנתונים ונסה שוב.',
	mapAccountsReImportHelixAccountsHelpDesk: 'אם הבעיה נמשכת, פנה לתמיכה הטכנית',
	mapAccountsNoHelixAccountHasBeenImported: 'לא יובאו סעיפים ל- EY Helix.',
	mapAccountsNoHelixAccountHasBeenImportedCheckData: 'בדוק נתונים ב-EY Helix ונסה שוב.',

	//Helix Analyzer
	accountNotRelatedToDocumentOnPhaseTwo: 'אין סעיף הקשור למסמך זה',

	//PM TE SAD Widget
	materialityWidgetLabel: 'PM/TE/SAD',

	// TE labels
	planningmateriality: 'סף המהותיות PM',
	requiredTePercentage: 'הטעות הנסבלת (TE) הנדרשת',
	suggestedtepercentage: 'הטעות הנסבלת (TE) המוצעת',
	currentperiodte: 'הטעות הנסבלת (TE) לתקופה הנוכחית',
	priorperiodte: 'הטעות הנסבלת (TE) לתקופה קודמת',
	pmPriorPeriod: 'סף המהותיות (PM) בשנה קודמת',
	tepercentage: 'אחוז הטעות הנסבלת (TE)',
	teamount: 'סף הטעות הנסבלת (TE)',
	teLabel: 'הטעות הנסבלת TE',
	sugestedSAD: 'אחוז SAD מומלץ: ',
	priorSAD: 'SAD תקופה קודמת',
	currentPeriodSAD: 'הפרשי ביקורת לתקופה נוכחית SAD CY',
	sadPercentage: 'אחוז SAD',
	sadAmount: 'הסף הכמותי ל- SAD',
	rationaleLabel: 'הסבר לאפיון הסעיף',
	suggestedTEPercentageInfo: 'אם נבחר אחוז אחר על סמך גורמים ספציפיים לתיק הביקורת, תתבקש לתעד את הגורמים האלה למטה.',
	rationaleTEDescription: 'הזן את הרציונל עבור TE כאחוז מה-PM בהתחשב במאפיינים שנבחרו לעיל.',
	teAmmountInvalid: 'הזן סכום חוקי או בחר 50% או 75%',
	highRiskTEAmmountInvalid: 'הזן סכום חוקי או בחר 50%',
	highRiskTERequired: 'בהתבסס על התגובות לשיקולים לעיל, אחוז ה-TE שנקבע נדרש ואינו ניתן לשינוי.',

	// EY Helix Map Entities to Business Units Modal
	mapEntitiesModalTitle: 'ניהול ישויות',
	mapEntitiesModalLeyendDescription: 'נהל את המיפוי בין ישויות EY Canvas ויחידות עסקיות מפרויקט EY Helix להלן.',
	mapEntitiesModalLeyendNote: 'הערה: ניתן לשייך יחידה עסקית לישות EY Canvas אחת או מרובות. לאחר השמירה וייבוא הנתונים, הנתונים המשויכים ליחידות העסקיות ב-EY Helix יופיעו כממופים לישויות EY Canvas קשורות.',
	mapEntitiesModalEntityCodeLabel: 'קוד ישות',
	mapEntitiesModalEmptyEntitiesList: 'לא נוצרו ישויות.',
	mapEntitiesRelatedBusinessUnitDropdownPlaceholder: 'יחידה עסקית קשורה',
	mapEntitiesSelectedBusinessUnitsCount: '{0} יחידות עסקיות נבחרו',

	//AdjustedBasis
	enterAmount: 'הזן סכום',
	basisAmount: 'רף הבסיס',
	lowEndOfRange: 'קצה תחתון של הטווח',
	highEndOfRange: 'קצה עליון של הטווח',
	suggestedRange: 'בהתבסס על הגורמים שלעיל, שימוש באחוז לכיוון ה-{0} של הטווח {1} עד {2} עשוי להיות מתאים. אם סכום נבחר מחוץ לטווח הזה על סמך גורמים ספציפיים לתיק הביקורת, תתבקש לתעד את הגורמים האלה למטה.',
	suggestedRangeLowPMBracket: 'בהתבסס על הגורמים לעיל, ייתכן שאחוז של {0} יהיה מתאים. אם נבחר סכום מחוץ לאחוז זה על סמך גורמים ספציפיים לתיק הביקורת, תתבקש לתעד את הגורמים הללו.',
	middle: 'אֶמצַע',
	lowerEnd: 'קצה תחתון',
	higherEnd: 'קצה עליון',
	lowEnd: 'סוף נמוך',
	priorPeriodPm: 'סף המהותיות PM לתקופה קודמת: ',
	suggestedRangeSummary: 'טווח מוצע',
	loadingMateriality: 'טוען מהותיות...',
	pmBasisPercentage: 'אחוז בסיס לסף המהותיות PM',
	pmAmount: 'הרף הכמותי לסף המהותיות PM',
	currentPmAmount: 'סכום סף המהותיות PM לתקופה הנוכחית',
	pmAmountPlaceholder: 'הזן סף PM',
	currentPeriodPm: 'סף המהותיות PM לתקופה נוכחית: ',
	enterRationale: 'הזן הסבר',
	rationaleDescription: 'הזן את ההסבר לקביעת אחוז הבסיס לסף המהותיות PM בהתחשב במאפיינים שנבחרו לעיל',
	pmValidationMessage: 'סף המהותיות (PM) אינו יכול לחרוג מהקצה הגבוה של הטווח',
	sadValidationMessage: 'הסכום הנומינלי (NA) אינו יכול לחרוג מהקצה הגבוה של הטווח.',
	sadRationaleDiscription: 'הזן את הרציונל לאחוז SAD בהתחשב במאפיינים שנבחרו לעיל',
	nopriorperiodDocs: 'אין מסמכים זמינים לתקופה קודמת',
	addPriorPeriodEvidence: 'הוסף ראיית ביקורת משנה קודמת',
	addToEvidenceLabel: 'הוסף לראיות',
	moveToEvidenceLabel: 'עברו לראיות',
	addToEvidenceModalDescription: 'צור שם חדש או שמור את השם הקיים עבור המסמך שנבחר.',
	GoToSource: 'עבור למקור',
	//ITRiskITControls
	createNewITGC: 'בקרה כללית חדשה במערכות המידע (ITGC)',
	relateITGC: 'Relate ITGCs',
	createNewITSP: 'נוהל מבסס ל- IT  (IT-substantive testing procedures (ITSP)) חדש',
	relateITSP: 'קשר ITSP',
	noITGC: 'ללא ITGC',
	itRiskForITGCITSP: 'שם סיכון IT (נדרש)',
	createITGCModalDescription: "הזן את פרטי ה-ITGC להלן ובחר'<b>{0}</b>' כדי לסיים. כדי ליצור ITGC נוסף, בחר'<b>{1}</b>'.",
	createITSPModalDescription: "הזן את פרטי ה-ITSP להלן ובחר'<b>{0}</b>' לסיום. כדי ליצור ITSP נוסף, בחר'<b>{1}</b>'.",
	controlDesignEffectiveness: {
		[0]: {
			description: 'לא נבחר'
		},
		[1]: {
			description: 'יעיל'
		},
		[2]: {
			description: 'לא יעיל'
		}
	},
	controlOperationEffectiveness: {
		[0]: {
			description: 'לא נבחר'
		},
		[1]: {
			description: 'יעיל'
		},
		[2]: {
			description: 'לא יעיל'
		}
	},
	controlTesting: {
		[0]: {
			description: 'לא נבחר'
		},
		[1]: {
			description: 'כן'
		},
		[2]: {
			description: 'לא'
		}
	},
	itAppTypes: {
		[0]: {
			label: 'אפליקציית IT'
		},
		[1]: {
			label: 'SO'
		}
	},
	controlType: {
		[0]: {
			controlTypeName: '',
			shortName: 'לא נבחר'
		},
		[1]: {
			controlTypeName: ' בקרת אפליקציית IT',
			shortName: 'יישום'
		},
		[2]: {
			controlTypeName: 'IT Dependent Manual Control',
			shortName: 'ITDM'
		},
		[3]: {
			controlTypeName: 'מונעת ידנית',
			shortName: 'מונעת ידנית'
		},
		[4]: {
			controlTypeName: 'בקרה ידנית מגלה',
			shortName: 'בקרה ידנית מגלה'
		}
	},
	controlTypeEnumLabel: {
		[0]: {
			controlTypeName: 'לא נבחר'
		},
		[1]: {
			controlTypeName: ' בקרת אפליקציית IT'
		},
		[2]: {
			controlTypeName: 'IT Dependent Manual Control'
		},
		[3]: {
			controlTypeName: 'מונעת ידנית'
		},
		[4]: {
			controlTypeName: 'בקרה ידנית מגלה'
		}
	},
	controlFrequencyType: {
		[0]: {
			controlFrequencyTypeName: 'לא נבחר'
		},
		[1]: {
			controlFrequencyTypeName: 'הרבה פעמים ביום'
		},
		[2]: {
			controlFrequencyTypeName: 'יומי'
		},
		[3]: {
			controlFrequencyTypeName: 'שבועי'
		},
		[4]: {
			controlFrequencyTypeName: 'חודשי'
		},
		[5]: {
			controlFrequencyTypeName: 'רבעוני'
		},
		[6]: {
			controlFrequencyTypeName: 'שנתי'
		},
		[7]: {
			controlFrequencyTypeName: ' בקרת אפליקציית IT'
		},
		[8]: {
			controlFrequencyTypeName: 'אחרים'
		}
	},
	strategyType: {
		[0]: {
			strategyTypeName: 'לא נבחר'
		},
		[1]: {
			strategyTypeName: 'בקרות'
		},
		[2]: {
			strategyTypeName: 'ביקורת מבססת'
		},
		[3]: {
			strategyTypeName: 'הסתמכות על בקרות'
		},
		[4]: {
			strategyTypeName: 'לא להסתמך'
		}
	},
	aggregateITEvaluationType: {
		[0]: {
			aggregateITEvaluationTypeName: 'לא נבחר'
		},
		[1]: {
			aggregateITEvaluationTypeName: 'תמיכה'
		},
		[2]: {
			aggregateITEvaluationTypeName: 'לא נתמך'
		},
		[3]: {
			aggregateITEvaluationTypeName: 'תמיכה ב-FS & ICFR'
		},
		[4]: {
			aggregateITEvaluationTypeName: 'תמיכה ב-FS בלבד'
		}
	},

	sampleItemFilterLabels: {
		filterTypeOfTags: 'תגים',
		noFiltersAvailable: 'אין מסננים (filters) זמינים',
		filterToolTip: 'סנן',
		clearAll: 'נקה הכל',
		showMore: 'עוד',
		filters: 'מסננים',
		noResults: 'לא נמצאו תוצאות'
	},

	stratergyTypeLabels: {
		[0]: {
			label: 'לא נבחר'
		},
		[1]: {
			label: 'בסקופ הביקורת'
		},
		[2]: {
			label: 'מחוץ לסקופ הביקורת'
		}
	},
	noChangeReasonCommentAvailable: 'לחץ על ערוך סיבה מתוך אפשרויות גלגל השיניים של המסמך כדי להזין את הסיבה לשינוי.',
	changeReasonModalTitle: 'ערוך את הסיבה לשינוי',
	changeReasonModalText: 'בחר את הסיבה לשינוי(ים) שנעשה(ו) במסמך לאחר תאריך הדוח. אם בוצעו שינויים אדמיניסטרטיביים מרובים בחר את השינוי המשמעותי ביותר מהתפריט הנפתח למטה. אם נעשו שינויים אדמיניסטרטיביים וגם שינויים שאינם אדמיניסטרטיביים בחר למטה לא אדמרניסטטיבי.',
	changeReasonUploadModalTitle: 'סיבה להעלאת מסמך',
	changeReasonUploadModalText: 'בחר את הסיבה לשינוי(ים) שנעשה(ו) במסמך לאחר תאריך הדוח. אם בוצעו שינויים אדמיניסטרטיביים מרובים בחר את השינוי המשמעותי ביותר מהתפריט הנפתח למטה. אם נעשו שינויים אדמיניסטרטיביים וגם שינויים שאינם אדמיניסטרטיביים בחר למטה לא אדמרניסטטיבי.',
	changeReasonModalComboPlaceholder: 'בחר',
	changeReasonModalAnnotationText: "תעד את הנסיבות שנתקלו בהן ואת הסיבות להוספת המידע; נהלי ביקורת חדשים או נוספים שבוצעו ראיות ביקורת שהושגו והמסקנות שהוסקו וכן ההשפעה על דוח המבקר שלנו.'",
	changeReasonUploadModalAnnotationText: 'תעד את הנסיבות שנתקלו בהן ואת הסיבות להוספת המידע; נהלי ביקורת חדשים או נוספים שבוצעו ראיות ביקורת שהושגו והמסקנות שהוסקו וכן ההשפעה על דוח המבקר שלנו.',
	changeReasonModalAnnotationPlaceHolder: 'הזן סיבה לשינוי',
	changeReasonModalChangeReasonRequired: 'שנה סיבה לשמירה',
	reasonColumnTitle: 'סיבה',
	shared: 'מְשׁוּתָף',
	shareStatusOwned: 'בבעלות תיק ביקורת זה.',
	shareStatusShared: 'שותף לתוך תיק הביקורת הזה.',
	lastModifiedBy: 'שונה לאחרונה על ידי',
	fileSize: ' | {1} KB',
	openedLabelText: 'נפתח',
	currentlyBeingModifiedBy: 'שונה כעת על ידי',
	OpenGuidedWorkflowDocument: 'פתח את המסמך הזה באמצעות הפעלת EY Canvas FIT',
	submitProfile: 'שלח טופס פרופיל',
	submitProfileFit: 'שלח טופס פרופיל',
	contentUpdateUnAuthorizedTooltipMessage: 'אין הרשאות גישה מספיקות לביצוע עדכון תוכן. פנה למנהל תיק הביקורת שלך ובקש הרשאה ליזום עדכון תוכן.',
	submitProfileValidationErrorMessage: 'ניתן לשלוח את הפרופיל רק לאחר שכל השאלות קיבלו מענה. סנן את השאלות שטרם ניתן להן מענה, השלם אותן והגש שוב. אם הבעיה נמשכת, פנה אל התמיכה הטכנית.',
	pickADate: 'בחר תאריך',

	/* Sign Offs */
	preparerHoverText: 'חתום כמכין',
	reviewerHoverText: 'חתום כסוקר',
	preparerTitle: 'חתימות מכין',
	reviewerTitle: 'חתימות הסוקר',
	deleteHoverText: 'הסר חתימה',
	preparer: 'P',
	reviewer: 'R',
	preparerLabel: 'P',
	reviewerLabel: 'R',
	noSignOffsAvailable: 'אין חתימות זמינות',
	none: 'אף אחד',
	partnerInChargeLabel: 'PIC',
	eqrLabel: 'EQR',
	documentSignoffRequiredLabel: 'נדרשות החתימות של: ',

	relatedDocumentsTitle: 'מסמכים קשורים',
	relatedTasksCount: '{0} משימות קשורות',
	relatedTasksTitle: 'משימות קשורות',
	relateTemporaryFiles: 'קשר קבצים זמניים',
	bodyRelatedDocumentsTitle: 'מסמכים הקשורים לגוף',
	relatedObjectsTitle: 'אובייקטים קשורים',
	relateDocumentsTitle: 'נהל מסמכים קשורים',
	relateDocumentsToBodyTitle: 'הוסף ראיות',
	relateDocumentsDesc: 'בחר מסמכים שישויכו לטופס הקנבס (Canvas Form)',
	relateDocumentsToBodyDesc: 'קשר מסמך מהתקשרות זו או מהתקשרות אחרת בסביבת עבודה זו',
	relateDocumentsToTheBody: 'Relate a document from this engagement.',
	priorPeriodEvidencesToTheBody: 'ראיות מתקופה קודמת הקשורות לתוכן',
	relatedDocunentEngdisabed: 'המסמך איננו משותף בתיק הביקורת הזה.',
	showOnlyRelatedDocuments: 'הצג רק מסמכים קשורים',
	manageDocuments: 'נהל מסמכים',
	documentCount: '{0} מסמך',
	documentsCount: '{0} מסמכים',
	relateDocumentsSearchPlaceholder: 'חיפוש מסמכים',
	overwriteFormDesc: 'בחר טופס כדי להחליף את התגובות עם הנתונים מהטופס הנוכחי של ה- Canvas. שימו לב שטופס הקנבס הנוכחי יועבר לקבצים זמניים.',
	searchFormPlaceholder: 'חפש טופס',
	overwriteLabel: 'החלף',
	confirmOverwriteLabel: 'אשר החלפה',
	confirmOverwriteDesc: "האם אתה בטוח שברצונך להעתיק את התוכן של הטופס'{0}' לטופס'{1}'? תגובות יוחלפו בטופס'{2}', אך ראיות ואובייקטים קשורים לא. יש לשייך מחדש ראיות ואובייקטים רלוונטיים לטופס'{3}' לאחר השלמת ההחלפה. הטופס'{4}' ישמור על שכבות הפרופיל/שכבות ה- GAM (GAM layers) הקיימות שלו, לכן, אם הפרופיל הזה שונה מהטופס'{5}', אנא עיין ונקה את התוכן שהועתק. \n אין לסגור את הדפדפן או לנווט משם בזמן שתהליך ההחלפה מתרחש. לאחר השלמת תהליך ההחלפה, הטופס'{6}' יועבר לקבצים זמניים ותעבור לטופס'{7}'. לא ניתן לבטל פעולה זו.",
	formSelectionRequired: 'בחר טופס להחלפה.',

	open: 'פתח',
	startCoeditMode: 'התחל בעריכה מרובת משתמשים',
	endCoeditMode: 'סיום עריכה מרובת משתמשים',
	openReadOnly: 'פתוח לקריאה בלבד',
	copyLink: 'העתק קישור',
	rename: 'שנה שם',
	viewHistory: 'הצג היסטוריה',
	documentOpenModelLabel: 'המסמך שונה כעת',
	modelUserOpenedTheDocumentText: 'משתמש זה פתח את המסמך',
	modelDocumentOpenedText: 'מסמך זה משתנה כעת על-ידי',
	modelOpenedDocumentConflictText: 'פתיחת המסמך עלול לגרום לקונפליקטים ולכן אנו ממליצים לפתוח לקריאה-בלבד. אם ברצונך להיות עורך מסמך זה הרי ש',
	clickHereEnabledText: 'לחץ כאן.',
	documentOptions: 'אפשרויות מסמך',
	accountDetails: 'פרטי חשבון',

	// DAAS labels
	coEditModeIsEnding: 'עריכה מרובת משתמשים מסתיימת',
	coEditMode: 'עריכה מרובת משתמשים',
	checkInInProgressMessage: "הצ'ק-אין מתבצע. ייתכן שיחלפו עד 20 דקות עד שהמסמך ייעשה צ'ק-אין. אנא רענן לקבלת עדכונים",
	checkInInErrorLabel: "הצ'ק-אין נכשל",
	checkOutInProgressMessage: "ביצוע הצ'ק-אאוט מתבצע. ייתכן שיחלפו עד 20 דקות עד שהמסמך יעשה צ'ק אווט. אנא רענן לקבלת עדכונים",
	checkOutInProgressLabel: "הצ'ק-אאוט מתבצע.",
	checkInInProgressLabel: "הצ'ק-אין מתבצע",
	checkOutInErrorLabel: "הצ'ק-אאוט נכשל.",
	daasErrorMessage: 'לא ניתן להשלים את הפעולה כעת. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk)',
	coEditModeIsStarting: 'עריכה מרובת משתמשים מתחילה',
	daasOpenDocumentWarning: 'Multi-user editing may have been ended by another user. Refresh the page and try again.',
	beingEditedInCoeditMode: 'Being edited in multi-user edit. Edit started by {0}',
	beingEditedInCoeditModeOn: 'on {0}.',
	beingEditedInCoeditModeError: 'Being edited in multi-user edit',
	coEditModeAutomaticallyEnds: 'המסמך נמצא במצב עריכה מרובה משתמשים, שיסתיים באופן אוטומטי תוך {0} ימים.',
	coEditModeAutomaticallyEndsToday: 'המסמך נמצא במצב עריכה מרובה משתמשים, שיסתיים היום.',
	daasStartCollaborationModeWarning: 'Collaboration mode may have been started by another user. Refresh the page and try again.',
	documentCurrentlyBeingModifiedTitle: 'Document currently being modified',
	documentCurrentlyBeingModifiedHeader: 'This document is currently being modified by {0}. This user opened the document',
	documentCurrentlyBeingModifiedBody: 'Starting multi-user edit mode could cause conflicts, so we recommend discussing with {0} before proceeding. Select {1} to start the multi-user edit mode or {2} to return without starting multi-user edit mode.',
	documentEndMultiUserEditingTitle: 'סיום עריכה מרובת משתמשים',
	documentEndMultiUserEditingHeader: 'Warning: Other users may be actively editing this document.  This can be checked by opening the document and seeing if other users are currently in the file. Please confirm that all changes are complete before ending multi-user mode. File changes in multi-user mode may take up to 1 minute to be processed. Therefore, please wait at least 1 minute after exiting the file before ending multi-user mode.',
	documentEndMultiUserEditingBody: 'Select {0} to end the multi-user edit mode or {1} to return without ending multi-user edit mode.',
	startMultiuserEditing: 'Start',

	/* Engagement Comments */
	clear: 'Clear',
	close: 'Close',
	reOpen: 'פתח מחדש',
	reply: 'הוסף תגובה',
	replyLabel: 'ענה',
	unselectComment: 'בטל את הבחירה בתגובה',
	commentText: 'הזן טקסט',
	replyText: 'טקסט תגובה',
	openStatus: 'פתח',
	clearedStatus: 'Cleared',
	closedStatus: 'סגור',
	chartCommentsTitle: 'סקירת הערות',
	showComments: 'הצג הערות',
	noRecordsFound: 'לא נמצאו רשומות',
	noCommentsFound: 'השאיר מענה באמצעות הקלטים למטה. הקצה את ההערה למשתמש וציין עדיפות ותאריך יעד.',
	newComment: 'הוסף תגובה',
	addNoteTitle: 'הוסף הערה',
	editComment: 'ערוך תגובה',
	newReply: 'הוסף תגובה',
	editReply: 'ערוך תשובה',
	commentTextRequired: 'נדרש להזין טקסט תגובה',
	replyTextRequired: 'נדרש להזין טקסט בתשובה',
	myComments: 'ההערות שלי',
	assignTo: 'הוקצה ל',
	theCommentMustBeAssigned: 'ההקצאה נדרשת',
	priorityRequired: 'נדרש תעדוף',
	dueDateRequired: 'נדרש תאריך יעד',
	assignedTo: 'הוקצה ל',
	allComments: 'כל ההערות',
	assignedToMe: 'מוקצות לי',
	unassigned: 'Unassigned',
	draggableCommentsPlaceholder: 'הזן טקסט כדי להוסיף הערה חדשה',
	draggableNotesPlaceholder: 'הזן טקסט כדי להוסיף הערה חדשה',
	enterReply: 'הזן מענה',
	dueDate: 'תאריך יעד',
	commentsAmmount: '{count} הערות',
	singleCommentAmmount: 'הערה {count}',
	eyInternal: 'EY',
	noneAvailable: 'אף אחד לא זמין',

	navHelixProjects: 'חיבורי EY Helix',

	evidence: 'ראיות',
	priorPeriod: 'תקופה קודמת',
	temporaryFiles: 'קבצים זמניים',
	priorPeriodEvidence: 'תקופה קודמת',
	closed: 'כל ההקצאות נסגרו',

	/*Delete*/
	deleteFileTitle: 'מחיקת המסמך',
	deleteFileCloseBtnTitle: 'בטל',
	deleteFileConfirmBtnTitle: 'מחק',
	deleteFileCloseTitle: 'Close',
	deleteFileModalMessage: 'האם אתה בטוח שברצונך למחוק את המסמך?',
	/*Rename*/
	editCanvasformObjects: 'ערוך את המאפיינים ולחץ על <b>שמור</b>.',
	renameFileModalMessage: 'שנה את שם המסמך ולחץ על שמור.',
	renameScreenshotModalMessage: 'שנה את שם צילום המסך ולחץ על אשר.',

	renameFileTitle: 'שנה את שם המסמך',
	fileNameRequired: 'נדרש להזין את שם הקובץ',
	invalidCharacters: 'שם הקובץ אינו יכול לכלול: */:<>\\?|"',
	existingFileName: 'שם הקובץ אינו ייחודי. רענן את הדף או שנה את שם הקובץ בכדי להסיר הודעה זו.',
	maxLengthExceeded: 'שם המסמך לא יכול לחרוג מ- 115 תווים.',

	STEntityProfileBannerMessage: 'בוצעו שינויים באחד או יותר מפרופילי הישות שיגרמו לעדכוני תוכן. נווט חזרה לדף ישויות פרופיל ולחץ על"ייבא תוכן" כדי לקבל את התוכן החדש הרלוונטי לפרופיל של הישות או להחזיר את התשובות למצב קודם.',
	independenceValidationForOwnForm: 'נעשו שינויים בתגובות להצהרת אי התלות, אך לא הוגשו. וודא שהתגובות נשלחו, במידה והשינויים היו בכוונה. אחרת, אם השינויים לא היו בכוונה, יש להחזיר באופן ידני את התשובות לבחירות קודמות.',
	independenceValidationForOthersForm: 'נעשו שינויים בתגובות להצהרת אי התלות, אך לא הוגשו על ידי חבר הצוות. ודא שחבר הצוות סוקר את השינויים ומגיש אותם, במידה והשינויים היו בכוונה.',
	insufficientRightsForIndependenceSubmission: 'אין הרשאות מספיקות לעריכת תוכן. צור קשר עם מנהל ההתקשרות שלך ובקש הרשאה לערוך תוכן.',
	submitIndependenceProfileV2Message: 'אנא סקרו את הפרופיל ואשרו כי התגובות מדויקות. אם כן, אנא חתמו עליו והמשיכו בעבודה על תיק הביקורת.',
	submitIndependenceProfileV2EditMessage: 'לא בוצעו שינויים בפרופיל אשר יגרמו לשינוי בתוכן ההתקשרות. השתמש בדף עדכון התוכן של תיק הביקורת כדי לבצע עדכון תוכן במידת הצורך.',
	insufficientRightsForProfileV2Submission: 'אין לך מספיק הרשאות לעריכת הפרופיל. פנה למנהל תיק הביקורת שלך ובקש הרשאה לערוך את הפרופיל.',
	returnToDashboard: 'חזור ל- Dashboard',
	returnToDashboardFit: 'חזור ל- Dashboard',
	profileV2ChangeNotSubmittedBannerMessage: 'Changes have been made to the Profile that will result in content updates. Submit the Profile to receive the new content or revert the answers to the previous state.',
	independenceChangeNotSubmittedBannerMessage: 'בוצעו שינויים בטופס אי התלות, אותו יש למלא ולשלוח מחדש. שלח את טופס אי התלות או בטל את פעילותו (deactivate) של משתמש זה כדי לנקות את האימות (validation).',
	multiEntityIndividualProfileBannerMessage: 'אין לך מספיק הרשאות לעריכת הפרופיל. פנה למנהל תיק הביקורת שלך ובקש הרשאה לערוך את הפרופיל.',
	scotStrategy: 'Scot Strategy',
	wcgwStrategy: 'Wcgw Strategy',
	itProcessStrategy: 'אסטרטגיית תהליכי IT',

	/*Edit Wcgw*/
	editWcgw: 'עריכת WCGW',
	viewWcgw: 'צפה ב- WCGW',
	editScot: 'ערוך תת תהליך מהותי SCOT',
	viewScot: 'הצג תת תהליך מהותי SCOT',
	showIncomplete: 'הצג Incomplete',
	forms: 'טפסים',
	form: 'טופס',
	comments: 'הערות',
	changes: 'שינויים',
	editHeader: 'ערוך כותרת',
	editSection: 'עריכת מקטע',
	editBody: 'ערוך גוף טקסט',
	editSectionDescription: "ערוך את הפרטים עבור המקטע ולחץ על'שמור'.",
	editHeaderDescription: "ערוך את הפרטים עבור הכותרת ולחץ על'שמור'.",
	editBodyDescription: "ערוך את הפרטים עבור הגוף ולחץ על'שמור'.",
	manageObject: 'נהל פריט',
	relatedObjects: 'אובייקטים קשורים',

	/* Manage body objects */
	bro_manage_WCGWTask_title: 'קשר WCGWs',
	bro_manage_WCGWTask_instructions: 'נהל את ה WCGWs הרלוונטיים',
	bro_manage_WCGWTask_noDataLabel: 'לא נמצאו תוצאות',

	/*Add/Edit ITGC*/
	addITGC: 'הוסף בקרה כללית במערכות המידע (ITGC)',
	addNewITGC: 'הוסף ITGC חדש',
	addExistingITGC: 'הוסף ITGC קיים',
	addITGCDescription: 'הזן תיאור עבור בקרה כללית במערכות המידע (ITGC).',
	itControlNameRequired: 'נדרש שם בקרה כללית במערכות המידע (ITGC)',
	frequencyRequired: 'נדרשת תדירות',
	frequencyITGC: 'בחר תדירות',
	nameITGC: 'שם בקרה כללית במערכות המידע (ITGC) (חובה)',
	iTProcesslabel: 'תהליך IT',
	editITGC: 'ערוך את ה- ITGC',
	editITSP: 'ערוך ITSP (IT-substantive testing procedures)',
	editITGCDescription: 'ערוך את ה-ITGC ואת מאפייני הבקרה המשויכים לה',
	editITSPDescription: 'ערוך את ה-ITSP (IT-substantive testing procedures) ואת מאפייני הבקרה הקשורות',
	viewITGC: 'צפה ב-ITGC',
	viewITSP: 'צפה ב-ITSP (IT-substantive testing procedures)',
	itgcTaskDescription: 'בצע את הבדיקות המתוכננות שלנו על הבקרות הכלליות במערכות המידע (ITGCs) בכדי לקבל ראיות ביקורת נאותות מספיקות בקשר לאפקטיביות התפעולית של הבקרות כאמור לאורך כל תקופת ההסתמכות.',
	/**
	 * Add Edit ITGC
	 */
	addITSPDescription: 'הזן את תיאור ה-ITSP.',
	selectITRisk: 'בחר סיכון IT (חובה)',
	itRiskRequired: 'סיכון IT (חובה)',
	itspNameRequired: 'שם ITSP (חובה)',
	itspTaskDescription: 'התאם אישית את תיאור המשימות הזה כדי לתכנן את האופי, העיתוי וההיקף של נהלי IT מבססים בכדי להשיג ראיות ביקורת מתאימות ומספיקות לכך שסיכוני ה-IT מטופלים באפקטיביות לאורך תקופת ההסתמכות.<br />כאשר נוהל ה-IT המבסס מבוצע החל מתאריך ביניים, תכנן ובצע נהלים כדי להשיג ראיות נוספות לכך שסיכוני ה-IT מטופלים לתקופה המכוסה על ידי נהלי הביניים שלנו עד סוף התקופה.<br />יש לתעד את המסקנה שלנו בקשר לתוצאות ביצוע נהלים מבססים מספיקים בקשר ל IT.',
	itspRequired: 'נדרש שם ITSP',
	selectTestingStrategy: 'תכנן את האופי, העיתוי וההיקף של בדיקות הבקרות (TOCs) שלנו כדי לקבל מספיק ראיות ביקורת מתאימות לכך שהבקרה פועלת באפקטיביות כפי שתוכננה לאורך כל תקופת ההסתמכות על מנת למנוע או לזהות ולתקן הצגות מוטעות מהותיות ברמת מצג ההנהלה. <br /> המסקנה בדבר האפקטיביות התפעולית של הבקרות, על ידי הערכת תוצאות בדיקות הבקרות שלנו, לרבות כאשר הרחבנו את גודל המדגם שלנו וביצענו בדיקות של בקרות מפצות.',
	itControlNameTest: 'בדיקה {0}',

	/*Edit ITControl*/
	editITControl: 'ערוך את ה- ITGC / ITSP',
	viewITControl: 'צפה ב- ITGC / ITSP',

	/*Add/Edit ITRisk*/
	editITRisk: 'ערוך סיכון IT',
	editITRiskDescription: 'ערוך את סיכון ה-IT',
	viewITRisk: 'הצג סיכון IT',
	addITRisk: 'הוסף סיכון IT',
	addITRiskDescription: 'הזן את תיאור סיכון ה-IT',
	selectITProcess: 'בחר תהליך IT (חובה)',
	itRiskName: 'סיכון IT',
	itRiskNameRequired: 'סיכון IT (חובה)',
	riskNameRequired: 'נדרש סיכון IT',
	processIdRequired: 'נדרש תהליך IT',
	itProcessRequired: 'תהליך IT (חובה)',
	hasNoITGC: 'אין ITGCs המטפלים בסיכון ה-IT',

	/*Edit Risk*/
	editRisk: 'ערוך סיכון',
	viewRisk: 'צפה בסיכון',

	/*Edit Control*/
	editControl: 'ערוך בקרה',
	viewControl: 'צפה בבקשה',
	scotRelatedControls: 'בקרות הקשורות ל',
	applicationControl: 'בקרה אפליקטיבית',
	iTDependentManualControl: 'IT Dependent Manual Control',
	noAapplicationControlAvailable: 'אין בקרות אפליקטיביות',
	noITDependentManualControlAvailable: 'אין בקרות ידניות תלויות מחשב (ITDM)',
	isIPEManuallyTested: 'החלק האוטומטי של בקרת ITDM זו  רק השימוש בדוח/ות שנוצרו על ידי המערכת שנבדקים באופן מבסס',

	/*Edit ITProcess*/
	editITSOProcess: 'ערוך תהליך IT/SO',
	viewITSOProcess: 'הצג תהליך IT/SO',

	/*Edit ITApplication*/
	viewITAppSO: 'הצג  IT App/SO',
	editITAppSO: 'ערוך  IT App/SO',
	strategy: 'אסטרטגיית תת התהליך המהותי (SCOT)',
	nameRequired: 'נדרש שם',
	name: 'שם',

	/*Snap shot*/
	currentVersion: 'גרסה נוכחית',
	compareVersion: 'בחר גירסה להשוואה',
	snapshotVersionNotAvailable: 'אין גירסאות זמינות להשוואה',
	snapshots: 'צילומי מסך',
	sharedFormWarning: "זהו טופס Canvas משותף. האובייקטים והראיות קיימים בהתקשרות המקורית ולא יתווספו לתיק הביקורת הזה עם ביטול הקישור. לפרטים נוספים ראה <a style='color: #467cbe' href='https://live.atlas.ey.com/#library/104/p/SL33184174-396647/C_33404446/C_38129691' target='_blank'>enablement here</a>.",
	fullView: 'תצוגה מלאה',
	defaultView: 'תצוגת ברירת מחדל',
	print: 'הדפס',
	version: 'גירסה',
	navigationUnavailable: "הניווט אינו זמין בתצוגת עקוב אחר שינויים ומאפיינים. הצג את'שאלות ותשובות' כדי לאפשר שוב ניווט.",
	snapshotUpdate: 'מעודכן',
	snapshotNew: 'חדש',
	snapshotRemoved: 'הוסר',
	snapshotRollforward: 'נוצר בזמן של Roll-Forward',
	snapshotRestore: 'נוצר בזמן השחזור',
	snapshotCopy: 'נוצר בזמן העתקה',

	/*Special Body*/
	priorPeriodAmount: 'הסכום משנה קודמת',

	// Helix special body:
	helixScreenshotListLoading: 'טוען צילומי מסך ...',
	helixScreenshotLoading: 'טוען תמונת צילום מסך ...',
	helixScreenshotDeleting: 'מוחק צילום מסך ...',
	helixNotesLoading: 'טוען tickmarks...',
	helixNotesBoundingBoxShow: 'הצג הערות',
	helixNotesBoundingBoxHide: 'הסתר הערות',
	helixNoteReferenceNumber: '#',
	helixNoteReferenceNumberPlaceholder: 'הזן מספר סימוכין',
	helixNoteText: 'הערה',
	helixNoteTextPlaceholder: 'הזן טקסט ל- tickmark',
	helixNoteAnnotate: 'ביאור',
	helixNoteAnnotateMessage: 'בחר אזור בצילום המסך להערה אשר את ההערה ולחץ על סימן הביקורת (checkmark) לשמירה.',
	helixRemoveAnnotation: 'מחק ביאור',

	/* User lookup body */
	userLookupInstructionalText: 'הזן שם או דוא"ל והקש אנטר כדי לראות תוצאות.',
	userLookupShortInstructionalText: 'הזן שם או דואר אלקטרוני והקש Enter',

	/*Guidance*/
	guidance: 'הנחיות',
	noIncompleteBodies: 'בחר כותרת עליונה או קטע מתפריט הניווט כדי להציג תוכן',
	noUnresolvedComments: 'בחר כותרת עליונה או קטע מתפריט הניווט כדי להציג תוכן',
	addComment: 'הוסף תגובה',

	/*Independence*/
	otherFormIndependenceMessage: 'התוכן של טופס אי התלות עודכן והמשתמש לא התחבר שוב מאז העדכון. כתוצאה מכך תגובות מסוימות עשויות להיות לא שלמות. סטטוס אי התלות הקודם נשמר לעיון.',
	override: 'לעקוף',
	grantAccess: 'הענק גישה',
	denyAccess: 'דחה גישה',
	overrideSmall: 'לעקוף',
	grantAccessSmall: 'הענקת גישה',
	denyAccessSmall: 'מניעת גישה',
	status: 'סטטוס',
	undefined: 'לא מוגדר',
	incomplete: 'לא הושלם',
	noMattersIdentified: 'לא זוהו סוגיות',
	matterIdentifiedPendingAction: 'זוהתה סוגייה - הפעולה הושהתה',
	matterResolvedDeniedAccess: 'נפתרה הסוגייה - נחסמה גישה',
	matterResolvedGrantedAccess: 'נפתרה הסוגייה - הוענקה גישה',
	notApplicable: 'לא ישים',
	restored: 'שוחזר',
	overridden: 'לעקוף (Overridden)',
	priorNoMattersIdentified: 'לפני - לא זוהו סוגיות',
	priorMatterIdentifiedPendingAction: 'לפני - זוהתה סוגיה - פעולה בהמתנה',
	priorMatterResolvedGrantedAccess: 'לפני - הסוגיה נפתרה - הוענקה גישה',
	priorMatterResolvedDeniedAccess: 'לפני - הסוגיה נפתרה - הגישה נחסמה',
	byOn: 'מאת {0} ב',
	byLabel: 'על ידי',
	onLabel: 'על',
	modifiedBy: 'שונה על ידי',
	reason: 'סיבה',
	submit: 'שלח',
	submitTemplate: 'שלח תבנית',
	independenceHoverText: 'תפקידך חייב להיות שותף אחראי על הביקורת (PIC) או אקזקיוטיב דיירקטור (MD) בכדי להעניק למנוע או לעקוף גישה עבור משתמש זה.',
	enterRationaleText: 'הזן רציונל עבור',
	enterRationalePlaceholderText: 'הזן רציונל',
	requiredRationaleText: 'רציונל (חובה)',
	rationaleTextRequired: 'נדרש רציונל',

	sharedExternalWarning: 'טופס זה שותף באמצעות פורטל הלקוחות של ה- Canvas ונגיש לעיון על ידי חברי הצוות החיצוניים (external team members). הזן רק תגובות והערות שיש לשתף עם חברי הצוות החיצוניים.',
	independenceViewTemplateMessage: 'טופס זה משמש כתבנית לבירור סטטוס אי התלות האישי של כל חבר צוות. <br /> בעת השלמת בירור אי התלות, ישנן מספר שאלות הנוגעות לדרישות אי התלות החלות על הישות בביקורת, עליהן כל חבר צוות חייב לתת מענה. בחר את התשובות המתאימות לשאלות אלו. התשובות יסונכרנו לבירור סטטוס אי התלות האישי של כל חבר צוות. אם חבר צוות בחר בתגובה אחרת, הוא יצטרך לאשר מחדש את אי התלות כאשר הם ייכנסו מחדש להתקשרות. אם לא יכנסו שוב להתקשרות, אזי סטטוס אי התלות הקודם שלהם ותגובותיהם יישמרו. <br /> רק משתמשים מורשים יכולים לבצע שינויים בתבנית אי התלות. דבר עם מנהל תיק הביקורת. יש להגיש את כל השינויים שבוצעו, גם אם הם בוטלו באופן ידני לפני הארכיב.',

	/**
	 * FORM OBJECTS: SCOT-WCGW-CONTROL
	 */
	fo_instructionalText: 'בחר את האובייקטים אותם מתעד טופס הקאנבס',
	fsro_instructionalText: 'ניהול האובייקטים הקשורים לפסקה זה',
	relObj_title_risk: 'סיכונים',
	relObj_title_riskType: 'סוג הסיכון',
	fo_showOnlyRelated: 'הצג אובייקטים קשורים בלבד',
	scotsCount: '{0} תתי תהליכים מהותיים (SCOTs)',
	wcgwsCount: '{0} WCGWs',
	itsoCount: '{0} יישומי IT / לשכת שירות',
	controlsCount: '{0} בקרות',
	itControlsCount: '{0} בקרות IT',
	itGcCount: '{0} ITGCs',
	itSpCount: '{0} ITSPs',
	itProcessesCount: '{0} תהליכי IT',
	risksCount: '{0} סיכונים Risks',
	accountsCount: '{0} סעיפים',

	stEntitiesCount: '{0} ישויות',

	componentsCount: '{0} ישויות מוחזקות',
	view: 'הצג',
	searchByScotName: 'חיפוש לפי שם תת תהליך מהותי SCOT',
	searchByWcgwName: 'חפש לפי שם WCGW',
	searchByITSOAppName: 'חיפוש לפי שם יישום IT/SO',
	searchByControlName: 'חיפוש לפי שם בקרה',
	searchByItControlName: 'חיפוש לפי שם בקרת IT',
	searchByItProcessName: 'חיפוש לפי שם תהליך IT',
	searchByRiskName: 'חפש לפי שם סיכון Risk',
	searchByAccountName: 'חפש לפי שם חשבון',
	searchBySTEntityName: 'חפש לפי שם ישות',
	searchByEstimateName: 'חפש לפי שם אומדן',
	searchByComponentName: 'חפש לפי שם ישות מוחזקת',
	noScotsAvailable: 'אין תתי תהליכים מהותיים SCOTs זמינים בתיק ביקורת זה.',
	noRisksAvailable: 'לא קיימים סיכונים בהתקשרות זו.',
	noControlsAvailable: 'אין בקרות זמינות בתיק ביקורת זה.',
	noItControlsAvailable: 'אין בקרות IT זמינות בתיק ביקורת זה.',
	noItProcessesAvailable: 'אין תהליכי IT זמינים בתיק ביקורת זה.',
	noItApplicationsAvailable: 'אין יישומי IT זמינים בתיק ביקורת זה.',
	noAccountsAvailableLabel: 'אין חשבונות זמינים בהתקשרות זו.',
	noObjectsRelatedToForm: 'אין אובייקטים שקשורים לטופס קאנבס זה',
	noDocumentControlsAvailable: 'אין בקרות המשויכות במסמך זה.',
	noDocumentScotsAvailable: 'לא משויכים תתי תהליכים מהותיים (SCOTs) למסמך זה.',
	noSTEntitiesAvailable: 'אין ישויות זמינות בהתקשרות זו.',
	noComponentsAvailable: 'אין ישויות מוחזקות זמינות בהתקשרות זו.',
	editObjectDescription: 'ערוך את השיוך של האובייקטים לטופס זה',
	editObjectsLabel: 'ערוך אובייקטים',
	noITGCsOrITSPsHaveBeenIdentified: 'לא זוהו ITGCs או ITSPs',
	noItProcessIdentified: 'לא זוהה תהליך IT',
	noControlsIdentified: 'לא זוהו בקרות',
	noRelatedRisksIdentified: 'לא זוהו סיכונים משמעותיים או סיכוני הונאה קשורים',
	noItApplicationsIdentified: 'לא זוהו יישומי IT',
	noSCOTIdentified: 'לא זוהו תתי תהליכים מהותיים SCOTs',
	noWCGWIdentified: 'לא זוהו WCGWs',
	maxLimitLabel: 'נבחר המספר המירבי של אובייקטים.',
	minLimitLabel: 'נבחר המספר המינימלי של אובייקטים.',

	relatedITAppsTitle: 'תהליכי IT ויישומי IT קשורים',
	relatedWCGWTasksTitle: 'WCGWs ומשימות קשורות',
	noRelatedTasks: 'אין משימות קשורות',
	noRelatedWcgw: 'אין WCGWs קשורים',
	noRelatedControls: 'אין בקרות קשורות',
	controlRelatedRisksTitle: 'בקרות וסיכונים קשורים',
	sCOTRelatedRisksTitle: 'SCOTs וסיכונים קשורים',
	scotRelatedItApp: 'יישומי IT המקושרים ל-SCOT',
	relatedItApps: 'אפליקציות IT קשורות',
	relatedRisksTitle: 'סיכונים קשורים',
	relatedItRisksItProcessesTitle: 'בקרות ללא יישומי IT קשורים',
	testingTitle: 'בודק',
	strategyTitle: 'אסטרטגיית תת התהליך המהותי (SCOT)',
	yes: 'כן',
	no: 'לא',
	noRelatedRisks: 'לא מקושרים סיכונים משמעותיים או סיכוני הונאה',
	closeAllComments: 'סגור את כל ההערות',
	closeComments: 'סגור הערות',
	closeCommentsDescription: 'כל התגובות הפתוחות ואלו שנוקו (cleared) ייסגרו. האם אתה בטוח שברצונך לסגור את כל התגובות עבור {0} זה?',
	addCanvasFormDigital: 'דיגיטלי',
	addCanvasFormCore: 'ליבה',
	addCanvasFormNonComplex: 'לא מורכב',
	addCanvasFormComplex: 'מורכב',
	addCanvasFormListed: 'ברשימה',
	addCanvasFormGroupAudit: 'ביקורת קבוצתית',
	addCanvasFormPCAOBFS: 'PCAOB-FS',
	addCanvasFormPCAOBIA: 'PCAOB-IA',
	addCanvasFormStandards: 'תקנים',
	addCanvasFormLanguage: 'אנגלית (ארהב)',
	addCanvasFormNoResultFound: 'לא נמצאו תוצאות',
	addCanvasFormStandardsNotSelectedMessage: 'תקן הוא שדה חובה',
	addCanvasFormLanguageNotSelectedMessage: 'שפה היא שדה חובה',

	/* Confidentiality */
	confidentialityPlaceholder: 'בחר סודיות',
	confidentiality: 'סודיות',
	confidentialityTitle: 'מסמך סודיות',
	confidentialityText: 'הגדר את רמת הגישה הנדרשת לפתיחת מסמך זה. רמות הגישה נקבעות על ידי מנהלי ההתקשרות בדף ניהול צוות (manage team). אם מסמך זה הוגדר כבר כסודי, הרי שרק מי שיכול לפתוח את המסמך לערוך בו שינויים.',
	confidentialityNotOpenable: 'לא ניתן לפתוח את המסמך מכיוון שההרשאות שלך בתיק הביקורת אינן מספקות. רמות הגישה נקבעות על ידי מנהלי ההתקשרות בדף ניהול צוות (manage team).',
	confidentialityTargetNotOpenable: 'ניתן לפתוח מסמכים חסויים רק ממקור תיק הביקורת (source engagement).',
	backToCCP: 'חזרה לפרוטל הלקוחות - EY Canvas Client Portal',
	guidanceMessageBackToCCP: 'לאחר מילוי טופס זה, חזור לפורטל הלקוחות של EY Canvas (EY Client Portal)  והגש את הבקשה ל- EY.',
	noProfileInformationFound: 'לא נמצא מידע על פרופיל. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה אל התמיכה הטכנית.',
	confirmUpdate: 'אשר את העדכון',
	keepVersion: 'שמור על גרסה זו',
	conflictDescription: '{0} ערך את הטקסט הזה {1} מאז שנפתח. בחר את הגרסה העדכנית שיש לשמור.',
	currentConflictVersion: 'גרסה נוכחית',
	serverConflictVersion: 'גרסת שרת',
	conflictShowChanges: 'הצג עקוב אחר שינויים',
	sectionViewTrackChangesDropdownPlaceholder: 'בחר גרסה',
	verifyingIndependence: 'בודק סטטוס אי תלות, אנא המתן.',
	creatingIndependenceForm: 'יצירת טופס אי תלות.',
	meCallFailed: 'אחזור פרטי המשתמש נכשל. אנא רענן את הדף ונסה שוב. אם הבעיה נמשכת אנא פנה אל התמיכה הטכנית.',
	getUserByIdFailed: 'אחזור סטטוס אי התלות של המשתמש נכשל. אנא רענן את הדף ונסה שוב. אם הבעיה נמשכת אנא פנה אל התמיכה הטכנית.',
	independenceFormCreationFailed: 'יצירת טופס אי תלות של המשתמש נכשלה. אנא רענן את הדף ונסה שוב. אם הבעיה נמשכת אנא פנה אל התמיכה הטכנית.',
	gettingProfile: 'מקבל מידע על פרופיל אנא המתן.',
	invalidDocumentId: 'מזהה המסמך אינו חוקי. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	returnToEditMode: 'חזור למצב עריכה',
	saveAndCloseButtonTitle: 'שמור וסגור',
	formCreationFailed: 'יצירת הטופס נכשלה. אנא רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',

	/*Sign-off requirements*/
	signOffRequirements: 'דרישות חתימה',
	signoffRequirementsModalTitle: 'דרישות חתימה',
	signoffRequirementsModalDescription1: 'התאם את דרישות החתימה עבור מנהלים במסמך זה למטה.',
	signoffRequirementsModalDescription2: 'לא ניתן להתאים חלק מדרישות החתימה מכיוון שהן נדרשות על ידי EY Canvas.',
	signoffRequirementsModalSaveLabel: 'שמור',
	signoffRequirementsModalCancelLabel: 'בטל',
	signoffRequirementsModalCloseLabel: 'Close',
	signoffRequirementsModalPICLabel: 'PIC',
	signoffRequirementsModalEQRLabel: 'EQR',

	/*<Ares>*/
	/* View changes */
	viewChanges: 'הצג שינויים',
	viewChangesModalTitle: 'הצג שינויים',
	documentModificationAlert: 'פעילות זו שונתה לאחרונה על ידי',
	dismiss: 'לשחרר',

	/*Task List*/
	aresPageTitle: 'הפעלת EY Canvas FIT',
	aresPageSubtitle: 'השלם את השלבים שלהלן עם המידע המבוקש לגבי הביקורת שלך.',
	summary: 'סיכום',
	aresNoDocumentFound: 'אין מידע זמין אחר לפעילות שנבחרה',
	taskSubTitleNoValue: 'אין תיאור זמין',
	mainActivities: 'פעילויות עיקריות',
	unmarkComplete: 'בטל סימון הושלם',
	markCompleteTitleTip: 'סמן כהושלם',
	disableMarkCompleteTitleTip: 'ודא שכל המסמכים הקשורים חתומים על ידי לפחות מכין אחד וסוקר אחד כדי לסמן שהפעילות הזו הושלמה',
	/*Activity Summary*/
	activitySummary: 'סיכום פעילות',
	selectedAnswers: 'התשובות שנבחרו',
	allAnswers: 'כל התשובות',
	incompleteResponses: 'מענה שלא שולם',
	previous: 'הקודם',
	next: 'הבא',
	viewAsLabel: 'הצג כ',
	rolePreparerLabel: 'מכין (Preparer)',
	roleDetailedReviewerLabel: 'סוקר בסקירה מפורטת (detailed reviewer)',
	roleGeneralReviewerLabel: 'סוקר כללי',
	roleEQRLabel: 'EQR',
	/*Simple Helix*/
	helixPlaceholder: 'יש צורך בהנחיית סוג 7 עם מדריך הנחיות לצילומי מסך של helix.',
	noNotesAvailable: 'לא נוצרו tickmarks',
	addScreenshot: 'הוסף צילום מסך',
	replaceScreenshot: 'החלף צילום מסך',
	replaceFrameDescription: '`עיין בניתוח הבא ולחץ על החלף כדי להחליף את צילום המסך הקיים.`',
	addNote: 'הוסף tickmark',
	notes: 'tickmarks',
	noScreenshotsAvailable: 'לחץ על {viewDataAnalytic} כדי להתחיל',
	viewDataAnalytic: 'הצג ניתוח נתונים',
	/* Delete modal Helix screenshot*/
	modalTitle: 'מחק צילום מסך',
	sureDeleteBeforeName: 'האם ברצונך למחוק את צילום המסך?',
	sureDeleteAfterName: 'עם מחיקת צילום המסך כל ה- tickmarks המשויכים גם יימחקו ולא ניתן לבטל פעולה זו.',

	/*uploadDocument body type */
	relateExistingDocuments: 'קשר מסמכים קיימים',
	fromEngagementOr: 'מתיק ביקורת זה / מתיקי ביקורת אחרים או',
	browse: 'חפש',
	toUpload: 'להעלות',
	signoffs: 'Sign-offs',
	addDocument: 'הוסף מסמכים',
	uploadDocument: 'העלה מסמך',
	relateDocument: 'קשר מסמכים קיימים',
	generateAccountRiskAssessmentPackage: 'צור ALRA קבוצתי',
	relateDocumentsToBodyAresTitle: 'קשר מסמכים',
	discardLabel: 'Discard',
	uploadDocumentLabel: 'העלה מסמך',
	confirm: 'אשר',
	duplicateDocumentHeader: 'מסמך אחד או יותר עם אותו שם כבר קיים בהתקשרות זו (כראיות ביקורת או כקבצים זמניים).',
	duplicateDocumentInstruction: "בחר'החלף' כדי להעלות את המסמך ולהחליף את הקובץ הקיים או'התעלם (Discard)' לביטול. אם הקובץ הקיים הזה נמצא בראיות הביקורת המסמך יעלה לראיות הביקורת. אם הקובץ הקיים נמצא בקבצים זמניים המסמך יעלה לקבצים זמניים.",
	maxUploadFilesError: 'המערכת יכולה להעלות עד 10 מסמכים בו-זמנית',
	/*</Ares>*/
	noTaskRelatedToThisDocument: 'אין משימה הקשורה למסמך זה',
	uncheckTrackChangesToSave: 'Unselect the track changes option to save',
	reviewRoleCloseCommentsTitle: 'הערות (comments) לא פתורות',
	reviewRoleCloseCommentsDesc: 'ישנן הערות לא פתורות שיש לטפל בהן. השתמש במסנן כדי לזהות הערות לא פתורות בקלות.',

	/*Document Upload - PIC/EQR Required Body type*/
	requirementDetails: 'פרטי הדרישה',

	//Risk Factors
	riskFactor: 'אירוע ותנאי רלוונטיים/סיכון להצגה מוטעית',
	manageRisk: 'ניהול סיכונים',
	noRiskFactors: 'לא זוהו אירועים ותנאים רלוונטיים / סיכונים להצגה מוטעית',
	relateRiskFactorsToRisks: 'קבע את רמת השפעה של אירועים ותנאים',
	riskType: 'סוג הסיכון',
	relateToRisk: 'קשור לסיכון',
	noRisksIdentified: 'לא זוהו סיכונים',
	notDefined: 'לא מוגדר',
	selectValidRiskType: 'אנא בחר סוג סיכון חוקי',
	newRisk: 'הוסף סיכון חדש',
	notAROMM: 'לא מהווה סיכון להצגה מוטעית מהותית',
	describeRationale: 'תאר רציונל',
	noRisksIdentifiedForTheSpecificRiskType: '{0} לא זוהו',
	addAnAccount: 'שייך סעיף נוסף',
	selectAnAccount: 'בחירת סעיף',
	noAccountsHaveBeenIdentified: 'לא זוהו סעיפים',
	accountSelected: 'סעיף',
	statementType: 'סוג דוח',
	selectAssertions: 'בחר מצג הנהלה אחד או יותר',
	noAssertionsIdentifiedForAccount: 'לא זוהו מצגי הנהלה עבור סעיף זה',
	relatedAssertions: 'מצגי הנהלה מקושרים',
	editAccount: 'ערוך סעיף וגילוי',
	addNewDescription: 'הוסף תיאור חדש',
	editRiskFactorDescription: 'ערוך תיאור',
	enterRiskFactorDescription: 'הזן תיאור עבור האירוע והתנאי הרלוונטי/סיכון להצגה מוטעית',
	riskFactorDescriptionRequired: 'נדרש תיאור אירוע או תנאי רלוונטי / סיכון של הצגה מוטעית',
	riskFactorDescription: 'תיאור אירוע או תנאי רלוונטי / סיכון להצהרה מוטעית',
	createNewAccount: 'ליצור סעיף חדש',
	createAccountLabel: 'החשבון נוצר בהצלחה {0}',
	updateAccountLabel: 'העריכות נשמרו בהצלחה בחשבון {0}',
	deleteAccountLabel: 'נמחק בהצלחה',
	significanceLabel: 'איפיון סעיף',
	provideRationale: 'אנא ספק הסבר על מנת לשמור את בחירתך',
	clearRiskSignificance: 'נקה את משמעות ותיאור סיכון',
	clearSignificance: 'נקה את המשמעותיות ואת התיאור',

	// Account Summary
	unavailable: 'אינו זמין',
	notAvailable: 'לא זמין',
	fraudRisk: 'סיכון תרמית',
	significantRisk: 'סיכון משמעותי (Significant Risk)',
	significantRiskIndicator: 'SR',
	fraudRiskIndicator: 'FR',
	inherentRisk: 'ROMM',
	inherentRiskIndicator: 'ROMM',
	prioryearbalance: 'יתרת תקופה קודמת: ',
	accounts: 'סעיפים',
	accountsOther: 'סעיף - אחר',
	accountsSignDis: 'גילוי משמעותי',
	xMateriality: 'במכפלות של PM',
	xTEMateriality: 'x TE',
	estimateAssociated: 'אומדן משויך',
	designation: 'ייעוד',
	noAccountshasbeenIdentified: 'לא זוהו סעיפים.',
	noAccountsOtherhasbeenIdentified: 'לא זוהו סעיפים אחרים.',
	noAccountsSigfhasbeenIdentified: 'לא זוהו גילויים משמעותיים.',
	addOtherAccounts: 'הוסף סעיף - אחר',
	addSignificantDisclosure: 'הוסף גילוי משמעותי',
	pydesignation: 'ייעוד קודם: ',
	notapplicable: 'לא רלוונטי',
	noApplicable: 'לא ישים',
	changeDesignationMessage: 'אתה עומד לשנות את ייעוד הסעיף',
	changeDesignationTitle: 'שינוי ייעוד הסעיף',
	continue: 'המשך',
	currentYearBalance: 'תקופה נוכחית',
	currentPeriodBalance: 'תקופה נוכחית',
	priorYear: 'שנה קודמת',
	priorYearDesignation: 'ייעוד מתקופה קודמת',
	priorYearEstimation: 'אומדן תקופה קודמת',
	priorPeriodChange: '% שינוי',
	analytics: 'ניתוח',
	notROMMHeader: 'לא מהווה סיכון להצגה מוטעית מהותית',
	manageEyCanvasAccounts: 'ניהול סעיפים ב- EY Canvas',
	manageAccountMapping: 'ניהול מיפוי הסעיפים (Account mapping)',
	manageAccountMappingCloseButton: 'השתמש בכפתור בתחתית העמוד כדי לסגור.',
	manageAccountMappingToasterMessage: 'לא ניתן היה להתחבר ל- EY Helix. רענן את הדף ונסה שוב. אם הבעיה ממשיכה, אנא פנה לתמיכה הטכנית.',
	inherentRiskTypeChangeMessage: 'על ידי שינוי מצגי ההנהלה של הסעיף מרלוונטיות ללא רלוונטיות, קישורים מסוימים ב-Canvas, לרבות קישורי סיכונים WCGWs למצגי הנהלה -Assertions, יוסרו ונהלי הביורת המבססים העיקריים (PSPs) יומרו להיות OSPs. לחץ על"אשר" אם תרצה להמשיך. לחץ על"ביטול" בכדי לחזור ללא שינויים.',

	analyticsIconDisabled: 'EY Helix אינו מופעל עבור תיק הביקורת שלך.',
	//Estimate
	estimatesTitle: 'אומדנים',
	relatedAccount: 'סעיפים קשורים',
	relatedAccounts: 'סעיפים קשורים',
	currentBalance: 'יתרה נוכחית',
	priorBalance: 'יתרה קודמת',
	unrelateEstimates: 'חשבון שאינו כרוך באומדן',
	manageEstimates: 'נהל אומדנים',
	noEstimatesCreated: 'לא נוצרו אומדנים.',
	createEstimates: 'צור אומדן',
	estimateStarted: 'להתחיל',
	createEstimateDocument: 'יצירת תיעוד האומדן',
	noEstimatesFound: 'לא זוהו אומדנים',
	relateEstimateLink: 'קשר אומדנים',

	//Journal Source
	noJournalEntrySourcesAreAvailable: 'אין מקורות זמינים לפקודות היומן.',
	jeSourceName: 'מקור פקודת היומן',
	jeSourceNameTooltip: 'מקור פקודת היומן',
	changeInUse: 'שינוי בשימוש',
	grossValue: 'שווי ברוטו של עסקאות קשורות',
	relatedTransactions: 'מספר העסקאות הקשורות',
	relevantTransactions: 'רלוונטי לתתי תהליכים מהותיים (SCOTs)',
	expandAll: 'הרחב הכל',
	collapseAll: 'צמצם הכל',
	descriptionLabel: 'ספק תיאור קצר של מטרת מקור (source) זה',
	jeSourceTypesLabel: 'האם רישומי פקודות היומן נרשמים דרך מקור (source) זה, מערכת שנוצרה או באופן ידני?',
	journalEntries: 'האם נעשה שימוש במקור (source) זה לרישום פקודות יומן לא סטנדרטיות (כלומר עסקאות לא שגרתיות, חריגות או התאמות)?',
	identifySCOTsLabel: 'זהה את תתי התהליכים המהותיים (SCOTs) המשויכים למקור ה-JE (בחר את כל המתאימים)',
	systemGeneratedLabel: 'המערכת נוצרה',
	manualLabel: 'תדריך',
	bothLabel: 'שני',
	accountEstimateLabel: 'EST',
	addSCOTLabel: 'הוסף את תת התהליך המהותי (SCOT)',
	newSCOTLabel: 'SCOT חדש',
	addSCOTModalDescription: 'אתה יכול ליצור תת-תהליכים משמעותיים (SCOTs). השינויים יחולו לאחר השמירה.',
	scotnameRequired: 'נדרש שם תת-תהליך משמעותי (SCOT).',
	scotCategoryRequired: 'נדרשת קטגוריית תת-תהליך משמעותי (SCOT).',
	estimateRequired: 'נדרשת הערכה.',

	jeSourcesLabel: 'מקורות JE',
	jeSourcesToSCOTs: 'מקורות JE עבור תת-תהליך משמעותי (SCOT)',
	scotsToSignificantAccounts: 'תת-תהליכים משמעותיים (SOCTs) עבור חשבונות משמעותיים',

	//Modal common labels.
	modalCloseTitle: 'Close',
	modalConfirmButton: 'שמור שינויים',
	modalCancelTitle: 'בטל',
	modalSave: 'שמור',
	modalSaveAndClose: 'שמור וסגור',
	modalSaveAndAdd: 'שמור והוסף',
	modalSaveAndCreateAnother: 'שמור וצור אחד נוסף',

	//Add & Manage Risks
	addNewRiskModalTitle: 'הוסף סיכון חדש',
	manageRisksListModalTitle: 'ניהול סיכונים',
	riskInfoMessage: 'השינויים יחולו מיד לאחר השמירה.',
	risksListInstructionalText: 'באפשרותך לערוך ולמחוק סיכונים קיימים. באפשרותך גם להוסיף אחד חדש, במידת הצורך.',
	risksListEmptyArray: 'אין סיכונים זמינים. הוסף סיכון חדש בכדי להתחיל',
	addNewRiskButtonLabel: 'הוסף סיכון חדש',
	labelRiskName: 'שם הסיכון',
	riskDescriptionLabel: 'תיאור הסיכון',
	selectRiskType: 'סוג הסיכון',
	requiredRiskName: 'נדרש שם סיכון',
	requiredRiskType: 'נדרש סוג סיכון',
	deleteRiskTrashLabel: 'מחק סיכון',
	undoDeleteRiskTrashLabel: 'בטל מחיקה',
	notARommLabel: 'אין סיכון להצגה מוטעית מהותית',
	identifiedRiskFactors: 'מזוהים אירועים/תנאים/סיכונים של הצגה מוטעית, סיכונים להצגה מוטעית מהותית, סיכונים משמעותיים וסיכוני הונאה.',
	noneIdentified: 'לא זוהה',
	countUnassociatedRisk: "אירועים/תנאים/סיכונים להצגה מוטעית אינם קשורים/לא מסומנים כ'לא סיכון להצגה מוטעית מהותית/סיכון להצגה מוטעית מהותית'.",

	// Bar Chart / Account Summary
	accountsTotal: 'סה"כ סעיפים {0}',
	accountSummary: 'סיכום הסעיפים',
	allAccounts: 'כל הסעיפים',
	significantAccountsBarChart: 'סעיפים מהותיים',
	limitedAccounts: 'סעיפים בעלי סיכון מוגבל (Limited risk accounts)',
	insignificantAccounts: 'סעיפים לא מהותיים (Insignificant accounts)',
	noAccountsHasBeenIdentifiedBarChart: 'לא זוהו {0}.',
	selectedTotalAccountsCounter: 'סעיפים {0}/{1}',
	identifyInsignificantAccounts: 'זהה סעיפים לא מהותיים (Insignificant accounts)',
	identifySignificantAccounts: 'זיהוי סעיפים מהותיים',
	identifyLimitedAccounts: 'זיהוי סעיפים בסיכון מוגבל (limited risk accounts)',
	preInsigniAccounts: 'סעיף לא מהותי (Insignificant account) שזוהה בעבר ובתקופה הנוכחית הוא כבר לא נמוך מה- TE.',
	nonDesignatedInsignificant: 'לא ניתן להגדיר סעיף זה כלא מהותי. לחץ על התפריט לעדכון הסעיף כדי לשנות את הסעיף ללא מהותי.',
	tolerableError: 'הטעות הנסבלת (TE) אינה זמינה. עדכן את המהותיות ונסה שוב.',
	documentContainerLabel: 'Document',
	clickcreateformtogenerate: 'לחץ על {0} כדי ליצור תיעוד לסעיף בסיכון מוגבל (limited risk account)',
	createform: 'צור טופס',
	createLimitedRiskAccountDocumentation: 'צור תיעוד לסעיף בסיכון מוגבל',
	limitedAccountDocumentName: 'תיעוד סעיף בסיכון מוגבל (limited risk account)',

	//Modal Confirm Switch account
	modalSwitchTitle: 'שינויים שלא נשמרו',
	modalConfirmSwitch: 'אשר',
	modalConfirmSwitchDescription: 'השינויים לא נשמרים ויאבדו אם תחליט להמשיך. האם אתה רוצה להמשיך?',

	//Modal Edit Account
	manageAccountsModalTitle: 'ניהול סעיפים ב- EY Canvas',
	editAccountLinkLabel: 'ערוך סעיף',
	editAccountInstructionalText: 'אתה יכול לערוך או למחוק סעיפים קיימים של EY Canvas או ליצור סעיפים חדשים. השינויים יחולו לאחר השמירה.',
	selectAnAccountLabel: 'בחר סעיף',
	accountNameLabel: 'שם סעיף',
	accountLabel: 'סעיף',
	accountDesignationLabel: 'ייעוד הסעיף',
	accountStatementTypeLabel: 'סוג דוח',
	accountRelatedAssertionsLabel: 'מצגי הנהלה מקושרים',
	accountHasEstimateLabel: 'האם הסעיף מושפע מאומדנים?',
	requiredAccountName: 'נדרש שם סעיף',
	requiredAccountDesignation: 'נדרש ייעוד סעיף',
	requiredStatementType: 'נדרש סוג דוח',
	requiredRelatedAssertions: 'בחר מצג הנהלה',
	pspIndexDropdownLabel: 'בחר אינדקס PSP',
	removePSPIndexLabel: 'הסר אינדקס PSP',
	addPSPIndexLink: 'הוספת אינדקס PSP',
	pspIsRequired: 'נדרש אינדקס PSP',

	//Delete account modal
	deleteAccount: 'מחק סעיף',
	deleteAccountModalMessage: 'האם אתה בטוח שברצונך למחוק את הסעיף שנבחר?',
	cannotBeUndone: 'אין אפשרות לבטל זאת.',
	guidedWorkflow: 'הפעלת EY Canvas FIT',
	scotSummary: 'סיכום תתי התהליכים המהותיים (SCOTs)',
	scopeAndStrategy: 'היקף ואסטרטגיה',
	ToggleSwitch: {
		Inquire: 'תשאל',
		Completed: 'הושלם',
		isOn: 'כן',
		isOff: 'לא'
	},
	navExecution: 'ביצוע הביקורת',
	navCanvasEconomics: 'כלכלת EY Canvas (EY Canvas Economics)',
	navOversight: 'פיקוח על EY Canvas',
	navConclusion: 'מסקנה',
	navTeamMemberIndependence: 'אי תלות של חבר צוות',
	navGroupAudit: 'ניהול קבוצה',
	navGroupActivityFeed: 'הזנת פעילות קבוצה',
	navPrimaryStatus: 'סטטוס ראשוני',
	navComponentStatus: 'סטטוס הרכיב',
	navGroupStatus: 'סטטוס ביקורת קבוצה',
	navEngagementManagement: 'ניהול ההתקשרות',
	navProfile: 'פרופיל',
	navItSummary: 'סיכום IT',
	nav440GL: 'שינויים לאחר תאריך הדוח',
	navGroupStructureSummary: 'מבנה הקבוצה',
	navGroupInstructionSummary: 'הנחיות קבוצתיות',
	navGroupInvolvement: 'מעורבות קבוצתית',
	navNotApplicable: 'לא ישים',
	cropScreenshot: 'חיתוך צילום מסך',
	cropScreenshotModalDescription: 'חתוך את צילום המסך כך שיכלול רק חלקים רלוונטיים. חיתוך יסיר הערות קיימות, ה- tickmarks יישמרו וניתן יהיה להוסיף הערות שוב. לא ניתן לבטל חיתוך.',
	crop: 'חיתוך',
	replace: 'החלף',
	nameTheScreenshot: 'שם צילום מסך',
	nameLabel: 'שם',
	takeScreenshot: 'הוסף צילום מסך',
	measurementBasis: 'בסיס מדידה',
	MeasurementBasisMessage: 'בהתבסס על מיפוי הנתונים הבסיסיים של ה- EY Helix, נראה שהנתון הצפוי לבסיס המדידה שנבחר אינו מתאים (למשל, רווח לפני מס כאשר נמצאים בחובה). שקול אם: ',
	measurementBasisProjectMappedCorrectly: 'הנתונים בפרויקט EY Helix ממופים כהלכה',
	measurementBasisAppropriateValue: 'בסיס מדידה שונה עשוי להיות מתאים, או',
	measurementBasisAdjustValue: 'ייתכן שיהיה מתאים להתאים את ערך בסיס המדידה כמפורט להלן',
	basisValueFromHelix: 'ערך מאזן בוחן (TB)',
	rationaleDeterminationLabel: 'נימוק לאופן שבו נקבע סכום זה',
	loginInstructions: 'היכנס ופעל לפי ההנחיות להגדרת החשבון שלך.',
	gotoText: 'לך ל',
	asOfDate: 'מתאריך',
	annualizedBasisValue: 'ערך בסיס שנתי',
	basisValue: 'ערך בסיס',
	isAnnualizedAmountRepresentative: 'האם ערך הבסיס השנתי מייצג את הסכום הצפוי לדווח נכון לסוף התקופה?',
	isAnnualizedAmountRepresentativeForAssetsOrEquity: 'האם הערך מייצג את הסכום הצפוי לדיווח בתום תקופת הביקורת?',

	enterExpectedFinancialPerioadAmount: 'הזן את הסכום הצפוי בסוף תקופת הביקורת',
	enterRationaleAmountDetermined: 'הזן את הרציונל לאופן שבו נקבע סכום זה',
	printNotAvailable: 'ל-{0} אין תוכן ובהתאם לא מוצג מידע',
	canvasFormPrintNotAvailable: 'האפשרות להדפסת טופס Canvas (Canvas Form Print) אינה זמינה כעת. אנא נסה שוב או פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	saving: 'שומר..',
	removing: 'מסיר..',
	activitySummaryTitle: 'סיכום פעילות',
	currentLabel: 'נוכחי',
	PMLabel: 'PM',
	planningMaterialityLabel: 'סף המהותיות PM',
	TELabel: 'TE',
	tolerableErrorLabel: 'הטעות הנסבלת TE',
	SADLabel: 'SAD',
	SADNominalAmountLabel: 'סכום נומינלי של SAD',
	PriorLabel: 'קודם',
	editRichText: 'הזן טקסט',
	noTypeTwoResponseAvailable: 'אין תגובה זמינה. <br /> לחץ על {clickHere} כדי להגיב.',
	clickHere: 'כאן',
	helixNavigationTitle: 'עבור לדף התצורה של EY Helix כדי לקשר או להגדיר פרוייקט EY Helix',
	helixNavigationLink: 'עבור אל EY Helix',
	measurementBasisValue: 'ערך בסיס מדידה',
	inlineSaveUnsavedChanges: 'ישנם שינויים שלא נשמרו, האם ברצונך להמשיך?',
	rationaleIncomplete: 'הרציונל לא שלם',
	projectMismatchDisplayMessageOnDataImport: 'פרויקט EY Helix הראשי שלך השתנה. נא לאשר את הגדרות EY Helix ולייבא נתונים מהפרויקט החדש.',
	importSuccess: 'נתוני EY Helix יובאו בהצלחה.',
	importHelix: 'לחץ על ייבוא ​​כדי לייבא נתוני ספר חשבונות ראשי GL מ- EY Helix.',
	importLabel: 'ייבוא',
	reImportLabel: 'יבוא מחדש',
	lastImportedBy: 'יבוא אחרון על ידי',
	lastImportedOn: 'יבוא אחרון בתאריך',
	dataImportedFromProjec: 'נתונים מיובאים מהפרויקט',
	reImportConfirmationTitle: 'ייבוא ​​מחדש של נתונים מ- EY Helix',
	reImportConfirmationMessagePart1: 'יבוא מחדש של נתונים מ-EY Helix ישנה נתונים קיימים או יוסיף נתונים חדשים בתוך פעילויות רלוונטיות ולא ניתן לבטל זאת.',
	reImportConfirmationMessagePart2: 'למידע נוסף וסיכום של האופן שבו תהליך ייבוא הנתונים מחדש משפיע על הפעלת EY Canvas FIT, עיין ב-EY Atlas.',
	defineRisksTitle: 'הגדר סיכונים',
	assessAndDefineRisksTitle: 'הערך והגדר סיכונים',
	identifiedRiskFactorsTitle: 'אירועים/תנאים שזוהו וסיכונים קשורים: ',
	descriptionIncompleteLabel: 'התיאור לא שלם',
	noRiskHasBeenRelatedMsg: 'שום סיכון לא היה מקושר',
	rationaleIncompleteMsg: 'הרציונל לא שלם',
	loading: 'טוען ...',
	importInProgressLabel: 'ייבוא מתבצע. זה יכול לקחת כמה דקות. אנא רענן את הדף כדי לראות סטטוס מעודכן.',
	importInProgressMessage: 'ייבוא נתוני Helix מתבצע. רענן את הדף כדי לראות את סטטוס הייבוא.',
	importHelixProjectError: 'אירעה שגיאה בייבוא נתונים מ-EY Helix. בדוק שהסטטוס של פרויקט EY Helix מוצג כ-Analytics זמין, רענן את הדף ולחץ שוב על ייבוא או ייבא מחדש. אם הבעיה נמשכת פנה לתמיכה הטכנית.',
	importDeletionConfirmationMsg: 'האם אתה בטוח שברצונך למחוק את הייבוא של נתוני EY Helix? מחיקת ייבוא תמחק נתונים קיימים בתוך פעילויות רלוונטיות ולא ניתן לבטל זאת.',
	deletePreviousImport: 'מחק את ייבוא ה- EY Helix',
	//Assess Risks - Summary Page
	assessRisksAccordionLabel: 'מצגי הנהלה מקושרים',
	assessRisksNoItemsFound: 'לא זוהו סיכונים',
	assessRiskAccountsAndRelatedAssertions: 'סעיפים ומצגי הנהלה מקושרים',
	assessRiskNoAccountsLinked: 'אין סעיף מקושר',
	accountRiskAssessmentSummary: 'סעיפים וגילויים',
	// Flow chart
	flowchartTitle: 'תרשים זרימה',
	launchFlowchart: 'הפעל תרשים זרימה',
	clickherelink: 'לחץ כאן',
	orderToCashPlacement: 'הזמנה ל- Placement למזומן',
	orderToCashPlacementMessage: 'מחלקת התוכניות (Program Dept.) מנהלת משא ומתן עם לקוחות חדשים ומנהלת משא ומתן על חידוש חוזים ו/או שינוי חוזים עם לקוחות מתמשכים. חוזה מכיל פרטים כגון: תמחור, תנאים ואחריות',
	saveFlowChart: 'שמור',
	newstep: 'שלב חדש',
	zoomIn: 'להגדיל את התצוגה (Zoom In)',
	zoomOut: 'להקטין את התצוגה (Zoom Out)',
	resetZoom: 'אפס זום',
	toogleInteractivity: 'אינטראקטיביות של Toogle',
	fitView: 'התאם תצוגה',
	numberOfSteps: 'מספר שלבים',
	flowchartSuccessfullyCreated: 'תרשים זרימה נוצר בהצלחה.',
	flowchartLinkedEvidenceMessage: 'תרשים זרימה זה נוצר בהתקשרות אחרת. הגישה לתרשים הזרימה בהתקשרות זו תוסר כאשר הקישור לראיה יוסר.',
	flowchartSmartEvidenceSourceIdNullMessage: 'אין תת תהליך מהותי (SCOT) זמין.',
	noTaskDocumentAvailableFlowchart: 'מסמך זה הוא קובץ זמני. אנא קשר למשימה כראיה כדי לגשת לפרטי תרשים הזרימה',
	// Control Attributes
	controlAttributes: 'מאפייני בקרה',
	noControlAttributes: 'אין בקרה זמינה',
	flowchartStepMoremenu: 'תפריט נוסף',
	createControlAttributes: 'אין מאפייני בקרה זמינים.<br />לחץ על {clickHere} כדי ליצור מאפיין בקרה חדש.',
	createNewControlAttribute: 'מאפיין חדש',
	editControlAttribute: 'ערוך מאפיין',
	createControlAtttributeInstructions: "הזן את פרטי המאפיין להלן ובחר <b>\'שמור וסגור\'</b> כדי לסיים. כדי ליצור מאפיין נוסף, בחר <b>\'שמור וצור אחר\'.</b> המאפיינים ימוינו על סמך אינדקס המאפיינים (attribute index).",
	editControlAttributeInstructions: "ערוך את פרטי המאפיין להלן ובחר <b>\'שמור\'</b> כדי לסיים. המאפיינים ימוינו על סמך אינדקס המאפיינים (attribute index).",
	editAttributeButtonLabel: 'ערוך מאפיין',
	deleteAttributeButtonLabel: 'מחק מאפיין',
	deleteControlAttributeInstructions: 'האם אתה בטוח שברצונך למחוק את המאפיין שנבחר? לא ניתן לבטל פעולה זו.',
	// Control Attributes Form
	requiredAttributeIndexLabel: 'אינדקס המאפיינים (חובה)',
	requiredAttributeDescriptionLabel: 'תיאור המאפיין (חובה)',
	errorMessageAttributeIndexRequired: 'נדרש',
	errorMessageAttributeDescriptionRequired: 'נדרש',
	errorMessageAttributeDescriptionMaxLength: 'התגובה מכילה {#} תווים החורגים מהמקסימום של {##} תווים. התאם את התיאור על ידי הפחתת טקסט או עיצוב ונסה שנית. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	errorMessageAttributeTestingTypesRequired: 'נדרש',
	proceduresLabel: 'נהלים שיש לבצע',
	modalRequiredProceduresLabel: 'נהלים שיש לבצע (חובה)',
	attributeTestingTypesLabel: {
		inquiry: 'תשאול (Inquiry)',
		observation: 'תַצְפִּית (Observation)',
		inspection: 'בְּדִיקָה (Inspection)',
		reperformance: 'ביצוע חוזר/חישוב מחדש (Reperformance/recalculation)'
	},

	/*CRA Badge*/
	ir: 'IR',
	cr: 'CR',
	cra: 'CRA',
	incompleteCra: 'CRA לא מלא',
	incomplete: 'לא הושלם',

	//Progess bar labels
	savingProgress: 'שומר..',
	discardChangesLabel: 'התעלם משינויים',
	removeFromBody: 'הסר מהתוכן',
	uploading: 'מעלה...',
	uploadComplete: 'ההעלאה הושלמה',
	downloadComplete: 'ההורדה הושלמה',
	processing: 'מעבד...',

	/* ISA BODIES */
	/* Common */
	deleteEntityConfirmation: 'האם אתה בטוח שברצונך למחוק את <b>{0}</b>? לא ניתן לבטל פעולה זו.',
	/* ITAPP-SCOT */
	searchScot: 'חפש  תת תהליך מהותי (SCOT)',
	addITApp: 'הוסף יישום IT',
	relatedItApp: 'יישומי IT מקושרים',
	itApplicationHeader: 'יישומי IT',
	scotNoDataPlaceholder: 'אין מידע זמין',
	noScotsOrControlsPlaceholder: 'אין תתי תהליכים מהותיים (SCOTs) או בקרות קשורות; {noScotsOrControlsPlaceholderEditAssoc}.',
	noScotsOrControlsPlaceholderEditAssoc: 'ערוך קישורים',
	noScotsOrControlsPlaceholderTarget: 'אין תתי תהליכים מהותיים (SCOTs) או בקרות קשורות.',
	scotHeader: 'SCOTs',
	controlsHeader: 'בקרות',
	controlsApplicationHeader: 'יישום',
	controlsITDMHeader: 'ITDM',
	itAppScotNoDataPlaceholderLabel: 'לא נוספו יישומי IT.',
	itAppScotNoDataPlaceholder: 'לא נוספו יישומי IT.<br /> כדי להתחיל לחץ על {itAppScotNoDataPlaceholderAddItApp}',
	itAppScotNoDataPlaceholderAddItApp: 'הוסף יישום IT',
	editItAppOption: 'ערוך יישום IT',
	removeItAppOption: 'מחק יישום IT',
	viewItAppOption: 'הצג יישום IT',
	editScotsISA: 'ערוך תת תהליך מהותי SCOT',
	viewScotISA: 'הצג תת תהליך מהותי SCOT',
	viewControlISA: 'הצג בקרות',
	scotAndRelatedControls: 'תתי תהליכים מהותיים (SCOTs) ובקרות',
	otherControls: 'בקרות אחרות',
	controlTypeLabel: 'סוג בקרה',

	/*SCOT-ITAPP*/
	addOrRelateItAppPlaceholder: '{identifyRelatedItApps} או מסמך שה-{documentScotHasNoItApps}.',
	identifyRelatedItApps: 'זיהוי יישומי IT קשורים',
	documentScotHasNoItApps: 'לתת תהליך מהותי (SCOT) אין יישומי IT קשורים',
	correctScotDocumentationPlaceholder: 'תת התהליך המהותי (ה-SCOT) הוגדר כמי שאינו נתמך על ידי יישום IT. יישום ו/או בקרת ITDM שויכו לתת תהליך מהותי זה, {correctScotDocumentation}.',
	correctScotDocumentation: 'בדוק שוב את ההגדרה שאף יישומי IT לא תומכים ב-SCOT זה',
	controlsWithoutRelatedItApps: 'סיכוני IT',
	controlsWithRelatedItApps: 'בקרות עם יישומי IT קשורים',

	/*AddEditITApplication */
	saveAndCreateNewButtonTitle: 'שמור וצור חדש',
	instructionalMessage: 'הוסף יישום IT ובחר את ה-SCOTs הקשורים. בקרות שייך הקשורות לאפליקציית ה-IT, כאשר רלוונטי.',
	ITApplicationNamePlaceholder: 'שם יישום IT (חובה)',
	scotDropdownPlaceholderText: 'בחר את תתי התהליכים המהותיים (SCOTs) שיהיו קשורים ליישום ה-IT',
	selectScotPlaceholderText: 'בחר את תתי התהליכים המהותיים (SCOTs) שיהיו קשורים ליישום ה-IT',
	selectControlPlaceholderText: 'בחר את הבקרות שיהיו קשורות ליישום ה-IT',
	noRelatedScotsPlaceholderText: 'אין תתי תהליכים מהותיים (SCOTs) קשורים',
	noRelatedControlsPlaceholderText: 'אין בקרות קשורות',
	CreateSOModelTitle: 'הוסף לשכת שירות',
	CreateSOInstructionalMessage: "הזן את פרטי לשכת השירות החדשה להלן ובחר'<b>{0}</b>' כדי לסיים. כדי ליצור לשכת שירות נוספת, בחר'<b>{1}</b>'.`, //'צור לשכת שירות חדשה ושייך תתי תהליכים מהותיים SCOTs ובקרות הקשורות אליה.",
	saveAndCloseLabel: 'שמור וסגור',
	saveAndCreateLabel: 'שמור וצור אחד נוסף',
	SONamePlaceholder: 'שם לשכת השירות (חובה)',
	SOSelectScotPlaceholderText: 'בחר תתי תהליכים מהותיים (SCOTs) הקשורים ללשכת השירות',
	SOSelectControlPlaceholderText: 'בחר בקרות הקשורות ללשכת השירות',
	CreatedSOLabel: 'SO הוסיף',
	createdITAppLabel: 'נוספה אפליקציית IT',
	searchNoResultFoundText: 'לא נמצאו תוצאות',
	searchNoResultsFoundText: 'לא נמצאו תוצאות',
	iTApplicationNameRequired: 'נדרש שם יישום IT',
	soNameRequired: 'נדרש שם לשכת השירות',
	editITAppDesctiptionLabel: 'ערוך את יישום ה-IT ואת תתי התהליכים המהותיים (SCOTs) והבקרות הקשורות',
	editSODescriptionLabel: "ערוך את פרטי לשכת השירות להלן ובחר <b>'Save'</b> כדי לסיים.",
	viewITApplication: 'הצג יישום IT',
	itApplicationName: 'שם יישום ה- IT',
	serviceOrganizationName: 'שם ארגון השירות',
	newItApplication: 'יישום IT חדש',

	/*Add/Edit ITProcess*/
	itProcessName: 'שם תהליך ה- IT',
	addItProcessDescription: 'צור תהליך IT',
	addItProcess: 'הוסף תהליך IT',
	itProcessNameRequired: 'נדרש שם תהליך IT',
	editItProcess: 'ערוך תהליך IT',
	editItProcessDescription: 'ערוך את תהליך ה-IT',
	viewItProcess: 'צפה בתהליך ה-IT',
	taskTitle: 'הבן ותעד את תהליך ה-IT: {0}',
	taskDescription: 'תיעד את ההבנה שלנו לגבי תהליך ה-IT. צרף את הטופס הרלוונטי כראיה לתמיכה בהבנתנו את תהליך ה-IT.<br />כאשר ITGCs מופיעים במקטע מאפייני המשימה (Task Attributes), בצע נהלי WT בכדי לאשר את ההבנה שלנו של ה-ITGCs ולהעריך את התכנון והיישום שלהם. צרף ראיות לביצוע הנהלים שלנו.',
	newItProcess: 'תהליך IT חדש',
	noITProcessesFound: 'לא זוהו תהליכי IT',

	/* IT Process - Task */
	itProcessSearchPlaceholder: 'חיפוש תהליך IT',
	itProcessHeader: 'תהליך IT',
	itProcessTasksHeader: 'משימות',
	itProcessAddUPD: 'הוסף UDP',
	itProcessEditItProcess: 'ערוך תהליך IT',
	itProcessRelateUDP: 'קשר UDP',
	itProcessViewProcess: 'צפה בתהליך ה-IT',
	itProcessNoDataPlaceholder: 'לא נוספו תהליכי IT.<br /> כדי להתחיל לחץ על {addItProcess}.',
	itProcessSourceInstructionalText: 'לפחות UDP אחד חייב להיות קשור לתהליך ה-IT. {itProcessSourceInstructionalTextCreateUdp} או {itProcessSourceInstructionalTextRelateUdp}',
	itProcessSourceInstructionalTextCreateUdp: 'צור UDP חדש.',
	itProcessSourceInstructionalTextRelateUdp: 'קשר UDP קיים.',
	itProcessTargetInstructionalText: 'אין UDP הקשור לתהליך ה-IT.',

	/* IT APP IT PROCESSES RELATION*/
	itApplicationHeaderRelate: 'יישומי IT',
	itProcessesHeaderRelate: 'תהליכי IT',
	itAppNoDataPlaceHolderLabel: 'לא זוהו יישומי IT.',
	itAppNoDataPlaceHolder: 'לא זוהו יישומי IT.<br />{identifyItApp}',
	identifyItApp: 'זהה יישום IT.',
	itProcessNoDataPlaceHolderRelationLabel: 'לא זוהו תהליכי IT.',
	itProcessNoDataPlaceHolderRelation: 'לא זוהו תהליכי IT. <br /> {identifyItProcess}',
	identifyItProcess: 'זהה תהליך IT.',
	editItApp: 'ערוך יישום IT',
	deleteItApp: 'מחק יישום IT',
	drag: 'גרור את תהליך ה-IT ליישומי ה-IT הקשורים',
	// editItProcess: 'Edit IT process',
	deleteItProcess: 'מחק תהליך IT',
	unassociatedProcess: '{0} תהליכים לא קשורים',
	unassociatedItApplications: '{0} יישומי IT לא קשורים',
	showOnlyUnrelated: 'הצג רק לא קשור - {0}',
	searchItProcess: 'חיפוש תהליך IT',
	searchItApplication: 'חיפוש יישום IT',
	itProcessesLabel: 'תהליכי IT',
	itApplicationsLabel: 'יישומי IT',
	showAllItAplications: 'הצג את כל יישומי ה-IT',
	showAllItProcesses: 'הצג את כל תהליכי ה-IT',
	relatedToLabel: "רשימה של כל <span class='child-entity-count'>{count}</span> <span class='child-entity-name'>{child}</span> הקשורים ל<span class='parent- entity-object-name'>{parent}</span>",

	/* IT Process > IT Risk */
	itProcessItRiskNoDataPlaceholder: 'לא זוהה תהליך IT.<br/>{identifyItProcess}',
	itProcessItRiskNoDataPlaceholderTarget: 'לא זוהו תהליכי IT.',
	itApplication: 'אפליקציות IT',
	itGC: 'ITGCs',
	addItRiskBtnTitle: 'הוסף סיכון IT',
	itProcessItRiskUnrelatedITGC: 'בקרות כלליות במערכות המידע (ITGCs) לא קשורים',
	itProcessItRiskUnrelatedITGCUppercase: 'בקרות כלליות במערכות המידע (ITGCs) לא קשורים',
	itProcessItRiskNoRisksNoControlsPlaceholder: 'אין דרישה לכלול את סיכוני ה-IT או ה-ITGCs עבור תהליך IT זה מכיוון שאין בקרות יישומים או ITDM עם הערכת עיצוב יעילה הקשורה ליישומי IT הקשורים לתהליך IT זה.',
	itProcessItRiskNoRisksControlsPlaceholder: 'בהתבסס על הבקרות האפליקטיביות (AC) ובקרות ידניות תלויות מחשב (ITDM) {itRiskIdentify} עבור תהליך IT זה',
	itRiskIdentify: 'יש לזהות סיכוני IT',
	itProcessItRiskItProcessContentTitle: 'סיכוני IT ו-ITGCs',
	itProcessItRiskItRiskNoItgcRequiredPlaceholder: 'אין ITGCs המטפלים בסיכון ה-IT.',
	itProcessItRiskItRiskItgcRequiredPlaceholder: 'לכל הסיכונים המזוהים חייב להיות לפחות ITGC אחד שזוהה או ציון שלסיכון ה-IT אין ITGCs.<br/>זהה {newITGC} או {existingITGC} המטפלים בסיכון ה-IT או ציינו שיש {noItRisksIdentified} שמטפלים ב- סיכון IT.',
	noItRisksIdentified: 'אין ITGCs',
	newITGC: 'בקרה כללית חדשה במערכות המידע (ITGC)',
	existingITGC: 'בקרה כללית קיימת במערכות המידע (ITGC)',
	unrelatedItGCModalMessage: 'מחק ITGCs שלא שויכו אשר אינם נחוצים עוד.',
	unrelatedItGCModalNoDataPlaceholder: 'אין בקרת ITGC שלא שויכה',
	removeItRisk: 'הסר סיכון IT',
	deleteItRiskConfirmation: 'האם אתה בטוח שברצונך להסיר את סיכון ה-IT <b>{0}</b>? פעולה זו תמחק את סיכון ה-IT ולא ניתן לבטלה.',
	relateItGcTitle: 'קשר ITGC',
	relateItGcEntityTitle: 'סיכון IT',
	relateItGcDescription: 'בחר את ה-ITGCs הרלוונטיים לסיכון ה-IT.',
	relateItGcSearchPlaceholder: 'חפש ב-ITGC',
	relateItGcShowSelectedOnlyText: 'הצג רק ITGCs קשורים',
	relateItGcNoDataPlaceholder: 'אין ITGCs זמינים. צור ITGC חדש כדי להמשיך.',
	relateITSPTitle: 'התייחס ל-ITSP',
	relateITSPDescription: 'בחר את ה-ITSPs הרלוונטיים לסיכון ה-IT.',
	relateITSPSearchPlaceholder: 'חפש לפי שם ITSP',
	relateITSPShowSelectedOnlyText: 'הצג רק ITSPs קשורים',
	relateITSPNoDataPlaceholder: 'אין ITSP זמין. צור ITSP חדש כדי להמשיך.',

	/* IT Process Task Relationship */
	relateUDP: 'קשר UDP',
	relateUDPDescription: 'בחר את משימות ה-UDP הרלוונטיות לתהליך ה-IT.',
	relateUDPListHeaderItemName: 'שם משימה',
	relateUDPSearchPlaceholder: 'חפש לפי שם משימה',
	relateUDPNoResultsFoundPlaceholder: 'לא נמצאו תוצאות',
	relateUDPCountLabel: '{0} משימות',
	relateUDPClose: 'Close',
	relateUDPShowOnlyRelatedTasks: 'הצג רק משימות קשורות',
	relateUDPNoDataFoundPlaceholder: 'אין משימות זמינות',
	relateUDPNoDataPlaceHolder: 'לא זוהה תהליך IT',

	/* ITGC test strategy */
	itProcessItRiskItGcWithoutDesignEffectiveness: 'ITGCs ללא יעילות עיצוב',
	searchItGC: 'חיפוש תהליך IT',
	itGCNoDataPlaceHolder: 'לא זוהה תהליך IT',
	addItRisks: 'הוסף סיכוני IT',
	itDMHeader: 'ITDM',
	itAppHeader: 'אפליקציית IT',
	itTestHeader: 'בדוק',
	itTestingHeader: 'בודק',
	itgcHeader: 'ITGCs',
	controlsSelectedHeader: 'בקרות שנבחרו לבדיקה',
	iTRisksAndITGCs: 'סיכוני IT ו-ITGCs',
	NoITGCForITRiskPlaceholder: 'לא קיימות בקרות כלליות במערכות המידע (ITGC) בסביבת IT בכדי לטפל בסיכון IT זה',
	ITGCsNotIdentifiedRiskNoITGCs: 'ITGCs לא זוהו עבור סיכון זה. {identifyAnITGC} או ציין כי {itRiskHasNoITGCs}.',
	identifyAnITGC: 'זהה ITGC',
	itRiskHasNoITGCs: 'לסיכון IT אין ITGCs',

	/**
	 * IT SO > SCOT
	 */
	searchItSO: 'חפש לשכת שירות (SO)',
	addItSOBtnTitle: 'הוסף לשכת שירות',
	itSoNoDataPlaceHolder: 'לא זוהו לשכות שירות.<br/><a>{identifyAnSo}<a/>',
	noItSoDataPlaceHolder: 'לא זוהו לשכות שירות (SO)',
	identifyAnSo: 'זיהוי לשכת שירות',
	soHeader: 'לשכת שירות',
	editSO: 'ערוך את לשכת השירות (SO)',
	deleteSO: 'מחק את לשכת השירות (SO)',
	viewSO: 'הצג את לשכת השירות (SO)',
	controlRelatedToSO: 'בקרות הקשורות ל-SO',

	/**
	 * Manage IT SP
	 */
	addITSP: 'הוסף נוהל מבסס ל- IT  (IT-substantive testing procedures (ITSP))',
	searchPlaceholderManageITSP: 'חיפוש תהליך IT',
	noManageITSPDataPlaceholder: 'לא זוהה תהליך IT',
	itRiskColumnHeader: 'סיכוני IT',
	itDesignEffectivenessHeader: 'Design Effectiveness',
	itTestingColumnHeader: 'בודק',
	itGCColumnHeader: 'ITGCs',
	itSPColumnHeader: 'ITSPs',
	searchClearButtonTitle: 'Clear',
	itProcessItRiskUnrelatedITSP: ' נהלים מבססים ל- IT  (IT-substantive testing procedures (ITSPs)) לא קשורים',
	manageITSPUnrelatedITSPUppercase: 'ITSPs לא קשורים',
	unrelatedITSPModalMessage: 'מחק ITSPs לא משויכים שאינם נחוצים עוד.',
	unrelatedITSPModalNoDataPlaceholder: 'אין ITSP לא משויך',
	noITGCPlaceholderMessageFragment1: 'לכל הסיכונים המזוהים חייב להיות לפחות ITGC אחד שזוהה או ציון שלסיכון ה-IT אין ITGCs: ',
	noITGCPlaceholderMessageFragment2: 'זהה',
	noITGCPlaceholderMessageFragment3: 'ITGC חדש',
	noITGCPlaceholderMessageFragment4: 'אוֹ',
	noITGCPlaceholderMessageFragment5: 'ITGC קיים',
	noITGCPlaceholderMessageFragment6: 'שמתייחס לסיכון ה-IT או מציין שיש',
	noITGCPlaceholderMessageFragment7: 'אין ITGCs',
	noITGCPlaceholderMessageFragment8: 'המטפלים בסיכון ה-IT',
	addNewITSP: 'הוסף נוהל מבסס ל- IT  (IT-substantive testing procedures (ITSP)) חדש',
	addExistingITSP: 'הוסף נוהל מבסס ל- IT  (IT-substantive testing procedures (ITSP)) קיים',
	noITSPPlaceholderMessageFragment1: 'אם הערכנו את ה-ITGCs כלא אפקטיביים או קבענו שאין ITGCs שקיימים כדי לטפל בסיכון ה-IT, ייתכן שנוכל לבצע נהלי בדיקות IT-מהותיות (ITSPs) כדי להשיג ביטחון סביר שסיכון ה-IT בתהליך ה-IT הקשור ל-IT. ITGC לא אפקטיבי לא נוצל.',
	noITSPPlaceholderMessageFragment2: 'זהה ITSP חדש',
	noITSPPlaceholderMessageFragment3: 'אוֹ',
	noITSPPlaceholderMessageFragment4: 'בהתייחס לנוהל מבסס ל- IT  (IT-substantive testing procedures (ITSP)) קיים',
	noITSPPlaceholderMessageFragment5: '.',
	noITGCsExitForITRisk: 'אין ITGCs עבור סיכון IT.',
	noITSPExitForITRisk: 'לא זוהו ITSP עבור סיכון ה-IT.',
	manageITSPItemExpansionMessage: 'סיכוני IT',
	noITGCExists: 'אין ITGCs המטפלים בסיכון ה-IT.',
	iTGCName: 'שם ITGC',
	itSPName: 'שם ITSP',
	operationEffectiveness: 'אפקטיביות תפעולית',
	savingLabel: 'Saving',
	deletingLabel: 'מוחק',
	removingLabel: 'מסיר',
	itFlowModalDescription: 'עבור אל ה-{itSummaryLink} כדי לערוך/להסיר את האובייקטים האלה שאינם מתאימים יותר למעורבות.',
	itSummaryLink: 'מסך סיכום IT',
	manageITSPYes: 'כן',
	manageITSPNo: 'לא',

	understandITProcess: 'הבנת תהליכי IT',
	activity: 'פעילות',
	unsavedPageChangesMessage: 'יש לך שינויים שלא נשמרו שיאבדו אם תבחר להמשיך. האם אתה בטוח שברצונך לצאת מהדף הזה?',
	unsavedChangesTitle: 'שינויים שלא נשמרו',
	unsavedChangesLeave: 'צא מהדף הזה',
	unsavedChangesStay: 'הישאר בדף זה',

	notificationDownErrorMessage: 'אופציית ההתראות אינו זמין זמנית. רענן את הדף ונסה שוב. אם הודעה זו ממשיכה, פנה לתמיכה הטכנית.',
	notificationUpbutSomeLoadingErrorMessage: 'אירעה שגיאה טכנית שמונעת מאופציית ההתראות לפעול. רענן את הדף ונסה שוב.',
	markCompleteError: 'כל המסמכים המוצגים דורשים חתימה של מכין אחד (P) וסוקר אחד ® לכל הפחות.',
	markCompleteDescription: 'כל המסמכים חייבים להיות חתומים על ידי מכין אחד (P) וסוקר אחד ® לכל הפחות, בכדי לסמן את הפעילות ככזו שהושלמה.',
	lessthan: 'פחות מ',
	openingFitGuidedWorkflowFormError: 'לא ניתן לפתוח את טופס ההפעלה של EY Canvas FIT',
	timeTrackerErrorFallBackMessage: 'אופציית מעקב הזמן אינה זמינה זמנית. רענן את הדף ונסה שוב. אם הודעה זו ממשיכה, פנה לתמיכה הטכנית.',
	timeTrackerLoadingFallbackMessage: 'אופציית מעקב הזמן (Time Tracking) אינה זמינה כעת. זה יהיה זמין בקרוב.',
	priorPeriodRelateDocument: 'קשר ראיית ביקורת מהתקופה הקודמת',
	selectedValue: 'ערך נבחר',
	serviceGateway: 'שער שירות (Service Gateway)',
	docNameRequired: 'השם לא יכול להיות ריק',
	docInvalidCharacters: 'השם אינו יכול לכלול: */:<>\\?|"',
	invalidComment: 'לא ניתן להוסיף תגובה. אם אתה בוחר מספר פריטים ברשימה, בחר רק פריט בודד ונסה שוב. אם הבעיה נמשכת, רענן את הדף ונסה שוב או פנה לתמיכה הטכנית.',
	inputInvaildCharacters: 'הקלט אינו יכול לכלול את מחרוזת התווים הבאה: */:<>\\?|"',

	// FIT Navigation panel
	relatedActivities: 'פעילויות קשורות',
	backToRelatedActivities: 'חזרה לפעילויות קשורות',
	backToMainActivities: 'חזרה לפעילויות העיקריות',
	formOptions: 'אפשרויות טופס',

	// FIT Sharing
	shareActivity: 'שתף פעילות',
	shareLabel: 'שתף',
	shareInProgress: 'השיתוף מתבצע',
	manageSharing: "שיתוף פעילות זו מחייב את הרשאת המשתמש'ניהול שיתוף של הפעלת EY Canvas FIT'. נווט לדף'נהל צוות' כדי לנהל הרשאות או ליצור קשר עם חבר אחר בצוות",
	dropdownPlaceholderSA: 'בחר את תיק הביקורת לשיתוף',
	fitSharingModalInfo: 'שתף פעילות זו עם פעילות אחת או יותר מאותה התקשרות או מהתקשרות אחרת באותה סביבת עבודה. אם הפעילויות שנבחרו אינן משותפות כבר, הרי שהתגובות בפעילויות שנבחרו להלן יוחלפו. אם הפעילות עדיין משותפת, ניתן יהיה לבחור רק אחת והתגובות בפעילות זו יוחלפו.',
	lastModifiedDate: 'שונה לאחרונה ב: ',
	noActivityToShare: 'אין פעילות זמינה לשיתוף',
	activityNotShared: '{0} לא שותף',
	activityShareSuccessfull: '{0} שותף בהצלחה',
	sharedWithAnotherFITActivity: 'פעילות זו משותפת עם פעילות אחרת',
	sharedActivityWithAnotherCanvas: 'שתף פעילות עם הפעלת EY Canvas FIT אחרת',
	shareActivityModalTitle: 'שתף פעילות',
	showRelationshipsTitle: 'הצג מערכות יחסים',
	shareActivityEngagement: 'תיק ביקורת',
	shareActivityRelationshipsModalTitle: 'קשרי פעילות משותפת',
	shareActivityWorkspaceHeading: 'פעילות זו משותפת עם ההתקשרויות הבאות ופעילות(יות) קשורות באותה סביבת עבודה.',
	shareModalOkTitle: 'שתף',
	shareModalContinueLabel: 'המשך',
	selectedActivityInfoLabel: 'בהתקשרות שנבחרה שונה לאחרונה ב: ',
	noSharedActivityInfoLabel: 'להתקשרות זו אין מסמך אחר מאותו סוג לשתף איתו.',
	alreadyHasSharedActivityInfoLabel: 'הפעילות שנבחרה כבר משותפת עם פעילויות אחרות. שיתוף הפעילות הנוכחית יסנכרן את התגובות מהפעילות שנבחרה לפעילות הנוכחית.',
	selectActivityResponsesForSharingLabel: 'בחר אילו תגובות למסמך צריכות להחליף את האחרות: ',
	selectActivityResponsesForCurrentRadioLabel: 'שתף תגובות מהמסמך הנוכחי למסמך הנבחר לעיל',
	selectActivityResponsesForSelectedRadioLabel: 'שתף תגובות מהמסמך הנבחר לעיל למסמך הנוכחי',
	selectActivityResponsesWarningEarlierTimeLabel: "The current activity was modified at an earlier time compared to the selected engagement's activity. Please consider this before confirming the sharing option's below the table.",
	selectActivityResponsesWarningModifiedMoreRecentlyLabel: 'הפעילות הנוכחית שונתה לאחרונה בהשוואה לפעילות המסמך שנבחר. אנא שקול זאת לפני שתאשר את אפשרות השיתוף למעלה.',
	selectActivityUnsuccessfulMessage: 'השיתוף נכשל. נא לנסות שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	otherEngagemntDropdownlabel: 'התקשרויות נוספות בסביבת העבודה: ',
	documentSearchPlaceholder: 'חפש במסמך',
	showOnlySelected: 'הצג רק את מה שנבחר',

	//FIT Copy
	copyLabel: 'העתק',
	copyActivity: 'העתקת פעילות',
	copyInProgress: 'העתקה בתהליך',
	fitCopyModalInfo: 'העתק תגובות מפעילות זו לפעילות אחת או יותר מאותה התקשרות או מהתקשרות אחרת באותה סביבת עבודה.',
	dropdownPlaceholderCA: 'בחר את ההתקשרות להעתקה',
	noCopyActivityInfoLabel: 'אין להתקשרות זו מסמך מאותו סוג להעתיק אליו.',
	copyActivityHoverLabel: 'פעילות זו כבר משותפת עם פעילויות אחרות ולא ניתן להעתיק אליה',
	copyActivityWarningEarlierTimeLabel: 'הפעילות הנוכחית שונתה במועד מוקדם יותר בהשוואה לפעילות ההתקשרות שנבחרה. אנא שקול זאת לפני שתאשר את אפשרויות ההעתקה.',

	//Unlink
	unlinkModalTitle: 'בטל קישור לפעילות',
	unlinkModalDescription: 'האם אתה בטוח שברצונך לבטל את הקישור לפעילות שנבחרה?',
	unlinkLabel: 'בטל קישור',
	insufficientPermissionsLabel: 'הרשאה לא מספקת',
	unlinkFailMessage: 'ביטול הקישור נכשל. אנא רענן ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית של EY.',
	unlinkSuccessfulMessage: 'ביטול הקישור הצליח',
	unlinkInProgressLabel: 'ביטול הקישור בתהליך',
	unlinkError: 'שגיאה בביטול קישור',
	unlinkInProgressInfo: 'ביטול הקישור מתבצע. פעולה זו עשויה להימשך עד חמש עשרה דקות. לאחר השלמת ביטול הקישור, יהיה צורך לסגור את הטופס הזה ולפתוח אותו שוב.',

	/** Manage scot modal labels */
	scotName: 'שם תת התהליך המהותי SCOT',
	scotCategory: 'קטגוריית תת תהליך מהותי (SCOT)',
	estimate: 'אומדן',
	noScotsAvailablePlaceHolder: 'אין תת תהליך מהותי (SCOT) זמין. הוסף תת תהליך מהותי (SCOT) חדש כדי להתחיל',
	addScotDisableTitle: 'מלא את כל הפרטים עבור תת תהליך מהותי (SCOT) כדי להוסיף תת תהליך מהותי (SCOT) חדש',
	deleteScotTrashLabel: 'מחק תת תהליך מהותי (SCOT)',
	undoDeleteScotTrashLabel: 'בטל מחיקה',
	scotNameValidationMessage: 'נדרש שם תת תהליך מהותי (SCOT)',
	scotCategoryValidationMessage: 'נדרשת קטגוריית תת תהליך מהותי (SCOT)',
	scotWTTaskDescription:
		'<p>עבור כל תת תהליך מהותי (SCOT) רוטיני ולא רוטיני ותהליכי גילוי משמעותיים, אנו מאשרים את ההבנה שלנו בכל תקופה על ידי ביצוע נהלי walkthrough. בנוסף, עבור ביקורת PCAOB, אנו מבצעים נהלי walkthrough של תתי תהליכים מהותיים הכרוכים באומדנים (Estimation SCOTs). <br/> עבור כל תתי התהליכים המהותיים(SCOTs), כאשר אנו נוקטים אסטרטגיית הסתמכות על בקרות, ועבור בקרות המתייחסות לסיכונים משמעותיים(SR), אנו מאשרים שהבקרות הרלוונטיות תוכננו ויושמו כראוי.אנו מאשרים שההחלטה שלנו לנקוט באסטרטגיית ההסתמכות על הבקרות עדיין מתאימה. <br/> <br/> אנו מסיקים שהתיעוד שלנו מתאר במדויק את האופן בו תת התהליך המהותי(SCOT) פועל וכי זיהינו את כל ה - WCGW המתאימים, לרבות סיכונים הנובעים משימוש ב - IT, ובקרות רלוונטיות(כאשר רלוונטי). <br/> <br/> עבור תתי תהליכים מהותיים הכרוכים באומדנים(Estimation SCOTs), כאשר אנו משתמשים באסטרטגיה מבססת בלבד, אנו קובעים אם ההבנה שלנו של תת התהליך המהותי הכרוך באומדן(Estimation SCOT) מתאימה בהתבסס על הנהלים המבססים שלנו. </p>',

	relate: 'מקושר',
	unrelate: 'לא קשור',
	related: 'קשור',
	relatedSCOTs: 'תתי תהליכים מהותיים (SCOT) קשורים',
	thereAreNoSCOTsIdentified: 'לא זוהו תתי תהליכים מהותיים (SCOT)',
	selectSCOTsToBeRelated: 'בחר תתי תהליכים מהותיים (SCOT) בכדי לקשר אותם',

	//OAR Tables
	OARBalanceSheet: 'מאזן',
	OARIncomeStatement: 'דוח רווח והפסד',
	OARCurrentPeriod: 'תאריך Analysis',
	OARAmountChangeFrom: 'שנה מ',
	OARPercentageChangeFrom: '% שינוי מ',
	OARNoDataAvailable: 'אין נתונים זמינים. עיין בדף {0} וייבא את הנתונים כדי להמשיך הלאה.',
	OARAnnotationLabel: 'לחץ כדי לבדוק את הסיבה לשינויים בלתי צפויים או היעדר שינויים צפויים',
	OARAnnotationSelectedIcon: 'תיעוד הסיבה לשינויים בלתי צפויים או היעדר שינויים צפויים',
	OARAnnotationModalTitle: 'הערה',
	OARAnnotationModalPlaceholder: 'מסמכים שמופיעים בהם שינויים חריגים, בלתי צפויים או היעדר שינויים צפויים.',
	OARWithAnnotationLabel: 'תיעוד של שינויים בלתי צפויים',
	OARAnnotation: 'הערה',
	OARAccTypeWithAnnotationCountLabel: '{0} הערות בתוך סוג הסעיף',
	OARSubAccTypeWithAnnotationCountLabel: '{0} הערות בתוך סוג המשנה של הסעיף',
	OARColumnA: 'א',
	OARColumnB: 'ב',
	OARColumnC: 'C',
	OARComparative1Period: 'תאריך השוואה 1',
	OARComparative2Period: 'תאריך השוואה 2',
	OARExpand: 'הרחב את קבוצת החשבונות',
	OARCollapse: 'צמצם את קבוצת החשבונות',
	OARHelixNavigationLink: 'עבור אל EY Helix למידע נוסף',
	OARPrintNoDataAvailable: 'אין נתונים זמינים',
	OARAdjustedBalance: 'Adjusted Balance',
	OARLegendLabel: 'ערכים המסומנים ב-* מציינים שהם כוללים התאמה. עבור לפרטי ההתאמה לקבלת פרטים נוספים.',
	OARAccountType: 'סוג החשבון',
	astrixLabel: '*',

	//OAR Helix integration
	helixIntegrationModalDescription: 'זהו טקסט הממתין להגדרה',
	OSJETabText: 'הצד השני של פקודת היומן',
	activityAnalysisTabText: 'ניתוח פעילות',
	preparerAnalysisTabText: 'ניתוח מכין',
	accountMetricsTabText: 'מדדי (metrics) חשבון',
	noAnalyticsData: 'אין ניתוח זמין לתצוגה',

	printActivitiesTitle: 'הדפס פעילויות',
	printActivitiesModalInfo: 'אנא בחר אילו פעילויות תרצה לכלול.',
	printActivitiesModalConfirmButton: 'PDF  ערוך',
	printActivitiesDropdownLabel: 'פעילויות FIT',
	printActivitiesAll: 'הכול',
	oarSetupText: 'עבור לדף {0} כדי לקשר או להגדיר פרויקט של EY Helix',
	helixNotAvailable: 'EY Helix אינו זמין עבור תיק הביקורת שלך.',
	dragDropUploadPlaceholder: 'גרור ושחרר מסמך אחד או יותר או לחץ על <span>{addDocument}</span>',

	noTaskAssociatedToastMessage: 'מכיוון שטופס הקנבס נמצא בקבצים זמניים, המסמכים שנוספו נוספו גם לקבצים זמניים',

	// chart labels.
	assets: 'נכסים',
	liabilities: 'התחייבויות',
	equity: 'הון',
	revenues: 'הכנסות',
	expenses: 'הוצאות',
	noAccountsAvailable: 'אין חשבונות זמינים',

	// ALRA
	ALRAFilterByAccount: 'סנן לפי חשבון',
	ALRANoRecords: 'לא נמצאו תוצאות',
	ALRAAssertions: 'יעדי ביקורת',
	ALRAInherent: 'גורמי סיכון מובנה',
	ALRAHigher: 'גורמי סיכון גבוה יותר',
	ALRAAccountDisclosure: 'חשבונות/גילויים',
	ALRAType: 'סוג',
	ALRAName: 'שם',
	ALRARisks: 'סיכונים',
	ALRAC: 'C',
	ALRAEO: 'E/O',
	ALRAMV: 'M/V',
	ALRARO: 'R&O',
	ALRAPD: 'P&D',
	ALRAR: 'R',
	ALRANoRisksAssociated: 'אין סיכון הקשור לחשבון זה',
	ALRAAccountsDisclosureName: 'שם סעיף/גילוי',
	ALRAHigherRisk: 'סיכון גבוה יותר',
	ALRAHigherInherentRisk: 'סיכון מובנה גבוה יותר',
	ALRAHigherRiskCode: 'H',
	ALRALowerRisk: 'בסיכון נמוך',
	ALRALowerInherentRisk: 'סיכון מובנה נמוך יותר',
	ALRALowerRiskCode: 'L',
	ALRALimitedRiskAccount: 'הסעיף זוהה כסעיף בסיכון מוגבל (limited risk account)',
	ALRAInsignificantRiskAccount: 'הסעיף זוהה כלא מהותי (insignificant account)',
	ALRADesignations: 'ייעודים',
	ALRABalances: 'יתרות',
	ALRADesignation: 'ייעוד',
	ALRAAnalysisPeriod: 'תאריך Analysis',
	ALRAxTE: 'xTE',
	ALRAPercentageChangeFrom: '% שינוי מ',
	ALRAPriorPeriodDesignation: 'ייעוד מתקופה קודמת',
	ALRAPriorPeriodEstimate: 'אומדן תקופה קודמת',
	ALRAComparativePeriod1: 'תאריך השוואה 1',
	ALRAComparativePeriod2: 'תאריך השוואה 2',
	ALRASelectUpToThreeOptions: 'בחר עד 3 אפשרויות',
	ALRASelectUpToTwoOptions: 'בחר עד שתי אפשרויות',
	ALRAValidations: 'תיקוף',
	ALRANoSignOffs: 'אין חתימות',
	ALRAIncompleteInherentRisk: 'סיכון מובנה בלתי שלם',
	ALRARelatedDocuments: 'מסמכים קשורים',
	ALRAGreaterExtent: 'במידה רבה יותר',
	ALRALesserExtent: 'במידה פחותה',
	ALRARiskRelatedToAssertion: 'סיכון קשור',
	ALRAContributesToHigherInherentRisk: 'סיכון מקושר הגורם לסיכון שבמהות  להיות גבוה יותר',

	// Assess inherent risk
	HigherRiskAssertionWithoutRisksThatContributesToTheHigherInherentRisk: 'מצג הנהלה מזוהה ככזה שהוא בעל סיכון מובנה גבוה יותר ללא סיכון אחד לפחות שתורם לסיכון הגלום הגבוה יותר. שייך סיכונים וזהה אילו סיכונים תורמים לסיכון הגלום הגבוה יותר של מצג ההנהלה.',

	//MEST - Multi-entity account Execution Type selection listing
	account: 'סעיף',
	taskByEntity: 'משימה לפי ישות',
	bodyInformation: 'עליך ללחוץ על ייבוא תוכן למטה כדי לשמור כל שינוי.',

	/*user search component*/
	seachInputRequired: 'חפש קלט שנדרש',
	nameOrEmail: 'שם או דוא"ל',
	emailForExternal: 'דואל',
	noRecord: 'לא נמצאו תוצאות',
	userSearchPlaceholder: 'הזן שם או דוא"ל ולחץ Enter כדי לראות תוצאות.',
	userSearchPlaceholderForExternal: 'הזן דוא"ל ולחץ Enter כדי לראות תוצאות.',
	clearAllValues: 'נקה את כל הערכים',
	inValidEmail: 'אנא הזן דוא"ל חוקי',

	//reactive frame
	maxTabsLocked: 'הגעתם למקסימום הכרטיסיות המותרות. בטל את ההצמדה וסגור אחת מהכרטיסיות כדי לפתוח אחת חדשה.',
	openInNewTab: 'פתח בלשונית חדשה',
	unPinTab: 'בטל את הצמדת הכרטיסייה',
	pinTab: 'הצמד כרטיסייה',
	closeDrawer: 'סגור את  סל האפשרויות',
	minimize: 'לְצַמְצֵם',

	accountHeader: 'סעיפים',
	sCOTSummaryAccountNoDataLabel: 'כל תת תהליך מהותי (SCOT) חייב להיות קשור לסעיף או לגילוי משמעותי אחד לפחות. בחר סעיף או גילוי משמעותי קיים כדי לקשר ל-SCOT זה',
	sCOTSummaryNoDataLabel: 'לא נוצרו תתי תהליכים מהותיים SCOTs',
	scotSearchNoResultsFound: 'No results found',
	scotSummary225TabsName: {
		[0]: {
			label: "הצג לפי סעיף'"
		},
		[1]: {
			label: "הצג לפי תתי תהליכים מהותיים SCOT'"
		}
	},

	// Display Account Balances
	currentPeriodAccountBalance: 'יתרת חשבון בתקופה נוכחית: ',
	priorPeriodAccountBalance: 'יתרת חשבון בתקופה קודמת: ',

	ALRANoResults: 'לא נמצאו תוצאות. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	associatedRomsCount: 'מספר כולל של סיכונים מקושרים: {0}',
	alraMessage: "תגובת ייעוד החשבון אינה מותאמת לייעוד ב'ערוך סעיף וגילוי'",
	estimateCategoryResponseNotAlignedToDesignation: "תגובת קטגוריית אומדן אינה בהלימה מול  האפיון ב'עריכת אומדן'",


	// Analytics Overview
	analyticsOverviewTitle: 'סקירה כללית של אנליטי (Analytics Overview)',
	noSignificantAccountRecords: 'לא נוצרו סעיפים מהותיים.',
	noSignificantAccountMapped: 'לא ממופים סעיפים משמעותיים לישות שנבחרה.',
	noLimitedAccountMapped: 'לא ממופים חשבונות מוגבלים (limited accounts) לישות שנבחרה.',
	openAnalyticDocumentation: 'פתח תיעוד אנליטי',
	openLimitedRiskAccountDocumentation: 'פתח תיעוד חשבון בסיכון מוגבל (Limited Risk Account)',
	associatedSCOTs: 'תתי תהליכים מהותיים (SCOTS) משויכים: ',
	analysisPeriodLabel: 'תאריך ניתוח {0}',
	analysisPeriodChangeLabel: '% שינוי מ-{0}',
	xTELabel: 'xTE',
	risksLabel: 'סיכונים',
	comparativePeriod1: 'תאריך השוואתי 1 {0}',
	analysisPeriodTitle: 'תאריך Analysis',
	analysisPeriodChangeTitle: '% שינוי מ',
	comparativePeriodTitle: 'תאריך השוואה 1',
	noAccountAvailable: 'אין חשבון זמין',

	// Estimates
	titleEstimateCategory: 'קטגוריית אומדן',
	titleRisks: 'סיכונים',

	voiceNoteNotAvailable: 'הערה קולית והקלטת מסך אינם זמינים בתצוגת  סל האפשרויות. עבור לתצוגת מסך מלא בכדי להשתמש בתכונות אלה.',

	financialStatementType: {
		[1]: {
			label: 'נכס'
		},
		[2]: {
			label: 'נכס שוטף'
		},
		[3]: {
			label: 'נכס לא שוטף'
		},
		[4]: {
			label: 'התחייבות'
		},
		[5]: {
			label: 'התחייבות שוטפת'
		},
		[6]: {
			label: 'התחייבות לא שוטפת'
		},
		[7]: {
			label: 'הון '
		},
		[8]: {
			label: 'הַכנָסָה'
		},
		[9]: {
			label: 'הוצאה'
		},
		[10]: {
			label: 'רווח / (הפסד) לא תפעולי'
		},
		[11]: {
			label: 'הכנסה כוללת אחרת (OCI)'
		},
		[12]: {
			label: 'אחרים'
		},
		[13]: {
			label: 'סוג סעיף (Account Type)'
		},
		[14]: {
			label: 'סוג משנה של הסעיף'
		},
		[15]: {
			label: 'סוג סעיף (Account class)'
		},
		[16]: {
			label: 'קבוצת משנה של הסעיף '
		},
		[17]: {
			label: '(הכנסה) / הוצאה נטו'
		}
	},
	accountTypes: {
		[1]: {
			label: 'סעיף מהותי'
		},
		[2]: {
			label: 'חשבון בסיכון מוגבל'
		},
		[3]: {
			label: 'סעיף לא מהותי (Insignificant account)'
		},
		[4]: {
			label: 'סעיף אחר'
		},
		[5]: {
			label: 'גילוי משמעותי'
		}
	},
	noClientDataAvailable: 'אין נתונים זמינים',

	analysisPeriod: 'תאריך Analysis',
	comparativePeriod: 'תאריך השוואתי',
	perchangeLabel: '% שינוי',

	entityCreateAccountLabel: 'צור סעיף וגילוי',
	insignificantAccount: 'סעיף לא מהותי (Insignificant account)',
	noAccountRecords: 'לא זוהו סעיפים',
	noAccountsForEntity: 'לא ממופים חשבונות או גילויים לישות שנבחרה.',
	noLimitedRiskAccountRecords: 'אין סעיפים זמינים שזוהו כסעיפים בסיכון מוגבל (limited risk account).',
	createAccount: 'צור חשבון',
	createDocument: 'צור מסמך',
	noAccountResults: 'לא זוהו סעיפים.',
	createGroupInvolvementDocument: 'צור טופס מעורבות',
	chooseVersionsToCompare: 'בחר גרסה להשוואה',
	noTrackChangesOption: 'אין גרסאות זמינות המסומנות כעקוב אחר שינויים',
	trackChangesDefaultMessage: "בחר גרסה מהתפריט הנפתח'בחר גרסה להשוואה' כדי להמשיך.",
	whichRiskContributeToHigherRisk: 'אילו סיכונים תורמים למצג ההנלה להיות בעל סיכון גבוה יותר?',

	//multi-entity Entity List
	createMultiEntity: 'ישות חדשה',
	editMultiEntity: 'ערוך ישות',
	noEntitiesAvailableCreateNewLink: 'לחץ כאן',
	noEntitiesAvailable: 'לא נוצרו ישויות. {noEntitiesAvailableCreateNewLink} כדי להתחיל',
	noEntitiesFound: 'לא נמצאו תוצאות',
	createMultiEntityProfile: 'צור פרופיל ישות',

	createEntity: 'צור ישות',
	includeEntities: 'הרישום מרובה הישויות חייב לכלול ישות אחת לפחות. {createEntity} כדי להתחיל.',
	//multi-entity table
	multiEntityCode: 'אינדקס סטנדרטי של ישות',
	multiEntityName: 'שם הישות',
	multiEntityGroup: 'קבוצת חברות',
	multiEntityActions: 'פעולות',
	relateMultiEntityUngrouped: 'לא מקובצים',
	selectAll: 'בחר הכל',
	entitiesSelected: 'ישויות שנבחרו',
	entitySelected: 'ישות שנבחרה',
	meNoEntitiesAvailable: 'אין ישויות זמינות',
	meSwitchEntities: 'החלף ישויות',
	meSelectEntity: 'בחר ישות',
	allEntities: 'כל הישויות',
	noEntitiesIdentified: 'לא זוהו ישויות',
	contentDeliveryInProcessMessage: 'העברת התוכן בעיצומה. ייתכן שיחלפו עד עשר דקות עד שהתוכן יועבר.',
	importContent: 'ייבוא תוכן',
	profileSubmit: 'שלח פרופיל',
	importPSPs: 'ייבא PSPs',
	contentUpdateInsufficienRolesLabel: 'אין הרשאות מתאימות לעדכון תוכן. צור קשר עם מנהל ההתקשרות כדי לקבל הרשאות מתאימות.',
	// MEST Switcher
	meEntitySwitcher: 'מחליף ישויות',
	//Error Boundary
	errorBoundaryMessage: 'אירעה שגיאה. אנא רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	sectionName: 'שם המקטע',
	maxLength: 'הטקסט אינו יכול לחרוג מ-{number} תווים.',
	required: 'נדרש',
	yearAgo: 'לפני שנה',
	yearsAgo: 'לפני שנים',
	monthAgo: 'לפני חודש',
	monthsAgo: 'לפני חודשים',
	weekAgo: 'לפני שבוע',
	weeksAgo: 'לפני שבועות',
	daysAgo: 'ימים לפני',
	dayAgo: 'יום לפני',
	today: 'היום',
	todayLowercase: 'היום',
	yesterday: 'אתמול',
	approaching: 'בקירוב',

	associatedToInherentRiskFactor: 'מקושר לגורם סיכון מובנה',

	createMissingDocument: 'צור מסמך חסר',
	createMissingDocumentBody: "לחץ על'אשר' כדי ליצור את המסמך עבור הפריטים הקשורים עבורם חסר כעת מסמך.",
	documentCreationSuccessMsg: 'יצירת מסמכים בעיצומה. רענן את הדף לקבלת עדכונים.',

	noRisksRelatedToAssertion: 'אין סיכונים הקשורים למצג הנהלה (assertion) זה של החשבון.',

	noAssertionsRelatedToAccount: 'אין מצגי הנהלה (assertions) הקשורים לחשבון זה',

	sharing: 'שיתוף',

	risksUnrelatedToAccountAssertion: 'סיכונים שאינם קשורים למצגי הנהלה (assertion) בחשבון',
	cantCompleteTask: "במסמכים המשויכים למשימה הקשורה חסרות חתימות (sign-offs). פתח את המשימה, השלם את המסמכים החסרים ולאחר מכן נסה לסמן שוב כ'הושלם'.",
	cantCompleteTasksTitle: 'לא ניתן לסמן שהושלם.',
	ok: 'אשר',
	documentsEngagementShareLabel: 'בחר את המסמך בתוך ההתקשרות לשיתוף עם',
	documentsEngagementCopyLabel: 'בחר את המסמך להעתקה מתוך ההתקשרות',
	lastModifiedon: 'שונה לאחרונה בתאריך',
	newAccountAndDisclosure: 'סעיף חדש וגילוי נאות',
	newAccountORDisclosure: 'סעיף חדש או גילוי נאות',

	externalDocuments: 'מסמכים חיצוניים',
	noExternalDocumentsAvailable: 'אין מסמכים חיצוניים זמינים',
	addExternalDocuments: 'הוסף מסמכים חיצוניים',
	relateExternalDocuments: 'קישור למסמכים חיצוניים',

	helixNotMappedToAccount: 'נתוני EY Helix אינם ממופים לסעיף זה. אנא עדכן את המיפוי וייבא מחדש נתונים בכדי להמשיך.',
	trackChangesNotAvailableForSpecialBodyDisplayMessage: 'הפונקציונליות של מעקב אחר שינויים אינה זמינה עבור התגובה(ות) להלן.',
	noDocumentRelatedObjectsApplicable: 'אובייקטים אינם נדרשים להיות משויכים לטופס זה של תהליך העבודה המודרך (guided workflow)',
	helixViewerLoader: 'טוען תצוגת Helix Viewer...',
	trackChangesViewDefaultMessage: "לתגובות מסוימות אין פונקציונליות של מעקב אחר שינויים. זה יצוין עם ההודעה הבאה בפרטי הפעילות שבבסיסם: 'פונקציונליות של מעקב אחר שינויים אינה זמינה עבור התגובות להלן.' לפיכך, היעדר הודעה על השינויים להלן אינו מעיד אם בוצעו שינויים.",

	//Relate task modal
	relateTasksTitle: 'קשר משימות',
	taskLocationLabel: 'מיקום המשימה',
	relateTaskInstructionalText: 'הוסף או הסר משימות שהמסמך צריך להיות קשור אליהן. אם המסמך הוא ראיה, הסרתו מהמשימה האחרונה תעביר אותו לקבצים זמניים.',
	noResultFound: 'לא נמצאו תוצאות',
	relatedTaskCounter: '{0} משימה',
	relatedTasksCounter: '{0} משימות',
	onlyShowRelatedTasks: 'הצג רק משימות קשורות',
	relateTaskName: 'שם משימה',
	relateTaskType: 'סוג',

	/*Relate Entities*/
	relateEntitiesTitle: 'קשר ישויות',
	relateEntitiesSearchPlaceholder: 'הזן כדי לחפש לפי שם',
	relateEntitiesName: 'שם הישות',
	relateEntitiesIndex: 'אינדקס סטנדרטי של ישות',
	relatedEntitiesCounter: '{0} ישויות',
	relatedEntityCounter: '{0} ישויות',
	onlyShowRelatedEntities: 'הצג רק ישויות קשורות',
	entity: 'ישות',

	step01: 'שלב 01',
	step02: 'שלב 02',
	shareActivityStep1Description: 'בחר התקשרות ומסמך',
	shareActivityStep2Description: 'בחר אילו תגובות למסמך צריכות להחליף את האחרות',
	documentsShareLabel: 'שתף את התגובה מהמסמך הנבחר למטה עם שאר המסמכים.',
	selectedActivity: 'פעילות נבחרת',
	sharedHoverLabel: 'פעילות זו כבר משותפת עם פעילויות אחרות. שיתוף פעילות זו יסנכרן את התגובות מפעילות זו לכל הפעילות המשותפת',
	noAssertionsRelatedLabel: 'אין קישור למצגי ההנהלה.',

	// Bulk mark complete:
	bulkMarkCompleteInstructionalText: 'כל המסמכים חייבים להיות חתומים על ידי מכין אחד (P) וסוקר אחד ® לכל הפחות בכדי לסמן את הפעילות ככזו שהושלמה.',
	bulkMarkCompleteEngagementColumn: 'תיק הביקורת',
	bulkMarkCompleteDocumentsMissingSignOffs: 'חסריות חתימות (sign-off) . לחץ על {bulkMarkCompleteMissingSignOffsClickableText} כדי לחתום.',
	bulkMarkCompleteMissingSignOffsClickableText: 'כאן',
	bulkMarkCompleteNoAccessToEngagement: 'אין לך גישה להתקשרות בה נמצאת המשימה הזו',
	bulkMarkCompleteInProgressMessage: '״התהליך בעיצומו. זה עשוי לקחת עד עשר דקות. נא לרענן לעדכונים',
	bulkMarkCompleteRelatedDocumentsModalTitle: 'חתימות מסמכים',
	bulkMarkCompleteFilterUnreadyTasks: 'הצג רק משימות עם אישורי מסמכים חסרים.',
	bulkMarkCompleteNotAllowedModalTitle: 'לא ניתן לסמן שהושלם.',
	bulkMarkCompleteNotAllowedModalDescription: 'עליך לבחור לפחות משימה אחת כדי לסמן שהשלמה',
	bulkMarkCompleteRelatedDocumentsModalDescription: 'על מנת לסמן את השלמת המשימה שנבחרה, כל המסמכים חייבים להיות חתומים על ידי מכין אחד וסוקר אחד לפחות.',
	bulkMarkCompleteRelatedDocumentsModalRefreshSignoffs: 'רענון חתימות והערות',
	selectedTaskCounter: '({0}) המשימה שנבחרה',
	selectedTasksCounter: '({0}) משימות נבחרות',

	// Mark complete (old):
	markCompleteNotAllowedModalDescription: "מסמכים המשויכים למשימה הקשורה חסרים סימנים. פתח את המשימה, השלם את המסמכים החסרים ולאחר מכן נסה לסמן שוב כ'השלמה'",
	markCompleteInstructionalText: 'כל המסמכים חייבים להיות חתומים לפחות על ידי מכין אחד וסוקר אחד , בכדי שניתן יהיה לסמן את הפעילות ככזו שהושלמה',

	// Adobe Analytics
	aaCookieConsentTitle: 'ברוכים הבאים אל',
	aaCookieContentPrompt: 'האם ברצונך לאפשר קבצי Cookie?',
	aaCookieConsentExplanation: '<p>בנוסף לקובצי Cookie הנחוצות בהחלט להפעלת אתר זה, אנו משתמשים בסוגי ה- Cookies הבאים בכדי לשפר את חווייתך ואת שירותינו: <strong> קבצי Cookie פונקציונליים</strong> לשיפור חווייתך (למשל זכירת הגדרות), <strong>קבצי Cookie של ביצועים </strong> למדידת הביצועים של האתר ושיפור החוויה שלך, <strong>קובצי Cookie של פרסום/פילוח</strong>, המוגדרים על ידי צדדים שלישיים שאיתם אנו מבצעים קמפיינים פרסומיים ומאפשרים לנו לספק לך פרסומות רלוונטיות עבורך.</p><p>Review our <a target="_blank" href="https://www.ey.com/en_us/cookie-policy">cookie policy</a>  למידע נוסף.</p>',
	aaCookieConsentExplanationWithDoNotTrack: "<p> בנוסף לעוגיות Cookie הנחוצות בהחלט להפעלת אתר זה, אנו משתמשים בסוגי Cookies הבאים כדי לשפר את חווייתך ואת שירותינו: <strong>קבצי Cookie פונקציונליים</strong> כדי לשפר את החוויה שלך (למשל זכור הגדרות), <strong>עוגיות ביצועים</strong> למדידת ביצועי האתר ולשיפור חווייתך, <strong>קבצי עוגיות של פרסום / פילוח</strong>, המוגדרים על ידי צדדים שלישיים שאיתם אנו מבצעים קמפיינים פרסומיים ומאפשרים לנו לספק לך פרסומות רלוונטיות עבורך.</p><p>זיהינו שהפעלת את ההגדרה'אל תעקוב' בדפדפן שלך; כתוצאה מכך, קובצי עוגיות של פרסום / פילוח מושבתים אוטומטית.</p><p>Review our <a target='_blank' href='https://www.ey.com/en_us/cookie-policy'>cookie policy</a> for more information.</p>",
	aaCookieConsentDeclineOptionalAction: 'אני דוחה קבצי Cookie אופציונליים',
	aaCookieConsentAcceptAllAction: 'אני מאשר את כל קבצי ה- Cookie',
	aaCookieConsentCustomizeAction: 'התאמה אישית של קבצי Cookie',
	aaCookieConsentCustomizeURL: 'https://www.ey.com/en_us/cookie-settings',

	// Cookie Settings
	cookieSettings: {
		title: 'הגדרות קבצי cookie',
		explanation: 'אנא ספק את הסכמתך לשימוש בקבצי cookie ב-ey.com ובפלטפורמת My EY. בחר אחד או יותר מסוגי קבצי ה- cookie המפורטים להלן, ולאחר מכן שמור את הבחירות שלך. עיין ברשימה למטה לפרטים על סוגי קבצי ה- cookies ומטרתם.',
		emptyCookieListNotice: 'קובצי Cookie מקטגוריה זו אינם משמשים באפליקציה זו',
		nameTableHeader: 'שם קובץ ה cookie',
		providerTableHeader: 'ספק קובץ ה cookie',
		purposeTableHeader: 'מטרת קובץ ה cookie',
		typeTableHeader: 'סוג קובץ ה cookie',
		durationTableHeader: 'משך קובץ ה cookie',
		formSubmit: 'שמור את הבחירה שלי',
		requiredCookieListTitle: 'קבצי cookies חובה',
		functionalCookieListTitle: 'קבצי cookies פונקציונאליים',
		functionalCookieAcceptance: 'אני מקבל את קבצי ה- cookies הפונקציונליים להלן',
		functionalCookieExplanation: 'קבצי cookies פונקציונאליים, המאפשרים לנו לשפר את חווייתך (למשל על ידי זכירת כל ההגדרות שבחרת).',
		performanceCookieListTitle: 'קבצי Cookies של ביצועים',
		performanceCookieAcceptance: 'אני מקבל את קבצי ה- Cookies של הביצועים שלהלן',
		performanceCookieExplanation: 'קובצי Cookie לביצועים, שעוזרים לנו למדוד את ביצועי האתר ולשפר את החוויה שלך. בשימוש בקבצי cookie לביצועים איננו אוגרים נתונים אישיים כלשהם, אלא רק משתמשים במידע שנאסף באמצעות קבצי cookie אלה בצורה מצטברת ואנונימית.',
		advertisingCookieListTitle: 'פילוח קבצי cookie',
		advertisingCookieAcceptance: 'אני מקבל את קבצי ה cookie לפרסום/פילוח להלן',
		advertisingCookieExplanation: 'קבצי cookie לפרסום/פילוח, בהן אנו משתמשים כדי לעקוב אחר פעילות המשתמשים והפעולות שלהם בכדי שנוכל לספק שירות מותאם אישית יותר, ו(במקרה של קבצי cookie לפרסום) אשר נקבעות על ידי הצדדים השלישיים איתם אנו מבצעים קמפיינים פרסומיים ומאפשרים לנו לספק פרסומות רלוונטיות עבורך.',
		doNotTrackNotice: "זיהינו שהפעלת את ההגדרה'אל תעקוב' בדפדפן שלך; כתוצאה מכך, קבצי עוגיות של פרסום / פילוח מושבתים אוטומטית.",
	},
	accountFormsMissing: '{0} חשבונות ללא טפסים',
	createAccountForms: 'צור טופס(טפסים) בחשבון',
	createAccountFormsDescription: "לחץ על'אשר' כדי ליצור את המסמך עבור הפריטים הקשורים עבורם חסר כעת מסמך.",
	createMissingDocuments: 'חשבונות קשורים ללא מסמך(כים)',
	accountDocumentsCreated: 'העברת התוכן בעיצומה. ייתכן שיחלפו עד עשר דקות עד שהתוכן יועבר.',

	evidenceMissingPICSignoffs: 'ראיות הביקורת ללא חתימות על ידי ה- PIC',
	evidenceMissingEQRSignoffs: 'ראיות הביקורת ללא חתימות על ידי ה- EQR',
	evidenceMissingPICEQRSignoffs: 'ראיות הביקורת ללא חתימות על ידי ה- EQR או על ידי ה- PIC',
	evidenceMissingPICSignoffRequirements: 'ראיות הביקורת ללא דרישת(ות) לחתימה על ידי ה- PIC',
	evidenceMissingEQRSignoffRequirements: 'ראיות הביקורת ללא דרישת(ות) לחתימה על ידי ה- EQR',
	evidenceMissingPICEQRSignoffRequirements: 'ראיות הביקורת ללא דרישת(ות) לחתימה על ידי ה- EQR או על ידי ה- PIC',
	evidenceMissingSignoffs: 'ראיות ביקורת ללא חתימה(ות)',

	// Bulk task relate
	bulkTaskRelateFailureMessage: 'חלק מהמסמכים שנבחרו לא היו יכולים להיות משויכים למשימה(ות) שנבחרו.',
	/*endoflabels*/
	evidenceMissingPreparerOrReviwerSignoffs: 'העלאת מסמכים - חסרות חתימות מכין או סוקר',

	manageITProcess: 'ניהול תהליך IT',
	manageITRisk: 'ניהול סיכונים טכנולוגיים',
	manageITControl: 'ניהול בקרת IT',
	manageITSP: 'נהל נוהל מבסס ל- IT  (IT-substantive testing procedures (ITSP))',
	manageITApp: 'ניהול יישומי IT',
	manageSCOT: 'נהל תתי תהליכים מהותיים (SCOT)',
	addAresCustomDescription: 'בחר את סוג התוכן שיתווסף לטופס תהליך עבודה מודרך (guided workflow) זה, הזן פרטים ולחץ על שמור.',

	documentImportSuccess: '{0} נוצר בהצלחה. ייתכן שהפעולה תימשך עד עשר דקות עד שהתוכן יועבר במלואו.',
	documentImportFailure: 'יצירת המסמך נכשלה. אנא רענן או נסה שוב מאוחר יותר. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	formNotAvailable: 'לא נמצא טופס Canvas תואם.',
	selectTask: 'בחר משימה כדי לקשר להנחיה',
	canvas: 'Canvas',
	selectEngagement: 'בחר התקשרות',

	//Modal Manage sub-scope
	manageSubScopeTitle: 'נהל תת היקפים (sub-scopes)',
	manageSubScopeDescription: 'צור תתי-היקפים (sub-scopes) חדשים או ערוך או מחק תתי-היקפים (sub-scopes) קיימים להלן.',
	addSubScope: 'הוסף תת-היקף (sub-scope)',
	subScopeName: 'שם תת-היקף (Sub-scope)',
	knowledgeScope: 'היקף בהתאם לידע (Knowledge scope)',
	subScopeAlreadyExist: 'שם תת-היקף (Sub-scope) כבר קיים',
	subScopes: 'תתי היקפים (sub-scopes)',
	notAvailableSubScopes: 'אין תת היקפים (sub-scopes) זמינים.',
	SubScopeNameValidation: 'אורך שם תת-ההיקף (Sub scope) עולה על יותר מ-255 תווים.',

	//CRA Summary
	manageAccount: 'ניהול חשבון',
	newAccount: 'סעיף חדש',

	noRelatedObjectITProcessFlow: 'אין אובייקט קשור. קשר אובייקט כדי להתחיל.',

	//Add New Flow Chart Steps
	flowChartNewSteps: {
		newStepTitle: 'שלב חדש',
		placeholderText_1: 'הזן את פרטי השלב למטה ובחר',
		placeholderText_2: "'שמור וסגור'",
		placeholderText_3: ' לסיום. כדי ליצור שלב נוסף, בחר',
		placeholderText_4: "'שמור וצור אחר'.",
		columnLabel: 'עמודה (חובה)',
		counterOf: 'של',
		counterChar: 'תווים',
		stepNameLabel: 'שם שלב (חובה)',
		errorMsgStepNameRequired: 'נדרש שם שלב',
		stepDescLabel: 'תיאור שלב (חובה)',
		stepDescPlaceholder: 'הזן תיאור שלב',
		errorMsgStepDescRequired: 'נדרש תיאור שלב',
		required: 'נדרש',
		errorMsgStepDescExceedMaxLength: 'תיאור השלב חורג ממקסימום התווים המותרים',
		buttonCancel: 'בטל',
		buttonSaveAndClose: 'שמור וסגור',
		buttonSaveAndCreateAnother: 'שמור וצור אחד נוסף',
		errorMsgColumnRequired: 'עמודה נדרשת',
		headerNameForWCGW: 'שם WCGW',
		headerNameForControl: 'שם בקרה',
		headerNameForITApp: 'שם יישום ה- IT',
		headerNameForServiceOrganisation: 'שם לשכת השירות',
		relateLabelForWCGW: 'קשר WCGWs',
		relateLabelForControl: 'קשר בקרות',
		relateLabelForITApp: 'קשר יישומי IT',
		relateLabelForServiceOrganisation: 'קשר לשכות שירות SO',
		designEffectiveness: 'Design Effectiveness',
		testing: 'בודק',
		lowerRisk: 'בסיכון נמוך',
		wcgwNoRowsMessage: 'לא קושרו WCGWs כלל. לחץ על {0} כדי להתחיל',
		controlNoRowsMessage: 'לא קושרו בקרות. לחץ על {0} בכדי להתחיל',
		itAppNoRowsMessage: 'אין יישומי IT קשורים. לחץ על {0} כדי להתחיל',
		serviceOrganisationNoRowsMessage: 'לא קושרו לשכות שירות. לחץ על {0} כדי להתחיל',
		wgcwTabLabel: 'WCGWs',
		controlsTabLabel: 'בקרות',
		itAppsTabLabel: 'יישומי IT',
		serviceOrganisationTabLabel: 'ארגוני שירות',
		connectionSuccessMessage: 'החיבור נוצר בהצלחה.',
		connectionFailedMessage: 'לא ניתן ליצור חיבור. אנא נסה שוב.',
		selfConnectFailMessage: 'המקור והיעד אינם יכולים להיות זהים.',
		connectionDuplicateMessage: 'החיבור כבר קיים.',
		connectionDeleteSuccessMessage: 'החיבור נמחק בהצלחה.',
		connectionDeleteFailMessage: 'לא ניתן למחוק את החיבור. אנא נסה שוב.',
		editStepFailMessage: 'לא ניתן היה לערוך את השלב. אנא נסה שוב.',
		flowchartStepGetByIdFailMessage: 'שלב לא חוקי, אנא רענן ונסה שוב.',
		flowchartStepGetByIdFailureMessage: 'שלב תרשים זרימה זה אינו זמין עוד. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
		newStepFailureMessage: 'לא היתה אפשרות ליצור שלב חדש. נסה שוב.',
		deleteConnector: 'מחק את המחבר (connector)',
		edgeConnectorOptions: 'אפשרויות מחבר (connector)',
		edgeStartPoint: 'נקודת התחלה',
		edgeEndPoint: 'End Point',
		relateDocumentToFlowchartStepError: 'לא ניתן להשלים את הפעולה בשלב זה. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
		relateDocumentsOrObjects: 'קישור מסמכים או אובייקטים',
		thisstep: 'לשלב זה'
	},

	flowChartWCGW: {
		wcgwsCounter: '{0} WCGWs',
		wcgwCounter: '{0} WCGW',
		headerName: 'קשר WCGWs',
		showOnlyRelatedText: 'הצג רק כאלו שקשורים',
		noResultsFound: 'לא נמצאו תוצאות'
	},

	flowchartITAPPSO: {
		showOnlyRelatedText: 'הצג רק כאלו שקשורים',
		noResultsFound: 'לא נמצאו תוצאות'
	},

	flowChartITApplication: {
		itApplicationsCounter: '{0} יישומי IT',
		itApplicationCounter: '{0} יישום IT',
		headerName: 'קשר יישומי IT',
		columnName: 'שם יישום ה- IT',
		noDataFound: 'לא נמצאו יישומי IT'
	},

	flowChartITSO: {
		itSOsCounter: '{0} לשכות שירות',
		itSOCounter: '{0} לשכת שירות',
		headerName: 'קשר לשכות שירות SO',
		columnName: 'שם לשכת השירות',
		noDataFound: 'לא נמצאו לשכות שירות'
	},

	flowChartControl: {
		controlsCounter: 'קשר בקרות {0}',
		headerName: 'קשר בקרות',
		showOnlyRelatedText: 'הצג רק כאלו שקשורים',
		noResultsFound: 'לא נמצאו תוצאות',
		noWCGWs: 'לא נוצרו בקרות'
	},

	relateSCOT: {
		header: 'קשר תתי תהליכים מהותיים SCOTs',
		estimate: 'אומדן',
		scotsCounter: '{0} תתי תהליכים מהותיים (SCOTs)',
		scotCounter: '{0} תת תהליך מהותי (SCOT)',
		headerName: 'שם תת התהליך המהותי SCOT',
		showOnlyRelated: 'הצג רק כאלו שקשורים',
		noResultsFound: 'לא נמצאו תוצאות',
		noScotCreated: 'לא נוצרו תתי תהליכים מהותיים (SCOTs) בהתקשרות'
	},

	relatedStepObjects: {
		relatedWCGWs: 'WCGWs מקושרים',
		relatedControls: 'בקרות קשורות',
		relatedDocuments: 'ראיות קשורות',
		relatedITApplications: 'יישומי IT קשורים',
		relatedSOs: 'לשכות שירות קשורות'
	},

	flowchartEditSteps: {
		nextStep: 'השלב הבא',
		previousStep: 'השלב הקודם',
		editStepTitle: 'ערוך שלב',
		editPlaceholderText_1: 'ערוך את פרטי השלבים והאובייקטים הקשורים למטה. לחץ',
		editPlaceholderText_2: "'שמור וסגור'",
		editPlaceholderText_3: 'כדי לשמור ולחזור לתרשים הזרימה. ניווט לשלבים אחרים באמצעות האפשרויות למטה ישמור את העדכונים שלך.',
		draftEditStepFailMessage: 'לא ניתן ליצור שלב בתרשים זרימה. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	},

	flowChartStepmoreMenu: {
		edit: 'ערוך',
		delete: 'מחק'
	},

	relateEstimate: {
		scot: 'SCOTs',
		strategy: 'Scot Strategy',
		type: 'סוג',
		noSCOT: 'כל אומדן חייב להיות קשור לתת תהליך מהותי (SCOT) אחד לפחות. לחץ',
		noSCOTmsg: ' בכדי להתחיל',
		estimate: 'אומדן',
		routine: 'Routine',
		nonRoutine: 'Non Routine',
		notSelected: 'לא נבחר',
		relateSCOTs: 'קשר תתי תהליכים מהותיים SCOTs',
		remove: 'הסר',
		noEstimate: 'אין אומדן זמין'
	},

	flowChartStepIcons: {
		wcgws: 'WCGWs',
		controls: 'בקרות',
		iTApps: 'יישומי IT',
		serviceOrganisations: 'ארגוני שירות'
	},

	flowChartStepIcon: {
		wcgw: 'WCGW',
		control: 'בקרה',
		iTApp: 'אפליקציית IT',
		serviceOrganisation: 'לשכת שירות',
		evidence: 'ראיות'
	},

	flowChartErrorMessage: {
		stepOutsideOfTheColumns: 'Steps cannot be placed outside of the flowchart area',
		stepBetweenTheColumns: 'Steps cannot be placed between the columns',
		stepOnTopOrTooCloseToAnotherStep: 'Steps cannot be placed on top of the other steps'
	},

	//Delete Flow Chart Steps
	flowChartStepsDelete: {
		deletestep: 'מחק שלב',
		deleteStepModalMessage: 'האם אתה בטוח שברצונך למחוק את השלב הזה? כל ה-WCGWs, הבקרות, יישומי ה-IT, לשכות השירות והראיות הקשורים לשלב שנמחק לא יהיו מקושרים יותר.',
		cannotBeUndone: 'חוזי לקוחות חדשים או מחודשים',
		deleteStepFailMessage: 'מחיקת השלב נכשלה. אנא נסה שוב',
		deleteDraftStepErrorMessage: 'השלב שנוצר בטיוטה לא נמחק. כדי למחוק שלב זה, אנא בחר את השלב ובצע את המחיקה שוב.'
	},
	notEntered: 'לא נכנס',
	estimateCategory: 'קטגוריית אומדן',
	noResultsFoundWithPeriod: 'לא נמצאו תוצאות',
	noEstimateAvailable: 'אין אומדן זמין',
	noRelatedObject: 'אין אובייקט קשור.',
	relateAnObject: 'קשר אובייקט',
	copyrightMessage: 'זכויות יוצרים © <year> כל הזכויות שמורות',
	leadsheet: 'גיליון ראשי (Lead sheet)',
	controlName: 'שם בקרה',
	noControlAvailable: 'אין בקרה זמינה',
	independenceError: 'יש להשלים את כל התגובות החלקיות לפני הגשת הצהרת אי התלות.',
	riskTypeNotAssociated: 'הסיכון החדש שנוסף אינו תואם את סוגי הסיכון המותרים ולכן אינו מופיע להלן. הוסף סיכון נוסף מהסוג המותר או בחר מהרשימה הבאה',
	accountsAndRelatedEstimates: 'חשבונות ואומדנים קשורים',
	noEstimatesAssociated: 'לא שוייכו אומדנים',
	noAssertionsAvailable: 'אין מצגי הנהלה זמינים',
	noAccountsOrDisclosuresAvailable: 'אין חשבונות או גילויים זמינים',

	relateEstimateToRisk: {
		riskType: 'סוג הסיכון',
		risk: 'סיכון',
		hasestimate: "Has estimate?",
		accounts: 'סעיפים',
		isItRelevant: 'האם זה רלוונטי?',
		assertions: 'יעדי ביקורת',
		invalidRiskParentRiskErrMsg: 'הרשומה לא נמצאה. אנא רענן את הדף כדי להמשיך.',
		noEstimate: 'אין אומדן זמין',
		invalidRelateRiskOrEstimateRelationErrMsg: 'האובייקט כבר קושר. אנא רענן את הדף כדי להמשיך.',
		invalidUnRelateRiskOrEstimateRelationErrMsg: 'האובייקט כבר לא היה קשור. אנא רענן את הדף כדי להמשיך.'
	},

	savingChanges: 'שמירת שינויים',
	showEstimateAccountsWithoutEstimates: 'הצג סעיפים הכרוכים באומדנים (estimate accounts) ללא אומדנים שקושרו אליהם',
	showEstimateSCOTsWithoutEstimates: 'הצג תתי תהליכים מהותיים הכרוכים באומדנים (estimate SCOTs) ללא אומדנים שקושרו אליהם',
	manageSCOTs: 'נהל תתי תהליכים מהותיים (SCOT)',
	sCOTsAndRelatedEstimates: 'SCOTs ואומדנים קשורים',
	relateEstimateToRiskNoDataMessage: 'אין רשומות זמינות, אנא קשר לפחות סעיף אחד ומצג הנהלה אחד עם סיכון קשור, ככל ורלוונטי',
	maps: 'מפות',
	mapsUpbutSomeLoadingErrorMessage: '`אירעה שגיאה טכנית המונעת את פעולת פונקציית המפות. רענן את הדף ונסה שוב.',
	mapsDownErrorMessage: 'תכונת המפות אינה זמינה זמנית. רענן את הדף ונסה שוב. אם הודעה זו נמשכת, צור קשר עם התמיכה הטכנית.',
	financialStatements: 'דוחות כספיים',
	serviceGatewayAutomation: 'שער שירות (Service Gateway) ואוטומציה',
	priorPeriodCategory: 'קטגוריית תקופה קודמת',
	relatedAccountWithColon: 'חשבונות קשורים: ',
	noRelatedAccount: 'אין חשבונות קשורים',
	noRetionaleAvailable: 'אין רציונל זמין',
	leftNavIconApprovals: 'אישורים',
	editDuplicateSectionHeader: 'ערוך את הפרטים עבור המקטע ולחץ על שמור.',

	relatedEvidences: 'קשר ראיות',
	relatedEvidencesInstruction: 'קשר ראיות מהתקשרות זו.',
	relatedTemporaryFilesInstruction: 'קשר מסמך זמני מהתקשרות זו.',
	noDataLabel: 'לא נמצאו נתונים',
	editDuplicateSection: 'עריכת מקטע',
	showOnlyRelated: 'הצג רק כאלו שקשורים',
	aiChatbot: 'EYQ Assurance Knowledge',
	StEntityNoRecords: 'לא ממופים חשבונות או גילויים לישות שנבחרה.',
	versionLabel: 'גירסה',
	relatedEstimates: 'אומדנים קשורים',
	viewEvidenceRelatedToBody: 'View evidence related to the body',
	selectHeaderFromRail: 'Select a header from left navigation pane to proceed',
	manageITProcesses: 'ניהול תהליכי IT',
	rationaleForLR: 'רציונל לחשבון בסיכון מוגבל (limited risk account)',
	rationaleForInsignificant: 'רציונל לסעיף לא מהותי (Insignificant Account)',
	rationalIsMissing: 'הרציונל חסר.',
	craSummaryText1: 'לכל סעיף או גילוי משמעותי חייב להיות לפחות מצג הנהלה אחד קשור. לחץ',
	scotDetails223: {
		relatedAccounts: 'סעיפים קשורים',
		scotType: 'סוג',
		manageScot: 'נהל תתי תהליכים מהותיים (SCOT)',
		editScot: 'עריכת תתי תהליכים מהותיים SCOTs',
		scotNotAvailableMessage: "תתי תהליכים מהותיים'SCOTs אינם זמינים עבור מסמך זה",
		relatedScotNotAvailableMessage: 'אין תת תהליך מהותי SCOT קשור. קשר SCOT מדף המאפיינים בכדי להתחיל',
		risksDocumented: 'סיכונים המתועדים ב- walkthrough זה',
		risksAvailableHeader: 'כן',
		risksNotAvailableHeader: 'לא,',
		viewRelatedRisks: 'הצגת סיכונים קשורים',
		noRelatedAccountsMessage: 'אין חשבונות קשורים'
	},

	scotDetails226: {
		noscotsidentified: 'No SCOTs have been identified'
	},

	scotDetails224: {
		riskRelatedWalkthrough: 'סיכונים הקשורים ב- WT זה',
		relatedToWTDocuments: 'Related to other WT documents',
		riskNotRelatedWalkthrough: 'סיכונים שאינם קשורים ב- WT זה',
		substantiveNotSufficient: 'מבסס אינו מספיק',
		journalEntry: 'פקודת יומן',
		noDirectRiskSourcesAreAvailable: 'לא נמצאו סיכונים קשורים',
		scotNotAvailableMessage: "תתי תהליכים מהותיים'SCOTs אינם זמינים עבור מסמך זה",
		relatedScotNotAvailableMessage: 'אין תת תהליך מהותי SCOT קשור. קשר SCOT מדף המאפיינים בכדי להתחיל',
		relatedDocuments: 'Related documents',
		risk: "Risk: ",
		riskSpecialCircumstances: 'Risk special circumstances',
		relateInstructionText: "This risk has been identified in another SCOT.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		unrelateInstructionText: "This risk has been identified in the critical path of another walkthrough.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		concurrencyErrorMessage: "This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.",
	},
	ipe: 'IPE',
	scotSummary198: {
		noAccountsDisclosureCreated: 'לא נוצרו סעיפים או גילוייםם משמעותיים',
		noScotEstimateIdentified: 'לא זוהו תתי תהליכים מהותיים SCOTs או אומדנים.',
		noScotIdentified: 'לא זוהו תתי תהליכים מהותיים SCOTs',
		scots: 'SCOTs',
		estimates: 'אומדנים',
		errorMessage: 'לא היתה אפשרות להשלים פעולה זו. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
		noResultsFound: 'לא נמצאו תוצאות',
		searchAccounts: 'חיפוש סעיפים',
		searchScotEstimates: 'חיפוש SCOTs/אומדנים',
		accounts: 'סעיפים',
		scotsOrEstimate: 'SCOTs/אומדנים',
		accountNotRelatedToScotValidation: 'כל סעיף או גילוי משמעותי חייב להיות קשור לתת תהליך מהותי (SCOT) אחד לפחות. בחר תיבת סימון כדי לקשר SCOT רלוונטי לסעיף או גילוי משמעותי זה.',
		scotNotRelatedToAccountValidation: 'כל תת תהליך מהותי (SCOT) חייב להיות קשור לסעיף או לגילוי משמעותי אחד לפחות. בחר תיבת סימון כדי לקשר את SCOT זה לסעיף או לגילוי משמעותי רלוונטי.',
		showValidations: 'הצג אימותים (validations)',
	},
	scotSummary225: {
		relatedScots: 'תתי תהליכים מהותיים SCOTs קשורים',
		relatedAcconts: 'סעיפים קשורים',
		scotListHeader: 'SCOTs ואומדנים',
		noScotsMessage: 'כל סעיף או גילוי משמעותי חייב להיות קשור לתת תהליך מהותי (SCOT) אחד לפחות. בחר SCOT קיים שיקושר לסעיף משמעותי זה או לגילוי משמעותי זה',
		noAccountsMessage: 'No significant accounts or disclosures have been created.',
		noAccountsAvailableOnSearch: 'No results found',
		relateAccounts: 'Relate accounts and disclosures',
		noAccountsCreated: 'No accounts have been created',
		noScotsCreated: 'No SCOTs have been created',
		relateScots: 'Relate SCOTs',
	},
	bodyUnavailableInCCP: 'תוכן זה אינו זמין דרך פורטל הלקוחות של EY Canvas.',
	pyBalance: 'יתרת PY',
	cyBalance: 'יתרת CY',
	designationNotDefined: 'לא הוגדר ייעוד',
	controlRiskAssessment: 'הערכת הסיכון המשולב (CRA)',
	first: 'ראשון',
	noImportedTrialBalance: 'אין מאזן בוחן (TB) מיובא.',
	placeHolderMessageWhenHelixMappingIsTrue: 'לחץ על {0} כדי לקשר כלי ניתוח (analyzer) חדש.',
	documentPrintSuccess: 'Document print in progress. It may take up to ten minutes. Once completed, the print will be added to the temporary files.',
	documentPrintError: 'הדפסת המסמך נכשלה. אנא רענן או נסה שוב לאחר זמן מה. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.',
	backToEvidenceWarningMessage: 'This action could not be completed. Please refresh and try again. If issue persists, please contact the Help Desk.',
	rationaleMissingForLR: 'Each limited risk account shall have a rationale provided',
	rationaleMissingForIR: 'Each insignificant account shall have a rationale provided',
	craSummaryText2: ' Each account shall have designation determined. Click',
	contentDrivingEntity: 'Content driving entity',
	contentDrivingEntityPlaceholder: 'Content driving entity has not been selected',
	rationaleForPlaceholder: 'Provide rationale for this account designation',
	contentDrivingEntityRequired: 'Content driving entity (required)',
	refreshContentLayers: 'Refresh content layers',
	noAccessLabel: 'Unauthorized. Contact your administrator and try again.',
	copyForHelpDeskDetails: 'Copy for Help Desk details',
	copyForHelpDeskDetailsSuccess: 'Details copied to clipboard',

	//toast activity as guest user
	sharedGuidedworkflowEvidenceWarning: 'This is a shared Guided Workflow Activity. The objects and evidence exist in the original engagement and will not be added to this engagement upon unlink. See <a style="color: #467cbe" href="https://live.atlas.ey.com/#library/104?pref=20058/9/5" target="_blank">enablement here</a> for further details.',
	sharedGuidedworkflowResponseWarning: "This is a shared Guided Workflow Activity. The responses are being shared with other activities in the same workspace. View relationships by accessing the'Show relationships' menu item from this activity's summary section."
};

export const groupStructure = {
	createComponent: 'ישות מוחזקת חדשה',
	deleteComponent: 'מחק את הישות המוחזקת',
	manageComponents: 'ניהול ישות מוחזקת',
	emptyComponents: 'לא נוצרו ישויות מוחזקות. צור {newComponent} כדי להתחיל.',
	scope: 'היקף',
	role: 'Role',
	pointOfContact: 'נקודת מגע',
	linkRequest: 'בקשת קישור',
	instructions: 'הוראות',
	instructionsSent: 'ההנחיות נשלחו',
	status: 'סטטוס',
	createComponentInstructionalText: "הזן את פרטי הישות המוחזקת להלן ובחר <b>'שמור וסגור'</b> בכדי לסיים. כדי ליצור ישות מוחזקת נוספת, בחר <b>'שמור וצור אחר'</b>.",
	componentName: 'שם ישות מוחזקת (component)',
	region: 'אזור (region)',
	notUsingCanvas: 'לא משתמש ב-EY Canvas',
	referenceOnly: 'לצרכי סימוכין בלבד',
	saveAndCreateAnother: 'שמור וצור אחד נוסף',
	dueDate: 'תאריך יעד',
	components: 'ישויות מוחזקות',
	allocation: 'הקצאה',
	documents: 'Evidence',
	discussions: 'דיונים',
	EDAP: 'EDAPs',
	siteVisits: 'ביקורים באתר',
	reviewWorkComponent: 'סקירת עבודות שבוצעו',
	other: 'אחרים',
	significantUpdates: 'עדכונים משמעותיים',
	executionComplete: 'שלב הביצוע הושלם',
	gaRoleTypesLabel: [{
		id: 1,
		displayName: 'צוות ראשי'
	},
	{
		id: 2,
		displayName: 'אֵזוֹרִי (regional)'
	},
	{
		id: 3,
		displayName: 'ישות מוחזקת (component)'
	}
	],
	gaLinkStatusLabel: [{
		id: GALinkStatus.NotSent,
		displayName: 'לשלוח'
	},
	{
		id: GALinkStatus.Sent,
		displayName: 'נשלח/לא התקבל'
	},
	{
		id: GALinkStatus.ComponentNotUsingCanvas,
		displayName: 'לא משתמש ב-EY Canvas'
	},
	{
		id: GALinkStatus.ReferenceOnly,
		displayName: 'לצרכי סימוכין בלבד'
	},
	{
		id: GALinkStatus.Accepted,
		displayName: 'התקבל'
	},
	{
		id: GALinkStatus.Rejected,
		displayName: 'נדחה'
	},
	{
		id: GALinkStatus.Unlinked,
		displayName: 'לא מקושר'
	},
	{
		id: GALinkStatus.Pending,
		displayName: 'נשלח/לא התקבל'
	}
	],
	notAvailable: 'לא זמין',
	search: labels.searchPlaceholder,
	noResultsFound: 'לא נמצאו תוצאות',
	noComponentsFound: 'לא נמצאו ישויות מוחזקות.',
	contentSwitcher: [{
		id: gaRoleTypes.primary,
		displayName: 'צוות ראשי'
	},
	{
		id: gaRoleTypes.component,
		displayName: 'ישות מוחזקת (component)'
	},
	{
		id: gaRoleTypes.regional,
		displayName: 'אזור (region)'
	}
	],
	gaRegionTypesLabel: {
		id: gaRegion.notApplicable,
		displayName: 'לא ישים'
	},
	//TODO: To be removed
	pointOfContactValues: [{
		id: pointOfContactTypes.EYcontact,
		displayName: 'איש קשר של EY'
	},
	{
		id: pointOfContactTypes.externalContact,
		displayName: 'איש קשר חיצוני'
	}
	],
	saveAndClose: labels.modalSaveAndClose,
	cancelBtn: labels.modalCancelTitle,
	gaScopesValues: [{
		id: gaScopeType.full,
		displayName: 'מלא'
	},
	{
		id: gaScopeType.specific,
		displayName: 'ספציפי'
	},
	{
		id: gaScopeType.specifiedAuditProcedures,
		displayName: 'נהלי ביקורת ספציפיים (Specified procedures)'
	},
	{
		id: gaScopeType.review,
		displayName: 'Review'
	}
	],
	edit: labels.edit,
	delete: labels.delete,
	tooltipIcon: 'בחר באפשרות זו אם הישות המוחזקת (CT) אינה משתמשת ב-EY Canvas בכדי לקבל הנחיות בביקורת קבוצה ולהגיש תוצרים בין-משרדיים (באמצעות חבילת הדיווח).',
	tooltipReferenceIcon: 'ישויות מוחזקות (CT) המוגדרות כ- <b>Reference only</b> משמשות למטרות ארגוניות בלבד. ההתקשרויות של הישויות המוחזקות הללו לא יקבלו בקשות קישור בין סביבות העבודה, הנחיות או משימות וגם ההתקשרות של צוות הביקורת הראשי (PT) לא תקבל משימה קבוצתית (group task).',
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	modalConfirmBtnLabel: labels.confirmLabel,
	clear: 'Clear',
	clearUpper: labels.clear,
	nameOrEmail: 'הזן את האימייל של איש הקשר של EY',
	editComponent: 'ערוך ישות מוחזקת',
	editComponentInstructionalText: "ערוך את פרטי הישות המוחזקת להלן ובחר <b>\'שמור\'</b> בכדי לסיים.",
	linkAlreadyAcceptedInfo: 'ניתן לערוך רק את שדה האימייל מכיוון שבקשת הקישור כבר נשלחה לצוות הביקורת של הישות המוחזקת.',
	sendAll: 'שלח הכל',
	send: 'לשלוח',
	resend: 'שלח מחדש',
	scopeAndStrategy: 'היקף ואסטרטגיה',
	execution: 'ביצוע הביקורת',
	conclusion: 'מסקנה',
	reportingForms: 'טפסי דיווח (Reporting Forms)',
	manageGroupPermission: 'אין לך הרשאת <b>ניהול קבוצה</b> לבצע פעולה זו. בקש את הרשאת <b>ניהול קבוצה</b> ממנהל ההתקשרות.',
	manageComponentModalDesc: 'צור ישויות מוחזקות חדשות או ערוך ומחק ישויות מוחזקות קיימות להלן.',
	editLinkInfo: 'ניתן לערוך רק את שדה האימייל מכיוון שבקשת הקישור כבר נשלחה לצוות הביקורת של הישות המוחזקת.',
	invalidPointOfContact: 'יש צורך באיש קשר כדי לשלוח את בקשת הקישור. ערוך את הישות המוחזקת בכדי להוסיף את פרטי איש הקשר.',
	manageComponentModalActions: 'פעולות',
	manageComponentModalComponents: 'ישויות מוחזקות',
	manageComponentModalDelete: 'מחק',
	noThereAtLeastOneComponentToSendAll: 'אין ישויות מוחזקות עם סטטוסים כשירים לשם שליחת בקשת קישור. הסטטוס של הישות המוחזקת חייב להיות <b>שלח</b> או <b>שלח מחדש</b> כדי לשלוח בקשת קישור.',
	showKnowledgeDescription: 'הצג כותרת ותיאור מתוך הידע Knowledge',
	hideKnowledgeDescription: 'הסתר כותרת ותיאור מהידע Knowledge',
	instructionName: 'הזן את שם ההנחייה',
	instructionDescriptionPlaceholder: 'הזן תיאור הנחיות',
	selectDueDate: 'תאריך יעד (חובה)',
	show: '‏הצג',
	allocationHeader: 'הקצאה',
	allocationInstructionForKnowledge: 'ניתן להקצות הנחיות מאתר הידע רק לפי היקף. בחר את ההיקף הרלוונטי להלן.',
	allocationInstructionForCustom: 'ניתן להקצות הנחיות מותאמות אישית לפי היקף או לפי ישות מוחזקת. בחר את הקצאת ההנחייה להלן, ולאחר מכן הקצו להיקף/ים או לישות/לישויות הרלוונטיות.',
	allocateScope: 'הקצאה להיקפים',
	allocateComponent: 'הקצאה לישויות מוחזקות',
	pillScopesPlural: 'היקפים (scopes)',
	pillScopesSingular: 'היקף',
	pillComponentsPlural: 'ישויות מוחזקות',
	pillComponentsSingular: 'ישות מוחזקת (component)',
	selectScopesPlaceholder: 'בחר היקפים',
	selectComponentsPlaceholder: 'בחר ישויות מוחזקות',
	searchNoResultFoundText: labels.searchNoResultFoundText,
	newCustomInstruction: 'הנחייה חדשה בהתאמה אישית',
	instructionNameNewCustomInstruction: 'שם הנחיה',
	addCustom: 'הוסף מותאם אישית',
	custom: 'מותאם אישית',
	required: 'נדרש',
	remove: 'הסר',
	selectAll: 'בחר הכל',
	unselectAll: 'בטל את הבחירה בכולם',
	lowerPoC: 'נקודת מגע',
	editPoCTooltip: 'איש קשר לא חוקי או לא קיים. ערוך את איש הקשר בכדי לשלוח בקשת קישור.',
	recomendationType: [{
		id: 1,
		label: 'נדרש'
	},
	{
		id: 2,
		label: 'אופציונלי'
	},
	{
		id: 3,
		label: 'לא ישים'
	}
	],
	confirmLabel: labels.confirmLabel,
	deleteComponentInstructionalText: '<b>האם אתה בטוח שברצונך למחוק ישות מוחזקת (CT) זו ממבנה הקבוצה?</b><br />כאשר הישות המוחזקת תימחק, הקישור לישות המוחזקת יוסר וההתקשרויות לא יוכלו עוד להחליף תיעוד. כמו כן, כל השיוכים בין הישות המוחזקת לסעיפים ולהנחיות יימחקו.',
	noActivitiesAvailable: 'אין פעילויות זמינות.',
	relatedComponents: 'ישויות מוחזקות (components) קשורות',
	relatedComponentsSingular: 'ישות מוחזקת (component) קשורה',
	relatedComponentsPlural: 'ישויות מוחזקות (components) קשורות',
	publish: 'פרסם',
	publishModalHeader: 'פרסם שינויים',
	publishChangesInstructional: '<b>האם אתה בטוח שברצונך לפרסם שינויים בסיכום הנחיות הקבוצה?</b><br />הסט הקודם של הנחיות הקבוצה יימחק. לאחר פרסום השינויים, ניתן לשלוח את ההנחיות המעודכנות מסיכום הנחיות הקבוצה.',
	publishManageGroupPermission: 'עליך להיות בעל הרשאת ניהול הקבוצה כדי לבצע פעולה זו. בקש הרשאה ממנהל התקשרות.',
	lastPublished: 'פורסם לאחרונה: ',
	publishChangesNotAvailable: 'עדיין לא זמין',
	noRecordsFound: labels.noRecordsFound,
	deleteInstruction: 'מחק הנחיות',
	deleteInstructionInstructionalText: '<b>האם אתה בטוח שברצונך למחוק את ההנחיה? </b><br />לא ניתן יהיה לבטל פעולה זו.',
	sendInstructionsTitle: 'שלח הנחיות',
	sendInstructionsInstructionalText: "ודא שההנחיות העדכניות ביותר פורסמו על ידי לחיצה על'פרסם' בעמוד להלן. לאחר מכן, עיין בהנחיות של הישות המוחזקת להלן ובחר'שלח' כדי לשלוח את ההנחיות להתקשרות של הישות המוחזקת.",
	instructionsAlreadySent: 'הגרסה האחרונה של ההנחיות כבר נשלחה.',
	missingDueDates: 'חסר תאריך יעד של טופס דיווח.',
	createInstructionsModalButton: 'צור הנחיות',
	createInstructionsModalActionToastMessageStart: 'חסרות הנחיות להערכת סיכונים קבוצתיות עבור',
	createInstructionsModalActionToastMessageEnd: 'ישויות מוחזקות.',
	createInstructionsModalDescription: 'לישויות המוחזקות הבאות בהיקף ביקורת מלא (full) וספציפי (specific) לא הוקצו הנחיות להערכת סיכונים קבוצתיות. בחירה ב<b>צור</b> תיצור הנחיה להערכת סיכונים קבוצתית עבור כל רכיב המפורט להלן.',
	createInstructionsModalScope: 'היקף',
	createInstructionsModalHeader: 'צור הנחיות',
	createInstructionsModalmodalConfirmBtnLabel: 'צור',
	createInstructionsModalmodalCancelBtnLabel: 'בטל',
	createInstructionsModalmodalCloseBtnTitletip: 'סגור',
	createInstructionsModalNewGraInstructionDescription: 'מצורפת הערכת הסיכונים לסעיפים הרלוונטיים לישות המוחזקת שלך. בדוק את הערכת הסיכונים וודא שלהתקשרות שלך יש את הסעיפים  והסיכונים המזוהים האלה. לגבי סיכונים נוספים המזוהים מקומית או סיכונים שעליהם צוות הביקורת של הישות המוחזקת אינו מסכים, יש לתקשר עם צוות הביקורת של חברת האם, כך שניתן יהיה להתאים את הערכת הסיכונים הן על ידי הצוות הראשי והן על ידי צוות הביקורת של הישות המוחזקת בהתאם.',
	createInstructionsModalErrorMessage: 'יצירת הנחיות להערכת סיכונים קבוצתיות נכשלה עבור הישויות המוחזקות הבאות: <b>{0}</b>. אנא רענן את הדף ונסה שוב.',
	createInstructionsDuplicatedModalErrorMessage: 'יצירת הנחיה להערכת סיכונים קבוצתית נכשלה. לא ניתן לשכפל את שם ההנחייה.',
	gaLinkActionTooltip: {
		NotUsingCanvasLabel: 'לא משתמש ב-EY Canvas',
		NotUsingCanvas: 'לחיצה על <b>שלח</b> תיצור את משימות הקבוצה הראשיות <br/> עבור ישות מוחזקת זו אך <br/> לא יישלחו הנחיות.',
		NotLinkedLabel: 'לא מקושר',
		NotLinked: 'בקשת הקישור לא נשלחה אל <br/> צוות הביקורת של הישות המוחזקת (CT). שלח את בקשת הקישור <br/> כדי לשלוח הנחיות.',
		Unlinked: 'לא מקושר'
	},
	viewHistory: 'הצג היסטוריה',
	viewSentInstructionsTitle: 'צפה בהנחיות שנשלחו',
	save: labels.saveLabel,
	cancel: labels.cancelLabel,
	viewHistoryInstructionalText: 'בחר הנחיות בכדי להציג גרסאות קודמות של הנחיות שנשלחו לצוות הביקורת של הישות המוחזקת (CT).',
	viewHistorySelectInstruction: 'בחר הנחיה',
	viewHistoryDateSent: 'תאריך שליחה: ',
	viewHistoryStatus: 'סטָטוּס: ',
	viewHistoryStatusAccepted: 'התקבל',
	viewHistoryStatusPending: 'ממתין',
	viewHistoryStatusRejected: 'נדחה',
	viewHistoryStatusSystemError: 'System error',
	viewHistorySelectVersion: 'בחר גרסה',
	noAccountsFound: 'לא נמצאו סעיפים או גילויים בהתקשרות זו או באחרות. <br />בחר ב-{link} כדי ליצור או לערוך סעיפים או גילויים קיימים חדשים.',
	generalCommunications: 'תקשורת כללית',
	reportingDeliverables: 'דיווח על תוצרים',
	changesPublishedNotSent: 'השינויים לא נשלחו',
	changesPublishedBrNotSent: 'השינויים<br/>לא נשלחו',
	changesPublishedNotSentYes: 'כן',
	deleteSubScopeInstructionalTextModal: 'האם אתה בטוח שברצונך למחוק <br/> את תת-ההיקף (Sub scope) שנבחר?',
	deleteSubScopeTitleModal: 'מחק תת-ההיקף (sub-scope)',
	riskAssessmentModal: {
		headerText: 'הערכת סיכונים',
		modalCloseBtnTitletip: labels.close,
		manageAndDisclosures: "הקישור'ניהול חשבונות וגילויים'",
		next: 'הישות המוחזקת הבאה',
		back: 'ישות מוחזקת (CT) קודמת'
	},
	riskAssessment: 'הערכת סיכונים',
	preview: 'מסמך לצפייה',
	accountsAndDisclosureSummary: 'חשבון וגילוי',
	noAccountSnapshotPlaceholder: 'אין נתוני חשבון להצגה עבור ישות מוחזקת זו.',
	createOversightProjectButtonLabel: 'צור פרויקט EY Canvas Oversight',
	createOversightProjectTitle: 'האם אתה רוצה שפרויקט EY Canvas Oversight יווצר עם ההתקשרות הראשית הזו?',
	createOversightProjectDescription: 'ההתקשרות(יות) האזוריות (regional) ו/או של ישויות מוחזקות (component) ב- EY Canvas, המזוהות במבנה קבוצה זה ימולאו אוטומטית כחלק מהגדרת הפרויקט של ה- EY Canvas Oversight.',
	createOversightModalHeader: 'שם פרויקט EY Canvas Oversight',
	createOversightModalDescription: 'הזן את השם של פרויקט EY Canvas Oversight.',
	createOversightModalTextLabel: 'שם הפרוייקט',
	projectRedirectionButtonLabel: 'פרויקטים של EY Canvas Oversight',
	projectAssociationTextLabel: 'ישנם פרויקטים של EY Canvas Oversight הקשורים להתקשרות זו.',
	sendLinkDisableTooltip: 'התקשרות זו הועתקה, לרבות ישויות מוחזקות (components) בתהליך הביקורת של הקבוצה (Group audit flow). לא ניתן לשלוח קישורים מחדש. צור ישות מוחזקת (component) חדשה ושלח קישור לפי הצורך.',
	instructionsCannotBeSentUntilPublished: 'Instructions cannot be sent until they are published.'
};

export const groupInvolvement = {
	NoComponentsAvailables: 'לא נוצרה ישות מוחזקת (component) .<b> ניהול ישויות מוחזקות</b> בכדי להתחיל.',
	GroupInvolvementToastMsgStart: 'חסר טופס מעורבות קבוצתית עבור',
	GroupInvolvementToastMsgEnd: 'ישות(יות) מוחזקת(ות)',
	CreateGroupInvolvementHeader: 'צור טפסי מעורבות',
	GroupInvolvementInstructionalText: 'לישות(יות) המוחזקת(ות) הבאות אין טפסי מעורבות קבוצתיים שהוקצו להם.<br/> Selecting &#39;<b>Create</b>&#39; תיצור טופס מעורבות קבוצתית עבור כל ישות מוחזקת המפורטת להלן.',
	createGroupInvolvementDocumentErrorMessage: 'יצירת מסמך מעורבות קבוצתית נכשלה עבור הישויות המוחזקות הבאות: <b>{0}</b>. אנא רענן את הדף ונסה שוב.',
	createGroupInvolvementDocumentSuccessMessage: 'טפסי מעורבות קבוצתיים נוצרו בהצלחה. רענן את העמוד תוך 30 שניות כדי לראות מסמכים זמינים.',
	involvementTypePlanned: 'סוג המעורבות המתוכנן',
	significantUpdatesToPlannedInvolvement: 'עדכונים משמעותיים למעורבות המתוכננת',
	executionComplete: 'שלב הביצוע הושלם',
	generateGroupInvolvementCommunications: 'הדפס טפסי מעורבות',
	generateGroupInvolvementInstructionalText: 'לישויות המוחזקות הבאות יש טפסי מעורבות קבוצתיות המשויכות אליהם. בחר את טפסי המעורבות הקבוצתית של איזה ישות מוחזקת שיכללו במסמך אחד למטה.<br /><br /> לאחר בחירת הרכיבים, בחירה ב<b>&#39;צור&#39;</b> צור מסמך מעורבות קבוצתית אחד עם מסמך מעורבות קבוצתית של כל ישות מוחזקת המפורטת להלן.',
	componentTeams: 'צוותי ביקורת של ישויות מוחזקות',
	noComponentsSelectedErrorMessage: 'בחר ישויות מוחזקות ליצירת תקשורת מעורבות קבוצתית.',
	documentName: 'חבילת מעורבות קבוצתית של {taskName}',
	selectAll: groupStructure.selectAll,
	unselectAll: groupStructure.unselectAll,
	modalConfirmBtnLabel: groupStructure.createInstructionsModalmodalConfirmBtnLabel,
	modalCancelBtnLabel: groupStructure.cancelBtn,
	modalCloseBtnTitletip: groupStructure.modalCloseBtnTitletip
};

export const itPlanning = {
	supportingITColumnsHeaders: {
		applicationTool: {
			name: 'Applications/Tools'
		},
		network: {
			name: 'רשתות'
		},
		database: {
			name: 'מסדי נתונים'
		},
		operatingSystem: {
			name: 'מערכות הפעלה'
		}
	},
	relatedITProcessesColumnsHeaders: {
		relatedITProcess: 'Related IT processes',
		category: 'Category'
	},
	itPlanningPlaceholders: {
		smartEvidenceSourceEntityId: 'טכנולוגיה קשורה אינה זמינה עבור מסמך זה',
		smartEvidenceSourceId: 'לא נמצא אובייקט קשור. קשר אובייקט כדי להתחיל.',
	},
	relatedITProcessesPlaceholders: {
		smartEvidenceSourceEntityId: 'Related IT process not available for this document',
		smartEvidenceSourceId: 'לא נמצא אובייקט קשור. קשר אובייקט כדי להתחיל.',
		relatedITProcessEmpty: 'No IT process related to the technology'
	},
	noTechnologiesIdentified: 'לא זוהו טכנולוגיות',
	supportingITEmpty: 'אין יישומים/כלים תומכים הקשורים לטכנולוגיה',
	supportingITNetworkEmpty: 'אין רשתות תומכות הקשורות לטכנולוגיה',
	searchPlaceholder: 'חפש',
	newTechnology: 'טכנולוגיה חדשה',
	noSupportingDatabases: 'אין מאגרי מידע תומכים הקשורים לטכנולוגיה',
	createEntityFormDocument: 'צור מסמך',
	noSupportingOperatingSystem: 'No supporting operating systems related to the technology',
	manageTechnology: 'Manage technology'
};

export const itRiskFactors = {
	accepted: 'התקבל',
	rejected: 'נִדחֶה',
	accept: 'קבל',
	reject: 'דחה',
	rejectionRationale: 'נימוק דחייה',
	rejectionCategory: 'קטגוריית דחייה',
	rejectionRationaleRequired: 'Rejection rationale (required)',
	rejectionCategoryRequired: 'Rejection category (required)',
	riskName: 'Risk name',
	smartEvidenceValidations: {
		smartEvidenceSourceEntityId: 'גורמי סיכון אינם זמינים עבור מסמך זה',
		smartEvidenceSourceId: 'No related object. Relate an object to get started.'
	},
	manageChangePlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'כל הסיכונים נדחו'
	},
	manageOperationsPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	},
	manageAccessPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	},
	SDLCPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'כל הסיכונים נדחו'
	},
	manageSecuritySettingsPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	}
};

export const rejectionTypeResource = [{
	id: rejectionType.itRiskOther,
	label: 'ITRisk Other'
},
{
	id: rejectionType.itRiskOption2,
	label: 'ITRisk -"Option 2"'
},
{
	id: rejectionType.itRiskOption3,
	label: 'ITRisk -"Option 3"'
}
];

export const sampleList = {
	newSample: 'מדגם חדש',
	createSampleModalDescription: "הזן פרטים למדגם להלן ובחר'<b>{0}</b>' כדי לסיים. כדי ליצור מדגם נוסף, בחר'<b>{1}</b>'.",
	saveAndCreateAnother: 'שמור וצור אחד נוסף',
	saveAndClose: 'שמור וסגור',
	sampleDescription: 'תיאור למדגם (חובה)',
	sampleDate: 'תאריך המדגם (חובה)',
	sampleListId: 'מזהה רשימת מדגם (Sample List)',
	ok: 'אשר',
	addSample: 'הוסף מדגם',
	cancel: 'בטל',
	saveAndCloseForHeader: 'שמור וסגור',
	saveAndCreateAnotherHeader: 'שמור וצור אחד נוסף',
	required: 'נדרש',
	description: 'תיאור המדגם',
	date: 'תאריך',
	attributeStatus: 'מאפיינים',
	tags: 'תגים',
	open: 'פתח',
	notApplicableLabel: 'לא ישים',
	notPresent: 'לא נוכח',
	present: 'קיים',
	pagingShowtext: 'הצג',
	placeHolderMessage: 'אין מדגמים זמינים. לחץ על {clickHere} כדי להתחיל.',
	noSampleListAvailable: 'אין רשימת מדגם זמינה',
	editSample: 'ערוך מדגם',
	editSampleDescription: "ערוך את פרטי המדגם להלן ובחר'<b>{0}</b>' כדי לסיים.",
	editSampleSave: 'שמור',
	sampleCanNotBeCreated: 'לא ניתן ליצור מדגמים עבור מסמך זה.',
	noRelatedObject: 'אין אובייקט קשור. קשר אובייקט כדי להתחיל.',
	noResultsFound: 'לא נמצאו תוצאות'
};

export const AdditionDocumentationLabels = {
	addAdditionalDocumentation: 'הוסף תיעוד נוסף',
	editAdditionalDocTitle: 'עריכת תיעוד נוסף',
	removeAdditionalDocumentation: 'הסר תיעוד נוסף',
	cancel: 'בטל',
	save: 'שמור',
	of: 'שֶׁל',
	additionalDocTitlePlaceholder: 'תיעוד נוסף (נדרש)',
	additionalDocTitle: 'תיעוד נוסף (נדרש)',
	remove: 'הסר',
	enterAdditionalDocTitle: "הזן תיעוד נוסף למטה ובחר <b>'{0}'</b> כדי לסיים.",
	editAdditionalDocDesc: "ערוך תיעוד נוסף להלן ובחר <b>'{0}'</b> כדי לסיים.",
	characters: 'תווים',
	required: 'נדרש',
	descriptionMaxLengthError: 'התגובה חורגת מהמקסימום המותר.',
	attributeIndexLabel: 'אינדקס מאפיינים'
};

export const sampletAttributeConstants = [{
	id: 1,
	label: 'פתח'
},
{
	id: 3,
	label: 'קיים'
},
{
	id: 7,
	label: 'הצג עם הערות'
},
{
	id: 5,
	label: 'לא נוכח'
},
{
	id: 4,
	label: 'לא ישים'
},
];

export const groupInstructions = {
	ALRAPackageModalTitle: 'שם חבילה של ALRA',
	ALRAPackageModalInstructionalText: 'הזן את השם של חבילת ALRA שאתה מוסיף ל-Evidence.',
	ALRAPackageModalNameField: 'הזן שם',
	ALRAPackageSuccessToastMessage: 'תהליך יצירת החבילה החל. עשויות לחלוף עד עשר דקות עד להשלמתו.',
	ALRAPackageInProgressToastMessage: 'תהליך יצירת החבילה בעיצומו, עשויות להימשך עד עשר דקות עד להשלמתו.',
	delete: labels.delete,
	deleteSectionModalTitle: labels.deleteSection,
	deleteSectionInstructionalText: '<b>האם אתה בטוח שברצונך למחוק את המקטע?</b><br />לא ניתן לבטל פעולה זו.',
	deleteSectionTooltipText: 'יש למחוק את ההנחיות<br />לפני שניתן למחוק את המקטע.',
	modalConfirmBtnLabel: labels.confirmLabel,
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	missing: 'חָסֵר',
	sendAllModalTriggerButton: 'שלח הכל',
	sendAllModalTooltipText: 'אין הנחיות זמינות לשלוח לצוותי הביקורת של הישויות המוחזרות (CTs).',
	publishModalTooltipText: 'יש לפרסם את הנחיות הקבוצה לפני שליחתן. כאשר ההנחיות מתפרסמות, כל השינויים נשמרים כהנחיות חדשות, תוך עוקף את הגרסה הקודמת של ההנחיות. לאחר מכן ניתן לשלוח הנחיות חדשות אלה לצוותי הביקורת של הישויות המוחזקות.',
	sendAllModalErrorMessage: 'Group instructions for the following Components were not sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk. <br /> <b>{0}</b>',
	sendAllModalHeaderText: 'שלח את כל ההנחיות',
	sendAllModalConfirmBtnLabel: 'לשלוח',
	sendAllModalCancelBtnLabel: 'בטל',
	sendAllModalCloseBtnTitletip: 'סגור',
	sendAllModalDescription: 'בחירה ב- <b>Send</b> תשלח הנחיות לצוותי הביקורת של הישויות המוחזקות הבאות.',
	generateGroupRiskAssessmentCommunications: 'צור ALRA קבוצתי',
	bulkALRAPackageName: 'חבילת הערכת סיכונים ברמת החשבון {instructionName}',
	groupInstructionSummaryReport: 'דוח סיכום הנחיה קבוצתית',
	groupInstructionSummaryReportTitletip: 'הצג וייצא את פרטי ההנחיות לקבוצה, היסטוריית ההנחיות ושינויים במיפוי ישות מוחזקת/סעיף.',
	exportGroupRiskAssessment: 'ייצוא סיכום',
	reportingDeliverables: groupStructure.reportingDeliverables,
	groupRiskAssessment: 'הערכת סיכונים קבוצתית'
};

export const sectionTitles = [{
	id: KnowledgeSectionIds.GeneralCommunications,
	sectionTitle: groupStructure.generalCommunications
},
{
	id: KnowledgeSectionIds.ScopeOfWork,
	sectionTitle: 'היקף העבודה'
},
{
	id: KnowledgeSectionIds.ReportingForms,
	sectionTitle: groupStructure.reportingDeliverables
},
{
	id: KnowledgeSectionIds.ProceduresPerformedCentrally,
	sectionTitle: 'נהלים שבוצעו באופן מרכזי'
},
{
	id: KnowledgeSectionIds.GroupRiskAssessment,
	sectionTitle: groupInstructions.groupRiskAssessment
},
{
	id: KnowledgeSectionIds.OtherCommunications,
	sectionTitle: 'תקשורת אחרת'
}
];

export const groupAuditToolbar = {
	search: labels.placeholderForSearch
};

export const AccountType = [{
	id: 1,
	accounttypename: 'סעיף מהותי'
},
{
	id: 2,
	accounttypename: 'סעיף בסיכון מוגבל (limited risk account)'
},
{
	id: 3,
	accounttypename: 'סעיף לא מהותי (Insignificant account)'
},
{
	id: 4,
	accounttypename: 'סעיף אחר'
},
{
	id: 5,
	accounttypename: 'גילוי משמעותי'
}
];

export const PriorityType = [{
	value: 1,
	label: 'נמוך'
},
{
	value: 2,
	label: 'בינוני'
},
{
	value: 3,
	label: 'גבוה'
},
{
	value: 4,
	label: 'קריטי'
}
];

export const AccountSummaryAccountType = [{
	id: '0',
	accounttypename: 'כל הסעיפים'
},
{
	id: '1',
	accounttypename: 'סעיפים מהותיים'
},
{
	id: '2',
	accounttypename: 'סעיפים בעלי סיכון מוגבל (Limited risk accounts)'
},
{
	id: '3',
	accounttypename: 'סעיפים לא מהותיים (Insignificant accounts)'
},
{
	id: '4',
	accounttypename: 'סעיף - אחרים'
},
{
	id: '5',
	accounttypename: 'גילויים משמעותיים'
}
];

export const TaskStatus = [{
	id: 1,
	status: 'פתח'
},
{
	id: 2,
	status: 'מתבצע'
},
{
	id: 3,
	status: 'בסקירה'
},
{
	id: 4,
	status: 'המשימות למענה  בביקורת הושלמו'
},
{
	id: 5,
	status: 'הוסר'
}
];

export const reviewNoteLabels = {
	/*Review Notes*/
	emptyNoteDetailsMessage: 'בחר הערה לצפייה בפרטים. בכדי להפעיל כמות גדולה של בקרות השתמש במקש ה Ctrl או במקש ה shift ובחר הערות סקירה מרובות. אם ברצונך לעבוד על הערה בודדת בחר מתוך רשימה זו.',
	documentReviewNotesLabel: 'הערות במסמך',
	addNewReviewNoteButtonText: 'הוסף הערה',
	noNotesAssociatedWithDocumentLabel: 'אין הערות המשויכות למסמך זה',
	allNotesLabel: 'כל ההערות',
	charactersLabel: 'תווים',
	myNotesLabel: 'ההערות שלי',
	showClearedLabel: 'הצג Cleared',
	showClosedLabel: 'הצג Closed',
	toLabel: 'אל',
	toUserLabel: 'אל',
	ofLabel: 'של',
	textAreaPlaceholder: 'הזן הערה',
	addNewNoteModalClose: 'Close',
	addNewNoteModalTitleLabel: 'הוסף הערה חדשה',
	editNoteModalTitleLabel: 'ערוך הערה',
	deleteIconHoverText: 'מחק',
	deleteIconModalAcceptText: 'מחק',
	deleteIconModalConfirmMessage: 'האם אתה בטוח שברצונך למחוק את תשובתך להערה זו?',
	deleteIconModalConfirmMessageParent: 'האם הינך בטוח שברצונך למחוק את ההערה שנבחרה?',
	deleteIconModalTitleLabel: 'מחק הערה',
	deleteReplyIconModalTitle: 'מחק תשובה',
	emptyRepliesMessage: 'עדיין אין תשובות',
	replyInputPlaceholder: 'השב להערה זו',
	replyText: 'טקסט תגובה',
	editReplyModelTitle: 'ערוך תשובה',
	noteDueDateLabel: 'היעד בעוד: ',
	fromUserLabel: 'מ',
	priorityLabel: 'עדיפות',
	dueDateLabel: 'תאריך יעד',
	dueLabel: 'היעד בעוד',
	status: 'סטטוס',
	noteModifiedDateLabel: 'השתנה: ',
	cancelLabel: 'בטל',
	saveLabel: 'שמור',
	clearedBy: 'נסגר על ידי',
	closedBy: 'נסגר על ידי',
	reopenedBy: 'נפתח מחדש על ידי',
	reply: 'ענה',
	editIconHoverTextLabel: 'ערוך',
	required: 'נדרש',
	closeTitle: 'Close',
	otherEngagementNotes: 'הערות אחרות בתיק הביקורת',
	closeLabel: 'Close',
	showMore: 'הצג עוד',
	showLess: 'הצג פחות',
	showMoreEllipsis: 'הצג עוד ...',
	showLessEllipsis: 'הצג פחות ...',
	noResultFound: 'לא נמצאו תוצאות',
	engagementNameLabel: 'שם ההתקשרות: ',
	drag: 'גרור',
	formMaxLength: 'הטקסט אינו יכול לחרוג מ-{number} תווים.',
	voiceNoteButtonLabel: 'הודעה קולית',
	stopRecordingButtonLabel: 'הפסק',
	reopen: 'פתח שוב',
	noNotesFound: 'לא נמצאו הערות',
	noNotesFoundInstructional: 'השאר הערה באמצעות הקלטים למטה. הקצה את ההערה למשתמש וציין את העדיפות ואת תאריך היעד.',
	microphoneBlockedMessage: 'אפשר לדפדפן לגשת למיקרופון שלך כדי להשתמש בהערות קוליות. אם כבר איפשרת, אנא רענן ונסה שוב.',
	microphoneBlockedOnVideoMessage: 'אפשר לדפדפן לגשת למיקרופון שלך כדי להשתמש בקול בהקלטת מסך. אם כבר איפשרת זאת, אנא רענן ונסה שוב.',
	notInMainWindowVoice: 'הקלטות קול אינן מותרות בתוך סל האפשרויות, נא לפתוח את המסמך בלשונית חדשה בכדי לבצע את הפעולה.',
	notInMainWindowScreen: 'הקלטות מסך אסורות בתוך סל האפשרויות, נא לפתוח את המסמך בלשונית חדשה בכדי לבצע את הפעולה.',
	voiceNoteNotAvailable: 'הערה קולית והקלטת מסך אינם זמינים בתצוגת  סל האפשרויות. עבור לתצוגת מסך מלא בכדי להשתמש בתכונות אלה.',
	playButtonTitle: 'להפעיל',
	deleteButtonTitle: 'מחק',
	pauseButtonTitle: 'השהייה',
	screenRecord: 'הקלטת מסך',
	playbackReview: 'סקירת שמע'
};

export const IndividualAccountAttributeLabels = {
	attributesNotAvailableForDocument: 'מאפייני הסעיף אינם זמינים עבור מסמך זה.',
	noRelatedOnject: 'אין אובייקט קשור. קשר אובייקט כדי להתחיל.',
	noAttributesAvailable: 'אין מאפיינים זמינים',
	noRisksAvailable: 'אין סיכונים זמינים',
	attributeStandardRomms: 'מאפייםן ROMM סטנדרטי',
	continueButtonTitle: 'המשך',
	closeButtonTitle: 'בטל',
	newAssertionModalPlaceholder: 'בחירה זו תגרום לכך שמצג(י) ההנהלה הבא(ים) יזוהו כבעלי סיכון מובנה נמוך יותר (lower IR) שלא זוהו בעבר כרלוונטיות. האם ברצונך להמשיך?',
	assertion: 'מצג הנהלה',
	inherentRiskType: 'סיכון מובנה (IR)',
	assertionModalTitle: 'מצג הנהלה חדש',
	riskType: 'נמוך יותר'
};

export const entities = [{
	id: 0,
	name: 'הכל'
},
{
	id: 1,
	name: 'Document'
},
{
	id: 2,
	name: 'LeadSchedule'
},
{
	id: 3,
	name: 'סעיף'
},
{
	id: 4,
	name: 'תת התהליך המהותי (SCOT)'
},
{
	id: 5,
	name: 'תהליך IT'
},
{
	id: 6,
	name: 'תוכנית ביקורת'
},
{
	id: 7,
	name: 'סיכון'
},
{
	id: 8,
	name: 'משימה'
},
{
	id: 9,
	name: 'הצגה מוטעית'
},
{
	id: 10,
	name: 'ליקוי'
},
{
	id: 11,
	name: 'רכיב GA'
},
{
	id: 12,
	name: 'הוראות רכיב GA'
},
{
	id: 13,
	name: 'עדות רכיבי GA'
},
{
	id: 14,
	name: 'היקף GA'
},
{
	id: 15,
	name: 'הוראה ראשית של GA'
},
{
	id: 16,
	name: 'GA ראשי'
},
{
	id: 17,
	name: 'בקשת לקוח'
},
{
	id: 18,
	name: 'WCGW'
},
{
	id: 19,
	name: 'בקרה'
},
{
	id: 20,
	name: 'אפליקציית IT'
},
{
	id: 21,
	name: 'Canvas form'
},
{
	id: 22,
	name: 'מדור טופס'
},
{
	id: 23,
	name: 'גוף טופס'
},
{
	id: 24,
	name: 'טַעֲנָה'
},
{
	id: 25,
	name: 'מעורבות לקוח'
},
{
	id: 26,
	name: 'קבוצת לקוחות'
},
{
	id: 27,
	name: 'תג התקשרות '
},
{
	id: 28,
	name: 'תיק ביקורת'
},
{
	id: 29,
	name: 'כותרת טופס'
},
{
	id: 30,
	name: 'סטטוס טופס'
},
{
	id: 31,
	name: 'משתמש מעורבות'
},
{
	id: 32,
	name: 'משתמש בקבוצת לקוחות'
},
{
	id: 33,
	name: 'פריט אוטומציה'
},
{
	id: 34,
	name: 'בקרות כלליות במערכות המידע (ITGC)'
},
{
	id: 35,
	name: 'סיכון IT'
},
{
	id: 36,
	name: 'פריט אוטומציה'
}
];

export const PaceType = [{
	id: 1,
	paceTypename: 'Low'
},
{
	id: 2,
	paceTypename: 'Moderate'
},
{
	id: 3,
	paceTypename: 'High'
},
{
	id: 4,
	paceTypename: 'Close Monitoring'
}
];

export const DocumentHelper = {
	401: 'לא ניתן להשלים את הפעולה. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה אל התמיכה הטכנית.',
	413: 'המסמך חורג מגודל הקובץ המרבי המותר (250MB) ולא ניתן להעלותו. הקטן את גודל הקובץ ונסה שוב.',
	412: 'כבר קיים מסמך בשם זהה בתיק הביקורת',
	414: 'השם חורג מהאורך המרבי (120 תווים).',
	4099: 'קיים כבר מסמך באותו שם',
	/*this is a hack as we dont always know why conflict happened.*/
	410: 'מסמך זה נמחק ולכן לא ניתן לפתוח אותו.',
	411: 'מסמכים ריקים אינם מותרים.'
};

export const Errors = {
	/*Doc Helper Custom Messages */
	0: 'החיבור אבד. התחבר מחדש ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',
	10: 'זוהתה בעיה עם EY Canvas Document Helper. לחץ <a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">כאן</a> לקבלת הנחיות כיצד לפתור בעיה זו.',
	101: 'סטטוס תיק ביקורת לא חוקי',
	102: 'לא נמצא משתמש תיק ביקורת חוקי',
	103: 'לא הושלמו דרישות אי תלות של המשתמש (Engagement User Independence) בתיק הביקורת',
	104: 'חסרים Azure AD Scopes נדרשים',
	105: 'אירעה שגיאה בקבלת הרשאות תיק הביקורת. רענן את הדף ונסה שוב. אם הבעיה נמשכת, אנא פנה לתמיכה הטכנית.',
	106: "Unauthorized. Contact your administrator and try again.",
	107: 'משתמש מעורבות חוקי לא נמצא. רענן את הדף ונסה שוב. אם הבעיה נמשכת פנה לדלפק העזרה.',
	108: 'לא ניתן להשלים את ההסרה. רענן את הדף ונסה שוב. אם הבעיה נמשכת פנה לדלפק העזרה.',
	303: 'מסמך עם שם זהה מועלה כעת.',
	403: 'Access to this document is not available.  If this document is shared ensure you have access to the source engagement.  Refresh the page and try again.  If the error persists contact the Help Desk.',
	406: 'המסמך אינו יכול להיות ריק.',
	412: 'מסמך עם שם זהה כבר קיים בתיק הביקורת.',
	414: 'השם חורג מהאורך המרבי (120 תווים).',
	411: 'מסמכים ריקים אינם מותרים.',
	500: 'החיבור אבד. התחבר מחדש ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',
	600: 'לא ניתן להשלים את הפעולה בשלב זה. רענן את הדף ונסה שוב. אם הבעיה נמשכת פנה לתמיכה הטכנית.',
	601: 'שגיאה בהורדת צילום המסך. רענן את הדף ונסה שוב. אם הבעיה ממשיכה פנה לתמיכה הטכנית.',
	602: 'המסמך כבר נמצא בשיתוף פעולה',
	935: 'למשתמש אין הרשאות מספיקות לביצוע הפעולה.',
	zip: 'לא ניתן להעלות את קובץ ה- zip מכיוון שהוא מכיל אחד או יותר מסוגי קבצים שאינם נתמכים, או שהוא מכיל יותר מהכמות המקסימלית של קבצי ה- zip האפשרית',
	401000: 'התגלה שינוי ברשת, אנא טען מחדש את הדף כדי להמשיך',

	/*Accounts*/
	1001: 'יצירת החשבון נכשלה.',
	1002: 'חסר שם הסעיף.',
	1003: 'The selected Account has been deleted. Close this modal to see the updated list.',
	1004: 'לא נמצאו תוצאות. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	1005: 'Engagement ID שגוי.',
	1006: 'סוג סעיף לא חוקי.',
	1007: 'סוג דוח לא חוקי.',
	1008: 'החשבון שנבחר נמחק. סגור מודל זה כדי לראות את הרשימה המעודכנת.',
	1009: 'הבקשה להציג סעיף לפי מזהה ID נכשלה.',
	1010: 'הבקשה לחיפוש סעיף לפי מזהה תיק הביקורת נכשלה.',
	1011: 'הבקשה להציג סיכום SEM למזהה ID לסעיף נכשלה, הואיל ואינה חוקית.',
	1012: 'סוג סיכום לא חוקי.',
	1013: 'יצירת בקשה לסקירת הסעיף (Account Review) נכשלה.',
	1014: 'בקשת למחיקת סקירת הסעיף (Account Review) נכשלה.',
	1015: 'בקשת ליצירת סקירת סעיף לא חוקית.',
	1016: 'מספר זיהוי ID סקירת חשבון לא חוקי.',
	1017: 'החשבון אינו חלק מתיק ביקורת זה או שהוא נמחק.',
	1018: 'סקירת סעיף נוצרה על ידי משתמש אחר.',
	1019: 'סעיף נמחק על-ידי משתמש אחר. רענן את הדף ונסה שוב.',
	1020: 'נדרש לעדכן PSP לסעיף',
	1024: 'שם הסעיף הוא יותר מ-500 תווים.',
	1025: 'סעיף בסיכון מוגבל (Limited Risk Account) או סעיף לא מהותי (Insignificant Account) לא יכול להכיל אומדן',
	1026: 'הסעיף לא יכול לכלול מצגי הנהלה כפולים',
	1027: 'מזהה מצג הנהלה (assertion) לא חוקי',
	1037: 'PSP Indexes לא יכולים להיות משוכפלים',
	1039: 'החשבון שנבחר נמחק. סגור מודל זה כדי לראות את הרשימה המעודכנת.',
	1048: 'סוג הביצוע של החשבון אינו חוקי.',
	1053: 'לסעיף יש סיכון או אומדן משויך, לא ניתן להגדיר אותו כחשבון בסיכון מוגבל ( Limited Risk Account) או לחשבון לא משמעותי (Insignificant Account).',
	1054: 'מצגי הנהלה שיש למחוק היו קשורים לסיכון או אומדן',
	1065: 'לא ניתן לבצע שינויים במצגי ההנהלה שמקושרים אליהם סיכון משמעותי (SR), או סיכון הונאה (FR), או סיכון להצגה מוטעית מהותית (ROMM) או אומדן קשור. אתה צריך להסיר את היחסים האלה קודם.',
	1070: 'מזהה מאזן הבוחן לא יכול להיות null כאשר כלול  Helix.',
	1072: 'לא ניתן להשלים את הפעולה. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',

	1266: 'ההתקשרות הגיעה למספר המרבי של מסמכים במצב עריכה מרובה משתמשים. הכנס מסמכים מסוימים בחזרה ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	1267: 'Document is conflicted. Resolve conflicts and try again.  If the issue persists, contact the Help Desk.',
	1268: 'Document is already in co-edit mode. end co-edit mode and try again.  If the issue persists, contact the Help Desk.',
	1269: 'Document is a shared evidence. Unlink and try again.  If the issue persists, contact the Help Desk.',
	1270: 'לא ניתן למחוק או לעדכן גירסאות מסמכים במצב עריכה מרובה משתמשים. אנא סיים את העריכה מרובת המשתמשים ונסה שוב. אם הבעיה נמשכת, אנא צור קשר עם דלפק התמיכה.',
	1271: 'המסמך אינו במצב עריכה משותפת או שמצב סיום עריכה משותפת מתבצע. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית,',

	/*Assertions*/
	2001: 'הבקשה ליצירה לא חוקית.',
	2002: 'שם מצג ההנהלה (assertion) חסר.',
	2003: 'חסר מצג הנהלה (assertion).',
	2004: 'הבקשה להציג מצג הנהלה (assertion) נכשלה.',
	2005: 'Engagement ID שגוי.',
	2006: 'הבקשה להציג מצג הנהלה (assertion) לפי מזהה ID נכשלה.',
	2007: 'הבקשה להציג מצג הנהלה (assertion) ל- WCGWs נכשלה.',

	/*Risks*/
	4001: 'לא היתה אפשרות להשלים את הפעולה כעת. רענן את הדף ונסה שוב. במידה והבעיה נמשכת, אנא פנה לתמיכה הטכנית.',
	4002: 'חסר שם הסיכון.',
	4003: 'חסר סיכון.',
	4004: 'הבקשה להציג סיכון נכשלה.',
	4005: 'Engagement ID שגוי.',
	4006: 'הבקשה להציג סיכון באמצעות מזהה ID נכשלה.',
	4007: 'בקשת שאילתה לא חוקית.',
	4008: 'אומדן זה אינו זמין עוד. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	4009: 'בקשת עדכון לא חוקית.',
	4010: 'כבר הוקצה WCGW ספציפי לסיכון זה',
	4011: 'רשימת WCGW אינה יכולה להיות לא בתוקף.',
	4012: 'נכשל הנסיון להגדיר סוגי סיכון.',
	4013: 'הבקשה ליצירה לא חוקית.',
	4014: 'מצג ההנהלה (assertion) המקושר אינו חוקי. אנא רענן את הדף ונסה שוב. אם שגיאה זו נמשכת, פנה אל התמיכה הטכנית.',
	4015: 'ה- WCGW אינו חוקי. אנא רענן את הדף ונסה שנית. במידה ושגיאה זו נמשכת, אנא פנה לתמיכה הטכנית.',
	4016: 'הסיכון/אומדן שנבחר נמחק. סגור מודל זה כדי לראות את הרשימה המעודכנת.',
	4017: 'מצג ההנהלה (assertion) אינו זמין לסיכון. אנא רענן את הדף ונסה שוב. אם שגיאה זו נמשכת, פנה אל התמיכה הטכנית.',
	4018: 'מזהה סוג הסיכון (risk type ID) שהועבר אינו חוקי.',
	4019: 'שם הסיכון אינו חוקי.',
	4020: 'מזהה המסמך (Document ID) לא חוקי.',
	4021: 'שם הסיכון לא יכלול יותר מ -500 תווים.',
	4023: 'רשימת מזהי מצגי הנהלה אינה יכולה להיות ריק.',
	4024: 'לא ניתן ליצור טופס תיעוד לסיכון מוגבל עקב שגיאה. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	4025: 'Account Id שגוי.',
	4026: 'מזהה מצג הנהלה (Assertion ID) לא חוקי.',
	4027: 'AssertionRisk Modelלא יכול להיות ריק',
	4031: 'אפשרות לא חוקית של סיכון או גוף טופס.',
	4035: 'לא ניתן לערוך את IsHigherRisk עבור סיכוני משמעותי (SR) או סיכון הונאה (FR).',
	4036: 'KnowledgeAssertionId לא יכול להיות ריק אם לא הועבר מזהה מצג הנהלה. KnowledgeAssertionId חייב להיות ב-enums',
	4037: 'KnowledgeAssertionId כבר קיים עבור סעיף זה.',
	4038: 'RiskTypeId אינו תואם לאפשרויות המותרות.',
	4062: 'תת תהליך מהותי (SCOT) זה אינו זמין יותר. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	4063: 'לא ניתן לערוך את הקישור לתת התהליך המהותי (SCOT). רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	4076: 'This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	4079: 'This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*TASK*/
	5001: 'Get all tasks failed.',
	5002: 'משימה זו אינה זמינה עוד בתיק ביקורת זה.',
	5003: "הבקשה'הצג מסמכים קשורים למשימה' נכשלה. הפרמטרים לבקשה לא חוקיים.",
	5004: "הבקשה'הצג מסמך קשור למשימה' נכשלה. הפרמטרים לבקשה לא חוקיים.",
	5005: "הבקשה'הצג משימה לפי מזהה ID' נכשלה.",
	5006: 'הבקשה להציג קטגוריות משימה נכשלה.',
	5007: 'הבקשה להציג משימת תת קטגוריות נכשלה.',
	5008: 'הבקשה להציג תיאור משימה נכשלה.',
	5009: 'הבקשה להציג משימת בקשות לקוח נכשלה.',
	5010: 'הבקשה לשמירת משימות ללקוח נכשלה.',
	5011: 'המשימה שאתה מנסה לקשר פריטים אליה נמחקה או נדחתה.',
	5012: 'הפריט שאתה מנסה לקשר נמחק.',
	5013: 'המשימה שאתה מנסה לקשר פריטים אליה נמחקה או נדחתה.',
	5014: 'המסמך שאתה מנסה לקשר נמחק.',
	5015: 'הבקשה להציג משימת ראיות ביקורת נכשלה.',
	5016: 'הבקשה להציג משימת WCGW נכשלה.',
	5017: 'ה- Engagement ID אמור להיות גדול מ- 0.',
	5018: 'לא ניתן להשלים את השיוך. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לדלפק העזרה.',
	5019: 'תיאור המשימה ריק',
	5020: 'המשימה שנבחרה נמחקה או נדחתה. בהתאם, אין אפשרות להשלים פעולה זו בשלב זה.',
	5021: 'מזהה  (ID) מקור תיק הביקורת חסר.',
	5022: 'שמירת ההערה נכשלה',
	5023: 'משתמש תיק הביקורת לא נמצא.',
	5024: 'בקשה למחיקת משימה נכשלה.',
	5025: 'בקשה למחיקת משימות נכשלה.',
	5026: 'רשימת המשימות ריקה',
	5027: 'לא נמצאו הערות סקירה.',
	5028: 'נדרש שם קובץ.',
	5029: 'סיומת הקובץ נדרשת.',
	5030: 'אין אפשרות לכלול בשם הקובץ: */:< > \ \? |"',
	5031: 'שגיאה בעת עדכון שם המסמך.',
	5032: 'מזהה המסמך (Document ID) לא חוקי.',
	5033: 'סוג הפעולה לא נמצא.',
	5034: 'שינוי סטטוס המשימה נכשל',
	5035: 'לא ניתן להשלים את הפעולה שאתה מנסה לבצע בשלב זה. אנא נסה שוב מאוחר יותר. פנה לתמיכה הטכנית (Help Desk) אם שגיאה זו נמשכת.',
	5036: 'התוכן לא יכול להיות לא בתוקף או ריק בתא.',
	5037: 'הבקשה אינה יכולה להיות ריקה או לא בתוקף.',
	5038: 'הזן שם קובץ ייחודי כדי להמשיך.',
	5039: 'נדרש שם קובץ.',
	5040: 'שם הקובץ מוגבל ל-100 תווים.',
	5041: 'אין אפשרות לכלול בשם הקובץ: */:< > \ \? |"',
	5042: 'המשימה שנבחרה נדחתה.',
	5043: 'המשימה היא תת-תהליך של מספר משימות.',
	5044: 'המשימה היא משימת סיום שלב (Milestone Task).',
	5045: 'המשימה היא לא מסוג PSP או OSP.',
	5046: 'חריגה ממגבלת התווים.',
	5047: 'חריגה ממגבלת התווים.',
	5048: 'שדה חובה.',
	5049: 'אין אפשרות להסיר את המסמך שנבחר ממשימה זו. אנא רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה אל התמיכה הטכנית.',
	5050: 'מזהה  (ID) משימת קבוצה לא יכול להיות אפס או לא חוקי',
	5051: 'מזהה מקטע המשימה (Task section ID) אינו אמור להיות אפס או לא חוקי',
	5052: 'שגיאה בעת ניסיון להוסיף מסמך זה לפעילות הנוכחית. הבקשה נכשלה.',
	5053: 'שגיאה בעת ניסיון לעדכן מסמך זה במשימה הנוכחית. הבקשה נכשלה.',
	5054: 'שגיאה בעת ניסיון להוסיף עותק של מסמך זה במשימה הנוכחית. הבקשה נכשלה.',
	5055: 'שגיאה בעת ניסיון לשנות את שם המסמך. הבקשה נכשלה.',
	5056: 'אין אפשרות לערוך את כותרת המשימה עבור לידע כללי או משימת קבוצה',
	5057: 'סוג המשימה אמור להיות מסוג Ost',
	5058: 'אין אפשרות להסיר את המסמך מהמשימה, הואיל והוא משוייך למערכת.',
	5059: 'ערך ה- Time Phase לא חוקי.',
	5060: 'שגיאה בהוספת ראיית ביקורת. הבקשה נכשלה.',
	5061: 'משתמש אחר עדכן את המידע המוצג. רענן את הדף ונסה שוב. אם השגיאה נמשכת, פנה אל התמיכה הטכנית.',
	5062: 'המסמך המבוקש אינו זמין עבור הערוץ (Channel) שנבחר לתיק הביקורת שלך ב- EY Atlas. אנא צור קשר עם התמיכה הטכנית (Help Desk) על מנת שיינתן מידע לכותבי התוכן לצורך הכללתם בעתיד.',
	5063: 'פעולת תיקון לא חוקית.',
	5064: 'המשימה שנבחרה נמחקה או נדחתה. בהתאם, אין אפשרות להשלים פעולה זו בשלב זה.',
	5065: 'אין אפשרות לעדכן את סוג מקור המשימה. הבקשה שגויה.',
	5066: 'תקלה בקבלת ההנחיות. הבקשה נכשלה. ',
	5067: 'אין אפשרות לעדכן את סוג אופי המשימה. הבקשה שגויה.',
	5068: 'אין אפשרות לעדכן את סוג אופי המשימה. הבקשה שגויה.',
	5069: 'מחק הקצאת משימה שנכשלה.',
	5070: 'אחת או יותר מהמשימות שנבחרו נמחקו. אנא נסה שוב או צור קשר עם התמיכה הטכנית אם השגיאה נמשכת.',
	5071: 'אין אפשרות לעדכן את ההקצאה. הבקשה שגויה.',
	5072: 'לא נמצא מכין. הבקשה שגויה.',
	5073: 'לא נמצאה הקצאה.',
	5074: 'שמירת המשימה שהוקצתה נכשלה',
	5075: "ניתן להקצות את אותו חבר צוות רק למשימה אחת אחרת שבה לא הוא ה'מכין'. אנא נסה שוב או צור קשר עם התמיכה הטכנית אם השגיאה נמשכת.",
	5076: 'חברת הצוות הנבחר אינו חבר פעיל בתיק ביקורת זה. אנא נסה שנית או צור קשר עם התמיכה הטכנית אם השגיאה נמשכת.',
	5077: 'אחת או יותר מהמשימות שנבחרו נמחקו. אנא נסה שוב או צור קשר עם התמיכה הטכנית אם השגיאה נמשכת.',
	5078: 'הקצאת המשימה הנבחרת הוסרה. אנא נסה שוב או צור קשר עם התמיכה הטכנית אם השגיאה נמשכת.',
	5079: 'אחת או יותר מהמשימות שנבחרו נמחקו. אנא נסה שוב או צור קשר עם התמיכה הטכנית אם השגיאה נמשכת.',
	5080: 'לא קיימת רשימת הגירסאות של המסמך',
	5081: 'לא ניתן להקצות מחדש כמכין (Preparer) את המשתמש שמוקצה כעת למשימה זו. אנא נסה שוב או צור קשר עם תמיכה הטכנית (Help Desk) אם השגיאה נמשכת.',
	5082: 'עדכון נכשל. שם המסמך צריך להיות ייחודי לתיק הביקורת. רענן את הדף כדי להסיר הודעה זו.',
	5083: 'מגבלת התווים של פרטי המשימה חורגת.',
	5084: 'בקשה ליצירת מסמך המשימה נכשלה.',
	5085: 'בקשה למחיקת מסמך המשימה נכשלה.',
	5086: 'בקשת יצרת מסירת המשימה נכשלה.',
	5087: 'בקשת לחיבור (patch) המשימה נכשלה.',
	5088: 'משימה זו חייבת להכיל ראיות למסירה. נסה שוב או צור קשר עם התמיכה הטכנית (Help Desk).',
	5089: 'כל הראיות הקשורות למשימה זו (למעט ה- paper profiles) חייבות להכיל לפחות מכין (preparer) וסוקר אחד כדי לסמן שהושלמו. נסה שוב וצור קשר עם התמיכה הטכנית (Help Desk) אם השגיאה נמשכת.',
	5091: 'שם ה- Paper profile כבר קיים בתיק הביקורת.',
	5092: 'השם לא יכול להכיל אף אחד מהתווים הבאים:*/:<>\\?|\\',
	5093: 'שם פרופיל הנייר חורג מהאורך המרבי (100 תווים).',
	5111: 'הסעיף, מצג ההנהלה (ה- assertion), או הסעיף בסיכון מוגבל  (limited risk account) שנבחר נמחק. אנא רענן את הדף ונסה שנית. אם השגיאה נמשכת, אנא פנה לתמיכה הטכנית.',
	5116: 'סוג מסמך אינו חוקי',
	5139: 'לא ניתן להשלים את השיוך. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לדלפק העזרה.',
	5131: 'לא ניתן להשלים את השיוך. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לדלפק העזרה.',
	5146: 'לא ניתן לסמן שהמשימה הושלמה.',
	5156: 'לא ניתן לערוך את קישור המשימה. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',

	/*WCGW*/
	6001: 'יצירת WCGW נכשלה.',
	6002: 'חסר שם WCGW.',
	6003: 'חסר WCGW.',
	6004: 'קבלת ה- WCGW נכשל.',
	6005: 'Engagement ID שגוי.',
	6006: 'מזהה מצג הנהלה (Assertion ID) לא חוקי.',
	6007: 'הבקשה להציג WCGW לפי מזהה ID נכשלה.',
	6008: 'בקשה לא חוקית.',
	6009: 'מזהה ה- WCGW לא חוקי.',
	6010: 'לא היתה אפשרות לשייך את המשימה ל-WCGW.',
	6011: 'ה- WCGW שנבחר נמחק.',
	6012: 'המשימה וה- WCGW לא מקושרים לאותו מצג הנהלה (assertion).',
	6013: 'המשימה שנבחרה לא שייכת לאותו תיק ביקורת.',
	6014: 'לא ניתן היה לנתק את המשימה מה- WCGW.',
	6015: 'לא היתה אפשרות לשייך את המשימה ל-WCGW.',
	6016: 'המשימה נדחית ולא ניתן לשייך אותה ל- WCGW.',
	6017: 'המשימה אינה משימת אבן דרך (milestone) ולא ניתן לשייך אותה ל- WCGW.',
	6018: 'המשימה אינה תת-תהליך של מספר משימות (build step task) ואינה יכולה להיות משויכת ל- WCGW.',
	6019: 'המשימה אינה PSP או OSP ולא ניתן לשייך אותה ל- WCGW.',
	6020: 'המשימה ו- WCGW משויכים לאותו יעד ביקורת ולא ניתן לשייך ל- WCGW.',

	/*Engagement*/
	7001: "'הבקשה באמצעות מזהה ID' לא נמצא.",
	7002: 'בקשה לקבל תיקי ביקורת לפי מספר זיהוי סביבת העבודה נכשלה.',
	7003: 'בקשה לקבל את כל ישויות תיקי הביקורת נכשלה.',
	7004: 'בקשה לקבל ישויות לפי מספר זיהוי נכשלה.',
	7005: 'בקשה לקבל את כל המשתמשים של תיק הביקורת נכשלה.',
	7006: 'שם המשפחה לא יכול לעלות על 250 תווים',
	7007: 'שגיאת מסוג משתמש לא חוקי.',
	7008: 'השם הפרטי לא יכול לעלות על 250 תווים',
	7009: 'ממשק המשתמש (User GUI) לא יכול להיות לא חוקי (null).',
	7010: 'שגיאת סטטוס משתמש לא חוקי.',
	7011: 'בקשה ליצירת משתמש תיק ביקורת נכשלה.',
	7012: 'לא ניתן להזמין את {0} {1} מכיוון שהוא / היא כבר חבר צוות פעיל או ממתין.',
	7013: 'ראשי התיבות לא יעלו על 3 תווים',
	7014: 'דף הבית (Landing Page) של EY Canvas אינו נגיש בשלב זה. נסה שוב ואם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',
	7015: 'לא ניתן להזמין את {0} {1} מכיוון שקבוצות למתן הרשאות הגישה הבאות: {2} נמחקו מתיק הביקורת. רענן ונסה שוב.',
	7016: 'לא ניתן להזמין את {0} {1} מכיוון שהמשתמש כבר חבר צוות פעיל בקבוצות למתן הרשאות הגישה הבאות: {2}',
	7017: 'הדומיין אסור לקבוצות שנבחרו: {0}',
	7018: 'כתובת הדוא"ל אינה יכול להיות לא חוקית (null).',
	7019: 'השם הפרטי אינו יכול להיות לא חוקי (null).',
	7020: 'שם המשפחה אינו יכול להיות לא חוקי (null)',
	7021: 'ראשי התיבות של המשתמש אינם יכולים להיות לא חוקיים (null).',
	7022: 'משתמש PrimaryOffice אינו יכול להיות לא חוקי (null).',
	7023: 'שם הכניסה למשתמש אינו יכול להיות לא חוקי (null).',
	7024: 'המשתמש EYRole אינו יכול להיות לא חוקי (null).',
	7025: 'תפקיד המשתמש בתיק הביקורת אינו יכול להיות לא חוקי (null).',
	7026: 'לא ניתן להשלים את הפעולה. נסה שוב ואם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',
	7027: 'כתובת דוא"ל לא חוקית',
	7028: 'בקשה לחיבור (patch) משתמש תיק ביקורת נכשלה.',
	7029: 'משתמשי תיק ביקורת -  מספר זיהוי סטטוס משתמש תיק ביקורת לא חוקי.',
	7030: 'משתמשי תיק ביקורת -  מספר זיהוי תפקיד משתמש תיק ביקורת לא חוקי.',
	7031: 'מספר זיהוי משתמש תיק ביקורת אחד או יותר לא נמצא.',
	7032: 'דוא"ל אינו יכול להכיל יותר מ- 250 תווים.',
	7033: 'המשתמש המבוקש לא יכול להיות  לא חוקי (null).',
	7034: 'תור מעבד ההודעות האוניברסלי נכשל.',
	7035: 'ראשי התיבות לא יעלו על 3 תווים',
	7036: 'השם הפרטי לא יכול לעלות על 250 תווים',
	7037: 'שם המשפחה לא יכול לעלות על 250 תווים',
	7038: 'בקשה ליצירת משתמש חיצוני נכשלה.',
	7039: 'לא ניתן להזמין משתמש אחד או יותר מכיוון שהם כבר חבר צוות פעיל או ממתין. רענן את הדף ונסה שוב, אם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk)',
	7040: 'השם הפרטי לא יכול לעלות על 250 תווים',
	7041: 'שם המשפחה לא יכול לעלות על 250 תווים',
	7042: 'ראשי התיבות לא יעלו על 3 תווים',
	7043: 'GPN אינו יכול להיות גדול מ -250 תווים.',
	7044: 'ממשק המשתמש (GUI) אינו יכול להיות גדול מ -250 תווים.',
	7045: 'קבלת משתמש חיצוני על פי מספר זיהוי ID נכשלה',
	7046: 'המשתמש אינו יכול להיות לא חוקי (null).',
	7047: 'לא ניתן לשמור שינויים כעת. נסה שוב ואם הבעיה נמשכת, צור קשר עם התמיכה הטכנית (Help Desk).',
	7048: 'ה- EY Canvas Landing Page אינו נגיש כעת והוא נדרש לעריכה. נסה שוב ואם הבעיה נמשכת, פנה אל התמיכה הטכנית (Help Desk).',
	7049: 'בקשת עדכון משתמש תיק הביקורת נכשלה.',
	7050: 'לא ניתן להשבית חברי צוות. על תיק הביקורת לכלול לפחות חבר צוות אחד שיש לו הרשאות לנהל את תיק הביקורת. עדכן את הבחירה שלך ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',
	7051: 'קבלת משתמש פנימי על פי מספר זיהוי ID נכשלה',
	7052: 'המשתמש לא נמצא.',
	7053: "'הבקשה באמצעות מזהה ID' לא נמצא.",
	7054: 'בקשה לקבלת קישורים מהירים על פי מספר זיהוי ID תיק ביקורת (engagement ID) נכשלה.',
	7055: 'אין לך הרשאה להוסיף חברי צוות קודמים. פנה אל חבר צוות ביקורת אחר עם הרשאות מתאימות לבצע פעולה זו.',
	7056: 'EY Canvas לא יכול לשמור שינויים כעת. נסה שוב ואם הבעיה נמשכת, צור קשר עם התמיכה הטכנית (Help Desk).',
	7057: 'אין הרשאה להוסיף חברי צוות חדשים. פנה אל חבר צוות ביקורת אחר עם הרשאות מתאימות לבצע פעולה זו.',
	7058: 'סטטוס המשתמש השתנה. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',
	7062: 'פורטל הלקוחות של EY Canvas אינו זמין כעת ונדרש לעדכן את פרטי חברי הצוות הקיימים. נסה שוב ואם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',
	7063: 'לא ניתן להפוך חברים חיצוניים ללא פעילים. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',
	7064: 'פעולת תיקון לא חוקית.',
	7065: '{0} {1} לא ניתנים להפעלה באחת או יותר מקבוצות למתן הרשאות גישה. הדומיין (domain) אסור לקבוצות שנבחרו: {2}.',
	7066: '{0} אינו משתמש חוקי',
	7067: 'בקשה לרישום משתמש חיצוני נכשלה',
	7068: 'פורטל הלקוחות של EY Canvas אינו זמין כעת ונדרש לעדכן את פרטי חברי הצוות הקיימים. נסה שוב ואם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',
	7069: 'קבוצות למתן הרשאות הגישה שנבחרו אינן פעילות: {0}. הסר ונסה שוב.',
	7072: 'אירעה שגיאה במהלך ביטול קישור תהליך תיק הביקורת. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית (Help Desk) אם השגיאה נמשכת.',
	7074: 'לא ניתן לשמור שינויים. תיק הביקורת חייב לכלול לפחות חבר צוות פעיל אחד בעל הרשאות גישה לנהל את תיק הביקורת ועומד בדרישות אי התלות. אם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',
	7079: 'הבקשה להגשת אי התלות נכשלה.',
	7080: 'מזהה משתמש לא חוקי או שליחת הצהרת אי התלות אינה אפשרית, מכיוון שמזהה המשתמש אינו משוייך למשתמש המחובר.',
	7081: "השלם את כל השאלות לפני לחיצה על'שלח'. השתמש באפשרות'הצג לא הושלם' כדי לסנן ולהציג רק את השאלות שטרם הושלמו. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.",
	7082: 'לא נמצא מסמך אי תלות לבקשה.',
	7083: 'הקריאה לסיכום SDM נכשלה.',
	7084: 'מזהה המשתמש אינו חוקי או שפעולה של אי התלות אינה מורשית עבור המשתמש המחובר.',
	7085: 'הערת אי תלות צריכה להיות עד 4,000 תווים.',
	7086: 'הבקשה לפעולה של אי תלות נכשלה.',
	7087: 'תפקידך חייב להיות שותף אחראי על הביקורת (PIC) או אקזקיוטיב דיירקטור (MD) בכדי להעניק, למנוע או לעקוף גישה עבור משתמש זה.',
	7088: 'שינויים בשליחת אי התלות נכשלו, נסה שוב מאוחר יותר.',
	7098: 'מזהה Pacetype לא חוקי.',
	7099: 'אין תבנית אי תלות זמינה. נסה שוב, ובמידה והבעיה נמשכת צור קשר עם התמיכה הטכנית.',
	7154: 'לא נמצאו משתמשים עבור טופס ה- Cavas הנוכחי',
	7155: 'שליחת פרופיל אינה מותרת לתיק ביקורת משוחזר.',
	7156: 'התוכן עובר רענון כעת. נסה שוב מאוחר יותר. אם השגיאה נמשכת זמן ממושך אנא פנה לתמיכה הטכנית של IT (IT Help Desk).',
	7158: 'המסמך אינו זמין. רענן את הדף ונסה שוב. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.',

	/*SCOT*/
	8001: 'יצירת תת תהליך מהותי (SCOT) נכשלה.',
	8002: 'חסר שם תת תהליך מהותי (SCOT).',
	8003: 'חסר תת תהליך מהותי (SCOT)',
	8004: 'קבלת תת תהליך מהותי (SCOT) נכשל.',
	8005: 'Engagement ID שגוי.',
	8006: 'מזהה מצג הנהלה (Assertion ID) לא חוקי.',
	8007: 'הבקשה להציג תת תהליך מהותי (SCOT) באמצעות מזהה ID נכשלה.',
	8008: 'בקשה לא חוקית.',
	8009: 'The selected SCOT has been deleted. Close this modal to see the updated list.',
	8010: 'מזהה תת התהליך המהותי SCOT לא יכול להיות ריק או Null.',
	8011: 'מזהה תת התהליך המהותי SCOT חייב להיות גדול מאפס.',
	8012: 'מזהה המסמך אינו חוקי.',
	8013: 'הבקשה לעדכון תת התהליך המהותי SCOT נכשלה.',
	8014: 'שם תת התהליך המהותי SCOT אינו יכול להיות Null או ריק.',
	8015: 'שם תת התהליך המהותי SCOT אינו יכול להיות יותר מ- 500 תווים.',
	8016: 'סוג האסטרטגיה המשויך לתת התהליך המהותי SCOT אינו חוקי.',
	8017: 'סוג תת התהליך המהותי  (SCOT) אינו חוקי.',
	8018: 'יישום ה-IT של תת התהליך המהותי SCOT אינו חוקי.',
	8019: 'יישום אוטומטי (ITApplication) בתת תהליך מהותי (SCOT) צריך להיות ריק כאשר אין יישום שכזה שחל.',
	8028: 'The selected SCOT has been deleted. Close this modal to see the updated list.',

	/*User*/
	10001: 'העדפות משתמש חיבור למערכת לא נמצאו.',
	10002: 'בקשה לקבלת משתמש נכשלה.',
	10003: 'בקשה לקבלת נוכחות המשתמש נכשלה.',
	10005: 'לא ניתן לאחזר את פרטי המשתמש',

	/*Risk Type*/
	11001: 'הבקשה ליצירה לא חוקית.',
	11002: 'חסר שם של סוג הסיכון.',
	11003: 'חסר סוג סיכון.',
	11004: 'הבקשה להציג סוג סיכון נכשלה.',
	11005: 'Engagement ID שגוי.',

	/*TaskDocuments*/
	80004: 'פעולה אחת או יותר אינה חוקית.',
	80005: 'מזהה המסמך אינו חוקי.',

	/*Edit Control*/
	83001: 'השגת הבקרות נכשלה.',
	83002: 'השגת הבקרה על ידי מזהה נכשלה',
	83003: 'מזהה הבקרה ריק או null.',
	83004: 'מזהה בקרה לא חוקי',
	83005: 'מזהה המסמך אינו חוקי.',
	83006: 'חסר שם בקרה.',
	83007: 'אורך שם בקרה לא חוקי.',
	83008: 'בקשה לא חוקית',
	83009: 'מזהה תדירות בקרה לא חוקי',
	83010: 'מזהה סוג בקרה לא חוקי',
	83011: 'יישומי בקרת IT לא חוקי.',
	83012: 'מזהה סוג האפקטיביות בתכנון הבקרה לא חוקי',
	83013: 'מזהי יישומי IT לא חוקיים.',
	83014: 'רק יישומי ITA מסוג SO מותרים אם  הבקרה היא מסוג מונעת ידנית או בקרה ידנית מגלה.',
	83015: 'רק בבקרות מסוג ITDM עשויות להיבחר בדיקות IPE ידניות.',
	83016: 'בקרות בסיכון נמוך (Lower Risk controls) אינן מותרות עבור התקשרויות עם פרופיל זה.',
	83017: 'שכפל FilterSCOTs.',
	83018: 'שם הבקרה הוא יותר מ-500 תווים.',
	83019: 'זיהויי WCGW לא חוקיים',
	83020: 'אין להשתמש במזהי WCGW כפולים.',
	83021: 'אין להשתמש במזהי ITApplication  כפולים.',
	83022: 'הפרמטר {0} אינו חוקי.',
	83023: 'העמוד הנוכחי אינו חוקי.',
	83024: 'גודל עמוד לא חוקי.',
	83025: 'מחרוזת חיפוש לא תכלול יותר מ-100 תווים.',
	83026: 'יישום IT ללא לשכת שירות יכול להיות קשור רק לבקרה מסוג ITDpendentManualControl או ITApplicationControl',
	83027: 'שכפל FilterWCGWs.',
	83028: 'מזהה בקרת ידע לא חוקי',

	112000: 'לא נמצאו מסמכים עבור מסמכי המקור והיעד.',
	112001: 'לא ניתן לבצע את הבקשה כיוון שהיא לא בתוקף.',
	112002: 'גוף הבקשה אינו יכול להיות null או ריק בקריאה זו.',
	112003: 'מזהי מסמכי המקור והיעד לא צריכים להיות שווים.',
	112004: 'מזהה מסמך המקור לא צריך להיות null או ריק.',
	112005: 'מזהה מסמך היעד לא צריך להיות null או ריק.',
	112006: 'מקור EngagementId אינו יכול להיות null או ריק.',
	112007: 'Target EngagementId אינו יכול להיות null או ריק.',
	112008: 'מסמך המקור אינו תקף עבור ההתקשרות הנתונה.',
	112009: 'מסמך היעד אינו תקף להתקשרות הנתונה.',
	112010: 'מזהה תיק ביקורת היעד לא נמצא.',
	112011: 'אין מספיק תפקידים לקישור טפסים עבור תיק ביקורת המקור. עבוד עם מנהל ההתקשרות בכדי לקבל מספיק הרשאות.',
	112012: 'אין מספיק תפקידים לקישור טפסים עבור תיק ביקורת היעד. עבוד עם מנהל ההתקשרות בכדי לקבל מספיק הרשאות.',
	112013: 'משתמש לא חוקי עבור תיק ביקורת המקורי.',
	112014: 'משתמש לא חוקי עבור תיק ביקורת היעד.',
	112015: 'סוג מסמך מקור לא חוקי לשיתוף.',
	112016: 'סוג מסמך יעד לא חוקי לשיתוף.',
	112017: 'סוגי מסמכי המקור והיעד אינם מתאימים.',
	112018: 'מזהים של טופס ידע מקור ויעד אינם מתאימים.',
	112019: 'קישור משותף כבר קיים עבור מסמכי מקור ויעד.',
	112020: 'סביבות העבודה של תיק ביקורת המקור והיעד אינן תואמות.',
	112021: 'מסמך יעד לא יכול להיות יעד.',
	112022: 'הפעילות שנבחרה כבר משותפת עם פעילויות אחרות, ולא ניתן לבחור בה עוד לשיתוף.',
	112023: 'מסמך המקור אינו יכול להיות יעד.',
	112024: 'לא ניתן למצוא את מזהה תיק הביקורת של מזהה מסמך היעד.',
	112025: 'לא ניתן למצוא את מזהה תיק הביקורת של מזהה מסמך המקור.',
	112026: 'מסמך המקור של המקור ומזהה מסמך היעד לא אמורים להיות שווים.',
	112027: 'לא ניתן להמשיך עם שליחת הפרופיל הואיל וההתקשרות שיתפה את פעילויות ההפעלה של EY Canvas FIT. בטל את הקישור בין הפעילויות בכדי להמשיך.',
	112028: 'מזהה תיק הביקורת של המקור והיעד לא צריכים להיות שווים.',
	112029: 'מזהה תיק הביקורת ומזהה מסמך המקור והיעד צריכים להיות בהתאמה עם מזהה תיק הביקורת ומזהה המסמך.',
	112030: 'המסמך אינו תקף להתקשרות הנתונה.',
	112031: 'מזהה המסמך לא צריך להיות null או ריק.',
	112032: 'מזהה תיק הביקורת לא צריך להיות null או ריק.',
	112033: 'מזהי מסמכי היעד צריכים להיות ייחודיים.',
	112034: 'מסמכי יעד או מקור כבר שותפו.',
	112035: 'עבור מסמך קיים ביחס לתגובה מקושרת יכול להיות רק יעד אחד בלבד.',

	/*MissingDocument*/
	116001: 'Create form failed.',
	116002: 'לא נמצא מסמך ידע עבור DocumentTypeID הנתון.',
	116004: 'יצירת המסמך נכשלה. אנא רענן או נסה שוב מאוחר יותר. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',

	/* Annotation Errors*/
	12001: 'פרמטרי בקשה לא חוקיים.',
	12002: 'בקשה ליצירת הערה נכשלה.',
	12003: 'קבלת הערה נכשלה.',
	12004: 'Engagement ID שגוי.',
	12005: 'מספרי הזיהוי באוסף חייבים להיות גדולים מאפס.',
	12006: 'אוסף מספר זיהוי לא יכול להיות ריק.',
	12007: 'מספר זיהוי משימה לא חוקי.',
	12008: 'מזהה המסמך (Document ID) לא חוקי.',
	12009: 'חייב להיות מספר זיהוי מסמך או מספר זיהוי משימה חוקי.',
	12010: 'התגובות נדרשות להיות בשרשור (Replies require parent)',
	12011: 'מזהה סטטוס לא חוקי.',
	12012: 'על סוג המסמך להיות 440GL.',
	12013: 'סוג הסבר לא חוקי.',
	12014: 'משתמש תיק ביקורת לא חוקי.',
	12015: 'מסמך זה נמחק על-ידי משתמש אחר.',
	12016: 'בקשת עדכון ההערה נכשלה.',
	12017: 'ההערה שאתה משיב לה נמחקה על-ידי חבר צוות אחר. רענן את הדף ונסה שוב.',
	12018: 'סוג פעולת שינוי ההערה אינו יכול להיות לא חוקי (null).',
	12019: 'הערת הסקירה נמחקה.',
	12020: 'פעולה לא חוקית לביצוע.',
	12021: 'המשתמש אינו מחבר ההערה.',
	12022: 'חסר משתמש מורשה.',
	12023: 'משתמש מורשה אינו קיים ואינו שייך לתיק הביקורת.',
	12024: 'נדרש משתמש שהוקצה.',
	12025: 'משתמש שהוקצה אינו קיים ואינו שייך לתיק הביקורת.',
	12026: 'תגובות לא חוקיות.',
	12027: 'התגובות לא יכולות להיות ריקות.',
	12028: 'נדרש תאריך יעד.',
	12029: 'נדרשת עדיפות תקפה.',
	12030: 'הסטטוס של הערה זו השתנה. אינך יכול עוד לערוך או להגיב להערה. אנא סגור ופתח מחדש את החלון כדי לראות נתונים מעודכנים ולהמשיך בעריכה.',
	12031: 'ההערה חייבת להיות ברמה העליונה.',
	12032: 'ההערה חייבת שלא להיות ברמה העליונה.',
	12033: 'לפחות אחד מהערכים הבאים חייב שלא להיות ריק: סוג עדיפות, תאריך יעד, סטטוס או משתמש שהוקצה.',
	12034: 'התגובה חורגת מהאורך המרבי (4,000 תווים).',
	12035: 'מחרוזת החיפוש חורגת מהאורך המרבי (500 תווים).',
	12036: 'אפשרי לקבל רק מספר זיהוי משימה או מספר זיהוי מסמך, אין אפשרות לקבל את שניהם',
	12037: 'פעולת תיקון לא חוקית.',
	12038: 'הינך מנסה לערוך הערה שכבר לא קיימת. רענן את הדף ונסה שוב.',
	12039: 'הינך מנסה לערוך את אותה הערה כמו חבר צוות אחר. רענן את הדף ונסה שוב.',
	12040: 'בקשה לקבלת המשתמשים בהערות (Annotation users) נכשלה.',
	12041: 'קבלת המשתמשים בהערה (Annotation users) נכשלה. ערך שאילתה לא חוקי.',
	12042: 'סוג המסמך אינו חוקי ליצירת הערה.',
	12043: 'הינך מנסה להוסיף הערה למשימה או למסמך שכבר לא קיים. רענן את הדף ונסה שוב.',
	12044: 'הינך מנסה לערוך תשובה להערה שכבר לא קיימת. רענן את הדף ונסה שוב.',
	12045: 'הערת הסקירה לא נמצאה.',
	12046: 'ההערה שנבחרה כבר נמחקה על ידי משתמש אחר.',
	12047: 'ביכולתך למחוק רק תשובות להערות שנמצאות בסטטוס פתוח. רענן את הדף ונסה שוב.',
	12048: 'הינך מנסה לשנות את הסטטוס של הערה שאינה קיימת עוד. רענן את הדף ונסה שוב.',
	12049: 'הינך מנסה למחוק תשובה להערה שכבר נמחקה על ידי חבר צוות אחר. רענן את הדף ונסה שוב.',
	12050: 'ניתן למחוק רק הערות שנמצאות בסטטוס סגורות (Closed)',
	12051: 'ניתן ליצור הערות של סוג תגובה רק למסמכי Helix בתוקף.',
	12052: 'ההערה של סוג התגובה שאתה מחפש נמחקה.',
	12060: 'מספר הפניה נדרש חייב להיות גדול מ- 0 ונמוך מ -1000.',
	12061: 'מספר האסמכתא חייב להיות null.',
	12062: 'ניתן ליצור הערות רק לתגובה.',
	12066: 'אתה מנסה להשיב להערה שאינה פתוחה.',
	12067: 'אתה מנסה למחוק תשובה להערה שכבר נמחקה על ידי חבר צוות אחר. רענן את הדף ונסה שוב.',
	12068: 'אתה מנסה להוסיף הערה עם הקלטה שאינה חוקית או שאינה קיימת עוד. רענן את הדף ונסה שוב.',
	12069: 'אתה מנסה לעדכן הערה עם הקלטה שאינה חוקית או שאינה קיימת עוד. רענן את הדף ונסה שוב.',
	12070: 'ניתן להוסיף הערה אחת בלבד לכל דוח כספי. אנא ערוך את הקיים.',
	12071: 'מחיקת המידע נכשלה. בבקשה נסה שוב. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.',

	/*FlowchartStepControl*/
	123054: 'Control relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',
	123045: 'This control is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepWCGW*/
	123022: 'שלב תרשים זרימה זה אינו זמין עוד. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	123023: 'שלב תרשים זרימה זה אינו זמין עוד. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	123055: 'WCGW relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepITApplicationSO*/
	123056: 'IT Application / service organization relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the issue persists.',



	/*FlowchartStepDocument*/
	123048: 'לא ניתן לערוך את הקישור למסמך. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	123033: 'שלב תרשים זרימה זה אינו זמין עוד. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	123002: 'מסמך זה אינו זמין יותר. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',

	/*Configuration*/
	13001: 'תצורות לא נמצאו.',
	13002: 'בקשה לקבלת תצורות (Configurations) API נכשלה.',

	/*Documents*/
	14001: 'לא ניתן לבצע את הבקשה כיוון שהיא לא בתוקף.',
	14002: 'המסמך לא נמצא. הבקשה נכשלה.',
	14003: 'ה- Engagement ID אמור להיות גדול מ- 0.',
	14004: 'ה- Document ID אינו יכול להיות לא בתוקף או ריק.',
	14005: 'שגיאה בניסיון להשיג משימות משויכות. הבקשה נכשלה.',
	14006: 'אין אפשרות למחוק את המסמך שנבחר. אנא נסה שוב מאוחר יותר. אם הבעיה נמשכת, פנה אל התמיכה הטכנית.',
	14007: 'אירעה שגיאה בלתי צפויה.',
	14008: 'אירעה שגיאה לא צפויה במהלך החתימה.',
	14009: 'אירעה שגיאה בעת הוספת חתימה.',
	14010: 'מספר זיהוי האישור צריך להיות גדול מאפס.',
	14011: 'אירעה שגיאה בעת מחיקת חתימה.',
	14012: 'הגוף לא יכול להיות ריק.',
	14013: 'לא ניתן לבטל את הקישור של המסמך שנבחר. נסה שוב מאוחר יותר. אם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',
	14014: 'בקשה לקבלת סעיף לפי מספר זיהוי מסמך נכשלה.',
	14015: 'ישות קשורה לא חוקית.',
	14016: 'בקשה לקבלת אישור המסמך נכשלה.',
	14017: 'בקשה לקבלת כל ממצאי המסמכים נכלשה.',
	14018: 'בקשה לקלבת כל המסמכים נכשלה.',
	14019: 'החתימה לא נמצאה.',
	14020: 'ערך פעולה לא חוקי.',
	14021: 'סוג ממצא לא חוקי.',
	14022: 'המסמך אינו שייך לתיק הביקורת.',
	14023: 'שינוי סוג המסמך אינו חוקי.',
	14024: 'בקשה לקבלת כל המסמכים נכשלה. הפרמטר אינו חוקי.',
	14025: 'אירעה שגיאה בעת יצירת סקירת המסמך. בקשת ה- API נכשלה.',
	14026: 'אירעה שגיאה בעת מחיקת סקירת המסמך. בקשת ה- API נכשלה.',
	14027: 'מספר זיהוי תיק הביקורת לא צריך להיות לא חוקי (null) או ריק (empty).',
	14028: 'מספר זיהוי המשתמש לא חוקי.',
	14029: 'המשתמש אינו מורשה לבצע פעולה זו.',
	14030: 'לא נמצא מסמך עם מספר הזיהוי שהועבר (passed ID) בתיק הביקורת.',
	14031: 'מספר זיהוי סקירת המסמך אינו תקף.',
	14032: 'סקירת המסמכים לא נמצאה.',
	14033: 'המסמך כבר אושר.',
	14034: 'תהליך ביטול קישור מתבצע עבור תיק ביקורת אחר בסביבת העבודה או שהקישור של מסמך זה כבר בוטל. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',
	14035: 'המסמך אינו משותף עם תיק הביקורת הנתון (the given engagement).',
	14036: 'על מספר הגרסה להיות גדול מאפס.',
	14037: 'לא ניתן להשלים את הפעולה כעת. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk)',
	14038: 'השגת סיבות שינוי המסמך נכשלה.',
	14039: 'השגת הסיבה לשינוי המסמך לפי מספר זיהוי ID נכשלה.',
	14040: 'עדכון הסיבה לשינוי נכשל.',
	14041: 'סיבת שינוי לא חוקית.',
	14042: 'עידכון הסיבה לשינוי נכשלה.',
	14043: 'יצירת הסיבה לשינוי נכשלה.',
	14044: 'מחיקת הסיבה לשינוי נכשלה.',
	14045: 'מספר זיהוי סיבה לשינוי לא חוקי.',
	14046: 'המסמך אינו זמין. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',
	14047: 'למסמך כבר הוקצתה סיבה לשינוי.',
	14048: 'למסמך יש קונפליקט, אנא פתור לפני שתמשיך.',
	14049: 'נתונים לא חוקיים כדי לבדוק האם המסמך נוסף לצוות או למשתמש.',
	14050: 'שגיאה בעת הסרת מסמך הפניה.',
	14052: 'טקסט החיפוש חורג מהאורך המירבי המותר (500 תווים).',
	14053: 'לא ניתן לבצע את הבקשה כיוון שהיא לא בתוקף.',
	14054: 'פעולת תיקון לא חוקית.',
	14055: 'בקשה לפתור היסטוריית מסמך נכשלה',
	14056: 'אירעה שגיאה בעת ניסיון לאחזר את ההודעה.',
	14057: 'מסמך עם שם זהה כבר קיים בתיק הביקורת.',
	14058: 'נמצאה יותר מגרסת מסמך אחת עם אותו מספר. אנא צור קשר עם התמיכה הטכנית לקבלת סיוע נוסף.',
	14059: 'גרסת המסמך שנבחרה לא נמצאה. אנא רענן את הדף ונסה שוב. אם הבעיה נמשכת, אנא צור קשר עם התמיכה הטכנית לקבלת סיוע נוסף.',
	14060: 'לא ניתן היה להשלים את הפעולה בזמן. בבקשה נסה שוב מאוחר יותר. אם הבעיה נמשכת, אנא צור קשר עם התמיכה הטכנית לקבלת סיוע נוסף.',
	14061: 'המסמך לא משותף, לא ניתן לבטל את הקישור של המסמך. אם הבעיה נמשכת, אנא צור קשר עם התמיכה הטכנית לקבלת סיוע נוסף.',
	14062: 'מזהה סוג סודיות המסמך אינו חוקי.',
	14063: 'לא ניתן לשנות את סוג הסודיות של סוג מסמך זה.',
	14064: 'למשתמש אין הרשאה.',
	14065: 'תיאור מותאם אישית לא חוקי.',
	14066: 'עדכון הסיומת נכשל.',
	14067: 'סיומת לא חוקית.',
	14068: 'קבלת סיומת המסמכים נכשלה.',
	14069: 'הקישור של המסמך הזה כבר בוטל על ידי חבר צוות אחר. רענן את הדף ופנה לתמיכה הטכנית אם השגיאה נמשכת',
	14070: 'Invalid file type.',
	14071: 'The selected document version is no longer available. Please close and reopen this window to see the latest set of historical versions for the document.',
	14072: 'מזהה מסמך המקור אינו יכול להיות Null או ריק.',
	14073: 'מזהי טופס ה- Canvas לא יכולים להיות חסרים או ריקים.',
	14074: 'מזהי טופס ה- Canvas לא יכולים להיות כפולים.',
	14075: 'שיוך המסמך לטפסי ה- Canvas נכשל.',
	14076: 'טפסי Canvas המשויכים למסמך מקור לא נמצאו.',
	14077: 'מסמך המקור לא נמצא. הקריאה נכשלה.',
	14078: 'המסמך הנוכחי כבר משויך לטפסי Canvas קיימים.',
	14079: 'This document has been deleted and therefore it cannot be opened.',
	14080: 'The source approval user id is invalid.',
	14081: 'The source approval user id should valid GUID and must not be empty GUID.',
	14082: 'The modify user id is invalid.',
	14083: 'The modify user id should be a valid GUID and must not be empty GUID.',
	14084: 'File name cannot include: */:<>\\?|""',
	14085: 'The document name exceeded maximum length allowed.',
	14086: 'DocService failed while updating document details.',
	14087: 'The input is not valid.',
	14088: 'A input has duplicate document names.',
	14089: 'The bookmark observation is not valid.',
	14090: 'Request status has changed. Please refresh the page and try again if required. If the issue persists, contact the help desk.',
	14091: 'This request has been deleted. Refresh the page to view updated data. If the issue persists, contact the Help Desk.',
	14092: 'Document not eligible for update. Refresh the page and try again.  If the issue persists, contact the Help Desk.',

	/*SEM*/
	15001: 'הבקשה להציג סיכום SEM למזהה ID לסעיף נכשלה, הואיל ואינה חוקית.',
	15002: 'Account Id שגוי.',
	15003: 'Engagement ID שגוי.',
	15004: 'סוג סיכום לא חוקי.',
	15005: 'לא ניתן למצוא סעיף קשור. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית (Help Desk) אם הבעיה נמשכת.',

	/*Timephase*/
	16001: 'השגת ה- Time Phase נכשלה.',
	16002: 'תיק הביקורת חייב להיות גדול מאפס.',
	16003: 'ה- TIme Phase חייב להיות גדול מאפס.',
	16004: 'ערך מספר זיהוי משימה לא חוקי.',
	16005: 'ערך ה- Time Phase לא חוקי.',

	/*Validations*/
	17001: 'חסר מזהה (ID) בניית שלבים או מזהה (ID) של סוג מסמך.',
	17003: 'The document could not be found. Refresh the page and try again. If the issue persists, contact Help Desk.',

	/*TaskGroupSection*/
	18001: 'בקשה לקבלת מקטע קבוצת המשימות נכשלה.',

	/*Assignments*/
	19001: 'בקשה ליצירת הקצאה נכשלה.',
	19002: 'בקשה לקבלת הקצאה נכשלה.',

	/*Client Request*/
	21001: 'הבקשה לקישור לבקשת הלקוח נכשלה.',

	/*Related Components*/
	22001: 'בקשה לקבלת ישויות מוחזקות (components) קשורות נכשלה.',

	/*Component Ceation*/
	22022: 'שם הישות המוחזקת כבר קיים בהתקשרות  זו',
	22024: 'הישות המוחזקת שאליה אתה מנסה לשלוח הנחיות אינה זמינה עוד או שכבר נמחקה.',
	22027: 'נמצאה הנחייה קבוצתית ללא תאריך יעד.',
	22028: 'טרם פורסמה הנחיית היקף עבור הישות המוחזקת.',
	22029: 'לא פורסמה הנחיה חדשה עבור הישות המוחזקת לשליחה.',

	22040: 'לא ניתן היה לשלוח הנחיות. בדוק שסוג ההתקשרות נכון.',
	22048: 'הישות המוחזקת (component) שאתה מנסה לעדכן מועתק מהתקשרות אחרת בעת העתקת סביבת עבודה.',

	/*Send Instruction*/
	22049: 'Group instructions cannot be sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk.',

	/*User Presence*/
	23001: 'נוכחות משתמש מחובר לא נמצאה.',
	23002: 'בקשה לקבלת נוכחות המשתמש נכשלה.',
	23003: 'אימות מסמך נוכחות המשתמש נכשל.',
	23004: 'בקשה למחיקת נוכחות המשתמש נכשלה.',
	23005: 'המסמך לא נפתח על ידי משתמשים אחרים. רענן את הדף ונסה שוב. פנה  לתמיכה הטכנית (Help Desk) אם הבעיה נמשכת.',

	/* Forms */
	24001: 'בקשה לקבלת טופס נכשלה.',
	24002: 'Engagement ID שגוי.',
	24003: 'ה- Document ID אינו יכול להיות לא בתוקף או ריק.',
	24005: 'כותרת הטופס לא נמצאה.',
	24004: 'המסמך לא נמצא. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	24006: 'Section is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24007: 'פרמטרי בקשה לא חוקיים.',
	24008: 'מספר זיהוי הכותרת אינו יכול להיות לא חוקי (null) או ריק (empty).',
	24009: 'מספר זיהוי סעיף לא צריך להיות לא חוקי (null) או ריק (empty).',
	24010: 'פעולת עדכון תגובת גוף הטופס נכשלה.',
	24011: 'בקשה לא חוקית לעדכון תגובת גוף הטופס.',
	24012: 'Body is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24013: 'מספר זיהוי בגוף הטופס (Form Body Option) לא חוקי.',
	24014: 'מספר זיהוי סוג גוף הטופס לא חוקי.',
	24015: 'גוף בקשה לא חוקי עבור מספר זיהוי סוג גוף נתון.',
	24016: 'טקסט חופשי לא חוקי עבור מספר זיהוי סוג גוף נתון.',
	24017: 'תווים מרביים, לרבות תגי עיצוב טקסט עשיר. יש לצמצם את האורך או להסיר עיצוב מיותר ולנסות שוב.',
	24018: 'הנתון אינו מותר עבור מספר זיהוי למענה בגוף הטקסט.',
	24019: 'מספר זיהוי גוף אינו יכול להיות לא חוקי (null) או ריק (empty).',
	24020: 'גוף בקשה לא חוקי.',
	24021: 'הגוף נמחק.',
	24022: 'שם המדינה לא יכול להיות Null או ריק.',
	24023: 'השפה לא יכולה להיות null או ריקה.',
	24024: 'מחלקת השירות לא יכול להיות null או ריק.',
	24025: 'GamLayers לא יכול להיות null או ריק.',
	24026: 'הקריאה ליצירת הכותרת נכשלה.',
	24027: 'בקשה לא חוקית ליצירת כותרת.',
	24028: 'הקריאה ליצירת יחידה משוכפלת נכשלה.',
	24029: 'סוג הטופס לא חוקי.',
	24030: 'המקטע נמחק. רענן את הדף ונסה שוב. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.',
	24031: 'הגוף אינו מותאם אישית.',
	24032: 'בקשת יצירת כותרת אינה חוקית.',
	24033: 'מספר זיהוי לתיעוד שנמסר על ידי הישות אינו חוקי.',
	24034: 'מספר הזיהוי לתיעוד שנמסר על ידי הישות אינו חוקי.',
	24035: 'אירעה שגיאה ביצירת מסמך קשור.',
	24036: 'אירעה שגיאה במחיקת מסמך קשור.',
	24037: 'RelatedDocumentId לא יכול להיות ריק או Null.',
	24038: 'המסמך אינו תקף או אינו קיים.',
	24039: 'מסמך קשור אינו חוקי או אינו קיים.',
	24040: 'יצירת גוף מותאם אישית נכשלה.',
	24041: 'בקשת יצירת התוכן אינה חוקית.',
	24042: 'בקשת יצירת הפיסקה אינה חוקית.',
	24043: 'הבקשה ליצירת פסקה באמצעות מזהה ID נכשלה.',
	24044: 'יצירת הפיסקה נכשלה.',
	24045: 'העמוד הנוכחי אינו חוקי.',
	24046: 'גודל עמוד לא חוקי.',
	24047: 'מזהה אובייקט לא חוקי הקשור למסמך.',
	24048: 'האובייקט כבר מקושר לטופס הקאנבס.',
	24049: 'האובייקט לא נמצא.',
	24050: 'מזהה ה- UId (entity Uid) שהועבר אינו חוקי.',
	24051: 'אם סופק מזהה EntityId, יש לספק גם את המזהה EntityUid , ולהפך.',
	24052: 'יצירת צילום מסך של טופס קאנבס נכשלה.',
	24053: 'הבקשה להציג את הכותרת העליונה באמצעות מזהה ID נכשלה.',
	24054: 'הבקשה להציג את התוכן באמצעות מזהה ID נכשלה.',
	24055: 'אירעה שגיאה ביצירת פרופיל הטופס.',
	24056: 'מסמך FormProfile כבר קיים.',
	24057: 'אימות FormProfile של המסמך נכשל.',
	24058: 'מסמך FormProfile אינו קיים.',
	24059: 'המקטע אינו מותאם אישית.',
	24060: 'אימות FormProfile של המסמך נכשל. אם נבחר PCAOB-IA הרי שיש לבחור גם PCAOB-FS.',
	24061: 'אימות FormProfile של המסמך נכשל. אם לא נבחר PCAOB-FS הרי שאין לבחור גם PCAOB-IA.',
	24062: "אימות FormProfile של המסמך נכשל. אם נבחר'NCE -לא מורכב' אז אין לבחור'PCAOB - FS' /'PCAOB - IA'.",
	24063: 'מזהה מדינה אינו חוקי.',
	24064: 'מזהה השפה אינו חוקי.',
	24065: 'כותרת עליונה אינה מותאמת אישית.',
	24066: 'יצירת אובייקט הקשור למקטע בטופס נכשלה.',
	24067: 'האובייקט לא נמצא.',
	24068: 'Wcgw לא קשור לתת תהליך SCOT',
	24069: 'פרמטר {0} אינו חוקי.',
	24070: 'ה- UId של חברת האם שהועבר אינו תקף.',
	24071: 'אובייקט הקשור למקטע בטופס נכשל.',
	24072: 'המקטע אינו זמין. אנא רענן את הדף ונסה שנית. אם הבעיה נמשכת, פנה אל התמיכה הטכנית.',
	24073: 'לא ניתן לאחזר את צילום המסך. אנא רענן את הדף ונסה שנית. אם הבעיה נמשכת, פנה אל התמיכה הטכנית.',
	24074: 'צילומי מסך אינם זמינים עבור המסמך שנבחר. רענן את הדף ונסה שוב. אם הבעיה נמשכת פנה לתמיכה הטכנית.',
	24075: 'מזהה צילום מסך לא חוקי.',
	24076: 'מזהה הישות לא צריך להיות ריק או null',
	24077: 'מזהה הפריט הקשור למקטע הטופס שהועבר אינו חוקי.',
	24078: 'מזהה הפריט הקשור למקטע הטופס אינו יכול להיות Null או ריק.',
	24079: 'מזהה חברת האם שסופק אינו חוקי.',
	24080: 'פריט הקשור למקטע הטופס: לא נמצא רישום של חברת האם.',
	24081: 'האובייקט לא נמצא.',
	24082: 'האובייקט לא נמצא.',
	24083: 'אירעה שגיאה בעדכון FormProfile.',
	24084: 'טופס FormProfile אינו קיים.',
	24085: 'מזהה השפה צריך להיות גדול מ- 0.',
	24086: 'מזהה מדינה צריך להיות גדול מ- 0.',
	24087: 'אין אפשרות לעדכן מסמכים שיובאו ממאגר הידע. עדכן את פרופיל תיק הביקורת בכדי לשנות את פרופיל הטפסים.',
	24088: 'אין די תפקידים לעריכת תוכן. עבוד עם מנהל תיק הביקורת כדי לקבל הרשאות מתאימות.',
	24089: 'שם הכותרת המותאמת אישית חרג מהאורך המרבי (500 תווים). אנא התאם את השם ונסה שוב.',
	24090: 'שם מקטע מותאם אישית חרג מהאורך המרבי (500 תווים). אנא התאם את השם ונסה שוב.',
	24091: 'התווית המותאמת אישית של הסעיף חרגה מהאורך המרבי (100 תווים). אנא התאם את השם ונסה שוב.',
	24092: 'שם גוף מותאם אישית חרג מהאורך המרבי (500 תווים). אנא התאם את השם ונסה שוב.',
	24093: 'שם מקטע מותאם אישית אינו יכול להיות Null או ריק.',
	24094: 'שם התוכן אינו יכול להיות Null או ריק.',
	24096: 'שם כותרת עליונה מותאמת אישית אינו יכול להיות Null או ריק.',
	24097: 'אין אפשרות להשלים את ההחלפה כעת.',
	24098: 'מזהה סוג מסמך מקור או יעד אינו חוקי.',
	24099: 'מזהה מסמך מקור ומסמך יעד במאגרי הידע אינו זהה.',
	24100: 'מזהה מסמך המקור אינו יכול להיות Null או ריק.',
	24101: 'מזהה מסמך היעד אינו יכול להיות Null או ריק.',
	24103: 'הכותרת הראשית נמחקה. רענן את הדף ונסה שנית. אם הבעיה נמשכת, פנה אל התמיכה הטכנית.',
	24104: 'אין אפשרות לערוך כותרת עליונה.',
	24105: 'אין אפשרות לערוך את התוכן.',
	24106: 'פעולת העדכון אינה חוקית.',
	24107: 'אימות טופס FormProfile נכשל. אם התיק סומן לפי PCAOB-FS הרי שבהכרח הוא של ישות מורכבת complex.',
	24108: 'אימות FormProfile של המסמך נכשל. אם נבחר PCAOB-IA הרי שיש לבחור גם PCAOB-FS.',
	24110: 'עדכון תוכן מתבצע כעת. עדכן באופן ידני את התוכן של טופס זה בדף עדכון התוכן של ה Canvas form לאחר השלמת עדכון התוכן הנוכחי.',
	24111: 'ערכי המיפוי וביטול הקשור אינם יכולים להיות זהים.',
	24112: 'אין לשתף את מסמך המקור.',
	24114: 'הוספת הראיות נכשלה. אנא רענן את הדף ונסה שנית.',
	24115: 'הסרת הראיות נכשלה. אנא רענן את הדף ונסה שנית.',
	24116: 'שליחת הפרופיל נכשלהץ רענן את הדף ונסה שנית.',
	24117: 'בטופס קיימות תגובות שטרם הושלמו. רענן את הדף ונסה שנית.',
	24118: 'מזהה מסמך ומזהה מסמך קשור לא יכולים להיות זהים.',
	24119: 'עריכת האובייקטים הקשורים לתוכן נכשלה.',
	24120: 'מזהה האובייקט הקשור לתוכן לא יכול להיות ריק או null.',
	24121: 'האובייקט לא נמצא.',
	24122: 'חסר טוקן במקביל',
	24124: 'נמצא(ו) מסמכ(י) יעד (target document(s)) לא חוקיים.',
	24125: 'לא נמצאו טפסי Canvas יעד.',
	24126: 'הבקשה לא צריכה להיות ריקה או null.',
	24127: 'ייבוא ​​הנתונים מה-EY Helix לא הצליח. ייבא את הנתונים שוב מהגדרות ה- EY Helix ונסה שוב. אם הבעיה נמשכת פנה לתמיכה הטכנית.',
	24155: 'שליחת פרופיל אינה מותרת לתיק ביקורת משוחזר.',
	24164: 'אין לך הרשאות מתאימות לבצע שינויים בתבנית.',
	24166: 'שמירת פרופיל אינה מותרת. הטופס אינו זמין עבור פרופיל זה.',
	24167: 'לא ניתן לעדכן את סוג הגוף',
	24168: 'שינוי מזהה משתמש לא יכול להיות ריק או null',
	24169: 'שינוי מזהה משתמש יכול לקבל ערך רק כאשר linkedResponseUpdate הוא לא נכון',
	24170: 'מזהה משימה לא חוקי בבקשה',
	24171: 'זיהוי גוף כפול בתהליך',
	24172: 'הבקשה להשוואת המקטע נכשלה',
	24173: 'מזהי מסמכים לא יכולים להיות ריקים. כמו כן, זה לא יכול לחרוג מ-50 מסמכים נפרדים בכל פעם',
	24174: 'מסמך ה- Route צריך להיות חלק ממסמכי גוף הבקשה',
	24175: 'מזהה ראיה חכמה לישות (smart evidence entity) לא חוקי בגוף הבקשה',
	24176: 'למסמכים לגוף צריך להיות את אותו מזהה טופס ידע (knowledge form id)',
	24177: 'מזהה מקטע לא חוקי',
	24178: 'שילוב לא חוקי של DocumentID ו-DocumentIDList',
	24179: 'רשימת מזהי המסמכים לא יכולה להיות גדולה מ-50',
	24180: 'לא ניתן היה לעדכן את ספי המהותיות',
	24181: 'מזהה גוף הטופס לא חוקי',
	24182: 'מזהה טופס ידע לא חוקי או מזהה גוף טופס ידע',
	24183: 'אירעה שגיאה בעת שיוך המסמך לטופס זה. אנא רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	24184: 'המשתמש אינו פעיל עבור התקשרות זו של EY Canvas. הזמן את המשתמש דרך ניהול צוות (manage team) ולאחר מכן נסה שוב. אם הבעיה נמשכת פנה לתמיכה הטכנית',
	24188: 'One or more records are no longer available. Please refresh the page',

	/*Document Change Types*/
	25001: 'בקשה לקבלת סוגי שינויים במסמכים נכשלה.',
	25002: 'Engagement ID שגוי.',
	25003: 'בקשה לא חוקית.',

	/* Manage team */
	26001: 'בקשת לקבלת כל קבוצות משתמשים ללקוח (ClientUserGroups) נכשלה.',
	26002: 'בקשה ליצירת קבוצת לקוח חדשה / דוא"ל דומיין חדש נכשלה.',
	26003: 'שם הקבוצה אינו יכול להיות לא חוקי (null).',
	26004: 'דוא"ל הדומיין אינו יכול להיות לא חוקי (null).',
	26005: '{0} תג (label) דומיין לדוא"ל לא יעלה על 263 תווים.',
	26006: '{0} דומיין לדוא"ל לא יעלה על 263 תווים.',
	26007: '{0} החלק הראשון של הדומיין לדוא"ל יכול להיות * או אלפאבתי, תווים מיוחדים אחרים אינם מורשים.',
	26008: '{0} אם החלק הראשון הוא קלף כללי, עליו להיות בעל שני חלקים או יותר הבאים.',
	26009: '{0} החלק הראשון יכול להיות * או אלפאנומרי (שמכיל אותיות וספרות), חלקי דומיין אחרים לא מאפשרים תווים מיוחדים.',
	26010: 'על דוא"ל דומיין להיות ייחודי.',
	26011: 'מספר זיהוי ID לקבוצת הגישה של הלקוח שסופק אינו חוקי.',
	26012: 'אירעה שגיאה בעת מחיקת הקבוצה למתן הרשאות הגישה. ודא כי אין בקשות / משימות חיצוניות שהוקצו לקבוצה זו או לחברי הקבוצה ונסה שוב.',
	26013: 'קבוצה למתן הרשאות גישה כבר נמחקה, אנא רענן את הדף לקבלת הנתונים העדכניים ביותר.',
	26014: 'נדרש לפחות אימייל דומיין אחד',
	26015: 'פורטל הלקוחות של EY Canvas אינו זמין כעת ונדרש לעדכן את פרטי חברי הצוות הקיימים. נסה שוב ואם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',
	26016: 'אירעה שגיאה במהלך פעולת המחיקה',
	26017: 'פעולת קבלת נתונים נכשלה.',
	26018: 'בעיה חוזרת, מתן הרשאות לקבוצת גישה כבר אינה פעילה',
	26019: 'פעולת השמירה נכשלה',
	26020: 'לא ניתן להסיר אימייל של דומיינים כאשר הוקצו להם משתמשים פעילים. השינויים לא נשמרו.',

	/* TimePhaseTypes - Milestones */
	27001: 'השגת פרטי אבני דרך (milestone) נכשלה.',

	/*Client Request Counts*/
	28001: 'בקשה לקבלת מידע על בקשות הלקוח לא הביאה תוצאות.',

	/*Content updates Error messages*/
	29001: "'הבקשה באמצעות מזהה ID' לא נמצא.",
	29002: 'סוג האקטון (Acton) לא נמצא.',
	29003: 'מספר(י) זיהוי תוכן לא נמצאו',
	29004: 'בקשה לעדכון תוכן ממשק ה- API נכשלה.',
	29005: 'עדכון התוכן מתבצע. נסה שוב מאוחר יותר ואם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',
	29006: 'פרמטרי בקשה לא חוקיים.',

	/*IT Process*/
	30001: 'קבלת כל תהליך ה- IT נכשלה.',
	30002: 'קבל את כל ה- ITProcess - מספר זיהוי ID תיק ביקורת לא חוקי.',
	30003: 'שם תהליך ה- IT לא יכול להיות ריק.',
	30004: 'שם תהליך ה IT לא יכול להיות יותר מ 500 תווים.',
	30005: 'לא היתה אפשרות להשלים פעולה זו. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	30006: 'הבקשה לאיתור תהליך ה IT באמצעות מזהה נכשל.',
	30007: 'בקשה לא חוקית.',
	30008: 'תהליך ה-IT אינו זמין עוד. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	30009: 'המסמך לא נמצא.',
	30010: 'לא ניתן להשלים את המחיקה. רענן את הדף ונסה שוב. אם הבעיה נמשכת פנה לתמיכה הטכנית.',
	30012: 'לא ניתן להשלים את המחיקה. רענן את הדף ונסה שוב. אם הבעיה נמשכת פנה לתמיכה הטכנית.',
	30017: 'לא היתה אפשרות להשלים פעולה זו. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	30018: 'לא היתה אפשרות להשלים פעולה זו. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	30019: 'לא היתה אפשרות להשלים פעולה זו. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',

	/*Checklist*/
	31001: 'בקשה לקבלת כל רשימות הבדיקה (CheckList) נכשלה.',
	31002: 'הבקשה להצבת שאלונים (CheckList) נכשלה.',
	31003: 'פרמטר רשימות הבדיקה (CheckList) לא חוקי.',
	31004: 'שגיאת תיק ביקורת לא חוקית.',
	31005: 'שגיאת פרמטרים של בקשה לא חוקית.',
	31006: 'שגיאת פרמטר בקשה מסומנת (IsChecked) לא חוקית.',

	/*Archive*/
	32001: 'מספר זיהוי סטטוס תיק הביקורת אינו חוקי.',
	32002: 'הארכיב נכשל',
	32003: 'בקשת ה- ARC V1 נכשלת, רענן את הדף, פתור את האימותים ונסה שוב את תהליך הארכיב. פנה לתמיכה הטכנית (Help Desk) אם השגיאה נמשכת.',
	32004: 'עדכון סטטוס תיק הביקורת ב- LDC נכשל.',
	32005: 'Invalidate Engagement Cache Error.',
	32006: 'עדכון תוכן בתהליך. אינך יכול לארכב תיק ביקורת זה עד לסיום עדכון התוכן. נסה שוב מאוחר יותר וצור קשר עם התמיכה הטכנית (Help Desk) אם השגיאה נמשכת.',
	32007: 'ArcGUID הוא null או ריק.',
	32008: 'FileGuid הוא null או ריק.',
	32009: 'FileStoreHostTcp הוא null או ריק.',
	32200: 'ישנם אימותים (validations) שלא נפתרו בתיק ביקורת זה. רענן את הדף, פתור את האימותים ונסה שוב לארכב. צור קשר עם התמיכה הטכנית (Help Desk) אם השגיאה נמשכת.',
	32300: 'ישנם אימותים (validations) שלא נפתרו בתיק ביקורת זה. רענן את הדף, פתור את האימותים ונסה שוב לארכב. צור קשר עם התמיכה הטכנית (Help Desk) אם השגיאה נמשכת.',

	/*RBAC*/
	33001: 'מספר זיהוי ID תיק ביקורת לא נמצא.',
	33002: 'מספר זיהוי ID משתמש לא נמצא.',

	/*Helix Linked Projects*/
	34001: 'לא ניתן לבצע את הבקשה כיוון שהיא לא בתוקף.',
	34002: 'מספר זיהוי תיק הביקורת לא יכול להיות null או ריק.',
	34003: 'התוכן לא יכול להיות לא בתוקף או ריק בתא.',
	34004: 'מספר זיהוי הפרויקט לא יכול להיות null או ריק.',
	34005: 'שם הפרויקט לא יכול להיות null או ריק.',
	34006: 'הפרויקט כבר קושר, רענן את הדף ונסה שוב.',
	34007: 'בקשה לקבלת כל פרוייקטי Helix נכשלה.',
	34008: 'ה- Engagement ID אמור להיות גדול מ- 0.',
	34010: 'לא ניתן היה להשלים את השמירה. רענן את הדף ונסה שוב. אם הבעיה ממשיכה פנה לתמיכה הטכנית.',
	34009: 'מזהה הפרויקט השתנה. רענן את הדף ונסה שוב.',
	34011: 'סוג המטבע לא יכול להיות ריק בקריאה.',
	34012: 'קוד המטבע אינו יכול להיות ריק בקריאה.',
	34013: 'היחידה העסקית לא יכולה להיות לא בתוקף או ריקה בקריאה.',
	34014: 'פרויקט ה- EY Helix המקושר השתנה לא ניתן לעדכן את ההגדרות.',
	34017: 'לא ניתן היה להתחבר ל- EY Helix. רענן את הדף ונסה שוב. אם הבעיה ממשיכה, אנא פנה לתמיכה הטכנית.',
	34018: 'לא ניתן היה להשלים את הפעולה. אנא רענן את הדף ונסה שנית. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	34019: 'הייבוא ​​הושלם אך הנתונים לא חוקיים. רענן את הדף ולחץ על ייבוא ​​כדי לנסות שוב.',
	34027: 'פרויקט Helix נמצא בתהליך.',
	34036: 'לא ניתן היה להתחבר ל- EY Helix. רענן את הדף ונסה שוב. אם הבעיה ממשיכה, אנא פנה לתמיכה הטכנית.',
	34039: 'פרויקט EY Helix זה אינו עוד הפרויקט העיקרי להתקשרות שלך. אנא רענן את הדף ונסה שוב.',
	34040: 'עליך להיות בעל הרשאת <b>EY Helix Import</b> כדי לבצע פעולה זו. דבר עם מנהל התקשרות כדי לקבל גישה.',

	/* PAANS */
	35001: 'מדיניות ההרשאות (Authoring), האישורים וההודעות של EY אינה זמינה בשלב זה. אין באפשרותנו לאשר אם כל המדיניות הרלוונטית הושלמה. אם לא בדקת את המדיניות הרלוונטית, רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית (Help Desk).',

	/*Engagement Comments*/
	38001: 'שירות יצירת הערות להתקשרות נכשלה',
	38002: "שירות'קבל את כל הערות ההתקשרות/ נכשלה",
	38003: 'לא ניתן להשלים פעולה זו. רענן את הדף ונסה שוב. אם השגיאה נמשכת, פנה אל התמיכה הטכנית (Help Desk)',
	38004: 'לא ניתן להשלים פעולה זו. רענן את הדף ונסה שוב. אם השגיאה נמשכת, פנה אל התמיכה הטכנית (Help Desk)',
	38005: 'לא ניתן להשלים פעולה זו. רענן את הדף ונסה שוב. אם השגיאה נמשכת, פנה אל התמיכה הטכנית (Help Desk)',
	38006: 'לא ניתן להשלים פעולה זו. רענן את הדף ונסה שוב. אם השגיאה נמשכת, פנה אל התמיכה הטכנית (Help Desk)',
	38007: 'לא ניתן להשלים פעולה זו. רענן את הדף ונסה שוב. אם השגיאה נמשכת, פנה אל התמיכה הטכנית (Help Desk)',
	38008: 'לא ניתן להשלים פעולה זו. רענן את הדף ונסה שוב. אם השגיאה נמשכת, פנה אל התמיכה הטכנית (Help Desk)',
	38009: 'לא ניתן להשלים פעולה זו. רענן את הדף ונסה שוב. אם השגיאה נמשכת, פנה אל התמיכה הטכנית (Help Desk)',
	38010: 'הגוף לא צריך להיות ריק',
	38011: 'טקסט ההערה לא צריך להיות null או ריק',
	38012: 'ישות לא קיימת בהתקשרות נתונה',
	38013: 'זיהוי סטטוס הערת התקשרות צריך להיות גדול מ-0',
	38014: 'זיהוי הערת התקשרות של חברת האם צריך להיות גדול מ-0',
	38015: 'לא היה גוף של טופס קנבס התואם לקריטריונים שצוינו',
	38016: 'ההערה שאתה משיב לה נמחקה על ידי חבר צוות אחר. רענן את הדף ונסה שוב',
	38017: 'הערת התקשרות של חברת האם שסופקה נמחקה',
	38018: 'הערת התקשרות של חברת האם שסופקה היא בעצמה התשובה',
	38019: 'טקסט ההערה שסופק לא צריך להיות קצר מ-1 ולא ארוך יותר מ-4000 תווים',
	38020: 'ה-UI של הישות שסופק אינו חוקי',
	38021: 'ה-UID של חברת האם שסופק אינו חוקי',
	38022: 'שיחת מחיקת הערות התקשרות נכשלה',
	38023: 'מזהה ההערה לא צריך להיות ריק',
	38024: 'הפעולה צריכה להיות פעולה חוקית',
	38025: 'אתה מנסה למחוק הערה שכבר אינה קיימת. רענן את הדף ונסה שוב.',
	38026: 'אתה לא האחראי על ההערה',
	38027: 'לא נמצאה תגובה לעדכון',
	38028: 'עדכונים על הערות שנמחקו אסורים',
	38029: 'רק המחבר יכול לשנות את טקסט התגובה',
	38030: 'מזהה הערת התקשרות אינו חוקי',
	38031: 'מזהה סטטוס ההערה לא צריך להיות ריק',
	38032: 'לא היה תיעוד של הקשר בין המשתמש המבוקש לבין תיק הביקורת. רענן את הדף ונסה שוב. אם השגיאה נמשכת פנה לתמיכה הטכנית',
	38033: 'פעולת העדכון אינה חוקית',
	38034: 'מזהה ההערה המבוקש כבר נמצא בשימוש על ידי תגובה אחרת',
	38035: 'מזהה ההערה שסופק אינו חוקי',
	38036: 'מזהה המסמך לא צריך להיות ריק או ריק',
	38037: 'המזהה של המשתמש שאליו תוקצה ההערה לא אמור להיות ריק',
	38038: 'אין להקצות מחדש את ההערה בזמן פתיחתה או סגירתה',
	38039: 'ההערה שממנה אתה מנסה למחוק תגובה כבר נמחקה. רענן את הדף ונסה שוב',
	38040: 'לתשובה לא אמור להיות תאריך יעד',
	38041: 'לתשובה לא אמורה להיות עדיפות',
	38042: 'להערה צריך להיות תאריך יעד',
	38043: 'להערה צריכה להיות עדיפות',
	38044: 'אתה מנסה לערוך תשובה להערה שאינה קיימת יותר. רענן את הדף ונסה שוב',
	38045: 'אסור לערוך הן את מזהה סטטוס תיק הביקורת (Engagement Status)  והן את התוכן שלו שהוקצה למשתמש או לעדיפות',
	38046: 'שיחת Update עדכון הערות התקשרות נכשלה',
	38047: 'אתה יכול להשיב רק להערות פתוחות',
	39004: 'This tag group is no longer available. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*Risk-Estimate*/
	4064: 'סיכון זה אינו זמין יותר. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	4065: 'לא ניתן לערוך את הקישור לסיכון. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	4066: 'אומדן זה אינו זמין עוד. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',

	/*IT App/SO*/
	81001: 'השגת כל יישומי ה- IT נכשלה.',
	81002: 'קבל את כל יישומי ה-IT - מזהה התקשרות לא חוקי.',
	81003: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81004: 'לא ניתן היה להשלים את הפעולה. רענן את הדף ונסה שנית. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.',
	81005: 'לא ניתן היה להשלים את הפעולה. רענן את הדף ונסה שנית. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.',
	81006: 'סוג האסטרטגיה לא חוקי.',
	81007: 'מזהה המסמך (Document ID) לא חוקי.',
	81008: 'קבלת יישום IT באמצעות מזהה נכשלה.',
	81009: 'לא ניתן היה להשלים את הפעולה. רענן את הדף ונסה שנית. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.',
	81010: 'לא ניתן היה להשלים את הפעולה. רענן את הדף ונסה שנית. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.',
	81011: 'לא ניתן היה להשלים את הפעולה. רענן את הדף ונסה שנית. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.',
	81012: 'מזהה יישום IT לא יכול להיות ריק או null.',
	81013: 'לא ניתן לערוך את הקשר (relation). רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	81014: 'הקריאה למחיקת יישום IT / תהליך IT נכשלה.',
	81015: 'מזהה יישום IT / תהליך IT לא יכול להיות ריק.',
	81016: 'מזהה יישום IT / תהליך IT לא חוקי.',
	81017: 'לא ניתן לערוך את הקשר (relation). רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	81018: 'הקריאה ליצירת יישום IT / תהליך IT נכשלה.',
	81019: 'העבר את לשכת השירות (SO) במקום ה- ITApplication.',
	81020: 'לא ניתן להשלים את המחיקה. רענן את הדף ונסה שוב. אם הבעיה נמשכת פנה לתמיכה הטכנית.',
	81039: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81041: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/* ITControl */
	84001: 'השגת בקרות IT נכשלה.',
	84002: 'השגת בקרת IT על ידי מזהה נכשלה.',
	84003: 'מזהה בקרת IT הוא ריק או null.',
	84004: 'מזהה בקרת IT לא חוקי.',
	84005: 'מזהה המסמך אינו חוקי.',
	84006: 'חסר שם בקרת IT.',
	84007: 'אורך שם בקרת IT לא חוקי.',
	84008: 'מזהה תדירות בקרת IT לא חוקי.',
	84009: 'מזהה סוג  גישת בקרת IT לא חוקי.',
	84010: 'מזהה סוג אפקטיביות תכנון (Design Effectiveness) של בקרת IT לא חוקי.',
	84011: 'ערך בדיקת בקרת IT (IT Control Testing) לא חוקי.',
	84012: 'מזהה סוג אפקטיביות תפעולית (Operating Effectiveness) של בקרת IT לא חוקי.',
	84013: 'חסרה תדירות בקרת IT.',
	84014: 'מחיקת בקרת IT נכשלה.',
	84015: 'מחרוזת חיפוש לא תכלול יותר מ-100 תווים.',
	84016: 'לא ניתן היה להשלים את הפעולה. רענן את הדף ונסה שנית. אם הבעיה נמשכת צור קשר עם התמיכה הטכנית.',
	84018: 'לא ניתן היה להשלים את הפעולה. רענן את הדף ונסה שנית. אם הבעיה נמשכת צור קשר עם התמיכה הטכנית.',
	84019: 'לא ניתן להשלים את העדכון. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לדלפק העזרה.',

	/*ITRisk*/
	86001: 'שם ITRisk אינו יכול להיות ריק.',
	86002: 'שם ITRisk אינו יכול להיות יותר מ- 500 תווים.',
	86003: 'לא היתה אפשרות להשלים פעולה זו. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	86004: 'הבקשה לקבלת ITRisk באמצעות מזהה ID נכשלה.',
	86005: 'לא ניתן היה להשלים פעולה זו. רענן את הדף ונסה שוב. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.',
	86006: 'לא ניתן להשלים את השיוך. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לדלפק העזרה.',
	86007: 'לא ניתן היה להשלים פעולה זו. רענן את הדף ונסה שוב. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.',
	86008: 'לא ניתן להשלים את השיוך. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לדלפק העזרה.',
	86009: 'לא ניתן היה להשלים פעולה זו. רענן את הדף ונסה שוב. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.',
	86010: 'מחק סיכון טכנולוגיה נכשל.',

	/*RiskFactorFormHeaders*/
	89001: 'קישור גורם הסיכון לא נמצא.',
	89002: 'המסמך לא נמצא.',
	89003: 'מזהה מסמך חסר.',
	89004: 'אורך הרציונל עולה על יותר מ- 4000 תווים.',
	89005: 'לא היתה אפשרות לקשר סיכון לגורם הסיכון שנבחר. רענן את הדף ונסה שוב. במידה והבעיה נמשכת, אנא פנה לתמיכה הטכנית.',
	89014: 'מזהה הסיכון (Risk ID) אינו חוקי',
	89020: 'לא ניתן לשמור גורם סיכון. רענן את הדף ונסה שוב. אם הבעיה ממשיכה פנה לתמיכה הטכנית',

	/*Materiality*/
	91001: 'מהותיות לא נמצאה.',
	91002: 'לא ניתן היה לשמור נתונים. רענן את הדף ונסה שוב. אם הבעיה ממשיכה, אנא פנה לתמיכה הטכנית.',
	91003: 'בקשת עדכון מהותיות אינה חוקית.',
	91004: 'חסר האם זהו הסכום השנתי שצפוי לדווח עליו.',
	91005: 'כאשר העדכון הוא סכום שנתי שצפוי לדווח עליו בזמן אמת, חובה לציין סכום תחזית בסיס.',
	91006: 'סכום תחזית הבסיס לא יכול להיות יותר מ -15 ספרות או שיהיה לו נקודה עשרונית.',
	91007: 'כאשר מעדכנים את הסכום השנתי שצפוי לדווח עליו בזמן אמת, על הרציונל של סכום תחזית הבסיס להיות ריק.',
	91008: 'הרציונל לקביעת סכום תחזית הבסיס הוא קצר מדי או ארוך מדי.',
	91009: 'מזהה מהותיות לא חוקי',
	91010: 'מזהה סוג גוף מהותיות לא חוקי',
	91011: 'הסכום הנומינלי אינו יכול לחרוג מהקצה העליון של הטווח',
	91012: 'סף המהותיות לא יכלול יותר מ-15 ספרות ו-4 עשרוניות',

	/*Group Structure - Sub Scopes */
	92013: 'לא ניתן למחוק תת-היקף (Sub scope) מכיוון שיש לפחות צוות ביקורת של ישות מוחזקת/אזורי (Region/component team) קשור אחד.',
	92016: 'שם תת-היקף (Sub-scope) כבר קיים',

	/*Helix Account Mappings */
	94001: 'לא ניתן לשמור את מיפוי הסעיף (Account mapping). רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	94004: 'לפחות סעיף אחד ב- EY Canvas נמחק ולא ניתן היה למפות אותו. רענן את הדף ונסה שוב.',
	94005: 'לא ניתן לחדש את הפרויקט. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',

	/*Group Instructions */
	98001: 'אירעה שגיאה באחזור הנחיות הקבוצה. רענן את הדף ונסה שוב. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.',
	98002: 'מזהה מקטע אחד או יותר של הנחיות מאתר הידע אינו חוקי.',
	98003: 'אירעה שגיאה ביצירת הנחיות קבוצה. רענן את הדף ונסה שוב. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.',
	98004: 'שם ההנחייה לא יכול להיות ריק.',
	98005: 'שם ההנחיה לא יכול לכלול יותר מ-500 תווים.',
	98006: 'תיאור ההנחיות לא יכול להיות ריק.',
	98007: 'תיאור ההנחיות לא יכול לכלול יותר מ-30,000 תווים.',
	98009: 'לא ניתן לשכפל את שם ההנחייה.',
	98010: 'תאריך היעד לא יכול להיות ריק.',
	98011: 'ההנחייה כבר נמחקה.',
	98012: 'ההנחייה לא נמצאה.',
	98013: 'מזהה ההנחייה חייב להיות גדול מאפס.',
	98014: 'אירעה שגיאה בשמירת הנחיות הקבוצה. רענן את הדף ונסה שוב. אם הבעיה נמשכת, צור קשר עם התמיכה הטכנית.',
	98015: 'לא ניתן למחוק הנחיות חובה לישויות בסקופ הביקורת.',
	98016: 'ההנחיה נמחקה',
	98017: 'המשימה הקבוצתית כבר נמחקה.',
	98018: 'ערוך משימה קבוצתית לאחר המחיקה.',
	98019: 'הישות לא נמצאה.',

	98020: 'לא ניתן להפיק את חבילת הערכת הסיכונים. רענן את הדף ונסה שוב. אם הבעיה נמשכת, אנא צור קשר עם התמיכה הטכנית.',

	98021: 'שם המסמך לא יכול להיות ריק.',
	98022: 'שם המסמך לא יכול להכיל יותר מ-115 תווים.',
	98023: 'שם המסמך לא יכול לכלול תווי XML לא חוקיים.',
	98024: 'תהליך יצירת החבילה בעיצומו, עשויות להימשך עד עשר דקות עד להשלמתו',
	98025: 'לחלק מההנחיות אין ישות/ישויות קשורות. הקצה ישויות מוחזקות להנחיות ולאחר מכן צור שוב תקשורת הערכת סיכונים קבוצתית.',
	98026: 'לישות(יות) מוחזקת(ות) מסוימת(ות) אין חשבון(נות) קשור(ים). קשר חשבונות לישויות מוחזקות ולאחר מכן צור שוב תקשורת הערכת סיכונים קבוצתית.',
	98027: 'לחלק מהסעיף(ים) אין מסמכי חשבון. צור מסמך חשבון ולאחר מכן צור שוב הודעות הערכת סיכונים קבוצתיות.',
	98028: 'טופס קנבס זה אינו משויך למשימה חוקית.',
	98029: 'ישות מוחזקת אחת או יותר הן לצרכי הפניה (refrences) בלבד.',
	98030: 'ישות מוחזקת אחת או יותר אינן מההתקשרות של צוות הביקורת הראשי (PT) הזה.',
	98031: 'היקפים אחד או יותר אינם מההתקשרות של צוות הביקורת הראשי הנ"ל.',
	98032: 'ספירת המסמכים חרגה מהמגבלה.',
	98033: 'לא ניתן למחוק היקפים נדרשים בהתאם להנחיה.',

	/*Estimate */
	115017: 'האומדן לא נמצא',

	/* Group Audit */
	106003: 'שְׁגִיאָה. אנא רענן ונסה שוב.',

	/* TasksOverview */
	117001: 'הבקשה להשגת כל TaskOverview נכשלה.',
	117002: 'הבקשה לקבלת כל TaskOverview לא יכולה להיות ריקה.',
	117003: 'Engagement ID שגוי.',
	117004: 'ערך TaskCategory לא חוקי.',
	117005: 'ערך תצוגה לא חוקי.',
	117006: 'ערך DocumentCategorySearch לא חוקי.',
	117007: 'שכפול מזהי TaskCategory.',
	117008: 'שכפול מזהי TimePhase.',
	117009: 'שכפול מזהי משימות.',
	117010: 'זיהוי מסמכים כפולים.',
	117011: 'שכפל מזהי AssignedToUser.',

	/* Multientity */
	114001: 'השגת כל MultiEntity נכשלה.',
	114002: 'STentityName לא יכול להיות ריק.',
	114003: 'STentityName לא יכול להיות יותר מ-500 תווים.',
	114004: 'STLegalName לא יכול להיות ריק.',
	114005: 'STLegalName לא יכול להיות יותר מ-500 תווים.',
	114006: 'ניתן ליצור ריבוי ישויות רק עם MEST Engagements.',
	114007: 'הקריאה ליצירת Multi Entity Account נכשלה.',
	114008: 'ה-STentity שנבחר נמחק. סגור מודל זה כדי לראות את הרשימה המעודכנת.',
	114009: 'Account Id שגוי.',
	114010: 'STentityShortName לא יכול להיות יותר מ-100 תווים.',
	114011: 'בקשת הפרופיל להגשת STentity אינה חוקית.',
	114012: 'גוף הבקשה לפרופיל הגשת STentity אינו חוקי.',
	114013: 'גוף הבקשה לפרופיל הגשת STentity צריך להיות בעל מזהי STentity מובהקים.',
	114014: 'STEntityIDs עבור הגשת בקשת הפרופיל אינם חוקיים.',
	114015: 'בטופס קיימות תגובות שטרם הושלמו. רענן את הדף ונסה שנית.',
	114016: 'עדכון התוכן מושבת בגלל זה.',
	114017: 'ל-Engagement אין מסמך Multi Entity Individual Profile.',
	114018: 'STEntity IDs חסרים פרופיל בודד של ריבוי ישויות.',
	114019: 'ישות אחת או יותר שאתה ממפה כבר לא קיימת בהתקשרות. אנא רענן את הדף ונסה שוב.',
	114020: 'STentityShortName לא יכול להיות ריק.',
	114021: 'מזהה מסמך לא חוקי.',
	114022: 'מזהה התקשרות לא חוקי.',
	114023: 'רשומת STentityDocument כבר קיימת.',
	114024: 'רשומת STEntityDocument אינה קיימת.',
	114025: 'רשומת STentityDocument IsSystemAssociated.',
	114026: 'גוף בקשה לא חוקי.',
	114028: 'אין לכל אחת מהישויות מסמך פרופיל אחד.',
	114035: 'הקשר של ישות ST כבר קיים.',
	114036: 'לפחות מסמך פרופיל אחד צריך להיות חוקי בעת בקשת עדכון כל הישויות.',
	114037: 'הקשר  של ישות ST ושל תיק הלקוח כבר הוסרו.',
	114038: 'Get all MultiEntity layers failed.',
	114039: 'Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',
	114040: 'Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',

	/* Sample List */
	121101: 'מזהה רשימה לדוגמה (Sample List ID) לא חוקי.',
	121008: 'מזהה רשימה לדוגמה (Sample List ID) לא חוקי.',
	121011: 'מדגם זה אינו זמין יותר. רענן את הדף ונסה שוב. פנה ללתמיכה הטכנית אם השגיאה נמשכת.',
	121013: 'דוגמה זו אינה זמינה עוד בהתקשרות זו. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	121016: 'מדגם זה אינו זמין יותר. רענן את הדף ונסה שוב. פנה ללתמיכה הטכנית אם השגיאה נמשכת.',
	121037: 'אירעה שגיאה בעת ביצוע פעולה זו. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	121012: 'לא ניתן לעדכן את סטטוס המאפיין. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	121025: 'מסמך זה אינו זמין יותר. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	121027: 'לא ניתן לערוך את הקשר למסמך. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	121028: 'לא ניתן לערוך את הקשר למסמך. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	121014: 'לא ניתן לעדכן את סטטוס המאפיין. רענן את הדף ונסה שוב. פנה ללתמיכה הטכנית אם השגיאה נמשכת.',
	121029: 'מאפיין זה אינו זמין יותר. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
	121021: 'לא ניתן לעדכן את סטטוס המאפיין. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',

	/*Control Attributes */
	122018: 'לא ניתן היה להשלים את הפעולה. רענן את הדף ונסה שוב. אם הבעיה נמשכת פנה לתמיכה הטכנית.',
	122021: 'אירעה שגיאה בעת ביצוע פעולה זו. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',

	/*Flow chart*/
	123031: 'לא ניתן לקשר את Form Body Id ליותר מתרשים זרימה אחד.',

	1034: 'This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	1035: 'This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	/*Group Instructions */
	196033: 'הנחיות לא יכולות לכלול 0 פריטי הקצאה, עליהן להיות לפחות 1.',

	/*Information Security */
	200001: 'הפעולה שננקטה נאסרה על ידי EY Information Security. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',

	/*Tags */
	40007: 'תג זה אינו זמין יותר. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
	40029: 'לא ניתן לערוך את הקישור לתג. רענן את הדף ונסה שוב. אם הבעיה נמשכת, פנה לתמיכה הטכנית.',
};

export const roleForMember = [{
	id: 1,
	role: 'השותף האחראי על תיק הביקורת',
	roleAbbreviation: 'PIC'
},
{
	id: 2,
	role: 'השותף האחראי על תיק הביקורת',
	roleAbbreviation: 'EP'
},
{
	id: 3,
	role: 'ED',
	roleAbbreviation: 'ED'
},
{
	id: 4,
	role: 'שותף',
	roleAbbreviation: 'עיקרון'
},
{
	id: 5,
	role: "סניור מנג'ר",
	roleAbbreviation: 'Sr. Manager'
},
{
	id: 6,
	role: "מנג'ר",
	roleAbbreviation: "מנג'ר"
},
{
	id: 7,
	role: 'סניור',
	roleAbbreviation: 'סניור'
},
{
	id: 8,
	role: 'מתמחה',
	roleAbbreviation: 'מתמחה'
},
{
	id: 9,
	role: 'מתמחה',
	roleAbbreviation: 'Intrn'
},
{
	id: 10,
	role: 'שותף מלווה EQR',
	roleAbbreviation: 'EQR'
},
{
	id: 11,
	role: 'שותף אחר',
	roleAbbreviation: 'שותף אחר'
},
{
	id: 12,
	role: 'GDS - Staff',
	roleAbbreviation: 'צוות GDS'
},
{
	id: 13,
	role: 'ייעוץ (ITRA TAS הון אנושי או אחר)',
	roleAbbreviation: 'ניתוח משימות'
},
{
	id: 14,
	role: 'TAX',
	roleAbbreviation: 'TAX'
},
{
	id: 15,
	role: 'שירותי תמיכה ניהוליים',
	roleAbbreviation: 'סטטוס משימה מאת'
},
{
	id: 16,
	role: 'יועץ משפטי',
	roleAbbreviation: 'ספירת משימות'
},
{
	id: 17,
	role: 'סוקר AQR',
	roleAbbreviation: 'צפה בדוח מאת'
},
{
	id: 18,
	role: 'ML צוות הביקורת של אתר הישות המדווחת (CT)',
	roleAbbreviation: ' (אָחוּז)'
},
{
	id: 19,
	role: 'Client Supervisor',
	roleAbbreviation: 'ג. Supervisor'
},
{
	id: 20,
	role: 'צוות הלקוח',
	roleAbbreviation: "ג. Staff'"
},
{
	id: 21,
	role: 'אחראי על הביקורת הפנימית',
	roleAbbreviation: 'מפקח IA'
},
{
	id: 22,
	role: 'צוות ביקורת פנימית',
	roleAbbreviation: 'צוות IA'
},
{
	id: 23,
	role: 'רגולטור',
	roleAbbreviation: 'רגולטור'
},
{
	id: 24,
	role: 'אחר (למשל בדיקה נאותות)',
	roleAbbreviation: 'אחרים'
},
{
	id: 25,
	role: 'משרד',
	roleAbbreviation: 'משרד'
},
{
	id: 26,
	role: 'אזור',
	roleAbbreviation: 'אזור'
},
{
	id: 27,
	role: 'תעשייה',
	roleAbbreviation: 'IND'
},
{
	id: 28,
	role: 'National',
	roleAbbreviation: 'NAT'
},
{
	id: 29,
	role: 'Global',
	roleAbbreviation: 'GBL'
},
{
	id: 30,
	role: 'GDS - Senior',
	roleAbbreviation: 'סניור בצוות GDS'
},
{
	id: 31,
	role: 'GDS - Manager',
	roleAbbreviation: 'מנהל GDS'
}
];

export const accountConclusionTabs = {
	conclusions: 'מסקנות'
};

export const assertions = [{
	id: 1,
	assertionname: 'שלמות',
	assertionabbreviation: 'C',
	statementtypeid: 2,
	displayorder: 1
},
{
	id: 2,
	assertionname: 'קיום',
	assertionabbreviation: 'E',
	statementtypeid: 2,
	displayorder: 2
},
{
	id: 3,
	assertionname: 'ערך',
	assertionabbreviation: 'V',
	statementtypeid: 2,
	displayorder: 3
},
{
	id: 4,
	assertionname: 'זכויות ובעלות',
	assertionabbreviation: 'R&O',
	statementtypeid: 2,
	displayorder: 4
},
{
	id: 5,
	assertionname: 'הצגה וגילוי',
	assertionabbreviation: 'P&D',
	statementtypeid: 2,
	displayorder: 5
},
{
	id: 6,
	assertionname: 'שלמות',
	assertionabbreviation: 'C',
	statementtypeid: 1,
	displayorder: 6
},
{
	id: 7,
	assertionname: 'התרחשות',
	assertionabbreviation: 'O',
	statementtypeid: 1,
	displayorder: 7
},
{
	id: 8,
	assertionname: 'מדידה',
	assertionabbreviation: 'M',
	statementtypeid: 1,
	displayorder: 8
},
{
	id: 9,
	assertionname: 'הצגה וגילוי',
	assertionabbreviation: 'P&D',
	statementtypeid: 1,
	displayorder: 9
},
{
	id: 10,
	assertionname: 'שלמות',
	assertionabbreviation: 'C',
	statementtypeid: 3,
	displayorder: 10
},
{
	id: 11,
	assertionname: 'קיום/התרחשות',
	assertionabbreviation: 'E/O',
	statementtypeid: 3,
	displayorder: 11
},
{
	id: 12,
	assertionname: 'מדידה/ערך',
	assertionabbreviation: 'M/V',
	statementtypeid: 3,
	displayorder: 12
},
{
	id: 13,
	assertionname: 'זכויות ובעלות',
	assertionabbreviation: 'R&O',
	statementtypeid: 3,
	displayorder: 13
},
{
	id: 14,
	assertionname: 'הצגה וגילוי',
	assertionabbreviation: 'P&D',
	statementtypeid: 3,
	displayorder: 14
}
];

export const documentChangeTypesOptions = [{
	value: 1,
	label: 'שינוי שאינו אדמרניסטטיבי'
},
{
	value: 2,
	label: 'קבלת תיקונים כאשר נעשה שימוש בפונקציונליות של עקוב אחר שינויים'
},
{
	value: 3,
	label: 'הוספת הפנייה (cross referencing) נוספת לראיה שכבר קיימת'
},
{
	value: 4,
	label: "הוספת אישור מקורי שהתקבל בעבר באמצעות פקס או דוא'ל"
},
{
	value: 5,
	label: 'שינוי קוסמטי'
},
{
	value: 6,
	label: 'מילוי AP Form ובעלי תפקידים משמעותיים בביקורת'
},
{
	value: 7,
	label: 'מחיקה או השלכה של תיעוד שהוחלף'
},
{
	value: 8,
	label: 'הכנת הצהרות בכתב מההנהלה'
},
{
	value: 9,
	label: "חתימה על  רשימות הבדיקה (checklists) שהושלמו בהתייחס לתהליך הארכיב'"
},
{
	value: 10,
	label: 'מיון, איסוף והפניות (cross-referencing) מסמכים סופיים',
},
{
	value: 12,
	label: 'MEST בלבד: שינוי בראיות הקשורות לגופים עם תאריך דיווח מאוחר יותר מתאריך הדיווח ב- EY Canvas'
},
{
	value: 11,
	label: 'כשהוא מותאם לאזור הזמן המקומי, השינוי הוא בתאריך הדוח או לפניו (אמריקה בלבד)',
}
];

export const KnowledgeFormProfileAnswer = [{
	value: 1,
	label: 'מורכב',
	display: true
},
{
	value: 2,
	label: 'לא מורכב',
	display: true
},
{
	value: 3,
	label: 'ברשימה',
	display: true
},
{
	value: 4,
	label: 'לא רשומה',
	display: false
},
{
	value: 5,
	label: 'PCAOB - IA',
	display: true
},
{
	value: 6,
	label: ' Non-PCAOB-IA',
	display: false
},
{
	value: 7,
	label: 'PCAOB - FS',
	display: true
},
{
	value: 8,
	label: ' Non-PCAOB-FS',
	display: false
},
{
	value: 9,
	label: 'ביקורת קבוצתית',
	display: true
},
{
	value: 10,
	label: 'ביקורת שאינה ביקורת קבוצה',
	display: false
},
{
	value: 11,
	label: 'דיגיטלי',
	display: true
},
{
	value: 12,
	label: 'ליבה',
	display: true
}
];

export const strategyType = [{
	StrategyTypeId: 1,
	StrategyTypeName: 'בקרות'
},
{
	StrategyTypeId: 2,
	StrategyTypeName: 'ביקורת מבססת'
}
];

export const controlTypeName = {
	1: 'יישום',
	2: 'ITDM',
	3: 'מונעת ידנית',
	4: 'בקרה ידנית מגלה'
};

export const inCompleteList = [{
	value: 1,
	label: 'לא הושלם',
	title: 'לא הושלם'
}];

export const scotTypes = [{
	value: 1,
	label: 'Routine',
	title: 'Routine',
	isDisabled: false
},
{
	value: 2,
	label: 'Non Routine',
	title: 'Non Routine',
	isDisabled: false
},
{
	value: 3,
	label: 'Estimation',
	title: 'Estimation',
	isDisabled: false
}
];

export const scotTypesNew = [{
	value: 1,
	label: 'Routine',
	title: 'Routine',
	isDisabled: false
},
{
	value: 2,
	label: 'Non Routine',
	title: 'Non Routine',
	isDisabled: false
},
{
	value: 3,
	label: 'Estimation',
	title: 'Estimation',
	isDisabled: false
},
{
	value: 4,
	label: 'סגירת דוחות כספיים (FSCP)',
	title: 'סגירת דוחות כספיים (FSCP)',
	isDisabled: false
}
];

export const estimationTypes = [{
	value: 1,
	label: 'לא',
	title: 'לא',
	isDisabled: false
},
{
	value: 2,
	label: 'כן',
	title: 'כן',
	isDisabled: false
}
];

/* IT Control */
export const itApproachType = [{
	ITApproachTypeId: 1,
	ITApproachTypeName: 'בקרות'
},
{
	ITApproachTypeId: 2,
	ITApproachTypeName: 'ביקורת מבססת'
}
];

/*Controls */
export const controlFrequencyType = [{
	value: 1,
	label: 'הרבה פעמים ביום',
	title: 'הרבה פעמים ביום'
},
{
	value: 2,
	label: 'יומי',
	title: 'יומי'
},
{
	value: 3,
	label: 'שבועי',
	title: 'שבועי'
},
{
	value: 4,
	label: 'חודשי',
	title: 'חודשי'
},
{
	value: 5,
	label: 'רבעוני',
	title: 'רבעוני'
},
{
	value: 6,
	label: 'שנתי',
	title: 'שנתי'
},
{
	value: 8,
	label: 'אחרים',
	title: 'אחרים'
}
];

export const controlTypes = [{
	value: 1,
	label: ' בקרת אפליקציית IT'
},
{
	value: 2,
	label: 'IT Dependent Manual Control'
},
{
	value: 3,
	label: 'מונעת ידנית'
},
{
	value: 4,
	label: 'בקרה ידנית מגלה'
}
];

export const strategyTypeCheck = {
	1: 'בקרות',
	2: 'ביקורת מבססת'
};

export const designEffectivenessType = [{
	DesignEffectivenessTypeId: 1,
	DesignEffectivenessTypeName: 'יעיל'
},
{
	DesignEffectivenessTypeId: 2,
	DesignEffectivenessTypeName: 'לא יעיל'
}
];

export const controlEffectivenessType = [{
	ControlEffectivenessTypeId: 1,
	ControlEffectivenessTypeName: 'יעיל'
},
{
	ControlEffectivenessTypeId: 2,
	ControlEffectivenessTypeName: 'לא יעיל'
}
];

export const testing = [{
	testingId: 1,
	testingDescription: 'כן'
},
{
	testingId: 2,
	testingDescription: 'לא'
}
];

export const controlType = [{
	value: 1,
	label: ' בקרת אפליקציית IT',
	title: ' בקרת אפליקציית IT'
},
{
	value: 2,
	label: 'IT Dependent Manual Control',
	title: 'IT Dependent Manual Control'
},
{
	value: 3,
	label: 'מונעת ידנית',
	title: 'מונעת ידנית'
},
{
	value: 4,
	label: 'בקרה ידנית מגלה',
	title: 'בקרה ידנית מגלה'
}
];

export const aggregateITEvaluationType = [{
	value: 1,
	label: 'תמיכה',
	title: 'תמיכה'
},
{
	value: 2,
	label: 'לא נתמך',
	title: 'לא נתמך'
},
{
	value: 3,
	label: 'תמיכה ב-FS & ICFR',
	title: 'תמיכה ב-FS & ICFR'
},
{
	value: 4,
	label: 'תמיכה ב-FS בלבד',
	title: 'תמיכה ב-FS בלבד'
}
];

export const KnowledgeFormProfileQuestion = [{
	value: 1,
	label: 'מורכב'
},
{
	value: 2,
	label: 'ברשימה'
},
{
	value: 3,
	label: 'PCAOB - IA'
},
{
	value: 4,
	label: 'PCAOB - FS'
},
{
	value: 5,
	label: 'Location'
},
{
	value: 6,
	label: 'אנגלית (ארהב)'
},
{
	value: 7,
	label: 'ביקורת קבוצתית'
},
{
	value: 8,
	label: 'Digital'
}
];

export const KnowledgeLanguage = [{
	value: 1,
	label: 'אנגלית'
},
{
	value: 2,
	label: 'ספרדית (אמריקה הלטינית)'
},
{
	value: 3,
	label: 'צרפתית (קנדה)'
},
{
	value: 4,
	label: 'הוֹלַנדִי'
},
{
	value: 5,
	label: 'קרואטית'
},
{
	value: 6,
	label: 'צכי'
},
{
	value: 7,
	label: 'דַנִי'
},
{
	value: 8,
	label: 'פִינִית'
},
{
	value: 9,
	label: 'גרמנית (גרמניה אוסטריה)',
},
{
	value: 10,
	label: 'הוּנגָרִי'
},
{
	value: 11,
	label: 'אִיטַלְקִית'
},
{
	value: 12,
	label: 'יפני (יפן)'
},
{
	value: 13,
	label: 'נורווגית (נורבגיה)'
},
{
	value: 14,
	label: 'פולני'
},
{
	value: 15,
	label: 'סלובקית'
},
{
	value: 16,
	label: 'סלובנית'
},
{
	value: 17,
	label: 'שוודית'
},
{
	value: 18,
	label: 'עֲרָבִית'
},
{
	value: 19,
	label: 'סינית מפושטת (סין)'
},
{
	value: 20,
	label: 'סינית מסורתית (טייוואן)'
},
{
	value: 21,
	label: 'יווני'
},
{
	value: 22,
	label: 'עברית (ישראל)'
},
{
	value: 23,
	label: 'אינדונזית'
},
{
	value: 24,
	label: 'קוריאנית (רפובליקת קוריאה)'
},
{
	value: 25,
	label: 'פורטוגזית (ברזיל)'
},
{
	value: 26,
	label: 'רומנית'
},
{
	value: 27,
	label: 'רוסית (רוסיה)'
},
{
	value: 28,
	label: 'תאילנדית'
},
{
	value: 29,
	label: 'טורקית'
},
{
	value: 30,
	label: 'וייטנאמית'
},
{
	value: 31,
	label: 'PCAOB - אנגלית'
},
{
	value: 32,
	label: 'PCAOB - ספרדית (אמריקה הלטינית)'
},
{
	value: 33,
	label: 'PCAOB - צרפתית (קנדה)'
},
{
	value: 34,
	label: 'PCAOB - הולנדית'
},
{
	value: 35,
	label: 'PCAOB - קרואטית'
},
{
	value: 36,
	label: "PCAOB - צ'כית"
},
{
	value: 37,
	label: 'PCAOB - דנית'
},
{
	value: 38,
	label: 'PCAOB - פינית'
},
{
	value: 39,
	label: 'PCAOB - גרמנית (גרמניה, אוסטריה)',
},
{
	value: 40,
	label: 'PCAOB - הונגרית'
},
{
	value: 41,
	label: 'PCAOB - איטלקי'
},
{
	value: 42,
	label: 'PCAOB - יפנית (יפן)'
},
{
	value: 43,
	label: 'PCAOB - נורבגית (נורווגיה)'
},
{
	value: 44,
	label: 'PCAOB - פולנית'
},
{
	value: 45,
	label: 'PCAOB - סלובקית'
},
{
	value: 46,
	label: 'PCAOB - סלובנית'
},
{
	value: 47,
	label: 'PCAOB - שוודית'
},
{
	value: 48,
	label: 'PCAOB - ערבית'
},
{
	value: 49,
	label: 'PCAOB - סינית פשוטה (סין)'
},
{
	value: 50,
	label: 'PCAOB - סינית מסורתית (טייוואן)'
},
{
	value: 51,
	label: 'PCAOB - יוונית'
},
{
	value: 52,
	label: 'PCAOB - עברית (ישראל)'
},
{
	value: 53,
	label: 'PCAOB - אינדונזית'
},
{
	value: 54,
	label: 'PCAOB - קוריאנית (הרפובליקה של קוריאה)'
},
{
	value: 55,
	label: 'PCAOB - פורטוגזית (ברזיל)'
},
{
	value: 56,
	label: 'PCAOB - רומנית'
},
{
	value: 57,
	label: 'PCAOB - רוסית (רוסיה)'
},
{
	value: 58,
	label: 'PCAOB - תאילנדי'
},
{
	value: 59,
	label: 'PCAOB - טורקית'
},
{
	value: 60,
	label: 'PCAOB - וייטנאמי'
}
];

export const KnowledgeCountry = [{
	value: 1,
	label: 'מיוט'
},
{
	value: 2,
	label: 'איי בתולה בריטיים'
},
{
	value: 3,
	label: 'סְפָרַד'
},
{
	value: 4,
	label: 'בליז'
},
{
	value: 5,
	label: 'פרו'
},

{
	value: 6,
	label: 'סלובקיה'
},
{
	value: 7,
	label: 'ונצואלה'
},
{
	value: 8,
	label: 'נורווגיה'
},
{
	value: 9,
	label: 'איי פוקלנד (מלווינס)'
},
{
	value: 10,
	label: 'מוזמביק'
},

{
	value: 11,
	label: 'סין'
},
{
	value: 12,
	label: 'סודן'
},
{
	value: 13,
	label: 'ישראל'
},
{
	value: 14,
	label: 'בלגיה'
},
{
	value: 15,
	label: 'ערב הסעודית'
},

{
	value: 16,
	label: 'גיברלטר'
},
{
	value: 17,
	label: 'גואם'
},
{
	value: 18,
	label: 'איי נורפולק'
},
{
	value: 19,
	label: 'זמביה'
},
{
	value: 20,
	label: 'ראוניון'
},

{
	value: 21,
	label: 'אזרבייגן'
},
{
	value: 22,
	label: 'סנט הלנה'
},
{
	value: 23,
	label: 'איראן'
},
{
	value: 24,
	label: 'מונקו'
},
{
	value: 25,
	label: 'סן פייר ומיקלון'
},

{
	value: 26,
	label: 'ניו זילנד'
},
{
	value: 27,
	label: 'איי קוק'
},
{
	value: 28,
	label: 'סנט לוסיה'
},
{
	value: 29,
	label: 'זימבבואה'
},
{
	value: 30,
	label: 'עִירַאק'
},

{
	value: 31,
	label: 'טונגה'
},
{
	value: 32,
	label: 'סמואה האמריקנית'
},
{
	value: 33,
	label: 'מלדיבים'
},
{
	value: 34,
	label: 'מָרוֹקוֹ'
},
{
	value: 35,
	label: 'תקנים בינלאומיים לביקורת (ISA)'
},

{
	value: 36,
	label: 'אלבניה'
},
{
	value: 37,
	label: 'אפגניסטן'
},
{
	value: 38,
	label: 'גמביה'
},
{
	value: 39,
	label: 'בורקינה פאסו'
},
{
	value: 40,
	label: 'טוקלאו'
},

{
	value: 41,
	label: 'לוב'
},
{
	value: 42,
	label: 'קנדה'
},
{
	value: 43,
	label: 'איחוד האמירויות הערביות'
},
{
	value: 44,
	label: 'קוריאה הרפובליקה העממית הדמוקרטית של',
},
{
	value: 45,
	label: 'מונטסראט'
},

{
	value: 46,
	label: 'גרינלנד'
},
{
	value: 47,
	label: 'רואנדה'
},
{
	value: 48,
	label: 'פיגי'
},
{
	value: 49,
	label: 'גיבוטי'
},
{
	value: 50,
	label: 'בוצואנה'
},

{
	value: 51,
	label: 'כווית'
},
{
	value: 52,
	label: 'מדגסקר'
},
{
	value: 53,
	label: 'האי מאן'
},
{
	value: 54,
	label: 'הונגריה'
},
{
	value: 55,
	label: 'נמיביה'
},

{
	value: 56,
	label: 'מלטה'
},
{
	value: 57,
	label: 'גרזי'
},
{
	value: 58,
	label: 'תאילנד'
},
{
	value: 59,
	label: 'סנט קיטס ונוויס'
},
{
	value: 60,
	label: 'בהוטן'
},

{
	value: 61,
	label: 'פנמה'
},
{
	value: 62,
	label: 'סומליה'
},
{
	value: 63,
	label: 'בחריין'
},
{
	value: 64,
	label: 'בוסניה והרצגובינה'
},
{
	value: 65,
	label: 'צָרְפַת'
},

{
	value: 66,
	label: 'קוריאה הרפובליקה של',
},
{
	value: 67,
	label: 'אִיסלַנד'
},
{
	value: 68,
	label: 'פּוֹרטוּגָל'
},
{
	value: 69,
	label: 'תוניסיה'
},
{
	value: 70,
	label: 'גאנה'
},

{
	value: 71,
	label: 'קמרון'
},
{
	value: 72,
	label: 'יָוָן'
},
{
	value: 73,
	label: 'שטחי הדרום הצרפתיים'
},
{
	value: 74,
	label: 'איי הרד ומקדונלד'
},
{
	value: 75,
	label: 'אנדורה'
},

{
	value: 76,
	label: 'לוקסמבורג'
},
{
	value: 77,
	label: 'סמואה'
},
{
	value: 78,
	label: 'אנגווילה'
},
{
	value: 79,
	label: 'הולנד'
},
{
	value: 80,
	label: 'גינאה ביסאו'
},

{
	value: 81,
	label: 'ניקרגואה'
},
{
	value: 82,
	label: 'פרגוואי'
},
{
	value: 83,
	label: 'אנטיגואה וברבודה'
},
{
	value: 84,
	label: 'תקן דיווח כספי בינלאומי (IFRS)'
},
{
	value: 85,
	label: 'ניזר'
},

{
	value: 86,
	label: 'מִצְרַיִם'
},
{
	value: 87,
	label: 'מדינת הוותיקן'
},
{
	value: 88,
	label: 'לטביה'
},
{
	value: 89,
	label: 'קַפרִיסִין'
},
{
	value: 90,
	label: "האיים הקטנים של ארה'ב"
},

{
	value: 91,
	label: 'רוּסִיָה'
},
{
	value: 92,
	label: 'סנט וינסנט והגרנדינים'
},
{
	value: 93,
	label: 'גרנסי'
},
{
	value: 94,
	label: 'בורונדי'
},
{
	value: 95,
	label: 'קובה'
},

{
	value: 96,
	label: 'גיניאה המשוונית'
},
{
	value: 97,
	label: 'הטריטוריה הבריטית באוקיאנוס ההודי'
},
{
	value: 98,
	label: 'שבדיה'
},
{
	value: 99,
	label: 'אוגנדה'
},
{
	value: 100,
	label: 'מקדוניה הרפובליקה היוגוסלבית לשעבר',
},

{
	value: 101,
	label: 'סווזילנד'
},
{
	value: 102,
	label: 'אל סלבדור'
},
{
	value: 103,
	label: 'קירגיזסטן'
},
{
	value: 104,
	label: 'אירלנד'
},
{
	value: 105,
	label: 'קזחסטן'
},

{
	value: 106,
	label: 'הונדורס'
},
{
	value: 107,
	label: 'אורוגוואי'
},
{
	value: 108,
	label: 'גורגיה'
},
{
	value: 109,
	label: 'טרינידד וטובגו'
},
{
	value: 110,
	label: 'הרשות הפלסטינית'
},

{
	value: 111,
	label: 'מרטיניק'
},
{
	value: 112,
	label: 'איי אלנד'
},
{
	value: 113,
	label: 'פולינזיה הצרפתית'
},
{
	value: 114,
	label: 'חוף שנהב'
},
{
	value: 115,
	label: 'מונטנגרו'
},

{
	value: 116,
	label: 'דרום אפריקה'
},
{
	value: 117,
	label: 'דרום גורגיה ואיי הכריכים הדרומיים'
},
{
	value: 118,
	label: 'תֵימָן'
},
{
	value: 119,
	label: 'הונג קונג סין'
},
{
	value: 120,
	label: 'קניה'
},

{
	value: 121,
	label: 'צאד'
},
{
	value: 122,
	label: 'קולומביה'
},
{
	value: 123,
	label: 'קוסטה ריקה'
},
{
	value: 124,
	label: 'אנגולה'
},
{
	value: 125,
	label: 'ליטא'
},

{
	value: 126,
	label: 'סוּריָה'
},
{
	value: 127,
	label: 'מלזיה'
},
{
	value: 128,
	label: 'סיירה לאון'
},
{
	value: 129,
	label: 'סרביה'
},
{
	value: 130,
	label: 'פּוֹלִין'
},

{
	value: 131,
	label: 'סורינאם'
},
{
	value: 132,
	label: 'האיטי'
},
{
	value: 133,
	label: 'נאורו'
},
{
	value: 134,
	label: 'סאו טומה ופרינסיפה'
},
{
	value: 135,
	label: 'סוואלברד ויאן מאיין'
},

{
	value: 136,
	label: 'סינגפור'
},
{
	value: 137,
	label: 'מולדובה'
},
{
	value: 138,
	label: 'טייוואן'
},
{
	value: 139,
	label: 'סנגל'
},
{
	value: 140,
	label: 'גבון'
},

{
	value: 141,
	label: 'מקסיקו'
},
{
	value: 142,
	label: 'סיישל'
},
{
	value: 143,
	label: 'מיקרונזיה מדינות הפדרציה של',
},
{
	value: 144,
	label: 'אלגיריה'
},
{
	value: 145,
	label: 'אִיטַלִיָה'
},

{
	value: 146,
	label: 'סן מרינו'
},
{
	value: 147,
	label: 'ליבריה'
},
{
	value: 148,
	label: 'בְּרָזִיל'
},
{
	value: 149,
	label: 'קרואטיה'
},
{
	value: 150,
	label: 'איי פרו'
},

{
	value: 151,
	label: 'פאלאו'
},
{
	value: 152,
	label: 'פינלנד'
},
{
	value: 153,
	label: 'פיליפינים'
},
{
	value: 154,
	label: 'גמייקה'
},
{
	value: 155,
	label: 'גיאנה הצרפתית'
},

{
	value: 156,
	label: 'קייפ ורדה'
},
{
	value: 157,
	label: 'מיאנמר'
},
{
	value: 158,
	label: 'לסוטו'
},
{
	value: 159,
	label: "איי הבתולה של ארה'ב"
},
{
	value: 160,
	label: 'איי קיימן'
},

{
	value: 161,
	label: 'ניואה'
},
{
	value: 162,
	label: 'ללכת'
},
{
	value: 163,
	label: 'בלארוס'
},
{
	value: 164,
	label: 'דומיניקה'
},
{
	value: 165,
	label: 'אִינדוֹנֵזִיָה'
},

{
	value: 166,
	label: 'אוזבקיסטן'
},
{
	value: 167,
	label: 'ניגריה'
},
{
	value: 168,
	label: 'וואליס ופוטונה'
},
{
	value: 169,
	label: 'ברבדוס'
},
{
	value: 170,
	label: 'סרי לנקה'
},

{
	value: 171,
	label: 'הממלכה המאוחדת'
},
{
	value: 172,
	label: 'אקוודור'
},
{
	value: 173,
	label: 'גוואדלופ'
},
{
	value: 174,
	label: 'לאוס'
},
{
	value: 175,
	label: 'יַרדֵן'
},

{
	value: 176,
	label: 'איי שלמה'
},
{
	value: 177,
	label: 'מזרח טימור'
},
{
	value: 178,
	label: 'לבנון'
},
{
	value: 179,
	label: 'הרפובליקה המרכז - אפריקאית'
},
{
	value: 180,
	label: 'הוֹדוּ'
},

{
	value: 181,
	label: 'אי חג המולד'
},
{
	value: 182,
	label: 'ונואטו'
},
{
	value: 183,
	label: 'ברוניי'
},
{
	value: 184,
	label: 'בנגלדש'
},
{
	value: 185,
	label: 'אנטרטיקה'
},

{
	value: 186,
	label: 'בוליביה'
},
{
	value: 187,
	label: 'טורקיה'
},
{
	value: 188,
	label: 'איי בהאמה'
},
{
	value: 189,
	label: 'קומורו'
},
{
	value: 190,
	label: 'סהרה המערבית'
},

{
	value: 191,
	label: '  הרפובליקה הצכית'
},
{
	value: 192,
	label: '  אוקראינה'
},
{
	value: 193,
	label: '  אסטוניה'
},
{
	value: 194,
	label: 'בולגריה'
},
{
	value: 195,
	label: 'מאוריטניה'
},

{
	value: 196,
	label: 'קונגו הרפובליקה הדמוקרטית של',
},
{
	value: 197,
	label: 'ליכטנשטיין'
},
{
	value: 198,
	label: 'פיטקארן'
},
{
	value: 199,
	label: 'דנמרק'
},
{
	value: 200,
	label: 'איי מרשל'
},

{
	value: 201,
	label: 'יפן'
},
{
	value: 202,
	label: 'אוֹסְטְרֵיָה'
},
{
	value: 203,
	label: 'עומאן'
},
{
	value: 204,
	label: 'מונגוליה'
},
{
	value: 205,
	label: 'טגיקיסטן'
},

{
	value: 206,
	label: 'שוויץ'
},
{
	value: 207,
	label: 'גואטמלה'
},
{
	value: 208,
	label: 'אריתריאה'
},
{
	value: 209,
	label: 'נפאל'
},
{
	value: 210,
	label: 'מאלי'
},

{
	value: 211,
	label: 'סלובניה'
},
{
	value: 212,
	label: 'איי מריאנה הצפוניים'
},
{
	value: 213,
	label: '(לא ישים)'
},
{
	value: 214,
	label: 'ארובה'
},
{
	value: 215,
	label: 'קונגו'
},

{
	value: 216,
	label: 'קטאר'
},
{
	value: 217,
	label: 'גינאה'
},
{
	value: 218,
	label: 'ארצות הברית'
},
{
	value: 219,
	label: 'אֶתִיוֹפִּיָה'
},
{
	value: 220,
	label: 'אחרים'
},

{
	value: 221,
	label: 'גיאנה'
},
{
	value: 222,
	label: 'גֶרמָנִיָה'
},
{
	value: 223,
	label: 'ברמודה'
},
{
	value: 224,
	label: 'איי טורקס וקאיקוס'
},
{
	value: 225,
	label: 'אוֹסטְרַלִיָה'
},

{
	value: 226,
	label: 'קיריבטי'
},
{
	value: 227,
	label: 'פוארטו ריקו'
},
{
	value: 228,
	label: 'פקיסטן'
},
{
	value: 229,
	label: 'מאוריציוס'
},
{
	value: 230,
	label: 'מלאווי'
},

{
	value: 231,
	label: 'טורקמניסטן'
},
{
	value: 232,
	label: 'קמבודיה'
},
{
	value: 233,
	label: 'צילה'
},
{
	value: 234,
	label: 'קלדוניה החדשה'
},
{
	value: 235,
	label: 'פפואה גינאה החדשה'
},

{
	value: 236,
	label: 'אי בובט'
},
{
	value: 237,
	label: 'טובאלו'
},
{
	value: 238,
	label: 'קוראסאו'
},
{
	value: 239,
	label: 'הרפובליקה הדומיניקנית'
},
{
	value: 240,
	label: 'וייטנאם'
},

{
	value: 241,
	label: 'איי קוקוס (קילינג)'
},
{
	value: 242,
	label: 'גרנדה'
},
{
	value: 243,
	label: 'טנזניה'
},
{
	value: 244,
	label: 'ארגנטינה'
},
{
	value: 245,
	label: 'מקאו סין',
},

{
	value: 246,
	label: 'בנין'
},
{
	value: 247,
	label: 'רומניה'
},
{
	value: 248,
	label: 'אַרְמֶנִיָה'
},
{
	value: 249,
	label: 'Global'
},
{
	value: 250,
	label: "תקן הדיווח הכספי הבינ'ל לחברות קטנות ובינוניות'"
},

{
	value: 251,
	label: 'US GAAP'
},
{
	value: 252,
	label: 'מסגרת דיווח כספי AICPA לישויות קטנות ובינוניות'
},
{
	value: 253,
	label: 'דרום סודן'
}
];

export const pagingSvgHoverText = {
	first: 'עמוד ראשון',
	previous: 'דף קודם',
	next: 'העמוד הבא',
	last: 'העמוד האחרון'
};

export const priorityTypesForDropdown = [{
	value: 1,
	label: 'נמוך',
	className: 'Low'
},
{
	value: 2,
	label: 'בינוני',
	className: 'Medium'
},
{
	value: 3,
	label: 'גבוה',
	className: 'High'
},
{
	value: 4,
	label: 'קריטי',
	className: 'Critical'
}
];

export const reviewNoteFilterTypes = [{
	value: 0,
	label: 'הכול'
},
{
	value: 1,
	label: 'פתח'
},
{
	value: 2,
	label: 'Cleared'
},
{
	value: 3,
	label: 'סגור'
}
];

export const reviewStatus = [{
	id: 1,
	name: 'פתח'
},
{
	id: 2,
	name: 'Cleared'
},
{
	id: 3,
	name: 'סגור'
}
];

export const reviewNoteOpenStatusOption = [{
	value: 2,
	label: 'Clear'
},
{
	value: 3,
	label: 'סגור'
}
];

export const reviewNoteClearedStatusOption = [{
	value: 1,
	label: 'פתח שוב'
},
{
	value: 3,
	label: 'סגור'
}
];

export const reviewNoteBulkClearedStatusOption = [{
	value: 1,
	label: 'פתח שוב'
},
{
	value: 2,
	label: 'Clear'
},
{
	value: 3,
	label: 'סגור'
}
];

export const reviewNoteClosedStatusOption = [{
	value: 1,
	label: 'פתח שוב'
},
{
	value: 4,
	label: 'מחק'
}
];

export const taskTypeBadge = {
	1: 'OST',
	2: 'PST',
	3: 'WT',
	4: 'TOC',
	5: 'OSP',
	6: 'PSP',
	7: 'RT',
	8: 'GT',
	9: 'PIC',
	10: 'EQR',
	11: 'PIC/EQR',
	22: 'ACT',
	23: 'UDP'
};

export const riskTypes = [{
	id: 1,
	name: 'סיכונים משמעותיים',
	abbrev: 'SR',
	label: 'משמעותי',
	title: 'סיכון משמעותי (Significant Risk)'
},
{
	id: 2,
	name: 'סיכוני הונאה',
	abbrev: 'FR',
	label: 'הונאה',
	title: 'סיכון תרמית'
},
{
	id: 3,
	name: 'סיכון להצגה מוטעית מהותית',
	abbrev: 'R',
	label: 'סיכון להצגה מוטעית מהותית',
	title: 'סיכון להצגה מוטעית מהותית'
},
{
	id: 4,
	name: 'אומדן בסיכון נמוך מאוד (Very low risk estimate)',
	abbrev: 'VLRS',
	label: 'אומדן בסיכון נמוך מאוד (Very low risk estimate)',
	title: 'אומדן בסיכון נמוך מאוד (Very low risk estimate)'
},
{
	id: 5,
	name: 'אומדן בסיכון נמוך יותר (Lower risk estimate)',
	abbrev: 'LRE',
	label: 'אומדן בסיכון נמוך יותר (Lower risk estimate)',
	title: 'אומדן בסיכון נמוך יותר (Lower risk estimate)'
},
{
	id: 6,
	name: 'אומדן סיכון גבוה יותר (Higher risk estimate)',
	abbrev: 'HRE',
	label: 'אומדן סיכון גבוה יותר (Higher risk estimate)',
	title: 'אומדן סיכון גבוה יותר (Higher risk estimate)'
},
{
	id: 7,
	name: 'אומדן - לא נבחר',
	abbrev: 'ENS',
	label: 'אומדן - לא נבחר',
	title: 'אומדן - לא נבחר'
}

];

export const relatedRisksDropdownRiskTypes = [{
	id: 1,
	name: 'סיכונים משמעותיים',
	abbrev: 'SR',
	label: 'משמעותי',
	title: 'סיכון משמעותי (Significant Risk)'
},
{
	id: 2,
	name: 'סיכוני הונאה',
	abbrev: 'FR',
	label: 'הונאה',
	title: 'סיכון תרמית'
},
{
	id: 3,
	name: 'סיכון להצגה מוטעית מהותית',
	abbrev: 'R',
	label: 'סיכון להצגה מוטעית מהותית',
	title: 'סיכון להצגה מוטעית מהותית'
}
];

export const estimateTypes = [{
	id: 4,
	name: 'אומדן בסיכון נמוך מאוד (Very low risk estimate)',
	abbrev: 'VLRE',
	label: 'מאוד נמוך',
	title: 'אומדן בסיכון נמוך מאוד (Very low risk estimate)'
},
{
	id: 5,
	name: 'אומדן בסיכון נמוך יותר (Lower risk estimate)',
	abbrev: 'LRE',
	label: 'נמוך יותר',
	title: 'אומדן בסיכון נמוך יותר (Lower risk estimate)'
},
{
	id: 6,
	name: 'אומדן סיכון גבוה יותר (Higher risk estimate)',
	abbrev: 'HRE',
	label: 'גבוה יותר',
	title: 'אומדן סיכון גבוה יותר (Higher risk estimate)'
},
{
	id: 7,
	name: 'אומדן - לא נבחר',
	abbrev: 'NA',
	label: 'לא נבחר',
	title: 'אומדן - לא נבחר'
}
];

export const statementTypes = [{
	id: 1,
	name: 'דוח רווח והפסד'
},
{
	id: 2,
	name: 'מאזן'
},
{
	id: 3,
	name: 'שיהם'
}
];

export const RbacErrors = {
	106: 'אין הרשאות מספיקות לעריכת תוכן. עבוד עם מנהל ההתקשרות בכדי לקבל הרשאות מספקות.'
};

export const HelixProjectValidationErrors = {
	800: 'נראה שעדיין לא קיבלת גישה ל- EY Helix? לך ל',
	801: 'אינך חבר צוות מורשה של הפרויקט המקושר. צור קשר עם מנהל הפרויקט של EY Helix בכדי לקבל גישה.',
	901: 'פרויקט ה- EY Helix שנבחר אינו זמין עוד. לחץ על פרויקטים של EY Helix בכדי לקשר פרויקט חדש.',
	902: 'פרויקט ה- EY Helix שנבחר מסומן למחיקה. לחץ על פרויקטים של EY Helix בכדי לקשר פרויקט חדש.',
	903: 'פרויקט ה- EY Helix שנבחר מסומן לאחסון. לחץ על פרויקטים של EY Helix בכדי לקשר פרויקט חדש.',
	926: 'ה- analyzer שנבחר אינו זמין ב- EY Helix. רענן את הדף ונסה שוב. אם הבעיה ממשיכה, אנא פנה לתמיכה הטכנית.',
	927: 'הניתוחים אינם זמינים בפרויקט המקושר. עבור אל EY Helix והשלם את שלבי עיבוד הנתונים וניתוח כדי להמשיך.',
	928: 'מנתח לא חוקי או חסר ב- EY Helix. רענן את הדף ונסה שוב. אם הבעיה ממשיכה, אנא פנה לתמיכה הטכנית.',
	929: 'אירעה שגיאה הקשורה לפרויקט EY Helix המקושר. לא ניתן לייבא נתונים.'
};

export const EngagementProfileRequirementErrors = {
	108: 'פרופיל תיק הביקורת לא הושלם.'
};

export const IndependenceRequirementErrors = {
	103: 'לא הושלמו דרישות אי תלות של המשתמש (Engagement User Independence) בתיק הביקורת'
};

export const strategyTypes = [{
	id: 3,
	name: 'בסקופ הביקורת'
},
{
	id: 4,
	name: 'מחוץ לסקופ הביקורת'
}
];

export const itAppTypes = [{
	value: 0,
	label: 'אפליקציית IT'
},
{
	value: 1,
	label: 'לשכת שירות'
}
];

export const confidentialityLevels = {
	[confidentialityTypes.DEFAULT]: 'ברירת מחדל',
	[confidentialityTypes.LOW]: 'Low',
	[confidentialityTypes.MODERATE]: 'Moderate',
	[confidentialityTypes.HIGH]: 'High'

	// This has been disabled for release 2.5, uncomment if required
	// [confidentialityTypes.CONFIDENTIAL]: 'סודי'
};

export const formBodyOptionRiskTypes = [{
	id: 1,
	label: 'סיכון משמעותי (Significant Risk)'
},
{
	id: 2,
	label: 'סיכון תרמית'
},
{
	id: 3,
	label: 'סיכון להצגה מוטעית מהותית'
}
];

export const formViewTypes = [{
	value: 0,
	label: 'טופס'
},
{
	value: 1,
	label: 'שינויים'
},
{
	value: 2,
	label: 'פרטים'
}
];

export const railFilterValidations = [{
	value: 0,
	label: 'הכול'
},
{
	value: 1,
	label: 'מענה שלא שולם'
},
{
	value: 2,
	label: 'הערות (comments) לא פתורות'
}
];

export const aresRiskTypes = [{
	id: 1,
	name: 'סיכון משמעותי (Significant Risk)'
},
{
	id: 2,
	name: 'סיכון תרמית'
},
{
	id: 3,
	name: 'סיכון להצגה מוטעית מהותית'
},
{
	id: 4,
	name: 'אומדן בסיכון נמוך מאוד (Very low risk estimate)'
},
{
	id: 5,
	name: 'אומדן בסיכון נמוך יותר (Lower risk estimate)'
},
{
	id: 6,
	name: 'אומדן בסיכון גבוה יותר (Higher risk estimate)'
},
{
	id: 7,
	name: 'אומדן - לא נבחר'
}
];

export const materialityTypes = [{
	value: 1,
	label: 'רווח לפני מס'
},
{
	value: 2,
	label: 'EBIT (רווח לפני ריבית ומסים)'
},
{
	value: 3,
	label: 'EBITDA (רווח לפני ריבית, מסים, פחת והפחתות)',
},
{
	value: 4,
	label: 'רווח גולמי'
},
{
	value: 5,
	label: 'הכנסות'
},
{
	value: 6,
	label: 'הוצאות תפעול'
},
{
	value: 7,
	label: 'הון'
},
{
	value: 8,
	label: 'נכסים'
},
{
	value: 9,
	label: 'מדדים מבוססי פעילות (אחר)'
},
{
	value: 10,
	label: 'הפסדים לפני מס'
},
{
	value: 11,
	label: 'מדדים מבוססי הון (אחר)'
},
{
	value: 12,
	label: 'מדדים מבוססי רווחיות (אחר)'
}
];

export const helixCurrencyType = {
	[currencyType.Functional]: 'פוּנקצִיוֹנָלִי',
	[currencyType.Reporting]: 'דוחות'
};

export const controlRiskType = [{
	id: 1,
	name: 'הסתמכות על בקרות'
},
{
	id: 2,
	name: 'לא להסתמך'
},
{
	id: 3,
	name: 'בדיקת MP'
}
];

export const inherentRiskType = [{
	id: 1,
	name: 'גבוה יותר'
},
{
	id: 2,
	name: 'נמוך יותר'
},
{
	id: 3,
	name: 'מאוד נמוך'
}
];

export const AlraInherentRiskType = [{
	id: 3,
	name: 'לא רלוונטי'
},
{
	id: 2,
	name: 'נמוך יותר'
},
{
	id: 1,
	name: 'גבוה יותר'
}
];

export const scotInherentRiskType = [{
	id: 1,
	name: 'גבוה יותר'
},
{
	id: 2,
	name: 'נמוך יותר'
},
{
	id: 3,
	name: 'תת-תהליך מהותי (SCOT) שאינו שגרתי'
}
];

export const CRAStrings = {
	Minimal: 'Minimal',
	Low: 'Low',
	'Low +SC': "Low + SC",
	Moderate: 'Moderate',
	High: 'High',
	'High +SC': "High + SC"
};

export const priorityType = [{
	id: 1,
	name: 'Low',
	className: 'Low',
	label: 'L'
},
{
	id: 2,
	name: 'Medium',
	className: 'Medium',
	label: 'M'
},
{
	id: 3,
	name: 'High',
	className: 'High',
	label: 'H'
},
{
	id: 4,
	name: 'Critical',
	className: 'Critical',
	label: 'C'
}
];

export const kendoLabels = {
	addComment: 'הוסף תגובה',
	addColumnBefore: 'הוסף עמודה משמאל',
	addColumnAfter: 'הוסף עמודה מימין',
	addInlineComment: 'הוסף תגובה מוטבעת',
	addRowAbove: 'הוסף שורה למעלה',
	addRowBelow: 'הוסף שורה מתחת',
	alignLeft: 'הצדק שמאל',
	alignRight: 'נמק נכון',
	alignCenter: 'הצדק במרכז',
	alignFull: 'הצדק מלא',
	backgroundColor: 'צבע רקע',
	bulletList: 'הוסף רשימה לא מסודרת',
	bold: 'נוֹעָז',
	backColor: 'שִׂיא',
	createLink: 'הכנס היפר-קישור',
	createTable: 'צור טבלה',
	cleanFormatting: 'עיצוב נקי',
	deleteRow: 'מחק שורה',
	deleteColumn: 'מחק עמודה',
	deleteTable: 'מחק טבלה',
	fontSizeInherit: 'גודל גופן',
	foreColor: 'צבע גופן',
	format: 'פורמט',
	fontSize: 'גודל גופן',
	hyperlink: 'הוספת קישור',
	italic: 'נטוי',
	indent: 'לְשַׁנֵן',
	insertTableHint: 'צור טבלה של {0} על {1}',
	huge: 'ענק',
	'hyperlink-dialog-content-address': "כתובת אתר אינטרנט",
	'hyperlink-dialog-title': "הכנס היפר-קישור",
	'hyperlink-dialog-content-title': "כותרת",
	'hyperlink-dialog-content-newwindow': "תפתח קישור בחלון חדש",
	'hyperlink-dialog-cancel': "בטל",
	'hyperlink-dialog-insert': "הכנס",
	large: 'גדול',
	noDataPlaceholder: 'הזן טקסט',
	normal: 'נוֹרמָלִי',
	orderedList: 'הכנס רשימה מסודרת',
	outdent: 'Outdent',
	paragraphSize: 'גודל פסקה',
	print: 'הדפס',
	pdf: 'ייצוא ל-pdf',
	redo: 'לבצע שוב',
	removeFormatting: 'הסרת עיצוב',
	strikethrough: 'חוצה',
	small: 'קטן',
	subscript: 'מנוי',
	superscript: 'כתב על',
	underline: 'לָשִׂים דָגֵשׁ',
	undo: 'לבטל',
	unlink: 'בטל קישור'
};

export const kendoFormatOptions = [{
	text: 'פסקה',
	value: 'p'
},
{
	text: 'כותרת 1',
	value: 'h1'
},
{
	text: 'כותרת 2',
	value: 'h2'
},
{
	text: 'כותרת 3',
	value: 'h3'
},
{
	text: 'כותרת 4',
	value: 'h4'
},
{
	text: 'כותרת 5',
	value: 'h5'
},
{
	text: 'כותרת 6',
	value: 'h6'
}
];

export const kendoFontSize = [{
	text: '8',
	value: '8px'
},
{
	text: '9',
	value: '9px'
},
{
	text: '10',
	value: '10px'
},
{
	text: '11',
	value: '11px'
},
{
	text: '12',
	value: '12px'
},
{
	text: '14',
	value: '14px'
},
{
	text: '16',
	value: '16px'
},
{
	text: '18',
	value: '18px'
},
{
	text: '20',
	value: '20px'
},
{
	text: '22',
	value: '22px'
},
{
	text: '24',
	value: '24px'
},
{
	text: '26',
	value: '26px'
},
{
	text: '28',
	value: '28px'
},
{
	text: '36',
	value: '36px'
},
{
	text: '48',
	value: '48px'
},
{
	text: '72',
	value: '72px'
}
];

export const ItFlowValidationLabels = {
	ITAppWithoutAtLeastOneRelatedITProcess: 'אפליקציית IT לא קשורה',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: 'ITGC לא קשור',
	ITSPHasNorelatedITRisk: 'לא קשור ל-ITSP',
	ITProcessHasNoRelatedITApplication: 'תהליך IT לא קשור',
	ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication: 'חסר סיכון IT',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: 'חסר ITGC או סימון אין קיים',
	ITDMorITACWithNoRelatedITApplication: 'בקרת אפליקציה/ITDM ללא אפליקציית IT',
	ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'ITSP שיימחק',
	ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'ITGC שיימחק',
	ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'סיכון ה-IT להימחק',
	ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed: 'ITGC עם אסטרטגיית בדיקה לא חוקית',
	ITGCWithoutASelectedDesignEffectiveness: 'חסרה הערכת עיצוב של ITGC',
	SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero: 'לא קשור SCOT',
	AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed: 'תגובה לא עקבית עבור בקרות בדיקה',
	SCOTWithHasNoITApplicationHasITDMOrAppControls: 'אפליקציית IT ב-SCOT חסרה'
};

export const ISA315ITFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: ItFlowValidationLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: ItFlowValidationLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: ItFlowValidationLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: ItFlowValidationLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: ItFlowValidationLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed,
	label: ItFlowValidationLabels.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: ItFlowValidationLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
}
];

/* Notes modal labels */

export const reviewNoteModalLabels = {
	/*Review Notes*/
	engagement: 'תיק ביקורת',
	emptyReplyErrorMsg: 'הוסף טקסט בכדי להמשיך',
	lengthReplyErrorMsg: 'התשובה לא תעלה על 4000 תווים.',
	documentLabel: 'Document',
	task: 'משימה',
	allEngagementFilterLabel: 'כל שאר ההתקשרויות',
	otherEngagementComments: 'הערות אחרות בהתקשרות',
	notesModalInstructionalText: 'הצג והשב להערות עבור {0} הנבחרים למטה.',
	commentThread: 'שרשור הערה',
	singleNoteInstructionalText: 'הצג והשב להערה שנבחרה למטה.',
	emptyNoteDetailsMessage: 'בחר הערה כדי לצפות בפרטים. כדי לבחור מספר בקרות, השתמש במקשים control או shift ובחר מספר הערות ביקורת. אם ברצונך לעבוד על הערה בודדת, בחר הערה זו מהרשימה.',
	documentReviewNotesLabel: 'הערות במסמך',
	addNewReviewNoteButtonText: 'הוסף הערה',
	noNotesAssociatedWithDocumentLabel: 'השאר הערה באמצעות הקלטים למטה. הקצה את ההערה למשתמש וציין את העדיפות ואת תאריך היעד.',
	noNotesFound: 'לא נמצאו הערות',
	noNotesAssociatedWithTaskLabel: 'אין הערות {0} המשויכות למשימה.',
	allNotesLabel: 'כל ההערות',
	charactersLabel: 'תווים',
	myNotesLabel: 'ההערות שלי',
	showClearedLabel: 'הצג  שנוקו (cleared)',
	showClosedLabel: 'הצג סגורים (closed)',
	assignedToLabel: 'הוקצה ל',

	ofLabel: 'של',
	enterNoteText: 'הזן הערה',
	addNewNoteModalClose: 'Close',
	addNewNoteModalTitleLabel: 'הוסף הערה חדשה',
	editNoteModalTitleLabel: 'ערוך הערה',
	deleteIconHoverText: 'מחק',
	deleteIconModalAcceptText: 'מחק',
	deleteIconModalConfirmMessage: 'האם הינך בטוח שברצונך למחוק את תשובתך להערה זו?',
	deleteIconModalConfirmMessageParent: 'האם הינך בטוח שברצונך למחוק את ההערה שנבחרה?',
	deleteIconModalTitleLabel: 'מחק הערה',
	deleteReplyIconModalTitle: 'מחק תשובה',
	emptyRepliesMessage: 'עדיין אין תגובות',
	replyInputPlaceholder: 'השב להערה זו',
	replyInputPlaceholderEdit: 'ערוך תשובה עם הערה או הערה קולית',
	noteInputPlaceholderEdit: 'ערוך עם הערה או הערה קולית',
	replyText: 'טקסט תגובה',
	editReplyModelTitle: 'ערוך תשובה',
	editReplyPlaceholder: 'הזן מענה',
	noteDueDateLabel: 'היעד בעוד',

	priorityLabel: 'עדיפות',
	dueDateLabel: 'תאריך יעד',
	dueLabel: 'היעד בעוד',
	status: 'סטטוס',
	noteModifiedDateLabel: 'עבר שינוי',
	cancelLabel: 'בטל',
	saveLabel: 'שמור',
	clearedBy: 'נסגר על ידי',
	closedBy: 'נסגר על ידי',
	reopenedBy: 'נפתח מחדש על ידי',
	reply: 'ענה',
	editIconHoverTextLabel: 'ערוך',
	required: 'נדרש',
	closeTitle: 'Close',
	otherEngagementNotes: 'הערות אחרות בתיק הביקורת',
	closeLabel: 'Close',
	showMore: 'הצג עוד',
	showLess: 'הצג פחות',
	showMoreEllipsis: 'הצג עוד ...',
	showLessEllipsis: 'הצג פחות ...',
	noResultFound: 'לא נמצאו תוצאות',
	engagementNameLabel: 'שם ההתקשרות: ',
	taskReviewNotesLabel: 'הערות משימה',
	fromUserLabel: 'מ',
	toUserLabel: 'אל',
	view: 'הצג',
	dueDateRequiredTextError: 'נדרש תאריך יעד'
};

export const notesFilterLabels = [{
	id: notesFilter.allNotes,
	label: 'כל ההערות',
	value: notesFilter.allNotes
},
{
	id: notesFilter.myNotes,
	label: 'ההערות שלי',
	value: notesFilter.myNotes
},
{
	id: notesFilter.authoredByMeNotes,
	label: 'מוקצות לי',
	value: notesFilter.authoredByMeNotes
}
];

export const reviewerAssignments = {
	taskLayoutHeaderAssignments: 'משימות לביצוע',
	manageAssigmentsStep2: 'ערוך משימה שהוקצתה',
	editAssignment: 'ערוך הקצאה',
	deleteAssignment: 'מחק הקצאה',
	manageAssigmentsStep3: 'השלם הקצאה',
	taskAssigmentStatusHeader: 'סטטוס המשימה שהוקצתה',
	taskAssignmentName: 'הקצאה',
	dueDateAssigment: 'היעד בעוד',
	editDueDate: 'אופציונלי: ערוך ימים לפני תאריך הסיום',
	teamMemberAssigmentLabel: 'חבר צוות',
	currentAssigmentLabel: 'נוכחי',
	handOffToAssigmentLabel: 'העבר ל: ',
	priorToEndDateLabel: 'לפני תאריך הסיום',
	noTimePhaseAssigmentLabel: 'לא הוקצה לוח זמנים',
	closedByAssigmentLabel: 'נסגר על ידי',
	onAssingmentLabel: 'על',
	preparerAssigmentOpenTitleTip: 'העבר משימה זו לסגירת המשימה שהוקצתה לך',
	reviewerAssigmentOpenTitleTip: 'סמן את המשימה שהוקצתה כסגורה',
	reviewerAssigmentClosedTitleTip: 'סמן את המשימה שהוקצתה כפתוחה',
	AssigmentLabel: 'העבר משימה זו לסגירת המשימה שהוקצתה לך',
	timePhaseName: 'שלב הזמן (Time phase): ',
	timePhaseEndDate: 'תאריך הסיום: ',
	AssignmentType: [{
		id: 1,
		displayName: 'P'
	},
	{
		id: 2,
		displayName: 'סוקר לסקירה מפורטת'
	},
	{
		id: 3,
		displayName: 'סוקר כללי'
	},
	{
		id: 4,
		displayName: 'שותף'
	},
	{
		id: 5,
		displayName: 'EQR'
	},
	{
		id: 6,
		displayName: 'אחרים'
	}
	],
	AssignmentStatus: [{
		id: 1,
		displayName: 'פתח'
	},
	{
		id: 2,
		displayName: 'בתהליך'
	},
	{
		id: 3,
		displayName: 'סגור'
	},
	{
		id: 4,
		displayName: 'Unassigned'
	}
	],
	assignmentTableColumnHeader: 'הקצאה',
	teamMemberTableColumnHeader: 'חבר צוות',
	dueDaysTableColumnHeader: 'היעד בעוד',
	daysPriorToEndDate: 'ימים לפני תאריך סיום',
	handoffButton: 'העבר'
};
/* Notes modal labels */

export const handOffModal = {
	title: 'מסירה (Hand-off)',
	description: 'העביר (hand-off) את המשימה הזו לחבר הצוות הבא. כדי לחתום על קובץ ראיות, בחר מבין האפשרויות שלהלן.',
	dropdownLabel: 'מסירה (Hand-off) ל',
	closeTitle: 'לבטל',
	confirmButton: 'מסירה (Hand-off)',
	evidence: 'ראיה',
	evidenceSignOffTitle: 'חתום על הכל בתור: ',
	existingSignOffs: 'חתימות קיימות',
	noDocumentsAvailable: 'אין מסמכים זמינים'
};

// manage scot modal labels
export const manageSCOTModal = {
	title: 'נהל תתי תהליכים מהותיים (SCOT)',
	description: 'אתה יכול ליצור, לערוך או למחוק תתי תהליכים מהותיים (SCOT) קיימים. השינויים יחולו לאחר השמירה.',
	addSCOTLink: 'הוסף את תת התהליך המהותי (SCOT)'
};

export const deleteSCOTModal = {
	title: 'מחק תת תהליך מהותי (SCOT)',
	description: 'תת(י) התהליך(כים) המהותי(ים) (SCOT(s)) הבאים יימחקו. לא ניתן לבטל את הפעולה.'
};

export const manageITAppModalLabels = {
	title: 'ניהול יישומי IT',
	description: 'צור יישומי IT חדשים או ערוך ומחק יישומי IT קיימים להלן.',
	inputNameTitle: 'שם יישום ה- IT',
	deleteConfirmMessage: 'האם אתה בטוח שברצונך למחוק את יישום ה-IT <b>{0}</b>? אי אפשר לבטל את זה.',
	addSuccessMessage: "יישום ה-IT'{0}' נוצר בהצלחה",
	editSuccessMessage: "עריכות נשמרו בהצלחה ביישום ה-IT'{0}'",
	deleteSuccessMessage: "'{0}' נמחק בהצלחה"
};

export const manageSOModalLabels = {
	title: 'ניהול לשכות שירות',
	description: 'צור לשכות שירות חדשות או ערוך ומחק לשכות שירות קיימות להלן.',
	inputNameTitle: 'שם לשכת השירות',
	deleteConfirmMessage: 'האם אתה בטוח שברצונך למחוק את לשכת השירות <b>{0}</b>? אי אפשר לבטל את זה.',
	addSuccessMessage: "לשכת השירות'{0}' נוצרה בהצלחה",
	editSuccessMessage: "עריכות נשמרו בהצלחה בלשכת השירות'{0}'",
	deleteSuccessMessage: "'{0}' נמחק בהצלחה",
    addServiceOrganization: 'הוסף לשכת שירות',
	editServiceOrganization: 'ערוך את לשכת השירות (SO)',
	deleteServiceOrganization: 'מחק את לשכת השירות (SO)'
};

export const customNameModal = {
	title: 'סיומת שם מקור פקודת היומן',
	closeTitle: 'בטל',
	save: 'שמור',
	suffixRequired: 'נדרשת סיומת!',
	suffix: 'סיומת',
	addSuffix: 'הוסף סיומת',
	editSuffix: 'ערוך סיומת'
};

export const GuidedWorkFlowLabels = {
	RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM: 'אירועים ותנאים לא קשורים / סיכון להצגה מוטעית',
	RisksUnrelatedToAnAssertionForGuidedWorkflow: 'סיכונים לא מקושרים',
	IncompleteMeasurementBasisForecastAmount: 'חסר תיעוד לקביעת הבסיס',
	IncompleteMeasurementBasisForecastAmountRationale: 'נדרש רציונל לקביעת הבסיס',
	IncompleteMeasurementBasisAdjustedAmount: 'סכום שעודכן וטרם הושלם',
	IncompletePlanningMateriality: 'PM לא הושלם',
	PlanningMaterialityGreaterThanMaximumAmount: 'PM גדול מדי',
	IncompletePlanningMaterialityRationale: 'נדרש רציונל לקביעת ה- PM',
	IncompleteTolerableError: 'TE לא הושלם',
	TENotWithinRangeOfAllowedValues: 'אחוז TE לא חוקי',
	IncompleteTolerableErrorRationale: 'נדרש רציונל לקביעת ה- TE',
	IncompleteSAD: 'SAD לא הושלם',
	SADGreaterThanMaximum: 'SAD גדול מדי',
	IncompleteSADRationale: 'נדרש רציונל לקביעת ה- SAD',
	IncompletePACESelection: 'PACE לא הושלם',
	AccountWithoutIndividualRiskAssessmentForm: 'חשבון{0} - מסמך חסר',
	EstimateWithoutIndividualEstimateForm: 'אומדן{0} - מסמך חסר',
	AccountWithoutIndividualAnalyticForm: 'חשבון{0} - מסמך חסר',
	MultiEntityWithoutIndividualProfileForm: 'ישות ללא מסמך פרופיל פרטני בביקורת מרובה ישויות (Multi-Entity Individual Profile Document)',
	AccountAccountTypeIDDoesNotMatchAction: 'בחירת ייעוד הסעיף לא עקבית',
	AccountHasEstimateDoesNotMatchAction: 'בחירת הסעיף הכרוך באומדן אינה עקבית',
	AccountFormOptionHasRelatedRisksNotAssociatedToAccount: 'סיכון ללא שיוך סעיף קשור',
	AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk: 'מצג הנהלה (assertion) בעל סיכון שבמהות גבוה יותר (higher IR) ללא קישור של סיכון',
	AccountMissingSubstantiveProcedure: 'סעיף/ישויות ללא נוהל מבסס',
	MultiEntityNotRelatedToALLPSTACTForRelatedAccount: ' נדרש עדכון תוכן לסעיף/לישויות',
	ComponentWithoutGroupInvolvementForm: 'ישות מוחזקת ללא טופס מעורבות קבוצתית (לא כולל ישויות מוחזקות לצרכי הפניה (refrences) בלבד)',
	ComponentWithoutRelatedGroupAssessmentInstruction: 'רכיב ללא הנחייה להערכת סיכונים קבוצתית',
	IncompleteAssertionRiskLevel: 'רמת סיכון לא מלאה במצג ההנהלה',
	EstimateAccountWithoutEsimatePSPIndex: 'סעיף הכרוך באומדנים ללא קישור ל- PSP Index לאומדנים',
	AccountExecutedWithoutRelatedComponent: 'קבוצה - סעיפים (אשר מתבצעים בהתקשרויות אחרות) ללא ישות מוחזקת בסקופ קשורה ביקורת מלא או ספציפי',
	MultiEntityAccountWithoutRelatedToAnyMultiEntity: 'סעיף ללא ישות קשורה',
	ChangeNotSubmittedMultiEntityFullProfile: 'השינויים לא הועברו',
	ChangeNotSubmittedMultiEntityIndividualDocument: 'השינויים לא הועברו',
	AccountTypeWithMissingInformation: 'מידע חסר בסעיף',
	DocumentUploadMissingRequiredPICEQRSignOffs: 'ראיות ביקורת ללא חתימה(ות)',
	DocumentUploadMissingRequiredPICEQRSignOffRequirements: 'ראיות ביקורת ללא דרישת(ות) חתימה',
	DocumentUploadMissingPreparerOrReviewerSignOffs: 'העלאת מסמכים - חסרות חתימות מכין או סוקר',
	ITAppWithoutAtLeastOneRelatedITProcess: 'יישום IT לא מקושר',
	ITProcessHasNoRelatedITApplication: 'תהליך IT לא קשור',
	ITGCWithoutASelectedDesignEffectiveness: 'חסרה הערכת תכנון ל- ITGC',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: 'ITGC לא קשור',
	ITSPHasNorelatedITRisk: 'ITSP לא קשור',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: 'חסר ITGC או סמן כי לא קיים',
	EstimateWithoutAccountRelated: 'אומדן ללא קישור לסעיף',
	EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount: 'אומדן בסיכון גבוה/נמוך קשור לחשבון שלא כרוך באומדנים (non-estimate)',
	RiskEstimateRiskTypeIDDoesNotMatchAction: "תגובת קטגוריית אומדן אינה בהלימה מול  האפיון ב'עריכת אומדן'",
	LowerorHigherRiskEstimateWithoutEstimateSCOT: 'אומדן ללא תת תהליך מהותי ( SCOT) חוקי',
	EstimateWithoutIndividualEstimateDocument: 'חסר מסמך בודד לאומדן',
	EstimateAccountWithoutHigherOrLowerRiskEstimate: 'חשבון ללא הערכה תקפה',
	EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary: 'סעיף הכרוך באומדן ללא קישור של אומדן בסיכון גבוה או נמוך יותר',
	EstimateScotWithoutHigherOrLowerRiskEstimate: 'תת תהליך מהותי הכרוך באומדנים Estimate SCOT ללא הערכת סיכון גבוהה או נמוכה יותר',
	HigherRiskEstimateWithoutRisk: 'אומדן בסיכון גבוה יותר ללא שיוך של סיכון תקף',
	PICEQRSignOffRequirements: 'PIC or EQR Signoff requirement does not match response',
	AdjustmentsWithoutAnyEvidence: 'התאמות ללא ראיות',
	AdjustmentsThatDoNotNet: 'התאמות שאינן נטו',
	DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration: "בוצע צ'ק אווט למסמך או שהוא בתהליך צ'ק אווט לצורך שיתוף פעולה",
	NonEngagementWideTasksMissingEvidence: 'משימות רוחביות שאינן חלק מההתקשרות שחסרות להן ראיות ביקורת.',
	EstimatesMustBeMarkedHigherRisk: 'סיכון משמעותי/הונאה הקשור לאומדן שאינו סיכון גבוה יותר',
	SCOTEstimateNoRelatedWalkthroughForm: 'SCOT/אומדן ללא WT',
	SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2: 'SCOT ללא סעיף קשור',
	AccountSignificantDisclosureWithNoRelatedSCOTV2: 'Account - Significant Account / Significant Disclosure that is not a CT only Account with no related SCOT or an Estimate when it is an Estimate Account',
	ITApplicationWithoutITAppRiskAssessmentIndividualDocument: 'Technology risk assessment missing document',
	ITApplicationWithoutITAppPlanningIndividualDocument: 'Technology missing document',
	FormContentWithoutHeader: 'Form Content without Header',
	RisksWithoutAnyRelatedAssertions: 'There are risks that have not been related to at least one assertion',
	AssertionsWithIncompleteCRA: 'There are assertions missing an inherent and/or control risk assessment',
	LimitedRiskOrInsignificantAccountMissingRationale: 'All limited risk and insignificant accounts shall have rationale provided',
	ITProcessWithoutWalkthroughDocument: 'ITProcess without IT process - Walkthrough - Individual',
	ITProcessIsUncategorized: 'IT Process - ITProcessTypeID is Uncategorized',
	ITProcessWithNoRelatedITApplication: 'ITProcess - ITProcess with no related IT Application'
};

export const GuidedWorkFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.RiskEstimateRiskTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.RiskEstimateRiskTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.FormContentWithoutHeader,
	label: GuidedWorkFlowLabels.FormContentWithoutHeader
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary
},
{
	validationId: validationTypes.EstimateScotWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateScotWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.HigherRiskEstimateWithoutRisk,
	label: GuidedWorkFlowLabels.HigherRiskEstimateWithoutRisk
},
{
	validationId: validationTypes.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM,
	label: GuidedWorkFlowLabels.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.RisksUnrelatedToAnAssertionForGuidedWorkflow,
	label: GuidedWorkFlowLabels.RisksUnrelatedToAnAssertionForGuidedWorkflow
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmount
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmountRationale,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmountRationale
},
{
	validationId: validationTypes.IncompleteMeasurementBasisAdjustedAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisAdjustedAmount
},
{
	validationId: validationTypes.IncompletePlanningMateriality,
	label: GuidedWorkFlowLabels.IncompletePlanningMateriality
},
{
	validationId: validationTypes.PlanningMaterialityGreaterThanMaximumAmount,
	label: GuidedWorkFlowLabels.PlanningMaterialityGreaterThanMaximumAmount
},
{
	validationId: validationTypes.IncompletePlanningMaterialityRationale,
	label: GuidedWorkFlowLabels.IncompletePlanningMaterialityRationale
},
{
	validationId: validationTypes.IncompleteTolerableError,
	label: GuidedWorkFlowLabels.IncompleteTolerableError
},
{
	validationId: validationTypes.TENotWithinRangeOfAllowedValues,
	label: GuidedWorkFlowLabels.TENotWithinRangeOfAllowedValues
},
{
	validationId: validationTypes.IncompleteTolerableErrorRationale,
	label: GuidedWorkFlowLabels.IncompleteTolerableErrorRationale
},
{
	validationId: validationTypes.IncompleteSAD,
	label: GuidedWorkFlowLabels.IncompleteSAD
},
{
	validationId: validationTypes.SADGreaterThanMaximum,
	label: GuidedWorkFlowLabels.SADGreaterThanMaximum
},
{
	validationId: validationTypes.IncompleteSADRationale,
	label: GuidedWorkFlowLabels.IncompleteSADRationale
},
{
	validationId: validationTypes.IncompletePACESelection,
	label: GuidedWorkFlowLabels.IncompletePACESelection
},
{
	validationId: validationTypes.AccountWithoutIndividualRiskAssessmentForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualRiskAssessmentForm
},
{
	validationId: validationTypes.EstimateWithoutIndividualEstimateForm,
	label: GuidedWorkFlowLabels.EstimateWithoutIndividualEstimateForm
},
{
	validationId: validationTypes.AccountWithoutIndividualAnalyticForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualAnalyticForm
},
{
	validationId: validationTypes.MultiEntityWithoutIndividualProfileForm,
	label: GuidedWorkFlowLabels.MultiEntityWithoutIndividualProfileForm
},
{
	validationId: validationTypes.AccountAccountTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountAccountTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.AccountHasEstimateDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountHasEstimateDoesNotMatchAction
},
{
	validationId: validationTypes.AccountFormOptionHasRelatedRisksNotAssociatedToAccount,
	label: GuidedWorkFlowLabels.AccountFormOptionHasRelatedRisksNotAssociatedToAccount
},
{
	validationId: validationTypes.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk,
	label: GuidedWorkFlowLabels.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk
},
{
	validationId: validationTypes.MultiEntityNotRelatedToALLPSTACTForRelatedAccount,
	label: GuidedWorkFlowLabels.MultiEntityNotRelatedToALLPSTACTForRelatedAccount
},
{
	validationId: validationTypes.AccountMissingSubstantiveProcedure,
	label: GuidedWorkFlowLabels.AccountMissingSubstantiveProcedure
},
{
	validationId: validationTypes.ComponentWithoutGroupInvolvementForm,
	label: GuidedWorkFlowLabels.ComponentWithoutGroupInvolvementForm
},
{
	validationId: validationTypes.ComponentWithoutRelatedGroupAssessmentInstruction,
	label: GuidedWorkFlowLabels.ComponentWithoutRelatedGroupAssessmentInstruction
},
{
	validationId: validationTypes.EstimateAccountWithoutEstimatePSPIndex,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutEsimatePSPIndex
},
{
	validationId: validationTypes.AssertionInherentRiskWithoutRelatedHigherRisk,
	label: GuidedWorkFlowLabels.IncompleteAssertionRiskLevel
},
{
	validationId: validationTypes.AccountGroupWithoutAComponent,
	label: GuidedWorkFlowLabels.AccountExecutedWithoutRelatedComponent
},
{
	validationId: validationTypes.MultiEntityAccountWithoutRelatedToAnyMultiEntity,
	label: GuidedWorkFlowLabels.MultiEntityAccountWithoutRelatedToAnyMultiEntity
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityFullProfile,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityFullProfile
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityIndividualDocument,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityIndividualDocument
},
{
	validationId: validationTypes.AccountWithMissingValues,
	label: GuidedWorkFlowLabels.AccountTypeWithMissingInformation
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffs
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffRequirements
},
{
	validationId: validationTypes.DocumentUploadMissingPreparerOrReviewerSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingPreparerOrReviewerSignOffs
},
{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: GuidedWorkFlowLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: GuidedWorkFlowLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: GuidedWorkFlowLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: GuidedWorkFlowLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: GuidedWorkFlowLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
},
{
	validationId: validationTypes.EstimateWithoutAccountRelated,
	label: GuidedWorkFlowLabels.EstimateWithoutAccountRelated
},
{
	validationId: validationTypes.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount,
	label: GuidedWorkFlowLabels.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount
},
{
	validationId: validationTypes.LowerorHigherRiskEstimateWithoutEstimateSCOT,
	label: GuidedWorkFlowLabels.LowerorHigherRiskEstimateWithoutEstimateSCOT
},
{
	validationId: validationTypes.PICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.PICEQRSignOffRequirements
},
{
	validationId: validationTypes.EstimatesMustBeMarkedHigherRisk,
	label: GuidedWorkFlowLabels.EstimatesMustBeMarkedHigherRisk
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.NonEngagementWideTasksMissingEvidence,
	label: GuidedWorkFlowLabels.NonEngagementWideTasksMissingEvidence
},
{
	validationId: validationTypes.AdjustmentsWithoutAnyEvidence,
	label: GuidedWorkFlowLabels.AdjustmentsWithoutAnyEvidence
},
{
	validationId: validationTypes.AdjustmentsThatDoNotNet,
	label: GuidedWorkFlowLabels.AdjustmentsThatDoNotNet
},
{
	validationId: validationTypes.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration,
	label: GuidedWorkFlowLabels.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration
},
{
	validationId: validationTypes.ITApplicationWithoutITAppRiskAssessmentIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppRiskAssessmentIndividualDocument
},
{
	validationId: validationTypes.SCOTEstimateNoRelatedWalkthroughForm,
	label: GuidedWorkFlowLabels.SCOTEstimateNoRelatedWalkthroughForm
},
{
	validationId: validationTypes.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2,
	label: GuidedWorkFlowLabels.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2
},
{
	validationId: validationTypes.AccountSignificantDisclosureWithNoRelatedSCOTV2,
	label: GuidedWorkFlowLabels.AccountSignificantDisclosureWithNoRelatedSCOTV2
},
{
	validationId: validationTypes.ITApplicationWithoutITAppPlanningIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppPlanningIndividualDocument
},
{
	validationId: validationTypes.RisksWithoutAnyRelatedAssertions,
	label: GuidedWorkFlowLabels.RisksWithoutAnyRelatedAssertions
},
{
	validationId: validationTypes.AssertionsWithIncompleteCRA,
	label: GuidedWorkFlowLabels.AssertionsWithIncompleteCRA
},
{
	validationId: validationTypes.LimitedRiskOrInsignificantAccountMissingRationale,
	label: GuidedWorkFlowLabels.LimitedRiskOrInsignificantAccountMissingRationale
},
{
	validationId: validationTypes.ITProcessWithoutWalkthroughDocument,
	label: GuidedWorkFlowLabels.ITProcessWithoutWalkthroughDocument
},
{
	validationId: validationTypes.ITProcessIsUncategorized,
	label: GuidedWorkFlowLabels.ITProcessIsUncategorized
},
{
	validationId: validationTypes.ITProcessWithNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessWithNoRelatedITApplication
}

];

// Label overrides (redefine here labels / objects that apply for a different part of the application)
export const resourceOverrides = {
	['Ares']: {
		labels: {
			notAROMM: 'לא מהווה סיכון להצגה מוטעית מהותית',
			fraudRisk: 'סיכון תרמית',
			significantRisk: 'סיכון משמעותי (Significant Risk)',
			identifiedRiskFactors: 'אירוע/תנאי מזוהה, סיכון להצגה מוטעית מהותית, סיכונים משמעותיים וסיכוני הונאה',
			countUnassociatedRisk: "אירועים/תנאים אינם קשורים/לא מסומנים כ-'לא מהווה סיכון להצגה מוטעית מהותית'."
		},
		riskTypes: [{
			id: 1,
			name: 'סיכון משמעותי (Significant Risk)',
			abbrev: 'S',
			label: 'משמעותי',
			title: 'סיכון משמעותי (Significant Risk)'
		},
		{
			id: 2,
			name: 'סיכון תרמית',
			abbrev: 'F',
			label: 'הונאה',
			title: 'סיכון תרמית'
		},
		{
			id: 3,
			name: 'סיכון להצגה מוטעית מהותית',
			abbrev: 'R',
			label: 'סיכון להצגה מוטעית מהותית',
			title: 'סיכון להצגה מוטעית מהותית'
		}
		]
	}
};

export const jeSourceTypes = [{
	value: 1,
	label: 'המערכת נוצרה'
},
{
	value: 2,
	label: 'תדריך'
},
{
	value: 3,
	label: 'שני'
}
];

export const hasJournalEntriesOption = [{
	value: 1,
	label: 'כן'
},
{
	value: 2,
	label: 'לא'
}
];

export const filterReviewNoteStatus = [{
	value: 1,
	label: 'פתח'
},
{
	value: 2,
	label: 'Cleared'
},
{
	value: 3,
	label: 'סגור'
}
];

export const EntitiesLabels = {
	close: 'סגור',
	cancel: 'בטל',
	repNoRecordMessage: 'לא נמצאו תוצאות',
	edit: 'ערוך',
	delete: 'מחק',
	actions: 'פעולות',
	show: '‏הצג',
	first: 'ראשון',
	last: 'אחרון',
	prev: 'הדף הקודם',
	next: 'הדף הבא',
	search: 'חפש',
	primary: 'Primary',
	knowledgeRiskLabel: 'Risks from knowledge cannot be edited or deleted',


	[Entity.Account]: {
		manageEntity: 'נהל סעיפים וגילויים',
		searchEntity: 'חפש סעיפים',
		createEntity: 'סעיף חדש',
		entityName: 'סעיף',
		entityNameCaps: 'סעיף',
		entityNamePlural: 'סעיפים',
		placeholderText: 'צור סעיפיםחדשים וגילויים או ערוך ומחק סעיפים קיימים וגילויים להלן.',
		deleteConfirmLabel: 'האם אתה בטוח שברצונך למחוק חשבון זה? כל הקשרים הקיימים יוסרו. לא ניתן לבטל פעולה זו.'
	},
	[Entity.Estimate]: {
		manageEntity: 'נהל אומדנים',
		searchEntity: 'הערכות חיפוש',
		createEntity: 'אומדן חדש',
		entityName: 'אומדן',
		entityNameCaps: 'אומדן',
		entityNamePlural: 'אומדנים',
		placeholderText: 'צור אומדנים חדשים או ערוך ומחק אומדנים קיימים להלן.',
		deleteConfirmLabel: 'האם אתה בטוח שברצונך למחוק אומדן זה? כל הקשרים הקיימים יוסרו. לא ניתן לבטל פעולה זו.'
	},
	[Entity.Risk]: {
		manageEntity: 'ניהול סיכונים',
		searchEntity: 'חפש סיכון',
		createEntity: 'סיכון חדש',
		entityName: 'סיכון',
		entityNameCaps: 'סיכון',
		entityNamePlural: 'סיכונים',
		placeholderText: 'צור סיכונים חדשים או ערוך ומחק סיכונים קיימים להלן.',
		deleteConfirmLabel: 'האם אתה בטוח שברצונך למחוק סיכון זה? כל הקשרים הקיימים יוסרו. לא ניתן לבטל פעולה זו.'
	},
	[Entity.STEntity]: {
		manageEntity: 'ניהול ישויות',
		searchEntity: 'חפש ישויות',
		createEntity: 'ישות חדשה',
		entityName: 'ישות',
		entityNameCaps: 'ישות',
		entityNamePlural: 'ישויות',
		placeholderText: 'צור ישויות חדשות או ערוך ומחק ישויות קיימות להלן.',
		deleteEntity: 'מחק ישות',
		deleteConfirmLabel: 'האם אתה בטוח שברצונך למחוק ישות זו? כל הקשרים הקיימים יוסרו. לא ניתן לבטל פעולה זו.'
	},
	[Entity.Control]: {
		manageEntity: 'ניהול בקרה',
		searchEntity: 'חפש בקרה',
		createEntity: 'בקרה חדשה',
		entityName: 'בקרה',
		entityNameCaps: 'בקרה',
		entityNamePlural: 'בקרות',
		placeholderText: 'צור בקרות חדשות או ערוך ומחק בקרות קיימות להלן.',
		deleteEntity: 'מחק בקרה',
		deleteConfirmLabel: 'האם אתה בטוח שברצונך למחוק בקרה זו? כל הקשרים הקיימים יוסרו. לא ניתן לבטל פעולה זו.'
	},
	[Entity.ITProcess]: {
		manageEntity: 'נהל תהליכי IT',
		searchEntity: 'חיפוש בתהליך ה-IT',
		createEntity: 'תהליך IT חדש',
		entityName: 'תהליך ה-IT',
		entityNameCaps: 'תהליכי IT',
		entityNamePlural: 'תהליכי IT',
		placeholderText: "צור תהליכי IT חדשים או ערוך ומחק תהליכי IT קיימים להלן.",
		deleteEntity: 'מחק תהליך IT',
		deleteConfirmLabel: 'האם אתה בטוח שברצונך למחוק תהליך IT זה? כל הקשרים הקיימים יוסרו. לא ניתן לבטל פעולה זו.'
	},
	[Entity.ITRisk]: {
		manageEntity: 'ניהול סיכונים טכנולוגיים',
		searchEntity: 'חיפוש סיכונים טכנולוגיים',
		createEntity: 'סיכון טכנולוגי חדש',
		entityName: 'Technology Risk',
		entityNameCaps: 'סיכונים טכנולוגיים',
		entityNamePlural: 'סיכונים טכנולוגיים',
		placeholderText: 'צור סיכונים טכנולוגיים חדשים או ערוך ומחק סיכונים טכנולוגיים קיימים להלן.',
		deleteEntity: 'מחיקת סיכון טכנולוגי',
		deleteConfirmLabel: 'האם אתה בטוח שברצונך למחוק את הסיכון הטכנולוגי הזה? כל הקשרים הקיימים יוסרו. לא ניתן לבטל פעולה זו.'
	},
	[Entity.ITControl]: {
		ITGC: {
			manageEntity: 'נהל ITGCs',
			searchEntity: 'חפש ITGCs',
			createEntity: 'ITGC חדש',
			editEntity: 'ערוך ITGC',
			viewEntity: 'הצג ITGC',
			entityName: 'בקרות כלליות במערכות המידע (ITGC)',
			entityNameCaps: 'בקרות כלליות במערכות המידע (ITGC)',
			entityNamePlural: 'ITGCs',
			placeholderText: 'צור ITGC חדש או ערוך ומחק ITGCs קיימים להלן.',
			deleteEntity: 'מחק ITGC',
			close: 'סגור',
			cancel: 'בטל',
			processIdRequired: 'נדרש תהליך IT',
			save: 'שמור',
			confirm: 'אשר',
			iTProcesslabel: 'תהליך ה-IT',
			saveAndCloseLabel: 'שמור וסגור',
			saveAndCreateLabel: 'שמור וצור אחד נוסף',
			deleteConfirmLabel: 'האם אתה בטוח שברצונך למחוק ITGC זה? כל הקשרים הקיימים יוסרו. לא ניתן לבטל פעולה זו.',
			operationEffectiveness: 'אפקטיביות תפעולית (Operating effectiveness)',
			itDesignEffectivenessHeader: 'אפקטיביות תכנונית (Design effectiveness)',
			itTestingColumnHeader: 'בודק',
			testingTitle: 'בודק',
			frequency: 'תדירות',
			controlOpertaingEffectiveness: 'אפקטיביות תפעולית (Operating effectiveness)',
			designEffectiveness: 'אפקטיביות תכנונית (Design effectiveness)',
			frequencyITGC: 'בחר תדירות',
			nameITGC: 'שם ITGC (חובה)',
			itspNameRequired: 'שם ITSP (חובה)',
			noResultsFound: 'לא נמצאו תוצאות',
			selectITRisk: 'בחר סיכון טכנולוגי (נדרש)',
			itRiskRequired: 'סיכון טכנולוגי (נדרש)',
			itRiskName: 'Technology Risk',
			inputInvaildCharacters: 'קלט אינו יכול לכלול את מחרוזת התווים הבאה: */:<>\\?|"',
			itControlNameRequired: 'שם ITGC נדרש',
			itgcTaskDescription: 'לבצע את הבדיקות המתוכננות שלנו של ITGCs כדי לקבל ראיות ביקורת מתאימות מספיקות לגבי האפקטיביות התפעולית לאורך כל תקופת ההסתמכות.',
			selectITProcess: 'בחר תהליך IT (חובה)',
			itProcessRequired: 'תהליך IT (חובה)',
			riskNameRequired: 'נדרש סיכון טכנולוגי',
			addModalDescription: 'הזן את תיאור ה-ITGC.',
			editModalDescription: 'ערוך את ה-ITGC ואת המאפיינים המשויכים אליו.',
			controlDesignEffectiveness: {
				[0]: {
					description: 'לא נבחר'
				},
				[1]: {
					description: 'יעיל'
				},
				[2]: {
					description: 'לא יעיל'
				}
			},
			controlTesting: {
				[0]: {
					description: 'לא נבחר'
				},
				[1]: {
					description: 'כן'
				},
				[2]: {
					description: 'לא'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: 'לא נבחר'
				},
				[1]: {
					description: 'יעיל'
				},
				[2]: {
					description: 'לא יעיל'
				}
			}
		},
		ITSP: {
			manageEntity: 'נהל ITSP',
			searchEntity: 'חפש ITSPs',
			createEntity: 'ITSP חדש',
			editEntity: 'ערוך ITSP',
			viewEntity: 'צפה ב-ITSP',
			inputInvaildCharacters: 'קלט אינו יכול לכלול את מחרוזת התווים הבאה: */:<>\\?|"',
			addModalDescription: 'הזן את תיאור ה-ITSP.',
			editModalDescription: 'ערוך את ה-ITSP ואת המאפיינים המשויכים אליו.',
			entityName: 'נוהל מבסס ל- IT  (IT-substantive testing procedures (ITSP))',
			selectITProcess: 'בחר תהליך IT (חובה)',
			entityNameCaps: 'נוהל מבסס ל- IT  (IT-substantive testing procedures (ITSP))',
			processIdRequired: 'נדרש תהליך IT',
			entityNamePlural: 'ITSPs',
			itspRequired: 'שם ITSP נדרש',
			close: 'סגור',
			cancel: 'בטל',
			iTProcesslabel: 'תהליך ה-IT',
			save: 'שמור',
			confirm: 'אשר',
			saveAndCloseLabel: 'שמור וסגור',
			riskNameRequired: 'נדרש סיכון טכנולוגי',
			saveAndCreateLabel: 'שמור וצור אחד נוסף',
			placeholderText: 'צור ITSP חדש או ערוך ומחק ITSPs קיימים להלן.',
			deleteEntity: 'מחק ITGC',
			deleteConfirmLabel: 'האם אתה בטוח שברצונך למחוק ITSP זה? כל הקשרים הקיימים יוסרו. לא ניתן לבטל פעולה זו.',
			itspTaskDescription: 'התאם אישית את תיאור המשימה כדי לעצב את האופי, העיתוי וההיקף של נהלי ה-IT המבססים בכדי להשיג ראיות ביקורת מתאימות ונאותות לכך שהסיכונים הטכנולוגיים מקבלים מענה ביעילות לאורך כל תקופת ההסתמכות.<br />כאשר הנוהל המבסס של ה-IT מבוצע החל מתאריך ביניים, תכנן ובצע נהלים כדי להשיג ראיות נוספות לכך שהסיכונים הטכנולוגיים מקבלים מענה לתקופה המכוסה על ידי נהלי הביניים שלנו עד סוף התקופה.<br />We מסיקים מסקנה על תוצאות נהלי ה-IT המבססים שלנו.',
			operationEffectiveness: 'אפקטיביות תפעולית (Operating effectiveness)',
			itDesignEffectivenessHeader: 'אפקטיביות תכנונית (Design effectiveness)',
			itTestingColumnHeader: 'בודק',
			testingTitle: 'בודק',
			frequency: 'תדירות',
			controlOpertaingEffectiveness: 'אפקטיביות תפעולית (Operating effectiveness)',
			designEffectiveness: 'אפקטיביות תכנונית (Design effectiveness)',
			frequencyITGC: 'בחר תדירות',
			nameITGC: 'שם ITGC (חובה)',
			itspNameRequired: 'שם ITSP (חובה)',
			noResultsFound: 'לא נמצאו תוצאות',
			selectITRisk: 'בחר סיכון טכנולוגי (נדרש)',
			itRiskRequired: 'סיכון טכנולוגי (נדרש)',
			itRiskName: 'Technology Risk',
			itProcessRequired: 'תהליך IT (חובה)',
			controlDesignEffectiveness: {
				[0]: {
					description: 'לא נבחר'
				},
				[1]: {
					description: 'יעיל'
				},
				[2]: {
					description: 'לא יעיל'
				}
			},
			controlTesting: {
				[0]: {
					description: 'לא נבחר'
				},
				[1]: {
					description: 'כן'
				},
				[2]: {
					description: 'לא'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: 'לא נבחר'
				},
				[1]: {
					description: 'יעיל'
				},
				[2]: {
					description: 'לא יעיל'
				}
			},
		}
	},
	[Entity.ITSOApplication]: {
		manageEntity: 'ניהול יישום IT',
		searchEntity: 'חיפוש יישום IT',
		createEntity: 'יישום IT חדש',
		entityName: 'יישום IT',
		entityNameCaps: 'יישומי IT',
		entityNamePlural: 'יישומי IT',
		placeholderText: 'צור יישומי IT חדשים או ערוך ומחק יישומי IT קיימים להלן.',
		deleteEntity: 'מחיקת יישום IT',
		deleteConfirmLabel: 'האם אתה בטוח שברצונך למחוק יישום IT זה? כל הקשרים הקיימים יוסרו. לא ניתן לבטל פעולה זו.'
	},
	[Entity.SCOT]: {
		manageEntity: 'נהל תתי תהליכים מהותיים (SCOT)',
		searchEntity: 'חפש תת תהליך מהותי SCOTs',
		createEntity: 'תת תהליך מהותי (SCOT) חדש',
		entityName: 'תת התהליך המהותי (SCOT)',
		entityNameCaps: 'SCOTs',
		entityNamePlural: 'SCOTs',
		placeholderText: 'צור תתי תהליכים מהותיים SCOTs חדשים או ערוך ומחק SCOTs קיימים להלן.',
		deleteEntity: 'מחק את תת התהליך המהותי (SCOT)',
		deleteConfirmLabel: 'האם אתה בטוח שברצונך למחוק תת תהליך מהותי (SCOT) זה? כל הקשרים הקיימים יוסרו. לא ניתן לבטל פעולה זו.'
	},
	[Entity.SampleItem]: {
		manageEntity: 'Manage sample tags',
		searchEntity: 'תגי חיפוש',
		createEntity: 'תג חדש',
		createManageTagEntity: 'נהל קבוצות תגים',
		entityName: 'תג לדוגמא',
		entityNamePlural: 'תגים',
		entityNameForTagGroupPlural: 'קבוצת תגים',
		placeholderText: "צור תגים חדשים או ערוך ומחק תגים קיימים למטה. אם אתה צריך ליצור קבוצות תגים חדשות, לחץ על <b>'נהל קבוצות תגים'</b>",
		deleteEntity: 'מחק תג לדוגמה',
		deleteConfirmLabel: 'האם אתה בטוח שברצונך למחוק את התג שנבחר? זה יוסר מכל הדוגמאות שהוא קשור אליהן. לא ניתן לבטל פעולה זו.'
	},
	[Entity.SampleTagGroups]: {
		manageEntity: 'נהל קבוצות תגים לדוגמה',
		searchEntity: 'Search tag groups',
		createEntity: 'קבוצת תגים חדשה',
		entityName: 'sample tag group',
		entityNameCaps: 'קבוצת תגים',
		entityNamePlural: 'קבוצות תגים',
		placeholderText: 'צור קבוצות תגים חדשות או ערוך ומחק קבוצות תגים קיימות לדוגמה להלן.',
		deleteConfirmLabel: 'האם אתה בטוח שברצונך למחוק את התג שנבחר? זה יוסר מכל הדוגמאות שהוא קשור אליהן. לא ניתן לבטל פעולה זו.'
	},
};

export const inherentRiskFactorTypes = [{
	id: 1,
	label: 'מורכבות',
	displayOrder: 1
},
{
	id: 2,
	label: 'סובייקטיביות אי-וודאות',
	displayOrder: 2
},
{
	id: 3,
	label: 'הונאה או טעות',
	displayOrder: 3
},
{
	id: 4,
	label: 'שינוי',
	displayOrder: 4
},
{
	id: 5,
	label: 'אופי החשבון',
	displayOrder: 5
},
{
	id: 6,
	label: 'צדדים קשורים',
	displayOrder: 6
}
];

export const executionType = [{
	id: 1,
	label: 'PT',
	toolTip: 'נהלים בהתקשרות זו [PT בלבד]',
	value: "בהתקשרות זו [PT בלבד]'"
},
{
	id: 2,
	label: 'CT',
	toolTip: 'נהלים בהתקשרות(יות) אחרת(ות) [CT בלבד]',
	value: 'בהתקשרות/יות אחרת/ות [CT בלבד]'
},
{
	id: 3,
	label: 'PT/CT',
	toolTip: 'נהלים בהתקשרות זו  בפרויקט זה ובהתקשרות(יות) אחרת(ות) [PT/CT]',
	value: 'בהתקשרות זו ובהתקשרות/יות אחרת/ות [PT/CT]'
}
];

export const createEditAccountModalLabels = {
	createModalDescription: "הזן את פרטי החשבון החדשים למטה ובחר'<b>{0}</b>' כדי לסיים. כדי ליצור חשבון נוסף, בחר'<b>{1}</b>'.",
	editModalDescription: "ערוך את פרטי הסעיף להלן ובחר'<b>{0}</b>' כדי לסיים.",
	close: 'סגור',
	cancel: 'בטל',
	createAccount: 'סעיף חדש',
	editAccount: 'ערוך סעיף',
	newSignificantDisclosure: 'גילוי משמעותי חדש',
	save: 'שמור',
	confirm: 'אשר',
	saveAndCloseLabel: 'שמור וסגור',
	saveAndCreateLabel: 'שמור וצור אחד נוסף',
	accountNameLabel: 'שם סעיף (חובה)',
	accountDesignationLabel: 'ייעוד',
	accountExecutionTypeLabel: 'באיזו התקשרות Canvas יבוצעו ויתועדו נהלים עבור חשבון זה?',
	accountEstimateLabel: 'האם סעיף זה מושפע מאומדן?',
	yes: 'כן',
	no: 'לא',
	accountStatementTypeLabel: 'סוג דוח',
	pspIndexDropdownLabel: 'אינדקס PSP (נדרש, בחר עד 5)',
	removePSPIndexLabel: 'הסר אינדקס PSP',
	assertionsLabel: 'בחר מצגי הנהלה רלוונטיים',
	accountTypeOptions: AccountType,
	assertionOptions: assertions,
	executionTypeOptions: executionType,
	statementTypeOptions: statementTypes,
	noOptionsMessage: 'לא נמצאו תוצאות',
	accountNameErrorMsg: 'נדרש',
	pspIndexErrorMsg: 'נדרש',
	assertionWarningMessage: 'לא ניתן לבצע שינויים במצגי ההנהלה שמקושרים אליהם סיכון משמעותי (SR), או סיכון הונאה (FR), או סיכון להצגה מוטעית מהותית (ROMM) או אומדן קשור. אתה צריך להסיר את היחסים האלה קודם.',
	confirmChanges: 'אשר שינויים',
	executionTypeWarningMessage: 'השינויים שאתה עומד לשמור בחשבון זה ישפיעו על מצגי ההנהלה הקיימים ועל נהלי הביקורת המבססים העיקריים (ה-PSP) הקישורים יבוטלו. האם אתה בטוח שאתה רוצה להמשיך? לא ניתן לבטל פעולה זו.',
	contentUpdateToastMessage: 'עדכון תוכן זמין עבור {0}. אנא התחל את עדכון התוכן מדף עדכון התוכן.',
	assertionsRequired: 'יש לבחור לפחות מצד הנהלה אחד',
	pspIndexDisabledLabel: 'בחר עד חמישה אינדקסים של PSP. בטל את הבחירה באפשרות אחת או יותר כדי להמשיך.'
};

export const createEditRiskModalLabels = {
	createModalDescription: "הזן את פרטי הסיכון החדשים למטה ובחר'<b>{0}</b>' כדי לסיים. כדי ליצור סיכון נוסף, בחר'<b>{1}</b>'.",
	editModalDescription: "ערוך את פרטי הסיכון למטה ובחר'<b>{0}</b>' כדי לסיים.",
	close: 'סגור',
	cancel: 'בטל',
	createRisk: 'סיכון חדש',
	editRisk: 'ערוך סיכון',
	riskType: 'סוג סיכון (חובה)',
	riskTypeOptions: [{
		value: 1,
		label: 'סיכון משמעותי (Significant Risk)'
	},
	{
		value: 2,
		label: 'סיכון תרמית'
	},
	{
		value: 3,
		label: 'סיכון להצגה מוטעית מהותית'
	}
	],
	save: 'שמור',
	saveAndCloseLabel: 'שמור וסגור',
	saveAndCreateLabel: 'שמור וצור אחד נוסף',
	riskNameLabel: 'שם סיכון (חובה)',
	relatedAccountsAssertionsLabel: 'סעיפים ומצגי הנהלה (assertions) קשורים (אופציונלי)',
	relateAccounts: 'חשבונות קשורים',
	assertionsLabel: 'בחר מצגי הנהלה רלוונטיים',
	riskNameErrorMsg: 'נדרש',
	riskTypeErrorMsg: 'נדרש',
	assertionOptions: assertions,
	removeAccountLabel: 'הסר סעיף',
	required: 'נדרש',
	assertionsRequired: 'יש לבחור לפחות מצד הנהלה אחד'
};

export const CreateEditMestLabels = {
	createModalTitle: 'ישות חדשה',
	createModalDescription: "הזן את פרטי הישות החדשה למטה ובחר'<b>{0}</b>' כדי לסיים. כדי ליצור ישות נוספת, בחר'<b>{1}</b>'.",
	close: 'סגור',
	cancel: 'בטל',
	save: 'שמור',
	confirm: 'Confirm',
	primary: 'Primary',
	saveAndCloseLabel: 'שמור וסגור',
	saveAndCreateLabel: 'שמור וצור אחד נוסף',
	entityNameLabel: 'שם ישות (חובה)',
	entityStandardIndexLabel: 'אינדקס סטנדרטי של ישות (חובה)',
	entityDescriptionLabel: 'תיאור ישות',
	entityNameErrorMsg: 'נדרש',
	entityStandardIndexErrorMsg: 'נדרש',
	editModalTitle: 'ערוך ישות',
	editModalDescription: "ערוך את פרטי הישות למטה ובחר'<b>{0}</b>' כדי לסיים.",
	primaryEntitySelectionLabel: 'Select as the primary entity',
	primaryEntitySelectionMsg: "Only one entity in the engagement can be selected as the primary entity, which will be the determinant for the content delivered to the engagement. An entity will need to be selected as the primary to be able to submit the engagement profile. \'Update content\' permission is required to make or edit the primary entity selection.",
	primaryEntityDisableSelectionLabel: "To change the primary entity designation, select from the \'Edit\' of the entity you wish to designate as primary",
	noAccessLabel: 'Unauthorized. Contact your administrator and try again.',
	primaryEntityConfirmationLabel: 'Primary entity confirmation',
	primaryEntityConfirmationDisplay: '{0} is currently selected as the primary entity. Are you sure you want to change the primary entity?',
	profileV2ChangeNotSubmittedBannerMessage: 'Changes have been made to the profile that will result in content updates. Submit the profile to receive the new content or revert the answers to the previous state.',
};

export const CreateEditITProcessLabels = {
	close: 'סגור',
	cancel: 'בטל',
	yes: 'כן',
	no: 'לא',
	delete: 'מחק',
	save: 'שמור',
	saveAndCloseLabel: 'שמור וסגור',
	saveAndCreateLabel: 'שמור וצור אחד נוסף',
	newITProcessLabel: 'תהליך IT חדש',
	editITProcessLabel: 'עריכת תהליך ה-IT',
	viewITProcessLabel: 'הצג את תהליך ה-IT',
	addDescriptionLabel: "הזן את פרטי תהליך ה-IT החדשים למטה ובחר'<b>{0}</b>' כדי לסיים. כדי ליצור תהליך IT נוסף, בחר'<b>{1}</b>'.",
	editDescriptionLabel: "ערוך את פרטי תהליך ה-IT למטה ובחר'<b>{0}</b>' כדי לסיים.",
	iTProcessNameLabel: 'שם תהליך ה-IT (חובה)',
	confirm: 'אשר',
	confirmChanges: 'אשר',
	iTProcessNameErrorMsg: 'נדרש',
	inputInvaildCharacters: 'קלט אינו יכול לכלול את מחרוזת התווים הבאה: */:<>\\?|"',
	remove: 'הסר'
};

export const CreateEditITRiskLabels = {
	close: 'סגור',
	cancel: 'בטל',
	yes: 'כן',
	no: 'לא',
	delete: 'מחק',
	save: 'שמור',
	saveAndCloseLabel: 'שמור וסגור',
	saveAndCreateLabel: 'שמור וצור אחר',
	newITRiskLabel: 'סיכון טכנולוגי חדש',
	editITRiskLabel: 'עריכת סיכון טכנולוגי',
	itRiskNameLabel: 'סיכון טכנולוגי (נדרש)',
	confirm: 'אשר',
	confirmChanges: 'אשר',
	itRiskNameErrorMsg: 'נדרש',
	itProcessNotSelectedErrorMsg: 'נדרש',
	hasNoITGCLabel: 'אין ITGCs  אשר נותנים מענה לסיכון הטכנולוגי',
	editModalDescription: 'ערוך את תיאור הסיכון הטכנולוגי.',
	createModalDescription: 'הזן את תיאור הסיכון הטכנולוגי.',
	selectITProcess: 'בחר תהליך IT (חובה)',
	noITProcessAvailable: 'לא נוצרו תהליכי IT',
	relatedITProcessLabel: 'תהליך IT קשור',
	inputInvaildCharacters: 'קלט אינו יכול לכלול את מחרוזת התווים הבאה: */:<>\\?|"',
	remove: 'הסר'
};

export const CreateEditEstimateLabels = {
	createModalDescription: "הזן את פרטי האומדן החדשים למטה ובחר'<b>{0}</b>' כדי לסיים. כדי ליצור אומדן נוסף, בחר'<b>{1}</b>'.",
	editModalDescription: "ערוך את פרטי האומדן למטה ובחר'<b>{0}</b>' כדי לסיים.",
	close: 'סגור',
	cancel: 'בטל',
	save: 'שמור',
	saveAndCloseLabel: 'שמור וסגור',
	saveAndCreateLabel: 'שמור וצור אחד נוסף',
	createModalTitle: 'אומדן חדש',
	editEstimateLabel: 'ערוך אומדן',
	estimateNameLabel: 'שם האומדן (חובה)',
	analysisPeriodBalance: 'יתרת תאריך ניתוח (חובה)',
	analysisPeriodDate: 'תאריך ניתוח (חובה)',
	comparativePeriodBalance: 'יתרת תאריך השוואתית (חובה)',
	comparativePeriodDate: 'תאריך השוואתי (חובה)',
	estimateCategory: 'קטגוריית אומדן (חובה)',
	confirm: 'אשר',
	estimateNameErrorMsg: 'נדרש',
	analysisPeriodBalanceErrorMsg: 'נדרש',
	analysisPeriodDateErrorMsg: 'נדרש',
	comparativePeriodBalanceErrorMsg: 'נדרש',
	comparativePeriodDateErrorMsg: 'נדרש',
	estimateCategoryErrorMsg: 'נדרש',
	remove: 'הסר',
	balanceNOTApplicable: 'יתרות אינן ישימות',
	wtDetailPrefixForEstimate: '<p>Complete the estimate walkthrough task.</p>',

	riskLevelOptions: [{
		value: 4,
		label: 'סיכון נמוך מאוד'
	},
	{
		value: 5,
		label: 'בסיכון נמוך'
	},
	{
		value: 6,
		label: 'סיכון גבוה יותר'
	},
	{
		value: 7,
		label: 'לא נבחר'
	}
	]
};

export const CreateEditControlLabels = {
	createModalTitle: 'בקרה חדשה',
	editModalTitle: 'ערוך בקרה',
	viewModalTitle: 'הצג בקרה',
	close: 'Close',
	cancel: 'בטל',
	save: 'שמור',
	saveAndCloseLabel: 'שמור וסגור',
	saveAndCreateLabel: 'שמור וצור אחד נוסף',
	controlNameLabel: 'שם בקרה (חובה)',
	frequency: 'תדירות',
	controlType: 'סוג בקרה',
	frequencyTypeOptions: controlFrequencyType,
	controlTypeOptions: controlTypes,
	designEffectiveness: 'יעילות עיצוב',
	operatingEffectiveness: 'אפקטיביות תפעולית',
	testingLabel: 'בודק',
	lowerRiskLabel: 'Is control lower risk?',
	effective: 'יָעִיל',
	ineffective: 'לֹא יָעִיל',
	yes: 'כן',
	no: 'לא',
	required: 'נדרש',
	remove: 'הסר',
	noOptionsMessage: 'לא נמצאו תוצאות',
	disabledTabTooltipMessage: "בחר'סוג בקרה' בתור'בקרת יישומי IT' או'בקרה ידנית תלוית IT' בכדי לקשר יישומי IT",
	itAppLabels: {
		tabLabel: 'אפליקציות IT',
		dropdownLabel: 'קשור יישומי IT',
		noRelatedItems: 'אין יישומי IT קשורים',
		itApplications: 'יישומי IT'
	},
	soLabels: {
		tabLabel: 'לשכות שירות Sos',
		dropdownLabel: 'קשר לשכות שירות SO',
		noRelatedItems: 'אין לשכות שירות SO קשורות',
		serviceOrganizations: 'ארגוני שירות'
	},
	controlNameErrorMsg: 'נדרש',
	createModalDescriptionLabel: "הזן את פרטי הבקרה החדשים למטה ובחר'<b>{0}</b>' כדי לסיים. כדי ליצור בקרה נוספת, בחר'<b>{1}</b>'.",
	editModalDescriptionLabel: "ערוך את פרטי הבקרה להלן ובחר'<b>{0}</b>' כדי לסיים.",
	viewModalDescriptionLabel: 'הצג את הבקרה ו יישומי ה-IT ולשכות השירות הרלוונטיים.',
	wcgwLabel: 'WCGW'
};

export const ITApplicationTypeLabels = [{
	value: 1,
	label: 'יישום/כלי'
},
{
	value: 2,
	label: 'מסד נתונים'
},
{
	value: 3,
	label: 'מערכת הפעלה'
},
{
	value: 4,
	label: 'רשת'
},
{
	value: 6,
	label: 'ללא קטגוריה'
}
];

export const CreateEditITApplicationLabels = {
	close: 'סגור',
	cancel: 'בטל',
	yes: 'כן',
	no: 'לא',
	delete: 'מחק',
	save: 'שמור',
	saveAndCloseLabel: 'שמור וסגור',
	saveAndCreateLabel: 'שמור וצור אחד נוסף',
	newITApplicationLabel: 'יישום IT חדש',
	editITApplicationLabel: 'ערוך יישום IT',
	iTApplicationNameLabel: 'שם יישום IT',
	confirm: 'אשר',
	confirmChanges: 'אשר שינויים',
	noOptionsMessage: 'לא נמצאו תוצאות',
	iTAppNameErrorMsg: 'נדרש',
	controls: 'בקרות',
	substantive: 'ביקורת מבססת',
	remove: 'הסר',
	iTApplicationStrategyLabel: 'אסטרטגיית יישום IT',
	SCOTsLabel: 'שמות תתי תהליכים מהותיים SCOT',
	StrategyLabel: 'אסטרטגיית תת התהליך המהותי (SCOT)',
	ControlsLabel: 'בקרות',
	ControlTypeLabel: 'סוג',
	addDescriptionLabel: "הזן את פרטי יישום ה-IT החדש למטה ובחר'<b>{0}</b>' כדי לסיים. כדי ליצור יישום IT נוסף, בחר'<b>{1}</b>'.",
	editDescriptionLabel: "ערוך את פרטי יישום ה-IT למטה ובחר'<b>{0}</b>' כדי לסיים.",
	scotErrorMessage: 'תת תהליך מהותי SCOT עשוי להיות קשור ליישום ה-IT, מכיוון שקיימות בקרות שקושרו.',
	SCOTsLabels: {
		tabLabel: 'SCOTs',
		dropdownLabel: 'קשר תתי תהליכים מהותיים SCOTs',
		noRelatedItems: 'אין תתי תהליכים מהותיים (SCOTs) קשורים'
	},
	ControlsLabels: {
		tabLabel: 'בקרות',
		dropdownLabel: 'קשר בקרות',
		noRelatedItems: 'אין בקרות קשורות'
	},
	strategyType: {
		1: 'בקרות',
		2: 'ביקורת מבססת',
		3: 'הסתמכות על בקרות',
		4: 'לא להסתמך'
	},
	controlType: {
		1: 'אפליקציית IT',
		2: 'בקרה ידנית תלויית מחשב (ITDM)',
		3: 'מונעת ידנית',
		4: 'בקרה ידנית מגלה'
	},
	technologyTypeOptions: ITApplicationTypeLabels,
	technologyType: 'Select technology type'
};

export const CreateEditSCOTLabels = {
	createModalTitle: 'SCOT חדש',
	editModalTitle: 'ערוך תת תהליך מהותי SCOT',
	viewModalTitle: 'הצג תת תהליך מהותי SCOT',
	createModalDescription: "הזן את פרטי ה-SCOT החדשים להלן ובחר'<b>{0}</b>' כדי לסיים. כדי ליצור SCOT נוסף, בחר'<b>{1}</b>'.",
	editModalDescription: "ערוך את פרטי ה-SCOT להלן ובחר'<b>{0}</b>' כדי לסיים.",
	close: 'סגור',
	cancel: 'בטל',
	save: 'שמור',
	saveAndCloseLabel: 'שמור וסגור',
	saveAndCreateLabel: 'שמור וצור אחד נוסף',
	scotNameLabel: 'שם תת התהליך המהותי SCOT (חובה)',
	scotStrategyLabel: 'Scot Strategy',
	scotTypeLabel: 'סוג תת התהליך המהותי  (SCOT)',
	hasEstimateLabel: 'האם תת תהליך מהותי SCOT זה מושפע מאומדן?',
	itAPPUsedLabel: 'האם נעשה שימוש ביישומי IT?',
	routine: 'Routine',
	nonRoutine: 'לא רוטיני',
	controls: 'בקרות',
	substantive: 'ביקורת מבססת',
	yes: 'כן',
	scotNameErrorMsg: 'נדרש',
	remove: 'הסר',
	noOptionsMessage: 'לא נמצאו תוצאות',
	disabledTabTooltipMessage: "בחר'האם נעשה שימוש ביישומי IT כלשהם?' בכדי לקשר יישומי IT",
	itAppLabels: {
		itApplications: 'יישומי IT קשורים',
		tabLabel: 'אפליקציות IT',
		dropdownLabel: 'קשר יישומי IT',
		noRelatedItems: 'אין יישומי IT קשורים'
	},
	soLabels: {
		serviceOrganizations: 'לשכות שירות קשורות',
		tabLabel: 'לשכות שירות Sos',
		dropdownLabel: 'קשר לשכות שירות SO',
		noRelatedItems: 'אין לשכות שירות SO קשורות'
	},
	wtDetailPrefix: "הזן שם פרויקט ובחר התקשרות ראשית/אזורית אחת או איותר של ה- EY Canvas מהרשימה למטה בכדי ליצור פרויקט EY Oversight. בהתבסס על ההתקשרות (ויות)שנבחרו, ההתקשרות (ויות) של ה- EY Canvas שזוהו במבנה EY Canvas Group ימולאו אוטומטית כחלק מההגדרה של פרויקט זה. לחץ על <a target=_blank href='{link}'><b>User Guide</b></a>  למידע נוסף לגבי EY Canvas Oversight.",
};

export const ViewSampleItemLabels = {
	previous: 'הקודם',
	next: 'הבא',
	sampleDateLabel: 'תאריך המדגם',
	attributesHeader: 'מאפיינים',
	statusHeader: 'סטטוס',
	noAttributesLabel: 'אין מאפיינים זמינים.',
	present: 'קיים',
	presentWithComments: 'הצג עם הערות',
	notPresent: 'לא נוכח',
	notApplicatable: 'לא ישים',
	naLabel: 'לא רלוונטי',
	additionDocumentation: 'תיעוד נוסף',
	deleteSampleHeader: 'מחק מדגם',
	deleteSmapleDescription: 'האם אתה בטוח שברצונך למחוק את המדגם שנבחר? לא ניתן לבטל פעולה זו.',
	deleteSamplePreText: 'תיאור המדגם',
	relateTagModalTitle: 'קשר תגים למדגם',
	relateTagModalDescription: "קשר תג אחד או יותר למדגם. כדי להוסיף תג חדש, לחץ על <b>'נהל תגים'</b>. אסוציאציות של תגים לא יישמרו בארכיב, אבל התגים עצמם יאוחסנו בארכיב כך שהם זמינים לשימוש ב-rollforward.",
	relateTagTableHeader: 'שם תג',
	relateTagTableSubHeader: 'קבוצת תגים',
	tagsCounter: '{0} תגים',
	tagCounter: '{0} תג',
	relateTagGroupLabel: 'קבוצת תגים',
	relateTagSearchPlaceholder: 'חפש',
	relateTagClearSearch: 'Clear',
	relateTagShowSelectedOnly: 'הצג רק כאלו שקשורים',
	manageTagsLabel: 'נהל תגים',
	addTag: 'הוסף תג',
	supportingDocumentationTitle: 'תיעוד תומך',
	dropdownAll: 'הכול',
	noResultsLabel: 'לא נמצאו תוצאות',
	noDataLabel: 'לא נמצאו נתונים',
	attributeStatusModalTitle: 'Mark all as present',
	attributeStatusModalCancelButton: 'בטל',
	attributeStatusModalConfirmButton: 'שמור',
	attributeStatusModalDescription: 'האם אתה בטוח שברצונך לסמן מאפיינים כקיימים? רק מאפיינים שלא סומן עבורם סטטוס יסומנו כשקיימים.',
	attributeModalDeleteErrorMessage: 'לא ניתן לעדכן את סטטוס המאפיין. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.',
};

export const ShortRiskTypeForAres = {
	1: 'משמעותי',
	2: 'הונאה',
	3: 'שבמהות (inherent)',
	4: 'סיכון נמוך מאוד',
	5: 'בסיכון נמוך',
	6: 'סיכון גבוה יותר',
	7: 'לא נבחר'
};

export const RelateEstimateToAssertionLabels = {
	relateAccountsAndAssertions: 'קשר סעיפים ומצגי הנהלה',
	relateAccountsToEstimate: 'קישור חשבונות לאומדן',
	accounts: 'סעיפים',
	designation: 'ייעוד',
	relatedAssertions: 'מצגי הנהלה מקושרים',
	accountNameField: 'AccountName',
	accountTypeIdField: 'accountTypeId',
	assertionsField: 'יעדי ביקורת',
	executionTypeIdField: 'executionTypeId',
	notSelected: 'לא נבחר',
	pathField: 'נתיב',
	noAccountsAvailable: 'אין חשבונות זמינים',
	noRelatedAccounts: 'אין חשבונות הקשורים לאומדן',
	accountId: 'מזהה חשבון',
	remove: 'הסר'
};

//Send instructions switcher
export const sendIntructionsSwitcherLabels = {

	[sendInstructionsSwitcherIds.groupInstructions]: 'הנחיות קבוצתיות',
	[sendInstructionsSwitcherIds.groupRiskAssessment]: 'הערכת סיכונים קבוצתית'
};

export const RelateEstimateToAccountLabels = {
	relatEstimatesToAccount: 'קשר אומדנים לחשבון',
	showOnlyRelatedEstimates: 'הצג רק אומדנים קשורים',
	noEstimatesResult: labels.noResultsFound,
	noEstimatesLabel: 'לא נוצרו אומדנים בהתקשרות',
	estimateNameHeader: 'שם האומדן',
	relatedEstimateCounter: 'אומדן {0}',
	relatedEstimatesCounter: '{0} אומדנים',
	relatedAccount: 'סעיף/גילוי',
	close: labels.close
};

export const RelateAccountsToEstimateLabels = {
	relateAccountsToEstimate: 'קשר חשבונות לאומדן',
	showOnlyRelatedAccounts: 'הצג רק חשבונות קשורים',
	noAccountsResult: labels.noResultsFound,
	noAccountsLabel: 'לא נוצרו חשבונות בהתקשרות',
	accountNameHeader: 'שם סעיף',
	relatedAccountCounter: 'חשבון {0}',
	relatedAccountsCounter: '{0} חשבונות',
	relatedEstimate: labels.estimate,
	close: labels.close
};

export const SupportingDocumentationLabels = {
	evidence: 'ראיות',
	priorPeriod: 'תקופה קודמת',
	temporaryFiles: 'קבצים זמניים',
	externalDocuments: 'מסמכים חיצוניים',
	addEvidenceBtn: 'הוסף ראיות',
	addTemporaryFilesBtn: 'הוסף קובץ זמני',
	notes: 'הערות',
	signOffs: 'Sign-offs',
	name: 'שם',
	supportingDocumentationTitle: 'תיעוד תומך',
	temporaryFilesEmptyPlaceholder1: 'אין מסמכים זמניים קשורים.',
	temporaryFilesEmptyPlaceholder2: 'כדי לקשר מסמך זמני, לחץ על {addTemporaryFiles}.',
	evidencePlaceholderLine1: 'אין ראיות קשורות.',
	evidencePlaceholderLine2: 'כדי לקשר ראיות, לחץ על {addEvidenceBtn}.',
	removeFromSample: 'הסר מהמדגם',
	unlink: 'בטל קישור',
	retailControlEvidenceLabel: 'האם שמרנו ראיות בקרה כדי לתמוך בבדיקה שלנו של המאפיינים של פריט ספציפי זה במדגם?',
	removeEvidenceModalTitle: 'הסר ראיות מהמדגם',
	removeEvidenceModalDesc: 'האם אתה בטוח שברצונך להסיר את כל הראיות ממדגם זה?',
	removeEvidenceErrorMessage: 'מסמכים אלה אינם זמינים עוד במדגם זה. רענן את הדף ונסה שוב. פנה לתמיכה הטכנית אם השגיאה נמשכת.'
}

export const deleteSampleItemAttributeModal = {
	modalDescription: 'האם אתה בטוח שברצונך להסיר את הבחירה עבור מאפיין זה?תיעוד נוסף יימחק.',
	modalTitle: 'הסר את הבחירה',
	modalConfirmButton: 'הסר',
	modalCancelButton: 'בטל',
	additionalDocumentationLabel: 'תיעוד נוסף'
}
export const accountsFilterLabels = [{
	id: accountsFilter.allAccounts,
	label: 'כל הסעיפים',
	value: accountsFilter.allAccounts
},
{
	id: accountsFilter.accountsWithRelatedEstimates,
	label: 'חשבונות אשר מקושר אליהם אומדן',
	value: accountsFilter.accountsWithRelatedEstimates
},
{
	id: accountsFilter.accountsWithoutRelatedEstimates,
	label: 'חשבונות אשר לא מקושר אליהם אומדן',
	value: accountsFilter.accountsWithoutRelatedEstimates
}
];

export const changeSampleItemAttributeModal = {
	modalDescription: 'האם אתה בטוח שברצונך לשנות את הבחירה עבור מאפיין זה? מסמכים נוספים יימחקו.',
	modalTitle: 'שנה בחירה',
	modalConfirmButton: 'שינוי',
	modalCancelButton: 'בטל'
}
export const scotsFilterLabels = [{
	id: scotsFilter.allScots,
	label: 'כל תתי התהליכים המהותיים (SCOTs)',
	value: scotsFilter.allScots
},
{
	id: scotsFilter.scotsWithRelatedEstimates,
	label: 'תתי תהליכים מהותיים (SCOTs) עם קישור לאומדנים',
	value: scotsFilter.scotsWithRelatedEstimates
},
{
	id: scotsFilter.scotsWithoutRelatedEstimates,
	label: 'תתי תהליכים מהותיים (SCOTs) ללא קישור לאומדנים',
	value: scotsFilter.scotsWithoutRelatedEstimates
}
];

export const CreateEditTagGroupLabels = {
	createModalTitle: 'קבוצת תגים חדשה לדוגמה',
	editModalTitle: 'עריכת קבוצת תגים לדוגמה',
	createModalDescription: "הזן את פרטי קבוצת התגים למטה ובחר <b>\'שמור וסגור\'</b> כדי לסיים. כדי ליצור קבוצת תגים אחרת, בחר <b>\'שמור וצור קבוצת תגים נוספת\'</b>.",
	editModalDescription: "ערוך את פרטי קבוצת התגים למטה ובחר'<b>{0}</b>' כדי לסיים.",
	close: 'סגור',
	cancel: 'בטל',
	save: 'שמור',
	saveAndCloseLabel: 'שמור וסגור',
	saveAndCreateLabel: 'שמור וצור אחד נוסף',
	tagGroupNameLabel: 'שם קבוצת תגים (חובה)',
	required: 'נדרש'
};

export const CreateEditTagLabels = {
	createModalTitle: 'תג חדש לדוגמא',
	editModalTitle: 'Edit sample tag',
	createModalDescription: "הזן את פרטי התג למטה ובחר'<b>שמור וסגור</b>' כדי לסיים. כדי ליצור תג נוסף, בחר'<b>שמור וצור אחר</b>'.",
	editModalDescription: `Edit the tag details below and select'<b>{0}</b>' to finish.`,
	tagNameLabel: 'שם תג (חובה)',
	tagGroupNameLabel: 'שם קבוצת תגים (חובה)',
	tagColorLabel: 'צבע (חובה)',
	saveAndCloseLabel: 'שמור וסגור',
	saveAndCreateLabel: 'שמור וצור אחד נוסף',
	cancelLabel: 'בטל',
	required: 'נדרש',
	save: 'שמור',
	noresultsLabel: 'אין קבוצות תגים זמינות',
	tagColors: [{
		text: 'אָדוֹם',
		color: 'אָדוֹם'
	},
	{
		text: 'כתום',
		color: 'כתום'
	},
	{
		text: 'ירוק כחלחל',
		color: 'ירוק כחלחל'
	},
	{
		text: 'כחול',
		color: 'כחול'
	},
	{
		text: 'סגול',
		color: 'סגול'
	},
	{
		text: 'ירוק',
		color: 'ירוק'
	}
	]
};

export const ITProcessFlowLabels = {
	itProcess: 'תהליך IT',
	technology: 'Technology',
	technologies: 'טכנולוגיות',
	technologyName: 'שם הטכנולוגיה',
	supportingTechnologyName: 'שם הטכנולוגיה התומכת',
	technologyType: 'סוג טכנולוגיה',
	showOnlyRelated: 'הצג רק כאלו שקשורים',
	technologiesCounter: '{0} טכנולוגיות',
	technologyCounter: 'טכנולוגיית {0}',
	supportingTechnologyLabel: 'טכנולוגיה תומכת',
	relatedITAppNoDataPlaceholder: 'אין טכנולוגיה הקשורה לתהליך ה-IT',
	relateTechnology: 'קשר טכנולוגיה',
	supportingITPAppNoDataPlaceholder: 'אין טכנולוגיה התומכת בתהליך ה-IT',
	itRiskHeader: 'סיכוני IT',
	itgcHeader: 'ITGCs',
	itspHeader: 'ITSPs',
	noDataPlaceholderITGC: 'יש לזהות לפחות ITGC אחד או לקבוע כי לסיכון ה-IT אין ITGC. צור {createNewITGC}, {relateITGC} או ציין שיש {noITGC}  אשר נותנים מענה לסיכון ה-IT.',
	noDataPlaceholderITSP: 'אם הערכנו ITGCs כלא אפקטיביים או קבענו שאין ITGCs קיימים אשר נותנים מענה לסיכון ה-IT, ייתכן שנוכל לבצע נהלי IT מבססים (ITSPs) בכדי שנוכל לקבל ביטחון סביר שסיכון ה-IT בתהליך ה-IT הקשור ל-ITGC הלא אפקטיבי לא התממש. צור {createNewITSP} או {relateITSP}.',
	noRecordsFound: 'לא זוהו סיכוני IT עבור תהליך IT זה',
	noITGCPlaceholder: 'אין ITGCs  אשר נותנים מענה לסיכון ה-IT.',
	relateSupportingTechnology: 'קשר טכנולוגיה תומכת',
	relatedTechnologiesNotAvailable: 'טכנולוגיות קשורות אינן זמינות עבור מסמך זה',
	supportingTechNotAvailable: 'טכנולוגיות תומכות שאינן זמינות עבור מסמך זה',
	relatedITRisksNotAvailable: 'סיכוני IT קשורים אינם זמינים עבור מסמך זה',
	relateITGC: 'Relate ITGCs',
	itRisk: 'סיכון IT',
	itgcCounter: '{0} ITGC',
	itgcsCounter: '{0} ITGCs',
	itgcName: 'שם ITGC',

};

export const ITRisksFlowLabels = {
	itRisk: 'סיכון IT',
	relatedITRiskNoDataPlaceholder: 'אין סיכוני IT הקשורים לתהליך ה-IT',
	newITRisk: 'סיכון IT חדש',
	relatedITRisksNotAvailable: 'סיכוני IT קשורים אינם זמינים עבור מסמך זה',
	deleteConfirmLabel: 'האם אתה בטוח שברצונך למחוק את סיכון ה- IT שנבחר? לא ניתן לבטל פעולה זו.',
	deleteITRisk: 'מחיקת סיכון IT',
	CreateITFlowITRiskLabels: {
		close: 'סגור',
		cancel: 'בטל,',
		yes: 'כן',
		no: 'לא,',
		delete: 'מחק,',
		save: 'שמור,',
		saveAndCloseLabel: 'שמור וסגור',
		saveAndCreateLabel: 'שמור וצור אחד נוסף',
		newITRiskLabel: 'סיכון IT חדש',
		itRiskNameLabel: 'שם סיכון IT (נדרש)',
		itRiskCharactersLabel: 'תווים',
		itRiskOfLabel: 'של',
		confirm: 'אשר',
		confirmChanges: 'אשר',
		itRiskNameErrorMsg: 'נדרש',
		itProcessNotSelectedErrorMsg: 'נדרש',
		hasNoITGCLabel: 'אין ITGCs אשר נותנים מענה לסיכון ה-IT',
		createModalDescription: "הזן את פרטי סיכון ה-IT שלהלן ובחר'<b>שמור וסגירה</b>' כדי לסיים. כדי ליצור סיכון IT נוסף, בחר'<b>שמור וצור אחר</b>'.",
		relatedITProcessLabel: 'תהליך IT',
		inputInvaildCharacters: 'הקלט אינו יכול לכלול את מחרוזת התווים הבאה: */:<>\\?|"',
		remove: 'Remove',
		editModalDescription: "ערוך את פרטי סיכון ה-IT להלן ובחר'<b>שמור</b>' כדי לסיים.,",
		editITRiskLabel: 'Edit IT risk'
	}
};

export const ITProcessListingLabels = {
	all: 'הכול,',
	manageChange: 'נהל שינוי',
	manageAccess: 'ניהול גישה',
	manageSecuritySettings: 'ניהול הגדרות אבטחה',
	itOperations: 'תפעול IT',
	systemImplementation: 'הטמעת מערכת',
	category: 'קטגוריה',
	uncategorized: 'ללא קטגוריה',
	technologies: 'טכנולוגיות',
};

export const ITProcessQuickFilterOptions = {
	0: ITProcessListingLabels.all,
	1: ITProcessListingLabels.manageChange,
	2: ITProcessListingLabels.manageAccess,
	3: ITProcessListingLabels.manageSecuritySettings,
	4: ITProcessListingLabels.itOperations,
	5: ITProcessListingLabels.systemImplementation
}

// Relate Technology Modal
export const RelateTechnologyModalLabels = {
	relateTechnology: 'קשר טכנולוגיה',
	relatedTechnologiesDescription: 'שם הטכנולוגיה',
	supportingTechnologyName: 'שם הטכנולוגיה התומכת',
	technology: '{0} technology',
	technologies: '{0} technologies'
};

export const AccountStandardROMMListingLabels = {
	accountRisksNotAvailableForDocument: 'סיכוני הסעיף אינם זמינים עבור מסמך זה.',
	noRelatedObject: 'אין אובייקט קשור. קשר אובייקט כדי להתחיל.',
	noResultsFound: 'אין סיכונים זמינים.',
	acceptedText: 'התקבל',
	rejectedText: 'נִדחֶה',
	allRisksRejected: 'כל הסיכונים נדחו',
	relevantAssertions: 'מצגי הנהלה רלוונטיים',
	rejectLabel: 'דחה',
	acceptLabel: 'קבל',
	rejectionRationaleLabel: 'נימוק דחייה',
	rejectionCategoryText: 'קטגוריית דחייה',
	editRejectionRationaleText: 'ערוך את נימוקי הדחייה',
	rejectionRationalePlaceholder: "Are you sure you want to reject the selected risk? Enter the details below and select <strong>\'Reject\'</strong>.",
	cancel: 'Cancel',
	rejectionRationaleTextAreaPlaceholder: 'Rationale (required)',
	rejectionCategoryDropdownPlaceholder: 'Rejection category (required)',
	required: 'Required',
	preRejectedText: 'Pre-rejected',
	additionalContextLabel: 'Additional context',
	additionalContextwhyRiskShouldBeRejected: 'Click {0} to add additional context specific to this client for why this risk should be rejected.',
	hereLink: 'here',
	editAdditionalContextText: 'Edit additional context'
}

export const RejectionCategory = [{
	id: 1,
	label: 'סבירות להתרחשות (Likelihood) - אין אפשרות סבירה להתרחשות (ללא התחשבות בבקרות)'
},
{
	id: 2,
	label: 'השפעה (magnitude) - הצגה מוטעית פוטנציאלית אינה מהותית'
},
{
	id: 3,
	label: 'הסיכון אינו מהותי עבור משימה זו'
},
{
	id: 4,
	label: 'ROMM מטופל בהתקשרות אחרת (ביקורות קבוצתיות בלבד)'
},
]

export const formLabels = {
	required: labels.required,
	maxLength: labels.maxLength
};

export const paginationLabels = {
	show: labels.pagingShowtext,
	first: 'הדף הראשון',
	last: 'הדף האחרון',
	prev: 'הדף הקודם',
	next: 'הדף הבא'
};
