import React, {useLayoutEffect, useState, useRef, useCallback} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import styled from 'styled-components';
import {useEngagementId} from '../../../../../../../../util/customHooks';
import {isNullOrEmpty, labels} from '../../../../../../../../util/utils';
import {entities, scotSummary198SelectedType, clientSideEventNames} from '../../../../../../../../util/uiconstants';
import {relateAccountToScot, unrelateAccountToScot} from '../../../../../../../../actions/Account/accountactions';
import {
	createAssertionRiskRelation,
	deleteAssertionRiskRelation
} from '../../../../../../../../actions/Risk/riskactions';
import PropTypes from 'prop-types';
import SCOTSummaryHeader198 from './SCOTSummaryHeader198';
import SCOTSummaryScotSearch198 from './SCOTSummaryScotSearch198';
import {
	isScotHeaderSelected,
	isAccountHeaderSelected,
	showAllAccounts,
	handleError,
	updateStateOnClickOfAccountHeader,
	searchByScotName,
	searchByAccountName,
	clearAllHeaderClick,
	getInValidAccounts,
	getInValidScots,
	getAccountList,
	getScotList,
	getAllAccounts,
	isEstimatedScot
} from './SCOTSummaryUtils198';
import SCOTSummaryContent198 from './SCOTSummaryContent198';

export default function SCOTSummaryDetailComponent198({scotAccountSummaryData = {}, reloadContent}) {
	const [scotSummaryData, setScotSummaryData] = useState();
	const engagementId = useEngagementId();
	const allAccounts = useSelector((state) => state.accounts.byId);

	const dispatch = useDispatch();

	const [onEnterAccountSearch, setOnEnterAccountSearch] = useState('');
	const [onEnterScotSearch, setOnEnterScotSearch] = useState('');
	const [isAccountToggleEnabled, setIsAccountToggleEnabled] = useState(false);
	const [isScotToggleEnabled, setIsScotToggleEnabled] = useState(false);
	const isDataRefreshed = useRef(false);

	useLayoutEffect(() => {
		if (isNullOrEmpty(scotSummaryData)) return;

		const scotIds = [scotSummaryData?.selectedScotHeaderId];
		const accountList = getAccountList(scotSummaryData, isAccountToggleEnabled, scotIds, onEnterAccountSearch);
		let updatedData = {
			...scotSummaryData,
			selectedAccounts: accountList
		};
		if (isAccountToggleEnabled && updatedData?.selectedScotHeaderId > 0 && accountList?.length === 0) {
			updatedData = {
				...updatedData,
				noAccountsDisclosureCreated: labels.scotSummary198.noResultsFound
			};
		}
		setScotSummaryData(updatedData);
	}, [isAccountToggleEnabled]);

	useLayoutEffect(() => {
		if (isNullOrEmpty(scotSummaryData)) return;

		let updatedData = {...scotSummaryData};
		if (isScotToggleEnabled && isEstimatedScot(updatedData, updatedData.selectedScotHeaderId)) {
			updatedData = clearAllHeaderClick(scotSummaryData);
			updatedData = {
				...updatedData,
				selectedScotHeaderId: 0,
				selectedAccounts: showAllAccounts(scotSummaryData)
			};
		}

		const scotList = getScotList(
			updatedData,
			isScotToggleEnabled,
			scotSummaryData.selectedAccountHeaderId,
			onEnterScotSearch
		);

		updatedData = {
			...updatedData,
			selectedScots: scotList
		};
		setScotSummaryData(updatedData);
	}, [isScotToggleEnabled]);

	useLayoutEffect(() => {
		if (isNullOrEmpty(scotAccountSummaryData)) return;

		let accountList, scotList;

		if (reloadContent) {
			reloadContent = false;
			const scotIds = [scotSummaryData.selectedScotHeaderId];
			accountList = getAccountList(scotAccountSummaryData, isAccountToggleEnabled, scotIds, onEnterAccountSearch);
			scotList = getScotList(
				scotAccountSummaryData,
				isScotToggleEnabled,
				scotSummaryData.selectedAccountHeaderId,
				onEnterScotSearch
			);
		} else {
			// Initialize scotSummaryData with the provided data - On Initial Page Load
			accountList = searchByAccountName(scotAccountSummaryData);
			scotList = searchByScotName(scotAccountSummaryData);
		}

		const updatedData = {
			...scotAccountSummaryData,
			selectedScots: scotList,
			selectedAccounts: accountList
		};
		setScotSummaryData(updatedData);
	}, [scotAccountSummaryData]);

	const onCheckChange = useCallback(
		(selectedScotEstimate) => {
			if (isNullOrEmpty(selectedScotEstimate)) return;

			const hasRelation = !selectedScotEstimate.hasRelation;
			updateLoadingOrAccountScotRelationObject(selectedScotEstimate, false, hasRelation, true);

			if (hasRelation) {
				relateScotOrEstimateToAccount(selectedScotEstimate, hasRelation);
			} else {
				unrelateScotOrEstimateFromAccount(selectedScotEstimate, hasRelation);
			}
		},
		[updateLoadingOrAccountScotRelationObject, relateScotOrEstimateToAccount, unrelateScotOrEstimateFromAccount]
	);

	function updateLoadingOrAccountScotRelationObject(
		selectedScotEstimate,
		isSuccess = false,
		hasRelation = false,
		isLoading = false
	) {
		setScotSummaryData((prevData) => ({
			...prevData,
			accounts: prevData.accounts.map((account) => ({
				...account,
				scots: account.scots.map((scotEstimate) =>
					scotEstimate.accountId === selectedScotEstimate.accountId &&
					scotEstimate.scotId === selectedScotEstimate.scotId
						? isSuccess
							? {...scotEstimate, hasRelation}
							: {...scotEstimate, isLoading}
						: scotEstimate
				)
			}))
		}));
		if (isSuccess) {
			window.dispatchEvent(new CustomEvent(clientSideEventNames.getValidations));
		}
	}

	function relateScotOrEstimateToAccount(selectedScotEstimate, hasRelation) {
		const errMessage = labels.scotSummary198.errorMessage;

		if (selectedScotEstimate.entityId === entities.SCOT) {
			const payload = {scotId: selectedScotEstimate.scotId};

			dispatch(relateAccountToScot(engagementId, selectedScotEstimate.accountId, payload))
				.then(() => {
					updateLoadingOrAccountScotRelationObject(selectedScotEstimate, true, hasRelation);
					getAllAccounts(engagementId, dispatch);
				})
				.catch(() => {
					handleError(errMessage, dispatch);
				})
				.finally(() => updateLoadingOrAccountScotRelationObject(selectedScotEstimate, false));
		} else if (selectedScotEstimate.entityId === entities.Estimate) {
			let selectedAccountAssertions = allAccounts[selectedScotEstimate.accountId]?.assertions?.map((assertion) => ({
				assertionId: assertion.id
			}));
			if (selectedAccountAssertions?.length === 0) {
				//If no assertion associated with an account then create default assertion
				selectedAccountAssertions = [
					{
						accountId: selectedScotEstimate.accountId,
						knowledgeAssertionId: scotSummary198SelectedType.defaultKnowledgeAssertionId
					}
				];
			}

			if (selectedAccountAssertions?.length > 0) {
				dispatch(
					createAssertionRiskRelation(
						engagementId,
						{id: selectedScotEstimate.scotId},
						selectedAccountAssertions,
						null,
						null,
						null,
						true
					)
				)
					.then(() => {
						updateLoadingOrAccountScotRelationObject(selectedScotEstimate, true, hasRelation);
						getAllAccounts(engagementId, dispatch);
					})
					.catch(() => {
						handleError(errMessage, dispatch);
					})
					.finally(() => updateLoadingOrAccountScotRelationObject(selectedScotEstimate, false));
			} else {
				handleError(errMessage, dispatch);
				updateLoadingOrAccountScotRelationObject(selectedScotEstimate, false);
			}
		}
	}

	function unrelateScotOrEstimateFromAccount(selectedScotEstimate, hasRelation) {
		const errMessage = labels.scotSummary198.errorMessage;

		if (selectedScotEstimate.entityId === entities.SCOT) {
			dispatch(unrelateAccountToScot(engagementId, selectedScotEstimate.accountId, selectedScotEstimate.scotId))
				.then(() => {
					updateLoadingOrAccountScotRelationObject(selectedScotEstimate, true, hasRelation);
				})
				.catch(() => {
					handleError(errMessage, dispatch);
				})
				.finally(() => updateLoadingOrAccountScotRelationObject(selectedScotEstimate, false));
		} else if (selectedScotEstimate.entityId === entities.Estimate) {
			const selectedAccountAssertions = allAccounts[selectedScotEstimate.accountId]?.assertions?.map(
				(assertion) => assertion.id
			);
			if (selectedAccountAssertions?.length > 0) {
				dispatch(
					deleteAssertionRiskRelation(engagementId, {id: selectedScotEstimate.scotId}, selectedAccountAssertions)
				)
					.then(() => {
						updateLoadingOrAccountScotRelationObject(selectedScotEstimate, true, hasRelation);
					})
					.catch(() => {
						handleError(errMessage, dispatch);
					})
					.finally(() => updateLoadingOrAccountScotRelationObject(selectedScotEstimate, false));
			} else {
				handleError(errMessage, dispatch);
				updateLoadingOrAccountScotRelationObject(selectedScotEstimate, false);
			}
		}
	}

	const onClearAccountText = useCallback(() => {
		setOnEnterAccountSearch('');

		const scotIds = [scotSummaryData.selectedScotHeaderId];
		const accountList = getAccountList(scotSummaryData, isAccountToggleEnabled, scotIds, '');
		const updatedData = {
			...scotSummaryData,
			selectedAccounts: accountList
		};
		setScotSummaryData(updatedData);
	}, [scotSummaryData, isAccountToggleEnabled, getAccountList]);

	const onClearScotText = useCallback(() => {
		setOnEnterScotSearch('');

		const scotList = getScotList(scotSummaryData, isScotToggleEnabled, scotSummaryData.selectedAccountHeaderId, '');
		let updatedData = {
			...scotSummaryData,
			selectedScots: scotList
		};

		setScotSummaryData(updatedData);
	}, [scotSummaryData, isScotToggleEnabled, getScotList]);

	const onSearchAccountName = useCallback(
		(searchText) => {
			setOnEnterAccountSearch(searchText);

			const scotIds = [scotSummaryData.selectedScotHeaderId];
			const selectedAccountList = getAccountList(scotSummaryData, isAccountToggleEnabled, scotIds, searchText);
			let updatedData = {
				...scotSummaryData,
				selectedAccounts: selectedAccountList,
				noAccountsDisclosureCreated: selectedAccountList.length > 0 ? null : labels.scotSummary198.noResultsFound
			};

			setScotSummaryData(updatedData);
		},
		[scotSummaryData, isAccountToggleEnabled, getAccountList]
	);

	const onSearchScotName = useCallback(
		(searchText) => {
			setOnEnterScotSearch(searchText);
			const scotList = getScotList(
				scotSummaryData,
				isScotToggleEnabled,
				scotSummaryData.selectedAccountHeaderId,
				searchText
			);
			let updatedData = {
				...scotSummaryData,
				selectedScots: scotList
			};

			setScotSummaryData(updatedData);
		},
		[scotSummaryData, isScotToggleEnabled, getScotList]
	);

	const onClickScotEstimateHeader = useCallback(
		(scotId) => {
			if (scotId <= 0) return;
			let accountList = [];

			const isHeaderSelected = isScotHeaderSelected(scotSummaryData, scotId);
			scotId = isHeaderSelected ? 0 : scotId;

			let updatedData = clearAllHeaderClick(scotSummaryData);

			if (updatedData.selectedAccountHeaderId > 0) {
				updatedData = {
					...updatedData,
					selectedScots: searchByScotName(updatedData)
				};
			}

			const scotIds = [scotId];
			accountList = getAccountList(updatedData, isAccountToggleEnabled, scotIds, onEnterAccountSearch);

			if (scotId === 0) {
				updatedData = {
					...updatedData,
					selectedAccounts: accountList,
					selectedScotHeaderId: scotId,
					selectedAccountHeaderId: 0
				};
			} else {
				updatedData = {
					...updatedData,
					header: updatedData.header.map((scot) =>
						scot.scotId === scotId ? {...scot, isSelected: !scot.isSelected} : scot
					),
					accounts: updatedData.accounts.map((account) => ({
						...account,
						scots: account.scots.map((scot) =>
							scot.scotId === scotId ? {...scot, isSelected: !scot.isSelected} : scot
						)
					})),
					selectedAccountHeaderId: 0,
					selectedAccounts: accountList,
					selectedScotHeaderId: scotId,
					noAccountsDisclosureCreated: accountList.length > 0 ? null : labels.scotSummary198.noResultsFound
				};
			}

			setScotSummaryData(updatedData);
		},
		[
			scotSummaryData,
			isScotHeaderSelected,
			clearAllHeaderClick,
			searchByScotName,
			getAccountList,
			isAccountToggleEnabled,
			onEnterAccountSearch
		]
	);

	const toggleHandlerForAccount = useCallback(() => {
		setIsAccountToggleEnabled((prev) => !prev);
	}, []);

	const toggleHandlerForScot = useCallback(() => {
		setIsScotToggleEnabled((prev) => !prev);
	}, []);

	const onClickAccountHeader = useCallback(
		(accountId) => {
			if (accountId === 0) return;
			let updatedData = clearAllHeaderClick(scotSummaryData);

			if (updatedData.selectedScotHeaderId > 0) {
				updatedData = {
					...updatedData,
					selectedAccounts: showAllAccounts(updatedData),
					selectedScotHeaderId: 0
				};
			}

			const isHeaderSelected = isAccountHeaderSelected(scotSummaryData, accountId);
			const selectedAccountId = isHeaderSelected ? 0 : accountId;

			updatedData = {
				...updatedData,
				selectedAccountHeaderId: selectedAccountId
			};
			updatedData = {
				...updatedData,
				selectedScots: getScotList(
					updatedData,
					isScotToggleEnabled,
					updatedData.selectedAccountHeaderId,
					onEnterScotSearch
				)
			};

			if (!isHeaderSelected) {
				updatedData = updateStateOnClickOfAccountHeader(updatedData, accountId, isScotToggleEnabled);
			}

			setScotSummaryData(updatedData);
		},
		[
			scotSummaryData,
			isScotToggleEnabled,
			onEnterScotSearch,
			clearAllHeaderClick,
			showAllAccounts,
			isAccountHeaderSelected,
			getScotList,
			updateStateOnClickOfAccountHeader
		]
	);

	return (
		<>
			{scotSummaryData?.noScotEstimateIdentified && (
				<section className="placeholder motif-body2-default-light">{scotSummaryData?.noScotEstimateIdentified}</section>
			)}
			{scotSummaryData?.noScotEstimateIdentified === null && (
				<Wrapper>
					<StyledTable>
						<section className="titleheading motif-body2-default-regular">
							<SCOTSummaryScotSearch198
								scotSummaryData={scotSummaryData}
								onSearchScotName={onSearchScotName}
								onClearScotText={onClearScotText}
								isScotToggleEnabled={isScotToggleEnabled}
								toggleHandlerForScot={toggleHandlerForScot}
								getInValidScots={getInValidScots}
							/>
						</section>
						<section className="table-container">
							{scotSummaryData?.noScotEstimateIdentified === null && (
								<table className="table table-header-rotated" cellpadding="0" cellspacing="0">
									<thead>
										<SCOTSummaryHeader198
											scotSummaryData={scotSummaryData}
											onSearchAccountName={onSearchAccountName}
											onClearText={onClearAccountText}
											onClickScotEstimateHeader={onClickScotEstimateHeader}
											isAccountToggleEnabled={isAccountToggleEnabled}
											toggleHandlerForAccount={toggleHandlerForAccount}
											getInValidAccounts={getInValidAccounts}
											isScotToggleEnabled={isScotToggleEnabled}
										/>
									</thead>
									<tbody>
										<SCOTSummaryContent198
											scotSummaryData={scotSummaryData}
											onCheckChange={onCheckChange}
											onClickAccountHeader={onClickAccountHeader}
											isScotToggleEnabled={isScotToggleEnabled}
										/>
									</tbody>
								</table>
							)}
						</section>
					</StyledTable>
				</Wrapper>
			)}
		</>
	);
}

SCOTSummaryDetailComponent198.propTypes = {
	scotAccountSummaryData: PropTypes.arrayOf(PropTypes.object),
	reloadContent: PropTypes.func
};

const Wrapper = styled.section`
	position: relative;
	.ScotEstimateSearchbox {
		margin-top: var(--px-16);
		.ScotEstimateSearchWrapper {
			width: calc(var(--px-550) - var(--px-18));
			height: var(--px-44);
			//for 125% resolution
			@media screen and (max-width: 1600px), screen and (min-height: 1020px) {
				width: calc(var(--px-540) - var(--px-18)) !important;
			}
			//for 150% resolution
			@media screen and (max-width: 1280px), screen and (max-height: 720px) {
				width: calc(var(--px-540) - var(--px-18)) !important;
			}
		}
	}

	.cursor-pointer {
		cursor: pointer !important;
	}

	.noaccountplaceholder {
		width: 100%;
		display: flex;
		background: var(--neutrals-00white);
		padding: var(--px-12) var(--px-20) var(--px-12) var(--px-12);
		justify-content: center;
		color: var(--neutrals-700);
	}
	.fade {
		height: calc(100% - var(--px-7));
		display: flex;
		position: absolute;
		right: var(--px-6);
		bottom: var(--px-6);
		/*border: 1px solid red;*/
		width: 5%;
		z-index: 999;
		background: linear-gradient(95deg, transparent, var(--neutrals-00white));
	}
	.titleheading {
		margin: 0;
		padding: var(--px-20) 0 var(--px-15) var(--px-825);
		font-size: var(--px-14);
		font-weight: bold;
		border-radius: var(--px-8);
		background-color: var(--neutrals-bg-gradient1) !important;

		//for 125% resolution
		@media screen and (max-width: 1600px), screen and (min-height: 1020px) {
			padding: var(--px-20) 0 var(--px-15) calc(var(--px-540) + var(--px-275));
		}
		//for 150% resolution
		@media screen and (max-width: 1280px), screen and (max-height: 720px) {
			padding: var(--px-20) 0 var(--px-15) calc(var(--px-540) + var(--px-275)) !important;
		}

		.motif-badge {
			margin: 0 var(--px-6);
		}

		.title {
			display: flex;
			padding-bottom: var(--spacing-small);
			border-bottom: var(--px-1) solid var(--neutrals-200);
			margin-right: var(--px-16);
			.badgewrapper {
				display: flex;
				align-items: center;
			}
			.name {
				color: var(--neutrals-900);
				align-content: center;
				white-space: nowrap;
			}
			.validation {
				margin-left: auto;
				align-items: center;
				display: inline-flex;
				gap: var(--spacing-small);
				.validationicon {
					svg {
						width: var(--px-20);
						height: var(--px-20);
						& > path {
							color: var(--orange-500);
						}
					}
				}

				.validationtext {
					color: var(--neutrals-700);
				}
			}
		}
	}
`;
const StyledTable = styled.section`
	border: 1px solid var(--neutrals-200);
	border-radius: var(--px-8);
	.table-container {
		max-height: var(--px-650);
		min-height: var(--px-650);
		border-radius: var(--px-8);
		width: 100%;
		overflow: auto;
		background-color: var(--neutrals-bg-gradient1) !important;

		//for 125% resolution
		@media screen and (max-width: 1600px), screen and (min-height: 1020px) {
			max-height: var(--px-580) !important;
			min-height: var(--px-580) !important;
		}
		//for 150% resolution
		@media screen and (max-width: 1280px), screen and (max-height: 720px) {
			max-height: var(--px-530) !important;
			min-height: var(--px-530) !important;
		}
	}

	.table-header-rotated {
		border-collapse: collapse;
		font-size: var(--px-14);
	}

	.table-header-rotated thead {
		position: sticky;
		top: 0;
		z-index: 9;
		vertical-align: bottom;
		box-shadow: calc(var(--px-2) * -1) 0 var(--px-2) 0 rgba(0, 0, 0, 0.5);
	}

	.table-header-rotated thead th:first-child .patchone {
		position: absolute;
		bottom: 0;
		z-index: 1;
		width: var(--px-228);
		height: var(--px-225);
		background: var(--neutrals-bg-gradient1);
		clip-path: polygon(100% 0%, 0% 0%, 0% 100%);
		top: 0;
		left: var(--px-550);
		//for 125% resolution
		@media screen and (max-width: 1600px), screen and (min-height: 1020px) {
			width: var(--px-228);
			height: var(--px-225);
			left: var(--px-540);
		}

		//for 150% resolution
		@media screen and (max-width: 1280px), screen and (max-height: 720px) {
			width: var(--px-220);
			height: var(--px-225);
			left: var(--px-540);
		}
	}

	.table-header-rotated thead th:first-child .account {
		width: var(--px-550);
		padding: var(--px-12) var(--px-0) var(--px-15) var(--px-16);
		display: flex;
		//for 125% resolution
		@media screen and (max-width: 1600px), screen and (min-height: 1020px) {
			width: var(--px-540) !important;
		}
		//for 150% resolution
		@media screen and (max-width: 1280px), screen and (max-height: 720px) {
			width: var(--px-540) !important;
		}
		.motif-badge {
			margin: 0 var(--px-6);
		}

		.titlewrapper {
			width: 100%;
			.title {
				display: flex;
				padding-bottom: var(--spacing-small);
				border-bottom: var(--px-1) solid var(--neutrals-200);
				.badgewrapper {
					display: flex;
					align-items: center;
				}

				.name {
					color: var(--neutrals-900);
					align-content: center;
					white-space: nowrap;
				}
				.validation {
					margin-left: auto;
					align-items: center;
					display: inline-flex;
					gap: var(--spacing-small);
					.validationicon {
						svg {
							width: var(--px-20);
							height: var(--px-20);
							& > path {
								color: var(--orange-500);
							}
						}
					}

					.validationtext {
						color: var(--neutrals-700);
					}
				}
			}
		}
	}

	.table-header-rotated thead th:first-child,
	.table-header-rotated tbody th {
		position: sticky;
		left: 0;
		z-index: 8;
		vertical-align: bottom;
	}

	.table-header-rotated thead th {
		height: var(--px-225);
		background-color: var(--neutrals-bg-gradient1) !important;
	}

	.table-header-rotated tbody tr:last-child {
		border-bottom: 1px solid var(--neutrals-200);
		border-right: 1px solid var(--neutrals-200);
	}

	.table-header-rotated tbody tr:nth-child(odd),
	.table-header-rotated tbody tr:nth-child(odd) th {
		background-color: var(--neutrals-00white);
	}

	.table-header-rotated tbody tr:nth-child(even),
	.table-header-rotated tbody tr:nth-child(even) th {
		background-color: var(--neutrals-25);
	}

	.table-header-rotated tbody tr td:not(.empty):hover {
		background: var(--neutrals-50) !important;
		&.selected-header {
			background: var(--neutrals-100) !important;
		}
	}

	.table-header-rotated tbody th > div {
		text-align: left;
		display: inline-block;
		width: var(--px-550);
		cursor: pointer;
		&.selected-header {
			cursor: pointer;
			background: var(--neutrals-100) !important;
		}

		//for 125% resolution
		@media screen and (max-width: 1600px), screen and (min-height: 1020px) {
			width: var(--px-540) !important;
		}
		//for 150% resolution
		@media screen and (max-width: 1280px), screen and (max-height: 720px) {
			width: var(--px-540) !important;
		}
		// overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		padding: var(--px-12) var(--px-20) var(--px-12) var(--px-12);
		font-weight: 300;
		border-right: 1px solid var(--neutrals-200);

		.accountnames {
			display: flex;
			gap: var(--px-15);
			.name {
				width: 80%;
				flex: 1;
				display: flex;
			}
			.selected-header {
				background: var(--neutrals-100) !important;
			}
			.estimatecount {
				margin-left: auto;
				line-height: normal;
				align-items: center;
				display: flex;
				justify-content: end;
				.badge {
					width: var(--px-35);
					margin-left: var(--px-8);
					text-align: right;
					.motif-badge-error {
						cursor: pointer;
					}
					.motif-badge-default {
						cursor: default;
					}
				}
				.has-estimate {
					width: var(--px-20);
					display: flex;
					.motif-icon {
						display: flex;
						align-items: center;
						cursor: default;
						svg {
							width: var(--px-16);
							height: var(--px-16);
						}
					}
				}
			}
		}
	}
	.table-header-rotated td {
		width: var(--px-72);

		&.empty {
			width: var(--px-36);
			background-color: var(--neutrals-50);
		}
		&.selected-header {
			width: var(--px-36);
			background-color: var(--neutrals-100);
			cursor: pointer;
		}
	}

	.table-header-rotated td {
		padding: 0;
		border-right: 1px solid var(--neutrals-200);

		.StyledMotifCheckbox {
			justify-content: center;
			display: flex;
			align-items: center;
			align-content: center;
			width: auto;
			padding-left: var(--px-4);
		}

		.motif-progress-bar-wrapper {
			width: var(--px-30);
			height: var(--px-30);
			text-align: center;
			margin-left: var(--px-20);
		}
	}
	.table-header-rotated th > div span.content {
		// cursor: pointer;
	}

	.table-header-selected-bgColor {
		background-color: var(--neutrals-200) !important;
	}
	.table-header-rotated th > div {
		text-align: left;
	}
	.table-header-rotated th.rotate {
		height: var(--px-140);
		white-space: nowrap;
		&.title {
			font-weight: bold;
			& > .patch,
			& > .iconcount {
				display: none;
				border: 0;
			}

			& > div {
				width: var(--px-36);
				transform: translate(var(--px-23), calc(var(--px-9) * -1)) rotate(315deg);
				& > span {
					height: var(--px-24);
					padding: 0 0 0 var(--px-22);
					& > .content {
						height: var(--px-22);
						cursor: default !important;
					}
				}
			}
			.scotname {
				margin-left: var(--px-24) !important;
			}
		}

		.countholder {
			position: relative;
			display: flex;
			justify-content: center;
		}

		.iconcount {
			position: absolute;
			bottom: 0;
			z-index: 2;
			margin: var(--px-10) 0 var(--px-10) var(--px-43);
			.motif-badge-error {
				cursor: pointer;
			}
			.motif-badge-default {
				cursor: default;
			}
		}

		.patch {
			position: absolute;
			cursor: pointer;
			bottom: 0;
			z-index: 1;

			width: var(--px-75);
			height: var(--px-38);
			background: var(--neutrals-bg-gradient1);
			clip-path: polygon(50% 5%, 2% 100%, 98% 100%);

			&.selected-header {
				cursor: pointer;
				background-color: var(--neutrals-100);
			}
		}
	}
	.table-header-rotated th.rotate > div {
		transform: translate(var(--px-44), calc(var(--px-17) * -1)) rotate(315deg);
		width: var(--px-72);
	}
	.table-header-rotated th.rotate > div > span {
		border-bottom: 1px solid transparent;
		border-image: linear-gradient(to right, var(--neutrals-200), var(--neutrals-bg-gradient1));
		border-image-slice: 1;
		padding: var(--px-7) 0 var(--px-7) var(--px-22);
		width: var(--px-355);
		height: var(--px-51);
		display: block;
		background-color: var(--neutrals-bg-gradient1) !important;
		cursor: pointer;
		&.selected-header {
			cursor: pointer;
			background-color: var(--neutrals-100) !important;
		}
	}

	.table-header-rotated th.rotate > div span.content {
		max-width: var(--px-300);
		text-overflow: ellipsis;
		white-space: nowrap;
		display: flex;
		align-items: center;
		height: var(--px-34);
		cursor: pointer !important;

		.scotname {
			margin-left: var(--px-10);
		}
	}

	.table-header-rotated th.row-header {
		padding: 0 var(--px-10);
		border-bottom: 1px solid var(--neutrals-200);
	}

	.cursor-pointer {
		cursor: pointer !important;
	}
`;

const StyledLoader = styled.section`
	background: var(--neutrals-00white);
	width: 100%;
	height: 100%;
	position: absolute;
	z-index: 999;
	opacity: 0.5;
	top: var(--px-0);
	pointer-events: none;
	justify-content: center;
	align-items: center;
	display: flex;
`;
