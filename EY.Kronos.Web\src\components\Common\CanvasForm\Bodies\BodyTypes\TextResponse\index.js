/**
 * TextResponse.js
 * Created by <PERSON> on 05/15/2020.
 */

import React, {useEffect, useCallback, useMemo, useState, useRef} from 'react';
import styled from 'styled-components';
import $ from 'jquery';
import {contentIcCreate24px, actionIcInfo24px} from '@ey/motif-react-wrapper/assets/icons/index';
import {useDocumentId, useEngagementId, useUserPreferences} from '../../../../../../util/customHooks';
import {useIsDrawerMode} from '../../../../../../util/Ares/customHooks';
import {clientSideEventNames, formBodyType} from '../../../../../../util/uiconstants';
import Utility from '../../../../../../util/utilityfunctions';
import {labels, GetCardTypeForBody} from '../../../../../../util/utils';
import {loadStorage, saveStorage} from '../../../../localStorage';
import BodyKnowledgeDescription from '../../BodyKnowledgeDescription';
import ShowMoreLess from '../../../../../Common/ShowMoreLess/ShowMoreLess';
import Loader from '../../../../LoadingIndicator/LoadingIndicator';
import EditTextResponse from '../../Common/EditTextResponse';
import EllipsesControl from '../../../../../Common/EllipsesControl/EllipsesControl';
import ResourceWithLink from '../../../../../Common/ResourceWithLink/ResourceWithLink';
import Icon from '../../../../BaseComponentWrappers/MotifIcon';
import Modal from '../../../../../Common/Modal/Modal';
import InlineTextResponse from './Inline/InlineTextResponse';
import InlineCommentsWrapper from './Inline/InlineCommentsWrapper';

let inlineKendo = undefined;
let textWhenClickOutside = undefined;

export default function TextResponse(props) {
	const {
		isAresView,
		showGuidance,
		guidanceFilter,
		body,
		headerId,
		sectionId,
		saveComplete,
		getBodiesBySectionId,
		isSnapshotView,
		isDisabled,
		showEditTextResponse,
		setShowEditTextResponse,
		useMinimalToolset,
		isViewportBody
	} = props;

	const [showKendoModal, setShowKendoModal] = useState(false),
		[showLoader, setShowLoader] = useState(false),
		[saveChanges, setSaveChanges] = useState(false),
		[showBodyFreeText, setShowBodyFreeText] = useState(true),
		[showInlineKendo, setShowInlineKendo] = useState(false),
		[maxCharCountExceeded, setMaxCharCountExceeded] = useState(false),
		[maxCharCountMessage, setMaxCharCountMessage] = useState(''),
		[saveAndCloseChanges, setSaveAndCloseChanges] = useState(false),
		[freeTextAres, setFreeTextAres] = useState(false),
		[cancel, setCancel] = useState(false);

	const engagementId = useEngagementId(),
		userPreferences = useUserPreferences(),
		documentId = useDocumentId(),
		isDrawerMode = useIsDrawerMode();

	const _isDisabled = isDisabled || isSnapshotView || !userPreferences.engagementUserIsEngagementAccessible;

	const aresOrInlineEditorView = isAresView || body?.bodyTypeId === formBodyType.InlineRichText;
	const formBodyBorder = GetCardTypeForBody(body, isAresView);

	const inlineKendoEditorRef = useRef();
	const currentEditingBodyId = loadStorage('bodyId');

	const showInlineKendoOnClick = (evt) => {
		if (_isDisabled && evt) {
			evt.preventDefault();
			evt.stopPropagation();

			return false;
		}

		if (aresOrInlineEditorView) {
			setSaveChanges(false);
			setSaveAndCloseChanges(false);
			setCancel(false);
			setShowInlineKendo(true);
			setShowBodyFreeText(false);
			!isDrawerMode && bringEditorToView();
			saveStorage('bodyId', body.id);
		}
	};

	const bringEditorToView = () => {
		const scrollBehaviour = {behavior: 'smooth', block: 'center', inline: 'nearest'};
		setTimeout(() => {
			inlineKendoEditorRef.current && inlineKendoEditorRef.current.scrollIntoView(scrollBehaviour);
		}, 100);
	};

	const hideInlineKendo = useCallback(() => {
		setShowInlineKendo(false);
		setShowBodyFreeText(true);
		setCancel(false);
		saveStorage('bodyId', undefined);
	}, []);

	const showInlineKendoEditor = useCallback(() => {
		setShowInlineKendo(true);
		setShowBodyFreeText(false);
		setCancel(false);
	}, []);

	useEffect(() => {
		if (body.id === currentEditingBodyId && showEditTextResponse === true) {
			setShowKendoModal(true);
		} else if (showEditTextResponse && isAresView) {
			showInlineKendoEditor();
		}
		setSaveAndCloseChanges(false);
	}, [showEditTextResponse]);

	useEffect(() => {
		if (body.id === currentEditingBodyId) {
			if (showEditTextResponse === true || showInlineKendo === true) {
				if (body.isConflict) {
					if (aresOrInlineEditorView) {
						showInlineKendoOnClick();
					} else {
						setShowKendoModal(true);
					}
				} else {
					if (!showInlineKendo) {
						showInlineKendoOnClick();
					}
				}
			} else if (isAresView) {
				hideInlineKendo();
			}
		}
	}, [body]);

	useEffect(() => {
		if (showLoader === true && saveComplete === true) {
			setShowLoader(false);
		}
		setSaveAndCloseChanges(false);
	}, [saveComplete]);

	useEffect(() => {
		if (isAresView) {
			window.addEventListener(clientSideEventNames.closeInlineRichTextEditor, hideInlineKendo);
			return () => {
				window.removeEventListener(clientSideEventNames.closeInlineRichTextEditor, hideInlineKendo);
			};
		}
	}, []);

	const testShowMoreLessLabel = (val) => {
		setFreeTextAres(val);
	};

	const showMoreRichText = useMemo(() => {
		return (
			<ShowMoreLess showMoreLessLabel={testShowMoreLessLabel} isFromKendo tolerance={10}>
				{body.bodyFreeText && Utility.parseRichText(body.bodyFreeText)}
			</ShowMoreLess>
		);
	}, [body.bodyFreeText]);

	const hasRichText =
		body &&
		body.bodyFreeText &&
		body.bodyFreeText.replaceAll('<p></p>', '').trim().length > 0 &&
		body.bodyFreeText.replaceAll('<p data-placeholder="Enter text" class="placeholder"></p>', '').length > 0 &&
		body.bodyFreeText.localeCompare('') !== 0;

	const setSelectedComment = ({commentId}) => {
		let selected = $(inlineKendoEditorRef.body).find('span[data-for=' + commentId + ']');
		if (selected.length > 0) {
			selected[0].scrollIntoView({
				behavior: 'smooth'
			});
		}
	};

	return (
		<Wrapper
			className={`SectionBodyWrapper TextResponseBody ${formBodyBorder} ${isViewportBody ? 'BodyViewport' : ''}
						${showLoader ? 'loading' : ''}`}
			key={body.id}
		>
			{body && body.id > 0 && (
				<BodyKnowledgeDescription
					isAresView={isAresView}
					showGuidance={showGuidance}
					guidanceFilter={guidanceFilter}
					isSnapshotView={_isDisabled}
					bodyTypeId={body.bodyTypeId}
					bodyModel={body}
					engagementId={engagementId}
					documentId={documentId}
					headerId={headerId}
					sectionId={sectionId}
					bodyId={body.id}
					bodyDescriptionText={body.name}
					isCustom={body.isCustom}
					isDuplicate={body.isDuplicate}
					getBodiesBySectionId={getBodiesBySectionId}
				/>
			)}
			<section
				ref={inlineKendoEditorRef}
				id={`SectionBodyData-${body.id}`}
				className={`SectionBodyData SectionBodyDescriptionWrapper
							${aresOrInlineEditorView === true ? 'SectionBodyAresDescriptionWrapper' : ''}
							${showBodyFreeText ? 'sectionBodyAresDescriptionContainer' : 'sectionBodyAresDescriptionNoBorder'}`}
			>
				{showBodyFreeText === true && hasRichText ? (
					<>
						{showLoader && <Loader show view="inlineView" styleName="LoadingIndicator" />}
						<section
							key={body.id}
							onClick={(e) => {
								if (!_isDisabled) {
									if (!$(e.target).hasClass('toggler-button')) {
										e.persist();
										setTimeout(() => {
											/*just in case the user clicks on another editor when there are
												unsaved changes in one, let the outsideClick event fire first*/
											!aresOrInlineEditorView && showInlineKendoOnClick(e);
										}, 100);
									}
								}
							}}
							id="sectionFreeTextAres"
							className={`${
								aresOrInlineEditorView === true && !_isDisabled ? 'section-freeText-ares' : 'section-freeText'
							} ${freeTextAres ? '' : 'freeTextAresNoShowMore'}`}
						>
							{!_isDisabled ? showMoreRichText : Utility.getHtmlTextElement(body.bodyFreeText, _isDisabled)}
						</section>
					</>
				) : (
					<>
						{aresOrInlineEditorView === true ? (
							<>
								{showBodyFreeText === true && (
									<StyledNoDataPlaceholder className="no-response" id={`placeholder-${body.id}`}>
										{!isViewportBody && !_isDisabled ? (
											<ResourceWithLink
												className="kendo-add-response"
												onPlaceholderClickConfig={{clickHere: showInlineKendoOnClick}}
												placeholder={labels.noTypeTwoResponseAvailable}
											/>
										) : (
											<section className="NoResponseLabel">{labels.noResponseAvailable}</section>
										)}
									</StyledNoDataPlaceholder>
								)}
							</>
						) : (
							<section className="editToContinue">{labels.clickEditResponseToContinue}</section>
						)}
					</>
				)}
				{!_isDisabled && (
					<>
						{aresOrInlineEditorView ? (
							<>
								<section
									className={`edit-ResponseWrapper Ares-edit-ResponseWrapper ${
										showBodyFreeText ? 'SectionTxtVisible' : 'SectionTxtInVisible'
									}`}
								>
									{showInlineKendo === true && (
										<>
											<InlineCommentsWrapper
												engagementId={engagementId}
												bodyId={body.id}
												headerId={headerId}
												sectionId={sectionId}
												documentId={documentId}
												setSelectedComment={setSelectedComment}
											>
												<InlineTextResponse
													body={body}
													cancel={cancel}
													headerId={headerId}
													sectionId={sectionId}
													hideInlineKendo={hideInlineKendo}
													userPreferences={userPreferences}
													saveAndCloseChanges={saveAndCloseChanges}
													saveChanges={saveChanges}
													showLoader={showLoader}
													setCancel={setCancel}
													setMaxCharCountExceeded={setMaxCharCountExceeded}
													setMaxCharCountMessage={setMaxCharCountMessage}
													setSaveAndCloseChanges={setSaveAndCloseChanges}
													setSaveChanges={setSaveChanges}
													setShowLoader={setShowLoader}
													setShowEditTextResponse={setShowEditTextResponse}
													textWhenClickOutside={textWhenClickOutside}
													isAresView={isAresView}
													showInlineKendoEditor={showInlineKendoEditor}
													useMinimalToolset={useMinimalToolset}
												/>
											</InlineCommentsWrapper>
										</>
									)}
									{showBodyFreeText === true && hasRichText && (
										<section className="edit-response-link" onClick={showInlineKendoOnClick} id={body.id}>
											<Icon src={contentIcCreate24px} />
											<EllipsesControl content={labels.editRichText} tooltip={labels.editRichText} noOfLines={1} />
										</section>
									)}
								</section>
								{maxCharCountExceeded === true && (
									<section className="max-count-exceeded">
										<Icon src={actionIcInfo24px} />
										{maxCharCountMessage}
									</section>
								)}
							</>
						) : (
							<section className={`edit-ResponseWrapper`}>
								<section
									className="edit-response-icon"
									onClick={() => {
										setSaveChanges(false);
										setSaveAndCloseChanges(false);
										setCancel(false);
										setShowKendoModal(true);
									}}
									id={body.id}
									title={labels.edit}
								>
									<Icon src={contentIcCreate24px} title={labels.edit} />
								</section>
								<Modal
									theme={Utility.getTheme()}
									focusTrapPaused
									show={showKendoModal}
									onHide={() => {
										setCancel(true);
									}}
									onOkClick={() => {
										setSaveChanges(true);
										setSaveAndCloseChanges(false);
									}}
									onSaveAndCreateNew={() => {
										setSaveAndCloseChanges(true);
										setSaveChanges(false);
									}}
									showButtons
									modalonmodal
									title={labels.editResponse}
									confirmBtnTitle={labels.save}
									closeTitle={labels.closeLabel}
									closeBtnTitle={labels.cancelButtonLabel}
									modalsize="large"
									modalBodyStyle="EditResponseModalBody cutsomScrollbar"
									modalContainerClass="EditResponseModalContent"
									isOkButtonDisabled={showLoader}
									isCancelButtonDisabled={showLoader}
									isSaveAndCreateNewButtonDisabled={showLoader}
									showSaveAndCreateNewButton
									saveAndCreateNewBtnTitle={labels.saveAndCloseButtonTitle}
								>
									<StyledEditorBody className="StyledEditorBody">
										<EditTextResponse
											headerId={headerId}
											sectionId={sectionId}
											bodyId={body.id}
											body={body}
											setShowModal={setShowKendoModal}
											isConflict={body.isConflict}
											cancel={cancel}
											setCancel={setCancel}
											setShowLoader={setShowLoader}
											saveTextChanges={saveChanges}
											setSaveTextChanges={setSaveChanges}
											saveAndCloseChanges={saveAndCloseChanges}
											setSaveAndCloseChanges={setSaveAndCloseChanges}
											setShowEditTextResponse={setShowEditTextResponse}
										/>
									</StyledEditorBody>
								</Modal>
							</section>
						)}
					</>
				)}
			</section>
		</Wrapper>
	);
}

const Wrapper = styled.section`
	display: block;
	transition: all 0.3s;
	position: relative;
	.LoadingIndicator {
		position: absolute;
		top: 30%;
		left: 50%;
		display: none;
	}
	&.loading {
		// z-index: 2;
		// opacity: 0.5;
		pointer-events: none;
		.LoadingIndicator {
			display: block;
		}
		.Ares-edit-ResponseWrapper {
			.inline-loader {
				.LoadingIndicator {
					.MotifProgressBarLabel {
						padding-left: 0;
					}
				}
			}
		}
		.StyledInlineEditorBody {
			opacity: 0.5;
			pointer-events: none;
		}
	}
	.SectionBodyData {
		flex-direction: unset !important;
		position: relative;
		&.SectionBodyAresDescriptionWrapper {
			&.sectionBodyAresDescriptionContainer {
				border: var(--px-1) solid var(--neutrals-300);
				background-color: var(--neutrals-00white);
				padding: 0;
				border-radius: var(--px-12);
				margin: var(--px-24) 0 0 0 !important;
				width: 100% !important;
				.section-freeText-ares {
					padding: var(--px-24) var(--px-16);
					.richText-Body {
						margin-bottom: var(--px-10);
						&.collapsed {
							max-height: var(--px-110) !important;
						}
						&.ql-editor {
							padding: 0 !important;
							p {
								line-height: var(--px-30);
							}
							.highlight-teal {
								font-size: inherit;
								line-height: inherit !important;
								border-top: 0 !important;
								border-right: var(--px-1) solid var(--neutrals-700) !important;
								border-bottom: 0 !important;
								border-left: var(--px-1) solid var(--neutrals-700) !important;
								& + .highlight-teal {
									margin-left: calc(var(--px-1) * -1);
								}
							}
							strong {
								font-weight: 700;
							}
						}
						u > * {
							text-decoration: underline;
						}
					}
					.toggler-button {
						margin-top: var(--px-14);
						font-size: var(--px-14);
						font-weight: 700;
						line-height: var(--px-22);
						opacity: 1;
					}
				}
				.freeTextAresNoShowMore {
					padding: var(--px-24) var(--px-16);
					.RichTextBodySpan ul li,
					.RichTextBodySpan ol li {
						margin-left: var(--px-20);
						margin-bottom: 0;
					}
				}
				.Ares-edit-ResponseWrapper {
					position: absolute;
					bottom: var(--px-20);
					right: var(--px-24);
					.inline-loader {
						.LoadingIndicator {
							top: var(--px-30);
							left: var(--px-10);
							width: calc(100% - var(--px-20));
							display: flex;
							height: calc(100% - var(--px-40));
							border-radius: var(--px-12);
						}
					}
					.edit-response-link {
						position: relative;
						top: var(--px-10);
						margin-left: 0;
						width: auto;
						font-size: var(--px-14);
						font-weight: 400;
						line-height: var(--px-22);
						.motif-icon {
							display: flex;
							align-items: center;
							svg {
								color: var(--blue-600);
							}
						}
					}
					&.singleLinetAresEditResponseWrapper {
						bottom: var(--px-30);
					}
				}
				.freeTextAresNoShowMore + .Ares-edit-ResponseWrapper {
					bottom: var(--px-30);
				}
				.no-response {
					border: var(--px-1) dashed var(--neutrals-900);
					width: 100%;
					padding: var(--px-84);
					margin: var(--px-14) !important;
					border-radius: var(--px-8);
					cursor: default;
					.NoResponseLabel,
					.NoResponseLabel span {
						font-weight: 300 !important;
						color: var(--neutrals-900) !important;
					}
					.kendo-add-response {
						margin: 0;
						span {
							font-size: var(--px-14);
							font-weight: 300;
							line-height: var(--px-20);
							color: var(--neutrals-900);
						}
						a {
							font-size: var(--px-14);
							font-weight: 700;
							line-height: var(--px-22);
						}
					}
				}
			}
			&.sectionBodyAresDescriptionNoBorder {
				border: 0;
				flex-direction: column !important;
				margin: var(--px-3) 0 0 0 !important;
				width: 100% !important;
				padding: 0;
				.Ares-edit-ResponseWrapper {
					.inline-loader {
						.LoadingIndicator {
							display: flex;
							width: var(--px-42);
							height: var(--px-42);
						}
					}
					.StyledInlineEditorBody {
						.InlineKendoEditorWrapper {
							background: transparent;
							.edit-response-wrapper {
								.KendoEditorWrapper {
									.StyledRichTextEditor {
										.ql-toolbar {
											border-color: var(--neutrals-200);
											border-radius: var(--px-8) var(--px-8) 0 0;
											.ql-stroke {
												stroke: var(--neutrals-900);
											}
											.ql-size {
												width: var(--px-65);
												.ql-picker-label {
													&::before {
														color: var(--neutrals-900);
													}
												}
												.ql-picker-options {
													background-color: var(--neutrals-00white);
													.ql-picker-item {
														color: var(--neutrals-900);
													}
												}
											}
										}
										.motif-rich-text-editor {
											.ql-container {
												border-color: var(--neutrals-200);
												.ql-editor {
													outline: none !important;
													p {
														span {
															line-height: normal;
														}
													}
													li[data-list='bullet'] > .ql-ui:before {
														font-family: auto;
													}
													table {
														td {
															border: var(--px-1) solid var(--neutrals-900);
														}
													}
												}
											}
										}
									}
									.rich-text-kendo-editor {
										.kendo-editor-label {
											font-size: var(--px-12);
											font-weight: 400;
											line-height: var(--px-20);
											margin-right: var(--px-10);
										}
										.k-editor {
											background-color: var(--neutrals-00white);
											border: var(--px-1) solid var(--neutrals-200);
											border-radius: var(--px-8) var(--px-8) 0 0;
											border-bottom: 0;
											.k-toolbar {
												padding: var(--px-18) var(--px-16);
												border: 0;
												border-bottom: var(--px-1) solid var(--neutrals-200);
												box-shadow: none;
												background-color: var(--neutrals-00white);
												border-radius: var(--px-8) var(--px-8) 0 0;
												.k-button-group {
													.k-dropdownlist {
														width: var(--px-180);
														background-color: var(--neutrals-00white);
														border: var(--px-1) solid var(--neutrals-200) !important;
														color: var(--neutrals-900);
														.k-input-inner {
															font-size: var(--px-14) !important;
															font-weight: 300 !important;
															line-height: var(--px-22) !important;
														}
														.k-button {
															border: 0 !important;
															border-left: var(--px-1) solid var(--neutrals-200) !important;
														}
													}
													.k-picker {
														background-color: var(--neutrals-00white);
														border: var(--px-1) solid var(--neutrals-200) !important;
														color: var(--neutrals-900);
														border-radius: var(--px-8) !important;
														.k-button {
															border: 0 !important;
														}
													}
													.k-button {
														background-color: var(--neutrals-00white);
														border: var(--px-1) solid var(--neutrals-200) !important;
														color: var(--neutrals-900);
														border-radius: var(--px-8) !important;
														& + .k-button {
															border-radius: 0 !important;
														}
														&.k-group-start {
															border-radius: var(--px-8) 0 0 var(--px-8) !important;
														}
														&.k-group-end {
															border-radius: 0 var(--px-8) var(--px-8) 0 !important;
														}
													}
												}
											}
											.k-editor-content {
												padding: var(--px-16);
											}
										}
									}
								}
							}
						}
						.save-container {
							background-color: var(--neutrals-00white);
							border: var(--px-1) solid var(--neutrals-300);
							border-radius: 0 0 var(--px-8) var(--px-8);
							float: unset;
							width: 100%;
							margin: 0;
							justify-content: flex-end;
							padding: var(--px-12) var(--px-16);
							border-top: 0;
							.save-link,
							.cancel-link {
								color: var(--blue-600);
								display: inline-flex;
								align-items: center;
								font-size: var(--px-14);
								font-weight: 400;
								.motif-icon {
									display: inline-flex;
									svg {
										fill: var(--blue-600);
										path {
											fill: var(--blue-600);
										}
									}
								}
							}
						}
						&.error {
							.kendo-editor-label {
								color: var(--neutrals-900);
							}
						}
					}
				}
				.max-count-exceeded {
					position: unset;
					margin-top: var(--px-8);
					font-size: var(--px-12);
					font-weight: 300;
					line-height: var(--px-20);
					.motif-icon {
						display: flex;
						align-items: center;
						margin-top: calc(var(--px-2) * -1);
						svg {
							width: var(--px-15) !important;
							height: var(--px-15) !important;
						}
					}
				}
			}
		}
		.no-response {
			text-align: center;
			margin: auto;
			cursor: pointer;
		}
		.kendo-add-response {
			margin-left: var(--px-220);
			a {
				color: var(--blue-600);
				font-weight: bold;
				&:hover {
					text-decoration: underline;
				}
			}
		}
	}
	.SectionBodyData-withOverlay {
		z-index: 999999;
		background-color: var(--neutrals-100);
	}
	.edit-ResponseWrapper {
		display: inline-flex;
		margin-left: auto;
		.motif-modal-overlay {
			padding: var(--px-10);
			z-index: 1002;
		}
		.motif-modal-footer {
			button {
				padding: var(--px-5);
			}
		}
		.save-container {
			display: inline-flex;
			font-weight: 400;
			line-height: var(--px-20);
			color: var(--neutrals-1000);
			//cursor: pointer;
			float: right;
			margin-top: var(--px-10);
			.save-link {
				padding-right: var(--px-16);
				cursor: pointer;
				svg {
					fill: var(--green-500);
					margin-right: var(--px-8);
				}
			}
			.cancel-link {
				cursor: pointer;
				svg {
					fill: var(--red-600);
					margin-right: var(--px-8);
				}
			}
		}
	}
	.section-freeText {
		display: inline-flex;
		color: var(--neutrals-900);
		margin: 0;
		padding: 0;
		width: calc(100% - var(--px-40));
		word-break: break-word;
		.richText-Body {
			font-size: inherit;
			width: 100%;
			line-height: normal;
			.k-content {
				background-color: transparent;
			}
			a,
			a * {
				color: var(--blue-600) !important;
			}
			tr,
			th,
			td {
				border: var(--px-1) solid var(--neutrals-300);
				border-collapse: collapse;
				background-color: transparent;
				span {
					font-size: inherit;
				}
			}
			table {
				width: calc(100% - var(--px-10));
				max-width: 100%;
				border-collapse: collapse;
				background-color: transparent;
				table-layout: fixed;
				td,
				th {
					width: 33%;
				}
			}
		}
	}
	.section-freeText-ares {
		color: var(--neutrals-900);
		margin: 0;
		padding: 0;
		width: calc(100% - var(--px-40));
		word-break: break-word;
		min-height: var(--px-24);
		align-items: center;
	}
	.highlight-teal {
		font-size: inherit;
		line-height: inherit;
		border: var(--px-1) solid #93f0e6;
		background: #93f0e6 !important;
		padding: var(--px-2);
	}
	.diffins {
		background-color: #e6ffe6;
		font-family: inherit;
		text-decoration: none;
	}
	.diffdel {
		color: #999;
		background-color: #ffe6e6;
		font-family: inherit;
	}
	.mod {
		background-color: #ffe1ac;
		font-family: inherit;
	}
	.Ares-edit-ResponseWrapper {
		display: flex;
		&.SectionTxtInVisible {
			width: 100%;
		}
		.inline-loader {
			z-index: 10000;
			position: absolute;
			left: 50%;
			top: 40%;
			.LoadingIndicator {
				left: 39%;
				width: var(--px-500);
				.MotifProgressBarLabel {
					color: var(--green-500);
					font-size: var(--px-14);
				}
			}
		}
	}
	.editToContinue {
		display: inline-flex;
		margin: 0;
		width: calc(100% - var(--px-150));
		color: var(--neutrals-700);
	}
	.edit-response-icon {
		display: inline-flex;
		width: var(--px-40);
		justify-content: flex-end;
		margin-left: auto;
		.motif-icon {
			display: flex;
			align-items: center;
			justify-content: center;
			svg {
				width: var(--px-24) !important;
				height: var(--px-24) !important;
				padding: var(--px-2);
				fill: var(--neutrals-700);
			}
		}
		.edit-response-Text {
			display: inline-flex;
			align-items: center;
			justify-content: center;
			margin-left: var(--px-5);
			color: var(--blue-600);
			cursor: pointer;
			max-width: calc(100% - var(--px-25));
			width: auto;
		}
	}
	.motif-loader-container.inlineView {
		position: absolute;
	}
	.motif-modal-body {
		height: 100%;
	}
	.motif-modal-footer {
		position: relative;
		bottom: 0;
		width: 100%;
	}
	.confirmModel {
		display: flex;
		padding: var(--px-20);
		font-size: var(--px-16);
		color: var(--neutrals-900);
	}
	.k-state-disabled {
		display: none;
	}
	.SectionBodyDescriptionWrapper {
		margin: 0 0 var(--px-20) var(--px-10);
		border: var(--px-1) solid var(--neutrals-300);
		&.SectionBodyAresDescriptionWrapper {
			width: calc(100% - var(--px-10)) !important;
			.section-freeText-ares {
				width: 100%;
			}
			.Ares-edit-ResponseWrapper {
				.edit-response-link {
					justify-content: flex-end;
					.motif-icon {
						display: flex;
						align-items: center;
					}
					.ellipses {
						max-width: var(--px-100);
						min-width: auto;
						width: auto;
					}
				}
			}
		}
	}
	.edit-response-link {
		color: var(--blue-600);
		cursor: pointer;
		position: absolute;
		bottom: var(--px-10);
		margin-left: calc(var(--px-120) * -1);
		float: right;
		width: var(--px-120);
		font-weight: bold;
		display: flex;
		.motif-icon {
			display: flex;
			align-items: center;
		}
		svg {
			height: var(--px-23) !important;
			width: var(--px-15) !important;
			fill: var(--blue-600);
			margin-right: var(--px-5);
		}
		.ellipses {
			max-width: var(--px-100);
		}
	}
	.max-count-exceeded {
		bottom: var(--px-10);
		position: absolute;
		display: flex;
		align-items: center;
		line-height: var(--px-20);
		font-weight: bold;
		color: var(--red-600);
		.motif-icon {
			margin-right: var(--px-5);
			svg {
				fill: var(--red-600);
				width: var(--px-20);
				height: var(--px-20);
				path {
					fill: var(--red-600);
				}
			}
		}
	}
`;
const StyledEditorBody = styled.section`
	height: 100%;
	.editResponse-model {
		.motif-modal-body {
			height: 80vh;
		}
		.motif-modal-footer {
			padding: var(--px-20);
		}
		.motif-modal-content {
			min-height: 90%;
			min-width: 100%;
		}
	}
	.edit-response-body {
		height: 100%;
		display: flex;
		.edit-response-wrapper {
			height: 100%;
			width: 100%;
			display: flex;
			flex-direction: column;
			.TopSection {
				display: flex;
				margin-bottom: var(--px-24);
				margin-top: var(--px-8);
				.descriptionWrapper {
					display: flex;
					width: 85%;
					flex-direction: column;
					.createRichTextElementSpanWrapper {
						max-height: var(--px-110) !important;
						font-size: var(--px-14);
						font-weight: 300;
						line-height: var(--px-22);
						p {
							margin-top: 0;
						}
					}
					.confirmModalDescGuidanceWapper {
						display: flex;
						align-items: center;
						font-size: var(--px-14) !important;
						font-weight: 300;
						line-height: var(--px-22);
						.guidanceIcon {
							.motif-icon {
								height: 100%;
								display: flex;
								align-items: center;
							}
						}
					}
					.edit-response-desc {
						display: inline-flex;
						width: fit-content;
						line-height: var(--px-20);
						font-size: var(--px-12) !important;
						font-weight: 700;
						cursor: pointer;
						color: var(--blue-600);
						&:hover {
							text-decoration: underline;
						}
					}
				}
				.ShowHideCommentContainer {
					display: inline-flex;
					width: 15%;
					margin-left: auto;
					align-items: flex-start;
					padding-left: var(--px-20);
					.motif-button {
						margin-bottom: var(--px-10);
						height: var(--px-40);
					}
					.motif-form-field {
						margin-left: auto;
						height: auto !important;
						.motif-toggle-switch-wrapper {
							justify-content: flex-end;
						}
					}
					.motif-toggle-switch-on-text,
					.motif-toggle-switch-off-text {
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
						width: auto;
						max-width: var(--px-150);
						margin: 0;
					}
				}
			}
			.BottomSection {
				display: flex;
				height: calc(100% - var(--px-30));
				overflow: hidden;
				.KendoEditorWrapper {
					display: inline-flex;
					width: 56%;
					display: inline-flex;
					height: 100%;
					&.CommentsNotVisible {
						width: 100%;
						& + .CommentsContainerWrapper {
							width: 0;
						}
					}
					&.CommentsVisible {
						width: calc(100% - 44%);
						.form-group {
							margin-bottom: 0;
						}
						.k-widget.k-editor {
							height: 100%;
							resize: none;
						}
					}
					.form-group {
						&.rich-text-kendo-editor {
							width: 100%;
							height: 100%;
							.k-editor {
								&.k-widget {
									.k-widget.k-toolbar {
										&::before {
											content: unset;
										}
										.k-button-group {
											.k-icon-button {
												&.k-button-md {
													padding: var(--px-4) !important;
												}
											}
											.k-input-md .k-input-inner,
											.k-picker-md .k-input-inner {
												padding: var(--px-4) var(--px-16) !important;
											}
											.k-input-md.k-icon-picker .k-input-inner,
											.k-picker-md.k-icon-picker .k-input-inner {
												padding: var(--px-4) !important;
											}
											.comment-button {
												padding: var(--px-6) !important;
												.add-comments {
													height: var(--px-18) !important;
													svg {
														width: var(--px-18) !important;
														height: var(--px-18) !important;
													}
													.addCommentsLabelTxt {
														font-size: var(--px-14);
														line-height: var(--px-22);
													}
												}
											}
										}
									}
									.k-editor-content {
										.k-content {
											& > p {
												margin: 0 !important;
											}
										}
									}
								}
							}
						}
					}
					table.k-widget.k-editor.k-header.k-editor-widget {
						height: 100%;
						background-color: var(--neutrals-100) !important;
						.k-editor-toolbar > li {
							min-width: var(--px-24);
						}
						.k-editor-toolbar .k-tool {
							width: auto;
							height: var(--px-24);
							line-height: var(--px-24);
							border-color: var(--neutrals-100);
						}
					}
					table.k-editor {
						height: 100%;
						.k-editor-toolbar > li {
							min-width: var(--px-24);
						}
						.k-editor-toolbar .k-tool {
							width: auto;
							height: var(--px-24);
							line-height: var(--px-24);
						}
					}
					.k-editor {
						height: 100%;
						resize: none;
						.k-editor-toolbar > li {
							min-width: var(--px-24);
						}
						.k-editor-toolbar .k-tool {
							width: auto;
							height: var(--px-24);
							line-height: var(--px-24);
						}
					}
				}
				.EngagementCommentsBox {
					display: inline-flex;
					width: 100%;
					height: 100%;
					padding-left: var(--px-20);
					.EngagementCommentsViewBox {
						width: 100%;
						min-height: unset;
						.EngagemnentCommentsChartContainer {
							width: 100%;
							.EngagementCommentHeaderContainer {
								padding: var(--px-10) var(--px-20);
								.ChartToggleFilter {
									top: var(--px-10);
								}
							}
							.EngagementCommentBody {
								.engagementCommentBodyCommentWrapper {
									.comment-body {
										padding: 0;
										.StyledMiddlePane {
											padding: 0;
											.renderVoiceNoteWrapper {
												padding: 0;
											}
										}
									}
									.comment-footer {
										.comment-create-date {
											.StyledEllipse {
												width: auto;
											}
										}
									}
								}
							}
						}
						.tabBarItemWrapper {
							display: flex;
							.motif-badge {
								min-width: auto;
							}
						}
					}
				}
				.motif-loader.motif-loader-container.inlineView.LoadingIndicator {
					position: absolute;
				}
				.EngagementCommentsViewBox {
					min-height: unset;
				}
			}
		}
	}
`;

const StyledNoDataPlaceholder = styled.section`
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: inherit;
	max-height: inherit;
	align-content: center;
	color: var(--neutrals-700);
	span {
		font-size: var(--px-16);
		text-align: center;
		a {
			font-weight: bold;
			cursor: pointer;
		}
	}
`;
