import React, {useEffect, useState} from 'react';
import CreateEditMestModal from '@ey/canvascoreservices/CreateEditMestModal';
import {useDispatch, useSelector} from 'react-redux';
import {currentResource} from '../../../util/utils';
import {
	clearMultiEntity,
	createSTEntity,
	getMultiEntityById,
	updateSTEntity
} from '../../../actions/MultiEntities/stEntitiesactions';
import {serviceURL} from '../../../util/uiconstants';
import Env from '../../../util/env';
import {clearErrors} from '../../../actions/erroractions';
import Utility from '../../../util/utilityfunctions';
import {setIsModalOpen} from '../../../actions/setismodalopen';
import ErrorSummary from '../ErrorSummary/ErrorSummary';
import {setFormV2ToasterVisibility} from '../../../actions/toastactions';
import {getStEntitiesPrimaryEntity} from '../../../selectors/Ares/selectors';
//import CreateEditMestModal from './CreateEditMestModal/src';

export default function CreateEditMestModalWrapper(props) {
	const {
		isCreate,
		isEdit,
		showModal,
		setShowModal,
		entityId,
		onClose,
		isManageEntities,
		onEntityCreated,
		onEntityModified,
		entities,
		hasContentUpdateRole,
		getAllAction
	} = props;
	const [engagementId, multiEntity] = useSelector((state) => [state.engagementId, state.multiEntity]);
	const baseURL = Env.getBaseURL(serviceURL, engagementId);
	const dispatch = useDispatch();
	const primaryEntity = useSelector((state) => getStEntitiesPrimaryEntity(state));
	const [hasChanges, setHasChanges] = useState(false);

	useEffect(() => {
		const handleModalOpen = () => {
			dispatch(setFormV2ToasterVisibility(false));
			dispatch(clearErrors());
			dispatch(setIsModalOpen(true));
		};

		const handleModalClose = () => {
			if (!isManageEntities) {
				dispatch(setIsModalOpen(false));
			}
			dispatch(clearErrors());
		};

		if (showModal) {
			handleModalOpen();
		}

		return () => {
			handleModalClose();
		};
	}, []);

	const actionProps = {};
	if (isManageEntities) {
		actionProps.isManageEntities = isManageEntities;
	}

	const handleOnEntityCreated = (entity) => {
		let promise = Promise.resolve();

		if (onEntityCreated) {
			promise = onEntityCreated(entity);
		}
		return promise;
	};

	const handleOnEntityEdited = (entity) => {
		let promise = Promise.resolve();

		if (onEntityModified) {
			promise = onEntityModified(entity);
		}
		return promise;
	};

	const onCreateEntity = (entity) => {
		setHasChanges(true);

		return createSTEntity(`${baseURL}MultiEntity`, entity, actionProps);
	};

	const onEditEntity = (entity) => {
		setHasChanges(true);

		return updateSTEntity(`${baseURL}MultiEntity/${entityId}`, entity, actionProps);
	};

	const handleOnClose = () => {
		let promise = Promise.resolve();
		if (hasChanges) {
			if (isEdit) {
				promise = dispatch(clearMultiEntity())
					.then(() => dispatch(clearErrors()))
					.then(() => handleOnEntityEdited(multiEntity));
			} else if (isCreate) {
				promise = handleOnEntityCreated(multiEntity);
			}
		} else {
			promise = dispatch(clearMultiEntity()).then(() => dispatch(clearErrors()));
		}

		promise = promise.then(() => {
			onClose && onClose();
		});

		return promise;
	};

	return (
		<>
			{isCreate && (
				<CreateEditMestModal
					engagementId={Number(engagementId)}
					showModal={showModal}
					setShowModal={setShowModal}
					createEntityAction={onCreateEntity}
					labels={currentResource.CreateEditMestLabels}
					onClose={handleOnClose}
					theme={Utility.getTheme()}
					errorComponent={<ErrorSummary />}
					hasContentUpdateRole={hasContentUpdateRole}
					primaryEntity={primaryEntity}
					getAllAction={getAllAction}
					toastAction={setFormV2ToasterVisibility}
				/>
			)}
			{isEdit && (
				<CreateEditMestModal
					engagementId={Number(engagementId)}
					showModal={showModal}
					setShowModal={setShowModal}
					labels={currentResource.CreateEditMestLabels}
					multiEntityDetails={multiEntity}
					entityId={entityId}
					onClose={handleOnClose}
					editEntityAction={onEditEntity}
					getSTEntityById={() => getMultiEntityById(Number(engagementId), Number(entityId), actionProps)}
					theme={Utility.getTheme()}
					errorComponent={<ErrorSummary />}
					hasContentUpdateRole={hasContentUpdateRole}
					primaryEntity={primaryEntity}
					getAllAction={getAllAction}
					toastAction={setFormV2ToasterVisibility}
				/>
			)}
		</>
	);
}
