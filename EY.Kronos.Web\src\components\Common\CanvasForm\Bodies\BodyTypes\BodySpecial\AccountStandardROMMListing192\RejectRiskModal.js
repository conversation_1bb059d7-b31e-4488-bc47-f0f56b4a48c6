import React, {useState} from 'react';
import {useDispatch} from 'react-redux';
import styled from 'styled-components';
import Modal from '../../../../../../../components/Common/Modal/Modal';
import {currentResource} from '../../../../../../../util/utils';
import EllipseControl from '../../../../../../../components/Common/EllipsesControl/EllipsesControl';
import MotifSelect from '@ey/motif-react-wrapper/Select';
import MotifOption from '@ey/motif-react-wrapper/Option';
import FormField from '../../../../../../../components/Common/BaseComponentWrappers/MotifFormField';
import Label from '../../../../../../../components/Common/BaseComponentWrappers/MotifLabel';
import Utility from '../../../../../../../util/utilityfunctions';
import TextAreaEditorWrapper from '../../../../../../../components/Common/TextAreaEditor/TextAreaEditor';
import MotifErrorMessage from '@ey/motif-react-wrapper/ErrorMessage';
import {patchAcceptAndRejectRisk} from '../../../../../../../actions/Risk/riskrejectactions';
import {useEngagementId} from '../../../../../../../util/customHooks';
import Loader from '../../../../../../../components/Common/LoadingIndicator/LoadingIndicator';
import RiskDetails from '../../../../../../../components/Common/RiskDetails/RiskDetails';
import { riskSubTypes } from '../../../../../../../util/uiconstants';

export default function RejectRiskModal(props) {
	const {setShowRejectRiskModal, riskDetails, onRejectAndAcceptRisks, accountStandardROMMListingLabels} = props;
	const dispatch = useDispatch();
	const engagementId = useEngagementId();
	const [rejectRiskModalData, setRejectRiskModalData] = useState({
		rejectionCategory: null,
		rejectionRationale: ''
	});
	const [error, setError] = useState({
		rejectionCategory: '',
		rejectionRationale: ''
	});
	const [showLoader, setShowLoader] = useState(false);

	const handleChange = (val, inputName) => {		
		let value = val.currentTarget ? val.currentTarget.value : val;

		// If the input is for rejection category, map label to id
				
		if (inputName === 'rejectionCategory') {
	         value = currentResource.RejectionCategory.find(
		      (item) => item.label === value
	         ) ?? value;
		     value = value.id ? { id: value.id, label: value.label } : value;
		}


		setRejectRiskModalData({
			...rejectRiskModalData,
			[inputName]: value
		});
	};

	const onRejectionRationale = () => {
		if (riskDetails.riskSubTypeId !== riskSubTypes.SubTypePreRejectedStandardROMM) {
			const isRationaleEmpty =
				!rejectRiskModalData.rejectionRationale ||
				rejectRiskModalData.rejectionRationale.trim().length === 0;
			const isCategoryEmpty = !rejectRiskModalData.rejectionCategory;

			if (isRationaleEmpty || isCategoryEmpty) {
				setError({
					rejectionRationale: isRationaleEmpty ? accountStandardROMMListingLabels.required : '',
					rejectionCategory: isCategoryEmpty ? accountStandardROMMListingLabels.required : ''
				});
				return;
			}
		}

		setShowLoader(true);
		let patchRiskData = {};
        if(riskDetails.riskSubTypeId === riskSubTypes.SubTypePreRejectedStandardROMM) {
            // If the risk is pre-rejected, we only patch or edit the  additional context i.e rejection rationale 
            patchRiskData = {
                isRejected: true,
				riskSubTypeId: riskDetails.riskSubTypeId,
            };
        } else {
            // For other risk subtypes, we patch rejection rationale and category
            patchRiskData = {
                isRejected: true,
				riskSubTypeId: riskDetails.riskSubTypeId,
                rationale: rejectRiskModalData.rejectionRationale,
                rejectionTypeId: rejectRiskModalData.rejectionCategory?.id,
            };
        }
 
		dispatch(
			patchAcceptAndRejectRisk(
				engagementId,
				riskDetails.id,
				patchRiskData,
			)
		)
			.then(() => {
				onRejectAndAcceptRisks();
				setShowRejectRiskModal({ show: false });
			}).catch(() => {
				// setting error state to empty strings to clear any previous errors i.e required messages
				setError({
					rejectionRationale:  '',
					rejectionCategory:  ''
				});

			})
			.finally(() => {
				setShowLoader(false);
			});
	};

	const renderRejectionCategorySelect = (options, value, disabled, onChange) => (
		<MotifSelect value={value.label} disabled={disabled} onChange={onChange} key="rejectionCategory">
			{options.length > 0 ? (
				options.map((option) => (
					<MotifOption className="ellipsis" key={option.id} label={option.label} value={option.label}>
						<EllipseControl content={option.label} tooltip={option.label} noOfLines={1} />
					</MotifOption>
				))
			) : (
				<MotifOption value="" label="" />
			)}
		</MotifSelect>
	);

	const rejectionCategoryOptions = () => {
		if (riskDetails?.riskSubTypeId === riskSubTypes.SubTypePreRejectedStandardROMM) {
			const selected = currentResource.RejectionCategory.find((item) => item.id === riskDetails.rejectionTypeId);
			return renderRejectionCategorySelect(
				selected ? [selected] : [],
				selected || '',
				selected ? true : false,
				undefined
			);
		} else {
			const options = Object.values(currentResource.RejectionCategory);
			return renderRejectionCategorySelect(
				options,
				rejectRiskModalData.rejectionCategory || '',
				false,
				(ev) => handleChange(ev, 'rejectionCategory')
			);
		}
	};

	return (
		<Modal
			title={accountStandardROMMListingLabels.rejectionRationaleLabel}
			modalContainerClass="reject-risk-modal"
			onHide={() => {
				setShowRejectRiskModal({show: false});
			}}
			show
			showButtons
			modalsize="small"
			takeover
			onOkClick={() => {
				onRejectionRationale();
			}}
			confirmBtnTitle={accountStandardROMMListingLabels.rejectLabel}
			closeBtnTitle={accountStandardROMMListingLabels.cancel}
			closeTitle={accountStandardROMMListingLabels.cancel}
			modalbodystyle="modalBodyFlexHieght nobodyOverflow"
			isOkButtonDisabled={showLoader}
			isCancelButtonDisabled={showLoader}
			disableClose={showLoader}
		>
			<StyledRiskRejectionModal>
				{showLoader ? (
					<Loader show size="sm" styleName="LoadingIndicator" />
				) : (
					<>
						<section className="reject-risk-modal-placeholder motif-body1-default-light">
							{Utility.createRichTextElement(accountStandardROMMListingLabels.rejectionRationalePlaceholder)}
						</section>
						<section className="risk-name motif-body2-default-regular">
							<RiskDetails risk={riskDetails} isEllipseRequired={true} />
						</section>
						<FormField role="group" className="rejection-category-dropdown">
							<Label position="in">{accountStandardROMMListingLabels.rejectionCategoryDropdownPlaceholder}</Label>
							{rejectionCategoryOptions()}
							{error.rejectionCategory && <MotifErrorMessage role="alert">{accountStandardROMMListingLabels.required}</MotifErrorMessage>}
						</FormField>

						<TextAreaEditorWrapper
							showHeadingLabel
							textAreaEditorHeaderLabel={accountStandardROMMListingLabels.rejectionRationaleTextAreaPlaceholder}
							placeholder={accountStandardROMMListingLabels.rejectionRationaleTextAreaPlaceholder}
							maxLength={4000}
							value={
								riskDetails?.riskSubTypeId === riskSubTypes.SubTypePreRejectedStandardROMM
									? riskDetails?.knowledgeRationale || ''
									: rejectRiskModalData.rejectionRationale
							}
							className="rejection-rationale-textarea"
							isDisabled={riskDetails?.riskSubTypeId === riskSubTypes.SubTypePreRejectedStandardROMM ? true : false}
							onChange={(ev) => handleChange(ev, 'rejectionRationale')}
							textAreaError={error.rejectionRationale.length > 0}
							textAreaErrorMessage={error.rejectionRationale}
							hideClearButton
							textAreaEditorLabel = ''
						/>
					</>
				)}
			</StyledRiskRejectionModal>
		</Modal>
	);
}
const StyledRiskRejectionModal = styled.section`
    .LoadingIndicator {
       margin-top: var(--px-36);
    }
	.reject-risk-modal-placeholder {
		margin-bottom: var(--spacing-x-large);
		padding-bottom: var(--spacing-x-large);
		border-bottom: var(--px-1) solid var(--neutrals-100);
	}
	.rejection-category-dropdown {
		padding-bottom: var(--spacing-x-large);
		height: var(--px-68);
	}
	.risk-name {
		margin-bottom: var(--spacing-x-large);
		.risk-item {
			margin-left: 0;
			padding: 0;
			.risk-name-section {
				width: 100%;
				.RiskNameWrapper {
					flex: 1;
					min-width: var(--px-0);
					font-weight: var(--fw-regular);
				}
				.riskType-icon {
					width: auto;
				}
			}
		}
	}
	.motif-text-area {
		height: var(--px-200);
		@media screen and (min-width: 1845px) {
		   height: var(--px-375);
		}
	}
	.motif-textarea-support-wrapper {
		display: none;
	}
`;
