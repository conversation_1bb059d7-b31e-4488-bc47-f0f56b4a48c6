import {ITProcessTypes} from '../../../util/uiconstants';
const mapSupportingProcesses = ({itProcessID, itProcessName, itProcessTypeID}) => ({
	id: itProcessID,
	name: itProcessName,
	category: ITProcessTypes[itProcessTypeID]
});

export default (response) => {
	const {supportedITProcesses = [], ...data} = response?.data?.[0] ?? {};
	return {
		...data,
		supportedITProcesses: supportedITProcesses?.map(mapSupportingProcesses)
	};
};
