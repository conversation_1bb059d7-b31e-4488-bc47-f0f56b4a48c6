/**
 * Created by calhosh on 4/14/2017.
 */

import envConfig from '@ey/voltron-config';
export const serviceURL = 'serviceUrl';
export const ENGAGEMENTMETADATA_URL = '/engagementmetadata.json';
export const HELIX_PROJECT_FIT = `${envConfig.url.Helix_Analyzer_Base_Url}/`;
export const emptyGuid = '00000000-0000-0000-0000-000000000000';
export const droppedDocumentCount = 'droppedDocumentCount';

export const numberFormats = [
	{id: 0, value: '1,234,567,890', label: 'numbericStlye1', title: '1,234,567,890', decimalSeparator: '.'},
	{id: 1, value: '1,23,45,67,890', label: 'numbericStlye7', title: '1,23,45,67,890', decimalSeparator: '.'},
	{id: 2, value: '1 234 567 890', label: 'numbericStlye2', title: '1 234 567 890', decimalSeparator: '.'},
	{id: 3, value: '1.234.567.890', label: 'numbericStlye3', title: '1.234.567.890', decimalSeparator: ','}
];

export const features = {
	CREATE_ALRA_PACKAGE: 'CREATE_ALRA_PACKAGE',
	ALRA_PACKAGE_BULK: 'ALRA_PACKAGE_BULK',
	ALRA_PACKAGE_IN_PROGRESS: 'ALRA_PACKAGE_IN_PROGRESS',
	INVOLVEMENT_PACKAGE_IN_PROGRESS: 'INVOLVEMENT_PACKAGE_IN_PROGRESS'
};

export const RichTextAvailableColors = ['#000000', '#ff4136', '#189d3e', '#ffe600', '#ff6D00', '#0070c0'];

export const calledFrom = {
	documentControl: 'CALLED_FROM_DOCUMENT_CONTROL',
	footerControl: 'CALLED_FROM_FOOTER_CONTROL',
	contextMenu: 'CALLED_FROM_CONTEXT_MENU'
};

/*Rounding Options*/
export const roundingOptions = [
	{value: 'none', label: 'none'},
	{value: 'thousands', label: 'In thousands'},
	{value: 'millions', label: 'In millions'},
	{value: 'lakhs', label: 'In lakhs'},
	{value: 'crores', label: 'In crores'}
];

/** Voltron icon names. */
export const voltronIconNames = ['hide-annotations', 'histroyTrackChanges'];

export const AssertionIds = {
	// StatementTypeId : { AssertionName : AssertionId }
	2: {
		Completeness: 1,
		Existence: 2,
		Valuation: 3,
		RightsAndObligations: 4,
		PresentationAndDisclosure: 5
	},
	1: {
		Completeness: 6,
		Occurrence: 7,
		Measurement: 8,
		PresentationAndDisclosure: 9
	},
	3: {
		Completeness: 10,
		ExistenceOccurrence: 11,
		MeasurementValuation: 12,
		RightsAndObligations: 13,
		PresentationAndDisclosure: 14
	}
};

export const assertionsFormat = [
	{id: 1, assertionname: 'Completeness', assertionabbreviation: 'C', statementtypeid: 2, displayorder: 1},
	{id: 2, assertionname: 'Existence', assertionabbreviation: 'E', statementtypeid: 2, displayorder: 2},
	{id: 3, assertionname: 'Valuation', assertionabbreviation: 'V', statementtypeid: 2, displayorder: 3},
	{id: 4, assertionname: 'Rights and Obligations', assertionabbreviation: 'R&O', statementtypeid: 2, displayorder: 4},
	{
		id: 5,
		assertionname: 'Presentation and Disclosure',
		assertionabbreviation: 'P&D',
		statementtypeid: 2,
		displayorder: 5
	},
	{id: 6, assertionname: 'Completeness', assertionabbreviation: 'C', statementtypeid: 1, displayorder: 6},
	{id: 7, assertionname: 'Occurrence', assertionabbreviation: 'O', statementtypeid: 1, displayorder: 7},
	{id: 8, assertionname: 'Measurement', assertionabbreviation: 'M', statementtypeid: 1, displayorder: 8},
	{
		id: 9,
		assertionname: 'Presentation and Disclosure',
		assertionabbreviation: 'P&D',
		statementtypeid: 1,
		displayorder: 9
	},
	{id: 10, assertionname: 'Completeness', assertionabbreviation: 'C', statementtypeid: 3, displayorder: 10},
	{id: 11, assertionname: 'Existence/Occurrence', assertionabbreviation: 'E/O', statementtypeid: 3, displayorder: 11},
	{id: 12, assertionname: 'Measurement/Valuation', assertionabbreviation: 'M/V', statementtypeid: 3, displayorder: 12},
	{id: 13, assertionname: 'Rights and Obligations', assertionabbreviation: 'R&O', statementtypeid: 3, displayorder: 13},
	{
		id: 14,
		assertionname: 'Presentation and Disclosure',
		assertionabbreviation: 'P&D',
		statementtypeid: 3,
		displayorder: 14
	}
];

export const assertionNameMapper = {
	MeasurementValuation: {
		1: 'Measurement',
		2: 'Valuation',
		3: 'MeasurementValuation'
	},
	ExistenceOccurrence: {
		1: 'Occurrence',
		2: 'Existence',
		3: 'ExistenceOccurrence'
	},
	RightsAndObligations: {
		// in case of R&O  in income statement, Api is handlinng  to change incomestatement to both
		1: 'RightsAndObligations',
		2: 'RightsAndObligations',
		3: 'RightsAndObligations'
	},
	PresentationAndDisclosure: {
		1: 'PresentationAndDisclosure',
		2: 'PresentationAndDisclosure',
		3: 'PresentationAndDisclosure'
	},
	Completeness: {
		1: 'Completeness',
		2: 'Completeness',
		3: 'Completeness'
	}
};

export const RightsAndObligations = 'RightsAndObligations'; // This is used in the UI to display Rights and Obligations in the assertion modal
/*window widths*/
export const WIDTH_9 = 9;
export const WIDTH_12 = 12;
export const WIDTH_13 = 13;
export const WIDTH_30 = 30;
export const WIDTH_40 = 40;
export const WIDTH_45 = 45;
export const WIDTH_50 = 50;
export const WIDTH_55 = 55;
export const WIDTH_58 = 58;
export const WIDTH_60 = 60;
export const WIDTH_70 = 70;
export const WIDTH_80 = 80;
export const WIDTH_90 = 90;
export const WIDTH_100 = 100;

export const dateFormat = 'YYYY-MM-DD';
export const dateTimeFormat = 'YYYY-MM-DD HH:mm:ss';

export const dateFormats = [
	{id: 0, value: 'MM/DD/yyyy', label: 'MM/dd/yyyy', momentLocale: 'MM/DD/YYYY'},
	{id: 1, value: 'DD/MM/yyyy', label: 'dd/MM/yyyy', momentLocale: 'DD/MM/YYYY'},
	{id: 2, value: 'yyyy/MM/DD', label: 'yyyy/MM/dd', momentLocale: 'YYYY/MM/DD'}
];

export const defaultDateFormat = 0;

export const defaultNumberFormat = 0;

export const minimunDate = '0001-01-01T00:00:00Z';

export const displayLanguages = [
	{displayLanguageId: 1, label: 'English', value: 'en-us'},
	{displayLanguageId: 4, label: 'Français', value: 'fr-ca'},
	{displayLanguageId: 5, label: 'Español', value: 'es'},
	{displayLanguageId: 7, label: 'Português', value: 'pt-br'},
	{displayLanguageId: 8, label: '日本語', value: 'ja'},
	{displayLanguageId: 9, label: '简体中文', value: 'zh-cn'},
	{displayLanguageId: 10, label: '繝體中文', value: 'zh-tw'},
	{displayLanguageId: 11, label: 'עברית', value: 'he'},
	{displayLanguageId: 12, label: '한국어', value: 'ko'},
	{displayLanguageId: 13, label: 'Pуѝѝкий', value: 'ru'},
	{displayLanguageId: 14, label: 'عربى', value: 'ar'}
];

export const minorErrorCodes = [1001];

export const formRoutes = {
	canvasform: '/canvasform',
	independence: '/independence',
	profile: '/profile',
	snapshot: '/canvasform/changes',
	details: '/canvasform/details'
};

export const smartScreenRoutes = {
	sem: '/smartscreens/sem',
	accountConclusion: '/smartscreens/accountconclusion',
	fourFortyGl: '/smartscreens/440gl',
	independence: '/smartscreens/independence'
};

export const dataSortingOrder = {
	knowledgeDisplayOrder: 'knowledgeDisplayOrder',
	name: 'name',
	label: 'label',
	customLabel: 'customLabel',
	isCustom: 'isCustom',
	documentName: 'documentName'
};

// Possible sort orders for tasks getAll API
export const taskSortingOrder = {
	knowledge: 'knowledge',
	name: 'taskName',
	dueDate: 'dueDate',
	none: 'none'
};

export const documentSortingOrder = {
	groupActivity: 'groupActivity',
	documentName: 'documentName',
	fileSize: 'fileSize'
};

/*This object hold area information to track on-going promises. used in react-promise-tracker*/
export const promiseAreas = {
	MILESTONE_DETAILS_AREA: 'MILESTONE_DETAILS_AREA',
	QUICK_LINK_AREA: 'QUICK_LINK_AREA',
	REVIEWNOTE_SUMMARY_AREA: 'REVIEWNOTE_SUMMARY_AREA',
	USERROLE_SELECTION_AREA: 'USERROLE_SELECTION_AREA',
	TASK_LIST_AREA: 'TASK_LIST_AREA',
	ENGAGEMENT_METRICS_AREA: 'ENGAGEMENT_METRICS_AREA',
	TASK_DETAILS_AREA: 'TASK_DETAILS_AREA',
	GROUP_AUDIT_TASK_AREA: 'GROUP_AUDIT_TASK_AREA',
	BUILD_TASK_LIST: 'BUILD_TASK_LIST',
	ITPROCESS_UNRELATED_ITGC_COUNT: 'ITPROCESS_UNRELATED_ITGC_COUNT',
	FIT_ACTIVITY_ICONS_SIGNOFFS: 'FIT_ACTIVITY_ICONS_SIGNOFFS',
	FIT_BREADCRUMB: 'FIT_BREADCRUMB',
	RISK_GET_BY_ID_AREA: 'RISK_GET_BY_ID_AREA'
};

/*User Lookup Type*/
export const UserLookupType = {
	NotSpecified: 0,
	Internal: 1,
	External: 2,
	ThirdPartyUser: 3
};

/*User ERP TypeId*/
export const UserERPTypeId = {
	NotSpecified: 0,
	Internal: 1,
	External: 2,
	Both: 3,
	ThirdPartyUser: 4
};

export const DocumentChangeTypes = {
	NonAdministrativeChange: 1,
	AcceptingRevisions: 2,
	AdditionalCrossReferencing: 3,
	AddingOriginalConfirmation: 4,
	CosmeticChange: 5,
	CompletingFormAP: 6,
	DeletingDiscardingDocumentation: 7,
	PreparingManagementLetter: 8,
	SignOffCompletionChecklist: 9,
	SortingCollatingCrossRefDoc: 10
};

/*Enum for Document Type complete list*/
export const documentTypes = {
	UNDEFINED: 0,
	EVIDENCEDOCUMENT: 1,
	CRASHDOCUMENT: 2,
	PRIORYEAREVIDENCEDOCUMENT: 3,
	CLIENTFILESDOCUMENT: 4,
	SNAPSHOTDOCUMENT: 5,
	SMARTFORMDOCUMENT: 6,
	EVIDENCE: 7,
	CRASH: 8,
	PRIORYEAREVIDENCE: 9,
	CLIENTFILES: 10,
	SNAPSHOT: 11,
	PROFILE: 12,
	KEYRISKIDENTIFICATION: 13,
	KEYRISKIDENTIFICATIONUBT: 14,
	ENTITYLEVELCONTROLSSUMMARY: 15,
	MANAGEMENTPROCEDURESSUMMARY: 16,
	DETERMINEPMTESAD: 17,
	ACCOUNTSSUMMARY: 18,
	SCOTSSUMMARY: 19,
	ITSOSUMMARY: 20,
	CRASUMMARY: 21,
	AUDITPLAN: 22,
	EVALUATEAUDITRESULTS: 23,
	CONFIRMFINALMATERIALITY: 24,
	SUMMARYOFAUDITDIFFERENCES: 25,
	ACCOUNTCONCLUSIONCLASSIC: 26,
	LEADSCHEDULE: 27,
	ARCHIVE: 28,
	PAPERPROFILEDOCUMENT: 29,
	PAPERPROFILE: 30,
	GROUPAUDITSTRUCTURESUMMARY: 31,
	GROUPAUDITINSTRUCTIONSUMMARY: 32,
	GROUPCOMPONENTINSTRUCTIONDOCUMENT: 33,
	OARINTERIMCOMPARISON: 34,
	OARCURRENTYEARCOMPARISON: 35,
	GROUPCOMPONENTSUMMARYDOCUMENT: 36,
	RETURNEDDOCUMENT: 37,
	WORKPLANSUMMARY: 38,
	TIEOUT: 39,
	LEADSCHEDULETEMPORARYSMARTFORM: 40,
	OARINTERIMCOMPARISONTEMPORARYSMARTFORM: 41,
	OARCURRENTYEARCOMPARISONTEMPORARYSMARTFORM: 42,
	TIEOUTTEMPORARYSMARTFORM: 43,
	EYSENTTOCLIENTFILES: 44,
	ITEVALUATIONSMARTFORM: 45,
	ITEVALUATIONTEMPORARYSMARTFORM: 46,
	BULKDOCUMENTEXPORT: 47,
	CANVASFORM: 48,
	CANVASFORMEVIDENCE: 49,
	CANVASFORMTEMPORARY: 50,
	AUTOMATIONPARENT: 51,
	AUTOMATIONHUBINPUT: 52,
	AUTOMATIONHUBOUTPUT: 53,
	CONTROLFORM: 54,
	INDEPENDENCEFORMTEMPLATE: 56,
	EVIDENCENOTDISPLAYED: 57,
	INDEPENDENCEFORMINDIVIDUAL: 58,
	AUTOMATIONHUBCLIENTOUTPUT: 65,
	CONTENT_UPDATE_IN_PROGRESS_CANVAS_FORM: 68,
	SEM: 69,
	ACCOUNT_CONCLUSION: 70,
	FOUR_FORTY_GL: 71,
	SMARTWORKINGPAPER_PARENT: 72,
	SMARTWORKINGPAPER: 73,
	PAPERPROFILETEMPORARY: 74,
	PROFILEV2: 76,
	LEGACYBITMAPREFERENCE: 77,
	CANVASFORM_BODYIMAGE: 78,
	GUIDEDWORKFLOWTEMPORARY: 79,
	GUIDEDWORKFLOW: 80,
	LIMITEDRISKDOCUMENTATION: 81,
	GUIDEDWORKFLOWV2: 82,
	COPY_CANVASFORMINPROGRESS: 83,
	HELIXSCREENSHOT: 84,
	COPY_DOCUMENT_IN_PROGRESS: 85,
	IT_FLOW_ISA315: 86,
	BULKEXPORTCANVASFORM_NOTDISPLAYED: 87,
	EVIDENCE_NOTDISPLAYED_REQUIRESAPPROVAL: 88,
	MANAGE_TEAM: 89,
	EVIDENCE_NOTARCHIVED: 90,
	SMARTAUTOMATIONEVIDENCE: 91,
	SMARTAUTOMATIONEVIDENCETEMPORARY: 92,
	ESTIMATESDOCUMENT: 93,
	ANALYTICSDOCUMENT: 94,
	MULTIENTITYENGAGEMENTSUBPROFILE: 95,
	MULTIENTITYINDIVIDUALPROFILE: 96,
	RECORDING: 97,
	VOICE_RECORDING: 98,
	SYSTEMDIAGNOSTIC: 99,
	HELIXIMPORT: 100,
	COPYKNOWLEDGEDELIVEREDEVIDENCE: 101,
	SCREEN_RECORD: 102,
	ACCOUNTLEVELRISKASSESSMENT: 103,
	PIC_CHECKLIST: 104,
	EQR_CHECKLIST: 105,
	GROUPSTRUCTUREGUIDEDWORKFLOW: 106,
	GROUPINSTRUCTIONGUIDEDWORKFLOW: 107,
	GUIDEDWORKFLOWV2_TEMPORARY: 108,
	CANVASFORMSNAPSHOT: 109,
	ALRA_INDIVIDUAL: 110,
	CLIENT_SHARED_HELIX_ANALYTIC: 111,
	GROUP_INVOLVEMENT_INDIVIDUAL_COMPONENT: 112,
	GROUP_INVOLVEMENT_SUMMARY: 113,
	ACCOUNT_STANDARD_WORKING_PAPER: 114,
	ACCOUNT_STANDARD_WORKING_PAPER_TEMPORARY: 115,
	ALRA_INDIVIDUAL_TEMPORARY: 120,
	CONTROL_UNDERSTAND_CONTROLS: 121,
	CONTROL_UNDERSTAND_CONTROLS_TEMPORARY: 122,
	CONTROL_TEST_CONTROL_ATTRIBUTES: 123,
	CONTROL_TEST_CONTROL_ATTRIBUTES_TEMPORARY: 124,
	SCOT_WALKTHROUGH: 126,
	SCOT_WALKTHROUGH_TEMPORARY: 127,
	ESTIMATE_INDIVIDUAL_ESTIMATE_TEMPORARY: 128,
	EVIDENCE_DISPLAYED_NO_APPROVAL_REQUIRED: 129,
	GUIDED_WORKFLOWV2_NOSIGNOFF_REQUIRED: 130,
	GUIDED_WORKFLOWV2_NOSIGNOFF_REQUIRED_TEMPORARY: 131,
	CRA_SUMMARY_V2: 132,
	CRA_SUMMARY_V2_TEMPORARY: 133,
	SCOT_SUMMARYV2: 134,
	SCOT_SUMMARYV2_TEMPORARY: 135,
	ESTIMATE_WALKTHROUGH: 136,
	ESTIMATE_WALKTHROUGH_TEMPORARY: 137,
	IT_APP_PLANNING_INDIVIDUAL: 138,
	IT_APP_PLANNING_INDIVIDUAL_TEMPORARY: 139,
	IT_APP_RISK_ASSESSMENT_INDIVIDUAL: 140,
	IT_APP_RISK_ASSESSMENT_INDIVIDUAL_TEMPORARY: 141,
	IT_PROCESS_WALKTHROUGH_INDIVIDUAL: 142,
	IT_PROCESS_WALKTHROUGH_INDIVIDUAL_TEMPORARY: 143,
	CONTROL_DEFICIENCY_INDIVIDUAL: 144,
	CONTROL_DEFICIENCY_INDIVIDUAL_TEMPORARY: 145,
	REVIEW_AND_APPROVAL_SUMMARY: 147,
	REVIEW_AND_APPROVAL_SUMMARY_TEMPORARY: 148,
	SMALL_ENGAGEMENT_DATA_IMPORT: 149,
	CLIENT_SURVEY_TEMPLATE: 150,
	MULTI_ENTITY_INDIVIDUAL_PROFILE_V2_NO_SIGN_OFF_REQUIRED: 151
};

export const GuidedWorkflowV2DocumentTypes = [
	documentTypes.LIMITEDRISKDOCUMENTATION,
	documentTypes.ACCOUNT_CONCLUSION,
	documentTypes.GUIDEDWORKFLOWV2,
	documentTypes.ESTIMATESDOCUMENT,
	documentTypes.ANALYTICSDOCUMENT,
	documentTypes.MULTIENTITYENGAGEMENTSUBPROFILE,
	documentTypes.MULTIENTITYINDIVIDUALPROFILE,
	documentTypes.ACCOUNTLEVELRISKASSESSMENT,
	documentTypes.PIC_CHECKLIST,
	documentTypes.EQR_CHECKLIST,
	documentTypes.GROUPSTRUCTUREGUIDEDWORKFLOW,
	documentTypes.GROUPINSTRUCTIONGUIDEDWORKFLOW,
	documentTypes.ALRA_INDIVIDUAL,
	documentTypes.GROUP_INVOLVEMENT_INDIVIDUAL_COMPONENT,
	documentTypes.GROUP_INVOLVEMENT_SUMMARY,
	documentTypes.ACCOUNT_STANDARD_WORKING_PAPER,
	documentTypes.ACCOUNT_STANDARD_WORKING_PAPER_TEMPORARY,
	documentTypes.ALRA_INDIVIDUAL_TEMPORARY,
	documentTypes.GUIDEDWORKFLOWV2_TEMPORARY,
	documentTypes.DETERMINEPMTESAD,
	documentTypes.IT_FLOW_ISA315,
	documentTypes.PROFILEV2,
	documentTypes.INDEPENDENCEFORMTEMPLATE,
	documentTypes.INDEPENDENCEFORMINDIVIDUAL,
	documentTypes.CONTROL_UNDERSTAND_CONTROLS,
	documentTypes.CONTROL_UNDERSTAND_CONTROLS_TEMPORARY,
	documentTypes.CONTROL_TEST_CONTROL_ATTRIBUTES,
	documentTypes.CONTROL_TEST_CONTROL_ATTRIBUTES_TEMPORARY,
	documentTypes.SCOT_WALKTHROUGH_TEMPORARY,
	documentTypes.SCOT_WALKTHROUGH,
	documentTypes.ESTIMATE_INDIVIDUAL_ESTIMATE_TEMPORARY,
	documentTypes.SCOT_WALKTHROUGH_TEMPORARY,
	documentTypes.EVIDENCE_DISPLAYED_NO_APPROVAL_REQUIRED,
	documentTypes.GUIDED_WORKFLOWV2_NOSIGNOFF_REQUIRED,
	documentTypes.GUIDED_WORKFLOWV2_NOSIGNOFF_REQUIRED_TEMPORARY,
	documentTypes.CRA_SUMMARY_V2,
	documentTypes.CRA_SUMMARY_V2_TEMPORARY,
	documentTypes.SCOT_SUMMARYV2,
	documentTypes.SCOT_SUMMARYV2_TEMPORARY,
	documentTypes.ESTIMATE_WALKTHROUGH,
	documentTypes.ESTIMATE_WALKTHROUGH_TEMPORARY,
	documentTypes.IT_APP_PLANNING_INDIVIDUAL,
	documentTypes.IT_APP_PLANNING_INDIVIDUAL_TEMPORARY,
	documentTypes.IT_APP_RISK_ASSESSMENT_INDIVIDUAL,
	documentTypes.IT_APP_RISK_ASSESSMENT_INDIVIDUAL_TEMPORARY,
	documentTypes.IT_PROCESS_WALKTHROUGH_INDIVIDUAL,
	documentTypes.IT_PROCESS_WALKTHROUGH_INDIVIDUAL_TEMPORARY,
	documentTypes.CONTROL_DEFICIENCY_INDIVIDUAL,
	documentTypes.CONTROL_DEFICIENCY_INDIVIDUAL_TEMPORARY,
	documentTypes.REVIEW_AND_APPROVAL_SUMMARY,
	documentTypes.REVIEW_AND_APPROVAL_SUMMARY_TEMPORARY,
	documentTypes.MULTI_ENTITY_INDIVIDUAL_PROFILE_V2_NO_SIGN_OFF_REQUIRED
];

export const fileExtensionsEligibleForDaaS = ['docx', 'pptx', 'xlsx', 'xlsb', 'xlsm', 'xltx'];

export const daasOptions = {
	checkout: 'checkout',
	checkin: 'checkin'
};

export const IndependenceAndProfileDocumentTypes = [
	documentTypes.INDEPENDENCEFORMTEMPLATE,
	documentTypes.PROFILEV2,
	documentTypes.INDEPENDENCEFORMINDIVIDUAL
];

export const IndividualFitDocumentTypes = [
	documentTypes.ESTIMATESDOCUMENT,
	documentTypes.ANALYTICSDOCUMENT,
	documentTypes.ALRA_INDIVIDUAL,
	documentTypes.MULTIENTITYINDIVIDUALPROFILE,
	documentTypes.GROUP_INVOLVEMENT_INDIVIDUAL_COMPONENT,
	documentTypes.ALRA_INDIVIDUAL_TEMPORARY,
	documentTypes.IT_APP_PLANNING_INDIVIDUAL,
	documentTypes.IT_APP_PLANNING_INDIVIDUAL_TEMPORARY,
	documentTypes.IT_APP_RISK_ASSESSMENT_INDIVIDUAL,
	documentTypes.IT_APP_RISK_ASSESSMENT_INDIVIDUAL_TEMPORARY,
	documentTypes.IT_PROCESS_WALKTHROUGH_INDIVIDUAL,
	documentTypes.IT_PROCESS_WALKTHROUGH_INDIVIDUAL_TEMPORARY,
	documentTypes.CONTROL_DEFICIENCY_INDIVIDUAL,
	documentTypes.CONTROL_DEFICIENCY_INDIVIDUAL_TEMPORARY
];

export const TemporaryDocumentTypes = [
	documentTypes.LEADSCHEDULETEMPORARYSMARTFORM,
	documentTypes.OARINTERIMCOMPARISONTEMPORARYSMARTFORM,
	documentTypes.OARCURRENTYEARCOMPARISONTEMPORARYSMARTFORM,
	documentTypes.TIEOUTTEMPORARYSMARTFORM,
	documentTypes.ITEVALUATIONTEMPORARYSMARTFORM,
	documentTypes.CANVASFORMTEMPORARY,
	documentTypes.PAPERPROFILETEMPORARY,
	documentTypes.GUIDEDWORKFLOWTEMPORARY,
	documentTypes.SMARTAUTOMATIONEVIDENCETEMPORARY,
	documentTypes.GUIDEDWORKFLOWV2_TEMPORARY,
	documentTypes.ACCOUNT_STANDARD_WORKING_PAPER_TEMPORARY,
	documentTypes.IT_APP_PLANNING_INDIVIDUAL_TEMPORARY,
	documentTypes.IT_APP_RISK_ASSESSMENT_INDIVIDUAL_TEMPORARY,
	documentTypes.IT_PROCESS_WALKTHROUGH_INDIVIDUAL_TEMPORARY,
	documentTypes.CONTROL_DEFICIENCY_INDIVIDUAL_TEMPORARY
];

export const DocUploadDocumentWithTemporaryTab = [
	documentTypes.INDEPENDENCEFORMTEMPLATE,
	documentTypes.ACCOUNT_CONCLUSION,
	documentTypes.PROFILEV2,
	documentTypes.GUIDEDWORKFLOW,
	documentTypes.GUIDEDWORKFLOWV2,
	documentTypes.IT_FLOW_ISA315,
	documentTypes.ESTIMATESDOCUMENT,
	documentTypes.ANALYTICSDOCUMENT,
	documentTypes.MULTIENTITYINDIVIDUALPROFILE,
	documentTypes.ACCOUNT_STANDARD_WORKING_PAPER,
	documentTypes.ACCOUNT_STANDARD_WORKING_PAPER_TEMPORARY,
	documentTypes.ACCOUNTLEVELRISKASSESSMENT,
	documentTypes.PIC_CHECKLIST,
	documentTypes.EQR_CHECKLIST,
	documentTypes.GROUPSTRUCTUREGUIDEDWORKFLOW,
	documentTypes.GROUPINSTRUCTIONGUIDEDWORKFLOW,
	documentTypes.ALRA_INDIVIDUAL,
	documentTypes.GROUP_INVOLVEMENT_INDIVIDUAL_COMPONENT,
	documentTypes.CONTROL_UNDERSTAND_CONTROLS,
	documentTypes.CONTROL_TEST_CONTROL_ATTRIBUTES,
	documentTypes.SCOT_WALKTHROUGH,
	documentTypes.REVIEW_AND_APPROVAL_SUMMARY_TEMPORARY
];

export const DocumentTypesWithLimitedRelatedObjects = [
	// documentTypes.ANALYTICSDOCUMENT,
	documentTypes.ACCOUNT_STANDARD_WORKING_PAPER,
	documentTypes.ACCOUNT_STANDARD_WORKING_PAPER_TEMPORARY,
	documentTypes.ACCOUNT_CONCLUSION,
	documentTypes.ALRA_INDIVIDUAL,
	documentTypes.ALRA_INDIVIDUAL_TEMPORARY,
	documentTypes.MULTIENTITYINDIVIDUALPROFILE,
	documentTypes.GROUP_INVOLVEMENT_INDIVIDUAL_COMPONENT,
	documentTypes.ESTIMATESDOCUMENT,
	documentTypes.SCOT_WALKTHROUGH,
	documentTypes.ESTIMATE_WALKTHROUGH,
	documentTypes.IT_APP_PLANNING_INDIVIDUAL,
	documentTypes.IT_APP_RISK_ASSESSMENT_INDIVIDUAL,
	documentTypes.IT_PROCESS_WALKTHROUGH_INDIVIDUAL,
	documentTypes.CONTROL_TEST_CONTROL_ATTRIBUTES
];

export const DocumentHubStatus = {
	UPLOADSTART: 'UPLOADSTART',
	ERROR: 'ERROR',
	UPLOADINGPROGRESS: 'UPLOADINGPROGRESS',
	UPLOADCOMPLETE: 'UPLOADCOMPLETE',
	DOWNLOADSTART: 'DOWNLOADSTART',
	DOWNLOADPROGRESS: 'DOWNLOADPROGRESS',
	DOWNLOADCOMPLETE: 'DOWNLOADCOMPLETE'
};

export const documentSources = {
	USERGENERATED: 1,
	KNOWLEDGEDELIVERED: 3,
	RESTORE: 10,
	ROLLFORWARD: 11,
	COPYENGAGEMENT: 13,
	SUPPORTPANEPRIORYEAREVIDENCE: 20,
	CUSTOMCANVASFORM: 36,
	COPYCANVASFORM: 41
};

export const CopyDocumentNameBehaviorType = {
	SameAsSource: 0,
	Timestamp: 1,
	CustomDocumentName: 2
};

export const formBodyType = {
	KnowledgeOnly: 1,
	TextResponse: 2,
	ToggleTwoButton: 3,
	ToggleThreeButton: 4,
	DropDownList: 5,
	RadioButton: 6,
	CheckBox: 7,
	RelatedWCGWsSCOTs: 8,
	NumericTextBox: 9,
	RelatedRisks: 10,
	DatePicker: 11,
	RelatedITRiskITProcess: 12,
	RelatedITApps: 13,
	WCGWTasks: 14,
	DecimalTextBox: 44,
	PlanningMateriality: 48,
	TolerableError: 49,
	SADNominalAmount: 50,
	UncorrectedMisstatementsThreshold: 51,
	HelixComponent: 80,
	SingleLineInput: 81,
	MultiLineInput: 82,
	DocumentUpload: 83,
	Spacer: 84,
	SingleSelectTitle: 85,
	MultiSelectTitle: 86,
	RelateRiskFactor: 87,
	RelateRiskAssertion: 88,
	DisplayRiskAndRiskFactor: 89,
	AccountSummaryComponent: 90,
	ToggleSwitch: 91,
	DisplayLimitedRiskAccountsBody: 92,
	DefineEstimatesForAccounts: 93,
	DisplayInsignificantAccountsBody: 94,
	RadioButtonHorizontal: 95,
	Slider: 96,
	DisplayMeasurementBasisBody: 97,
	MaterialityAdjustedBasisInput: 98,
	SliderLegend: 99,
	GuidedWorkflowPlanningMateriality: 100,
	TEMateriality: 101,
	MaterialitySADInput: 103,
	PACEInput: 104,
	UserLookup: 105,
	CreateRiskFactorBody: 106,
	ISA315ScotItApplication: 107,
	ISA315ItApplicationScot: 108,
	ISA315ItProcessItApplication: 109,
	ISA315ItProcessTask: 110,
	ISA315ItProcessItRisk: 111,
	ISA315ItGCTestStrategy: 112,
	ISA315SoScot: 113,
	ISA315ManageItSp: 114,
	PaceConclusion: 115,
	EntityLevelControlAssessment: 116,
	KnowledgeTitle: 117,
	OptionalPlainTextInput: 118,
	InlineRichText: 119,
	JournalEntrySource: 120,
	UnderstandJournalEntrySource: 121,
	OtherBasisTypeDescription: 122,
	ToggleSwitchTwoOptions: 123,
	DocumentUploadRequired: 124,
	KnowledgeOnlyPlain: 125,
	Blocker: 126,
	NotDisplayedPreselectedOptions: 127,
	CheckboxViewport: 129,
	RadioVerticalViewport: 130,
	KnowledgeOnlyViewport: 131,
	TextResponseViewport: 132,
	EstimateListing: 133,
	RelateSCOT: 134,
	AnalyticsOverview: 141,
	BalanceSheetTable: 142,
	IncomeStatementTable: 143,
	ViewAndEditAccount: 144,
	DisplayAccountBalances: 145,
	MaterialityViewer: 146,
	ALRASummary: 147,
	AccountGroupedByAccountType: 148,
	EstimatesLeadsheet: 149,
	EstimateBalanceInput: 150,
	RelateEstimateToAssertion: 151,
	RelateEstimateToRisk: 152,
	Summary: 153,
	MultiEntityList: 154,
	MultiEntityProfileListing: 155,
	MultiEntityOSTList: 156,
	MultiEntityPSPContentUpdateSubmit: 157,
	MultiEntityOSPList: 158,
	MultiEntityRelateEntityToAccount: 159,
	MultiEntitySelectAccountExecutionType: 160,
	MultiEntityProfileSubmit: 161,
	AssessInheritRisk: 162,
	DocumentUploadPICApprovalRequired: 163,
	DocumentUploadEQRApprovalRequired: 164,
	ALRAAccountList: 165,
	GroupStructureComponentList: 166,
	GroupStructureRelateAccountsToComponents: 167,
	GroupStructureRelateRisksToComponents: 168,
	GroupStructureSendLinkToComponents: 169,
	GroupStructureGroupStructureSummary: 170,
	GroupInstructionsGeneralCommunications: 171, // Old Key Information
	GroupInstructionsReportingFormList: 172,
	GroupInstructionsOtherCommunicationsList: 173,
	GroupInstructionsGroupRiskAssessment: 174,
	GroupInstructionsGroupInstructionsSummary: 175,
	HelixComponentV2: 176,
	AccountSetExecutionType: 177,
	GroupInvolvementGroupInvolvementIndex: 178,
	GroupInvolvementGroupInvolvementSummary: 179,
	DocumentUploadLegend: 180,
	DocumentUploadPICEQRApprovalRequired: 181,
	DocumentUploadAnyApprovalRequired: 182,
	FlowChart: 184,
	ControlAttributes: 185,
	SampleList: 186,
	ControlProperties: 187,
	GroupRiskAssessmentV2: 188,
	SampleListSize: 189,
	AccountRelatedEstimateListing: 190,
	IndividualAccountAttributes: 191,
	AccountStandardROMMListing: 192,
	MultiEntityListWithAtleastOneEntity: 194,
	MultiEntityProfileListingWithValidationsAlways: 195,
	CRASummary: 197,
	SCOTSummaryV2: 198,
	SCOTAndEstimateListing: 199,
	ITAppITPlanningListing: 200,
	RelateSupportingITAppApplicationOrTool: 203,
	RelateSupportingITAppDatabase: 204,
	RelateSupportingITAppOperatingSystem: 205,
	RelateSupportingITAppNetwork: 206,
	ITAppRiskAssessmentListing: 207,
	ITRiskFactorManageChange: 208,
	ITRiskFactorManageAccess: 209,
	ITRiskFactorManageSecuritySettings: 210,
	ITRiskFactorManageOperations: 211,
	ITRiskFactorSDLC: 212,
	RelatedITProcesses: 213,
	ITProcessListing: 214,
	ITProcessUnderstandRelateITApps: 215,
	ITProcessUnderstandRelateITRisks: 216,
	ITProcessSupportingITApps: 217,
	ITRiskAndRelatedITControls: 219,
	SCOTViewSCOT: 223,
	ViewIndividualSCOTSummary: 224,
	SCOTIdentifyV2: 225,
	SCOTSummaryV3: 226
};

export const documentGroups = {
	AccountLevelRiskAssessments: 1,
	AccountAnalytics: 2,
	GroupComponentInvolvment: 3,
	IdentifyEstimates: 4,
	MultiEntityProfiles: 5,
	SCOTAndEstimateWalkthroughs: 6,
	MultiEntityProfilesWithoutApprovals: 7
};

export const bodyTypesWithEmbeddedDocumentTypes = [
	{bodyTypeId: formBodyType.AnalyticsOverview, documentTypeId: documentTypes.ANALYTICSDOCUMENT},
	{bodyTypeId: formBodyType.EstimateListing, documentTypeId: documentTypes.ESTIMATESDOCUMENT},
	{bodyTypeId: formBodyType.MultiEntityProfileListing, documentTypeId: documentTypes.MULTIENTITYINDIVIDUALPROFILE},
	{bodyTypeId: formBodyType.ALRAAccountList, documentTypeId: documentTypes.ALRA_INDIVIDUAL},
	{
		bodyTypeId: formBodyType.GroupInvolvementGroupInvolvementIndex,
		documentTypeId: documentTypes.GROUP_INVOLVEMENT_INDIVIDUAL_COMPONENT
	},
	{bodyTypeId: formBodyType.EstimatesLeadsheet, documentTypeId: documentTypes.ALRA_INDIVIDUAL},
	{bodyTypeId: formBodyType.AccountRelatedEstimateListing, documentTypeId: documentTypes.ESTIMATESDOCUMENT},
	{
		bodyTypeId: formBodyType.MultiEntityProfileListingWithValidationsAlways,
		documentTypeId: documentTypes.MULTI_ENTITY_INDIVIDUAL_PROFILE_V2_NO_SIGN_OFF_REQUIRED
	}
];

export const documentTypesWithDocumentGroups = [
	{documentGroupId: documentGroups.AccountLevelRiskAssessments, documentTypeId: documentTypes.ALRA_INDIVIDUAL},
	{documentGroupId: documentGroups.AccountAnalytics, documentTypeId: documentTypes.ANALYTICSDOCUMENT},
	{
		documentGroupId: documentGroups.GroupComponentInvolvment,
		documentTypeId: documentTypes.GROUP_INVOLVEMENT_INDIVIDUAL_COMPONENT
	},
	{documentGroupId: documentGroups.IdentifyEstimates, documentTypeId: documentTypes.ESTIMATESDOCUMENT},
	{documentGroupId: documentGroups.SCOTAndEstimateWalkthroughs, documentTypeId: documentTypes.SCOT_WALKTHROUGH},
	{documentGroupId: documentGroups.SCOTAndEstimateWalkthroughs, documentTypeId: documentTypes.ESTIMATE_WALKTHROUGH},
	{documentGroupId: documentGroups.MultiEntityProfiles, documentTypeId: documentTypes.MULTIENTITYINDIVIDUALPROFILE},
	{
		documentGroupId: documentGroups.MultiEntityProfilesWithoutApprovals,
		documentTypeId: documentTypes.MULTI_ENTITY_INDIVIDUAL_PROFILE_V2_NO_SIGN_OFF_REQUIRED
	}
];

export const documentWithoutApprovalAndDocumentOptions = [
	documentTypes.MULTI_ENTITY_INDIVIDUAL_PROFILE_V2_NO_SIGN_OFF_REQUIRED
];

export const bodyFreeTextBodyTypes = [
	formBodyType.TextResponse,
	formBodyType.SingleLineInput,
	formBodyType.MultiLineInput,
	formBodyType.OptionalPlainTextInput,
	formBodyType.NumericTextBox,
	formBodyType.DatePicker,
	formBodyType.DecimalTextBox,
	formBodyType.PlanningMateriality,
	formBodyType.TolerableError,
	formBodyType.SADNominalAmount,
	formBodyType.UncorrectedMisstatementsThreshold,
	formBodyType.PACEInput,
	formBodyType.InlineRichText,
	formBodyType.OtherBasisTypeDescription
];

export const multiOptionBodyTypes = [
	formBodyType.ToggleTwoButton,
	formBodyType.ToggleThreeButton,
	formBodyType.DropDownList,
	formBodyType.RadioButton,
	formBodyType.CheckBox,
	formBodyType.SingleSelectTitle,
	formBodyType.MultiSelectTitle,
	formBodyType.ToggleSwitch,
	formBodyType.RadioButtonHorizontal,
	formBodyType.Slider,
	formBodyType.DocumentUpload,
	formBodyType.ToggleSwitchTwoOptions
];

export const simpleAndComplexBodyTypesWithResponse = [
	formBodyType.TextResponse,
	formBodyType.SingleLineInput,
	formBodyType.MultiLineInput,
	formBodyType.NumericTextBox,
	formBodyType.DatePicker,
	formBodyType.DecimalTextBox,
	formBodyType.OptionalPlainTextInput,
	formBodyType.DropDownList,
	formBodyType.RadioButton,
	formBodyType.CheckBox,
	formBodyType.RadioButtonHorizontal,
	formBodyType.Slider,
	formBodyType.ToggleSwitchTwoOptions
];

export const complexBodyTypes = [
	formBodyType.ToggleTwoButton,
	formBodyType.ToggleThreeButton,
	formBodyType.DropDownList,
	formBodyType.RadioButton,
	formBodyType.CheckBox,
	formBodyType.MultiSelectTitle,
	formBodyType.ToggleSwitch,
	formBodyType.RadioButtonHorizontal,
	formBodyType.Slider,
	formBodyType.SliderLegend,
	formBodyType.ToggleSwitchTwoOptions
];

export const simpleBodyTypes = [
	formBodyType.KnowledgeOnly,
	formBodyType.TextResponse,
	formBodyType.NumericTextBox,
	formBodyType.DatePicker,
	formBodyType.DecimalTextBox,
	formBodyType.SingleLineInput,
	formBodyType.MultiLineInput,
	formBodyType.PACEInput,
	formBodyType.UserLookup,
	formBodyType.InlineRichText,
	formBodyType.KnowledgeOnlyPlain,
	formBodyType.KnowledgeTitle,
	formBodyType.Blocker,
	formBodyType.Spacer
];

export const specialBodyTypes = [
	formBodyType.RelatedWCGWsSCOTs,
	formBodyType.RelatedRisks,
	formBodyType.RelatedITRiskITProcess,
	formBodyType.RelatedITApps,
	formBodyType.PlanningMateriality,
	formBodyType.TolerableError,
	formBodyType.SADNominalAmount,
	formBodyType.HelixComponent,
	formBodyType.RelateRiskFactor,
	formBodyType.RelateRiskAssertion,
	formBodyType.DocumentUpload,
	formBodyType.DisplayRiskAndRiskFactor,
	formBodyType.AccountSummaryComponent,
	formBodyType.DisplayLimitedRiskAccountsBody,
	formBodyType.DisplayMeasurementBasisBody,
	formBodyType.MaterialityAdjustedBasisInput,
	formBodyType.DefineEstimatesForAccounts,
	formBodyType.SliderLegend,
	formBodyType.GuidedWorkflowPlanningMateriality,
	formBodyType.TEMateriality,
	formBodyType.MaterialitySADInput,
	formBodyType.CreateRiskFactorBody,
	formBodyType.ISA315ItApplicationScot,
	formBodyType.ISA315ItProcessTask,
	formBodyType.ISA315SoScot,
	formBodyType.EntityLevelControlAssessment,
	formBodyType.OptionalPlainTextInput,
	formBodyType.JournalEntrySource,
	formBodyType.UnderstandJournalEntrySource,
	formBodyType.OtherBasisTypeDescription,
	formBodyType.DocumentUploadRequired,
	formBodyType.AnalyticsOverview,
	formBodyType.BalanceSheetTable,
	formBodyType.IncomeStatementTable,
	formBodyType.ViewAndEditAccount,
	formBodyType.DisplayAccountBalances,
	formBodyType.MaterialityViewer,
	formBodyType.ALRASummary,
	formBodyType.AccountGroupedByAccountType,
	formBodyType.EstimateListing,
	formBodyType.EstimateBalanceInput,
	formBodyType.EstimatesLeadsheet,
	formBodyType.ISA315ItProcessItApplication,
	formBodyType.ISA315ScotItApplication,
	formBodyType.ISA315ItProcessItRisk,
	formBodyType.ISA315ItGCTestStrategy,
	formBodyType.ISA315ManageItSp,
	formBodyType.RelateSCOT,
	formBodyType.AccountStandardROMMListing,
	formBodyType.RelateEstimateToAssertion,
	formBodyType.RelateEstimateToRisk,
	formBodyType.Summary,
	formBodyType.MultiEntityList,
	formBodyType.MultiEntityProfileListing,
	formBodyType.MultiEntityOSTList,
	formBodyType.MultiEntityPSPContentUpdateSubmit,
	formBodyType.MultiEntityOSPList,
	formBodyType.MultiEntityRelateEntityToAccount,
	formBodyType.MultiEntitySelectAccountExecutionType,
	formBodyType.MultiEntityProfileSubmit,
	formBodyType.AssessInheritRisk,
	formBodyType.DocumentUploadPICApprovalRequired,
	formBodyType.DocumentUploadEQRApprovalRequired,
	formBodyType.ALRAAccountList,
	formBodyType.GroupStructureComponentList,
	formBodyType.GroupStructureRelateAccountsToComponents,
	formBodyType.GroupStructureRelateRisksToComponents,
	formBodyType.GroupStructureSendLinkToComponents,
	formBodyType.GroupStructureGroupStructureSummary,
	formBodyType.GroupInstructionsGeneralCommunications,
	formBodyType.GroupInstructionsReportingFormList,
	formBodyType.GroupInstructionsOtherCommunicationsList,
	formBodyType.GroupInstructionsGroupRiskAssessment,
	formBodyType.GroupInstructionsGroupInstructionsSummary,
	formBodyType.HelixComponentV2,
	formBodyType.GroupInvolvementGroupInvolvementIndex,
	formBodyType.GroupInvolvementGroupInvolvementSummary,
	formBodyType.DocumentUploadRequired,
	formBodyType.DocumentUpload,
	formBodyType.DocumentUploadLegend,
	formBodyType.DocumentUploadPICEQRApprovalRequired,
	formBodyType.DocumentUploadAnyApprovalRequired,
	formBodyType.GroupRiskAssessmentV2,
	formBodyType.SampleListSize,
	formBodyType.AccountRelatedEstimateListing,
	formBodyType.IndividualAccountAttributes,
	formBodyType.MultiEntityListWithAtleastOneEntity,
	formBodyType.CRASummary,
	formBodyType.SCOTSummaryV2,
	formBodyType.SCOTAndEstimateListing,
	formBodyType.ITAppITPlanningListing,
	formBodyType.RelateSupportingITAppApplicationOrTool,
	formBodyType.RelateSupportingITAppDatabase,
	formBodyType.RelateSupportingITAppOperatingSystem,
	formBodyType.RelateSupportingITAppNetwork,
	formBodyType.ITProcessListing,
	formBodyType.ITProcessUnderstandRelateITApps,
	formBodyType.ITProcessUnderstandRelateITRisks,
	formBodyType.ITProcessSupportingITApps,
	formBodyType.ITRiskAndRelatedITControls,
	formBodyType.SCOTViewSCOT,
	formBodyType.ViewIndividualSCOTSummary
];

export const ClientPortalSpecialBodyWhiteList = [
	formBodyType.RelatedWCGWsSCOTs,
	formBodyType.RelatedRisks,
	formBodyType.RelatedITRiskITProcess,
	formBodyType.RelatedITApps,
	formBodyType.ISA315SoScot
];

export const ProfileActionTypes = {
	undefined: 0,
	profileSubmitFirstTime: 1,
	profileSubmitRollforward: 2,
	profileEdit: 3,
	globalContentUpdate: 4,
	importPsps: 5,
	importPsts: 6,
	canvasFormContentUpdate: 7,
	importGaInstructions: 8,
	addManualForm: 9,
	profileDelivery: 10,
	mandatoryContentUpdate: 11,
	customCanvasFormProfileSyncAndContentUpdate: 12
};

export const journalSourceTypes = {
	systemGenerated: 'systemGenerated',
	manual: 'manual',
	both: 'both'
};

export const formAction = {
	OverwriteForm: 1
};

export const documentFilter = {
	FilterSharedEvidence: 3
};

export const itApplicationFilter = {
	itApplication: 1,
	so: 2
};

export const guidanceType = {
	AtlasDocument: 1,
	AtlasAttachment: 2,
	HelixAnalyxer: 3,
	AutomationService: 4,
	OtherApplications: 5,
	ServiceDeliveryModel: 6,
	HelixComponent: 7,
	GuidanceTypeEight: 8,
	CanvasFormAttachment: 9
};

export const CanvasFormsWithoutNavigationPanel = [
	documentTypes.ALRA_INDIVIDUAL_TEMPORARY,
	documentTypes.ACCOUNT_STANDARD_WORKING_PAPER_TEMPORARY,
	documentTypes.GUIDEDWORKFLOWV2_TEMPORARY
];

export const ClassicSummaryScreenDocumentTypes = [
	documentTypes.ACCOUNTSSUMMARY,
	documentTypes.SCOTSSUMMARY,
	documentTypes.ITSOSUMMARY,
	documentTypes.CRASUMMARY,
	documentTypes.AUDITPLAN
];

export const OpenInCanvasDocumentTypeIDs = [
	documentTypes.PROFILE,
	documentTypes.ACCOUNTSSUMMARY,
	documentTypes.SCOTSSUMMARY,
	documentTypes.ITSOSUMMARY,
	documentTypes.CRASUMMARY,
	documentTypes.AUDITPLAN,
	documentTypes.GROUPAUDITSTRUCTURESUMMARY,
	documentTypes.GROUPAUDITINSTRUCTIONSUMMARY,
	documentTypes.GROUPCOMPONENTSUMMARYDOCUMENT,
	documentTypes.WORKPLANSUMMARY,
	documentTypes.ITEVALUATIONSMARTFORM,
	documentTypes.ITEVALUATIONTEMPORARYSMARTFORM,
	documentTypes.CANVASFORMEVIDENCE,
	documentTypes.CANVASFORMTEMPORARY,
	documentTypes.INDEPENDENCEFORMTEMPLATE,
	documentTypes.INDEPENDENCEFORMINDIVIDUAL,
	documentTypes.SEM,
	documentTypes.ACCOUNT_CONCLUSION,
	documentTypes.FOUR_FORTY_GL,
	documentTypes.ARCHIVE,
	documentTypes.ACCOUNTCONCLUSIONCLASSIC,
	documentTypes.DETERMINEPMTESAD,
	documentTypes.GUIDEDWORKFLOWTEMPORARY,
	documentTypes.GUIDEDWORKFLOW,
	documentTypes.SMARTAUTOMATIONEVIDENCE,
	documentTypes.SMARTAUTOMATIONEVIDENCETEMPORARY,
	documentTypes.PROFILEV2,
	documentTypes.LIMITEDRISKDOCUMENTATION,
	documentTypes.GUIDEDWORKFLOWV2,
	documentTypes.ESTIMATESDOCUMENT,
	documentTypes.MULTIENTITYENGAGEMENTSUBPROFILE,
	documentTypes.MULTIENTITYSUBPROFILE,
	documentTypes.MULTIENTITYINDIVIDUALPROFILE,
	documentTypes.ACCOUNTLEVELRISKASSESSMENT,
	documentTypes.PIC_CHECKLIST,
	documentTypes.EQR_CHECKLIST,
	documentTypes.GROUPSTRUCTUREGUIDEDWORKFLOW,
	documentTypes.GROUPINSTRUCTIONGUIDEDWORKFLOW,
	documentTypes.ALRA_INDIVIDUAL,
	documentTypes.GROUP_INVOLVEMENT_INDIVIDUAL_COMPONENT,
	documentTypes.ALRA_INDIVIDUAL_TEMPORARY,
	documentTypes.ANALYTICSDOCUMENT,
	documentTypes.SCOT_WALKTHROUGH,
	documentTypes.ESTIMATE_INDIVIDUAL_ESTIMATE_TEMPORARY,
	documentTypes.SCOT_WALKTHROUGH_TEMPORARY,
	documentTypes.EVIDENCE_DISPLAYED_NO_APPROVAL_REQUIRED,
	documentTypes.GUIDED_WORKFLOWV2_NOSIGNOFF_REQUIRED,
	documentTypes.GUIDED_WORKFLOWV2_NOSIGNOFF_REQUIRED_TEMPORARY,
	documentTypes.CRA_SUMMARY_V2,
	documentTypes.CRA_SUMMARY_V2_TEMPORARY,
	documentTypes.SCOT_SUMMARYV2,
	documentTypes.SCOT_SUMMARYV2_TEMPORARY,
	documentTypes.ESTIMATE_WALKTHROUGH,
	documentTypes.ESTIMATE_WALKTHROUGH_TEMPORARY,
	documentTypes.IT_APP_PLANNING_INDIVIDUAL,
	documentTypes.IT_APP_PLANNING_INDIVIDUAL_TEMPORARY,
	documentTypes.IT_APP_RISK_ASSESSMENT_INDIVIDUAL,
	documentTypes.IT_APP_RISK_ASSESSMENT_INDIVIDUAL_TEMPORARY,
	documentTypes.IT_PROCESS_WALKTHROUGH_INDIVIDUAL,
	documentTypes.IT_PROCESS_WALKTHROUGH_INDIVIDUAL_TEMPORARY,
	documentTypes.CONTROL_DEFICIENCY_INDIVIDUAL,
	documentTypes.CONTROL_DEFICIENCY_INDIVIDUAL_TEMPORARY,
	documentTypes.REVIEW_AND_APPROVAL_SUMMARY,
	documentTypes.REVIEW_AND_APPROVAL_SUMMARY_TEMPORARY,
	documentTypes.MULTI_ENTITY_INDIVIDUAL_PROFILE_V2_NO_SIGN_OFF_REQUIRED,
	documentTypes.ACCOUNT_STANDARD_WORKING_PAPER
];

export const SmartAutomationEvidenceIDs = [
	documentTypes.SMARTAUTOMATIONEVIDENCE,
	documentTypes.SMARTAUTOMATIONEVIDENCETEMPORARY
];

export const numericValueRegex = /(^[0-9]+$|^$)/;
export const decimalValueRegex = /^[0-9]\d{0,13}(\.\d{0,2})?$/;
export const oneDecimalValueRegex = /^[0-9]\d{0,14}(\.\d{0,1})?$/;
export const integerRegex = /^([+-]?[1-9]\d*|0)$/;
export const positiveIntegerRegex = /^\d+$/;
export const positiveNullableIntegerRegex = /^\d*$/;
export const positiveIntegerNoSpecialCharRegex = /[^0-9]|[^\d]/g;

//Example: /^\d+(?:\.\d{0,1})?$/ => Dot is the decimalSeparator
export const positiveNumberWithOneDecimalRegex = (decimalSeparator) => {
	let stringRegex = `^\\d+(?:\\${decimalSeparator}\\d{0,1})?$`;

	return new RegExp(stringRegex);
};

export const non_MEST = 0;

export const AccountType = {
	Significant: 1,
	LimitedRisk: 2,
	InSignificant: 3,
	Others: 4,
	SignificantDisclosure: 5
};

export const AccountTypes = {
	[AccountType.Significant]: 'significantAccount',
	[AccountType.LimitedRisk]: 'limitedRiskAccount',
	[AccountType.InSignificant]: 'insignificantAccount',
	[AccountType.SignificantDisclosure]: 'significantDisclosure'
};

export const ExecutionType = {
	ExecutedInEngagement: 1,
	GroupExecutedInComponentEngagement: 2,
	GroupExecutedInPrimaryAndComponentEngagement: 3
};

export const StatementType = {
	Income: 1,
	BalanceSheet: 2,
	Both: 3
};

export const TaskTypeEnum = {
	CONTROL_TASK: 1,
	CONTROL_TASK_CONTROL: 2,
	CONTROL_TASK_ELC: 3,
	CONTROL_TASK_ITSO: 4,
	CONTROL_TASK_MP: 5,
	CONTROL_TASK_SO: 6,
	RESPONSE_TASK: 7,
	CASHFLOW_MISSTATEMENT_RESPONSE_TASK: 8,
	DEFICIENCY_RESPONSE_TASK: 9,
	DISCLOSURE_MISSTATEMENT_RESPONSE_TASK: 10,
	MISSTATEMENT_RESPONSE_TASK: 11,
	STANDARD_TASK: 12,
	OTHER_STANDARD_TASK: 13,
	PRIMARY_STANDARD_TASK: 14,
	SUBSTANTIVE_TASK: 15,
	OTHER_SUBSTANTIVE_PROCEDURE_TASK: 16,
	PRIMARY_SUBSTANTIVE_PROCEDURE_TASK: 17,
	WALKTHROUGH_TASK: 18,
	WALKTHROUGH_TASK_ITSO: 19,
	WALKTHROUGH_TASK_SCOT: 20,
	TODO: 21,
	ACCOUNT_CONCLUSION_TASK: 22,
	GROUP_TASK: 23,
	PRIMARY_GROUP_TASK: 24,
	COMPONENT_GROUP_TASK: 25,
	ITSO_OTHER_SUBSTANTIVE_PROCEDURE_TASK: 26,
	COMPONENT_GROUP_TASK_UNLINKED: 27
};

export const taskApiRoutes = {
	task: 'tasks/',
	category: 'TaskCategories/',
	engagement: 'Engagements/',
	workspace: 'Workspace/',
	relatedWCGWs: 'tasks/?expandRelatedWCGW=true',
	annotation: 'Annotations/',
	document: 'Documents/'
};

export const TimePhaseTypes = {
	Scope: 1,
	Strategy: 2,
	Walkthroughs: 3,
	InterimControlTesting: 4,
	InterimSubstantiveTesting: 5,
	WalkthroughsYearEndProcesses: 6
};
export const attributeListSkipTaskOpen = ['tagIcon', 'nextWaveIcon', 'tagMenu', 'nextWaveMenu'];
export const ValidationGroup = {
	ArchiveTimelineFinalizeEvidence: 10001,
	ArchiveTimelineCompleteRemainingTasks: 10002,
	ArchiveTimelineResolveValidations: 10003,
	CanvasForm: 10004,
	SubstantiveEvaluationMatrix: 10005,
	FourFortyGl: 10006,
	RiskWithNoRelatedSubstantiveTask: 10007,
	RiskWithNoRelatedWcgw: 10008,
	DashboardScopeAndStrategy: 10009,
	DashboardExecution: 10010,
	DashboardConclusion: 10011,
	ArchiveTimelineHiddenValidations: 10012,
	IndependenceValidations: 10013,
	AllIncompleteResponsesValidations: 10014,
	ProfileV2Validations: 10015,
	IndependenceV2Validations: 10017,
	IndependenceViewTemplate: 10018,
	ITFlowCanvasForm: 10019,
	GuidedWorkFlow: 10021,
	AccountsWithoutIndividualALRADocument: 10023,
	DocumentsMissingRequiredPICEQRSignOff: 10028,
	AdjustmentModuleValidation: 10029,
	EstimatesWithoutIndividualDocument: 10030
};

export const reviewNotesType = {
	task: 1,
	document: 2
};

export const entityTypes = {
	Engagement: 'engagement',
	Task: 'task',
	Document: 'document',
	openReviewNoteCount: 'opennotescount'
};

export const itControlApproachType = {
	controls: 1,
	substantive: 2
};

export const LinkType = {
	Share: 'Share',
	Copy: 'Copy'
};

export const entities = {
	all: 0,
	Document: 1,
	LeadSchedule: 2,
	Account: 3,
	SCOT: 4,
	ItProcess: 5,
	AuditPlan: 6,
	Risk: 7,
	Task: 8,
	Misstatement: 9,
	Deficiency: 10,
	GaComponent: 11,
	GaComponentInstruction: 12,
	GaComponentEvidence: 13,
	GaScope: 14,
	GaPrimaryInstruction: 15,
	GaPrimary: 16,
	ClientRequest: 17,
	WCGW: 18,
	Control: 19,
	ItSoApplication: 20,
	CanvasForm: 21,
	FormSection: 22,
	FormBody: 23,
	Assertion: 24,
	ClientEngagement: 25,
	ClientGroup: 26,
	EngagementTag: 27,
	Engagement: 28,
	FormHeader: 29,
	FormStatus: 30,
	EngagementUser: 31,
	ClientGroupUser: 32,
	PspIndex: 33,
	ItControl: 34,
	ItRisk: 35,
	AutomationLineItem: 36,
	PMAmount: 37,
	TEAmount: 38,
	SADNominalAmount: 39,
	UMTAmount: 40,
	Annotation: 41,
	CanvasFormComment: 42,
	TaskAssignment: 43,
	PACEType: 44,
	STEntity: 50,
	Estimate: 51,
	SampleItem: 53
};

export const scotEstimateSubHeaders = {
	scot: 0,
	noScotsIdentified: -1,
	scotNotFound: -2,
	estimateNotFound: -3
};

export const scotSummary198SelectedType = {
	selectedScots: 'selectedScots',
	selectedAccounts: 'selectedAccounts',
	defaultKnowledgeAssertionId: 12
};

export const ITApproachType = {
	Controls: 1,
	Substantive: 2
};

export const taskBuildMilestoneIds = {
	WCGWTask: 29,
	RiskWCGW: 30,
	GuidedWorkflow: 55
};

export const ControlADCConstants = {
	ITApplicationControlType: 1,
	ITDependentcontrolType: 2,
	gaasCountryId: 218,
	auditProfileAnswerOptionId: 314
};

export const controlDesignEffectivenessTypeId = {
	Effectiveness: 1,
	No_Effectiveness: 2,
	Null: null
};

export const TaskCategory = {
	my: 1,
	all: 2,
	sas: 3,
	scot: 4,
	itso: 5,
	account: 6,
	execution: 7,
	conclusion: 8,
	teamsummary: 9
};

export const TaskSubCategory = {
	all: 1,
	preparer: 2,
	reviewer: 3
};

export const AuditPhases = {
	ViewAll: 0,
	ScopeAndStrategy: 1,
	Execution: 2,
	ConclusionAndWrapup: 3,
	PerformPreliminaryEngagementActivities: 4,
	IdentifyAndAssessRisks: 5,
	RespondToRisks: 6,
	ConcludeAndReport: 7
};

export const validationTypes = {
	IndependenceCompliance: 1,
	IndependenceFormversionNotCurrent: 2,
	UserAccessTobeReviewed: 3,
	OverrideByIneligibleRole: 4,
	UnrelatedSCOTs: 5,
	UnrelatedControls: 6,
	ControlsStrategySelectedWithNoRelatedWCGWs: 7,
	ControlSstrategySelectedWithNoRelatedControls: 8,
	ControlsStrategySelectedWithNoRelatedControlSelectedForTesting: 9,
	ControlsstrategyselectedandthereisaWCGWwithnorelatedControlsSelectedForTesting: 10 /*seriously*/,
	UnrelatedRisks: 11,
	CompleteCRAforThisAssertion: 12,
	UnrelatedWCGWs: 13,
	SignificantAccountHasNorelatedSCOTs: 14,
	RelystrategySelectedWithNoRelatedWCGWs: 15,
	RelystrategySelectedWithNoRelatedControls: 16,
	RelystrategySelectedWithNoRelatedControlsSelectedForTesting: 17,
	RelystrategySelectedAndThereisaWCGWwithnorelatedControlsSelectedForTesting: 18,
	ITSummary: 19,
	ConfirmAccuracyOfAuditorReport: 20,
	CompleteArchiveForm: 21,
	CompleteArchiveTask: 22,
	UnlinkGroupSharedEvidence: 23,
	NorelatedControlsITSubstantiveProceduresSelectedForTesting: 24,
	ThereisaRiskWithNorelatedControlsITSubstantiveProceduresSelectedForTesting: 25,
	AuditPlan: 26,
	NoSubstantiveTasksareRelatedtotheAssertion: 27,
	UnrelatedTOC: 28,
	UnrelatedWalkthrough: 29,
	NoWalkthroughTasksAreRelatedtotheSCOT: 30,
	NoControlTasksareRelatedtoaControlSelectedForTesting: 31,
	UnrelatedITSOWalkthrough: 32,
	UnrelatedITSOTOC: 33,
	UnrelatedITSOOSP: 34,
	TheITSOProcessDoesNotHaveaRelatedWalkthroughtask: 35,
	AControlDoesNotHaveaRelated: 36,
	AITSubstantiveProcedureDoesNotHaveaRelatedOSP: 37,
	RemainingDocumentConflicts: 38,
	SyncConflictSharedEvidenceNotDisplayedinCanvasValidationCount: 39,
	SignoffOnAllEvidence: 40,
	AddEvidenceToTaskWithoutEvidence: 41,
	InvalidEngagementCode: 42,
	InvalidClientCode: 43,
	DuplicateFileNames: 44,
	ITEvaluation: 45,
	InconsistentITEvaluations: 46,
	IncompleteAggregateITEvaluation: 47,
	ResponseIncomplete: 48,
	UnassociateFinding: 49,
	UnassociatedDeficiency: 50,
	SubstantiveEvaluationMatrix: 51,
	CRASummary: 52,
	AccountsSummary: 53,
	SCOTsSummary: 54,
	EstimateSCOTwithoutEstimateAccount: 55,
	AssertionwithcontrolriskofrelywithoutaresponsiveTOC: 56,
	AssertionWithControlRiskofrelyWithoutAtleastoneResponsive: 57,
	AssertionWithoutResponsiveSubstantiveTask: 58,
	AssertionWithoutaResponsiveControl: 59,
	CanvasFromComments: 60,
	FinalizeIncompleteCanvasFromResponses: 61,
	AddressOpenReviewNotes: 62,
	RemoveTrackChangesFromEvidence: 63,
	MarkTasksComplete: 64,
	UnlinkSharedEvidence: 65,
	DocumentsWithoutModificationType: 66,
	NonAdminChangesWithoutChangeReason: 67,
	UnapprovedModificationsForNonAdminChangesByAppropriateRole: 68,
	FourFortyMissingExecReviewerSignOff: 69,
	FormContentWithoutHeader: 71,
	AllIncompleteResponses: 73,
	ProfileV2ChangeNotSubmitted: 74,
	IndependenceChangeNotSubmitted: 76,
	IndependenceTemplateDifferentFromIndividual: 77,
	ITAppWithoutAtLeastOneRelatedITProcess: 78,
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: 79,
	ITSPHasNorelatedITRisk: 80,
	ITProcessHasNoRelatedITApplication: 81,
	ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication: 82,
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: 83,
	ITDMorITACWithNoRelatedITApplication: 84,
	ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed: 85,
	ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed: 86,
	ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed: 87,
	ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed: 88,
	ITGCWithoutASelectedDesignEffectiveness: 89,
	SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero: 91,
	AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed: 93,
	SCOTWithHasNoITApplicationHasITDMOrAppControls: 94,
	RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM: 96,
	RisksUnrelatedToAnAssertionForGuidedWorkflow: 97,
	IncompleteMeasurementBasisForecastAmount: 98,
	IncompleteMeasurementBasisForecastAmountRationale: 99,
	IncompleteMeasurementBasisAdjustedAmount: 100,
	IncompletePlanningMateriality: 101,
	PlanningMaterialityGreaterThanMaximumAmount: 102,
	IncompletePlanningMaterialityRationale: 103,
	IncompleteTolerableError: 104,
	TENotWithinRangeOfAllowedValues: 105,
	IncompleteTolerableErrorRationale: 106,
	IncompleteSAD: 107,
	SADGreaterThanMaximum: 108,
	IncompleteSADRationale: 109,
	IncompletePACESelection: 110,
	MultiEntityNotRelatedToALLPSTACTForRelatedAccount: 129,
	AccountWithoutIndividualRiskAssessmentForm: 130,
	EstimateWithoutIndividualEstimateForm: 131,
	AccountWithoutIndividualAnalyticForm: 132,
	MultiEntityWithoutIndividualProfileForm: 133,
	AccountAccountTypeIDDoesNotMatchAction: 134,
	AccountHasEstimateDoesNotMatchAction: 135,
	AccountFormOptionHasRelatedRisksNotAssociatedToAccount: 136,
	AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk: 137,
	AccountMissingSubstantiveProcedure: 138,
	ComponentWithoutGroupInvolvementForm: 139,
	ComponentWithoutRelatedGroupAssessmentInstruction: 140,
	MultiEntityAccountWithoutRelatedToAnyMultiEntity: 141,
	AssertionInherentRiskWithoutRelatedHigherRisk: 143,
	AccountGroupWithoutAComponent: 144,
	EstimateAccountWithoutEstimatePSPIndex: 145,
	ChangeNotSubmittedMultiEntityFullProfile: 146,
	ChangeNotSubmittedMultiEntityIndividualDocument: 147,
	AccountWithMissingValues: 148,
	DocumentUploadMissingRequiredPICEQRSignOffs: 149,
	DocumentUploadMissingRequiredPICEQRSignOffRequirements: 150,
	DocumentUploadMissingPreparerOrReviewerSignOffs: 152,
	AdjustmentsWithoutAnyEvidence: 153,
	EstimateWithoutAccountRelated: 154,
	EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount: 155,
	RiskEstimateRiskTypeIDDoesNotMatchAction: 156,
	LowerorHigherRiskEstimateWithoutEstimateSCOT: 157,
	EstimateAccountWithoutHigherOrLowerRiskEstimate: 158,
	EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary: 159,
	EstimateScotWithoutHigherOrLowerRiskEstimate: 160,
	AdjustmentsThatDoNotNet: 161,
	HigherRiskEstimateWithoutRisk: 162,
	DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration: 163,
	NonEngagementWideTasksMissingEvidence: 164,
	PICEQRSignOffRequirements: 165,
	EstimatesMustBeMarkedHigherRisk: 166,
	MultiEntityListWithAtleastOneEntity: 167,
	ITApplicationWithoutITAppPlanningIndividualDocument: 168,
	ITApplicationWithoutITAppRiskAssessmentIndividualDocument: 169,
	SCOTHasNoRelatedSignificantAccountOrDisclosure: 169,
	SCOTEstimateNoRelatedWalkthroughForm: 170,
	SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2: 171,
	AccountSignificantDisclosureWithNoRelatedSCOTV2: 172,
	RisksWithoutAnyRelatedAssertions: 173,
	AssertionsWithIncompleteCRA: 174,
	LimitedRiskOrInsignificantAccountMissingRationale: 175,
	ITProcessWithoutWalkthroughDocument: 176,
	ITProcessIsUncategorized: 177,
	ITProcessWithNoRelatedITApplication: 178
};

export const ValidationSubGroupsForDashboard = {
	IndependenceCompliance: 9008,
	AccoutSummary: 9001,
	ScotSummary: 9002,
	ItSummary: 9004,
	CraSummary: 9003,
	AuditPlan: 9005,
	SubstantiveEvaluationMatrix: 51,
	ValidationsByCanvasForm: 9012,
	ITEvaluation: 9009,
	UnrelatedMisstatements: 49,
	UnrelatedDeficiencies: 50,
	DuplicateFileNames: 44,
	UnresolvedConflicts: 38,
	ModificationsAfterTheReportReleaseDate: 9013
};

export const clientSideEventNames = {
	onRailChange: 'onRailChange',
	onFormChange: 'onFormChange',
	relatedObjectExpand: 'relatedObjectExpand',
	setSelectedAssertionName: 'setSelectedAssertionName',
	getScotsForAssertion: 'getScotsForAssertion',
	resize: 'resize',
	updateHeaderValidations: 'updateHeaderValidations',
	updateSectionValidations: 'updateSectionValidations',
	updateBodyValidations: 'updateBodyValidations',
	getContentUpdateHistoryFromAPI: 'getContentUpdateHistoryFromAPI',
	onCreateOrEdit: 'onCreateOrEdit',
	railFilterValidations: 'railFilterValidations',
	noIncompleteBodies: 'noIncompleteBodies',
	onSnapshotChange: 'onSnapshotChange',
	getValidations: 'getValidations',
	onCommentStatusChange: 'onCommentStatusChange',
	onCommentGroupClick: 'onCommentGroupClick',
	onCollapseChange: 'onCollapseChange',
	showKendoModal: 'showKendoModal',
	onFormObjectClick: 'onFormObjectClick',
	unselectCurrentSectionId: 'unselectCurrentSectionId',
	changeCommentTab: 'changeCommentTab',
	filterComments: 'filterComments',
	showCommentsModal: 'showCommentsModal',
	closeCommentsModal: 'closeCommentsModal',
	aresGoToActiveSlideHeaders: 'goToActiveSlide-headers',
	aresGoToActiveSlideTasks: 'goToActiveSlide-tasks',
	saveBody: 'saveBody',
	showGuidanceModal: 'showGuidanceModal',
	closeGuidanceModal: 'closeGuidanceModal',
	commentUpdated: 'commentUpdated',
	closeTypeEightGuidanceModal: 'closeTypeEightGuidanceModal',
	clearKendoEditorContent: 'clearKendoEditorContent',
	closeCommentsValidationModal: 'closeCommentsValidationModal',
	unsavedChangesConfirm: 'unsavedChangesConfirm',
	unsavedChangesCancel: 'unsavedChangesCancel',
	navigationPanelExpanded: 'navigationPanelExpanded',
	clearFormData: 'clearFormData',
	aresBodyDataLoaded: 'bodyDataLoaded',
	getBodiesBySectionId: 'getBodiesBySectionId',
	updateExpandWhenModalIsOpen: 'updateExpandWhenOarTableModalIsOpen',
	openRelateTaskModal: 'openRelateTaskModal',
	entityFormCreated: 'entityFormCreated',
	entityFormDeleted: 'entityFormDeleted',
	getAccountsForBody: 'getAccountsForBody',
	hideHasMissingDocuments: 'hideHasMissingDocuments',
	getEstimatesForBody: 'getEstimatesForBody',
	resetTimer: 'resetTimer',
	estimateDocumentDeleted: 'estimateDocumentDeleted',
	checkoutCheckin: 'checkoutCheckin',
	documentUploadComplete: 'documentUploadComplete',
	callRelateFlowchartStepDocument: 'callRelateFlowchartStepDocument',
	closeInlineRichTextEditor: 'closeInlineRichTextEditor'
};

export const HttpStatusCodes = {
	Ok: 200,
	Created: 201,
	NoContent: 204,
	Unauthorized: 401,
	Forbidden: 403,
	NotFound: 404,
	Conflict: 409,
	PreConditionFailed: 412,
	RequestEntityTooLarge: 413,
	BadRequest: 400,
	InternalServerError: 500,
	LengthRequired: 411
};

export const wcgwApiRoutes = {
	wcgws: 'wcgws',
	associateTask: 'wcgws/{wcgwId}/tasks',
	unassociateTask: 'wcgws/{wcgwId}/tasks/{taskId}'
};

export const reviewNoteStatus = {
	Open: {id: 1, value: 'open'},
	Cleared: {id: 2, value: 'cleared'},
	Closed: {id: 3, value: 'closed'}
};

export const rnhEngagementSources = {
	currentEngagement: 1,
	otherEngagement: 2,
	allEngagements: 3,
	ceapV2Protected: 10
};

export const tabsUniqueConstants = {
	documentReviewNotes: 'documentReviewNotes',
	taskReviewNotes: 'taskReviewNotes',
	allNotes: 'allNotes',
	myNotes: 'myNotes',
	relateEvidence: 'relateEvidence',
	createPaperProfile: 'createPaperProfile',
	internalMember: 'internalMember',
	externalMember: 'externalMember',
	otherEngagementNotes: 'otherEngagementNotes'
};

export const specialBodyPagingOptions = {
	options: [10, 20, 50]
};

export const specialBodyExtendedPagingOptions = {
	defaultPageSize: 20,
	options: [20, 50, 100, 200]
};

export const pagingOptions = {
	defaultPageSize: 20,
	options: [20, 50, 100]
};

export const extendedPagingOptions = {
	options: [20, 50, 100, 200]
};

export const classViewPagingOptions = {
	options: [50, 100, 200]
};

export const estimateListingPagingOptions = {
	options: [20, 50, 100, 200]
};

export const attributeStatus = {
	present: 3,
	notApplicable: 4,
	notPresent: 5,
	presentWithComments: 7
};

export const sampleItemTypes = {
	normalSample: 1,
	remedialSample: 2
};

export const documentCategoryNames = {
	evidence: 'evidence',
	temporary: 'temporary',
	priorPeriod: 'prioryear',
	external: 'external',
	componentGroup: 'componentGroup',
	evidenceAndTemporary: 'evidenceAndTemporary',
	afterRrd: 'afterRrd',
	canvasFormBodyEvidence: 'canvasFormBodyEvidence',
	primaryReturned: 'primaryReturned',
	primaryReceived: 'primaryReceived',
	componentSent: 'componentSent',
	componentReturned: 'componentReturned',
	componentResolved: 'componentResolved',
	canvasFormPrint: 'canvasFormPrint',
	clientRequestEvidence: 'clientRequestEvidence',
	clientRequestTemporary: 'clientRequestTemporary',
	clientRequestPriorYear: 'clientRequestPriorYear',
	clientRequestExternal: 'clientRequestExternal',
	clientRequestDocument: 'clientRequestDocument',
	componentEvidenceAndGroup: 'componentEvidenceAndGroup',
	workspace: 'workspace',
	componentSentDocumentToPrimary: 'componentSentDocumentToPrimary',
	engagement: 'engagement', // not used
	sharedEvidence: 'sharedevidence',
	canvasFormReport: 'canvasFormReport',
	systemDiagnostic: 'systemDiagnostic',
	basicDocumentAttributes: 'basicDocumentAttributes',
	clientAnalytics: 'clientAnalytics',
	clientRequestClientAnalytics: 'clientRequestClientAnalytics',
	canvasFormNavigation: 'canvasFormNavigation',
	canvasFormNavigationIndividual: 'canvasFormNavigationIndividual'
};

// TaskOverview API parameters

export const taskOverviewParameters = {
	taskCategoryId: {
		Scot: 'scot',
		ItSo: 'itso',
		Account: 'account',
		Risk: 'risk',
		TaskSectionId: 'taskSectionID',
		AuditPhase: 'auditPhase',
		ListOfTasks: 'listOfTasks',
		MultiEntity: 'multiEntity'
	},
	view: {
		Tasks: 'tasks',
		Documents: 'documents',
		ReviewNotes: 'reviewNotes',
		Assignments: 'assignments',
		ClientRequests: 'clientRequests'
	},
	documentCategorySearch: {
		CanvasFormNavigation: documentCategoryNames.canvasFormNavigation,
		CanvasFormNavigationIndividual: documentCategoryNames.canvasFormNavigationIndividual
	}
};

// FormActions API parameters

export const engagementFormActionsParameters = {
	formCategory: {
		AccountRiskAssessment: 'accountRiskAssessment',
		GroupInvolvementIndex: 'groupInvolvement',
		EstimateAssessment: 'estimateAssessment'
	}
};

export const documentSearchTypes = {
	engagement: 'engagement',
	task: 'task',
	canvasForm: 'canvasform',
	canvasFormbody: 'canvasformbody',
	engagementReferenceDocument: 'engagementReferenceDocument',
	myReferenceDocument: 'myReferenceDocument',
	afterRrd: 'afterRrd',
	gaInstruction: 'GAInstruction',
	account: 'account',
	flowChartStep: 'flowChartStep',
	sampleItem: 'sampleitem'
};
export const sampleItemFilterKeys = {
	sampleItemFilter: 'sampleItemFilter',
	sampleItemListKey: 'sampleItemListKey'
};
export const TagType = {
	Filtering: 'Filtering',
	Notes: 'Notes',
	FilteringNotArchived: 'filteringNotArchived'
};

export const DocumentStatus = {
	ContentInProgress: 1,
	ContentComplete: 2,
	ContentError: 3,
	ContentFullReload: 4,
	ContentUpdateInprogress: 5,
	ContentErrorFullReload: 6,
	UnlinkInProgress: 7,
	UnlinkComplete: 8,
	UnlinkError: 9,
	CanvasFormManualCreation: 10,
	FinalizeNotStarted: 11,
	FinalizeCompleted: 12,
	FinalizeDeleted: 13,
	LinkInProgress: 14,
	LinkComplete: 15,
	LinkError: 16,
	CopyInProgress: 17,
	DaaSCheckoutInProgress: 18,
	DaaSCheckoutComplete: 19,
	DaaSCheckoutError: 20,
	DaaSCheckinInProgress: 21,
	DaaSCheckinComplete: 22,
	DaaSCheckinError: 23,
	DaaSDiscardInProgress: 24,
	DaaSDiscardComplete: 25,
	DaaSDiscardError: 26
};

export const ContentContract = {
	SqlFileStreamBlob: 1,
	AzureBlob: 2,
	CanvasCustomBlob: 3,
	FileSystemBlob: 4,
	DaaS: 5
};

export const keyCode = {
	CANCEL: 3,
	HELP: 6,
	BACK_SPACE: 8,
	TAB: 9,
	CLEAR: 12,
	RETURN: 13,
	ENTER: 13,
	SHIFT: 16,
	CONTROL: 17,
	ALT: 18,
	PAUSE: 19,
	CAPS_LOCK: 20,
	ESCAPE: 27,
	SPACE: 32,
	PAGE_UP: 33,
	PAGE_DOWN: 34,
	END: 35,
	HOME: 36,
	ARROWLEFT: 37,
	ARROWUP: 38,
	ARROWRIGHT: 39,
	ARROWDOWN: 40,
	PRINTSCREEN: 44,
	INSERT: 45,
	DELETE: 46,
	KEY_0: 48,
	KEY_1: 49,
	KEY_2: 50,
	KEY_3: 51,
	KEY_4: 52,
	KEY_5: 53,
	KEY_6: 54,
	KEY_7: 55,
	KEY_8: 56,
	KEY_9: 57,
	SEMICOLON: 59,
	EQUALS: 61,
	KEY_A: 65,
	KEY_B: 66,
	KEY_C: 67,
	KEY_D: 68,
	KEY_E: 69,
	KEY_F: 70,
	KEY_G: 71,
	KEY_H: 72,
	KEY_I: 73,
	KEY_J: 74,
	KEY_K: 75,
	KEY_L: 76,
	KEY_M: 77,
	KEY_N: 78,
	KEY_O: 79,
	KEY_P: 80,
	KEY_Q: 81,
	KEY_R: 82,
	KEY_S: 83,
	KEY_T: 84,
	KEY_U: 85,
	KEY_V: 86,
	KEY_W: 87,
	KEY_X: 88,
	KEY_Y: 89,
	KEY_Z: 90,
	CONTEXT_MENU: 93,
	NUMPAD0: 96,
	NUMPAD1: 97,
	NUMPAD2: 98,
	NUMPAD3: 99,
	NUMPAD4: 100,
	NUMPAD5: 101,
	NUMPAD6: 102,
	NUMPAD7: 103,
	NUMPAD8: 104,
	NUMPAD9: 105,
	MULTIPLY: 106,
	ADD: 107,
	SEPARATOR: 108,
	SUBTRACT: 109,
	DECIMAL: 110,
	DIVIDE: 111,
	F1: 112,
	F2: 113,
	F3: 114,
	F4: 115,
	F5: 116,
	F6: 117,
	F7: 118,
	F8: 119,
	F9: 120,
	F10: 121,
	F11: 122,
	F12: 123,
	F13: 124,
	F14: 125,
	F15: 126,
	F16: 127,
	F17: 128,
	F18: 129,
	F19: 130,
	F20: 131,
	F21: 132,
	F22: 133,
	F23: 134,
	F24: 135,
	NUM_LOCK: 144,
	SCROLL_LOCK: 145,
	PLUS: 187,
	COMMA: 188,
	MINUS: 189,
	PERIOD: 190,
	SLASH: 191,
	BACK_QUOTE: 192,
	OPEN_BRACKET: 219,
	BACK_SLASH: 220,
	CLOSE_BRACKET: 221,
	QUOTE: 222,
	META: 224
};

export const actionTags = {
	open: 'open',
	openReadOnly: 'openr'
};

export const Entity = {
	Document: 1,
	LeadSchedule: 2,
	Account: 3,
	SCOT: 4,
	ITProcess: 5,
	AuditPlan: 6,
	Risk: 7,
	Task: 8,
	Misstatement: 9,
	Dificiency: 10,
	GAComponent: 11,
	GAComponentInstruction: 12,
	GAComponentEvidence: 13,
	GAScope: 14,
	GAPrimaryInstruction: 15,
	GAPrimary: 16,
	ClientRequest: 17,
	WCGW: 18,
	Control: 19,
	ITSOApplication: 20,
	CanvasForm: 21,
	FormSection: 22,
	FormBody: 23,
	Assertion: 24,
	ClientEngagement: 25,
	ClientGroup: 26,
	EngagementTag: 27,
	Engagement: 28,
	FormHeader: 29,
	FormStatus: 30,
	EngagementUser: 31,
	ClientGroupUser: 32,
	PSPIndex: 33,
	ITControl: 34,
	ITRisk: 35,
	AutomationLineItem: 36,
	PMAmount: 37,
	TEAmount: 38,
	SADNominalAmount: 39,
	UMTAmount: 40,
	Annotation: 41,
	EngagementComment: 42,
	Assignment: 43,
	PACEType: 44,
	TaskSection: 45,
	CalendarEvent: 46,
	TimePhase: 47,
	Approval: 48,
	DocumentQuickReference: 49,
	STEntity: 50,
	Estimate: 51,
	SampleItem: 53,
	SampleTagGroups: 54
};

export const entityCategory = {
	sampleFilters: 'sampleFilters'
};

export const specialBodiesSlices = {
	[Entity.SCOT]: 'scots',
	[Entity.Risk]: 'risks',
	[Entity.ITProcess]: 'itProcesses',
	[Entity.ITRisk]: 'itRisks',
	[Entity.ITControl]: 'itControls',
	[Entity.Control]: 'controls',
	[Entity.WCGW]: 'wcgws',
	[Entity.ITSOApplication]: 'itApplications',
	[Entity.Task]: 'tasks'
};

export const actionTypesEnum = {
	Open: 1
};

export const canvasWebRoutes = {
	addTask: '#addTask',
	addRequest: '#addClientRequest',
	addExternalTask: '#addExternalTask',
	clientRequestId: '#request',
	addCanvasForm: '#addCanvasForm',
	task: '#task',
	reviewerAssignments: '#reviewerAssignments',
	relateObject: '#relateObject',
	addFinding: '#addFinding',
	finding: '#finding',
	collaboration: '#clientRequests',
	manageGroup: '#managegroupaudit'
};

export const canvasUrlParams = {
	inCountryBaseUrl: 'inCountryBaseUrl',
	countryid: 'countryid',
	engagementid: 'engagementid',
	engagementdesc: 'engagementdesc',
	workspacedesc: 'workspacedesc',
	workspaceid: 'workspaceid',
	engagementversion: 'engagementversion',
	addTask: 'addTask',
	addRequest: 'addRequest',
	addExternalTask: 'addExternalTask',
	addFinding: 'addFinding',
	clientRequestId: 'clientRequestId',
	clientRequestGuid: 'clientRequestGuid',
	clientRequestStatusId: 'clientRequestStatusId',
	isRetry: 'isRetry',
	isClientRequest: 'isClientRequest',
	clientRequestTypeId: 'clientRequestTypeId',
	findingId: 'findingId',
	returnUrl: 'returnUrl',
	addCanvasForm: 'addCanvasForm',
	type: 'type',
	taskDetails: 'task',
	relateObject: 'relateObject',
	reviewerAssignments: 'reviewerAssignments',
	taskType: 'tasktype',
	deficiencyId: 'deficiencyid',
	misstatementId: 'misstatementid',
	finding: 'finding',
	standardId: 'standardid',
	languageId: 'languageid',
	formTypeId: 'formtypeid',
	isInterim: 'isinterim',
	mode: 'mode',
	taskGroup: 'taskgroup',
	categoryId: 'categoryid',
	subCategoryId: 'subcategoryid',
	includeCounts: 'includeCounts',
	includeBookmarks: 'includeBookmarks',
	includePagination: 'includePagination',
	includeUIElements: 'includeUIElements',
	includeScheduledInfo: 'includeScheduledInfo',
	includeAssignments: 'includeAssignments',
	includeTaskList: 'includeTaskList',
	documentId: 'documentId',
	taskBuildMilestone: 'taskBuildMilestone',
	riskCategory: 'riskCategory'
};

export const NoApprovalRequiredDocumentTypes = [
	documentTypes.GUIDED_WORKFLOWV2_NOSIGNOFF_REQUIRED,
	documentTypes.GUIDED_WORKFLOWV2_NOSIGNOFF_REQUIRED_TEMPORARY,
	documentTypes.MULTI_ENTITY_INDIVIDUAL_PROFILE_V2_NO_SIGN_OFF_REQUIRED
];

export const PaperProfileIDs = [documentTypes.PAPERPROFILE, documentTypes.PAPERPROFILETEMPORARY];

export const EngagementCommentStatus = {
	open: 1,
	cleared: 2,
	closed: 3
};

export const EngagementCommentStatusIds = {
	[1]: 'open',
	[2]: 'cleared',
	[3]: 'closed'
};

export const EngagementCommentFilterTypes = {
	toggle: 'toggle',
	commentClicked: 'commentClicked'
};

export const OperationTypes = {
	create: 'create',
	update: 'update',
	delete: 'delete',
	relate: 'relate', // Add relationship between entities
	unrelate: 'unrelate' // Remove relationship between entities
};

export const EngagementCommentInputModalModes = {
	create: OperationTypes.create,
	update: OperationTypes.update
};

export const CardTypes = {
	DefaultView: 1,
	CardViewTop: 2,
	CardViewMiddle: 3,
	CardViewBottom: 4,
	CardView: 5,
	CardViewMiddleTop: 6,
	CardViewBottomTop: 7
};

export const FormBodyBorders = {
	1: 'DefaultView',
	2: 'CardViewTop',
	3: 'CardViewMiddle',
	4: 'CardViewBottom',
	5: 'CardView',
	6: 'CardViewMiddleTop',
	7: 'CardViewBottomTop'
};

export const AresFormBodyBorders = {
	1: 'AresDefaultView',
	2: 'AresCardViewTop',
	3: 'AresCardViewMiddle',
	4: 'AresCardViewBottom',
	5: 'AresCardView',
	6: 'AresCardViewMiddleTop',
	7: 'AresCardViewBottomTop'
};

export const ViewModes = {
	Form: 0,
	Changes: 1,
	Details: 2
};

export const EngagementCommentToggleOptions = {
	allComments: 'allComments',
	myComments: 'myComments',
	assignedToMe: 'assignedToMe'
};

export const reviewNoteRoutes = {
	reviewNoteHub: '/reviewnotes/',
	path: {
		all: {id: entities.all, path: 'all'},
		document: {id: entities.Document, path: 'document'},
		task: {id: entities.Task, path: 'task'}
	},
	annotation: 'annotations/',
	users: 'Users/',
	hash: {
		add: '#AddNote',
		addDocNote: '#AddDocNote',
		addTaskNote: '#AddTaskNote',
		editNote: '#EditNote'
	},
	include: {
		roles: 'Roles'
	}
};

export const relateDocumentsTo = {
	canvasForm: 'CANVAS_FORM',
	canvasFormBody: 'CANVAS_FORM_BODY',
	canvasFormBodyAres: 'CANVAS_FORM_BODY_ARES',
	canvasFormBodyKronos: 'CANVAS_DOC_UPLOAD_BODY_KRONOS'
};

//TODO: These names are currently mapped to api response.
// Should be taken from api rather than placing them in constants
// Should be taken from api rather than placing them in constants
export const documentTabLayoutTabs = {
	evidenceName: 'evidences',
	temporaryName: 'temporaryfiles',
	priorPeriodName: 'prioryearevidences',
	externalDocsName: 'externaldocuments',
	groupDocsName: 'groupdocuments',
	conflictsName: 'conflicts',
	recentlyDeletedName: 'recentlydeleted',
	regionComponentName: 'regionComponent', //For the doc list of GA tasks.
	activityFeed: 'activityFeed',
	activityFeedComponent: 'activityFeedComponent',
	compareVersionsGroupSummary: 'compareVersionsGroupSummary',
	myPinnedDocuments: 'mypinneddocuments',
	teamPinnedDocuments: 'teampinneddocuments',
	glAccountDocUpload: 'glAccountDocUpload'
};

export const documentWithoutTabLayout = [
	documentTabLayoutTabs.conflictsName,
	documentTabLayoutTabs.regionComponentName,
	documentTabLayoutTabs.activityFeed,
	documentTabLayoutTabs.activityFeedComponent,
	documentTabLayoutTabs.compareVersionsGroupSummary,
	documentTabLayoutTabs.glAccountDocUpload,
	documentTabLayoutTabs.groupDocsName
];

export const fileTypesUpload = {
	options: ['pdf', 'docx', 'txt', 'log']
};

export const relatedDocumentsTab = {
	EVIDENCE: 'evidence',
	TEMPORARY: 'temporary',
	PRIOR_PERIOD: 'priorPeriod',
	EXTERNAL_DOCUMENTS: 'external',
	GUIDANCE: 'guidance'
};

export const reviewNoteTypes = {
	task: 1,
	document: 2
};

export const engagementUserStatus = {
	Undefined: 0,
	Active: 1,
	Pending: 2,
	Rejected: 3,
	DeActivated: 4,
	RequestForArchiveAccess: 5,
	RejectArchiveAccess: 6,
	CopyEngagementInactive: 7,
	UnlinkedInactive: 8,
	TemplateInactive: 9,
	RollForwardInactive: 10,
	LocalizedToIsolatedUniversal: 11,
	InvitationInProgress: 12,
	ScheduledInactive: 13
};

export const EngagementStatus = {
	Active: 1,
	Suspended: 2,
	Archived: 3,
	Deleted: 4,
	ArchiveInProgress: 5,
	Reactivated: 6,
	Restored: 7,
	MarkedForDeletion: 8,
	ArchiveError: 9,
	RollForwardError: 10,
	ReactivateError: 11,
	RestoreError: 12,
	RollForwardInProgress: 13,
	ReactivateInProgress: 14,
	RestoreInProgress: 15,
	GAMXMigrationInProgress: 16,
	GAMXMigrationError: 17,
	CreateTransactionPending: 18,
	CreateTransactionFailed: 19,
	UpdateTransactionPending: 20,
	UpdateTransactionFailed: 21,
	CopyInProgress: 22,
	CopyError: 23,
	CopyTemplateError: 24,
	ExternalReviewEngagementInProgress: 25,
	ExternalReviewEngagementError: 26,
	ExternalReviewEngagement: 27,
	LocalizedToIsolatedUniversal: 28,
	LocalizedToIsolatedUniversalArchived: 29,
	ContentDeliveryInProgress: 30,
	ContentDeliveryError: 31,
	ContentDeliveryFail: 32,
	CanvasExternalAccessPortalV2: 33,
	DatacenterSplit: 100
};

export const annotationType = {
	note: 1,
	issue: 2,
	comment: 3,
	clientRequestComment: 4,
	systemGenerated: 5
};

export const updateAction = {
	bulkUpdate: 0,
	editNote: 1,
	editReply: 2,
	replyNote: 3,
	bulkStatusUpdate: 4,
	editComment: 5,
	editAnnotation: 6
};

export const referenceNumber = {
	one: 1
};

export const updateAnnotationAction = {
	bulkUpdate: 0,
	editNote: 1,
	editReply: 2,
	replyNote: 3,
	bulkStatusUpdate: 4,
	deleteReply: 5,
	editAnnotation: 6
};

export const reviewNoteStatusFilter = {
	All: 0,
	Open: 1,
	Cleared: 2,
	Closed: 3
};

export const ReviewNoteStatusFilterParameter = {
	All: {value: 0},
	Open: {value: 1},
	OpenAndClosed: {value: 2},
	OpenAndCleared: {value: 3}
};

export const userSearchMaxLength = 50;
export const documentNameMaxLength = 115;

export const independenceStatus = {
	UNDEFINED: 0,
	INCOMPLETE: 1,
	NOMATTERSIDENTIFIED: 2,
	MATTERIDENTIFIEDPENDINGACTION: 3,
	MATTERRESOLVEDDENIEDACCESS: 4,
	MATTERRESOLVEDGRANTEDACCESS: 5,
	NOTAPPLICABLE: 6,
	RESTORED: 7,
	OVERRIDDEN: 8,
	PRIORNOMATTERSIDENTIFIED: 9,
	PRIORMATTERIDENTIFIEDPENDINGACTION: 10,
	PRIORMATTERRESOLVEDGRANTEDACCESS: 11,
	PRIORMATTERRESOLVEDDENIEDACCESS: 12
};

export const engagementAccessType = {
	DEFAULT: 1,
	INDEPENDENCEONLY: 2
};

export const roles = {
	PARTNERINCHARGE: 1,
	ENGAGEMENTPARTNER: 2,
	EXECUTIVEDIRECTOR: 3,
	PRINCIPAL: 4,
	SENIORMANAGER: 5,
	MANAGER: 6,
	SENIOR: 7,
	STAFF: 8,
	INTERN: 9,
	ENGAGEMENTQUALITYREVIEWER: 10,
	OTHERPARTNER: 11,
	GDSSTAFF: 12,
	ADVISORY: 13, // (ITRA,TAS,Human Capital or Other)
	TAX: 14,
	EXECUTIVESUPPORTSERVICES: 15,
	GENERALCOUNSEL: 16,
	AUDITQUALITYREVIEWER: 17,
	MLCOMPONENTTEAM: 18,
	CLIENTSUPERVISOR: 19,
	CLIENTSTAFF: 20,
	INTERNALAUDITSUPERVISOR: 21,
	INTERNALAUDITSTAFF: 22,
	REGULATOR: 23,
	OTHER: 24, // (E.G. DUE DILIGENCE REVIEW)
	OFFICE: 25,
	AREA: 26,
	INDUSTRY: 27,
	NATIONAL: 28,
	GLOBAL: 29,
	GDSSENIOR: 30,
	GDSMANAGER: 31
};

export const OfflineTaskListFilters = {
	categoryId: 2,
	subcategoryId: 1
};

export const independenceActions = {
	override: 'Override',
	grant: 'Grant',
	deny: 'Deny',
	submit: 'Submit'
};

export const helixBasisAmount = {
	DEFAULT: 0
};

export const helixAnalysisDateElapsedMonthCount = {
	DEFAULT: 12
};

export const confidentialityTypes = {
	DEFAULT: 0,
	LOW: 1,
	MODERATE: 2,
	HIGH: 3

	// This has been disabled for release 2.5, uncomment if required
	// CONFIDENTIAL: 4
};

export const canvasFormBannerTypes = {
	shared: 'Shared',
	independence: 'Independence',
	isSharedExternal: 'IsSharedExternal',
	profileV2: 'ProfileV2',
	profileV2Edit: 'profileV2Edit',
	independenceCustomInstructions: 'independenceCustomInstructions'
};

export const validationFilter = {
	NONE: 0,
	INCOMPLETE: 1,
	COMMENTS: 2
};

export const summaryFilters = {
	ALL: 0,
	SELECTEDANSWERS: 1,
	INCOMPLETERESPONSES: 2,
	UNRESOLVEDCOMMENTS: 3
};

export const navigationFilters = {
	ALL: 0,
	INCOMPLETERESPONSES: 1,
	UNRESOLVEDCOMMENTS: 2
};

export const assignmentTypes = {
	Preparer: 1,
	DetailReviewer: 2,
	GeneralReviewer: 3,
	Partner: 4,
	EQR: 5,
	Other: 6
};

export const summaryTypes = {
	PreparerAndDetailReviewer: 1,
	GeneralReviewer: 2,
	EQR: 3,
	None: 4
};

export const activitySummaryRoleOptions = [
	{label: 'rolePreparerLabel', value: assignmentTypes.Preparer},
	{label: 'roleDetailedReviewerLabel', value: assignmentTypes.DetailReviewer},
	{label: 'roleGeneralReviewerLabel', value: assignmentTypes.GeneralReviewer},
	{label: 'roleEQRLabel', value: assignmentTypes.EQR}
];

export const guidEmpty = '00000000-0000-0000-0000-000000000000';

export const taskIconNames = {
	defaultIcon: 'work-office-complete'
};

export const TaskStatusIds = {
	Open: 1,
	InProgress: 2,
	InReview: 3,
	Complete: 4,
	Removed: 5
};

export const taskStatus = {
	[TaskStatusIds.Open]: {
		className: 'open'
	},
	[TaskStatusIds.InProgress]: {
		className: 'progress'
	},
	[TaskStatusIds.InReview]: {
		className: 'review'
	},
	[TaskStatusIds.Complete]: {
		className: 'complete'
	},
	[TaskStatusIds.Removed]: {
		className: 'removed'
	},
	[TaskStatusIds.Completed]: {
		className: 'completed'
	}
};

export const AssignmentStatuses = {
	Open: 1,
	InProcess: 2,
	Complete: 3,
	Unassigned: 4
};

export const AnnotationAction = {
	add: 'add',
	edit: 'edit'
};

export const specialOptionsForRelateToRiskDropdown = {
	noRisksIdentified: -1,
	notAROMM: -2,
	rommSignificantRisks: 1,
	rommFraudRisks: 2,
	romm: 3
};

export const relateToRiskRationaleMaxCharacters = 4000;

export const activitySummarySelectOptions = [
	{label: 'allAnswers', value: 0},
	{label: 'incompleteResponses', value: 2},
	{label: 'unresolvedComments', value: 3}
];

export const navigationFilterSelectOptions = [
	{label: 'all', value: validationFilter.NONE},
	{label: 'incompleteResponses', value: validationFilter.INCOMPLETE},
	{label: 'unresolvedComments', value: validationFilter.COMMENTS}
];

export const aresSummaryHeaderId = -1;

export const aresTaskViewRoutes = {
	guidedworkflow: 'guidedworkflow',
	guidedworkflowV2: 'guidedworkflowv2',
	header: 'header',
	section: 'section',
	summary: 'summary'
};

export const misstatementAdjustmentGroup = {
	Adjustments: 'adjustments'
};

export const formNavigationButtonType = {
	next: 'next',
	back: 'back'
};

export const strategyTypes = {
	CONTROLS: 1,
	SUBSTANTIVE: 2,
	RELY: 3,
	NOTRELY: 4
};

export const riskTypes = {
	NOTAROMM: 0,
	SIGNIFICANTRISK: 1,
	FRAUDRISK: 2,
	INHERENTRISK: 3
};

export const riskFactorKnowledgeTag = {
	FRAUDRISK: 3,
	SIGNIFICANTRISK: 4
};

export const pspIndexesConstants = {
	MIN_SELECTED_PSP_INDEXES: 1,
	MAX_PSP_INDEXES_SELECTED: 5
};

export const themes = {
	light: 'light',
	dark: 'dark'
};

/* Loading indicator types */
export const loadingIndicatorTypes = {
	progress: 'progress',
	standard: 'standard'
};

export const materialityTypes = {
	PretaxIncome: 1,
	EBIT: 2,
	EBITDA: 3,
	GrossMargin: 4,
	Revenues: 5,
	OperatingExpenses: 6,
	Equity: 7,
	Assets: 8,
	Other: 9,
	CapitalBasedOther: 11,
	EarningsBasedOther: 12
};

export const materialitySADpercentageDecimalAmmount = 1;

export const currencyType = {
	Functional: 'Functional',
	Reporting: 'Reporting'
};

export const ShareResponseType = {
	CurrentToSelected: 'CurrentToSelected',
	SelectedToCurrent: 'SelectedToCurrent'
};

export const EstimateType = {
	yes: 'Yes',
	no: 'No'
};
export const helixConfigurationTimelineCheckmarkStatus = {
	Complete: 1,
	Warning: 2,
	Incomplete: 3,
	Error: 4
};

export const riskTypeIdEnum = {
	significant: 1,
	fraud: 2,
	inherent: 3
};

export const fileExtensionMapping = {
	// Need to mentioned keys in upper case becase same will be use for comparison.

	mapping: [
		{
			keys: ['XLS', 'XLSX', 'XLSM', 'CSV', 'XLTX', 'XLAM', 'XLSB'],
			mapIconName: 'excel'
		},
		{
			keys: ['DOCX', 'DOC', 'DOCM'],
			mapIconName: 'word'
		},
		{
			keys: ['PDF'],
			mapIconName: 'pdf'
		},
		{
			keys: ['JPG', 'JPEG', 'GIF', 'PNG'],
			mapIconName: 'jpg'
		},
		{
			keys: ['PPT', 'PPTX'],
			mapIconName: 'powerpoint'
		},
		{
			keys: ['PST', 'MSG'],
			mapIconName: 'outlook'
		},
		{
			keys: [''],
			mapIconName: 'paper-profile'
		},
		{
			keys: ['SMART', 'SMT', 'LS'],
			mapIconName: 'smart-evidence'
		},
		{
			keys: ['SMTA'],
			mapIconName: 'smt-icon'
		},
		{
			keys: ['ZIP'],
			mapIconName: 'zip'
		},
		{
			keys: ['FORM'],
			mapIconName: 'form'
		}
	]
};

export const Priority = {
	low: 1,
	medium: 2,
	high: 3,
	critical: 4
};

/*Enum for helix project import status complete list*/
export const helixProjectImportStatus = {
	InQueue: 1,
	InProgress: 2,
	Completed: 3,
	Error: 4,
	NotStarted: 5
};

export const actionType = {
	IndependenceException: 1,
	ProfileAnswer: 2,
	RiskFactor: 3,
	MaterialityBasis: 4,
	PMRiskLevel: 5,
	TERiskLevel: 6,
	OverrideResponse: 7,
	SADRange: 8,
	PersonalIndependenceResponse: 9,
	CanvasAction: 10,
	ParentProfileAnswer: 11,
	ParentRiskFactor: 12,
	ParentMaterialityBasis: 13,
	ParentPMRiskLevel: 14,
	ParentTERiskLevel: 15,
	ParentSADRange: 16,
	ParentAccountEstimate: 17,
	AccountEstimate: 18,
	ParentEstimateInherentRisk: 19,
	EstimateInherentRisk: 20,
	ParentMultiEntityProfile: 21,
	MultiEntityProfile: 22,
	ParentAccountType: 23,
	AccountType: 24,
	ParentInherentRiskCategoryAndLevel: 25,
	InherentRiskCategoryAndLevel: 26,
	ParentGroupInvolvementCategory: 27,
	GroupInvolvementCategory: 28,
	ParentAssociateRisk: 29,
	AssociateRisk: 30
};

export const controlType = {
	itApplicationControl: 1,
	itDependentManualControl: 2,
	manualPrevent: 3,
	manualDetect: 4
};

/* Notes modal constants */

export const notesFilter = {
	allNotes: 1,
	myNotes: 2,
	authoredByMeNotes: 3
};

export const maxLengthText = {
	maxLength100: 100,
	maxLength200: 200,
	maxLength400: 400,
	maxLength500: 500,
	maxLength4000: 4000
};

export const colorArray = [
	'Maroon100',
	'Maroon200',
	'Maroon300',
	'Maroon400',
	'Maroon500',
	'Maroon600',
	'Maroon700',
	'black',
	'blackyellow',
	'blue100',
	'blue200',
	'blue300',
	'blue400',
	'blue500',
	'blue600',
	'blue600yellow400',
	'blue700',
	'gr50black',
	'gr50grey450',
	'gr50grey600',
	'green100',
	'green200',
	'green300',
	'green400',
	'green500',
	'green600',
	'green700',
	'grey50',
	'grey100',
	'grey100blue15',
	'grey100blue500',
	'grey100grey450',
	'grey100grey550',
	'grey200',
	'grey225',
	'grey250',
	'grey300',
	'grey300black',
	'grey400',
	'grey450',
	'grey500',
	'grey500grey450',
	'grey500grey600',
	'grey550',
	'grey600',
	'orange100',
	'orange200',
	'orange300',
	'orange400',
	'orange500',
	'orange600',
	'orange700',
	'purple100',
	'purple200',
	'purple300',
	'purple400',
	'purple500',
	'purple600',
	'purple700',
	'red100',
	'red200',
	'red300',
	'red400',
	'red500',
	'red600',
	'red700',
	'teal100',
	'teal200',
	'teal300',
	'teal400',
	'teal500',
	'teal600',
	'teal700',
	'white',
	'whiteGrey200',
	'whiteGrey250',
	'whiteGrey450',
	'whiteGrey550',
	'whiteblack',
	'yellow400'
];

export const fontSizeMapping = {
	S: {fontSize: 10, lineHeight: 16},
	M: {fontSize: 12, lineHeight: 20},
	L: {fontSize: 14, lineHeight: 20},
	XL: {fontSize: 16, lineHeight: 22},
	XXL: {fontSize: 20, lineHeight: 24}
};

export const fontWeightMapping = {
	light: 300,
	normal: 400,
	bold: 600
};

export const gaRoleTypes = {
	primary: 1,
	regional: 2,
	component: 3
};

export const gaRegion = {
	notApplicable: 1
};
export const pointOfContactTypes = {
	EYcontact: 1,
	externalContact: 2
};
export const gaScopeType = {
	full: 1,
	specific: 2,
	specifiedAuditProcedures: 3,
	review: 4
};

export const HelixSnapshotDefaultExtension = 'png';

export const documentsApiExpandableItems = {
	tasks: 'tasks'
};

export const riskFactorRelationPatchDataType = {
	riskId: 'riskId',
	rationale: 'rationale',
	documentId: 'documentId',
	isNotMaterial: 'isNotMaterial'
};

export const materialityInputNames = {
	sadNominalAmountInput: 'sadNominalAmountInput',
	tolerableErrorInput: 'tolerableErrorInput',
	planningMaterialityInput: 'planningMaterialityInput'
};

export const maxLengthAnnotation = 4000;

export const trialBalanceType = {
	Planning: 1,
	PlanningPriorYear: 2,
	FinancialStatements: 3,
	FinancialStatementsPriorPeriod: 4
};

export const trialBalanceCategories = {
	Planning: 1,
	CurrentPeriod: 2,
	PriorPeriod: 3
};

export const financialStatementType = {
	Asset: 1,
	CurrentAsset: 2,
	NonCurrentAsset: 3,
	Liability: 4,
	CurrentLiability: 5,
	NonCurrentLiability: 6,
	Equity: 7,
	Revenue: 8,
	Expense: 9,
	NonOperatingIncome: 10,
	OtherComprehensiveIncome: 11,
	Other: 12,
	AccountType: 13,
	AccountSubType: 14,
	AccountClass: 15,
	AccountSubClass: 16,
	NetIncomeExpense: 17
};

export const helixViewerError = {
	errorFetchingToken: 1000
};

export const financialStatementCategory = {
	BalanceSheet: 1,
	IncomeStatement: 2,
	All: 3
};

export const toastMessageDisplayDuration = 3000;

export const ToastMessageVariants = {
	Error: 'error',
	Warning: 'warning',
	Success: 'success',
	Info: 'info'
};

export const VOICE_NOTE_DOCUMENT_SOURCE_ID = 8;

export const CanvasFormOpenType = {
	New: 1,
	Existing: 2,
	Drawer: 3
};

export const CanvasFormsIds = [
	documentTypes.CANVASFORMEVIDENCE,
	documentTypes.ACCOUNT_CONCLUSION,
	documentTypes.CANVASFORMTEMPORARY,
	documentTypes.IT_FLOW_ISA315,
	documentTypes.GUIDEDWORKFLOWTEMPORARY,
	documentTypes.GUIDEDWORKFLOW,
	documentTypes.LIMITEDRISKDOCUMENTATION,
	documentTypes.GUIDEDWORKFLOWV2,
	documentTypes.ESTIMATESDOCUMENT,
	documentTypes.ANALYTICSDOCUMENT,
	documentTypes.MULTIENTITYENGAGEMENTSUBPROFILE,
	documentTypes.MULTIENTITYINDIVIDUALPROFILE,
	documentTypes.ACCOUNTLEVELRISKASSESSMENT,
	documentTypes.PIC_CHECKLIST,
	documentTypes.EQR_CHECKLIST,
	documentTypes.GROUPSTRUCTUREGUIDEDWORKFLOW,
	documentTypes.GROUPINSTRUCTIONGUIDEDWORKFLOW,
	documentTypes.ALRA_INDIVIDUAL,
	documentTypes.GROUP_INVOLVEMENT_SUMMARY,
	documentTypes.GROUP_INVOLVEMENT_INDIVIDUAL_COMPONENT,
	documentTypes.CLIENT_SHARED_HELIX_ANALYTIC,
	documentTypes.ACCOUNT_STANDARD_WORKING_PAPER,
	documentTypes.ACCOUNT_STANDARD_WORKING_PAPER_TEMPORARY,
	documentTypes.ALRA_INDIVIDUAL_TEMPORARY,
	documentTypes.CONTROL_UNDERSTAND_CONTROLS,
	documentTypes.CONTROL_UNDERSTAND_CONTROLS_TEMPORARY,
	documentTypes.CONTROL_TEST_CONTROL_ATTRIBUTES,
	documentTypes.CONTROL_TEST_CONTROL_ATTRIBUTES_TEMPORARY,
	documentTypes.SCOT_WALKTHROUGH,
	documentTypes.SCOT_WALKTHROUGH_TEMPORARY,
	documentTypes.SCOT_SUMMARYV2,
	documentTypes.SCOT_SUMMARYV2_TEMPORARY,
	documentTypes.ESTIMATE_WALKTHROUGH,
	documentTypes.ESTIMATE_WALKTHROUGH_TEMPORARY,
	documentTypes.ESTIMATE_INDIVIDUAL_ESTIMATE_TEMPORARY,
	documentTypes.EVIDENCE_DISPLAYED_NO_APPROVAL_REQUIRED,
	documentTypes.GUIDED_WORKFLOWV2_NOSIGNOFF_REQUIRED,
	documentTypes.GUIDED_WORKFLOWV2_NOSIGNOFF_REQUIRED_TEMPORARY,
	documentTypes.CRA_SUMMARY_V2,
	documentTypes.CRA_SUMMARY_V2_TEMPORARY,
	documentTypes.IT_APP_PLANNING_INDIVIDUAL,
	documentTypes.IT_APP_PLANNING_INDIVIDUAL_TEMPORARY,
	documentTypes.IT_APP_RISK_ASSESSMENT_INDIVIDUAL,
	documentTypes.IT_APP_RISK_ASSESSMENT_INDIVIDUAL_TEMPORARY,
	documentTypes.IT_PROCESS_WALKTHROUGH_INDIVIDUAL,
	documentTypes.IT_PROCESS_WALKTHROUGH_INDIVIDUAL_TEMPORARY,
	documentTypes.CONTROL_DEFICIENCY_INDIVIDUAL,
	documentTypes.CONTROL_DEFICIENCY_INDIVIDUAL_TEMPORARY,
	documentTypes.REVIEW_AND_APPROVAL_SUMMARY,
	documentTypes.REVIEW_AND_APPROVAL_SUMMARY_TEMPORARY
];

export const GALinkStatus = {
	NoPointOfContact: 1,
	NotSent: 2,
	Pending: 3,
	Accepted: 4,
	Rejected: 5,
	Expired: 6,
	Unlinked: 7,
	Delegated: 8,
	SystemError: 9,
	Sent: 10,
	InvalidPointOfContact: 11,
	ComponentNotUsingCanvas: 12,
	ReferenceOnly: 13
};

export const jsonDocumentPatchOperations = {
	add: 'Add',
	remove: 'Remove',
	replace: 'Replace',
	copy: 'Copy',
	move: 'Move',
	test: 'Test'
};

export const GAComponentTableColumns = {
	name: 'name',
	pointOfContact: 'pointOfContact',
	role: 'role',
	linkRequest: 'linkRequest',
	groupInstructionsStatus: 'groupInstructionsStatus',
	actions: 'actions',
	scope: 'scope',
	discussions: 'discussions',
	EDAP: 'EDAP',
	siteVisits: 'siteVisits',
	reviewWorkComponent: 'reviewWorkComponent',
	other: 'other',
	significantUpdates: 'significantUpdates',
	executionComplete: 'executionComplete',
	changesPublishedNotSent: 'changesPublishedNotSent',
	riskAssessment: 'riskAssessment'
};

export const GAGIFormActionColumns = {
	EDAP: {formActionValue: 1, orderValue: 2},
	Discussions: {formActionValue: 2, orderValue: 1},
	ReviewingWorkOfComponent: {formActionValue: 3, orderValue: 4},
	SignificantUpdates: {formActionValue: 4, orderValue: 6},
	SiteVisits: {formActionValue: 5, orderValue: 3},
	Other: {formActionValue: 6, orderValue: 5},
	ExecutionComplete: {formActionValue: 7, orderValue: 7}
};

export const GAInstructionBodyTemplateColumns = {
	instructionName: 'instructionName',
	dueDate: 'dueDate',
	components: 'components',
	documentsCount: 'documentsCount',
	deleteInstruction: 'deleteInstruction'
};

export const ListHeaderColumnTypes = {
	label: 'label',
	custom: 'custom',
	icon: 'icon',
	number: 'number',
	empty: 'empty'
};

export const inherentRiskTypeId = {
	NONE: 0,
	HIGHER: 1,
	LOWER: 2,
	NOT_RELEVANT: 3
};

export const KnowledgeSectionIds = {
	GeneralCommunications: 1, // Old Key Information
	ScopeOfWork: 2,
	ReportingForms: 3,
	ProceduresPerformedCentrally: 4,
	GroupRiskAssessment: 5,
	OtherCommunications: 6
};

export const KnowledgeSections = {
	[KnowledgeSectionIds.GeneralCommunications]: 'GeneralCommunications',
	[KnowledgeSectionIds.ScopeOfWork]: 'ScopeOfWork',
	[KnowledgeSectionIds.ReportingForms]: 'ReportingForms',
	[KnowledgeSectionIds.ProceduresPerformedCentrally]: 'ProceduresPerformedCentrally',
	[KnowledgeSectionIds.GroupRiskAssessment]: 'GroupRiskAssessment',
	[KnowledgeSectionIds.OtherCommunications]: 'OtherCommunications'
};

export const sortConstants = {
	knowledgeDisplayOrder: 999.0
};

export const ViewKeys = {
	Estimate: 'ESTIMATE_BODY_DOCUMENT',
	Account: 'ACCOUNT_BODY_DOCUMENT'
};

export const GAFormCategory = {
	AccountRiskAssessment: 1,
	GroupInvolvement: 2
};

export const ExpandValues = {
	GAScopes: 'gascopes',
	GATaskDocuments: 'gataskdocuments',
	GSTeamsToSend: 'gsTeamsToSend',
	AllExceptReference: 'allExceptReference'
};

/* MultiEntitySelectAccountExecutionType body type accounts and executionType filter */

export const accountTypeAllowed = [1, 2, 5];

export const executionTypeAllowed = [1, 3];

export const multiEntityType = {
	None: 0,
	DefaultDelivery: 1,
	EngagementDelivery: 2,
	PerEntityDelivery: 3
};

export const HelixAttributesUrlTags = {
	CurrencyCodeTag: `CurCd=`,
	CurrencyTypeTag: `CurType=`,
	AnalysisYearToDateTag: `AnalysYtd=`,
	ComparativeYearToDateTag: `CompYtd=`,
	ComparativeQtr: `CompQtr=`,
	ComparativePeriod: `CompPer=`,
	BusinessUnitTag: `BU=`,
	ActionTypeTag: `AcTyp=`,
	EYAccountTag: `EYAcct=`,
	AnalysisQtr: `AnsQtr=`,
	AnalysisPeriod: `AnsPer=`,
	SeparatorTag: `~|~`,
	AmpersandTag: `~&~`,
	BUTag: 'BU',
	SecondComparativeYearToDateTag: `2CompYtd=`,
	CompareTag: `Compare=`
};

export const HelixReportKeys = {
	FSC: 'FSC',
	UIC: 'UIC',
	OSJE: 'OSJE'
};

export const HelixComponentKeys = {
	ActAreaC: 'ActAreaC',
	AcctMetricT: 'AcctMetricT',
	BalCompT: 'BalCompT',
	BalAreaC: 'BalAreaC',
	Dimchange: 'Dimchange',
	Osje: 'Osje',
	BalComp3P: 'BalComp3P'
};

export const GAInstructionStatus = {
	NotSent: 1,
	Pending: 2,
	Accepted: 3,
	Rejected: 4,
	SystemError: 5,
	Expired: 6,
	Sent: 7,
	Unlinked: 8,
	Queued: 9
};

export const ValidSentInstructionStatuses = [
	GAInstructionStatus.NotSent,
	GAInstructionStatus.Rejected,
	GAInstructionStatus.SystemError,
	GAInstructionStatus.Expired,
	GAInstructionStatus.Sent,
	GAInstructionStatus.Pending,
	GAInstructionStatus.Queued,
	GAInstructionStatus.Accepted
];

export const ValidLinkStatuses = [
	GALinkStatus.Accepted,
	GALinkStatus.Sent,
	GALinkStatus.Pending,
	GALinkStatus.ComponentNotUsingCanvas
];

export const KnowledgeInstructionSection = {
	GeneralCommunications: 1,
	ScopeOfWork: 2,
	ReportingForms: 3,
	ProceduresPerformedCentrally: 4,
	GroupRiskAssessment: 5,
	OtherCommunications: 6
};

export const frameSubEvent = {
	COMPARE_SECTION_CHANGED: 'compareSectionChanged',
	TRACK_CHANGES_SECTION_CHANGED: 'trackChangesSectionChanged',
	FRAME_READY: 'frameReady',
	COMPARE_DOCUMENT_LIST: 'CompareDocumentList',
	UPDATE_FRAME_TITLE: 'UpdateFrameTitle',
	MORE_MENU_CLICK: 'MoreMenuClick',
	CLOSE_MENU: 'CloseMenu'
};

export const sendAllModal = {
	startSeparator: '/GroupComponents/',
	endSeparator: '/SendInstructions'
};

export const sendInstructionsSwitcherIds = {
	groupInstructions: 'groupInstructions',
	groupRiskAssessment: 'groupRiskAssessment'
};

export const groupInvolvementSources = {
	groupInvolvementIndex: 'groupInvolvementIndex',
	groupInvolvementSummary: 'groupInvolvementSummary'
};

export const specialBodiesWithNoTrackChanges = [
	formBodyType.RelatedWCGWsSCOTs,
	formBodyType.RelatedRisks,
	formBodyType.RelatedITRiskITProcess,
	formBodyType.RelatedITApps,
	formBodyType.PlanningMateriality,
	formBodyType.TolerableError,
	formBodyType.SADNominalAmount,
	formBodyType.UncorrectedMisstatementsThreshold,
	formBodyType.HelixComponent,
	formBodyType.RelateRiskFactor,
	formBodyType.RelateRiskAssertion,
	formBodyType.DisplayMeasurementBasisBody,
	formBodyType.MaterialityAdjustedBasisInput,
	formBodyType.SliderLegend,
	formBodyType.GuidedWorkflowPlanningMateriality,
	formBodyType.TEMateriality,
	formBodyType.MaterialitySADInput,
	formBodyType.UserLookup,
	formBodyType.CreateRiskFactorBody,
	formBodyType.ISA315ScotItApplication,
	formBodyType.ISA315ItApplicationScot,
	formBodyType.ISA315ItProcessItApplication,
	formBodyType.ISA315ItProcessTask,
	formBodyType.ISA315ItProcessItRisk,
	formBodyType.ISA315ItGCTestStrategy,
	formBodyType.ISA315SoScot,
	formBodyType.ISA315ManageItSp,
	formBodyType.OtherBasisTypeDescription,
	formBodyType.DocumentUploadRequired,
	formBodyType.CheckboxViewport,
	formBodyType.RadioVerticalViewport,
	formBodyType.KnowledgeOnlyViewport,
	formBodyType.TextResponseViewport,
	formBodyType.EstimateListing,
	formBodyType.RelateSCOT,
	formBodyType.AnalyticsOverview,
	formBodyType.BalanceSheetTable,
	formBodyType.IncomeStatementTable,
	formBodyType.ViewAndEditAccount,
	formBodyType.MaterialityViewer,
	formBodyType.ALRASummary,
	formBodyType.EstimatesLeadsheet,
	formBodyType.EstimateBalanceInput,
	formBodyType.RelateEstimateToAssertion,
	formBodyType.RelateEstimateToRisk,
	formBodyType.Summary,
	formBodyType.MultiEntityList,
	formBodyType.MultiEntityProfileListing,
	formBodyType.MultiEntityPSPContentUpdateSubmit,
	formBodyType.MultiEntityRelateEntityToAccount,
	formBodyType.MultiEntitySelectAccountExecutionType,
	formBodyType.MultiEntityProfileSubmit,
	formBodyType.AssessInheritRisk,
	formBodyType.ALRAAccountList,
	formBodyType.GroupStructureComponentList,
	formBodyType.GroupStructureRelateAccountsToComponents,
	formBodyType.GroupStructureGroupStructureSummary,
	formBodyType.GroupInstructionsGeneralCommunications,
	formBodyType.GroupInstructionsReportingFormList,
	formBodyType.GroupInstructionsOtherCommunicationsList,
	formBodyType.GroupInstructionsGroupRiskAssessment,
	formBodyType.GroupInstructionsGroupInstructionsSummary,
	formBodyType.HelixComponentV2,
	formBodyType.GroupInvolvementGroupInvolvementIndex,
	formBodyType.GroupInvolvementGroupInvolvementSummary,
	formBodyType.DocumentUploadPICApprovalRequired,
	formBodyType.DocumentUploadEQRApprovalRequired,
	formBodyType.DocumentUploadLegend,
	formBodyType.DocumentUploadPICEQRApprovalRequired,
	formBodyType.DocumentUploadAnyApprovalRequired,
	formBodyType.FlowChart,
	formBodyType.ControlAttributes,
	formBodyType.SampleList,
	formBodyType.ControlProperties,
	formBodyType.GroupRiskAssessmentV2,
	formBodyType.SampleListSize,
	formBodyType.AccountRelatedEstimateListing,
	formBodyType.IndividualAccountAttributes,
	formBodyType.AccountStandardROMMListing
];

export const taskSearchTypes = {
	document: 'RelatedDocument',
	ITProcess: 'ITProcess'
};

export const picEqrbodyTypeIdList = [
	formBodyType.DocumentUploadPICApprovalRequired,
	formBodyType.DocumentUploadEQRApprovalRequired,
	formBodyType.DocumentUploadPICEQRApprovalRequired,
	formBodyType.DocumentUploadAnyApprovalRequired
];

export const viewportBodyTypesList = [
	formBodyType.CheckboxViewport,
	formBodyType.RadioVerticalViewport,
	formBodyType.KnowledgeOnlyViewport,
	formBodyType.TextResponseViewport
];

export const riskTypeDisplayOrder = [riskTypes.SIGNIFICANTRISK, riskTypes.FRAUDRISK, riskTypes.INHERENTRISK];

export const controlEffectivenessType = {
	None: 0,
	Effective: 1,
	Ineffective: 2
};

export const hideValidationsWithoutApproval = {
	PIC_EQR_BODIES: 4
};

export const flowchartNodeType = {
	flowChartColumn: 'FlowchartColumn',
	flowChartStep: 'FlowchartStep'
};

export const flowchartStepMode = {
	addMode: 'add',
	editMode: 'edit'
};

export const orderByType = {
	flowChartDocumentOrder: 'HasExecutiveReview'
};

export const cssPixel = {
	ZeroRem: 0,
	OneRem: 1,
	TwoRem: 32,
	ThreeRem: 48,
	ThreeDot125Rem: 50,
	SixRem: 96
};

export const flowchartColumnType = {
	initiating: 'Initiating',
	initiation: 'Initiation',
	processing: 'Processing',
	recording: 'Recording',
	reporting: 'Reporting'
};

export const flowchartStepCssName = {
	initiating: 'column-1-card',
	processing: 'column-3-card',
	recording: 'column-2-card',
	reporting: 'column-4-card'
};

export const flowChartNodeConnectionPositions = [
	{id: 1, position: 'top'},
	{id: 2, position: 'bottom'},
	{id: 3, position: 'left'},
	{id: 4, position: 'right'}
];

export const flowchartConnectorTypes = {
	default: 1,
	pointertype: 2
};

export const controlDesignEffectivenessLables = {
	0: 'Not Selected',
	1: 'Effective',
	2: 'Ineffective'
};

export const booleanControlLabel = {
	false: 'No',
	true: 'Yes'
};

export const stepsWCGWTabs = [
	{
		id: 1,
		label: 'WCGWs',
		relateLabel: 'relateLabelForWCGW',
		listField: 'flowchartStepsWCGW',
		noRowsMessageField: 'wcgwNoRowsMessage',
		value: 0
	},
	{
		id: 2,
		label: 'Controls',
		relateLabel: 'relateLabelForControl',
		listField: 'flowchartStepsControl',
		noRowsMessageField: 'controlNoRowsMessage',
		value: 0
	},
	{
		id: 3,
		label: 'IT',
		relateLabel: 'relateLabelForITApp',
		listField: 'flowchartStepsITApplication',
		noRowsMessageField: 'itAppNoRowsMessage',
		value: 0
	},
	{
		id: 4,
		label: 'Service Organizations',
		relateLabel: 'relateLabelForServiceOrganisation',
		listField: 'flowchartStepsSO',
		noRowsMessageField: 'serviceOrganisationNoRowsMessage',
		value: 0
	}
];

export const attributeTestingTypes = {
	inquiry: 1,
	observation: 2,
	inspection: 3,
	reperformance: 4
};

export const scotSummary225Tabs = [
	{id: 0, name: 'Show by account'},
	{id: 1, name: 'Show by SCOT'}
];

export const sampleTypes = {
	controlAttributeTesting: 1
};

export const maxLengthAttributeIndex = 5;
export const maxLengthAttributeDescription = 4000;

export const FIT_GET_ALL_RELATED_DOCUMENTS = 'FIT_GET_ALL_RELATED_DOCUMENTS';

export const estimateLeadsheetViewTypes = {
	leadsheet: 1,
	accounts: 2,
	scots: 3,
	validations: 4
};

export const riskCategory = {
	risks: 'risks',
	estimates: 'estimates'
};

export const body198EntityButtonList = ['account', 'New Scot', 'scot', 'estimate'];

export const body225EntityButtonList = ['scot', 'account'];

export const xyCoordinates = {
	add_Y_Coordinate: 350,
	default_X_Coordinate: 224,
	default_Y_Coordinate: 128
};

export const reactFlowColumn = {
	width: 780,
	defaultHighestHeight: 650,
	defaultGapBtwStepAndHeight: 400,
	stepHeight282: 278,
	stepHeight204: 204,
	stepXPosition108: 108
};

export const estimateTypes = {
	veryLow: 4,
	low: 5,
	higher: 6,
	notSelected: 7
};

export const riskTypeList = [
	{id: 1, name: 'Significant risks', abbrev: 'SR', label: 'Significant', title: 'Significant risk'},
	{id: 2, name: 'Fraud risks', abbrev: 'FR', label: 'Fraud', title: 'Fraud risk'},
	{
		id: 3,
		name: 'Risk of material misstatement',
		abbrev: 'IR',
		label: 'Risk of material misstatement',
		title: 'Risk of material misstatement'
	},
	{id: 4, name: 'Very low risk estimate', abbrev: 'VLRE', label: 'Very Low', title: 'Very low risk estimate'},
	{id: 5, name: 'Lower risk estimate', abbrev: 'LRE', label: 'Lower', title: 'Lower risk estimate'},
	{id: 6, name: 'Higher risk estimate', abbrev: 'HRE', label: 'Higher', title: 'Higher risk estimate'},
	{id: 7, name: 'Estimate - Not selected', abbrev: 'NA', label: 'Not selected', title: 'Estimate - Not selected'}
];

export const constRelateEstimateToRisk = {
	ACCOUNT_NAME: 'accountName',
	RISK_TYPE_ID: 'riskTypeId'
};
export const ScotType = {
	Routine: 1,
	NonRoutine: 2,
	Estimation: 3,
	FinancialStatementCloseProcess: 4
};

// Do not use this. Please use entities enum instead
export const entityType = {
	scot: 4
};

export const constAddEditFlowChartStep = {
	Next: 'next',
	Previous: 'previous',
	ParentXPosition: 'parentXPosition',
	PositionY: 'position.y',
	PositionX: 'position.x'
};

// FIT Navigation panel
export const NavigationPanelViews = {
	Activity: 'activity',
	Sections: 'sections'
};

export const TagCategoryFilter = {
	enagagementFilter: 1,
	notes: 2,
	filteringNotArchived: 3
};

export const buttonLabelDirections = {
	left: 'left',
	right: 'right'
};

// Representes the level of modal toasts.
export const modalLevelToast = {
	level1: 1,
	level2: 2
};

export const canvasClient = {
	source: 'EY Canvas',
	kind: 'GRA'
};

export const navigationModalEvents = {
	key: 'navigationModalKey',
	eventKey: 'forceModalOpen_',
	markComplete: 'markcomplete',
	share: 'share',
	copy: 'copy'
};

export const groupRiskAssessmentAccessViewRoles = ['group risk assessment view role', 'All'];
export const contactListAccessViewRoles = ['Contacts', 'contacts view role', 'All'];

export const milestonesAccessViewRoles = ['Milestones', 'milestones view role', 'All'];
export const projectManagementAccessViewRoles = ['Project management', 'project management view role', 'All'];
export const groupAuditAccessViewRoles = ['group audit view role', 'All'];
export const insightAccessViewRoles = ['Insights', 'audit insight view role', 'All'];
export const reportingAccessViewRoles = ['Reporting', 'reporting view role', 'All'];
export const ccpAccessViewRoles = [
	'EY Canvas Client Portal',
	'Canvas Client Portal',
	'canvas client portal view role',
	'All'
];
export const dashboardAccessViewRoles = [
	...milestonesAccessViewRoles,
	...projectManagementAccessViewRoles,
	...groupAuditAccessViewRoles,
	...insightAccessViewRoles,
	...reportingAccessViewRoles,
	...ccpAccessViewRoles
];

export const ovsRoutes = {
	dashboard: '/dashboard',
	contacts: '/contacts',
	gra: '/groupriskassessment'
};

export const FlowchartStepToolTipKeys = {
	WCGW: 'isWCGWTooltipVisible',
	CONTROL: 'isControlTooltipVisible',
	IT_APP: 'isITAppTooltipVisible',
	SO: 'isSOTooltipVisible',
	DOCUMENT: 'isDocumentTooltipVisible'
};

export const FlowchartStepNameKeys = {
	WCGW: 'wcgwName',
	CONTROL: 'controlName',
	IT_APP: 'itApplicationName',
	SO: 'soName',
	DOCUMENT: 'documentName'
};

export const FlowchartStepKeys = {
	WCGW: 'flowchartStepsWCGW',
	CONTROL: 'flowchartStepsControl',
	IT_APP: 'flowchartStepsITApplication',
	SO: 'flowchartStepsSO',
	DOCUMENT: 'flowchartStepsDocument'
};
export const accountsFilter = {
	allAccounts: 1,
	accountsWithRelatedEstimates: 2,
	accountsWithoutRelatedEstimates: 3
};
export const scotsFilter = {
	allScots: 1,
	scotsWithRelatedEstimates: 2,
	scotsWithoutRelatedEstimates: 3
};

export const modalLevels = {
	defaultLevel: 0
};

export const storageKeys = {
	bodyPagination: 'bodyPagination'
};

export const documentTypesShowedOnLeftNav = [
	...GuidedWorkflowV2DocumentTypes.filter((dt) => IndividualFitDocumentTypes.indexOf(dt) === -1)
];

export const ValidationWithoutApprovalsDocumentTypes = [
	documentTypes.INDEPENDENCEFORMINDIVIDUAL,
	documentTypes.PROFILEV2,
	documentTypes.SCOT_WALKTHROUGH,
	documentTypes.ESTIMATE_WALKTHROUGH,
	documentTypes.MULTI_ENTITY_INDIVIDUAL_PROFILE_V2_NO_SIGN_OFF_REQUIRED,
];

export const ValidationExcludedDocumentTypes = [documentTypes.INDEPENDENCEFORMTEMPLATE];

export const tagColors = ['Red', 'Orange', 'Teal', 'Blue', 'Purple', 'Green'];

export const tagFillColor = {
	red: 'var(--red-500)',
	orange: 'var(--orange-300)',
	teal: 'var(--teal-300)',
	blue: 'var(--blue-400)',
	purple: 'var(--purple-300)',
	green: 'var(--green-500)'
};

export const tagTypes = {
	Filtering: 1,
	Notes: 2,
	FilteringNotArchived: 3
};

export const SortDirection = {
	SORT_ASC: 'SORT_ASC',
	SORT_DESC: 'SORT_DESC',
	SORT_NOSORT: 'SORT_NOSORT'
};

export const multiEntityTypes = {
	tasksByEntity: 1,
	tasksByEngagement: 2,
	perEntity: 3
};

export const aresOnlyRelateDocumentBodies = [
	formBodyType.DocumentUploadPICApprovalRequired,
	formBodyType.DocumentUploadEQRApprovalRequired,
	formBodyType.DocumentUploadLegend,
	formBodyType.DocumentUploadPICEQRApprovalRequired,
	formBodyType.DocumentUploadAnyApprovalRequired,
	formBodyType.DocumentUpload,
	formBodyType.DocumentUploadRequired,
	formBodyType.FlowChart,
	formBodyType.GroupInstructionsGeneralCommunications,
	formBodyType.GroupInstructionsReportingFormList,
	formBodyType.GroupInstructionsOtherCommunicationsList,
	formBodyType.GroupInstructionsGroupRiskAssessment,
	formBodyType.GroupInstructionsGroupInstructionsSummary
];

export const permissionsPolicy = 'clipboard-write';

export const helixImportStatus = {
	importRequestedFromHelix: 1,
	importBeingProcessedByEYCanvas: 2,
	importCompleted: 3,
	importError: 4,
	hasPreviousSuccessfulImport: 5,
	disconnected: 6
};

export const flowchartTabMapping = {
	1: {field: 'flowchartStepsWCGW', label: 'wgcwTabLabel'},
	2: {field: 'flowchartStepsControl', label: 'controlsTabLabel'},
	3: {field: 'flowchartStepsITApplication', label: 'itAppsTabLabel'},
	4: {field: 'flowchartStepsSO', label: 'serviceOrganisationTabLabel'}
};

export const engagementSourceId = {
	CopyEngagement: 3,
	CEAPV2: 9,
	CEAPV2Protected: 10,
	CopyEngagementReviewOnly: 12
};

export const hideAIChatbotForEngagementSources = [
	engagementSourceId.CEAPV2,
	engagementSourceId.CEAPV2Protected,
	engagementSourceId.CopyEngagementReviewOnly
];

export const ITApplicationTypesIds = {
	applicationTool: 1,
	database: 2,
	operatingSystem: 3,
	network: 4,
	uncategorized: 6
};

export const ITApplicationTypes = [
	{value: ITApplicationTypesIds.applicationTool, label: 'Application/Tool'},
	{value: ITApplicationTypesIds.database, label: 'Database'},
	{value: ITApplicationTypesIds.operatingSystem, label: 'Operating system'},
	{value: ITApplicationTypesIds.network, label: 'Network'},
	{value: ITApplicationTypesIds.uncategorized, label: 'Uncategorized'}
];

export const ITApplicationApiRequestFilters = {
	applicationOrTool: 'applicationOrTool',
	database: 'database',
	operatingSystem: 'operatingSystem',
	network: 'network',
	uncategorized: 'uncategorized'
};

export const ITProcessType = {
	ManageChange: 1,
	ManageAccessRights: 2,
	ManageSecuritySettings: 3,
	ManageITOperations: 4,
	ManageSystemImplementation: 5,
	Uncategorized: 6
};

export const ITProcessTypes = {
	[ITProcessType.ManageChange]: 'Manage change',
	[ITProcessType.ManageAccessRights]: 'Manage access rights',
	[ITProcessType.ManageSecuritySettings]: 'Manage security settings',
	[ITProcessType.ManageITOperations]: 'Manage IT operations',
	[ITProcessType.ManageSystemImplementation]: 'Manage system implementation',
	[ITProcessType.Uncategorized]: 'Uncategorized'
};
export const IRCRType = {
	InherentRiskType: 'InherentRiskType',
	ControlRiskType: 'ControlRiskType'
};

export const subServiceLines = {
	Audit: 1,
	FIDS: 2,
	ITRA: 3,
	CapitalTransformation: 4,
	TransactionSupport: 5,
	PerformanceImprovement: 6,
	Risk: 7,
	FAAS: 9,
	ACR: 10,
	CCaSS: 11,
	BTS: 12,
	HumanCapital: 13,
	Law: 14,
	IndirectTax: 15,
	GCR: 16,
	TransactionTax: 17,
	ITS: 18,
	GCO: 19,
	BusinessConsulting: 20,
	TechnologyConsulting: 21,
	TransactionsAndCorporateFinance: 22,
	EYParthenon: 23,
	TechnologyRisk: 25
};

export const riskSubTypes = {
	SubTypeAttributeROMM: 9,
	SubTypeStandardROMM: 10,
	SubTypePreRejectedStandardROMM: 11
};

export const itProcessTypes = {
	ITGC: 'ITGC',
	SUPPORTING_TECHNOLOGY: 'SUPPORTING_TECHNOLOGY',
	ITSP: 'ITSP',
	ITAPPLICATIONS: 'ITAPPLICATIONS'
};

// Must remain Pascal case
export const subServiceLineNames = {
	[subServiceLines.Audit]: 'Audit',
	[subServiceLines.FIDS]: 'Fids',
	[subServiceLines.ITRA]: 'Itra',
	[subServiceLines.CapitalTransformation]: 'CapitalTransformation',
	[subServiceLines.TransactionSupport]: 'TransactionSupport',
	[subServiceLines.PerformanceImprovement]: 'PerformanceImprovement',
	[subServiceLines.Risk]: 'Risk',
	[subServiceLines.FAAS]: 'Faas',
	[subServiceLines.ACR]: 'Acr',
	[subServiceLines.CCaSS]: 'Ccass',
	[subServiceLines.BTS]: 'Bts',
	[subServiceLines.HumanCapital]: 'HumanCapital',
	[subServiceLines.Law]: 'Law',
	[subServiceLines.IndirectTax]: 'IndirectTax',
	[subServiceLines.GCR]: 'Gcr',
	[subServiceLines.TransactionTax]: 'TransactionTax',
	[subServiceLines.ITS]: 'Its',
	[subServiceLines.GCO]: 'Gco',
	[subServiceLines.BusinessConsulting]: 'BusinessConsulting',
	[subServiceLines.TechnologyConsulting]: 'TechnologyConsulting',
	[subServiceLines.TransactionsAndCorporateFinance]: 'TransactionAndCorporateFinance',
	[subServiceLines.EYParthenon]: 'EYParthenon',
	[subServiceLines.TechnologyRisk]: 'TechnologyRisk'
};

export const engagementProfileResources = {
	SustainableAuditMethodology: 'SustainableAuditMethodology'
};

export const judgementType = {
	SubstantiveNotSufficient: 1,
	JournalEntry: 2
};

export const estimateType = 3;

export const documentRelatedObjects = {
	Account: 1,
	IT_Process: 2,
	SCOT: 3,
	WCGW: 4,
	Control: 5,
	IT_Application: 6,
	None: 7,
	Engagement: 8,
	Risk: 9,
	ITGC: 10,
	Component: 11,
	Entity: 12,
	Estimate: 13
};

export const rejectionType = {
	itRiskOther: 5,
	itRiskOption2: 6,
	itRiskOption3: 7
};

export const body225ListTypes = {
	Risk: 'risk',
	Scot: 'scot'
};
