/**
 * main.less
*/

html,
body,
div,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video,
button {
	font-family: 'EYInterstate' !important;
	font-size: unset;
	border-radius: unset;
}

ol {
	margin: 0;
	padding: 0;
	li {
		margin: 0;
	}
}

body {
	/* Neon Skin CSS Starts */
	.AresContainer {
		overflow-x: hidden;
		overflow-y: hidden;
	}
	#neon-skin {
		.BodyContainerWrapper {
			.AresViewFirstRowIcons,
			.CommentsValidationWrapper {
				border-color: var(--neutrals-300) !important;
			}
			.SectionTopNameWrapper .CommentsValidationWrapper {
				border-color: var(--neutrals-300) !important;
				&.CommentIconVisible:not(.ValidationIconVisible) {
					.comments-validation-dropdown {
						.StyledSectionCommentCounter {
							border-radius: var(--px-20) !important;
						}
					}
				}
			}
		}
	}
	/* Neon Skin CSS Ends */

	/* Kendo CSS Starts */
	.k-animation-container.k-animation-container-shown {
		z-index: ***********99 !important;
	}
	.k-dialog-wrapper {
		z-index: 9999999999 !important;
		.k-edit-field {
			align-items: center !important;
		}
		.k-actions .k-button-solid-base {
			border-radius: 0 !important;
			background-color: #fff !important;
		}
		.k-actions .k-button-solid-primary {
			border-radius: 0 !important;
			background-color: #2e2e38 !important;
		}
		.k-widget.k-editor {
			.k-widget.k-toolbar {
				&::before {
					content: unset;
				}
				.k-button-group {
					.k-icon-button {
						&.k-button-md {
							padding: var(--px-4) !important;
						}
					}
					.k-input-md .k-input-inner,
					.k-picker-md .k-input-inner {
						padding: var(--px-4) var(--px-16) !important;
					}
					.k-input-md.k-icon-picker .k-input-inner,
					.k-picker-md.k-icon-picker .k-input-inner {
						padding: var(--px-4) !important;
					}
					.comment-button {
						padding: var(--px-6) !important;
						.add-comments {
							height: var(--px-16) !important;
							svg {
								width: var(--px-16) !important;
								height: var(--px-16) !important;
							}
						}
					}
				}
			}
			.k-editor-content {
				.k-content {
					& > p {
						margin: 0 !important;
					}
				}
			}
		}
	}
	.k-window {
		&.k-editor-window {
			z-index: 1000000000002 !important;
		}
	}
	.SectionBodyWrapper .k-table {
		height: auto !important;
		width: 100% !important;
	}
	/* Kendo CSS Ends */

	/* Timetracker CSS Starts */
	.StyledTimetrackerContainer {
		.MainContainerWrapper {
			height: 100% !important;
			.StyledTimeTrackerSection {
				z-index: 99;
			}
		}
	}
	/* Timetracker CSS Ends */

	/* Qualitris CSS Starts */
	.QSISlider {
		top: var(--px-60) !important;
		height: calc(100% - var(--px-60)) !important;
		& > div {
			&:first-child {
				height: 100% !important;
			}
		}
	}
	.QSIFeedbackButton {
		z-index: 96 !important;
		#QSIFeedbackButton-btn {
			bottom: 40% !important;
			width: var(--px-40) !important;
			@media only screen and (min-width: 1280px) and (max-width: 1500px) {
				bottom: 30% !important;
			}
			div {
				font-size: var(--px-14) !important;
				border-radius: 0 var(--px-5) var(--px-5) 0 !important;
				font-weight: 600;
				line-height: var(--px-22);
				color: #2e2e38 !important;
				background: #ffe600 !important;
			}
		}
	}
	#QSIFeedbackButton-target-container {
		z-index: 998 !important;
		top: var(--px-65);
		height: calc(100% - var(--px-120)) !important;
		border: 0 !important;
	}
	/* Qualitris CSS Ends */
}

.react-draggable {
	z-index: 9999 !important;
}

#mount {
	display: flex;
	flex: 1;
	width: 100%;
	.telemetry {
		display: flex;
		flex-direction: column;
		height: 100%;
		width: 100%;
		.main {
			.main {
				height: 100%;
				top: 0;
			}
		}
	}
	.main {
		.telemetry {
			.main {
				height: 100%;
				top: 0;
			}
		}
	}
}

.rootWrapper {
	@media screen {
		height: 99.9vh;
		display: flex;
		flex-flow: column;
		overflow: hidden;
	}
}

.main {
	@media screen {
		overflow: auto;
		display: flex;
		flex: 1;
		position: absolute;
		top: var(--px-56);
		height: calc(100vh - var(--px-56));
		width: 100%;
		margin: 0 auto;
		justify-content: center;
		overflow-x: hidden;
	}
	&.mainPrint {
		top: 0;
		height: 100vh;
		color: #2e2e38;
		-webkit-print-color-adjust: exact !important;
		-ms-print-color-adjust: exact !important;
		margin: 0;
		@media only screen and (min-width: 1260px) and (max-width: 1366px) {
			width: 99.5%;
		}
		* {
			-webkit-print-color-adjust: exact !important;
			-ms-print-color-adjust: exact !important;
		}
		svg {
			width: var(--px-20) !important;
			height: var(--px-20) !important;
			max-width: var(--px-20) !important;
			max-height: var(--px-20) !important;
			&.extra-large {
				width: var(--px-36) !important;
				height: var(--px-36) !important;
				max-width: var(--px-36) !important;
				max-height: var(--px-36) !important;
			}
			&.large {
				width: var(--px-24) !important;
				height: var(--px-24) !important;
				max-width: var(--px-24) !important;
				max-height: var(--px-24) !important;
			}
			&.medium {
				width: var(--px-20) !important;
				height: var(--px-20) !important;
				max-width: var(--px-20) !important;
				max-height: var(--px-20) !important;
			}
			&.small {
				width: var(--px-16) !important;
				height: var(--px-16) !important;
				max-width: var(--px-16) !important;
				max-height: var(--px-16) !important;
			}
			&.disable {
				cursor: default;
				color: var(--neutrals-500);
				fill: var(--neutrals-500);
			}
		}
		.MainContainerWrapper {
			&.left-margin {
				margin-left: 0 !important;
			}
		}
	}
}

.DrawerRootWrapper {
	.main {
		top: 0;
		height: 100vh;
		.StyledTaskViewDetails {
			.BodyContainerWrapper {
				height: 100% !important;
				position: relative !important;
			}
		}
		.StyledNavigationPanelCollapsed + .StyledTaskViewDetails {
			width: 100%;
		}
		.AddCommentsWrappers {
			.EngagementCommentBody {
				.engagementCommentBodyCommentWrapper {
					max-height: var(--px-500) !important;
					@media only screen and (max-width: 1280px) {
						max-height: var(--px-300) !important;
					}
				}
			}
		}
	}
	.NeonDrawerView {
		display: flex;
		justify-content: center;
		.drawer {
			width: 100%;
			.StyledTaskViewDetails {
				max-width: 2048px;
				@media only screen and (min-width: 992px) and (max-width: 1366px) {
					padding-left: var(--px-40);
					&.BodyDetailView {
						padding-left: 0;
					}
				}
			}
			#fit-navigation-panel {
				.StyledFormDetailsPanel {
					&.MestEngagementTrue {
						.form-details-container {
							.StyledHeadersList {
								max-height: calc(100vh - var(--px-322));
							}
						}
					}
					&.MestEngagementFalse {
						.form-details-container {
							.StyledHeadersList {
								max-height: calc(100vh - var(--px-255));
							}
						}
					}
				}
			}
		}
	}
}

.StyledFrameToggle {
	.drawer-opener {
		&.flush-left {
			z-index: 998 !important;
		}
	}
}

.relatedTaskDropdown {
	min-width: var(--px-25);
	height: auto;
	line-height: var(--px-10);
	.relatedTaskSection {
		width: var(--px-25);
		display: flex;
		flex-wrap: wrap;
		font-size: var(--px-10) !important;
		justify-content: center;
		.ellipses {
			margin-top: var(--px-5);
			justify-content: center;
		}
	}
	.taskList {
		.taskListBody {
			.listView {
				.TaskBadges {
					width: var(--px-65) !important;
					font-size: var(--px-12) !important;
					font-weight: 700 !important;
				}
				.TaskNames {
					line-height: var(--px-24) !important;
					max-width: calc(100% - var(--px-110)) !important;
					color: var(--blue-600) !important;
					font-size: var(--px-12) !important;
					font-weight: 700 !important;
					&:hover {
						color: var(--blue-600) !important;
					}
					a {
						color: var(--blue-600) !important;
						&:hover {
							color: var(--blue-600) !important;
						}
					}
				}
			}
			.StyledTaskDrawerSummoner {
				margin-bottom: var(--px-10);
				&:last-child {
					margin-bottom: 0;
				}
				.listView {
					.TaskNames {
						text-align: left;
						&:hover {
							cursor: pointer;
							text-decoration: underline;
						}
					}
				}
			}
		}
	}
	.motif-icon,
	.CloseButton {
		position: unset;
		right: var(--px-8);
		top: var(--px-10);
		svg {
			width: var(--px-18) !important;
			height: var(--px-18) !important;
		}
	}
}

.offlineprep,
.offline {
	border: var(--px-7) solid #109090;
	.rightContainer {
		padding-top: var(--px-30);
	}
}

.training {
	border: var(--px-7) solid #91278f;
}

.connection-available,
.syncstate {
	border: var(--px-7) solid #35a4e8;
}

.container {
	width: 100%;
}

ul {
	margin: 0;
	padding: 0;
	list-style-type: disc;
	margin-block-start: 0;
	margin-block-end: 0;
}

ol {
	margin: 0;
	padding: 0;
	margin-block-start: 0;
	margin-block-end: 0;
}

.hide {
	display: none !important;
}

.carret-right {
	width: 0;
	height: 0;
	margin-right: var(--px-10);
	vertical-align: top;
	margin-top: var(--px-3);
	display: inline-block;
}

.highlight-green {
	font-size: inherit;
	border: var(--px-1) solid #8de8ad;
	background: #8de8ad;
	padding: var(--px-2);
}

.highlight-teal {
	font-size: inherit;
	line-height: inherit !important;
	border-top: 0 !important;
	border-right: var(--px-1) solid #656579 !important;
	border-bottom: 0 !important;
	border-left: var(--px-1) solid #656579 !important;
	background: #93f0e6 !important;
	padding: var(--px-2);
	& + .highlight-teal {
		margin-left: calc(var(--px-1) * -1);
	}
}

.diffins {
	background-color: #e6ffe6;
	font-family: inherit;
	text-decoration: none;
}

.diffdel {
	color: #999;
	background-color: #ffe6e6;
	font-family: inherit;
}

.mod {
	background-color: #ffe1ac;
	font-family: inherit;
}

/* Motif DropdownPortal CSS Starts */
.motif-dropdown-menu-portal {
	z-index: 995 !important;

	&.document-cog-menu {
		z-index: 99999998 !important; //used in Kronos Related Documents Modal Prior Period SignOff. Don't change it.
		width: var(--px-200);
		border-radius: var(--px-4);
		.motif-dropdown-item {
			height: var(--px-45);
			.ellipses {
				margin-left: var(--px-16);
				div {
					font-style: normal;
					font-weight: 300;
					font-size: var(--px-14);
					line-height: var(--px-22);
				}
			}
		}
	}

	&.document-name-cog-menu-popup-container {
		z-index: 99999998 !important; //used in Kronos Related Documents Modal cog menu. Don't change it.
	}

	&.motifSignOffTooltip {
		z-index: 99999998 !important; //used in Kronos Related Documents Modal Prior Period SignOff. Don't change it.
		min-width: var(--px-450) !important;
		&.AresViewMotifSignOffTooltip {
			border-radius: var(--px-8);
			background-color: #fff;
			box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);
			z-index: 999999999 !important;
			.signoff-tooltip-content {
				h4 {
					border-radius: var(--px-8) var(--px-8) 0 0;
					border-bottom: 0;
					height: var(--px-42);
					display: flex;
					align-items: center;
					background-color: #f6f6fa;
					padding: var(--px-12) var(--px-12) var(--px-8) var(--px-12);
					color: #1a1a24;
					font-weight: 600;
					font-size: var(--px-14);
					line-height: var(--px-22);
				}
				.signOffContent {
					max-height: var(--px-160);
					background-color: #fff;
					border-radius: 0 0 var(--px-8) var(--px-8);
					.signOffItem {
						padding: var(--px-4) var(--px-12);
						color: #1a1a24;
						font-weight: 300;
						font-size: var(--px-12);
						line-height: var(--px-20);
						&:first-child {
							padding-top: var(--px-8);
						}
						&:last-child {
							padding-bottom: var(--px-12);
						}
						.user-name {
							width: calc(100% - var(--px-200));
							padding-right: var(--px-24);
						}
						.role-description {
							width: var(--px-100);
							padding-right: var(--px-24);
						}
						.signoff-date {
							width: var(--px-80);
							padding-right: var(--px-12);
						}
						button {
							width: var(--px-20);
							height: var(--px-20);
							padding: 0;
							svg {
								width: var(--px-20);
								height: var(--px-20);
							}
						}
					}
				}
			}
		}
	}

	&.CommentsStyledMotifDropdown {
		border-radius: var(--px-5);
		margin-bottom: var(--px-10);
	}

	.motif-dropdown-item {
		width: 100%;
		min-height: var(--px-40);
	}
}
/* Motif DropdownPortal CSS Ends */

/* Motif Modal CSS Starts */
#modal,
.motif-modal-root {
	.motif-modal-overlay {
		.motif-modal {
			.review-notes-detail-section {
				.StyledToastMessageContainer {
					width: calc(100% - var(--px-64)) !important;
					top: var(--px-52) !important;
					margin-left: var(--px-32);
					z-index: 9999;
				}
			}
		}
		.motif-modal,
		.motif-modal-content {
			position: relative;
			&.KronosModal {
				.motif-modal-body {
					.StyledRelateTaskItemsList {
						.RelateListContainers {
							.relate-list-body-wrapper {
								.RelateItemListItem {
									.relate-item-list-item {
										align-items: flex-start !important;
										.relateListCheckSpan {
											padding: var(--px-10) 0;
										}
										.motif-checkbox-wrapper {
											.motif-checkbox {
												.motif-checkbox-inner {
													margin-top: 0 !important;
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}

			&.CreateEditITProcessModalContainer {
				.CreateEditITProcessModalBody {
					.modal-body-wrapper {
						.modal-description {
							padding: 0 0 var(--px-24) 0;
							border-bottom: 1px solid var(--neutrals-100);
						}
						.line-break {
							margin: 0;
							display: none;
						}
						.it-process-name-input {
							margin-top: var(--px-24);
						}
					}
				}
			}

			&.StyledRelateITProcessITApplicationModal {
				.motif-modal-body {
					.StyledModalBanners {
						.StyledToastMessageContainer {
							top: var(--px-24) !important;
						}
					}
				}
			}
			.motif-modal-body {
				.StyledModalBannersErrors {
					top: 0;
					.StyledToastMessageContainer {
						top: 0 !important;
						margin: 0 !important;
						width: 100% !important;
					}
				}
			}
			.StyledModalBannersErrors {
				.StyledToastMessageContainer {
					top: var(--px-52) !important;
					margin: 0 var(--px-20) !important;
					width: 100%;
				}
			}
			.errorMessageWrapper {
				position: absolute;
				z-index: 2;
				top: var(--px-40);
			}
			.confirm-start-multiuser-editing-modal-body {
				.document-name {
					display: flex;
					align-items: flex-start;
					width: 100%;
					.StyledFileName {
						width: 100%;
						.DocumentName {
							width: 100%;
							.DocumentNameText {
								width: 100%;
								.DocumentName {
									width: 100%;
								}
							}
						}
						.documentName-icons {
							display: none;
						}
					}
				}
			}

			&.DeleteDocumentModalContainer {
				.DeleteDocumentModalBody,
				.motif-modal-body {
					min-height: var(--px-170) !important;
				}
			}

			&.removeEvidenceModalContainer {
				.motif-modal-body {
					min-height: var(--px-100);
				}
			}

			.review-notes-detail-section {
				.addedit-errorwrapper {
					.errorMessageWrapper {
						margin-top: var(--px-16) !important;
						margin-left: var(--px-32) !important;
					}
				}
			}

			&.ActivityModalBody,
			&.DeleteCustomBody {
				min-height: auto !important;
				.MotifProgressBarWrapper {
					position: absolute;
					display: flex;
					justify-content: center;
					width: 100%;
					height: 100%;
					background-color: var(--neutrals-00white);
					top: 0;
					right: 0;
					align-items: center;
					.motif-progress-bar {
						width: var(--px-20);
						height: var(--px-20);
					}
				}
				.motif-modal-footer {
					.motif-button {
						line-height: var(--px-24);
					}
				}
			}

			&.HelixScreenshotViewerModal {
				.StyledModalBodyHelixScreenshotViewerItem {
					height: 100%;
					overflow: hidden;
					.StyledHelixScreenshotViewerItem {
						overflow: hidden;
						.ImageAnnotatorWrapper {
							overflow: hidden;
							display: flex;
							border: var(--px-1) solid var(--neutrals-300);
							border-radius: var(--px-8);
							& > div {
								height: 100% !important;
								width: auto;
								overflow: auto;
								max-height: 100%;
								max-width: 100%;
								display: flex;
							}
						}
					}
				}

				.motif-modal-footer {
					.show-hide-notes {
						margin-left: auto;
						padding: var(--px-20);
					}
				}
			}

			&.ShowEditProfileModalContainer {
				.contentSwitcher {
					border-bottom: 0;
					padding: var(--px-24) 0 0 0;
					display: flex;
					.motif-content-switcher {
						margin-right: var(--px-25);
						margin-bottom: var(--px-24);
						.motif-button {
							height: var(--px-32);
							& + .motif-button {
								margin-left: var(--px-5);
							}
						}
					}
					.motif-checkbox {
						margin: 0 0 0 var(--px-20);
					}
				}
			}

			&.relatedDocumentModalMedium {
				width: 72%;
				max-width: 72%;
				height: calc(100vh - var(--px-40));
				max-height: 100%;
			}

			&.UnsavedChangesModalContent {
				.motif-modal-body {
					&.UnsavedChangesModalBody {
						min-height: var(--px-80) !important;
					}
				}
			}

			&.ConfidentialityModalContainer {
				.StyledConfidentiality {
					min-height: var(--px-400);
				}
			}

			&.delete-comment-modal {
				.motif-modal-body {
					min-height: var(--px-60) !important;
				}
			}

			&.newrisk-modal {
				.motif-modal-body {
					min-height: var(--px-50) !important;
				}
			}

			&.superkronos-modal {
				.motif-modal-body {
					overflow: hidden;
				}
			}

			&.changedesignation-modal {
				.motif-modal-body {
					min-height: auto !important;
				}
			}

			&.reimport-modal {
				.motif-modal-body {
					min-height: auto !important;
				}
			}

			&.add-note-modal {
				.add-note-modal-body {
					.edit-note-wrapper {
						padding: 0 var(--px-25);
					}
					.top-inputs {
						.date-picker-wrapper {
							.motif-form-field {
								height: var(--px-50) !important;
							}
						}
					}
				}
			}

			&.edit-note-modal {
				.edit-note-modal-body {
					.edit-note-wrapper {
						padding: 0 var(--px-25);
					}
					.top-inputs {
						.date-picker-wrapper {
							.motif-form-field {
								height: var(--px-50) !important;
							}
						}
					}
				}
			}

			&.conflictDescriptionModal {
				.conflictDescriptionModalBody {
					padding: var(--px-10) var(--px-20) 0 var(--px-20) !important;
					.conflictDescriptionLabel {
						padding: 0;
						font-weight: 300;
						font-size: var(--px-14);
						line-height: var(--px-22);
					}
					.conflictDescriptionTabWrapper {
						display: flex;
						justify-content: flex-start;
						align-items: center;
						border-bottom: var(--px-1) solid var(--neutrals-300);
						margin-top: var(--px-10);
						.conflictDescriptionTab {
							width: calc(100% - var(--px-300));
							padding: 0;
							border: 0;
						}
						.conflictDescriptioncheckbox {
							margin-left: auto;
							.motif-checkbox-label {
								width: 95% !important;
							}
						}
					}
					.conflictDescriptionTxt {
						font-size: var(--px-14);
						font-weight: 300;
						line-height: var(--px-22);
						padding: var(--px-20) var(--px-10) var(--px-20) 0;
						height: calc(100% - var(--px-100));
						overflow-y: auto;
						word-break: break-all;
						.createRichTextElementSpanWrapper {
							line-height: 1.42;
							ul {
								list-style: disc;
								position: relative;
								left: var(--px-20);
								width: calc(100% - var(--px-20));
							}
							ol {
								padding-left: var(--px-30);
							}
							& > ol {
								padding-left: var(--px-20);
							}
							table {
								width: 100%;
								table-layout: fixed;
								td,
								th {
									border: var(--px-1) solid var(--neutrals-300);
									padding-left: var(--px-10);
									ul {
										li {
											margin-left: var(--px-30);
										}
									}
								}
							}
							span,
							p {
								font-size: inherit;
								line-height: 1.42;
								color: unset;
							}
						}
						span,
						p {
							font-size: inherit;
							line-height: 1.42;
							color: unset;
						}
					}
				}
			}

			&.RenameDocumentModalContainer {
				.RenameDocumentModalBody {
					min-height: var(--px-170) !important;
				}
			}

			&.DeleteDocumentModalContainer {
				.DeleteDocumentModalBody,
				.motif-modal-body {
					min-height: var(--px-170) !important;
				}
			}

			&.RenameDocumentModalContainer {
				.RenameDocumentModalBody {
					min-height: var(--px-170) !important;
				}
			}

			&.EngagementCommentInputModal {
				.motif-modal-body {
					overflow-x: hidden !important;
				}
				.children-body {
					.StyledEngagementCommentsBox {
						.StyledEngagementCommentInputBox {
							.StyledMiddlePane {
								padding: 0;
								.EngagementCommentAddBoxContainer {
									.EngagementCommentsAssignUser {
										padding-top: 0;
										padding-left: 0;
									}
									.EngagementCommentsPriority {
										padding-top: 0 !important;
									}
									.EngagementCommentsDueDate {
										padding-top: 0;
										padding-right: 0;
									}
									.EngagementCommentTextArea {
										padding: var(--px-20) 0;
									}
								}
								.renderVoiceNoteWrapper {
									padding: 0;
									border: 0;
								}
							}
						}
					}
				}
			}

			&.EditResponseModalContent {
				.StyledEditorBody {
					.descriptionWrapper {
						.createRichTextElementSpanWrapper {
							p {
								margin-top: 0;
							}
							ul {
								padding-left: var(--px-25);
								li {
									list-style-type: disc;
									margin-bottom: var(--px-10);
								}
							}
							.k-table {
								width: 100%;
								tr {
									td {
										border: var(--px-1) solid #2e2e38;
									}
								}
							}
						}
						.confirmModalDescGuidanceWapper {
							margin-left: 0 !important;
							padding: var(--px-20) var(--px-10) 0 0 !important;
							border-top: 0 !important;
							display: block !important;
							border: 0;
							width: 100%;
							font-size: var(--px-14);
							.guidanceIcon {
								display: inline-block;
								margin-right: var(--px-3);
								margin-left: var(--px-3);
								vertical-align: top;
								svg {
									max-width: var(--px-24);
									max-height: var(--px-24);
									color: #2e2e38;
									cursor: default;
									width: var(--px-24) !important;
									height: var(--px-24) !important;
								}
							}
							.guidance-container {
								display: inline-block;
								width: calc(100% - var(--px-30));
								margin-top: calc(var(--px-2) * -1);
								.guidance-Wrapper {
									display: inline-flex;
									width: auto;
									overflow: hidden;
									max-width: 100%;
									&:last-child {
										.guidanceLinkSeparator {
											display: none;
										}
									}
									.guidanceLink {
										display: inline-flex;
										padding: 0 var(--px-7);
										cursor: pointer;
										overflow: hidden;
										font-size: var(--px-12);
										font-weight: normal;
										&:hover {
											border-bottom: 0;
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
}
/* Motif Modal CSS Ends */

// AresModal Styling Starts
.motif-modal-root {
	.motif-modal-overlay {
		.AresModal {
			&.motif-modal {
				&.no-padding {
					.motif-modal-body {
						padding: 0 !important;
					}
				}

				&.helix-mappingmodule-modal {
					.helix-mappingmodule-modal-body {
						padding: var(--px-24) var(--px-10) var(--px-24) var(--px-11_5) !important;
					}
				}

				&.deleteEntityModalWrapper {
					.motif-modal-body {
						.modalBodyWrapper {
							min-height: auto;
						}
					}
				}

				&.ManageObjectsModalContainer {
					height: var(--px-730);
				}

				&.EditObjectsModalContainer {
					height: var(--px-730);
					.RelateListContainers {
						.HeaderWrapper {
							.StyledRelateItemsListHeader {
								padding-bottom: var(--px-16) !important;
								.RelateItemsListHeaderComponent {
									width: 100% !important;
									display: flex;
									flex-direction: column;
									margin-right: 0 !important;
									gap: var(--px-16);
									.instructionalText {
										padding-bottom: var(--px-16) !important;
									}
									.RelatedTasksDocument {
										margin-top: 0 !important;
									}
								}
							}
							.relate-list-toolbar {
								margin-top: 0 !important;
							}
						}
						.RelateListColumnHeadersContainer {
							.relate-list-header--generic-entity {
								padding-left: var(--px-10) !important;
							}
						}
						.relate-list-body-wrapper {
							width: 100% !important;
							border-radius: 0 !important;
							.RelateItemListItem {
								.relate-item-list-item {
									.relate-item-list-svg {
										padding-left: var(--px-10) !important;
									}
									.relateListCheckSpan {
										padding-left: var(--px-10) !important;
									}
									.relate-item-list-labelTxt {
										max-width: calc(100% - var(--px-40)) !important;
									}
								}
								.relate-item-list-level1 {
									background-color: var(--neutrals-00white) !important;
									.relate-list-body-wrapper {
										border: 0 !important;
										border-radius: 0 !important;
									}
								}
							}
						}
					}
				}

				&.HelixIntegrationModalContent {
					.HelixIntegrationModalBody {
						padding-right: var(--px-6) !important;
					}
					.HelixIntegrationModalBody {
						.StyledTabContentWrapper {
							.StyledModalFooter {
								height: var(--px-50);
								padding: 0 !important;
							}
						}
					}
				}

				&.associateRiskModal {
					max-height: var(--px-750);
					@media screen and (min-width: 768px) and (max-width: 1280px) {
						max-height: 100%;
					}
				}

				&.documentOpenedModal {
					.document-opened-header {
						padding: 0;
					}
					.document-opnened-docname {
						.DocumentNameWrapper {
							padding: var(--px-10) 0;
						}
					}
					.document-opened-buttons {
						padding: var(--px-24) 0 0 0;
					}
				}

				&.discardChangesModal {
					.confirmModel {
						margin: 0;
						min-height: var(--px-50);
					}
				}

				&.SignoffRequirementsModalContainer {
					.SignoffRequirementsModalBody {
						.StyledSignoffRequirementsModal {
							padding: 0 !important;
							min-height: var(--px-200) !important;
							font-size: var(--px-14) !important;
							.SignoffRequirementsModalText,
							.SignoffRequirementsModalDescriptionText {
								font-size: var(--px-14) !important;
							}
							.DocumentNameContainer {
								.DocumentNameDetails {
									font-size: var(--px-14) !important;
								}
							}
						}
					}
				}

				&.MarkCompleteModalContainer {
					&.MarkCompleteModalContainerLoader {
						position: relative;
						.MarkCompleteModalBody {
							position: static;
							.StyledActivityContent {
								position: static;
								.MotifProgressBarWrapper {
									top: var(--px-56);
									left: 0;
									height: calc(100% - var(--px-56));
								}
							}
						}
					}
				}

				&.shareActivityModalWrapper {
					.motif-modal-body {
						.wizardContainer {
							width: 100%;
							padding-left: 0 !important;
							.componentTabsWrapper {
								padding: 0 var(--px-24);
								.steps {
									font-size: var(--px-14);
									font-weight: 400;
									line-height: var(--px-20);
								}
								.stepdetails {
									.ellipses {
										div {
											font-size: var(--px-14);
											font-weight: 300;
											line-height: var(--px-20);
										}
									}
								}
								.active {
									.steps {
										font-weight: 700;
									}
									.stepdetails {
										.ellipses {
											div {
												font-weight: 700;
											}
										}
									}
								}
							}
							.componentBodyWrapper {
								height: calc(100vh - var(--px-280));
								margin-bottom: var(--px-10);
								padding: 0 var(--px-24);
							}
							.componentBtnWrapper {
								padding: var(--px-24);
							}
						}
					}
				}

				&.sharingActivityModalContainer {
					position: relative;
					overflow: hidden;
					.sharingActivityModalBody {
						overflow-y: auto;
						position: unset;
						.errorMessageWrapper {
							top: var(--px-60);
						}
					}
				}

				&.unlinkModalContainer {
					height: var(--px-310);
					&.unlinkModalContainerLoader {
						position: relative;
						.motif-modal-body {
							.errorMessageWrapper {
								top: var(--px-60);
							}
						}
					}
				}

				&.aresViewRelateDocumentsWrapper {
					.motif-modal-body {
						.RelatedTasksContentBody {
							.HeaderWrapper {
								.relateDocuments__checkbox {
									right: var(--px-30);
								}
							}
							.RelateListWrapper {
								padding-left: 0 !important;
							}
						}
						.StyledRelateTaskItemsList {
							.RelateListContainers {
								.relate-list-body-wrapper {
									.RelateItemListItem {
										.relate-item-list-item {
											align-items: flex-start !important;
										}
										.motif-checkbox-wrapper {
											.motif-checkbox {
												.motif-checkbox-inner {
													margin-top: 0 !important;
												}
											}
										}
										.relate-item-list-labelTxt {
											.DocumentNameWrapper {
												padding: 0 var(--px-10) !important;
											}
										}
									}
								}
							}
						}
					}
				}

				&.ChangeReasonModalContainer {
					width: var(--px-692) !important;
					height: var(--px-689) !important;
					.ChangeReasonModalBody {
						padding-left: 0 !important;
						padding-right: 0 !important;
						.DocumentNameContainer {
							.DocumentNameWrapper {
								padding: var(--px-24) var(--px-24) 0 var(--px-24) !important;
							}
						}
						.ChangeReasonText,
						.ChangeReasonSelect,
						.EditReasonText,
						.EditAnnotation {
							padding: var(--px-10) var(--px-24) !important;
						}
					}
					.motif-modal-body {
						height: var(--px-541) !important;
						.ChangeReasonText {
							font-weight: 300 !important;
							font-size: var(--px-14) !important;
							line-height: var(--px-22) !important;
							border-bottom: var(--px-1) solid #e1e1e6;
							margin: var(--px-20) var(--px-24) 0 var(--px-24) !important;
							padding: 0 0 var(--px-24) 0 !important;
						}
						.EditReasonText {
							padding: 0 var(--px-24) var(--px-24) var(--px-24) !important;
							font-size: var(--px-14) !important;
							font-weight: 300 !important;
							line-height: var(--px-22) !important;
						}
						.ChangeReasonSelect {
							padding: var(--px-24) !important;
							.select-error {
								border: var(--px-1) solid #b9251c;
								.StyledErrorWrapper {
									color: #b9251c;
									.motif-icon {
										svg {
											fill: #b9251c;
										}
									}
								}
								&:hover {
									border: var(--px-1) solid #b9251c !important;
								}
							}
						}
					}
				}

				&.addtoevicence-modal {
					position: relative;
					.motif-modal-body {
						.errorMessageWrapper {
							top: var(--px-65);
						}
						.motif-progress-bar-wrapper {
							height: calc(100% - var(--px-57));
							width: 100%;
							z-index: 2;
						}
						.addToEvidenceHeading {
							font-weight: 300;
							font-size: var(--px-14);
							line-height: var(--px-22);
							color: var(--neutrals-900);
						}
						.DocumentNameWrapper {
							padding: var(--px-24) 0;
							border-bottom: var(--px-1) solid #e1e1e6;
							margin-bottom: var(--px-24);
							.DocumentNameContainer {
								.DocumentSVG {
									margin-right: var(--px-8);
								}
								.DocumentNameDetails {
									padding: 0;
									.DocumentName {
										.DocumentNameText {
											.DocumentName {
												font-weight: 600;
												font-size: var(--px-14);
												line-height: var(--px-22);
												color: #3a3a4a;
												opacity: 1;
											}
										}
									}
								}
							}
						}
						.single-line-text {
							display: flex;
							align-items: flex-end;
							.motif-form-field {
								width: calc(100% - var(--px-80));
								margin-right: var(--px-8);
								.motif-input-component {
									height: 100%;
									.motif-input {
										height: 100%;
										min-height: var(--px-40) !important;
									}
								}
							}
						}
						.text-area {
							display: flex;
							align-items: flex-end;
							.StyledTextAreaEditor {
								width: calc(100% - var(--px-30));
								margin-right: var(--px-8);
								.motif-form-field {
									.motif-input-component {
										min-height: var(--px-52);
										width: 100% !important;
										.motif-text-area {
											height: var(--px-22) !important;
											min-height: var(--px-22) !important;
										}
									}
								}
								.motif-error-message {
									display: flex;
									align-items: center;
									.motif-error-icon {
										svg {
											fill: #b9251c;
										}
									}
								}
							}
						}
						.fileExt {
							font-size: var(--px-14);
							font-weight: 300;
							line-height: var(--px-22);
							margin-bottom: var(--px-9);
							display: flex;
							align-self: flex-start;
							margin-top: var(--px-23_5);
						}
					}
				}

				&.DeleteDocumentModalContainer {
					width: var(--px-692);
				}

				&.AddEditCustomModalContainer {
					margin: var(--px-30);
					height: calc(100% - var(--px-60));
				}

				.motif-modal-body {
					.create-update-modal-body {
						padding: 0 var(--px-10) 0 0 !important;
					}

					.SignoffRequirementsPICCheckbox,
					.SignoffRequirementsEQRCheckbox {
						.motif-checkbox.motif-checkbox-label-wrap {
							align-items: center;
						}
					}

					.deleteNoteHeading,
					.DeleteDocument,
					.add-note-wrapper {
						padding: 0;
					}

					.conflictDescriptioncheckbox {
						.motif-checkbox {
							min-width: var(--px-170);
						}
					}

					&.RelateTasksModalBody {
						.HeaderWrapper {
							.StyledRelateItemsListHeader {
								.RelateItemsListHeaderComponent {
									width: calc(100% - var(--px-250));
								}
								.RelateItemsListHeaderSelectedOnly {
									max-width: var(--px-250);
									margin-top: var(--px-10);
									.motif-checkbox-label {
										width: var(--px-180);
									}
								}
							}
							.relate-list-toolbar {
								.additional-toolbar-controls {
									margin-left: var(--px-24);
									.SelectDropdownContainer {
										.react-select-container {
											width: var(--px-300);
										}
									}
								}
							}
						}
						.taskHeader {
							.tasTypeColumn {
								min-width: var(--px-55);
								font-size: var(--px-14);
								font-weight: 400;
							}
							.tasNameColumn {
								font-size: var(--px-14);
								font-weight: 400;
								width: 100%;
								margin-left: var(--px-15);
							}
							.RelatedTasksCounter {
								font-size: var(--px-14);
								font-weight: 400;
								margin-left: auto;
								width: 10%;
							}
						}
						.relate-list-body-wrapper {
							.RelateItemListItem {
								padding-left: var(--px-10);
								.relate-item-list-item {
									padding: var(--px-10) 0;
									width: calc(100% - var(--px-20));
									.relate-item-list-labelTxt {
										.StyledTaskNames {
											width: calc(100% - var(--px-30));
											max-width: calc(100% - var(--px-30));
											.TaskNames {
												.ellipses {
													a,
													div {
														font-size: var(--px-12);
														font-weight: 300;
														color: inherit;
													}
												}
											}
										}
										.hoveredTaskControls {
											display: inline-flex;
											margin-left: var(--px-10);
											width: var(--px-20);
											.hoveredTaskControlsLink {
												&:hover {
													border-bottom: 0;
												}
												.motif-icon {
													height: var(--px-12);
													svg {
														width: var(--px-12) !important;
														height: var(--px-12) !important;
														margin: 0 var(--px-3) !important;
													}
												}
											}
										}
									}
									.relate-item-list-loading-indicator {
										&.inlineView {
											margin: 0;
											padding: 0;
											width: 25%;
										}
									}
									.relate-item-list-added-labelTxt {
										margin: 0 0 0 auto;
									}
								}
							}
						}
					}

					&.edit-note-modal-body,
					&.add-note-modal-body {
						padding: var(--px-20) var(--px-24) !important;
						.edit-note-wrapper,
						.add-note-wrapper {
							padding: 0;
							.top-inputs {
								margin: 0;
							}
						}
					}

					&.DeleteDocumentModalBody {
						padding: 0 var(--px-24) var(--px-32) var(--px-24) !important;
						min-height: var(--px-145) !important;
						.DeleteDocument {
							.confirm-msg {
								font-size: var(--px-14);
								font-weight: 300;
								line-height: var(--px-22);
								color: #2e2e38;
								padding: var(--px-20) 0 var(--px-24) 0;
								border-bottom: var(--px-1) solid #e1e1e6;
								margin-bottom: var(--px-24);
							}
							.DocumentNameWrapper {
								.DocumentNameContainer {
									.DocumentSVG {
										margin-right: var(--px-8);
									}
									.DocumentNameDetails {
										padding: 0;
										.DocumentNameText {
											.DocumentName {
												font-size: var(--px-14);
												font-weight: 600;
												line-height: var(--px-22);
												color: #3a3a4a;
											}
										}
									}
								}
							}
						}
					}
				}
			}

			&.managerisks-modal {
				.motif-modal-body {
					.header-message {
						padding: 0 0 var(--px-20) 0;
						margin: 0;
					}
					.create-new-risk-container {
						padding-left: 0;
						padding-bottom: 0;
						.motif-icon {
							margin-left: var(--px-5);
						}
					}
					.risks-list-container {
						padding: var(--px-20) var(--px-20) var(--px-20) 0;
						border-bottom: none;
						.StyledRisksItem {
							.StyledTextAreaEditor {
								.text-area-editor {
									.motif-form-field {
										.motif-input-component {
											.motif-text-area {
												min-height: var(--px-200) !important;
											}
										}
									}
								}
							}
							.riskname-input {
								margin-right: var(--px-50);
							}
							.risk-controls {
								width: 5%;
								padding-top: var(--px-34);
								@media only screen and (min-width: 1280px) and (max-width: 1366px) {
									padding-top: var(--px-41);
								}
							}
							.StyledRiskTypesSelect {
								width: 35%;
								margin-top: var(--px-20);
							}
						}
					}
				}
			}

			&.editaccounts-modal {
				.assertions-wrapper .assertions-dropdown {
					.assertions-option .assertion-checkbox {
						top: auto;
						margin-top: calc(var(--px-2) * -1);
					}
					.assertions-option .motif-chip .motif-chip-button,
					.react-select-container
						.react-select__multi-value
						.react-select__multi-value__label
						.motif-chip
						.motif-chip-button {
						border-radius: var(--px-4);
					}
				}
				.accountname-input .motif-input-component {
					.motif-input {
						height: var(--px-54);
					}
				}
				.accimpacted-toggle {
					.motif-toggle-switch {
						width: var(--px-41);
						height: var(--px-22);
					}
					.toggle-label {
						vertical-align: text-top;
					}
				}
			}
		}
	}
}
// AresModal Styling Ends

// #region Entities Modal CSS Starts
.entities-modal,
.CreateEditAccountModalContainer,
.CreateEditControlModalContainer,
.CreateEditEstimateModalContainer,
.CreateEditITApplicationModalContainer,
.CreateEditITControlModalContainer,
.CreateEditITProcessModalContainer,
.CreateEditITRiskModalContainer,
.CreateEditMestModalContainer,
.CreateEditRiskModalContainer,
.CreateEditScotModalContainer,
.CreateEditTaskSectionModalContainer,
.CreateEditWCGWModalContainer {
	max-height: calc(100vh - var(--px-40)) !important;
	min-height: calc(100vh - var(--px-40)) !important;
}

.CreateEditMestModalContainer {
	.CreateEditMestModalBody {
		.StyledModalBody {
			.entity-std-index {
				.entity-standard-index-container {
					width: 100% !important;
				}
			}
		}
	}
}

.CreateEditITApplicationModalBody,
.CreateEditAccountModalBody {
	.modal-error-wrapper {
		.motif-toast {
			position: absolute;
			z-index: 5;
		}
	}
}

&:has(.EntitiesModalBody) {
	.tooltip {
		max-width: 40%;
		margin-left: var(--px-10);
	}
}

/* Voltron Modal CSS Starts */
.modal-backdrop {
	z-index: 999999 !important;
}
.modal-control {
	z-index: *********** !important;
	.errorMessageWrapper {
		margin: 0 var(--px-20) !important;
	}
}
.modal-open {
	.EditResponseModalContent {
		overflow: hidden !important;
		.modal-dialog {
			.modal-header {
				height: auto !important;
			}
			.modal-body {
				display: flex !important;
				flex-direction: column;
				padding: 0 !important;
				overflow-y: auto !important;
				height: calc(100% - var(--px-115)) !important;
			}
		}
	}
	.modal-control.in.modal {
		overflow: hidden;
		&.discardChangesModal,
		&.seeDescModal {
			background: rgba(46, 46, 56, 0.6);
		}
		.modal-dialog {
			.modal-body {
				height: calc(100% - 55px);
				overflow: hidden;
				font-size: var(--px-14);
				padding: var(--px-20) 0 var(--px-20) var(--px-20);
				.confirmModalDesc {
					display: inline-block;
					max-height: var(--px-200);
					width: 100%;
					padding-right: var(--px-20);
					p {
						font-size: var(--px-14);
					}
					ul {
						padding-left: var(--px-25);
						li {
							list-style-type: disc;
						}
					}
					.k-table {
						width: 100%;
						tr {
							td {
								border: var(--px-1) solid #2e2e38;
							}
						}
					}
				}
				.confirmModalDescGuidanceWapper {
					margin-left: 0 !important;
					padding: var(--px-20) var(--px-10) 0 0 !important;
					border-top: 0 !important;
					display: block !important;
					border: 0;
					width: 100%;
					font-size: var(--px-14);
					.guidanceIcon {
						display: inline-block;
						margin-right: var(--px-3);
						margin-left: var(--px-3);
						vertical-align: top;
						svg {
							max-width: var(--px-24);
							max-height: var(--px-24);
							color: #2e2e38;
							cursor: default;
							width: var(--px-24) !important;
							height: var(--px-24) !important;
						}
					}
					.guidance-container {
						display: inline-block;
						width: calc(100% - var(--px-30));
						margin-top: calc(var(--px-2) * -1);
						.guidance-Wrapper {
							display: inline-flex;
							width: auto;
							overflow: hidden;
							max-width: 100%;
							&:last-child {
								.guidanceLinkSeparator {
									display: none;
								}
							}
							.guidanceLink {
								display: inline-flex;
								padding: 0 var(--px-7);
								cursor: pointer;
								overflow: hidden;
								font-size: var(--px-12);
								font-weight: normal;
								&:hover {
									border-bottom: 0;
								}
							}
						}
					}
				}
				&.EngagementCommentInputModalBody {
					padding: 0;
					.StyledEngagementCommentInputBox {
						.StyledMiddlePane {
							.text-area-editor {
								.motif-form-field {
									.motif-input-component {
										.motif-text-area {
											min-height: var(--px-200);
											max-height: var(--px-300);
										}
									}
								}
							}
						}
					}
				}
				&.RiskFactorDescriptionModalBody {
					padding-right: var(--px-20) !important;
				}
			}
		}
	}
}
/* Voltron Modal CSS Ends */

/* Navigation Bar CSS Starts */
.NavigationBar {
	.StyledEYLogoWrapper {
		&.EYLogo {
			white-space: nowrap;

			@media only screen and (min-width: 1921px) {
				margin-left: 0 !important;
			}
		}
	}

	.EYNavigation {
		@media only screen and (min-width: 1921px) {
			margin-right: 0 !important;
		}

		.navAnchor,
		.navChild {
			height: var(--px-60);
		}

		.DefaultMenuContainer {
			height: var(--px-60);

			.Notification {
				.notification-main-wrap {
					max-height: var(--px-24) !important;

					.motif-icon {
						svg {
							width: var(--px-24) !important;
						}
					}
				}
			}
		}

		.MoreMenuContainer {
			.motif-menu-overlay {
				pointer-events: none;
				z-index: 9999999999 !important;

				&.motif-menu-overlay-open {
					.motif-menu {
						display: block;
					}
				}

				.motif-menu {
					display: none;
				}

				.motif-menu-nav {
					.motif-menu-list {
						.MotifMenuListWrapper {
							.motif-menu-list-item {
								background-color: transparent;

								a {
									background-color: transparent;

									&.motif-text-link {
										&:hover {
											border-bottom: 0 !important;
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	.EYEngagementDefault {
		max-height: var(--px-60);
		font-size: var(--px-14) !important;
	}
}
/* Navigation Bar CSS Ends */

/* CustomScrollbar CSS Starts */
.customScrollbar {
	overflow-y: auto;
	&::-webkit-scrollbar {
		height: 6px !important;
		width: 6px !important;
		border: none !important;
	}
	&::-webkit-scrollbar-track {
		background: #e7e7ea !important;
		height: 6px !important;
		width: 6px !important;
		border: none !important;
	}
	&::-webkit-scrollbar-thumb {
		background: var(--neutrals-300) !important;
		height: 6px !important;
		width: 6px !important;
		cursor: pointer !important;
		border: none !important;
	}
	&::-webkit-scrollbar-thumb:hover {
		background: #747480 !important;
		border: none !important;
	}
}
::-webkit-scrollbar {
	width: 6px !important;
	height: 6px !important;
	border: none !important;
}
::-webkit-scrollbar-track {
	background: #e7e7ea !important;
	width: 6px !important;
	border: none !important;
}
::-webkit-scrollbar-thumb {
	background: var(--neutrals-300) !important;
	width: 6px !important;
	cursor: pointer !important;
	border: none !important;
}
::-webkit-scrollbar-thumb:hover {
	background: #747480 !important;
	border: none !important;
}
/* CustomScrollbar CSS Ends */

/* Rich Text CSS Starts */
.richText-Body {
	span[style*='font-size: xx-small;'] * {
		font-size: var(--px-10) !important;
	}
	span[style*='font-size: x-small;'] * {
		font-size: var(--px-10) !important;
	}
	span[style*='font-size: small;'] * {
		font-size: var(--px-13) !important;
	}
	span[style*='font-size: medium;'] * {
		font-size: var(--px-16) !important;
	}
	span[style*='font-size: large;'] * {
		font-size: var(--px-18) !important;
	}
	span[style*='font-size: x-large;'] * {
		font-size: var(--px-24) !important;
	}
	span[style*='font-size: xx-large;'] * {
		font-size: var(--px-32) !important;
	}
}
/* Rich Text CSS Ends */

/* Motif Tooltip CSS Starts */
.validationTooltip {
	.motif-tooltip-inner {
		width: var(--px-250);
		.validationDropdownWrapper {
			display: block;
			width: 100%;
			padding: 0 var(--px-8);
			.validationDropdownContainer {
				display: flex;
				padding: var(--px-5) 0;
				&:hover,
				&:focus {
					background: transparent;
					border: 0;
				}
				.cursorPointer {
					cursor: pointer;
					&:hover {
						text-decoration: underline;
					}
				}
			}
			.validationDropdownHeading {
				display: flex;
				font-size: var(--px-12);
				font-weight: bold;
				padding: var(--px-10) 0 0;
				width: 100%;
			}
			.validationDropdownContainer {
				padding: var(--px-5) 0;
				width: 100%;
				min-height: var(--px-20);
				height: var(--px-30);
				.dropdownValidationName {
					display: inline-flex;
					width: calc(100% - var(--px-30));
					font-size: var(--px-12);
					margin-right: 0;
					padding-right: var(--px-5);
					padding-left: var(--px-2);
				}
				.dropdownValidationCount {
					display: inline-flex;
					width: var(--px-30);
					font-size: var(--px-12);
					justify-content: flex-end;
					color: #b9251c;
					.ellipses {
						justify-content: right;
					}
				}
			}
			.dropdownValidationName {
				display: inline-flex;
				width: calc(100% - var(--px-50));
				font-size: var(--px-12);
				margin-right: var(--px-10);
				padding-left: var(--px-2);
			}
			.dropdownValidationCount {
				display: inline-flex;
				width: var(--px-40);
				font-size: var(--px-12);
				justify-content: flex-end;
				color: #b9251c;
			}
		}
	}
}

.contentUpdateTooltip {
	width: var(--px-300);
	&::-webkit-scrollbar {
		width: 6px;
		height: 6px;
	}
	&::-webkit-scrollbar-track {
		background: #e7e7ea;
		width: 6px;
		height: 6px;
	}
	&::-webkit-scrollbar-thumb {
		background: var(--neutrals-300);
		width: 6px;
		height: 6px;
		cursor: pointer;
	}
	&::-webkit-scrollbar-thumb:hover {
		background: #747480;
	}
	.relatedCanvasFormContainer {
		max-height: var(--px-150);
		padding: 0 var(--px-10);
		width: 100%;
		display: inline-grid;
	}
	.relatedCanvasForm {
		width: 100%;
		padding: var(--px-5) var(--px-10) !important;
	}
	.relatedCanvasFormHeading {
		padding-bottom: var(--px-5) !important;
		border-bottom: var(--px-1) solid var(--neutrals-300);
		color: #000;
		width: 100%;
		display: inline-flex;
	}
	.inputMotifIcon {
		width: var(--px-18);
		height: var(--px-18);
		margin-left: auto;
	}
	.relatedCanvasFormText {
		padding-bottom: var(--px-5) !important;
		color: var(--blue-600);
		cursor: pointer;
		width: 100%;
		display: inline-block;
		&:hover {
			text-decoration: underline;
		}
	}
}

.contentTooltip {
	.motif-tooltip-inner {
		width: var(--px-270);
		.relatedCanvasForm {
			padding: 0 var(--px-10) !important;
			width: 100%;
		}
		.relatedCanvasFormHeading {
			padding-bottom: var(--px-5) !important;
			border-bottom: var(--px-1) solid var(--neutrals-300);
		}
		.relatedCanvasFormText {
			padding-bottom: var(--px-5) !important;
			color: var(--blue-600);
			cursor: pointer;
			&:hover {
				text-decoration: underline;
			}
		}
	}
}

.shareActivityDocumentTooltip {
	.motif-tooltip {
		width: var(--px-500);
	}
}

.insufficientRole-tooltip {
	.motif-tooltip {
		.motif-tooltip-wrapper {
			width: var(--px-316);
		}
	}
}

.import-project-tooltip,
.delete-project-tooltip {
	.motif-tooltip-content-children {
		width: var(--px-200);
	}
}

.horizontalRadioEllipsisClass {
	max-width: var(--px-400);
}

.assetsChartLabelTooltip {
	.motif-tooltip {
		.motif-tooltip-wrapper {
			width: var(--px-50);
			height: var(--px-30);
		}
	}
	.assetsChartLabelWrapper {
		display: flex;
		.ellipses {
			margin-right: var(--px-5);
		}
	}
}

.documentsTooltip {
	.motif-tooltip {
		width: var(--px-380);
		.motif-tooltip-content-children {
			width: 100%;
		}
	}
}

.StyledDisplayDocumentSignoffRequirementsTooltip,
.addToEvidenceTooltip,
.DocumentSharedTooltip,
.DocumentUnlinkTooltip {
	.motif-tooltip {
		z-index: 999999999;
	}
	z-index: 9999 !important;
}

.noInherentRiskTooltip,
.assertion-column-header,
.task-name-tooltip {
	.motif-tooltip {
		width: var(--px-180);
	}
}

.task-name-tooltip {
	.motif-tooltip {
		width: auto;
		max-width: var(--px-450);
	}
}

.documentConfidentialTooltip {
	position: absolute;
	top: 0;
	z-index: 9999999999;
}

.notification-tooltip-wrap {
	.motif-tooltip-inner {
		width: var(--px-500);
	}
	.motif-tooltip {
		height: calc(100vh - var(--px-100));
		z-index: 999;
	}
	.motif-tooltip-wrapper,
	.motif-tooltip-inner,
	.motif-tooltip-content-children {
		height: 100%;
	}
	.motif-tooltip-content-children {
		width: 100%;
		margin: 0 !important;
	}
	.all-types-filter-dropdown,
	.eng-filter-dropdown {
		padding: 0;
		height: var(--px-240);
		overflow: auto;
		ul.parent {
			cursor: default;
			padding: 0;
			margin: 0;
			&:focus {
				outline: none;
			}
			&:hover {
				background: none;
			}
		}
	}
	.all-types-filter-dropdown {
		width: var(--px-190);
	}
	.eng-filter-dropdown {
		width: var(--px-260);
	}
}

.notification-tooltip-placeholder {
	.motif-tooltip {
		width: var(--px-200);
		left: 0 !important;
	}
	.motif-tooltip-inner {
		.motif-tooltip-bottom {
			z-index: ***********999;
		}
	}
}

.assertionToolTip {
	.motif-tooltip {
		width: var(--px-250);
		.motif-tooltip-wrapper {
			margin-top: 0 !important;
		}
		.motif-tooltip-content-children {
			margin: 0;
			width: 100%;
		}
		.motif-tooltip-inner {
			.risk-Type-Header {
				padding: 0 var(--px-10) var(--px-10) 0;
				margin: 0;
				font-size: var(--px-14);
				font-weight: bold;
				border-bottom: var(--px-1) solid var(--neutrals-300);
				border-radius: var(--px-4) var(--px-4) 0 0;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			ul.assertionsList {
				font-size: var(--px-14);
				border-radius: 0 0 var(--px-4) var(--px-4) !important;
				li {
					font-weight: bold;
					padding: var(--px-8) 0;
					display: flex;
					.risk-Type-Label {
						width: auto;
						max-width: var(--px-80);
						display: flex;
						margin-bottom: var(--px-5);
						.risk-Type-Label-inner {
							max-width: 95%;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
							display: inline-block;
						}
					}
					.risk-Type-Value {
						margin-bottom: var(--px-5);
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
						display: inline-block;
						width: var(--px-120);
					}
					&:last-child {
						margin-bottom: 0;
					}
					&.emptyContainer {
						padding: var(--px-8) 0;
					}
				}
			}
		}
	}

	.motif-tooltip-right {
		width: var(--px-250);
		.motif-tooltip-inner {
			ul.assertionsList {
				font-size: var(--px-14);
				li {
					font-weight: bold;
					padding: var(--px-8) 0;
				}
			}
		}
	}
}

.OARFooterTooltip {
	box-shadow: var(--px-2) var(--px-2) var(--px-7) 0 rgba(0, 0, 0, 0.75) !important;
	.motif-tooltip {
		.motif-tooltip-wrapper {
			.motif-tooltip-inner {
				width: var(--px-450);
				max-height: var(--px-160);
				overflow-y: auto;
				.motif-tooltip-content-children {
					width: 85%;
					.BusinessUnitLabel {
						width: 100%;
						padding-bottom: var(--px-6);
						&:last-child {
							padding-bottom: 0;
						}
					}
				}
			}
		}
	}
}

.HelixV2Tooltip,
.OARHelixTooltip {
	.motif-tooltip {
		.motif-tooltip-wrapper {
			.motif-tooltip-inner {
				width: var(--px-220);
				.motif-text-alt-button {
					font-size: var(--px-12);
					position: relative;
					display: inline-block;
					top: 0;
					right: 0;
				}
			}
		}
	}
}

.PrioPeridCategoryTooltip {
	inset: 0 auto auto var(--px-10) !important;
	.motif-tooltip {
		.motif-tooltip-wrapper {
			.motif-tooltip-inner {
				.motif-text-alt-button {
					font-size: var(--px-12);
					position: relative;
					display: inline-block;
					top: 0;
					right: 0;
				}
			}
		}
	}
}

.risktypebadge-tooltip .risktypebadge-tooltipcontent,
.priorperiod-tooltip .motif-tooltip-content-children {
	h3 {
		margin-top: 0;
		margin-bottom: var(--px-10);
		width: var(--px-230);
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
}

.risktypebadge-tooltip {
	.motif-tooltip {
		.motif-tooltip-wrapper {
			.motif-tooltip-inner {
				.motif-tooltip-content-children {
					max-height: var(--px-200);
					overflow-y: auto;
					margin: 0;
					&::-webkit-scrollbar {
						width: 6px;
						height: 6px;
					}
					&::-webkit-scrollbar-track {
						background: #e7e7ea;
						width: 6px;
						height: 6px;
					}
					&::-webkit-scrollbar-thumb {
						background: var(--neutrals-300);
						width: 6px;
						height: 6px;
						cursor: pointer;
					}
					&::-webkit-scrollbar-thumb:hover {
						background: #747480;
					}
					.risktypebadge-tooltipcontent {
						margin-bottom: var(--px-20);
						padding-right: var(--px-24);
						list-style: none;
						width: var(--px-500);
						margin-left: 0;
						h3 {
							font-weight: 600;
							font-size: var(--px-12);
							line-height: var(--px-16);
							width: calc(100% - var(--px-20));
						}
						ul {
							h3 {
								font-weight: 600;
								font-size: var(--px-12);
								line-height: var(--px-16);
								width: calc(100% - var(--px-20));
							}
							li {
								font-weight: 300;
								font-size: var(--px-12);
								line-height: var(--px-20);
								margin-bottom: var(--px-10);
							}
						}
					}
				}
			}
		}
	}
}
.priorperiod-tooltip .motif-tooltip-content-children {
	ul {
		li {
			list-style: none;
			margin-bottom: var(--px-5);
			width: var(--px-200);
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}
	span {
		display: block;
		margin-bottom: var(--px-5);
	}
}
.priorprdchanges-tooltip {
	section:first-child:not(:last-child) {
		margin-bottom: var(--px-8);
	}
}
.proj-info-tooltip {
	.motif-tooltip-content-children {
		width: var(--px-230);
	}
}
.StyledGuidanceTypeEightTooltip {
	.motif-tooltip {
		width: var(--px-350);
		.motif-tooltip-wrapper {
			.motif-tooltip-inner {
				.motif-tooltip-content-children {
					margin-right: 0;
					width: 100%;
					h3 {
						margin: 0 0 var(--px-20) 0;
						color: var(--neutrals-900);
						font-size: var(--px-18);
						font-weight: 500;
					}
					.guidance-tooltip-inner {
						max-height: var(--px-250);
						padding-right: var(--px-10);
						.StyledGuidanceItem {
							font-size: var(--px-12);
							font-weight: 400;
							line-height: var(--px-20);
							ol,
							ul {
								margin: 0;
								li {
									color: var(--neutrals-900);
								}
							}
							p {
								margin-top: 0;
								line-height: inherit;
							}
							.ellipses {
								.createRichTextElementSpanWrapper {
									div {
										margin-right: 0 !important;
									}
								}
							}
						}
					}
				}
			}
		}
	}
}
.RelatedAccountWrapperTooltip,
.RiskTypeWrapperTooltip {
	.motif-tooltip {
		width: var(--px-400);
		.motif-tooltip-wrapper {
			.motif-tooltip-inner {
				.motif-tooltip-content-children {
					width: 100%;
					.risktypebadge-tooltipcontent {
						h3 {
							margin-top: 0;
							padding: 0;
							font-size: var(--px-12);
							font-weight: 400;
							line-height: var(--px-16);
						}
						ul {
							list-style: none;
							max-height: var(--px-150);
							padding: 0 var(--px-10) 0 0;
							&.RiskTypeUL {
								li {
									margin-left: var(--px-16);
									section {
										&:first-child {
											width: 100%;
										}
									}
								}
							}
							li {
								margin-bottom: var(--px-10);
								display: flex;
								align-items: center;
								font-size: var(--px-12);
								font-weight: 300;
								line-height: var(--px-20);
								&:last-child {
									margin-bottom: 0;
								}
								.AccountNames {
									display: inline-flex;
									width: calc(100% - var(--px-180));
									section {
										&:first-child {
											width: 100%;
										}
									}
								}
								.AssertionsBadgesWrapper {
									width: var(--px-180);
									display: inline-flex;
									justify-content: flex-start;
									padding-left: var(--px-10);
									position: relative;
									.AssertionBadge {
										.craBadgesWrapper {
											min-width: unset;
											.motif-tooltip-wrapper {
												.motif-tooltip-trigger-wrap {
													.assertion {
														border-radius: var(--px-4);
														border-color: #747480;
														font-size: var(--px-11);
														font-weight: 600;
														line-height: var(--px-16);
														color: #747480;
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
}

.ga-tooltip {
	--bg-color: #fff;
	--tooltip--bg-color: var(--bg-color);
	--tooltip--bg--color: var(--bg-color);
}

.doble-modal-tooltip {
	z-index: 1000000000;
}

.compare-tooltip {
	display: flex;
	&.dark {
		.motif-tooltip {
			.motif-tooltip-inner {
				.tootip-content {
					.header {
						color: #ffffff;
					}
					.documents {
						.link-item {
							.ellipses {
								color: #ffe600;
							}
						}
					}
				}
			}
		}
	}

	.motif-tooltip {
		width: var(--px-250);
		right: var(--px-10) !important;
		.motif-tooltip-inner {
			.motif-tooltip-content-children {
				display: flex;
				width: 100%;
			}
			.tootip-content {
				width: 100%;
				.header {
					color: #2e2e38;
					margin-bottom: var(--px-16);
					width: 85%;
				}
				.documents {
					display: flex;
					flex-direction: column;
					max-height: var(--px-140);
					overflow-y: auto;
					width: 100%;
					.link-item {
						display: flex;
						width: 95%;
						margin-bottom: var(--px-14);
						cursor: pointer;
						&.last {
							margin-bottom: 0;
						}
					}
				}
			}
		}
	}
}

.sectionNameEllipses {
	.tooltip-inner {
		max-width: var(--px-800) !important;
	}
}

.add-to-evidence-tooltip {
	display: flex;
}

.SectionListItemTooltip {
	width: var(--px-300);
	margin-left: calc(var(--px-40) * -1);
	.motif-tooltip-inner {
		width: 100%;
		.motif-tooltip-content-children {
			width: 100%;
		}
	}
}
/* Motif Tooltip CSS Ends */

.DocumentNameContainer {
	.DocumentNameDetails {
		.DocumentName {
			display: inline-block;
			.last-modified,
			.coedit-mode-complete {
				display: flex;
				width: 100%;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				margin-right: var(--px-10);
				max-width: var(--px-500);
				.modifiedBy {
					max-width: 90%;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}
			.currently-modified {
				max-width: 95%;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
			}
			.coedit-mode-complete {
				display: flex;
				max-width: var(--px-500);
				.coedit-mode-complete-label {
					margin-right: var(--px-3);
					max-width: 90%;
					display: inline-block;
				}
			}
			.modifiedName {
				text-overflow: ellipsis;
				overflow: hidden;
				margin-right: var(--px-3);
			}
		}
	}
	.DocumentSVG {
		svg {
			font-size: var(--px-12) !important;
		}
	}
}

.no-note-selected {
	display: flex;
	width: 100%;
	height: 100%;
	justify-content: center;
	align-items: center;
	.empty-note-details-text {
		margin: var(--px-10);
	}
}

.specialBodyAccordionHeaderOverflowDropDown {
	z-index: 10 !important;
}

.teamevent-tab {
	.risk-name + ul.teamevent-riskfactors {
		margin-bottom: 0;
	}
	ul.noteamevent-riskfactors {
		margin: 0;
	}
	.notrom-risklist ul {
		border-top: var(--px-1) solid #e1e1e6;
		margin: 0;
		padding: var(--px-15);
	}
}

.neon-dropdownportal.assignedto-dropdownportal {
	.motif-button {
		height: auto !important;
		line-height: unset !important;
	}

	.manage-assignment-header {
		background: #fff;
		border-bottom: var(--px-1) solid #e1e1e6;
		padding: var(--px-18) var(--px-24) var(--px-12) var(--px-24);
		border-radius: var(--px-12) var(--px-12) 0 0;

		.manageAssignmentTimePhaseWrapper .manageAssignmentTimePhase .manageAssignmentTimePhaseText {
			font-weight: 300;
		}

		.manageAssignmentTimePhaseWrapper .manageAssignmentTimePhase .manageAssignmentTimePhaseValue {
			font-weight: 400;
		}
	}

	.manageAssignmentList {
		@media only screen and (min-width: 1280px) and (max-width: 1366px) {
			max-height: var(--px-130);
		}
		.assignmentListWrapper {
			border: none;
			.assignmentHeading,
			.teamMemberHeading {
				border: none;
			}
			.assignmentHeading,
			.teamMemberHeading,
			.dueHeading {
				padding: var(--px-12) var(--px-20) var(--px-12) var(--px-24);
			}
		}
		.assignmentListDataWrapper {
			border-bottom: var(--px-1) solid #e1e1e6;
			.assignmentTextWrapper,
			.teamMemberText {
				border: none;
			}
			.assignmentTextWrapper,
			.teamMemberText,
			.dueText {
				padding: var(--px-26) var(--px-20) var(--px-12) var(--px-24);
			}
			.assignment-status-btn {
				padding-left: 0;
				padding-right: 0;
				margin-top: calc(var(--px-3) * -1);
			}
			.assignmentTextWrapper .reviewerAssignmentWrapper .reviewerAssignment {
				font-weight: 700;
			}
			.assignmentTextWrapper .reviewerAssignmentWrapper .assignmentNameText {
				font-weight: 400;
				margin-top: var(--px-5);
			}
			.teamMemberText {
				align-items: baseline;
				margin-top: var(--px-5);
			}
		}
		.assignmentListDataWrapper:last-child {
			border: none;
		}
	}

	.manage-assignment-footer {
		background: #fafafc;
		border: none;
		padding: var(--px-20) var(--px-24);
		border-radius: 0 0 var(--px-12) var(--px-12);
	}
}

@-webkit-keyframes modal-slidein-animation {
	from {
		-webkit-transform: translateX(-100%);
		transform: translateX(-100%);
	}
	to {
		-webkit-transform: translateX(1%);
		transform: translateX(1%);
	}
}

@keyframes modal-slidein-animation {
	from {
		-webkit-transform: translateX(-100%);
		transform: translateX(-100%);
	}
	to {
		-webkit-transform: translateX(1%);
		transform: translateX(1%);
	}
}

.motif-modal-slidein {
	width: 0;
	position: absolute;
	transform: translateX(1%);
	left: 0;
	max-height: 100%;
	height: 100%;
	-webkit-animation-name: modal-slidein-animation;
	animation-name: modal-slidein-animation;
	-webkit-animation-duration: 0.25s;
	animation-duration: 0.25s;
}

.StyledTaskViewContainer {
	position: relative;
	.background-wrap1,
	.background-wrap2 {
		z-index: 1;
	}
}

.relatedDocumentsItemWrapperContainer {
	.relatedDocuments__row {
		.relatedDocumentDocName {
			.DocumentNameWrapper {
				.DocumentNameContainer {
					.DocumentNameDetails {
						.input-container {
							font-size: var(--px-12);
							color: var(--neutrals-900);
							width: 50%;
							& + .note-error {
								font-size: var(--px-12);
								color: var(--neutrals-900);
								display: flex;
								margin-left: auto;
								align-items: center;
								svg {
									color: var(--red-600);
									fill: var(--red-600);
									path {
										fill: var(--red-600);
									}
								}
								.error-text {
									margin-left: var(--px-5);
									color: var(--red-600);
								}
							}
							.file-name-input {
								width: 85%;
							}
						}
					}
				}
			}
		}
	}
}

//Dark Theme CSS - Starts
body[motif-theme='dark'] {
	.StyledFrameToggle {
		.drawer-opener {
			width: var(--px-40) !important;
			background: #3a3a4a;

			.open-drawer {
				background: #3a3a4a;
				svg {
					fill: #fff;
				}
				&:hover {
					background: #f9f9fa;
					svg {
						color: #2e2e38;
						fill: #2e2e38;
					}
				}
			}
		}
	}

	// CustomScrollbar CSS Starts
	.customScrollbar {
		&::-webkit-scrollbar-track {
			background: #23232f !important;
		}
		&::-webkit-scrollbar-thumb {
			background: #3a3a4a !important;
		}
		&::-webkit-scrollbar-thumb:hover {
			background: #747480 !important;
		}
	}
	// CustomScrollbar CSS Ends

	.neon-dropdownportal.assignedto-dropdownportal {
		.manage-assignment-header {
			background: #2e2e3c;
			border-bottom: var(--px-1) solid #3a3a4a;
		}
		.assignmentListDataWrapper {
			border-bottom: var(--px-1) solid #3a3a4a;
		}
		.assignmentListWrapper,
		.manage-assignment-footer {
			background: #23232f;
		}
	}

	#neon-skin {
		.BodyContainerWrapper {
			.AresViewFirstRowIcons,
			.CommentsValidationWrapper {
				border-color: #3a3a4a !important;
			}
		}
		.TasksTabsNavContainer {
			background-color: #23232f !important;
		}
		.StyledTaskViewTabContent {
			border-color: #1a1a24;
			box-shadow: rgb(0 0 0) 0 var(--px-5) var(--px-10) 0;
			.StyledFormHeaderGroupItem {
				&.active-header {
					color: #fff;
				}
				.ellipsis {
					a {
						&:hover {
							color: #ffe600;
						}
					}
				}
			}
		}
	}

	.DocumentNameContainer {
		&.name-link {
			.documentName-icons {
				.signoff-requirement-badge {
					color: #fff !important;
					border-color: #fff !important;
				}
			}
		}
		.DocumentNameDetails {
			.DocumentName {
				color: #fff !important;
			}
		}
	}

	.DeleteDocumentModalBody {
		.DeleteDocument {
			.confirm-msg {
				color: #fff !important;
			}
		}
	}

	.diffins {
		background-color: #9aff9a;
		color: #2e2e38;
	}

	.diffdel {
		background-color: #ffb3b3;
		color: #2e2e38;
	}

	.mod {
		background-color: #ffc560;
		color: #2e2e38;
	}

	.highlight-green {
		border-color: #168736 !important;
		/*green600*/
		background: #168736 !important;
		/*green600*/
	}

	.highlight-teal {
		border-right: var(--px-1) solid #a4a3b1 !important;
		border-left: var(--px-1) solid #a4a3b1 !important;
		background: #004f4f !important;
		/*teal700*/
	}

	.EditAdcModalContainer {
		width: 67% !important;
		height: calc(100vh - var(--px-150)) !important;
	}

	.motif-dropdown-menu-portal {
		&.motifSignOffTooltip {
			&.AresViewMotifSignOffTooltip {
				z-index: 999999999 !important;

				.signoff-tooltip-content {
					h4 {
						background-color: #3a3a4a;
						color: #fff;
					}

					.signOffContent {
						background-color: #23232f;

						.signOffItem {
							color: #fff;
						}
					}
				}
			}
		}
	}

	.showHiddenValidationsWrapper {
		box-shadow: 0 0 var(--px-6) 0 rgba(0, 0, 0, 0.3) !important;
	}

	.confirmModalDescGuidanceWapper {
		.guidanceIcon {
			svg {
				color: #fff !important;
			}
		}
	}

	.header-container {
		background-color: #2e2e38 !important;
	}

	// .DraggableHeaderContainer {
	// 	background-color: #1a1a24 !important;
	// 	border-bottom: var(--px-1) solid #747480 !important;
	// 	color: #fff !important;

	// 	svg {
	// 		path {
	// 			fill: #fff !important;
	// 		}
	// 	}
	// }
	// .DraggableGuidance {
	// 	.DraggableHeaderContainer {
	// 		background-color: #ffe600 !important;
	// 		border-bottom: var(--px-1) solid #ffe600 !important;
	// 		color: #2e2e38 !important;

	// 		svg {
	// 			path {
	// 				fill: #2e2e38 !important;
	// 			}
	// 		}
	// 	}
	// }
	.EngagementCommentsViewBox {
		background-color: #23232f;

		.EngagementCommentHeader {
			color: #fff;
		}

		svg.motif-select-arrow {
			fill: #fff !important;
		}
	}

	.StyledEngagementCommentGroup {
		background-color: #23232f !important;

		.parent-comment {
			background-color: #23232f !important;
		}

		.parent-comment-reply-buttonWrapper,
		.parent-comment-reply-buttonWrapper .parent-comment-reply-button,
		.parent-comment-reply-buttonWrapper .parent-comment-unselect-button {
			background-color: #23232f !important;
		}
	}

	.edit-response-body {
		background-color: #23232f;
		color: #fff;

		.edit-response-wrapper .BottomSection .KendoEditorWrapper table.k-widget.k-editor.k-header.k-editor-widget {
			background-color: #2e2e38 !important;

			.k-editor-toolbar .k-tool {
				color: #fff !important;
			}
		}
	}

	.modal-control.in.modal {
		.modal-dialog {
			.modal-wrapper {
				background: #23232f;
			}

			.modal-body {
				background-color: #23232f;
				color: #fff;

				.confirmModalDesc {
					.k-table {
						tr {
							td {
								border: var(--px-1) solid #fff;
							}
						}
					}
				}
			}
		}
	}
}
//Dark Theme CSS - Ends

// Print CSS - Starts
@media print {
	@page {
		size: Letter;
		margin: 0;
		width: 100%;
		max-width: 100% !important;
	}

	* {
		-webkit-print-color-adjust: exact !important;
		-ms-print-color-adjust: exact !important;
	}

	html,
	body {
		height: initial !important;
		width: 100% !important;
		max-width: 100% !important;
		overflow: initial !important;
		-webkit-print-color-adjust: exact;
		background: transparent !important;
	}

	.page-break {
		margin-top: 10px;
		display: block;
		page-break-before: auto;
	}

	svg.xx-small {
		font-size: 10px;
	}

	svg.x-small {
		font-size: 10px;
	}

	svg.small {
		font-size: 13px;
	}

	svg.medium {
		font-size: 16px;
	}

	svg.large {
		font-size: 18px;
	}

	svg.x-large {
		font-size: 24px;
	}

	svg.xx-large {
		font-size: 32px;
	}

	.rootWrapper {
		height: inherit !important;

		.main {
			height: inherit !important;
			position: unset !important;
			top: unset !important;

			.MainContainerWrapper {
				padding: 10px !important;

				.StyledFormPrintWrapper {
					width: 99% !important;
					color: #2e2e38 !important;

					* {
						-webkit-print-color-adjust: exact !important;
						-ms-print-color-adjust: exact !important;
					}
				}
			}
		}
	}

	.StyledISA315ItAppScotBodyItAppItem {
		.StyledBodySpecialAccordionHeader {
			page-break-inside: avoid !important;
		}

		.body-special-accordion-content {
			page-break-inside: avoid !important;
		}
	}

	.StyledOARTable,
	.StyledSpecialBodyContainer {
		zoom: 85%;
	}
}
// Print CSS - Ends

/* Ellipses Tooltip CSS Starts */
.tooltip {
	position: absolute;
	display: block;
	font-style: normal;
	font-weight: 400;
	line-height: var(--px-20);
	line-break: auto;
	text-align: start;
	text-decoration: none;
	text-shadow: none;
	text-transform: none;
	letter-spacing: normal;
	word-break: normal;
	word-spacing: normal;
	word-wrap: normal;
	white-space: normal;
	filter: alpha(opacity=0);
	z-index: *********** !important;

	&.tooltipCustomWidth {
		width: auto;
		max-width: 20%;
	}

	.tooltip-arrow {
		position: absolute;
		width: 0;
		height: 0;
		border-color: transparent;
		border-style: solid;
	}

	&.fade {
		transition: opacity 0.15s linear;
	}

	&.in {
		filter: alpha(opacity=100);
		opacity: 1;

		&.top {
			.tooltip-arrow {
				border-top-color: var(--neutrals-00white) !important;
			}
		}

		&.bottom {
			.tooltip-arrow {
				border-bottom-color: var(--neutrals-00white) !important;
			}
		}
	}

	&.top {
		padding: var(--px-10) 0;
		margin-top: calc(var(--px-3) * -1);

		.tooltip-arrow {
			bottom: 0;
			left: 50%;
			border-width: var(--px-10) var(--px-10) 0;
			border-top-color: var(--neutrals-00white) !important;
			box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.3);
		}
	}

	&.bottom {
		padding: var(--px-10) 0;
		margin-top: var(--px-3);

		.tooltip-arrow {
			top: 0;
			left: 50%;
			margin-left: calc(var(--px-5) * -1);
			border-width: 0 var(--px-10) var(--px-10);
			border-bottom-color: var(--neutrals-00white) !important;
			box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.3);
		}
	}

	.tooltip-inner {
		padding: var(--px-6);
		font-size: var(--px-14);
		color: var(--neutrals-1000) !important;
		background-color: var(--neutrals-00white) !important;
		box-shadow: 0 0 var(--px-20) 0 rgba(0, 0, 0, 0.3);
		text-align: left;
		word-wrap: break-word;
		border-radius: var(--px-4);
		max-width: var(--px-700);
	}
}
/* Ellipses Tooltip CSS Ends */

// CSS for Modal Header Close Button when Disabled - Starts
.motif-modal-header {
	.motif-modal--header-icon-button {
		&:disabled {
			svg {
				path {
					fill: var(--neutrals-500) !important;
				}
			}
		}
	}
}
// CSS for Modal Header Close Button when Disabled - Ends

// CSS for Spacing Variables - Starts
[motif-theme] {
	--spacing-xx-small: 0.14286rem; // 2px
	--spacing-x-small: 0.28572rem; // 4px
	--spacing-small: 0.57143rem; // 8px
	--spacing-medium-small: 0.71429rem; // 10px
	--spacing-medium: 0.85715rem; // 12px
	--spacing-medium-large: 1rem; // 14px
	--spacing-large: 1.14286rem; // 16px
	--spacing-x-large: 1.71429rem; // 24px
	--spacing-xx-large: 2.28572rem; // 32px
	--spacing-xxx-large: 2.85715rem; // 40px
}
// CSS for Spacing Variables - Ends

.guidance-name {
	.tooltip-inner {
		margin-left: 25rem;
		margin-right: 25rem;
		padding: 1rem !important;
		border-radius: 0.5rem !important;
	}
}

.motif-dropdown-menu {
	.StyledCogDropdownMenuContent {
		height: auto;
		max-height: var(--px-266);
		overflow-y: auto;
	}
}

[motif-theme] .motif-checkbox-checked .motif-checkbox-inner .motif-checkbox-icon {
	color: var(--neutrals-00white);
}

[motif-theme] .motif-checkbox-disabled .motif-checkbox-input[checked]:not([checked='false']) + .motif-checkbox-inner {
	background-color: var(--ui-stroke-disabled);
	border-color: var(--ui-stroke-disabled);
}

[motif-theme]
	.motif-checkbox-disabled
	.motif-checkbox-input[checked]:not([checked='false'])
	+ .motif-checkbox-inner
	.motif-checkbox-icon {
	color: var(--neutrals-00white);
}

[motif-theme] .motif-checkbox-disabled .motif-checkbox-input + .motif-checkbox-inner {
	background-color: var(--neutrals-00white);
	border-color: var(--ui-stroke-disabled);
}

[motif-theme] .motif-checkbox-disabled .motif-checkbox-input[checked]:not([checked='false']) + .motif-checkbox-inner {
	background-color: var(--ui-stroke-disabled);
	border-color: var(--ui-stroke-disabled);
}

/* ql-editor CSS - Starts */
.ql-editor {
	padding: var(--spacing-medium-small) var(--spacing-medium-large) !important;
	outline: none !important;
	p {
		line-height: inherit;
		span {
			line-height: inherit;
		}
	}
	li[data-list='bullet'] > .ql-ui:before {
		font-family: auto !important;
	}
	table {
		td {
			border: var(--px-1) solid var(--neutrals-900);
		}
	}
	strong {
		font-weight: 700;
	}
	h1 {
		font-size: var(--px-34) !important;
		line-height: var(--px-42) !important;
	}
	h2 {
		font-size: var(--px-28) !important;
		line-height: var(--px-36) !important;
	}
	h3 {
		font-size: var(--px-24) !important;
		line-height: var(--px-32) !important;
	}
	h4 {
		font-size: var(--px-20) !important;
		line-height: var(--px-30) !important;
	}
	h5 {
		font-size: var(--px-18) !important;
		line-height: var(--px-28) !important;
	}
	h6 {
		font-size: var(--px-16) !important;
		line-height: var(--px-24) !important;
	}
}
/* ql-editor CSS - Ends */

.DocumentNameWrapper {
	.StyledFileName {
		.DocumentName {
			.coedit-mode {
				.coedit-mode-label {
					max-width: var(--px-450) !important;
				}
			}
		}
	}
}

/* Dropdown using UsePortal - Starts */
.motif-dropdown-menu {
	.dropdownRelatedContainer {
		height: var(--px-160);
		overflow-x: hidden;
		padding: 0 var(--px-10);
	}
	.dropdownRelatedWapper {
		padding: var(--px-5) 0 0;
		border-bottom: var(--px-1) solid var(--neutrals-700);
		.dropdownRelatedText {
			color: var(--neutrals-900);
			font-size: var(--px-12);
			width: calc(100% - var(--px-20));
			display: inline-block;
			font-weight: bold;
		}
		.motif-icon {
			svg {
				width: var(--px-13) !important;
				height: var(--px-13) !important;
				vertical-align: middle;
			}
		}
	}
	.motif-dropdown-item {
		.motif-text-link {
			max-width: 100%;
			font-weight: normal;
			.dropdownValidationName {
				display: block;
				width: 100%;
			}
		}
	}
	.relatedCanvasFormContainer {
		max-height: var(--px-150);
		overflow-y: auto;
		padding: 0;
		width: var(--px-300);
		display: inline-grid;
	}
	.relatedCanvasForm {
		width: 100%;
		padding: var(--px-5) var(--px-10) !important;
	}
	.relatedCanvasFormHeading {
		padding-bottom: var(--px-5) !important;
		border-bottom: var(--px-1) solid var(--neutrals-300);
		color: var(--neutrals-900);
		width: 100%;
		display: inline-flex;
	}
	.inputMotifIcon {
		width: var(--px-18);
		height: var(--px-18);
		margin-left: auto;
	}
	.relatedCanvasFormText {
		padding-bottom: var(--px-5) !important;
		color: var(--blue-600);
		cursor: pointer;
		width: 100%;
		display: inline-block;
		&:hover {
			text-decoration: underline;
		}
	}
}
/* Dropdown using UsePortal - Ends */

mgt-person {
	--person-line1-text-color: var(--neutrals-700);
}

.scotnametooltip {
	margin-left: calc(var(--px-108) * -1);
	margin-top: var(--px-18) !important;
}

/* StyledMotifNav CSS - Starts */
.StyledMotifNav {
	.motif-header {
		.motif-header-container {
			.motif-header-right {
				.NavAIChatbotSection {
					&:hover {
						background-color: var(--purple-50) !important;
					}
					svg {
						color: var(--purple-800) !important;
						path {
							fill: currentColor !important;
						}
					}
				}
			}
		}
	}
}
/* StyledMotifNav CSS - Ends */

/* Motif Textarea CSS - Starts */
/* Hide the character counter in Motif Textarea to use our custom counter and this will be removed in future */
.motif-form-field {
	.motif-textarea-support-wrapper {
		.motif-textarea-character-counter {
			display: none !important;
		}
	}
}
/* Motif Textarea CSS - Ends */

.scotsummaryTooltip {
	.motif-tooltip {
		.motif-tooltip-wrapper {
			.motif-tooltip-inner {
				width: var(--px-345);
				.wrappertooltip {
					display: flex;
					gap: var(--px-8);
					svg {
						max-width: var(--px-20);
						& > path {
							color: var(--orange-500);
						}
					}
				}
				.motif-text-alt-button {
					font-size: var(--px-12);
					position: relative;
					display: inline-block;
					top: 0;
					right: 0;
				}
			}
		}
	}
}

/*related account tooltip body 224*/
.relatedaccounttooltip {
	.motif-tooltip-inner {
		max-width: var(--px-630);
		.motif-tooltip-content-children{
			width: 100%;
			margin: 0;
		}
	}

	.title {
		color: var(--neutrals-900);
		line-height: var(--px-35)!important;
	}

	ul{
		width: 100%;
		display: block;
		max-height: var(--px-135);
		overflow: auto;
		padding-right: var(--px-14);
	}

	ul li {
		width: 100%;
		list-style-type: none;
		margin: 0;
		padding: var(--spacing-x-small) 0;
		cursor: pointer;
	}
}
.ProgressBarContainer {
	.motif-progress-bar-wrapper {
		&.motif-progress-bar-wrapper-circle {
			width: 100% !important;
			height: 100% !important;
			.motif-progress-bar {
				&.motif-progress-bar-circle {
					width: var(--px-30) !important;
					height: var(--px-30) !important;
				}
			}
		}
	}
}
// Rationale Ellipsis CSS - Starts
.rationale-ellipsis {
	.tooltip-inner {
		max-width: var(--px-1200) !important;
	}
}
// Rationale Ellipsis CSS - Ends
