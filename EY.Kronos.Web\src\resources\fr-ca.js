/* eslint-disable prettier/prettier,max-len */

import {
	Entity,
	GALinkStatus,
	confidentialityTypes,
	currencyType,
	gaRegion,
	gaRoleTypes,
	gaScopeType,
	notesFilter,
	pointOfContactTypes,
	validationTypes,
	KnowledgeSectionIds,
	sendInstructionsSwitcherIds,
	accountsFilter,
	scotsFilter,
	rejectionType
} from '../util/uiconstants';

/**
 * Created by calhosh on 4/14/2017.
 * FR CA resource file
 */
export const labels = {
	addEvidenceBtn: 'Ajouter un élément probant',
	multipleDocuments: 'Documents multiples',
	invalidEngagementId: 'L’identifiant de la mission est non valide. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	newComponent: 'nouvelle composante',
	workingOffline: 'Travailler hors ligne',
	syncInProgress: 'Synchronisation en cours',
	prepareOffline: 'Préparation des données pour le travail hors ligne',
	connectionAvailable: 'Connexion disponible',
	training: 'Formation',
	clickForOptions: 'Cliquez ici pour plus d’options',
	navReviewNotes: 'Notes de revue',
	navHeaderManageTeam: 'Gérer l’équipe',
	navManageGroup: 'Gérer le groupe',
	manageObjects: 'Gérer les objets',
	navCRASummary: 'Synthèse de l’EGR',
	navAuditPlan: 'Plan de mission',
	navWorkPlan: 'Plan de travail',
	navSEM: 'Matrice d’évaluation des procédures de corroboration',
	navFindings: 'Constatations',
	navContentUpdates: 'Mise à jour du contenu',
	navCanvasFormUpdates: 'Mises à jour des formulaires Canvas',
	navCopyHub: 'Centre de copie',
	navCopyHubNew: 'Centre de copie – NOUVEAU',
	navArchiveChecklist: 'Liste de contrôle de l’archivage',
	navExportHub: 'Centre d’exportation',
	navReporting: 'Rapport',
	navHelp: 'Aide générale',
	validationNavHelp: 'Aide relative à la validation',
	leaveUsFeedback: 'Faites-nous part de vos commentaires',
	navDashboard: 'Tableau de bord',
	tasksPage: 'Tâches',
	documentsPage: 'Documents',
	collaborationPage: 'Collaboration',
	automationPage: 'Automatisation',
	documentHelperConnectionIssue: 'Un problème concernant EY Canvas Document Helper a été détecté. Cliquez sur <a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">ici</a> pour obtenir des instructions sur la façon de régler ce problème.',
	noContentAvailable: 'Aucun contenu disponible',
	noSectionsAvailable: 'Aucune section disponible',
	noInformationAvailable: 'Aucune information disponible',
	collapse: 'Réduire',
	expand: 'Développer',
	duplicate: 'Dupliquer',
	duplicateSection: 'Dupliquer la section',
	duplicateSectionHeader: 'Voulez-vous vraiment dupliquer la section sélectionnée?',
	deleteSection: 'Supprimer la section',
	deleteSectionHeader: 'Voulez-vous vraiment supprimer la section sélectionnée?',
	deleteHeader: 'Supprimer l’en-tête',
	deleteHeaderTitle: 'Voulez-vous vraiment supprimer l’en-tête sélectionné?',
	confirmLabel: 'Confirmer',
	custom: 'Personnalisé',
	selectHeader: 'Sélectionner l’en-tête',
	selectSection: 'Sélectionner la section',
	noResultsFound: 'Aucun résultat',
	scot: 'Catégorie importante d’opérations',
	scotTypes: 'Type de catégorie importante d’opérations',
	frequency: 'Fréquence',
	SelectFrequency: 'Sélectionner la fréquence',
	SelectControlType: 'Sélectionner le type de contrôle',
	itBadge: 'TI',
	soBadge: 'SS',
	noRecordsAvailable: 'Aucun enregistrement n’est disponible',
	noIncompleteResponseSummaryView: 'Aucune réponse incomplète',
	noUnresolvedCommentsSummaryView: 'Aucun commentaire non résolu',
	edit: 'Modifier',
	editForm: 'Modifier le formulaire',
	editControl: 'Modifier le contrôle',
	delete: 'Supprimer',
	remove: 'Retirer',
	noBodies: 'Aucun corps de texte disponible',
	relateDocuments: 'Associer les documents',
	relatedDocuments: 'Documents associés',
	deleteBody: 'Supprimer le corps de texte',
	bodyDescription: 'Voulez-vous vraiment supprimer le corps de texte sélectionné?',
	description: 'Description',
	maxLengthForEditResponse: 'Le corps du texte dépasse la longueur maximale permise',
	maxLengthForEditResponseWithCount: 'La réponse contient {#} caractères, ce qui dépasse la longueur maximale de {##} caractères. Veuillez réduire le texte ou ajuster le formatage puis réessayer. Si l’erreur persiste, veuillez communiquer avec le Service de dépannage.',
	saveResponse: 'Si vous annulez les modifications, aucune des modifications apportées dans le texte de la réponse ne sera enregistrée. Les commentaires fermés ou supprimés demeureront en surbrillance. Veuillez confirmer que vous souhaitez ignorer toutes les modifications.',
	discardChangesModalText: 'Ignorer les modifications',
	seeBodyDescriptionText: 'Voir la description',
	hideBodyDescriptionText: 'Masquer la description',
	showBodyDescriptionText: 'Afficher la description',
	okLabel: 'OK',
	addButtonLabel: 'Ajouter',
	addEvidence: 'Ajouter des éléments probants',
	addTemporaryFiles: 'Ajouter des fichiers temporaires',
	notemporaryDocs: 'Aucun fichier temporaire disponible',
	relateFiles: 'Associer des fichiers',
	uploadFiles: 'Télécharger des fichiers',
	cancelButtonLabel: 'Annuler',
	clickEditResponseToContinue: 'Cliquer sur l’icône de modification pour continuer',
	editResponse: 'Modifier la réponse',
	save: 'Enregistrer',
	numericValuePlaceholder: 'Entrer le montant',
	saveLabel: 'Enregistrer',
	cancelLabel: 'Annuler',
	closeLabel: 'Fermer',
	editText: 'Modifier',
	select: 'Sélectionner',
	selectScot: 'Sélectionner les catégories d’opérations importantes',
	clearHoverText: 'Effacer',
	optional: '(facultatif)',
	nodocumentsAdded: 'Aucun document disponible',
	errorBanner: '{0} erreur(s)',
	NetworkErrorMessage: 'Une erreur s’est produite dans le réseau d’application. Actualisez la page et réessayez plus tard. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	of: 'sur',
	characters: 'caractères',
	show: 'Afficher : ',
	views: "Mode d'affichage : ",
	primaryRelated: 'Principaux formulaires Canvas associés',
	secondaryRelated: 'Formulaires Canvas secondaires associés',
	singleLineValuePlaceholder: 'Entrer le texte',
	paceInputPlaceholder: 'Identifiant PACE',
	multiLineValuePlaceholder: 'Entrer le texte',
	riskFactorDescribe: 'Décrire',
	riskFactorLabel: 'Situation et événement pertinents / risque d’anomalies',
	riskFactorEmptyWarning: 'Description manquante pour l’événement et la situation / le risque d’anomalies',
	riskFactorNoDescription: 'Créer ou sélectionner une situation et un événement pertinents / un risque d’anomalies',
	fraudRiskTagMessage: 'Cette situation et cet événement pertinents / ce risque d’anomalies donne(nt) toujours lieu à un risque de fraude ou, s’il y a lieu, doit(doivent) être désigné(s) comme ne représentant pas un risque d’anomalies significatives.',
	significantRiskTagMessage: 'Cette situation et cet événement pertinents / ce risque d’anomalies donne(nt) toujours lieu à un risque de fraude ou, s’il y a lieu, doit(doivent) être désigné(s) comme ne représentant pas un risque d’anomalies significatives.',
	on: 'le',
	sliderDeSelectMessage: 'Faire glisser le cercle pour indiquer une valeur ',
	yearPlaceholder: 'AAAA',
	dayPlaceholder: 'JJ',
	monthPlaceholder: 'MM',
	amLabel: 'AM',
	pmLabel: 'PM',
	relatedEntities: 'Entités associées',
	eyServiceGateway: 'EY Service Gateway',
	eyAutomation: 'EY Automation',
	eyserviceGatewayAutomation: 'EY Service Gateway & Automation',
	creating: 'Creating...',
	cannotCreateUdp: 'Cannot create UDP. Time Phase cannot be empty',

	// 440GL
	rrdReminderTitle: 'Sommaire de revue et d’approbation ‒ Rappel',
	rrdReminderMessage: 'La date de délivrance du rapport est {rrdDescription}. N’oubliez pas de signer le Sommaire de revue et d’approbation {rrdPendingDays}.',
	rrdReminderPendingDays: 'd’ici {0} jour(s)',

	//Create or Associate risks
	createAssociateRisksLabel: 'Créer un nouveau risque ou associer des risques existants',
	relatedRiskIsMissingWarning: 'Risque associé manquant',
	associateRiskDescription: 'Sélectionner un ou plusieurs risques ou créer un nouveau risque, puis les associer à la réponse à la question.',
	createNewRiskLabel: 'Créer un nouveau risque',
	noRiskIdentifiedLabel: 'Aucun risque identifié',

	// GuidanceModal
	eyAtlasLink: 'EY Atlas',
	guidanceHeaderMessage: 'Ce module contient',
	guidanceModalHeader: 'Indications',
	guidanceModalLabelText: 'Entrée',
	guidanceFooter: 'pour plus de renseignements.',
	guidanceSeveralEntriesText: 'plusieurs entrées : ',
	guidanceVisitText: 'Visiter',
	guidanceClickText: 'Cliquer',
	guidanceHereText: 'ici',
	guidanceFooterText: 'pour plus de renseignements.',
	analyticsInconsistencies: 'Lorsque vous passez en revue les analyses, vérifiez s’il y a des changements ou des activités qui ne correspondent pas à nos attentes. Ces situations peuvent indiquer de nouveaux risques, des catégories d’opérations distinctes, des changements aux catégories d’opérations importantes ou des risques de contournement des contrôles par la direction.',
	analyticsInconsistenciesOSJE: 'Le rapport Autre partie de l’EJ montre les écritures en partie double pour le compte sélectionné faisant l’objet de l’analyse. Nous recherchons les nouvelles combinaisons de comptes ou les combinaisons inhabituelles.',
	analyticsInconsistenciesActivityBySource: 'Le rapport Mouvements par source montre les mouvements mensuels bruts et les sources connexes pour le compte sélectionné. Nous nous concentrons sur les mouvements inhabituels ou les changements dans les sources ou le volume des mouvements pour une source.',
	analyticsInconsistenciesPreparerAnalysis: 'Le rapport Analyse par préparateur résume les mouvements bruts d’une période à l’autre des écritures enregistrées par les préparateurs pour le compte sélectionné. Nous nous concentrons sur les changements liés aux préparateurs ou sur les écritures enregistrées dans des comptes qui ne relèvent pas de leur rôle.',
	analyticsInconsistenciesAccountMetrics: 'Le rapport Mesures pour le compte donne les principales informations sur le compte, ce qui facilite la désignation du compte.',
	analyticsLoadingIsInProgress: 'L’analyse demandée est encore en cours de chargement. L’onglet sera accessible une fois le chargement terminé.',

	aboutDescriptioin: 'Modifier les volets de la MMA, les normes ou la langue. Les modifications apportées déclencheront une mise à jour de contenu.',
	aboutContentDescription: 'Edit the content layers, standards, language, or content driving entity. Edits will trigger a content update.',
	about: 'À propos',
	formType: 'Type de formulaire',
	gamLayer: 'Volets de la MMA',
	contentLayer: 'Volets du contenu',
	standard: 'Norme',
	language: 'Langue',
	createdBy: 'Créé par',
	createdOn: 'Créé le',
	contentLastUpdatedBy: 'Dernière mise à jour du contenu par',
	contentLastUpdatedOn: 'Dernière mise à jour effectuée le',
	notModified: 'Non modifié',

	rolesInsufficientTooltip: 'Pour modifier le contenu, vous devez avoir un rôle plus élevé. Vous pouvez obtenir les droits requis auprès de l’administrateur de la mission.',
	knowledgeFormToolTip: "Knowledge delivered documents cannot be updated. Update the Engagement Profile to change this form's profile.",
	selectTeamMember: 'Nom ou adresse courriel',

	// SeeMore component
	showMore: 'Afficher plus',
	showLess: 'Afficher moins',
	showMoreEllipsis: 'Afficher plus…',
	showLessEllipsis: 'Afficher moins…',

	relatedITapplicationSOs: 'Applications informatiques / Sociétés de services connexes',
	aggregateITevaluations: 'Évaluations globales des TI',
	lowerRisk: 'Risque faible',
	controlLowerRisk: 'Le risque lié au contrôle est faible',
	relatedITApplication: 'Applications informatiques associées',
	relatedITSO: 'Sociétés de services associées',
	noITApplicationUsed: 'Aucune application informatique utilisée',

	notSel: 'NON SÉLECTIONNÉE',
	internal: 'Interne',
	external: 'Externe',
	notSelected: 'Non sélectionné',
	noOptionSelected: 'Non sélectionné',
	tod: 'TDD',
	sap: 'PAC',
	int: 'INT',
	ext: 'EXT',
	toc: 'TDC',

	placeholderForSearch: 'Rechercher',
	source: 'Source',
	nature: 'Nature',
	testOfDetail: 'Test de détail',
	testOfControl: 'Test de contrôle',
	substantiveAnalyticalProcedure: 'Procédure analytique de corroboration',
	expandScot: '1. Développer la catégorie importante d’opérations',
	selectWCGWToDisplayTheResponsiveTask: '2. Sélectionner le risque d’anomalies significatives pour afficher les tâches qui y répondent',
	tasksWithNoRelatedWCGW: 'Tâches sans risque d’anomalies significatives associé',
	noTasksAvailable: 'Aucune tâche disponible',
	noWCGWAvailableForTask: 'Aucun risque d’anomalie significative disponible',
	noSubstantiveTasksAvailable: 'Aucune tâche Corroboration associée',
	selectAssertionToRelateWCGW: 'Sélectionner une assertion pour associer un risque à une erreur possible',
	significantAccounts: 'Comptes importants',
	riskName: 'Risque : ',
	accountName: 'Compte : ',
	control: 'Contrôle',
	controls: 'Contrôles',
	noScotsFound: 'Aucune catégorie importante d’opérations associée',
	relatedwcgw: 'Erreurs possibles associées',
	relatedRisks: 'Risques associés : ',
	boltIconTitle: 'Risque associé',
	relatedITApp: 'TI/SS associée',
	instructions: 'Instructions : ',
	expandRisk: '1. Développer le risque',
	selectAssertion: '2. Sélectionner une assertion',
	identifyRelatedWCGW: '3. Identifier l’erreur possible associée au risque',
	clickAccount: '1. Cliquer sur un compte',
	selectWCGW: '2. Sélectionner une erreur possible',
	identifyRelatedTask: '3. Identifier les tâches qui répondent à l’erreur possible',
	information: 'Information',
	requiredAssertions: 'Assertions requises',
	wcgwWithoutTasks: 'Erreur possible sans tâches',
	rommAssociatedWNotRelyAssertion: 'Le risque d’anomalies significatives est associé à une assertion avec « Pas d’appui » comme niveau de risque lié au contrôle, ou bien à une assertion avec un risque inhérent plus élevé lorsqu’une estimation est associée à ce compte.',
	hasRiskAssociated: 'Risque associé',
	clearSelections: 'Tout désélectionner',
	romm: 'Risque d’anomalies significatives',
	riskOfMaterialMisstatementsWithoutRelatedTask: 'Aucune tâche n’est associée à un risque d’anomalies significatives.',
	selectOneOrMoreTasksToSeeTheRelatedROMM: 'Sélectionner une ou plusieurs tâches pour afficher les risques d’anomalies significatives correspondants',
	invalidRelatedEntity: 'Compte associé introuvable. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	noResultsAvailable: 'Aucun résultat trouvé',
	riskOfMaterialMisstatement: 'Risque d’anomalies significatives',
	AccountConclusion: 'Conclusion sur le compte',
	CanvasForm: 'Formulaire Canvas',
	IndependenceForm: 'Formulaire sur l’indépendance',
	Profile: 'Profil',
	AccountDetails: 'Détails',
	Conclusions: 'Conclusions',
	accountDetailsTab: 'détails',
	conclusionsTab: 'conclusions',
	formsNoContentText: 'Aucun contenu disponible',
	formsDocumentNoRelatedObjects: 'Aucun objet associé au document',
	formsNoRelatedObjects: 'Aucun objet associé',
	formsBodyHeaderControl: 'Contrôles',
	formsBodyDesignEffectiveness: 'Efficacité de la conception',
	formsScotsAndWcgws: 'Catégories importantes d’opérations et erreurs possibles',
	wcgWAndRelatedControls: 'Erreurs possibles et contrôles associés',
	controlAndRelatedItSO: 'Contrôles et applications informatiques/sociétés de services associées',
	type: 'Type',
	designEffectiveness: 'Efficacité de la conception',
	approch: 'Approche',
	controlOpertaingEffectiveness: 'Efficacité du fonctionnement',
	iTAppSO: 'TI/SS',
	iTProcess: 'Processus informatique',
	iTControl: 'Contrôle informatique',
	iTRisk: 'Risques liés aux TI',
	aggregateITEvaluation: 'Évaluation globale des TI',
	relatedCanvasForm: 'Formulaire Canvas correspondant',
	relatedSections: 'Sections correspondantes',
	validations: 'Validations',
	profileV2Validation: 'Modifications non envoyées',
	profileV2ValidationModalDescription: 'Des modifications qui pourraient entraîner une mise à jour du contenu ont été apportées, mais n’ont pas été envoyées. Si les modifications sont volontaires, fermez cette fenêtre et envoyez les nouvelles réponses aux questions sur le profil. Si les modifications ne sont pas volontaires, passez en mode Afficher les changements et rétablissez manuellement les réponses précédemment sélectionnées.',
	profileV2ValidationCount: '1',
	itProcessWithoutRelatedTechnology: 'IT process without related technology',
	reviewNote: 'Notes de revue',
	editAssociations: 'Modifier les éléments associés',
	editAssociationsLower: 'modifier les éléments associés',
	riskWCGW: 'Relation risque-erreur possible',
	wcgwTask: 'Relation erreur possible-tâche',
	noAssertionFound: 'Aucune assertion n’a été associée. Cliquer {here} pour associer des assertions',
	limitedRiskAccountIdentifier: 'Compte présentant un risque limité',
	insignificantAccountIdentifier: 'Compte négligeable',
	noWCGWFound: 'Aucune erreur possible n’est associée à ce risque. Cliquez sur Modifier les éléments associés pour associer une ou plusieurs erreurs possibles.',
	noRelatedWCGWs: 'Aucune erreur possible associée',
	noWCGWAvailable: 'Aucun risque d’anomalie significative n’est disponible pour la ou les assertions sélectionnées.',
	expandCollapse: 'Cliquez ici pour développer/réduire',
	requiredAssertionsInfo: 'Afficher uniquement les assertions avec un risque lié au contrôle de niveau « Pas d’appui » et les assertions liées à des estimations présentant un risque élevé.',
	wCGWwithoutTasksInfo: 'Afficher uniquement les erreurs possibles liées à une assertion avec un risque lié au contrôle de niveau « Pas d’appui » et à des assertions liées à des estimations présentant un risque élevé, pour lesquelles n’a été associée aucune procédure de corroboration.',
	noBuildStepsAvailable: 'Aucune étape de production à présenter',
	risk: 'Risques',
	wcgw: 'ERREUR POSSIBLE',
	riskWcgw: 'Risque-erreur possible',
	wcgwTasks: 'Erreur possible-tâche',
	riskWcgwLabel: 'Associer le risque à l’erreur possible',
	wcgwTasksLabel: 'Associer l’erreur possible à la tâche',
	noRiskTypes: 'Aucun type de risque relevé',
	saveRisk: 'Enregistrer',
	noRisksFound: 'Aucun risque trouvé',
	haveBeenIdentified: 'a été identifié',
	noAccountsFound: 'Aucun enregistrement trouvé',
	noResponseAvailable: 'Aucune réponse disponible',
	noDocumentsAvailable: ' Aucun document disponible',
	noValue: 'Aucune valeur',
	showValidation: 'Validations',
	noAccountsIdentified: 'Aucun compte n’a été identifié.',
	noAssertionsIdentified: 'Aucune assertion n’a été identifiée.',
	noWcgwsIdentified: 'Aucune erreur possible n’a été identifiée.',
	pastingImagesNotAllowed: 'Il n’est pas permis de coller des images. Vous devez télécharger les images requises en tant qu’éléments probants et indiquer un renvoi.',
	incompleteResponse: 'Réponses incomplètes',
	unresolvedComments: 'Commentaires non résolus',
	inconsistentForms: 'Formulaires incomplets',
	limitedRiskAccount: 'Compte présentant un risque limité',
	inherentRiskAssessment: 'Évaluation du risque inhérent',
	task: 'Tâche',
	selected: 'sélectionnée',
	displaytoc: 'Afficher le TDC',
	workingoffline: 'Travailler hors ligne',
	syncinprogress: 'Synchronisation en cours',
	prepareoffline: 'Préparation des données pour le travail hors ligne',
	connectionavilable: 'Connexion disponible',
	softwareUpdate: 'Mises à jour logicielles',
	updateLater: 'Mettre à jour plus tard',
	updateNow: 'Mettre à jour maintenant',
	updateMsg: 'Des mises à jour logicielles sont disponibles pour EY Canvas. Sélectionnez Mettre à jour maintenant pour télécharger et installer les mises à jour. La page sera actualisée.',
	searchPlaceholder: 'Rechercher',
	filter: 'Filtre',
	leftNavSearchPlaceholder: 'Rechercher des en-têtes et des sections',
	back: 'Retour',
	updateAvailable: 'Mise à jour disponible',
	contentUpdateAvailableTooltip: "Mise à jour de contenu disponible. Cliquez ici pour accéder à l’écran'Mises à jour des formulaires Canvas' afin de lancer une mise à jour. ",
	moreMenu: 'Menu Plus',
	signoffPreparer: 'Approuver comme préparateur',
	signoffReviewer: 'Approuver comme responsable de la revue',
	pagingShowtext: 'Afficher',
	searchDocuments: 'Rechercher des documents',
	noRelatedDocuments: 'Aucun document associé.',
	noRelatedObjects: 'Aucun objet associé',
	documentName: 'Nom du document',
	formDetails: 'Données du formulaire',
	questionsAndResponses: 'Questions et réponses',
	details: 'Détails',
	trackChanges: 'Suivi des modifications',
	goToTrackChanges: 'Accéder à Suivi des modifications',
	attributes: 'Attributs',
	relatedActions: 'Actions associées',
	createCustom: 'Créer un formulaire personnalisé',
	createCustomButtonLabel: 'Create custom header, section, or body',
	overwriteForm: 'Écraser le formulaire',
	decimalNaN: 'NpC - N’est pas un chiffre',
	noRelatedObjectsApplicable: 'Il n’est pas obligatoire d’associer des objets à ce formulaire Canvas',
	objects: 'Objets',
	objectName: 'Nom de l’objet',
	addCustomDescription: 'Sélectionner le type de contenu à ajouter à ce formulaire Canvas, saisir les détails et cliquer sur enregistrer',
	headerTitle: 'Titre de l’en-tête',
	sectionTitle: 'Titre de la section (obligatoire)',
	aresSectionTitle: 'Titre de la section',
	customLabel: 'Étiquette personnalisée (facultatif)',
	customBodyDescription: 'Description du corps de texte',
	header: 'En-tête',
	section: 'Section',
	body: 'Corps de texte',
	requiredWCGW: 'Requis',
	headerTitleRequired: 'Titre de l’en-tête requis.',
	bodyDescriptionRequired: 'La description du corps de texte est requise.',
	bodySectionRequired: 'Le nom de la section est requis.',
	bodyHeaderRequired: 'Le nom de l’en-tête est requis.',
	sectionTitleRequired: 'Le titre de la section est requis.',
	headerRequiredMessage: 'Le nom de l’en-tête est requis.',
	enterDecimalAmount: 'Entrer un montant décimal',
	enterPercentage: 'Entrer un pourcentage',
	completeRiskFactorAssessment: 'Réaliser l’évaluation des événements et conditions relevés.',
	noScotsEstimatesIdentified: 'Aucune catégorie d’opérations importante ou estimation n’a été identifiée.',
	// Track changes
	trackChangesResponseLabel: 'Réponse en version suivi des modifications',
	trackChangesVersionLabel: 'Version en suivi des modifications',
	noResponseIdentified: 'Aucune réponse identifiée',

	// Compare responses
	compareResponsesLabel: 'Comparer les réponses',
	compareResponsesTitle: 'Comparer les réponses concernant l’entité',
	compareResponseNoDataPlaceholder: 'Aucune donnée disponible, car il n’y a qu’un seul document du même type pour la mission.',
	labelFor: 'pour',
	questions: 'Questions',
	answers: 'Réponses',
	countOfResponses: 'Nombre de réponses',
	openNotes: 'Notes ouvertes',
	clearedNotes: 'Notes réglées',
	click: 'Cliquez',
	clickToViewAnswer: 'pour afficher la réponse',
	clickToViewQuestionAnswer: 'pour afficher la question et la réponse',
	selectDocuments: 'Sélectionner des documents',
	selectedDocumentsCount: '{0} documents sélectionnés',
	selectedDocumentCount: '{0} document sélectionné',
	associatedDocuments: 'Documents associés',
	noAnswerProvided: 'Aucune réponse fournie',

	// Workspace Engagement
	thisEngagement: 'Cette mission',
	documentLocation: 'Emplacement du document',
	otherEngagementsInWorkspace: 'Autres missions dans l’espace de travail',
	added: 'Ajoutés',
	documentIneligibleForSharingMessage: 'Les documents confidentiels ne peuvent pas être partagés.',
	fitDocumentCannotbeSelected: "Les documents FIT ne peuvent pas être transférés d'une mission à l'autre. ",

	//Helix Configuration
	helixConfigurationTitle: 'Intégrer les données d’EY Helix',
	helixConfigurationPageDescription: 'Validez le projet EY Helix associé et importez les données dans EY Canvas. Si vous avez modifié des paramètres EY Helix ci-dessous ou avez apporté des changements à vos données EY Helix après avoir importé les données, vous devrez importer à nouveau les données pour que la mise à jour puisse se faire.',
	linkedEYHelixProjects: 'Projets EY Helix associés',
	client: 'Client : ',
	engagement: 'Mission : ',
	analysisDate: 'Date de l’analyse : ',
	eyHelixProjects: 'Projets EY Helix',
	noPrimaryEYHelixproject: 'Aucun projet EY Helix principal n’a été identifié.',
	here: 'ici',
	identifyEyHelixProjects: 'pour identifier un projet EY Helix et lancer le flux de travail.',
	eyHelix: 'EY Helix',
	primary: 'Primary',
	helixSettingsDescription: 'Cliquez sur Modifier pour sélectionner les paramètres qui seront appliqués au moment du chargement des analyseurs EY Helix.',
	editButton: 'Modifier',
	helixSettingsModalTitle: 'Paramètres EY Helix',
	currencyType: 'Type devise',
	currencyTypeError: 'Le type de devise n’a pu être récupéré à partir d’EY Helix. Confirmez que les données sont configurées correctement dans EY Helix et réessayez.',
	shortNumberFormat: 'Nombre abrégé',
	shortNumberFormatFooter: 'Arrondissement à appliquer aux valeurs numériques affichées dans les tableaux EY Helix',
	eyHelixAnalyzerFilterMetadataError: 'Impossible de se connecter à EY Helix. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage. ',
	functional: 'Fonctionnelle',
	reporting: 'De présentation',
	currencyCode: 'Code de monnaie',
	businessUnit: 'Unité fonctionnelle',
	roundingNumberFormat: 'Format du nombre arrondi',
	eyHelixProjectChangedLine1: 'Le projet EY Helix associé a été modifié depuis la dernière fois que les paramètres d’EY Helix ont été sauvegardés.',
	eyHelixProjectChangedLine2: 'Cliquez sur Modifier pour mettre à jour les paramètres avant de pouvoir importer ou réimporter des données d’EY Helix.',
	helixSettingsTimeline: 'Paramètres d’EY Helix',
	helixMapEntitiesToBU: 'Mapper les entités aux unités fonctionnelles',
	helixNumberOfMapEntities: 'Nombre d’unités fonctionnelles mappées',
	importEYHelixDataTimeline: 'Importer des données d’EY Helix',
	mapAccountsHelixTimeline: 'Mapper les comptes',
	setEYHelixSettings: 'Modifiez les paramètres EY Helix ci-après. Une fois les paramètres sauvegardés et les données importées, les dates de comparaison 1 et 2 sélectionnées seront utilisées pour les comparaisons par rapport à la date de l’analyse indiquée pour l’examen analytique global. Pour n’utiliser qu’une seule date de comparaison, sélectionnez « Aucune » dans le champ Date de comparaison 2.',
	eyHelixDataHasChangedLine1: 'Les données ont changé depuis la dernière fois que les paramètres ont été enregistrés. Refaites vos sélections ci-après et cliquez sur',
	eyHelixDataHasChangedLine2: 'pour mettre à jour les paramètres EY Helix.',
	all: 'Tous',
	multiple: 'Plusieurs',
	notApplicableAbbreviation: 'S. O.',
	importEyHelixData: 'Importer des données EY Helix',
	editScreenshot: 'Modifier',
	deleteNote: 'Supprimer',
	removeAnnotation: 'Supprimer l’annotation',
	addAnnotation: 'Ajouter l’annotation',
	addingBoundingBox: 'Sélectionnez une zone sur la capture d’écran pour annoter, confirmez l’annotation et cliquez sur le crochet pour enregistrer.',
	cancelBoundingBox: 'Annuler',
	deleteScreenshot: 'Supprimer la capture d’écran',
	openInFullscreen: 'Ouvrir en mode plein écran',
	helixURLErrorMessage1: 'La configuration du projet EY Helix mappé est incomplète.',
	helixURLErrorMessage2: 'Veuillez aller à la page {0} pour procéder à la mise à jour.',
	helixIsNotEnabledMessage: 'EY Helix n’est actuellement pas activé pour votre mission.',
	helixSetup: 'Configuration EY Helix',
	openAnalysisInHelix: 'Ouvrir l’analyse dans EY Helix',
	helixInvaliddate: 'Date non valide. Sélectionnez une date antérieure à la date de l’analyse.',
	helixcomparativedateoptional: 'Date de comparaison 2 dans EY Helix (facultatif)',
	helixpriorperioddate: 'Date de comparaison 1 dans EY Helix',
	helixanalysisperiod: 'Date de l’analyse dans EY Helix',
	helixfiscalDropDownLabel: 'Période {0} à {1}',

	helixSettingsEditButtonTitle: 'Modifier',
	helixImportDataEditButtonTitle: 'Importer',
	helixImportInsufficientPermissionsMessage: 'Vous ne disposez pas des autorisations requises pour importer des données Helix. Veuillez communiquer avec l’administrateur de la mission et demander l’autorisation de lancer l’importation des données Helix.',
	helixImportNotAllowed: 'Le profil de la mission ne permet pas l’importation de données EY Helix',
	helixDeleteImportInsufficientPermissionsMessage: 'Vous ne disposez pas des autorisations requises pour supprimer l’importation des données EY Helix. Veuillez communiquer avec l’administrateur de la mission et demander l’autorisation de supprimer des données EY Helix.',

	EYAccountMappingStepLabel: 'Gérer le mappage des comptes',

	EYAccountMappingOptional: 'Facultatif',
	EYAccountMappingStepTitleSettingsCompleted: 'Effectuer le mappage des comptes EY',
	EYAccountMappingStepTitleSettingsIncomplete: 'Définir les paramètres EY Helix pour accéder au module de mappage d’EY Helix',
	EYAccountMappingInstructions: 'Mapper les comptes clients aux comptes EY et traiter les changements. Une fois le mappage terminé, importer les données ci-dessous.',
	manageAccountMappingButtonLabel: 'Gérer le mappage des comptes',

	//EY Helix Setup Card
	EYHelixSetupTitle: 'EY Helix',
	EYHelixSetupSubTitle: 'Configurer et importer les données dans EY Canvas',
	LastImported: 'Dernière importation',
	EYHelixSettings: 'Connexions EY Helix',
	NoEYHelixProjectLinkedLabel1: 'Cette mission n’est pas liée à un projet EY Helix principal. Consultez',
	NoEYHelixProjectLinkedLabel2: 'pour établir le lien.',
	NoEYHelixProjectLinkedHperlink: 'Projets EY Helix',
	NoDataImportedLabel: 'Les données n’ont pas été importées d’EY Helix. Cliquez sur Configuration EY Helix pour lancer le processus.',
	noHelixConnections: 'Cliquez sur \’Connexions EY Helix\’ pour créer une connexion.',
	helixConnectionExists: 'connexion existante',
	helixConnectionsExist: 'connexions existantes',
	helixTrialBalanceImported: 'Balance des comptes importée pour Étendue et stratégie',
	helixTrialBalancesImported: 'Balances des comptes importées pour Étendue et stratégie',
	helixNoTrialBalanceImported: 'Cliquez sur \’Connexions EY Helix\’ pour importer une balance des comptes pour Étendue et stratégie.',

	// EY Helix - Map Accounts
	mapAccountsHelixCanvas: 'Cliquer sur Modifier pour mapper les comptes EY Canvas aux comptes importés d’EY Helix',
	mapAccountsHelixCanvasSubtitle: 'Glisser et déposer chaque compte EY Helix pour le mapper à un compte EY Canvas. Cliquer sur Gérer les comptes pour créer ou modifier des comptes EY Canvas. ',
	mapAccountsHelixHeaderLabel: 'Comptes EY Helix',
	mapAccountsCanvasHeaderLabel: 'Comptes EY Canvas',
	mapAccountsConnectedLabel: 'Comptes connectés',
	mapAccountsShowMappedLabel: 'Afficher les comptes mappés',
	mapAccountsHideMappedLabel: 'Masquer les comptes mappés',
	mapAccountsManageLabel: 'Gérer les comptes',
	mapAccountsAndDisclosuresManageLabel: 'Gérer les comptes et les informations à fournir',
	mapAccountsNoCanvasAccounts: 'Aucun compte n’a été identifié.',
	mapAccountsNoCanvasAccountsClick: 'Cliquer',
	mapAccountsNoCanvasAccountGetStarted: 'pour commencer.',
	mapAccountsRemoveAccount: 'Supprimer',
	mapAccountsReImportHelixAccounts: 'Échec de l’importation de données d’EY Helix. Veuillez réimporter les données et réessayer.',
	mapAccountsReImportHelixAccountsHelpDesk: 'Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	mapAccountsNoHelixAccountHasBeenImported: 'Aucun compte EY Helix n’a été importé.',
	mapAccountsNoHelixAccountHasBeenImportedCheckData: 'Vérifiez les données dans EY Helix et réessayez.',

	//Helix Analyzer
	accountNotRelatedToDocumentOnPhaseTwo: 'Aucun compte associé à ce document',

	//PM TE SAD Widget
	materialityWidgetLabel: 'SSI/EA/Seuil minimal de report des anomalies',

	// TE labels
	planningmateriality: 'Seuil de signification initial',
	requiredTePercentage: 'EA requise',
	suggestedtepercentage: 'EA suggérée',
	currentperiodte: 'EA de la période considérée',
	priorperiodte: 'EA de la période précédente',
	pmPriorPeriod: 'SSI de la période précédente',
	tepercentage: 'Pourcentage de l’EA',
	teamount: 'Montant de l’EA',
	teLabel: 'Erreur acceptable',
	sugestedSAD: 'Pourcentage suggéré pour le seuil minimal de report des anomalies : ',
	priorSAD: 'Sommaire des anomalies de la période précédente',
	currentPeriodSAD: 'Seuil minimal de report des anomalies pour la période en cours',
	sadPercentage: 'Pourcentage de seuil minimal de report des anomalies',
	sadAmount: 'Montant de seuil minimal de report des anomalies',
	rationaleLabel: 'Justification',
	suggestedTEPercentageInfo: 'Si un pourcentage différent est sélectionné en raison de facteurs propres à la mission, vous devrez consigner ces facteurs ci-dessous.',
	rationaleTEDescription: 'Justifiez l’EA en tant que pourcentage du SSI en tenant compte des attributs sélectionnés ci-dessus.',
	teAmmountInvalid: 'Entrer un montant valide ou sélectionner 50 % ou 75 %',
	highRiskTEAmmountInvalid: 'Entrer un montant valide ou sélectionner 50 %',
	highRiskTERequired: 'Compte tenu des réponses données ci-dessus, le pourcentage de l’EA indiqué est obligatoire et ne peut pas être modifié.',

	// EY Helix Map Entities to Business Units Modal
	mapEntitiesModalTitle: 'Gérer les entités',
	mapEntitiesModalLeyendDescription: 'Gérer le mappage entre les entités EY Canvas et les unités fonctionnelles du projet EY Helix ci-après.',
	mapEntitiesModalLeyendNote: 'Note : Une unité fonctionnelle peut être associée à une seule entité ou à plusieurs entités EY Canvas. Une fois le mappage enregistré et les données importées, les données associées aux unités fonctionnelles EY Helix s’afficheront comme étant mappées aux entités EY Canvas associées.',
	mapEntitiesModalEntityCodeLabel: 'Code de l’entité',
	mapEntitiesModalEmptyEntitiesList: 'Aucune entité n’a été créée.',
	mapEntitiesRelatedBusinessUnitDropdownPlaceholder: 'Unité fonctionnelle associée',
	mapEntitiesSelectedBusinessUnitsCount: '{0} unités fonctionnelles sélectionnées',

	//AdjustedBasis
	enterAmount: 'Entrer le montant',
	basisAmount: 'Montant de base',
	lowEndOfRange: 'Extrémité inférieure de la fourchette',
	highEndOfRange: 'Extrémité supérieure de la fourchette',
	suggestedRange: 'Compte tenu des facteurs ci-dessus, l’utilisation d’un pourcentage qui se situe vers {0} de la fourchette {1} à {2} peut être approprié. Si un montant est sélectionné à l’extérieur de cette fourchette en raison de facteurs propres à la mission, vous devrez consigner ces facteurs ci-dessous.',
	suggestedRangeLowPMBracket: 'Compte tenu des facteurs ci-dessus, un pourcentage de {0} peut être approprié. Si un pourcentage différent est sélectionné en raison de facteurs propres à la mission, vous devrez consigner ces facteurs.',
	middle: 'milieu',
	lowerEnd: 'extrémité inférieure',
	higherEnd: 'extrémité supérieure',
	lowEnd: 'extrémité inférieure',
	priorPeriodPm: 'SSI de la période précédente : ',
	suggestedRangeSummary: 'Fourchette suggérée',
	loadingMateriality: 'Chargement du seuil de signification en cours...',
	pmBasisPercentage: 'Pourcentage de base du SSI',
	pmAmount: 'Montant du SSI',
	currentPmAmount: 'Montant du SSI de la période considérée',
	pmAmountPlaceholder: 'Entrer le montant du SSI',
	currentPeriodPm: 'SSI de la période considérée : ',
	enterRationale: 'Saisir la raison',
	rationaleDescription: 'Justifiez le pourcentage utilisé pour le calcul du SSI en tenant compte des attributs sélectionnés ci-dessus.',
	pmValidationMessage: 'Le seuil de signification initial ne peut pas dépasser l’extrémité supérieure de la fourchette.',
	sadValidationMessage: 'Le seuil minimal de report des anomalies ne peut pas dépasser l’extrémité supérieure de la fourchette.',
	sadRationaleDiscription: 'Justifiez le pourcentage de seuil minimal de report des anomalies en tenant compte des attributs sélectionnés ci-dessus.',
	nopriorperiodDocs: 'Aucun document de la période précédente disponible',
	addPriorPeriodEvidence: 'Ajouter des éléments probants de la période précédente',
	addToEvidenceLabel: 'Ajouter aux éléments probants',
	moveToEvidenceLabel: 'Déplacer vers les éléments probants',
	addToEvidenceModalDescription: 'Créez un nouveau nom ou conservez le nom existant pour le document sélectionné.',
	GoToSource: 'Accéder à la source',
	//ITRiskITControls
	createNewITGC: 'Nouveau CGI',
	relateITGC: 'Relate ITGCs',
	createNewITSP: 'Nouvelle procédure de corroboration liée aux TI',
	relateITSP: 'Associer une procédure de corroboration liée aux TI',
	noITGC: 'Aucun CGI',
	itRiskForITGCITSP: 'Nom du risque lié aux TI (obligatoire)',
	createITGCModalDescription: 'Entrez les détails relatifs au CGI ci-dessous et sélectionnez ’<b>{0}</b>’ pour terminer. Pour créer un autre CGI, sélectionnez ’<b>{1}</b>’.',
	createITSPModalDescription: 'Entrez les détails relatifs à la procédure de corroboration liée aux TI ci-dessous et sélectionnez ’<b>{0}</b>’ pour terminer. Pour créer une autre procédure de corroboration liée aux TI, sélectionnez ’<b>{1}</b>’.',
	controlDesignEffectiveness: {
		[0]: {
			description: 'Non sélectionnée'
		},
		[1]: {
			description: 'Efficace'
		},
		[2]: {
			description: 'Inefficace'
		}
	},
	controlOperationEffectiveness: {
		[0]: {
			description: 'Non sélectionnée'
		},
		[1]: {
			description: 'Efficace'
		},
		[2]: {
			description: 'Inefficace'
		}
	},
	controlTesting: {
		[0]: {
			description: 'Non sélectionnée'
		},
		[1]: {
			description: 'Oui'
		},
		[2]: {
			description: 'Non'
		}
	},
	itAppTypes: {
		[0]: {
			label: 'TI'
		},
		[1]: {
			label: 'SS'
		}
	},
	controlType: {
		[0]: {
			controlTypeName: '',
			shortName: 'Non sélectionné'
		},
		[1]: {
			controlTypeName: 'Contrôle des applications informatiques',
			shortName: 'Application'
		},
		[2]: {
			controlTypeName: 'Contrôle manuel lié aux TI',
			shortName: 'Contrôle manuel lié aux TI'
		},
		[3]: {
			controlTypeName: 'Contrôle manuel de prévention',
			shortName: 'Contrôle manuel de prévention'
		},
		[4]: {
			controlTypeName: 'Contrôle manuel de détection',
			shortName: 'Contrôle manuel de détection'
		}
	},
	controlTypeEnumLabel: {
		[0]: {
			controlTypeName: 'Non sélectionné'
		},
		[1]: {
			controlTypeName: 'Contrôle des applications informatiques'
		},
		[2]: {
			controlTypeName: 'Contrôle manuel lié aux TI'
		},
		[3]: {
			controlTypeName: 'Contrôle manuel de prévention'
		},
		[4]: {
			controlTypeName: 'Contrôle manuel de détection'
		}
	},
	controlFrequencyType: {
		[0]: {
			controlFrequencyTypeName: 'Non sélectionnée'
		},
		[1]: {
			controlFrequencyTypeName: 'Plusieurs fois par jour'
		},
		[2]: {
			controlFrequencyTypeName: 'Une fois par jour'
		},
		[3]: {
			controlFrequencyTypeName: 'Une fois par semaine'
		},
		[4]: {
			controlFrequencyTypeName: 'Une fois par mois'
		},
		[5]: {
			controlFrequencyTypeName: 'Une fois par trimestre'
		},
		[6]: {
			controlFrequencyTypeName: 'Une fois par année'
		},
		[7]: {
			controlFrequencyTypeName: 'Contrôle des applications informatiques'
		},
		[8]: {
			controlFrequencyTypeName: 'Autre'
		}
	},
	strategyType: {
		[0]: {
			strategyTypeName: 'Non sélectionné'
		},
		[1]: {
			strategyTypeName: 'Contrôles'
		},
		[2]: {
			strategyTypeName: 'Corroboration'
		},
		[3]: {
			strategyTypeName: 'Appui'
		},
		[4]: {
			strategyTypeName: 'Pas d’appui'
		}
	},
	aggregateITEvaluationType: {
		[0]: {
			aggregateITEvaluationTypeName: 'Non sélectionnée'
		},
		[1]: {
			aggregateITEvaluationTypeName: 'Appui'
		},
		[2]: {
			aggregateITEvaluationTypeName: 'Pas d’appui'
		},
		[3]: {
			aggregateITEvaluationTypeName: 'Appui - ÉF et CIIF'
		},
		[4]: {
			aggregateITEvaluationTypeName: 'Appui -  ÉF seulement'
		}
	},

	sampleItemFilterLabels: {
		filterTypeOfTags: 'Balises',
		noFiltersAvailable: 'Aucun filtre disponible',
		filterToolTip: 'Filtrer',
		clearAll: 'Tout effacer',
		showMore: 'plus',
		filters: 'Filtres',
		noResults: 'Aucun résultat'
	},

	stratergyTypeLabels: {
		[0]: {
			label: 'Non sélectionnée'
		},
		[1]: {
			label: 'Visée'
		},
		[2]: {
			label: 'Non visée'
		}
	},
	noChangeReasonCommentAvailable: 'Cliquez sur Modifier la raison dans les options de la roue dentée du document pour saisir la raison du changement.',
	changeReasonModalTitle: 'Modifier la raison du changement',
	changeReasonModalText: 'Sélectionnez la raison des modifications apportées au document après la date du rapport. Si plusieurs modifications de nature administratives ont été apportées, sélectionnez la modification la plus importante dans le menu déroulant ci-dessous. Si des modifications de nature administrative et autres que de nature administrative ont été apportées, sélectionnez l’option « autre que de nature administrative » ci-dessous.',
	changeReasonUploadModalTitle: 'Raison pour laquelle le document a été téléchargé',
	changeReasonUploadModalText: 'Sélectionnez la raison des modifications apportées au document après la date du rapport. Si plusieurs modifications de nature administratives ont été apportées, sélectionnez la modification la plus importante dans le menu déroulant ci-dessous. Si des modifications de nature administrative et autres que de nature administrative ont été apportées, sélectionnez l’option « autre que de nature administrative » ci-dessous.',
	changeReasonModalComboPlaceholder: 'Sélectionner',
	changeReasonModalAnnotationText: 'Consignez les circonstances particulières et les raisons de l’ajout de l’information, les procédures d’audit nouvelles ou supplémentaires mises en œuvre, les éléments probants obtenus et les conclusions tirées, ainsi que l’incidence sur notre rapport.',
	changeReasonUploadModalAnnotationText: 'Consignez les circonstances particulières et les raisons de l’ajout de l’information, les procédures d’audit nouvelles ou supplémentaires mises en œuvre, les éléments probants obtenus et les conclusions tirées, ainsi que l’incidence sur notre rapport.',
	changeReasonModalAnnotationPlaceHolder: 'Indiquer la raison du changement',
	changeReasonModalChangeReasonRequired: 'Raison de la modification à enregistrer',
	reasonColumnTitle: 'Raison',
	shared: 'Partagé',
	shareStatusOwned: 'Appartenant à cette mission.',
	shareStatusShared: 'Partagé dans cette mission.',
	lastModifiedBy: 'Dernière modification par',
	fileSize: ' | {1} Ko',
	openedLabelText: 'Ouvert',
	currentlyBeingModifiedBy: 'En cours de modification par',
	OpenGuidedWorkflowDocument: 'Ouvrir ce document à partir de l’outil FIT d’EY Canvas',
	submitProfile: 'Envoyer le profil',
	submitProfileFit: 'Envoyer le profil',
	contentUpdateUnAuthorizedTooltipMessage: 'Vous ne disposez pas des autorisations requises pour effectuer la mise à jour du contenu. Veuillez communiquer avec l’administrateur de la mission et demander l’autorisation de lancer la mise à jour de contenu.',
	submitProfileValidationErrorMessage: 'Le profil ne peut être envoyé que lorsque vous avez répondu à toutes les questions. Utilisez la fonction de filtre pour trouver les réponses manquantes, ajoutez-les et envoyez à nouveau. Si le problème persiste, communiquez avec le Service de dépannage.',
	pickADate: 'Sélectionner une date',

	/* Sign Offs */
	preparerHoverText: 'Approuver comme préparateur',
	reviewerHoverText: 'Approuver comme responsable de la revue',
	preparerTitle: 'Approbations des préparateurs',
	reviewerTitle: 'Approbations des responsables de la revue',
	deleteHoverText: 'Retirer l’approbation',
	preparer: 'Préparateur',
	reviewer: 'Responsable de la revue',
	preparerLabel: 'P',
	reviewerLabel: 'R',
	noSignOffsAvailable: 'Aucune approbation disponible',
	none: 'Aucun',
	partnerInChargeLabel: 'PIC',
	eqrLabel: 'RRQM',
	documentSignoffRequiredLabel: 'Approbations requises de : ',

	relatedDocumentsTitle: 'Documents associés',
	relatedTasksCount: '{0} tâches associées',
	relatedTasksTitle: 'Tâches associées',
	relateTemporaryFiles: 'Associer des fichiers temporaires',
	bodyRelatedDocumentsTitle: 'Documents associés à un corps',
	relatedObjectsTitle: 'Objets associés',
	relateDocumentsTitle: 'Gérer les documents associés',
	relateDocumentsToBodyTitle: 'Ajouter des éléments probants',
	relateDocumentsDesc: 'Sélectionner les documents à associer au formulaire Canvas',
	relateDocumentsToBodyDesc: 'Associer un document de cette mission ou d’une autre mission à cet espace de travail',
	relateDocumentsToTheBody: 'Relate a document from this engagement.',
	priorPeriodEvidencesToTheBody: 'Éléments probants de la période précédente associés au corps du texte',
	relatedDocunentEngdisabed: 'Le document n’est pas partagé avec cette mission.',
	showOnlyRelatedDocuments: 'Afficher seulement les documents associés',
	manageDocuments: 'Gérer les documents',
	documentCount: '{0} document',
	documentsCount: '{0} documents',
	relateDocumentsSearchPlaceholder: 'Rechercher des documents',
	overwriteFormDesc: 'Sélectionnez un formulaire pour écraser les réponses à partir du formulaire Canvas actuel. Veuillez noter que le formulaire actuel sera déplacé vers les fichiers temporaires.',
	searchFormPlaceholder: 'Rechercher un formulaire',
	overwriteLabel: 'Écraser',
	confirmOverwriteLabel: 'Confirmer l’écrasement',
	confirmOverwriteDesc: "Voulez-vous vraiment copier le contenu du formulaire'{0}' dans le formulaire'{1}'? Les réponses seront écrasées dans le formulaire'{2}', mais pas les éléments probants ni les objets associés. Les éléments probants et les objets associés applicables doivent être réassociés au formulaire'{3}' une fois l’écrasement terminé. Les sections sur le profil et les volets de la MMA seront conservés dans le formulaire'{4}'. Par conséquent, si les sections sur le profil ne correspondent pas à celles du formulaire'{5}', veuillez passer en revue le contenu copié et le mettre à jour au besoin. \n Ne fermez pas le navigateur ou la page pendant le processus d’écrasement. Une fois le processus terminé, le formulaire'{6}' sera déplacé vers les fichiers temporaires et vous serez redirigé vers le formulaire'{7}'. Cette action ne peut pas être annulée. ",
	formSelectionRequired: 'Sélectionner un formulaire à écraser.',

	open: 'Ouvrir',
	startCoeditMode: 'Lancer le mode édition multiutilisateur',
	endCoeditMode: 'Quitter le mode édition multiutilisateur',
	openReadOnly: 'Ouvrir en lecture seule',
	copyLink: 'Copier le lien',
	rename: 'Renommer',
	viewHistory: 'Afficher l’historique',
	documentOpenModelLabel: 'Le document est en cours de modification',
	modelUserOpenedTheDocumentText: 'Cet utilisateur a ouvert le document',
	modelDocumentOpenedText: 'Ce document est en cours de modification par',
	modelOpenedDocumentConflictText: 'L’ouverture du document pourrait entraîner des conflits, il est donc recommandé de l’ouvrir en lecture seule. Si vous souhaitez devenir l’éditeur du document',
	clickHereEnabledText: 'cliquez ici.',
	documentOptions: 'Options du document',
	accountDetails: 'Renseignements sur le compte',

	// DAAS labels
	coEditModeIsEnding: 'Fin du mode édition multiutilisateur',
	coEditMode: 'Mode édition multiutilisateur',
	checkInInProgressMessage: 'Archivage en cours. Le processus peut prendre jusqu’à 20 minutes. Veuillez actualiser la page pour voir les mises à jour.',
	checkInInErrorLabel: 'Échec de la réimportation.',
	checkOutInProgressMessage: 'Extraction en cours. Le processus peut prendre jusqu’à 20 minutes. Veuillez actualiser la page pour voir les mises à jour.',
	checkOutInProgressLabel: 'Extraction en cours.',
	checkInInProgressLabel: 'Réimportation en cours.',
	checkOutInErrorLabel: 'Échec de l’extraction.',
	daasErrorMessage: 'L’opération ne peut pas être traitée pour le moment. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	coEditModeIsStarting: 'Début du mode édition multiutilisateur',
	daasOpenDocumentWarning: 'Multi-user editing may have been ended by another user. Refresh the page and try again.',
	beingEditedInCoeditMode: 'Being edited in multi-user edit. Edit started by {0}',
	beingEditedInCoeditModeOn: 'on {0}.',
	beingEditedInCoeditModeError: 'Being edited in multi-user edit',
	coEditModeAutomaticallyEnds: 'Le document est actuellement en mode édition multiutilisateur, qui prendra automatiquement fin dans {0} jours.',
	coEditModeAutomaticallyEndsToday: 'Le document est en mode édition multiutilisateur, qui prendra fin aujourd’hui.',
	daasStartCollaborationModeWarning: 'Collaboration mode may have been started by another user. Refresh the page and try again.',
	documentCurrentlyBeingModifiedTitle: 'Document currently being modified',
	documentCurrentlyBeingModifiedHeader: 'This document is currently being modified by {0}. This user opened the document',
	documentCurrentlyBeingModifiedBody: 'Starting multi-user edit mode could cause conflicts, so we recommend discussing with {0} before proceeding. Select {1} to start the multi-user edit mode or {2} to return without starting multi-user edit mode.',
	documentEndMultiUserEditingTitle: 'Quitter le mode édition multiutilisateur',
	documentEndMultiUserEditingHeader: 'Warning: Other users may be actively editing this document.  This can be checked by opening the document and seeing if other users are currently in the file. Please confirm that all changes are complete before ending multi-user mode. File changes in multi-user mode may take up to 1 minute to be processed. Therefore, please wait at least 1 minute after exiting the file before ending multi-user mode.',
	documentEndMultiUserEditingBody: 'Select {0} to end the multi-user edit mode or {1} to return without ending multi-user edit mode.',
	startMultiuserEditing: 'Start',

	/* Engagement Comments */
	clear: 'Effacer',
	close: 'Fermer',
	reOpen: 'Rouvrir',
	reply: 'Ajouter une réponse',
	replyLabel: 'Répondre',
	unselectComment: 'Désélectionner le commentaire',
	commentText: 'Entrer le texte',
	replyText: 'Texte de réponse',
	openStatus: 'Ouvertes',
	clearedStatus: 'Effacées',
	closedStatus: 'Fermées',
	chartCommentsTitle: 'Notes de revue',
	showComments: 'Afficher les commentaires',
	noRecordsFound: 'Aucun enregistrement trouvé',
	noCommentsFound: 'Veuillez laisser un commentaire en utilisant les données ci-après. Attribuez le commentaire à un utilisateur et indiquez la priorité ainsi que la date d’échéance.',
	newComment: 'Ajouter un commentaire',
	addNoteTitle: 'Ajouter une note',
	editComment: 'Modifier le commentaire',
	newReply: 'Ajouter une réponse',
	editReply: 'Modifier la réponse',
	commentTextRequired: 'Le commentaire doit contenir du texte',
	replyTextRequired: 'Texte de réponse requis',
	myComments: 'Mes commentaires',
	assignTo: 'Affecter à',
	theCommentMustBeAssigned: 'Le champ Attribué à doit être rempli',
	priorityRequired: 'Priorité requise',
	dueDateRequired: 'Date d’échéance requise',
	assignedTo: 'Attribuée à',
	allComments: 'Tous les commentaires',
	assignedToMe: 'Attribués à moi',
	unassigned: 'Non attribuée',
	draggableCommentsPlaceholder: 'Saisir le texte pour ajouter un nouveau commentaire',
	draggableNotesPlaceholder: 'Saisir le texte pour ajouter une nouvelle note',
	enterReply: 'Saisir la réponse',
	dueDate: 'Date d’échéance',
	commentsAmmount: '{count} commentaires',
	singleCommentAmmount: '{count} commentaire',
	eyInternal: 'EY',
	noneAvailable: 'Aucune information disponible',

	navHelixProjects: 'Connexions EY Helix',

	evidence: 'Éléments probants',
	priorPeriod: 'Période précédente',
	temporaryFiles: 'Fichiers temporaires',
	priorPeriodEvidence: 'Période précédente',
	closed: 'Toutes les affectations fermées',

	/*Delete*/
	deleteFileTitle: 'Supprimer le document',
	deleteFileCloseBtnTitle: 'Annuler',
	deleteFileConfirmBtnTitle: 'Supprimer',
	deleteFileCloseTitle: 'Fermer',
	deleteFileModalMessage: 'Voulez-vous vraiment supprimer le document sélectionné?',
	/*Rename*/
	editCanvasformObjects: 'Modifier les attributs et cliquer sur <b>Enregistrer</b>.',
	renameFileModalMessage: 'Renommez le document et cliquez sur Enregistrer',
	renameScreenshotModalMessage: 'Renommez la capture d’écran et cliquez sur Confirmer.',

	renameFileTitle: 'Renommer le document',
	fileNameRequired: 'Le nom du fichier est requis',
	invalidCharacters: 'Le nom du fichier ne peut pas contenir : */:<>\\?|"',
	existingFileName: 'Le nom du fichier n’est pas unique. Actualisez la page ou renommez le fichier pour supprimer ce message.',
	maxLengthExceeded: 'Le nom du document ne doit pas dépasser la limite de 115 caractères.',

	STEntityProfileBannerMessage: 'Des modifications qui entraîneront une mise à jour du contenu ont été apportées à un ou plusieurs profils d’entité. Retournez à la page de profilage et cliquez sur « Importer le contenu » pour recevoir le nouveau contenu applicable ou rétablissez les réponses précédentes.',
	independenceValidationForOwnForm: 'Des modifications ont été apportées aux réponses relatives à l’indépendance, mais n’ont pas été envoyées. Si les modifications sont volontaires, assurez-vous que les réponses sont envoyées. Si les modifications ne sont pas volontaires, passez en mode Afficher les changements et rétablissez manuellement les réponses précédemment sélectionnées.',
	independenceValidationForOthersForm: 'Des modifications ont été apportées aux réponses relatives à l’indépendance, mais n’ont pas été envoyées par le membre de l’équipe. Vérifiez que le membre de l’équipe passe en revue les modifications et les envoie si elles sont volontaires.',
	insufficientRightsForIndependenceSubmission: 'Vous ne disposez pas des autorisations requises pour modifier le contenu. Veuillez communiquer avec l’administrateur de la mission et demander l’autorisation de modifier le contenu.',
	submitIndependenceProfileV2Message: 'Veuillez passer en revue le profil et confirmer que les réponses sont exactes. Le cas échéant, veuillez fournir les approbations nécessaires et poursuivre la mission.',
	submitIndependenceProfileV2EditMessage: 'Aucune modification qui pourrait entraîner une mise à jour du contenu de la mission n’a été apportée au profil. Consultez la page de mise à jour du contenu de la mission pour effectuer une mise à jour du contenu au besoin.',
	insufficientRightsForProfileV2Submission: 'Vous ne disposez pas des autorisations requises pour modifier le profil. Veuillez communiquer avec l’administrateur de la mission et demander l’autorisation de modifier le profil.',
	returnToDashboard: 'Revenir au tableau de bord',
	returnToDashboardFit: 'Revenir au tableau de bord',
	profileV2ChangeNotSubmittedBannerMessage: 'Changes have been made to the Profile that will result in content updates. Submit the Profile to receive the new content or revert the answers to the previous state.',
	independenceChangeNotSubmittedBannerMessage: 'Des modifications ont été apportées formulaire relatif à l’indépendance, qui doit être envoyé à nouveau. Envoyez le formulaire relatif à l’indépendance ou désactivez cet utilisateur pour supprimer le contrôle de validation.',
	multiEntityIndividualProfileBannerMessage: 'Vous ne disposez pas des autorisations requises pour modifier le profil. Veuillez communiquer avec l’administrateur de la mission et demander l’autorisation de modifier le profil.',
	scotStrategy: 'Stratégie axée sur la catégorie importante d’opérations',
	wcgwStrategy: 'Stratégie axée sur l’erreur possible',
	itProcessStrategy: 'Stratégie axée sur le processus informatique',

	/*Edit Wcgw*/
	editWcgw: 'Modifier l’erreur possible',
	viewWcgw: 'Afficher l’erreur possible',
	editScot: 'Modifier la catégorie importante d’opérations',
	viewScot: 'Afficher la catégorie importante d’opérations',
	showIncomplete: 'Afficher les éléments incomplets',
	forms: 'Formulaires',
	form: 'Formulaire',
	comments: 'Notes',
	changes: 'Modifications',
	editHeader: 'Modifier l’en-tête',
	editSection: 'Modifier la section',
	editBody: 'Modifier le corps du texte',
	editSectionDescription: 'Modifiez les détails de la section et cliquez sur « Enregistrer ».',
	editHeaderDescription: 'Modifiez les détails de l’en-tête et cliquez sur « Enregistrer ».',
	editBodyDescription: 'Modifiez les détails du corps de texte et cliquez sur « Enregistrer ».',
	manageObject: 'Gérer lobjet',
	relatedObjects: 'Objets associés',

	/* Manage body objects */
	bro_manage_WCGWTask_title: 'Associer les erreurs possibles ',
	bro_manage_WCGWTask_instructions: 'Gérer les erreurs possibles qui s’appliquent',
	bro_manage_WCGWTask_noDataLabel: 'Aucun résultat',

	/*Add/Edit ITGC*/
	addITGC: 'Ajouter un CGI',
	addNewITGC: 'Ajouter un nouveau CGI',
	addExistingITGC: 'Ajouter un CGI existant',
	addITGCDescription: 'Entrer la description du CGI.',
	itControlNameRequired: 'Le nom du CGI est requis',
	frequencyRequired: 'La fréquence est requise',
	frequencyITGC: 'Sélectionner la fréquence',
	nameITGC: 'Nom du CGI (requis)',
	iTProcesslabel: 'Processus informatique',
	editITGC: 'Modifier le CGI',
	editITSP: 'Modifier la procédure de corroboration liée aux TI',
	editITGCDescription: 'Modifier le CGI et les attributs qui y sont associés',
	editITSPDescription: 'Modifier la procédure de corroboration liée aux TI et les attributs qui y sont associés',
	viewITGC: 'Afficher le CGI',
	viewITSP: 'Afficher la procédure de corroboration liée aux TI',
	itgcTaskDescription: 'Mettre en œuvre les tests des CGI que nous avons conçus de manière à obtenir des éléments probants suffisants et appropriés sur l’efficacité de leur fonctionnement pour toute la période d’appui.',
	/**
	 * Add Edit ITGC
	 */
	addITSPDescription: 'Saisir la description de la procédure de corroboration liée aux TI.',
	selectITRisk: 'Sélectionner le risque lié aux TI (requis)',
	itRiskRequired: 'Risque lié aux TI (requis)',
	itspNameRequired: 'Nom de la procédure de corroboration liée aux TI (requis)',
	itspTaskDescription: 'Adapter la description de cette tâche en fonction de la nature, du calendrier et de l’étendue des procédures de corroboration liées aux TI de manière à obtenir des éléments probants suffisants et appropriés confirmant que les risques liés aux TI sont traités efficacement pour toute la durée de la période d’appui.<br />Si les procédures de corroboration liées aux TI sont mises en œuvre à une date intermédiaire, concevoir et mettre en œuvre des procédures de manière à obtenir des éléments probants supplémentaires confirmant que les risques liés aux TI sont traités pour la période visée par nos procédures intermédiaires jusqu’à la fin de la période.<br />Nous formulons une conclusion sur les résultats des procédures de corroboration liées aux TI.',
	itspRequired: 'Le nom de la procédure de corroboration liée aux TI est requis.',
	selectTestingStrategy: 'Déterminer la nature, le calendrier et l’étendue de nos tests des contrôles de manière à obtenir des éléments probants suffisants et appropriés confirmant que les contrôles fonctionnent efficacement comme prévu pour toute la durée de la période d’appui et permettent de prévenir ou de détecter et de corriger les anomalies significatives au niveau des assertions.<br />Formuler une conclusion sur l’efficacité du fonctionnement des contrôles, en évaluant les résultats de nos tests des contrôles, y compris lorsque nous avons élargi la taille de nos échantillons et mis en œuvre des tests des contrôles compensatoires.',
	itControlNameTest: 'Test {0}',

	/*Edit ITControl*/
	editITControl: 'Modidier les CGI / procédures de corroboration liés aux TI',
	viewITControl: 'Afficher les CGI / procédures de corroboration liés aux TI',

	/*Add/Edit ITRisk*/
	editITRisk: 'Modifier le risque lié aux TI',
	editITRiskDescription: 'Modifier le risque lié aux TI',
	viewITRisk: 'Afficher le risque lié aux TI',
	addITRisk: 'Ajouter un risque lié aux TI',
	addITRiskDescription: 'Entrer la description du risque lié aux TI',
	selectITProcess: 'Sélectionner le processus informatique (requis)',
	itRiskName: 'Risque lié aux TI',
	itRiskNameRequired: 'Risque lié aux TI (requis)',
	riskNameRequired: 'Le risque lié aux TI est requis',
	processIdRequired: 'Le processus informatique est requis',
	itProcessRequired: 'Processus informatique (requis)',
	hasNoITGC: 'Aucun CGI ne répond au risque lié aux TI',

	/*Edit Risk*/
	editRisk: 'Modifier le risque',
	viewRisk: 'Afficher le risque',

	/*Edit Control*/
	editControl: 'Modifier le contrôle',
	viewControl: 'Afficher le contrôle',
	scotRelatedControls: 'Contrôles associés à',
	applicationControl: 'Contrôle d’application',
	iTDependentManualControl: 'Contrôle manuel lié aux TI',
	noAapplicationControlAvailable: 'Aucun contrôle d’application',
	noITDependentManualControlAvailable: 'Aucun contrôle manuel lié aux TI',
	isIPEManuallyTested: 'La partie automatisée de ce contrôle manuel lié aux TI ne consiste qu’en l’utilisation de rapports générés par le système qui font l’objet de tests de corroboration',

	/*Edit ITProcess*/
	editITSOProcess: 'Modifier le processus TI/SS',
	viewITSOProcess: 'Afficher le processus TI/SS',

	/*Edit ITApplication*/
	viewITAppSO: 'Afficher l’application informatique / la société de services',
	editITAppSO: 'Modifier l’application informatique / la société de services',
	strategy: 'Stratégie',
	nameRequired: 'Nom requis',
	name: 'Nom',

	/*Snap shot*/
	currentVersion: 'Version courante',
	compareVersion: 'Sélectionner une version à comparer',
	snapshotVersionNotAvailable: 'Aucune version disponible aux fins de comparaison',
	snapshots: 'Instantanés',
	sharedFormWarning: "Il s’agit d’un formulaire Canvas partagé. Les objets et les éléments probants de la mission initiale ne seront pas ajoutés à cette mission une fois dissociés de la mission initiale. Cliquez sur <a style='color: #467cbe' href='https://live.atlas.ey.com/#library/104/p/SL33184174-396647/C_33404446/C_38129691' target='_blank'>enablement here</a> pour plus de renseignements. ",
	fullView: 'Vue complète',
	defaultView: 'Vue par défaut',
	print: 'Imprimer',
	version: 'Version',
	navigationUnavailable: 'La navigation n’est pas possible dans les vues Suivi des modifications et Attributs. Affichez la vue Questions et réponses pour réactiver la navigation.',
	snapshotUpdate: 'Mise à jour',
	snapshotNew: 'Nouveau',
	snapshotRemoved: 'Supprimée',
	snapshotRollforward: 'Créé au moment de la récupération',
	snapshotRestore: 'Créé au moment de la restauration',
	snapshotCopy: 'Créé au moment de la copie',

	/*Special Body*/
	priorPeriodAmount: 'Montant de la période précédente',

	// Helix special body:
	helixScreenshotListLoading: 'Chargement des captures d’écran en cours...',
	helixScreenshotLoading: "Chargement de l'image de la capture d’écran en cours... ",
	helixScreenshotDeleting: 'Suppression de la capture d’écran en cours...',
	helixNotesLoading: 'Chargement des marques de pointage…',
	helixNotesBoundingBoxShow: 'Afficher les annotations',
	helixNotesBoundingBoxHide: 'Masquer les annotations',
	helixNoteReferenceNumber: '#',
	helixNoteReferenceNumberPlaceholder: 'Saisir le numéro de référence',
	helixNoteText: 'Note',
	helixNoteTextPlaceholder: 'Entrer le texte associé à la marque de pointage',
	helixNoteAnnotate: 'Annoter',
	helixNoteAnnotateMessage: 'Sélectionner une zone sur la capture d’écran pour annoter, confirmer l’annotation et cliquer sur le crochet pour sauvegarder. ',
	helixRemoveAnnotation: 'Effacer l’annotation',

	/* User lookup body */
	userLookupInstructionalText: 'Entrer le nom ou l’adresse courriel et appuyer sur la touche Entrée pour afficher les résultats.',
	userLookupShortInstructionalText: 'Entrer le nom ou l’adresse courriel et appuyer sur la touche Entrée',

	/*Guidance*/
	guidance: 'Indications',
	noIncompleteBodies: 'Sélectionner un en-tête ou une section dans le menu de navigation pour afficher le contenu',
	noUnresolvedComments: 'Sélectionner un en-tête ou une section dans le menu de navigation pour afficher le contenu',
	addComment: 'Ajouter un commentaire',

	/*Independence*/
	otherFormIndependenceMessage: 'Le contenu de ce formulaire sur l’indépendance a été mis à jour et l’utilisateur ne s’est pas connecté depuis. Par conséquent, certaines réponses pourraient être incomplètes. Le statut en matière d’indépendance précédent a été conservé pour référence.',
	override: 'Remplacer',
	grantAccess: 'Accorder l’accès',
	denyAccess: 'Refuser l’accès',
	overrideSmall: 'écrasement',
	grantAccessSmall: 'justifiant l’octroi de l’accès',
	denyAccessSmall: 'justifiant le refus de l’accès',
	status: 'État',
	undefined: 'Non défini',
	incomplete: 'Incomplet',
	noMattersIdentified: 'Aucune question n’a été relevée',
	matterIdentifiedPendingAction: 'Question relevée – Mesure à prendre',
	matterResolvedDeniedAccess: 'Question résolue – Accès refusé',
	matterResolvedGrantedAccess: 'Question résolue – Accès accordé',
	notApplicable: 'Sans objet',
	restored: 'Restauré',
	overridden: 'Remplacé',
	priorNoMattersIdentified: 'Auparavant ‒ Aucune question n’a été relevée.',
	priorMatterIdentifiedPendingAction: 'Auparavant ‒ Question relevée ‒ Mesure à prendre',
	priorMatterResolvedGrantedAccess: 'Auparavant ‒ Question résolue ‒ Accès autorisé',
	priorMatterResolvedDeniedAccess: 'Auparavant ‒ Question résolue ‒ Accès refusé',
	byOn: 'par {0} le',
	byLabel: 'par',
	onLabel: 'le',
	modifiedBy: 'Modifié par',
	reason: 'Raison',
	submit: 'Envoyer',
	submitTemplate: 'Envoyer le gabarit',
	independenceHoverText: 'Vous devez être l’associé responsable, l’associé responsable de la mission ou un associé délégué pour accorder ou refuser l’accès à cet utilisateur, ou pour le contourner.',
	enterRationaleText: 'Entrer la raison',
	enterRationalePlaceholderText: 'Saisir la raison',
	requiredRationaleText: 'Raison (requise)',
	rationaleTextRequired: 'Une raison est requise',

	sharedExternalWarning: 'Ce formulaire est partagé à partir du portail client d’EY Canvas et accessible par les membres de l’équipe externes. N’entrez que les réponses et les commentaires qui peuvent être partagés avec les membres de l’équipe externes.',
	independenceViewTemplateMessage: 'Ce formulaire sert de gabarit pour chaque demande d’informations relative à l’indépendance auprès des membres de l’équipe. <br /> Dans le formulaire de demande d’informations relative à l’indépendance, il y a plusieurs questions en lien avec les exigences applicables à l’entité auditée auxquelles les membres de l’équipe doivent répondre. Sélectionnez les réponses appropriées à chacune de ces questions. Les réponses seront synchronisées avec les demandes d’informations relatives à l’indépendance faites auprès de chaque membre de l’équipe. Si un membre de l’équipe a sélectionné une réponse différente, il devra confirmer à nouveau son indépendance pour pouvoir retourner travailler sur la mission. S’il ne retourne pas sur la mission, son statut d’indépendance et les réponses données précédemment seront conservés. <br /> Seuls les utilisateurs autorisés peuvent modifier le gabarit sur l’indépendance. Communiquez avec un administrateur de la mission. Toute modification apportée doit être envoyée, même si elle est annulée manuellement avant l’archivage.',

	/**
	 * FORM OBJECTS: SCOT-WCGW-CONTROL
	 */
	fo_instructionalText: 'Sélectionner les objets à consigner dans le formulaire Canvas',
	fsro_instructionalText: 'Gérer les objets associés à cette section',
	relObj_title_risk: 'Risques',
	relObj_title_riskType: 'Type de risque',
	fo_showOnlyRelated: 'Afficher seulement les objets associés',
	scotsCount: '{0} catégories importantes d’opérations',
	wcgwsCount: '{0} erreurs possibles',
	itsoCount: '{0} applications informatiques / société de services',
	controlsCount: '{0} Contrôles',
	itControlsCount: '{0} Contrôles informatiques',
	itGcCount: '{0} CGI',
	itSpCount: '{0} procédures de corroboration liées aux TI',
	itProcessesCount: '{0} Processus informatiques',
	risksCount: '{0} risques',
	accountsCount: '{0} comptes',

	stEntitiesCount: '{0} entités',

	componentsCount: '{0} composantes',
	view: 'Afficher',
	searchByScotName: 'Rechercher par nom de la catégorie importante d’opérations',
	searchByWcgwName: 'Chercher par nom d’erreur possible',
	searchByITSOAppName: 'Rechercher par nom de l’application informatique / société de services',
	searchByControlName: 'Rechercher par nom du contrôle',
	searchByItControlName: 'Rechercher par nom du contrôle informatique',
	searchByItProcessName: 'Rechercher par nom du processus informatique',
	searchByRiskName: 'Chercher par nom de risque',
	searchByAccountName: 'Rechercher par nom de compte',
	searchBySTEntityName: 'Rechercher par nom d’entité',
	searchByEstimateName: 'Rechercher par nom d’estimation',
	searchByComponentName: 'Rechercher par nom de composante',
	noScotsAvailable: 'Aucune catégorie importante d’opérations n’est disponible dans cette mission.',
	noRisksAvailable: 'Aucun risque n’est disponible pour cette mission.',
	noControlsAvailable: 'Aucun contrôle n’est disponible dans cette mission.',
	noItControlsAvailable: 'Aucun contrôle informatique n’est disponible dans cette mission.',
	noItProcessesAvailable: 'Aucun processus informatique n’est disponible dans cette mission.',
	noItApplicationsAvailable: 'Aucune application informatique n’est disponible dans cette mission.',
	noAccountsAvailableLabel: 'Aucun compte n’est disponible dans cette mission.',
	noObjectsRelatedToForm: 'Aucun objet associé à ce formulaire Canvas',
	noDocumentControlsAvailable: 'Aucun contrôle n’est associé à ce document.',
	noDocumentScotsAvailable: 'Aucune catégorie importante d’opérations n’est associée à ce document.',
	noSTEntitiesAvailable: 'Aucune entité n’est disponible dans cette mission.',
	noComponentsAvailable: 'Aucune composante n’est disponible dans cette mission.',
	editObjectDescription: 'Modifier l’association des objets à ce formulaire',
	editObjectsLabel: 'Modifier des objets',
	noITGCsOrITSPsHaveBeenIdentified: 'Aucun CGI ou aucune procédure de corroboration liée aux TI n’a été relevé',
	noItProcessIdentified: 'Aucun processus informatique n’a été identifié.',
	noControlsIdentified: 'Aucun contrôle n’a été relevé',
	noRelatedRisksIdentified: 'Aucun risque important ou risque de fraude associé n’a été relevé',
	noItApplicationsIdentified: 'Aucune application informatique n’a été relevée',
	noSCOTIdentified: 'Aucune catégorie d’opérations importante n’a été relevée',
	noWCGWIdentified: 'Aucune erreur possible n’a été relevée',
	maxLimitLabel: 'Le nombre maximal d’objets a été sélectionné.',
	minLimitLabel: 'Le nombre minimal d’objets a été sélectionné.',

	relatedITAppsTitle: 'Processus informatiques et applications informatiques associées',
	relatedWCGWTasksTitle: 'Erreurs possibles et tâches associées',
	noRelatedTasks: 'Aucune tâche associée',
	noRelatedWcgw: 'Aucune erreur possible associée',
	noRelatedControls: 'Aucun contrôle associé',
	controlRelatedRisksTitle: 'Contrôles et risques connexes',
	sCOTRelatedRisksTitle: 'Catégories importantes d’opérations et risques associés',
	scotRelatedItApp: 'Applications informatiques associées à la catégorie d’opérations importante',
	relatedItApps: 'Applications informatiques connexes',
	relatedRisksTitle: 'Risques associés',
	relatedItRisksItProcessesTitle: 'CGI et processus informatiques et risques liés aux TI associés',
	testingTitle: 'Tests',
	strategyTitle: 'Stratégie',
	yes: 'Oui',
	no: 'Non',
	noRelatedRisks: 'Aucun risque important ou risque de fraude associé',
	closeAllComments: 'Fermer tous les commentaires',
	closeComments: 'Fermer les commentaires',
	closeCommentsDescription: 'Tous les commentaires ouverts et réglés seront fermés. Voulez-vous vraiment fermer tous les commentaires pour ce/cette {0}?',
	addCanvasFormDigital: 'Numérique',
	addCanvasFormCore: 'De base',
	addCanvasFormNonComplex: 'Non complexe',
	addCanvasFormComplex: 'Complexe',
	addCanvasFormListed: 'Entité cotée',
	addCanvasFormGroupAudit: 'Audit de groupe',
	addCanvasFormPCAOBFS: 'PCAOB – ÉF',
	addCanvasFormPCAOBIA: 'PCAOB – AI',
	addCanvasFormStandards: 'Normes',
	addCanvasFormLanguage: 'Langue',
	addCanvasFormNoResultFound: 'Aucun résultat trouvé',
	addCanvasFormStandardsNotSelectedMessage: 'Le champ Norme est obligatoire.',
	addCanvasFormLanguageNotSelectedMessage: 'Le champ Langue est obligatoire',

	/* Confidentiality */
	confidentialityPlaceholder: 'Sélectionnez le niveau de confidentialité.',
	confidentiality: 'Confidentialité',
	confidentialityTitle: 'Indiquer le niveau de confidentialité',
	confidentialityText: 'Établir le niveau d’accès requis pour ouvrir ce document. Les niveaux d’accès sont établis par les administrateurs de missions à partir de la page Gérer l’équipe. Si le niveau de confidentialité a déjà été établi pour ce document, seules les personnes autorisées à ouvrir le document peuvent en changer le niveau de confidentialité.',
	confidentialityNotOpenable: 'Le document ne peut pas être ouvert, car vous n’avez pas les autorisations suffisantes pour cette mission. Les niveaux d’accès sont établis par les administrateurs de missions à partir de la page Gérer l’équipe.',
	confidentialityTargetNotOpenable: 'Les documents confidentiels ne peuvent être ouverts qu’à partir de la mission source.',
	backToCCP: 'Retourner au portail client d’EY Canvas',
	guidanceMessageBackToCCP: 'Après avoir rempli ce formulaire, retournez au portail client d’EY Canvas et envoyez la demande à EY.',
	noProfileInformationFound: 'Informations sur le profil introuvables. Actualisez la page et réessayez. Si le problème persiste, communiquer avec le Service de dépannage.',
	confirmUpdate: 'Confirmer la mise à jour',
	keepVersion: 'Conserver cette version',
	conflictDescription: 'Depuis qu’il a été ouvert, ce texte a été modifié par {0} le {1}. Sélectionnez la version à conserver.',
	currentConflictVersion: 'Version courante',
	serverConflictVersion: 'Version sur le serveur',
	conflictShowChanges: 'Afficher le suivi des modifications',
	sectionViewTrackChangesDropdownPlaceholder: 'Sélectionner une version',
	verifyingIndependence: 'Vérification du statut en matière d’indépendance en cours, veuillez patienter.',
	creatingIndependenceForm: 'Création du formulaire sur l’indépendance en cours.',
	meCallFailed: 'Échec de la récupération des informations de l’utilisateur. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	getUserByIdFailed: 'Échec de la récupération du statut en matière d’indépendance de l’utilisateur. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	independenceFormCreationFailed: 'Échec de la création du formulaire sur l’indépendance de l’utilisateur. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	gettingProfile: 'Chargement des informations sur le profil. Veuillez patienter.',
	invalidDocumentId: 'L’identifiant du document est invalide. Actualisez la page et réessayez. Si l’erreur persiste, communiquez avec le Service de dépannage.',
	returnToEditMode: 'Revenir au mode Modification',
	saveAndCloseButtonTitle: 'Enregistrer et fermer',
	formCreationFailed: 'Échec de la création du formulaire. Veuillez actualiser la page et réessayer. Si le problème persiste, communiquez avec le Service de dépannage.',

	/*Sign-off requirements*/
	signOffRequirements: 'Exigences d’approbation',
	signoffRequirementsModalTitle: 'Exigences d’approbation',
	signoffRequirementsModalDescription1: 'Ajuster les exigences d’approbation par un cadre ci-après pour ce document.',
	signoffRequirementsModalDescription2: 'Certaines exigences d’approbation ne peuvent pas être ajustées, car ce sont des exigences par défaut d’EY Canvas.',
	signoffRequirementsModalSaveLabel: 'Enregistrer',
	signoffRequirementsModalCancelLabel: 'Annuler',
	signoffRequirementsModalCloseLabel: 'Fermer',
	signoffRequirementsModalPICLabel: 'PIC',
	signoffRequirementsModalEQRLabel: 'RRQM',

	/*<Ares>*/
	/* View changes */
	viewChanges: 'Afficher les modifications',
	viewChangesModalTitle: 'Afficher les modifications',
	documentModificationAlert: 'La dernière modification de cette activité a été faite par',
	dismiss: 'Rejeter',

	/*Task List*/
	aresPageTitle: 'Outil FIT d’EY Canvas',
	aresPageSubtitle: 'Veuillez suivre les étapes ci-après et donner les informations requises relativement à votre audit.',
	summary: 'Sommaire',
	aresNoDocumentFound: 'Aucune autre information disponible pour l’activité sélectionnée',
	taskSubTitleNoValue: 'Aucune description disponible',
	mainActivities: 'Principales activités',
	unmarkComplete: 'Enlever la mention Terminée',
	markCompleteTitleTip: 'Inscrire comme terminée',
	disableMarkCompleteTitleTip: 'Assurez-vous que tous les documents associés sont approuvés par au moins un préparateur et un responsable de la revue pour inscrire cette activité comme terminée.',
	/*Activity Summary*/
	activitySummary: 'Synthèse de l’activité',
	selectedAnswers: 'Réponses sélectionnées',
	allAnswers: 'Toutes les réponses',
	incompleteResponses: 'Réponses incomplètes',
	previous: 'Précédent',
	next: 'Suivant',
	viewAsLabel: 'Afficher en tant que',
	rolePreparerLabel: 'Préparateur',
	roleDetailedReviewerLabel: 'Responsable de la revue détaillée',
	roleGeneralReviewerLabel: 'Responsable de la revue générale',
	roleEQRLabel: 'RRQM',
	/*Simple Helix*/
	helixPlaceholder: 'Guidance type 7 with guidanceurl is required for helix screenshots.',
	noNotesAvailable: 'Aucune marque de pointage créée',
	addScreenshot: 'Ajouter une capture d’écran',
	replaceScreenshot: 'Remplacer la capture d’écran',
	replaceFrameDescription: 'Passez en revue l’analyse ci-dessous et cliquez sur Remplacer pour remplacer la capture d’écran existante.',
	addNote: 'Ajouter une marque de pointage',
	notes: 'Marques de pointage ',
	noScreenshotsAvailable: 'Cliquer sur {viewDataAnalytic} pour commencer',
	viewDataAnalytic: 'Afficher l’analyse de données',
	/* Delete modal Helix screenshot*/
	modalTitle: 'Supprimer la capture d’écran',
	sureDeleteBeforeName: 'Voulez-vous supprimer la capture d’écran?',
	sureDeleteAfterName: 'Au moment de la suppression de la capture d’écran, toutes les marques de pointage qui y sont associées seront également supprimées et cette action ne peut pas être annulée.',

	/*uploadDocument body type */
	relateExistingDocuments: 'Associer les documents existants',
	fromEngagementOr: 'à partir de cette mission/d’autres missions ou',
	browse: 'naviguer',
	toUpload: 'pour télécharger',
	signoffs: 'Approbations',
	addDocument: 'Ajouter des documents',
	uploadDocument: 'Télécharger un document',
	relateDocument: 'Associer les documents existants',
	generateAccountRiskAssessmentPackage: 'Générer l’évaluation du risque au niveau du compte pour l’audit de groupe',
	relateDocumentsToBodyAresTitle: 'Associer les documents',
	discardLabel: 'Ignorer',
	uploadDocumentLabel: 'Télécharger le document',
	confirm: 'Confirmer',
	duplicateDocumentHeader: 'Un ou plusieurs documents ayant le même nom existent déjà dans cette mission (il s’agit d’éléments probants ou de fichiers temporaires).',
	duplicateDocumentInstruction: 'Sélectionner «Écraser» pour télécharger le document et remplacer le fichier existant ou sur «Ignorer» pour annuler. Si le fichier existant se trouve dans Éléments probants, le document sera alors téléchargé dans Éléments probants. Si le fichier existant se trouve dans Fichiers temporaires, le document sera alors téléchargé dans Fichiers temporaires.',
	maxUploadFilesError: 'Le système peut téléverser un nombre maximum de 10 documents en même temps.',
	/*</Ares>*/
	noTaskRelatedToThisDocument: 'Aucune tâche associée à ce document',
	uncheckTrackChangesToSave: 'Unselect the track changes option to save',
	reviewRoleCloseCommentsTitle: 'Commentaires non résolus',
	reviewRoleCloseCommentsDesc: 'Des commentaires non résolus doivent encore être traités. Utilisez le filtre pour repérer facilement les commentaires non résolus.',

	/*Document Upload - PIC/EQR Required Body type*/
	requirementDetails: 'Détails relatifs aux exigences',

	//Risk Factors
	riskFactor: 'Situation et événement pertinents / risque d’anomalies',
	manageRisk: 'Gérer les risques',
	noRiskFactors: 'Aucun événement ou aucune situation / aucun risque d’anomalies pertinent n’a été relevé.',
	relateRiskFactorsToRisks: 'Déterminer l’importance des événements et des conditions',
	riskType: 'Type de risque',
	relateToRisk: 'Associer au risque',
	noRisksIdentified: 'Aucun risque identifié',
	notDefined: 'Non défini',
	selectValidRiskType: 'Veuillez sélectionner un type de risque valide.',
	newRisk: 'Ajouter un nouveau risque',
	notAROMM: 'Il ne s’agit pas d’un risque d’anomalies significatives',
	describeRationale: 'Consigner les raisons',
	noRisksIdentifiedForTheSpecificRiskType: 'Aucun {0} n’a été identifié.',
	addAnAccount: 'Associer un autre document',
	selectAnAccount: 'Sélectionner un compte',
	noAccountsHaveBeenIdentified: 'Aucun compte n’a été identifié.',
	accountSelected: 'Compte',
	statementType: 'Type d’état',
	selectAssertions: 'Sélectionner une ou plusieurs assertions',
	noAssertionsIdentifiedForAccount: 'Aucune assertion n’a été relevée pour ce compte',
	relatedAssertions: 'Assertions connexes',
	editAccount: 'Modifier le compte et l’information à fournir',
	addNewDescription: 'Ajouter une nouvelle description',
	editRiskFactorDescription: 'Modifier la description',
	enterRiskFactorDescription: 'Entrer la description de la situation et de l’événement pertinents / du risque d’anomalies',
	riskFactorDescriptionRequired: 'Description de la situation et de l’événement pertinents / du risque d’anomalies requise',
	riskFactorDescription: 'Description de la situation et de l’événement pertinents / du risque d’anomalies',
	createNewAccount: 'Créer un nouveau compte',
	createAccountLabel: 'a bien créé le compte {0}',
	updateAccountLabel: 'a bien sauvegardé les modifications apportées au compte {0}',
	deleteAccountLabel: 'a bien été supprimé',
	significanceLabel: 'Importance',
	provideRationale: 'Veuillez fournir une justification pour pouvoir sauvegarder votre sélection',
	clearRiskSignificance: 'Effacer l’importance et la description du risque',
	clearSignificance: 'Effacer l’importance et la description',

	// Account Summary
	unavailable: 'Non disponible',
	notAvailable: 'Non disponible',
	fraudRisk: 'Risque de fraude',
	significantRisk: 'Risque important',
	significantRiskIndicator: 'RI',
	fraudRiskIndicator: 'RF',
	inherentRisk: 'Risque d’anomalies significatives',
	inherentRiskIndicator: 'Risque d’anomalies significatives',
	prioryearbalance: 'Solde de la période précédente : ',
	accounts: 'Comptes',
	accountsOther: 'Compte − Autre',
	accountsSignDis: 'Information à fournir importante',
	xMateriality: 'Multiples du SSI',
	xTEMateriality: 'x EA',
	estimateAssociated: 'Estimation associée',
	designation: 'Désignation',
	noAccountshasbeenIdentified: 'Aucun compte n’a été identifié.',
	noAccountsOtherhasbeenIdentified: 'Aucun autre compte identifié.',
	noAccountsSigfhasbeenIdentified: 'Aucune information à fournir importante identifiée.',
	addOtherAccounts: 'Ajouter un compte − Autre',
	addSignificantDisclosure: 'Ajouter une information à fournir importante',
	pydesignation: 'Désignation antérieure : ',
	notapplicable: 'S.O.',
	noApplicable: 'Sans objet',
	changeDesignationMessage: 'Vous vous apprêtez à modifier la désignation du compte.',
	changeDesignationTitle: 'Modifier la désignation du compte',
	continue: 'Continuer',
	currentYearBalance: 'Période considérée',
	currentPeriodBalance: 'Période considérée',
	priorYear: 'Exercice précédent',
	priorYearDesignation: 'Désignation de la période précédente',
	priorYearEstimation: 'Estimation de la période précédente',
	priorPeriodChange: '% de variation',
	analytics: 'Analyse de données',
	notROMMHeader: 'Il ne s’agit pas d’un risque d’anomalies significatives',
	manageEyCanvasAccounts: 'Gérer les comptes EY Canvas',
	manageAccountMapping: 'Gérer le mappage des comptes',
	manageAccountMappingCloseButton: 'Utiliser le bouton au bas de la page pour fermer.',
	manageAccountMappingToasterMessage: 'Impossible de se connecter à EY Helix. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	inherentRiskTypeChangeMessage: 'Si l’assertion à l’égard du compte passe de pertinente à non pertinente, certaines associations dans Canvas, y compris les erreurs possibles par rapport à l’assertion, seront retirées et les PCB associées seront converties en APC. Cliquez sur « Confirmer » pour continuer et procéder à la conversion. Cliquez sur « Annuler » pour revenir en arrière sans faire les changements.',

	analyticsIconDisabled: 'EY Helix n’est pas activé pour votre mission.',
	//Estimate
	estimatesTitle: 'Estimations',
	relatedAccount: 'Comptes connexes',
	relatedAccounts: 'Comptes connexes',
	currentBalance: 'Solde actuel',
	priorBalance: 'Solde précédent',
	unrelateEstimates: 'compte auquel aucune estimation n’est associée',
	manageEstimates: 'Gérer les estimations',
	noEstimatesCreated: 'Aucune estimation n’a été créée.',
	createEstimates: 'Créer une estimation',
	estimateStarted: 'pour commencer',
	createEstimateDocument: 'Créer la documentation sur l’estimation',
	noEstimatesFound: 'Aucune estimation n’a été identifiée.',
	relateEstimateLink: 'Lier des estimations',

	//Journal Source
	noJournalEntrySourcesAreAvailable: 'Aucune source d’écriture de journal n’est disponible',
	jeSourceName: 'Source d’écriture de journal',
	jeSourceNameTooltip: 'Source d’écriture de journal',
	changeInUse: 'Modification applicable',
	grossValue: 'Valeur brute des opérations associées',
	relatedTransactions: 'Nombre d’opérations associées',
	relevantTransactions: 'Pertinent pour les catégories d’opérations importantes',
	expandAll: 'Tout développer',
	collapseAll: 'Tout réduire',
	descriptionLabel: 'Fournir une brève description de l’objet de cette source',
	jeSourceTypesLabel: 'Les écritures de journal sont-elles enregistrées au moyen de cette source, automatisées ou manuelles?',
	journalEntries: 'Cette source est-elle utilisée pour enregistrer des écritures de journal non standard (c.-à-d., des opérations ou des ajustements non récurrents ou inhabituels)?',
	identifySCOTsLabel: 'Identifiez les catégories d’opérations importantes associées à la source d’écriture de journal (Sélectionnez toutes les réponses qui s’appliquent.)',
	systemGeneratedLabel: 'Automatisée',
	manualLabel: 'Manuelle',
	bothLabel: 'Les deux',
	accountEstimateLabel: 'Estimation',
	addSCOTLabel: 'Ajouter une catégorie d’opérations importante',
	newSCOTLabel: 'Nouvelle catégorie d’opérations importante',
	addSCOTModalDescription: 'Vous pouvez créer des catégories d’opérations importantes. Les modifications seront appliquées après l’enregistrement.',
	scotnameRequired: 'Le nom de la catégorie d’opérations importante est requis.',
	scotCategoryRequired: 'Le type de catégorie d’opérations importante est requis.',
	estimateRequired: 'Une estimation est requise.',

	jeSourcesLabel: 'Sources d’écritures de journal',
	jeSourcesToSCOTs: 'Sources d’écritures de journal associées aux catégories d’opérations importantes',
	scotsToSignificantAccounts: 'Catégories d’opérations importantes associées aux comptes importants',

	//Modal common labels.
	modalCloseTitle: 'Fermer',
	modalConfirmButton: 'Enregistrer les modifications',
	modalCancelTitle: 'Annuler',
	modalSave: 'Enregistrer',
	modalSaveAndClose: 'Enregistrer et fermer',
	modalSaveAndAdd: 'Enregistrer et ajouter',
	modalSaveAndCreateAnother: 'Enregistrer et créer une autre estimation',

	//Add & Manage Risks
	addNewRiskModalTitle: 'Ajouter un nouveau risque',
	manageRisksListModalTitle: 'Gérer les risques',
	riskInfoMessage: 'Les modifications seront appliquées après l’enregistrement.',
	risksListInstructionalText: 'Vous pouvez modifier ou supprimer les risques existants. Vous pouvez aussi en ajouter un nouveau, au besoin.',
	risksListEmptyArray: 'Aucun risque disponible. Veuillez ajouter un nouveau risque pour commencer.',
	addNewRiskButtonLabel: 'Ajouter un nouveau risque',
	labelRiskName: 'Nom du risque',
	riskDescriptionLabel: 'Description du risque',
	selectRiskType: 'Type de risque',
	requiredRiskName: 'Le nom du risque est requis',
	requiredRiskType: 'Le type de risque est requis',
	deleteRiskTrashLabel: 'Supprimer le risque',
	undoDeleteRiskTrashLabel: 'Annuler la suppression',
	notARommLabel: 'Aucun risque d’anomalies significatives',
	identifiedRiskFactors: 'Événements/situations/risques d’anomalies, risques d’anomalies significatives, risques importants et risques de fraudes identifiés.',
	noneIdentified: 'Aucun identifié',
	countUnassociatedRisk: 'Les événements/situations/risques d’anomalies sont non associés/ne portent pas la mention « Il ne s’agit pas d’un risque d’anomalies significatives / risque d’anomalies significatives».',

	// Bar Chart / Account Summary
	accountsTotal: 'Total de {0} compte(s)',
	accountSummary: 'Synthèse des comptes',
	allAccounts: 'Tous les comptes',
	significantAccountsBarChart: 'Comptes importants',
	limitedAccounts: 'Comptes présentant un risque limité',
	insignificantAccounts: 'Comptes négligeables',
	noAccountsHasBeenIdentifiedBarChart: 'Aucun {0} n’a été identifié.',
	selectedTotalAccountsCounter: '{0}/{1} comptes',
	identifyInsignificantAccounts: 'Identifier les comptes négligeables',
	identifySignificantAccounts: 'Identifier les comptes importants',
	identifyLimitedAccounts: 'Identifier les comptes présentant un risque limité',
	preInsigniAccounts: 'Le solde de ce compte auparavant considéré comme négligeable est supérieur à l’EA pour la période considérée.',
	nonDesignatedInsignificant: 'Ce compte ne peut pas être désigné comme étant négligeable. Cliquez sur la flèche du menu déroulant pour modifier la désignation du compte.',
	tolerableError: 'Le seuil d’erreur acceptable n’est pas disponible. Veuillez mettre à jour le seuil de signification et réessayer.',
	documentContainerLabel: 'Document',
	clickcreateformtogenerate: 'Cliquer sur {0} pour générer la documentation sur le compte présentant un risque limité.',
	createform: 'Créer un formulaire',
	createLimitedRiskAccountDocumentation: 'Créer la documentation sur le compte présentant un risque limité',
	limitedAccountDocumentName: 'Documentation sur le compte présentant un risque limité',

	//Modal Confirm Switch account
	modalSwitchTitle: 'Changements non enregistrés',
	modalConfirmSwitch: 'Confirmer',
	modalConfirmSwitchDescription: 'Les changements ne sont pas enregistrés et seront perdus si vous choisissez de continuer. Voulez-vous continuer?',

	//Modal Edit Account
	manageAccountsModalTitle: 'Gérer les comptes EY Canvas',
	editAccountLinkLabel: 'Modifier le compte',
	editAccountInstructionalText: 'Vous pouvez modifier ou supprimer des comptes EY Canvas existants, ou en créer de nouveaux. Les modifications seront appliquées après l’enregistrement.',
	selectAnAccountLabel: 'Sélectionner un compte',
	accountNameLabel: 'Nom du compte',
	accountLabel: 'Compte',
	accountDesignationLabel: 'Désignation du compte',
	accountStatementTypeLabel: 'Type d’état',
	accountRelatedAssertionsLabel: 'Assertions connexes',
	accountHasEstimateLabel: 'L’estimation a-t-elle une incidence sur ce compte?',
	requiredAccountName: 'Le nom du compte est requis',
	requiredAccountDesignation: 'La désignation du compte est requise',
	requiredStatementType: 'Le type d’état est requis',
	requiredRelatedAssertions: 'Sélectionner une assertion',
	pspIndexDropdownLabel: 'Sélectionner la référence PCB',
	removePSPIndexLabel: 'Supprimer la référence PCB',
	addPSPIndexLink: 'Ajouter une référence PCB',
	pspIsRequired: 'La référence PCB est requise',

	//Delete account modal
	deleteAccount: 'Supprimer le compte',
	deleteAccountModalMessage: 'Voulez-vous vraiment supprimer le compte sélectionné?',
	cannotBeUndone: 'Cette action ne peut pas être annulée.',
	guidedWorkflow: 'Outil FIT d’EY Canvas',
	scotSummary: 'Synthèse des catégories importantes d’opérations',
	scopeAndStrategy: 'Étendue et stratégie',
	ToggleSwitch: {
		Inquire: 'Effectuer une demande d’informations',
		Completed: 'Terminée',
		isOn: 'Oui',
		isOff: 'Non'
	},
	navExecution: 'Exécution',
	navCanvasEconomics: 'Données financières EY Canvas',
	navOversight: 'Surveillance d’EY Canvas',
	navConclusion: 'Conclusion',
	navTeamMemberIndependence: 'Indépendance des membres de l’équipe',
	navGroupAudit: 'Direction du groupe',
	navGroupActivityFeed: 'Flux d’activité du groupe',
	navPrimaryStatus: 'État de l’équipe principale',
	navComponentStatus: 'État de l’équipe affectée à l’audit de la composante',
	navGroupStatus: 'État de l’audit de groupe',
	navEngagementManagement: 'Gestion de la mission',
	navProfile: 'Profil',
	navItSummary: 'Synthèse TI',
	nav440GL: 'Des changements ont été apportés après la date du rapport.',
	navGroupStructureSummary: 'Structure de l’audit de groupe',
	navGroupInstructionSummary: 'Instructions pour l’audit de groupe',
	navGroupInvolvement: 'Participation à l’audit de groupe',
	navNotApplicable: 'Sans objet',
	cropScreenshot: 'Rogner une capture d’écran',
	cropScreenshotModalDescription: 'Rognez la capture d’écran pour n’inclure que les parties pertinentes. Le rognage supprimera les annotations existantes, les marques de pointage seront conservées et pourront être annotées à nouveau. Le rognage ne peut pas être annulé.',
	crop: 'Rogner',
	replace: 'Remplacer',
	nameTheScreenshot: 'Nom de la capture d’écran',
	nameLabel: 'Nommer',
	takeScreenshot: 'Ajouter une capture d’écran',
	measurementBasis: 'Base d’évaluation',
	MeasurementBasisMessage: 'Selon le mappage des données sous-jacentes dans EY Helix, la base de mesure sélectionnée ne semble pas se trouver à la position attendue (p. ex., bénéfice avant impôts dans un poste de débit). Vérifier ce qui suit : ',
	measurementBasisProjectMappedCorrectly: 'Les données dans le projet EY Helix sont correctement mappées.',
	measurementBasisAppropriateValue: 'Une base de mesure différente serait peut-être appropriée.',
	measurementBasisAdjustValue: 'Il pourrait être approprié d’apporter des ajustements à la valeur de la base de mesure comme suit : ',
	basisValueFromHelix: 'Valeur de la balance des comptes',
	rationaleDeterminationLabel: 'Justification de la manière dont ce montant a été déterminé',
	loginInstructions: 'veuillez vous connecter et suivre les indications pour créer un compte.',
	gotoText: 'Allez à,',
	asOfDate: 'En date du',
	annualizedBasisValue: 'Valeur de base annualisée',
	basisValue: 'Valeur de base',
	isAnnualizedAmountRepresentative: 'La valeur de base annualisée est-elle représentative du montant qui devrait être présenté à la fin de la période?',
	isAnnualizedAmountRepresentativeForAssetsOrEquity: 'La valeur est-elle représentative du montant qui devrait être présenté à la fin de la période visée par l’audit?',

	enterExpectedFinancialPerioadAmount: 'Veuillez saisir le montant prévu à la fin de la période visée par l’audit',
	enterRationaleAmountDetermined: 'Veuillez expliquer comment ce montant a été établi.',
	printNotAvailable: '{0} est vide et, par conséquent, aucune information ne peut être affichée',
	canvasFormPrintNotAvailable: 'L’impression de formulaires Canvas n’est pas disponible pour l’instant. Veuillez réessayer ou communiquez avec le Service de dépannage si le problème persiste.',
	saving: 'Enregistrement en cours…',
	removing: 'Suppression...',
	activitySummaryTitle: 'Synthèse de l’activité',
	currentLabel: 'Période considérée',
	PMLabel: 'Seuil de signification initial',
	planningMaterialityLabel: 'Seuil de signification initial',
	TELabel: 'Erreur acceptable',
	tolerableErrorLabel: 'Erreur acceptable',
	SADLabel: 'Sommaire des anomalies',
	SADNominalAmountLabel: 'Seuil minimal de report des anomalies',
	PriorLabel: 'Période précédente',
	editRichText: 'Modifier le texte',
	noTypeTwoResponseAvailable: 'Aucune réponse disponible. <br /> Cliquer {clickHere} pour répondre.',
	clickHere: 'ici',
	helixNavigationTitle: 'Veuillez accéder à la page Configuration d’EY Helix pour associer un projet EY Helix ou en configurer un.',
	helixNavigationLink: 'Accéder à EY Helix',
	measurementBasisValue: 'Valeur de base d’évaluation',
	inlineSaveUnsavedChanges: 'Des changements n’ont pas été enregistrés, voulez-vous continuer?',
	rationaleIncomplete: 'Justification incomplète',
	projectMismatchDisplayMessageOnDataImport: 'Votre projet EY Helix principal a été modifié. Veuillez confirmer les paramètres EY Helix et importer les données du nouveau projet.',
	importSuccess: 'Les données d’EY Helix ont bien été importées.',
	importHelix: 'Cliquer sur Importer pour importer les données du grand livre général avec EY Helix.',
	importLabel: 'Importer',
	reImportLabel: 'Réimporter',
	lastImportedBy: 'Dernière importation par',
	lastImportedOn: 'Date de la dernière importation',
	dataImportedFromProjec: 'Données importées du projet',
	reImportConfirmationTitle: 'Réimportation de données d’EY Helix',
	reImportConfirmationMessagePart1: 'La réimportation des données à partir d’EY Helix modifiera les données existantes ou ajoutera de nouvelles données dans les activités pertinentes. Cette action ne peut pas être annulée.',
	reImportConfirmationMessagePart2: 'Pour plus d’information et pour obtenir un résumé de l’incidence du processus de réimportation des données sur l’outil FIT d’EY Canvas, veuillez consulter EY Atlas.',
	defineRisksTitle: 'Définir les risques',
	assessAndDefineRisksTitle: 'Évaluer et définir les risques',
	identifiedRiskFactorsTitle: 'Événements/conditons et risques associés identifiés : ',
	descriptionIncompleteLabel: 'Description incomplète',
	noRiskHasBeenRelatedMsg: 'Aucun risque n’a été associé',
	rationaleIncompleteMsg: 'Justification incomplète',
	loading: 'Chargement...',
	importInProgressLabel: 'Importation en cours. Cette opération peut prendre plusieurs minutes. Veuillez actualiser la page pour afficher l’état mis à jour.',
	importInProgressMessage: 'Importation des données Helix en cours. Veuillez actualiser la page pour en afficher la progression.',
	importHelixProjectError: 'Une erreur s’est produite au moment de l’importation des données d’EY Helix. Vérifiez que l’état du projet EY Helix indique Analyse disponible, actualisez la page et cliquez à nouveau sur Importer ou sur Réimporter. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	importDeletionConfirmationMsg: 'Voulez-vous vraiment supprimer l’importation des données d’EY Helix? Cette action supprimera les données existantes dans les activités pertinentes et ne pourra pas être annulée.',
	deletePreviousImport: 'Supprimer l’importation des données d’EY Helix',
	//Assess Risks - Summary Page
	assessRisksAccordionLabel: 'Associer des risques',
	assessRisksNoItemsFound: 'Aucun risque n’a été relevé',
	assessRiskAccountsAndRelatedAssertions: 'Comptes et assertions connexes',
	assessRiskNoAccountsLinked: 'Aucun compte associé',
	accountRiskAssessmentSummary: 'Comptes et informations à fournir',
	// Flow chart
	flowchartTitle: 'Organigramme',
	launchFlowchart: 'Lancer l’organigramme',
	clickherelink: 'Cliquez ici',
	orderToCashPlacement: 'Mise en place du processus de la commande à l’encaissement',
	orderToCashPlacementMessage: 'Le Service des programmes négocie les contrats avec les nouveaux clients ainsi que le renouvellement et/ou la modification des contrats avec les clients existants. Les détails suivants figurent dans le contrat : prix, modalité et garanties.',
	saveFlowChart: 'Enregistrer',
	newstep: 'Nouvelle étape',
	zoomIn: 'Effectuer un zoom avant',
	zoomOut: 'Effectuer un zoom arrière',
	resetZoom: 'Réinitialiser le zoom',
	toogleInteractivity: 'Basculer en mode interactivité',
	fitView: 'Affichage FIT',
	numberOfSteps: 'Nombre d’étapes',
	flowchartSuccessfullyCreated: 'Organigramme créé avec succès.',
	flowchartLinkedEvidenceMessage: 'Cet organigramme a été créé dans une autre mission. L’accès à l’organigramme dans cette mission sera supprimé au moment de la dissociation des éléments probants.',
	flowchartSmartEvidenceSourceIdNullMessage: 'Aucune catégorie d’opérations importante disponible.',
	noTaskDocumentAvailableFlowchart: 'Ce document est un fichier temporaire. Veuillez l’associer à une tâche comme élément probant pour accéder aux détails de l’organigramme.',
	// Control Attributes
	controlAttributes: 'Attributs des contrôles',
	noControlAttributes: 'Aucun contrôle disponible',
	flowchartStepMoremenu: 'Menu Plus',
	createControlAttributes: 'Aucun attribut des contrôles disponible.<br />Cliquer {clickHere} pour créer un nouvel attribut des contrôles.',
	createNewControlAttribute: 'Nouvel attribut',
	editControlAttribute: 'Modifier l’attribut',
	createControlAtttributeInstructions: "Entrer les détails relatifs à l’attribut ci-dessous et sélectionner <b>\'Enregistrer et fermer\'</b> pour terminer. Pour créer un autre attribut, sélectionner <b>\'Enregistrer et créer un autre attribut\'.</b> Les attributs seront classés en fonction de leur référence. ",
	editControlAttributeInstructions: "Modifier les détails relatifs à l’attribut ci-dessous et sélectionner <b>\'Enregistrer\'</b> pour terminer. Les attributs seront classés en fonction de leur référence. ",
	editAttributeButtonLabel: 'Modifier l’attribut',
	deleteAttributeButtonLabel: 'Supprimer l’attribut',
	deleteControlAttributeInstructions: 'Voulez-vous vraiment supprimer l’attribut sélectionné? Cette action ne peut pas être annulée.',
	// Control Attributes Form
	requiredAttributeIndexLabel: 'Référence de l’attribut (obligatoire)',
	requiredAttributeDescriptionLabel: 'Description de l’attribut (obligatoire)',
	errorMessageAttributeIndexRequired: 'Obligatoire',
	errorMessageAttributeDescriptionRequired: 'Obligatoire',
	errorMessageAttributeDescriptionMaxLength: 'La réponse contient {#} caractères, ce qui dépasse la longueur maximale de {##} caractères. Veuillez réduire la description ou ajuster le formatage puis réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	errorMessageAttributeTestingTypesRequired: 'Obligatoire',
	proceduresLabel: 'Procédures à mettre en œuvre',
	modalRequiredProceduresLabel: 'Procédures à mettre en œuvre (obligatoire)',
	attributeTestingTypesLabel: {
		inquiry: 'Demande d’informations',
		observation: 'Observation physique',
		inspection: 'Inspection',
		reperformance: 'Réexécution / contrôle arithmétique'
	},

	/*CRA Badge*/
	ir: 'IR',
	cr: 'CR',
	cra: 'CRA',
	incompleteCra: 'EGR incomplète',
	incomplete: 'Incomplet',

	//Progess bar labels
	savingProgress: 'Enregistrement en cours…',
	discardChangesLabel: 'Ignorer les modifications',
	removeFromBody: 'Supprimer du corps du texte',
	uploading: 'Téléversement en cours...',
	uploadComplete: 'Téléversement terminé',
	downloadComplete: 'Téléchargement terminé',
	processing: 'Traitement en cours...',

	/* ISA BODIES */
	/* Common */
	deleteEntityConfirmation: 'Voulez-vous vraiment supprimer l’action <b>{0}</b>? Cette action ne peut pas être annulée.',
	/* ITAPP-SCOT */
	searchScot: 'Rechercher une catégorie d’opérations importante',
	addITApp: 'Ajouter une application informatique',
	relatedItApp: 'Applications informatiques connexes',
	itApplicationHeader: 'Applications informatiques',
	scotNoDataPlaceholder: 'Aucune information disponible',
	noScotsOrControlsPlaceholder: 'Aucune catégorie d’opérations importante associée ou aucun contrôle associé; {noScotsOrControlsPlaceholderEditAssoc}.',
	noScotsOrControlsPlaceholderEditAssoc: 'modifier les éléments associés',
	noScotsOrControlsPlaceholderTarget: 'Aucune catégorie d’opérations importante associée ou aucun contrôle associé.',
	scotHeader: 'Catégories d’opérations importantes',
	controlsHeader: 'Contrôles',
	controlsApplicationHeader: 'Application',
	controlsITDMHeader: 'Contrôles manuels liés aux TI',
	itAppScotNoDataPlaceholderLabel: 'Aucune application informatique n’a été ajoutée.',
	itAppScotNoDataPlaceholder: 'Aucune application informatique n’a été ajoutée.<br /> Pour commencer, cliquer sur {itAppScotNoDataPlaceholderAddItApp}',
	itAppScotNoDataPlaceholderAddItApp: 'Ajouter une application informatique',
	editItAppOption: 'Modifier l’application informatique',
	removeItAppOption: 'Supprimer l’application informatique',
	viewItAppOption: 'Afficher l’application informatique',
	editScotsISA: 'Modifier la catégorie d’opérations importante',
	viewScotISA: 'Afficher la catégorie d’opérations importante',
	viewControlISA: 'Afficher les contrôles',
	scotAndRelatedControls: 'Catégories d’opérations importantes et contrôles',
	otherControls: 'Autres contrôles',
	controlTypeLabel: 'Type de contrôle',

	/*SCOT-ITAPP*/
	addOrRelateItAppPlaceholder: '{identifyRelatedItApps} ou indiquer que {documentScotHasNoItApps}.',
	identifyRelatedItApps: 'Identifier les applications informatiques associées',
	documentScotHasNoItApps: 'Aucune application informatique n’est associée à la catégorie d’opérations importante',
	correctScotDocumentationPlaceholder: 'La catégorie d’opérations importante est désignée comme n’étant pas appuyée par une application informatique. Un ou des contrôles des applications et/ou un ou des contrôles manuels liés aux TI ont été associés à cette catégorie d’opérations importante, {correctScotDocumentation}.',
	correctScotDocumentation: 'revoir la désignation selon laquelle cette catégorie d’opérations importante n’est soutenue par aucune application informatique',
	controlsWithoutRelatedItApps: 'Contrôles non associés à des applications informatiques',
	controlsWithRelatedItApps: 'Contrôles avec applications informatiques associées',

	/*AddEditITApplication */
	saveAndCreateNewButtonTitle: 'Enregistrer et créer un nouvel élément',
	instructionalMessage: "Ajouter une application informatique et sélectionner les catégories d’opérations importantes connexes. Associer les contrôles se rapportant à l’application informatique, s'il y a lieu. ",
	ITApplicationNamePlaceholder: 'Nom de l’application informatique (requis)',
	scotDropdownPlaceholderText: 'Sélectionner les catégories d’opérations importantes devant être associées à l’application informatique',
	selectScotPlaceholderText: 'Sélectionner les catégories d’opérations importantes devant être associées à l’application informatique',
	selectControlPlaceholderText: 'Sélectionner les contrôles devant être associés à l’application informatique',
	noRelatedScotsPlaceholderText: 'Aucune catégorie d’opérations importante associée',
	noRelatedControlsPlaceholderText: 'Aucun contrôle associé',
	CreateSOModelTitle: 'Ajouter la société de services',
	CreateSOInstructionalMessage: "Entrez les détails de la nouvelle société de services ci-dessous et sélectionnez'<b>{0}</b>’ pour terminer. Pour créer une autre société de services, sélectionnez'<b>{1}</b>'.`, //’Créez une nouvelle société de services et associez les catégories d’opérations importantes et les contrôles qui y sont liés. ",
	saveAndCloseLabel: 'Enregistrer et fermer',
	saveAndCreateLabel: 'Enregistrer et créer',
	SONamePlaceholder: 'Nom de la société de services (requis)',
	SOSelectScotPlaceholderText: 'Sélectionner les catégories d’opérations importantes associées à la société de services',
	SOSelectControlPlaceholderText: 'Sélectionner les contrôles associés à la société de services',
	CreatedSOLabel: 'Société de services ajoutée',
	createdITAppLabel: 'application informatique ajoutée',
	searchNoResultFoundText: 'Aucun résultat trouvé',
	searchNoResultsFoundText: 'Aucun résultat trouvé',
	iTApplicationNameRequired: 'Le nom de l’application informatique est requis',
	soNameRequired: 'Le nom de la société de services est requis',
	editITAppDesctiptionLabel: 'Modifier l’application informatique ainsi que les catégories d’opérations importantes et les contrôles qui y sont associés',
	editSODescriptionLabel: 'Modifiez les détails relatifs à la société de services ci-dessous et sélectionnez <b>’Enregistrer’</b> pour terminer.',
	viewITApplication: 'Afficher l’application informatique',
	itApplicationName: 'Nom de l’application informatique',
	serviceOrganizationName: 'Nom de la société de services',
	newItApplication: 'Nouvelle application informatique',

	/*Add/Edit ITProcess*/
	itProcessName: 'Nom du processus informatique',
	addItProcessDescription: 'Créer un processus informatique',
	addItProcess: 'Ajouter un processus informatique',
	itProcessNameRequired: 'Le nom du processus informatique est requis',
	editItProcess: 'Modifier le processus informatique',
	editItProcessDescription: 'Modifier le processus informatique',
	viewItProcess: 'Afficher le processus informatique',
	taskTitle: 'Comprendre et consigner le processus informatique : {0}',
	taskDescription: 'Consigner notre compréhension du processus informatique. Joindre le formulaire pertinent à l’appui de notre compréhension du processus informatique.<br />Lorsque des CGI sont affichés dans la section Attributs de la tâche, exécuter des procédures de test de cheminement pour confirmer notre compréhension des CGI et en évaluer la conception et la mise en place. Joindre les éléments probants à l’appui de nos procédures.',
	newItProcess: 'Nouveau processus informatique',
	noITProcessesFound: 'Aucun processus informatique n’a été identifié.',

	/* IT Process - Task */
	itProcessSearchPlaceholder: 'Rechercher un processus informatique',
	itProcessHeader: 'Processus informatique',
	itProcessTasksHeader: 'Tâches',
	itProcessAddUPD: 'Ajouter la tâche Comprendre le processus',
	itProcessEditItProcess: 'Modifier le processus informatique',
	itProcessRelateUDP: 'Associer la tâche Comprendre le processus',
	itProcessViewProcess: 'Afficher le processus informatique',
	itProcessNoDataPlaceholder: 'Aucun processus informatique n’a été ajouté.<br /> Pour commencer, cliquer sur {addItProcess}.',
	itProcessSourceInstructionalText: 'Au moins une tâche Comprendre le processus doit être associée au processus informatique. {itProcessSourceInstructionalTextCreateUdp} ou {itProcessSourceInstructionalTextRelateUdp}',
	itProcessSourceInstructionalTextCreateUdp: 'Créer une nouvelle tâche Comprendre le processus',
	itProcessSourceInstructionalTextRelateUdp: 'associer une tâche Comprendre le processus existante.',
	itProcessTargetInstructionalText: 'Aucune tâche Comprendre le processus n’est associée au processus informatique.',

	/* IT APP IT PROCESSES RELATION*/
	itApplicationHeaderRelate: 'Applications informatiques',
	itProcessesHeaderRelate: 'Processus informatiques',
	itAppNoDataPlaceHolderLabel: 'Aucune application informatique n’a été relevée.',
	itAppNoDataPlaceHolder: 'Aucune application informatique n’a été relevée.<br />{identifyItApp}',
	identifyItApp: 'Identifier une application informatique.',
	itProcessNoDataPlaceHolderRelationLabel: 'Aucun processus informatique n’a été relevé.',
	itProcessNoDataPlaceHolderRelation: 'Aucun processus informatique n’a été identifié.<br />{identifyItProcess}',
	identifyItProcess: 'Identifier un processus informatique.',
	editItApp: 'Modifier l’application informatique',
	deleteItApp: 'Supprimer l’application informatique',
	drag: 'Glisser le processus informatique vers les applications informatiques associées',
	// editItProcess: 'Edit IT process',
	deleteItProcess: 'Supprimer le processus informatique',
	unassociatedProcess: '{0} processus non associés',
	unassociatedItApplications: '{0} applications informatiques non associées',
	showOnlyUnrelated: 'Afficher seulement les éléments non associés - {0}',
	searchItProcess: 'Rechercher un processus informatique',
	searchItApplication: 'Rechercher une application informatique',
	itProcessesLabel: 'Processus informatiques',
	itApplicationsLabel: 'Applications informatiques',
	showAllItAplications: 'Afficher toutes les applications informatiques',
	showAllItProcesses: 'Afficher tous les processus informatiques',
	relatedToLabel: "Liste de l’ensemble des <span class='child-entity-count'>{count}</span> <span class='child-entity-name'>{child}</span> associées à <span class='parent-entity-object-name'>{parent}</span> ",

	/* IT Process > IT Risk */
	itProcessItRiskNoDataPlaceholder: 'Aucun processus informatique n’a été identifié.<br />{identifyItProcess}',
	itProcessItRiskNoDataPlaceholderTarget: 'Aucun processus informatique n’a été relevé.',
	itApplication: 'Applications informatiques',
	itGC: 'CGI',
	addItRiskBtnTitle: 'Ajouter un risque lié aux TI',
	itProcessItRiskUnrelatedITGC: 'CGI dissociés',
	itProcessItRiskUnrelatedITGCUppercase: 'CGI non associés',
	itProcessItRiskNoRisksNoControlsPlaceholder: 'Il n’est pas requis d’inclure les risques liés à l’informatique ou les CGI pour ce processus informatique, car il n’y a aucun contrôle d’applications ni contrôle manuel lié aux TI dont la conception est efficace selon l’évaluation qui est associé aux applications informatiques auxquelles ce processus informatique est lié.',
	itProcessItRiskNoRisksControlsPlaceholder: "D'après les contrôles des applications et les contrôles manuels liés aux TI qui ont été relevés {itRiskIdentify} pour ce processus informatique ",
	itRiskIdentify: 'Les risques liés aux TI devraient être identifiés',
	itProcessItRiskItProcessContentTitle: 'Risques liés aux TI et CGI',
	itProcessItRiskItRiskNoItgcRequiredPlaceholder: 'Aucun CGI ne répond au risque lié aux TI.',
	itProcessItRiskItRiskItgcRequiredPlaceholder: 'Au moins un CGI doit être indiqué pour chaque risque identifié, ou il doit être indiqué que le risque lié aux TI n’est associé à aucun CGI. <br/>Identifier un {newITGC} ou un {existingITGC} qui répond au risque lié aux TI ou indiquer qu’{noItRisksIdentified} ne répond au risque lié au TI.',
	noItRisksIdentified: 'Aucun CGI',
	newITGC: 'nouveau CGI',
	existingITGC: 'CGI existant',
	unrelatedItGCModalMessage: 'Supprimer les CGI non associés qui ne sont plus requis.',
	unrelatedItGCModalNoDataPlaceholder: 'Aucun CGI non associé',
	removeItRisk: 'Supprimer le risque lié aux TI',
	deleteItRiskConfirmation: 'Voulez-vous vraiment supprimer le risque lié aux TI<b>{0}</b>? Cette action supprimera le risque lié aux TI et ne pourra pas être annulée.',
	relateItGcTitle: 'CGI associé',
	relateItGcEntityTitle: 'Risque lié aux TI',
	relateItGcDescription: 'Sélectionner les CGI qui sont pertinents pour le risque lié aux TI.',
	relateItGcSearchPlaceholder: 'Rechercher un CGI',
	relateItGcShowSelectedOnlyText: 'Afficher seulement les CGI associés',
	relateItGcNoDataPlaceholder: 'Aucun CGI disponible. Créer un nouveau CGI pour continuer.',
	relateITSPTitle: 'Procédure de corroboration liée aux TI associée',
	relateITSPDescription: 'Sélectionner les procédures de corroboration liées aux TI qui sont pertinentes pour le risque lié aux TI.',
	relateITSPSearchPlaceholder: 'Chercher par nom de procédure de corroboration liée aux TI',
	relateITSPShowSelectedOnlyText: 'Afficher seulement les procédures de corroboration liées aux TI associées',
	relateITSPNoDataPlaceholder: 'Aucune procédure de corroboration liée aux TI disponible. Créer une nouvelle procédure de corroboration liée aux TI pour continuer.',

	/* IT Process Task Relationship */
	relateUDP: 'Associer la tâche Comprendre le processus',
	relateUDPDescription: 'Sélectionner les tâches Comprendre le processus qui sont pertinentes pour le processus informatique.',
	relateUDPListHeaderItemName: 'Nom de la tâche',
	relateUDPSearchPlaceholder: 'Chercher par nom de tâche',
	relateUDPNoResultsFoundPlaceholder: 'Aucun résultat',
	relateUDPCountLabel: '{0} tâches',
	relateUDPClose: 'Fermer',
	relateUDPShowOnlyRelatedTasks: 'Afficher seulement les tâches associées',
	relateUDPNoDataFoundPlaceholder: 'Aucune tâche disponible',
	relateUDPNoDataPlaceHolder: 'Aucun processus informatique n’a été identifié',

	/* ITGC test strategy */
	itProcessItRiskItGcWithoutDesignEffectiveness: 'CGI sans efficacité de la conception',
	searchItGC: 'Rechercher un processus informatique',
	itGCNoDataPlaceHolder: 'Aucun processus informatique n’a été identifié',
	addItRisks: 'Ajouter des risques liés aux TI',
	itDMHeader: 'Contrôle manuel lié aux TI',
	itAppHeader: 'Application informatique',
	itTestHeader: 'Test',
	itTestingHeader: 'Tests',
	itgcHeader: 'ITGCs',
	controlsSelectedHeader: 'Contrôles sélectionnés aux fins des tests',
	iTRisksAndITGCs: 'Risques liés aux TI et CGI',
	NoITGCForITRiskPlaceholder: 'Il n’existe aucun CGI dans l’environnement informatique pour répondre à ce risque lié aux TI',
	ITGCsNotIdentifiedRiskNoITGCs: 'Aucun CGI n’a été identifié pour ce risque. {identifyAnITGC} ou indiquer que {itRiskHasNoITGCs}.',
	identifyAnITGC: 'Identifier un CGI',
	itRiskHasNoITGCs: 'risque lié aux TI n’est associé à aucun CGI',

	/**
	 * IT SO > SCOT
	 */
	searchItSO: 'Rechercher une société de services',
	addItSOBtnTitle: 'Ajouter une société de services',
	itSoNoDataPlaceHolder: 'Aucune société de services n’a été identifiée.<br/><a>{identifyAnSo}<a/>',
	noItSoDataPlaceHolder: 'Aucune société de services n’a été identifiée.',
	identifyAnSo: 'Identifier une société de services',
	soHeader: 'Société de services',
	editSO: 'Modifier la société de services',
	deleteSO: 'Supprimer la société de services',
	viewSO: 'Afficher la société de services',
	controlRelatedToSO: 'Contrôles associés à la société de services',

	/**
	 * Manage IT SP
	 */
	addITSP: 'Ajouter une procédure de corroboration liée aux TI',
	searchPlaceholderManageITSP: 'Rechercher un processus informatique',
	noManageITSPDataPlaceholder: 'Aucun processus informatique n’a été identifié',
	itRiskColumnHeader: 'Risques liés aux TI',
	itDesignEffectivenessHeader: 'Efficacité de la conception',
	itTestingColumnHeader: 'Tests',
	itGCColumnHeader: 'CGI',
	itSPColumnHeader: 'Procédures de corroboration liées aux TI',
	searchClearButtonTitle: 'Effacer',
	itProcessItRiskUnrelatedITSP: 'Procédures de corroboration liées aux TI dissociées',
	manageITSPUnrelatedITSPUppercase: 'Procédures de corroboration liées aux TI non associées',
	unrelatedITSPModalMessage: 'Supprimer les procédures de corroboration liées aux TI non associées qui ne sont plus requises.',
	unrelatedITSPModalNoDataPlaceholder: 'Aucune procédure de corroboration liée aux TI non associée',
	noITGCPlaceholderMessageFragment1: 'Au moins un CGI doit être indiqué pour chaque risque identifié, ou il doit être indiqué que le risque lié aux TI n’est associé à aucun CGI : ',
	noITGCPlaceholderMessageFragment2: 'Identifier un',
	noITGCPlaceholderMessageFragment3: 'nouveau CGI',
	noITGCPlaceholderMessageFragment4: 'ou',
	noITGCPlaceholderMessageFragment5: 'CGI existant',
	noITGCPlaceholderMessageFragment6: 'qui répond au risque lié aux TI ou indiquer qu’',
	noITGCPlaceholderMessageFragment7: 'aucun CGI',
	noITGCPlaceholderMessageFragment8: 'ne répond au risque lié au TI.',
	addNewITSP: 'Ajouter une nouvelle procédure de corroboration liée aux TI',
	addExistingITSP: 'Ajouter une procédure de corroboration liée aux TI existante',
	noITSPPlaceholderMessageFragment1: 'Si nous avons évalué que les CGI sont inefficaces ou que nous n’avons relevé aucun CGI qui réponde au risque lié aux TI, il se peut que nous puissions mettre en œuvre des procédures de corroboration liées aux TI pour obtenir l’assurance raisonnable que le risque lié aux TI dans le processus informatique associé au CGI inefficace n’a pas été exploité.',
	noITSPPlaceholderMessageFragment2: 'Identifier une nouvelle procédure de corroboration liée aux TI',
	noITSPPlaceholderMessageFragment3: 'ou',
	noITSPPlaceholderMessageFragment4: 'associer une procédure de corroboration liée aux TI existante',
	noITSPPlaceholderMessageFragment5: '.',
	noITGCsExitForITRisk: 'Il n’existe aucun CGI pour répondre à ce risque lié aux TI.',
	noITSPExitForITRisk: 'Il n’existe aucune procédure de corroboration liée aux TI pour ce risque lié à l’informatique.',
	manageITSPItemExpansionMessage: 'Risques liés aux TI',
	noITGCExists: 'Aucune procédure de corroboration liée aux TI ne répond au risque lié aux TI.',
	iTGCName: 'Nom du CGI',
	itSPName: 'Nom de la procédure de corroboration liée aux TI',
	operationEffectiveness: 'Efficacité du fonctionnement',
	savingLabel: 'Enregistrement en cours…',
	deletingLabel: 'Suppression en cours...',
	removingLabel: 'Retrait en cours...',
	itFlowModalDescription: 'Allez à {itSummaryLink} pour modifier/retirer les objets qui ne sont plus applicables à la mission.',
	itSummaryLink: 'Écran de synthèse TI',
	manageITSPYes: 'Oui',
	manageITSPNo: 'Non',

	understandITProcess: 'Comprendre les processus informatiques',
	activity: 'Activité',
	unsavedPageChangesMessage: 'Si vous continuez, les changements non enregistrés seront perdus. Voulez-vous vraiment quitter cette page?’',
	unsavedChangesTitle: 'Changements non enregistrés',
	unsavedChangesLeave: 'Quitter cette page',
	unsavedChangesStay: 'Rester sur cette page',

	notificationDownErrorMessage: 'La fonction Notifications n’est pas disponible pour l’instant. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	notificationUpbutSomeLoadingErrorMessage: 'Un problème technique est survenu qui empêche la fonction Notifications de fonctionner. Veuillez actualiser la page et réessayer. ',
	markCompleteError: 'Tous les documents présentés doivent être approuvés par au moins un préparateur et un responsable de la revue.',
	markCompleteDescription: 'Tous les documents doivent être approuvés par au moins un préparateur et un responsable de la revue pour que l’activité soit marquée comme étant terminée.',
	lessthan: 'Moins de',
	openingFitGuidedWorkflowFormError: 'Impossible d’ouvrir le formulaire de l’outil FIT d’EY Canvas',
	timeTrackerErrorFallBackMessage: 'La fonction Suivi du temps n’est pas disponible pour le moment. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	timeTrackerLoadingFallbackMessage: 'La fonction Suivi du temps n’est pas disponible pour le moment. Elle sera bientôt disponible.',
	priorPeriodRelateDocument: 'Associer les éléments probants de la période précédente',
	selectedValue: 'Valeur sélectionnée',
	serviceGateway: 'Service Gateway',
	docNameRequired: 'Le champ du nom ne peut pas être vide',
	docInvalidCharacters: 'Le nom du fichier ne peut pas contenir les caractères suivants : */:<>\\?|"',
	invalidComment: 'Le commentaire n’a pas pu être ajouté. Si plusieurs éléments sont sélectionnés dans une liste, n’en sélectionner qu’un et réessayer. Si le problème persiste, actualisez la page et réessayez ou communiquez avec le Service de dépannage.',
	inputInvaildCharacters: 'L’entrée ne peut pas contenir les caractères suivants : */:<>\\?|"',

	// FIT Navigation panel
	relatedActivities: 'Activités associées',
	backToRelatedActivities: 'Retour aux activités associées',
	backToMainActivities: 'Retour aux activités principales',
	formOptions: 'Options du formulaire',

	// FIT Sharing
	shareActivity: 'Partager l’activité',
	shareLabel: 'Partager',
	shareInProgress: 'Partage en cours',
	manageSharing: 'Pour associer cette activité, vous devez disposer de l’autorisation permettant à l’utilisateur de partager l’outil FIT d’EY Canvas. Naviguez jusqu’à la page « Gérer l’équipe » pour gérer les autorisations ou communiquez avec un autre membre de l’équipe.',
	dropdownPlaceholderSA: 'Sélectionnez la mission pour le partage',
	fitSharingModalInfo: 'Partager cette activité avec une ou plusieurs activités de la même mission ou d’une autre mission du même espace de travail. Si les activités sélectionnées ne sont pas déjà partagées, les réponses associées aux activités sélectionnées ci-dessous seront écrasées. Si les activités sont déjà partagées, une seule activité peut être sélectionnée et les réponses associées à cette activité seront écrasées.',
	lastModifiedDate: 'Dernière modification le : ',
	noActivityToShare: 'Aucune activité à partager',
	activityNotShared: 'L’activité {0} n’a pas été associée',
	activityShareSuccessfull: 'L’activité {0} a été associée avec succès',
	sharedWithAnotherFITActivity: 'Cette activité est partagée avec une autre activité.',
	sharedActivityWithAnotherCanvas: 'Associer  l’activité avec une autre activité de l’outil FIT d’EY Canvas',
	shareActivityModalTitle: 'Partager l’activité',
	showRelationshipsTitle: 'Afficher les relations',
	shareActivityEngagement: 'Mission',
	shareActivityRelationshipsModalTitle: 'Relations de l’activité partagée',
	shareActivityWorkspaceHeading: 'Cette activité est partagée avec les missions et les activités qui y sont associées suivantes dans le même espace de travail.',
	shareModalOkTitle: 'Partager',
	shareModalContinueLabel: 'Continuer',
	selectedActivityInfoLabel: 'de la mission sélectionnée a été modifié(e) pour la dernière fois le : ',
	noSharedActivityInfoLabel: 'Il n’y a pas d’autre document du même type à partager dans cette mission.',
	alreadyHasSharedActivityInfoLabel: 'L’activité sélectionnée est déjà partagée avec d’autres activités. En partageant l’activité en cours, vous synchroniserez les réponses liées à l’activité sélectionnée avec l’activité en cours.',
	selectActivityResponsesForSharingLabel: 'Indiquez dans quel sens effectuer le partage : ',
	selectActivityResponsesForCurrentRadioLabel: 'Prendre les réponses du document actuel pour les partager avec le document sélectionné ci-dessus',
	selectActivityResponsesForSelectedRadioLabel: 'Prendre les réponses du document sélectionné ci-dessus pour les partager avec le document actuel',
	selectActivityResponsesWarningEarlierTimeLabel: "The current activity was modified at an earlier time compared to the selected engagement's activity. Please consider this before confirming the sharing option's below the table.",
	selectActivityResponsesWarningModifiedMoreRecentlyLabel: 'L’activité en cours a été modifiée plus récemment que l’activité du document sélectionné. Veuillez prendre cette information en considération avant de confirmer le choix de partage ci-dessus.',
	selectActivityUnsuccessfulMessage: 'Échec du partage. Veuillez réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	otherEngagemntDropdownlabel: 'Autres missions dans l’espace de travail : ',
	documentSearchPlaceholder: 'Rechercher un document',
	showOnlySelected: 'Afficher seulement les éléments sélectionnés',

	//FIT Copy
	copyLabel: 'Copier',
	copyActivity: 'Copier l’activité',
	copyInProgress: 'Copie en cours',
	fitCopyModalInfo: 'Copier les réponses de cette activité dans une ou plusieurs activités de la même mission ou d’une autre mission du même espace de travail.',
	dropdownPlaceholderCA: 'Sélectionner la mission dans laquelle effectuer la copie',
	noCopyActivityInfoLabel: 'Il n’y a pas de document du même type dans lequel effectuer la copie dans cette mission.',
	copyActivityHoverLabel: 'Cette activité est déjà partagée avec d’autres activités et ne peut pas être copiée',
	copyActivityWarningEarlierTimeLabel: 'L’activité en cours a été modifiée à une date antérieure à celle de l’activité de la mission sélectionnée. Veuillez prendre cette information en considération avant de confirmer les options de copie.',

	//Unlink
	unlinkModalTitle: 'Dissocier l’activité',
	unlinkModalDescription: 'Voulez-vous vraiment dissocier l’activité sélectionnée?',
	unlinkLabel: 'Dissocier ',
	insufficientPermissionsLabel: 'Vous ne disposez pas des autorisations requises.',
	unlinkFailMessage: 'Échec de la dissociation. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	unlinkSuccessfulMessage: 'Dissociation réussie',
	unlinkInProgressLabel: 'Dissociation en cours',
	unlinkError: 'Erreur de détachement',
	unlinkInProgressInfo: 'La dissociation est en cours. Ce processus peut prendre jusqu’à 15 minutes. Une fois la dissociation terminée, ce formulaire devra être fermé puis rouvert.',

	/** Manage scot modal labels */
	scotName: 'Nom de la catégorie d’opérations importante',
	scotCategory: 'Type de catégorie d’opérations importante',
	estimate: 'Estimation',
	noScotsAvailablePlaceHolder: 'Aucune catégorie d’opérations importante disponible. Ajouter une nouvelle catégorie d’opérations importante pour commencer',
	addScotDisableTitle: 'Remplir tous les détails relatifs à la catégorie d’opérations importante à ajouter',
	deleteScotTrashLabel: 'Supprimer la catégorie d’opérations importante',
	undoDeleteScotTrashLabel: 'Annuler la suppression',
	scotNameValidationMessage: 'Le nom de la catégorie d’opérations importante est requis',
	scotCategoryValidationMessage: 'Le type de catégorie d’opérations importante est requis',
	scotWTTaskDescription: '<p>Nous confirmons notre compréhension de toutes les catégories d’opérations importantes courantes et non courantes et processus lié aux informations importantes à fournir à chaque période au moyen de tests de cheminement. De plus, dans le cas des audits réalisés selon les normes du PCAOB, nous effectuons des tests de cheminement des catégories d’opérations importantes d’estimation.<br/>Dans le cas de toutes les catégories d’opérations importantes où nous recourons à une stratégie axée sur les contrôles et dans le cas des contrôles qui répondent à des risques importants, nous confirmons que les contrôles pertinents ont été conçus et mis en place de façon appropriée. Nous confirmons que notre décision de recourir à une stratégie axée sur les contrôles convient toujours.<br/><br/>Nous concluons que notre documentation décrit avec exactitude le fonctionnement de la catégorie d’opérations importante et que nous avons relevé toutes les erreurs possibles pertinentes, y compris celles qui découlent du recours à l’informatique, et les contrôles connexes (le cas échéant).<br/><br/> Nous devons déterminer si notre compréhension des catégories d’opérations importantes d’estimation qui font l’objet d’une stratégie corroborative est appropriée à la lumière de nos procédures de corroboration.</p>',

	relate: 'Associer',
	unrelate: 'Dissocier',
	related: 'Associée',
	relatedSCOTs: 'Catégorie d’opérations importante associée',
	thereAreNoSCOTsIdentified: 'Il n’y a aucune catégorie d’opérations importante identifiée',
	selectSCOTsToBeRelated: 'Sélectionner les catégories d’opérations importantes devant être associées',

	//OAR Tables
	OARBalanceSheet: 'Bilan',
	OARIncomeStatement: 'État des résultats',
	OARCurrentPeriod: 'Date de l’analyse',
	OARAmountChangeFrom: 'Changement par rapport à',
	OARPercentageChangeFrom: '% de variation par rapport à',
	OARNoDataAvailable: 'Aucune donnée disponible. Passez en revue la page {0} et importez les données pour continuer.',
	OARAnnotationLabel: 'Cliquez pour passer en revue les raisons des changements inattendus ou de l’absence des changements prévus',
	OARAnnotationSelectedIcon: 'Consignez les raisons des changements inhabituels ou de l’absence des changements prévus',
	OARAnnotationModalTitle: 'Annotation',
	OARAnnotationModalPlaceholder: 'Consignez les éléments qui semblent inhabituels, les changements inattendus ou de l’absence des changements prévus.',
	OARWithAnnotationLabel: 'Documentation des changements inattendus',
	OARAnnotation: 'Annotations',
	OARAccTypeWithAnnotationCountLabel: '{0} annotations pour le type de compte',
	OARSubAccTypeWithAnnotationCountLabel: '{0} annotations pour le sous-type de compte',
	OARColumnA: 'A',
	OARColumnB: 'B',
	OARColumnC: 'C',
	OARComparative1Period: 'Date de comparaison 1',
	OARComparative2Period: 'Date de comparaison 2',
	OARExpand: 'Développer la catégorie de comptes',
	OARCollapse: 'Réduire la catégorie de comptes',
	OARHelixNavigationLink: 'Accéder à EY Helix pour des renseignements supplémentaires.',
	OARPrintNoDataAvailable: 'Aucune donnée disponible',
	OARAdjustedBalance: 'Solde ajusté',
	OARLegendLabel: 'Si une valeur est marquée d’un astérisque (*), cela indique qu’elle a été ajustée. Accédez au module Ajustement pour plus de détails.',
	OARAccountType: 'Type de compte',
	astrixLabel: '*',

	//OAR Helix integration
	helixIntegrationModalDescription: 'Il s’agit d’un texte en attente d’être défini.',
	OSJETabText: 'Autre partie de l’écriture de journal',
	activityAnalysisTabText: 'Analyse de l’activité',
	preparerAnalysisTabText: 'Analyse par préparateur',
	accountMetricsTabText: 'Mesures pour le compte',
	noAnalyticsData: 'Aucune analyse à afficher',

	printActivitiesTitle: 'Imprimer les activités',
	printActivitiesModalInfo: 'Veuillez sélectionner les activités que vous souhaitez inclure.',
	printActivitiesModalConfirmButton: 'Combiner les documents PDF',
	printActivitiesDropdownLabel: 'Activités FIT',
	printActivitiesAll: 'Toutes',
	oarSetupText: 'Veuillez aller à la page {0} pour associer ou configurer un projet EY Helix.',
	helixNotAvailable: 'EY Helix n’est pas disponible pour votre mission.',
	dragDropUploadPlaceholder: 'Glisser et déposer un ou plusieurs documents ou cliquer sur <span>{addDocument}</span>',

	noTaskAssociatedToastMessage: 'Étant donné que le formulaire Canvas se trouve dans les fichiers temporaires, les documents ajoutés ont également été enregistrés dans les fichiers temporaires.',

	// chart labels.
	assets: 'Actifs',
	liabilities: 'Passifs',
	equity: 'Capitaux propres',
	revenues: 'Produits',
	expenses: 'Charges',
	noAccountsAvailable: 'Aucun compte disponible',

	// ALRA
	ALRAFilterByAccount: 'Filtrer par compte',
	ALRANoRecords: 'Aucun résultat',
	ALRAAssertions: 'Assertions',
	ALRAInherent: 'Facteurs de risque inhérent',
	ALRAHigher: 'Facteurs de risque élevé',
	ALRAAccountDisclosure: 'Comptes/Informations à fournir',
	ALRAType: 'Type',
	ALRAName: 'Nom',
	ALRARisks: 'Risques',
	ALRAC: 'Exh',
	ALRAEO: 'Exi/R',
	ALRAMV: 'M/Év',
	ALRARO: 'D&O',
	ALRAPD: 'P&I',
	ALRAR: 'R',
	ALRANoRisksAssociated: 'Aucun risque associé à ce compte',
	ALRAAccountsDisclosureName: 'Nom des comptes/informations à fournir',
	ALRAHigherRisk: 'Risque élevé',
	ALRAHigherInherentRisk: 'Risque inhérent élevé',
	ALRAHigherRiskCode: 'É',
	ALRALowerRisk: 'Risque faible',
	ALRALowerInherentRisk: 'Risque inhérent faible',
	ALRALowerRiskCode: 'F',
	ALRALimitedRiskAccount: 'Le compte a été identifié comme présentant un risque limité',
	ALRAInsignificantRiskAccount: 'Le compte a été identifié comme étant négligeable',
	ALRADesignations: 'Désignations',
	ALRABalances: 'Soldes',
	ALRADesignation: 'Désignation',
	ALRAAnalysisPeriod: 'Date de l’analyse',
	ALRAxTE: 'xEA',
	ALRAPercentageChangeFrom: '% de variation par rapport à',
	ALRAPriorPeriodDesignation: 'Désignation de la période précédente',
	ALRAPriorPeriodEstimate: 'Estimation de la période précédente',
	ALRAComparativePeriod1: 'Date de comparaison 1',
	ALRAComparativePeriod2: 'Date de comparaison 2',
	ALRASelectUpToThreeOptions: 'Sélectionner jusqu’à 3 options',
	ALRASelectUpToTwoOptions: 'Sélectionner jusqu’à 2 options',
	ALRAValidations: 'Validations',
	ALRANoSignOffs: 'Aucune approbation',
	ALRAIncompleteInherentRisk: 'Risque inhérent incomplet',
	ALRARelatedDocuments: 'Documents associés',
	ALRAGreaterExtent: 'Plus élevé',
	ALRALesserExtent: 'Moins élevé',
	ALRARiskRelatedToAssertion: 'Risque associé',
	ALRAContributesToHigherInherentRisk: 'Risque associé contribuant au risque inhérent élevé.',

	// Assess inherent risk
	HigherRiskAssertionWithoutRisksThatContributesToTheHigherInherentRisk: 'Une assertion présentant un risque inhérent élevé sans au moins un risque contribuant au risque inhérent élevé a été relevée. Associer les risques et identifier ceux qui contribuent à l’assertion présentant un risque inhérent élevé.',

	//MEST - Multi-entity account Execution Type selection listing
	account: 'Compte',
	taskByEntity: 'Tâche par entité',
	bodyInformation: 'Vous devez cliquer sur Importer le contenu ci-dessous pour enregistrer les modifications.',

	/*user search component*/
	seachInputRequired: 'Entrer le texte recherché',
	nameOrEmail: 'Nom ou adresse courriel',
	emailForExternal: 'Courriel',
	noRecord: 'Aucun résultat trouvé',
	userSearchPlaceholder: 'Entrer le nom ou l’adresse courriel et appuyer sur la touche Entrée pour afficher les résultats.',
	userSearchPlaceholderForExternal: 'Entrer le courriel et appuyer sur la touche Entrée pour afficher les résultats.',
	clearAllValues: 'Effacer toutes les valeurs',
	inValidEmail: 'Veuillez entrer une adresse courriel valide',

	//reactive frame
	maxTabsLocked: 'Nombre maximal d’onglets autorisés atteint. Désépinglez et fermez l’un des onglets pour pouvoir en ouvrir un autre.',
	openInNewTab: 'Ouvrir dans un nouvel onglet',
	unPinTab: 'Désépingler l’onglet',
	pinTab: 'Épingler l’onglet',
	closeDrawer: 'Fermer le tiroir',
	minimize: 'Réduire',

	accountHeader: 'Comptes',
	sCOTSummaryAccountNoDataLabel: 'Chaque catégorie d’opérations importante doit être associée à au moins un compte important ou une information à fournir importante. Sélectionnez un compte important existant ou une information à fournir importante existante à associer à cette catégorie d’opérations importante.',
	sCOTSummaryNoDataLabel: 'Aucune catégorie d’opérations importante n’a été créée.',
	scotSearchNoResultsFound: 'No results found',
	scotSummary225TabsName: {
		[0]: {
			label: 'Afficher par compte'
		},
		[1]: {
			label: 'Afficher par catégorie d’opérations importante'
		}
	},

	// Display Account Balances
	currentPeriodAccountBalance: 'Solde du compte de la période considérée : ',
	priorPeriodAccountBalance: 'Solde du compte de la période précédente : ',

	ALRANoResults: 'Aucun résultat. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	associatedRomsCount: 'Nombre total de risques associés : {0}',
	alraMessage: 'Réponse relative à la désignation du compte non alignée sur la désignation dans Modifier le compte et l’information à fournir',
	estimateCategoryResponseNotAlignedToDesignation: 'Réponse relative à la catégorie d’estimation non alignée sur la désignation dans « Modifier l’estimation »',


	// Analytics Overview
	analyticsOverviewTitle: 'Aperçu des analyses',
	noSignificantAccountRecords: 'Aucun compte important n’a été créé.',
	noSignificantAccountMapped: 'Aucun compte important n’est mappé à l’entité sélectionnée.',
	noLimitedAccountMapped: 'Aucun compte présentant un risque limité n’est mappé à l’entité sélectionnée.',
	openAnalyticDocumentation: 'Ouvrir la documentation relative à l’analyse',
	openLimitedRiskAccountDocumentation: 'Ouvrir la documentation sur le compte présentant un risque limité',
	associatedSCOTs: 'Catégories d’opérations importantes associées : ',
	analysisPeriodLabel: 'Date de l’analyse : {0}',
	analysisPeriodChangeLabel: '% de variation par rapport à {0}',
	xTELabel: 'xEA',
	risksLabel: 'Risques',
	comparativePeriod1: 'Date de comparaison 1 : {0}',
	analysisPeriodTitle: 'Date de l’analyse',
	analysisPeriodChangeTitle: '% de variation par rapport à',
	comparativePeriodTitle: 'Date de comparaison 1',
	noAccountAvailable: 'Aucun compte disponible',

	// Estimates
	titleEstimateCategory: 'Catégorie d’estimation',
	titleRisks: 'Risques',

	voiceNoteNotAvailable: 'La note vocale et l’enregistrement d’écran ne sont pas disponibles dans le tiroir. Passez en mode plein écran pour utiliser ces fonctionnalités.',

	financialStatementType: {
		[1]: {
			label: 'Actif'
		},
		[2]: {
			label: 'Actif courant'
		},
		[3]: {
			label: 'Actif non courant'
		},
		[4]: {
			label: 'Passif'
		},
		[5]: {
			label: 'Passif courant'
		},
		[6]: {
			label: 'Passif non courant'
		},
		[7]: {
			label: 'Capitaux propres'
		},
		[8]: {
			label: 'Produits'
		},
		[9]: {
			label: 'Charges'
		},
		[10]: {
			label: 'Résultat hors exploitation'
		},
		[11]: {
			label: 'Autres éléments du résultat global (AERG)'
		},
		[12]: {
			label: 'Autres'
		},
		[13]: {
			label: 'Type de compte'
		},
		[14]: {
			label: 'Sous-type de compte'
		},
		[15]: {
			label: 'Catégorie de compte'
		},
		[16]: {
			label: 'Sous-catégorie de compte'
		},
		[17]: {
			label: 'Résultat net'
		}
	},
	accountTypes: {
		[1]: {
			label: 'Compte important'
		},
		[2]: {
			label: 'Compte présentant un risque limité'
		},
		[3]: {
			label: 'Compte négligeable'
		},
		[4]: {
			label: 'Autre compte'
		},
		[5]: {
			label: 'Information à fournir importante'
		}
	},
	noClientDataAvailable: 'Aucune donnée disponible',

	analysisPeriod: 'Date de l’analyse',
	comparativePeriod: 'Date de comparaison',
	perchangeLabel: '% de variation',

	entityCreateAccountLabel: 'Créer un compte et une information à fournir',
	insignificantAccount: 'Compte négligeable',
	noAccountRecords: 'Aucun compte n’a été identifié',
	noAccountsForEntity: 'Aucun compte ni aucune information à fournir n’est mappé à l’entité sélectionnée.',
	noLimitedRiskAccountRecords: 'Aucun compte présentant un risque limité disponible.',
	createAccount: 'Créer un compte',
	createDocument: 'Créer un document',
	noAccountResults: 'Aucun compte identifié.',
	createGroupInvolvementDocument: 'Créer un formulaire relatif à la participation',
	chooseVersionsToCompare: 'Choisir une version à comparer',
	noTrackChangesOption: 'Aucune version en mode de suivi des modifications disponible',
	trackChangesDefaultMessage: 'Sélectionner une version dans le menu déroulant « Choisir une version à comparer » pour continuer.',
	whichRiskContributeToHigherRisk: 'Quels risques contribuent à une assertion avec risque élevé?',

	//multi-entity Entity List
	createMultiEntity: 'Nouvelle entité',
	editMultiEntity: 'Modifier l’entité',
	noEntitiesAvailableCreateNewLink: 'Cliquer ici',
	noEntitiesAvailable: 'Aucune entité n’a été créée. {noEntitiesAvailableCreateNewLink} pour commencer.',
	noEntitiesFound: 'Aucun résultat',
	createMultiEntityProfile: 'Créer le profil de l’entité',

	createEntity: 'Créer une entité',
	includeEntities: 'La liste entités multiples doit inclure au moins une entité. {Créer une entité} pour commencer.',
	//multi-entity table
	multiEntityCode: 'Référence standard de l’entité',
	multiEntityName: 'Nom de l’entité',
	multiEntityGroup: "Groupe de l'entité ",
	multiEntityActions: 'Actions',
	relateMultiEntityUngrouped: 'Dissociées',
	selectAll: 'Tout sélectionner',
	entitiesSelected: 'entités sélectionnées',
	entitySelected: 'entité sélectionnée',
	meNoEntitiesAvailable: 'Aucune entité disponible',
	meSwitchEntities: 'Changer d’entité',
	meSelectEntity: 'Sélectionner une entité',
	allEntities: 'Toutes les entités',
	noEntitiesIdentified: 'Aucune entité identifiée',
	contentDeliveryInProcessMessage: 'Envoi du contenu en cours. L’envoi du contenu peut prendre jusqu’à 10 minutes.',
	importContent: 'Importer le contenu',
	profileSubmit: 'Envoi du profil',
	importPSPs: 'Importer des PCB',
	contentUpdateInsufficienRolesLabel: 'Rôles ne permettant pas de mettre à jour le contenu. Vous pouvez obtenir les droits requis auprès de l’administrateur de la mission.',
	// MEST Switcher
	meEntitySwitcher: 'Sélecteur d’entité',
	//Error Boundary
	errorBoundaryMessage: 'Une erreur s’est produite. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	sectionName: 'Nom de la section',
	maxLength: 'Le texte ne doit pas dépasser {number} caractères.',
	required: 'Obligatoire',
	yearAgo: 'il y a {0} an',
	yearsAgo: 'il y a {0} ans',
	monthAgo: 'il y a {0} mois',
	monthsAgo: 'il y a {0} mois',
	weekAgo: 'il y a {0} semaine',
	weeksAgo: 'il y a {0} semaines',
	daysAgo: 'il y a {0} jours',
	dayAgo: 'il y a {0} jour',
	today: 'Aujourd’hui',
	todayLowercase: 'aujourd’hui',
	yesterday: 'Hier',
	approaching: 'approchant',

	associatedToInherentRiskFactor: 'Asscocié à un facteur de risque inhérent',

	createMissingDocument: 'Créer le document manquant',
	createMissingDocumentBody: 'Cliquer sur Confirmer pour créer le document pour les éléments connexes pour lesquels il manque actuellement un document.',
	documentCreationSuccessMsg: 'Création du document en cours. Veuillez actualiser la page pour voir les mises à jour.',

	noRisksRelatedToAssertion: 'Aucun risque associé à l’assertion à l’égard de ce compte.',

	noAssertionsRelatedToAccount: 'Aucune assertion associée à ce compte',

	sharing: 'Partage',

	risksUnrelatedToAccountAssertion: 'Risques non associés à l’assertion à l’égard du compte',
	cantCompleteTask: 'Approbations manquantes pour les documents associés à la tâche connexe. Ouvrez la tâche, traitez les documents pour lesquels il manque une ou des approbations et essayez à nouveau de marquer la tâche comme étant terminée.',
	cantCompleteTasksTitle: 'Impossible de marquer comme étant terminée',
	ok: 'OK',
	documentsEngagementShareLabel: 'Sélectionnez le document de la mission à partager avec',
	documentsEngagementCopyLabel: 'Sélectionnez le document de la mission à copier',
	lastModifiedon: 'Dernière modification le',
	newAccountAndDisclosure: 'Nouveau compte et nouvelle information à fournir',
	newAccountORDisclosure: 'Nouveau compte ou nouvelle information à fournir',

	externalDocuments: 'Documents externes',
	noExternalDocumentsAvailable: 'Aucun document externe disponible',
	addExternalDocuments: 'Ajouter des documents externes',
	relateExternalDocuments: 'Associer des documents externes',

	helixNotMappedToAccount: 'Les données d’EY Helix ne sont pas mappées à ce compte. Veuillez mettre à jour le mappage et importer à nouveau les données pour continuer.',
	trackChangesNotAvailableForSpecialBodyDisplayMessage: 'Le mode de suivi des modifications n’est pas disponible pour les réponses ci-après.',
	noDocumentRelatedObjectsApplicable: 'Il n’est pas obligatoire d’associer des objets à ce formulaire lié au flux de travail guidé',
	helixViewerLoader: 'Chargement du tiroir d’affichage de données EY Helix…',
	trackChangesViewDefaultMessage: 'Le mode de suivi des modifications n’est pas disponible pour certaines réponses. Dans ce cas, le message suivant sera affiché dans les détails de l’activité sous-jacente : « Le mode de suivi des modifications n’est pas disponible pour les réponses ci-après. » Par conséquent, l’absence de notifications de modification ci-après n’indique pas si des modifications ont été apportées ou non.',

	//Relate task modal
	relateTasksTitle: 'Associer les tâches',
	taskLocationLabel: 'Emplacement de la tâche',
	relateTaskInstructionalText: 'Ajouter ou supprimer les tâches auxquelles le document devrait être associé. Si le document fait partie des éléments probants et que vous le supprimez de la dernière tâche, il sera déplacé dans les fichiers temporaires.',
	noResultFound: 'Aucun résultat.',
	relatedTaskCounter: '{0} tâche',
	relatedTasksCounter: '{0} tâches',
	onlyShowRelatedTasks: 'Afficher seulement les tâches associées',
	relateTaskName: 'Nom de la tâche',
	relateTaskType: 'Type',

	/*Relate Entities*/
	relateEntitiesTitle: 'Associer des entités',
	relateEntitiesSearchPlaceholder: 'Appuyez sur Entrée pour rechercher par nom',
	relateEntitiesName: 'Nom de l’entité',
	relateEntitiesIndex: 'Référence standard de l’entité',
	relatedEntitiesCounter: '{0} entités',
	relatedEntityCounter: '{0} entités',
	onlyShowRelatedEntities: 'Afficher seulement les entités associées',
	entity: 'Entité',

	step01: 'Étape 01',
	step02: 'Étape 02',
	shareActivityStep1Description: 'Sélectionner une mission et un document',
	shareActivityStep2Description: 'Choisir quelles réponses du document doivent remplacer les autres',
	documentsShareLabel: 'Partager la réponse du document sélectionné ci-après avec les autres documents.',
	selectedActivity: 'Sélectionner une activité',
	sharedHoverLabel: 'Cette activité est déjà partagée avec d’autres activités. En partageant cette activité, vous synchroniserez les réponses liées à cette activité avec toutes les activités partagées.',
	noAssertionsRelatedLabel: 'Aucune assertion associée.',

	// Bulk mark complete:
	bulkMarkCompleteInstructionalText: 'Toutes les tâches associées à ce formulaire sont indiquées ci-après. Pour que les tâches sélectionnées soient marquées comme étant terminées, tous les documents doivent être approuvés par au moins un préparateur et un responsable de la revue.',
	bulkMarkCompleteEngagementColumn: 'Mission',
	bulkMarkCompleteDocumentsMissingSignOffs: 'Approbations manquantes. Cliquez sur {bulkMarkCompleteMissingSignOffsClickableText} pour confirmer l’approbation.',
	bulkMarkCompleteMissingSignOffsClickableText: 'ici',
	bulkMarkCompleteNoAccessToEngagement: 'Vous n’avez pas accès à la mission dans laquelle se trouve cette tâche.',
	bulkMarkCompleteInProgressMessage: 'Processus en cours. Le processus peut prendre jusqu’à 10 minutes. Veuillez actualiser la page pour voir les mises à jour.',
	bulkMarkCompleteRelatedDocumentsModalTitle: 'Approbations du document',
	bulkMarkCompleteFilterUnreadyTasks: 'Afficher uniquement les tâches pour lesquelles des documents n’ont pas été approuvés.',
	bulkMarkCompleteNotAllowedModalTitle: 'Impossible de marquer comme étant terminée',
	bulkMarkCompleteNotAllowedModalDescription: 'Vous devez sélectionner au moins une tâche pour qu’elle soit marquée comme étant terminée.',
	bulkMarkCompleteRelatedDocumentsModalDescription: 'Pour que la tâche sélectionnée soit marquée comme étant terminée, tous les documents doivent être approuvés par au moins un préparateur et un responsable de la revue.',
	bulkMarkCompleteRelatedDocumentsModalRefreshSignoffs: 'Actualiser les approbations et les notes',
	selectedTaskCounter: '({0}) tâche sélectionnée',
	selectedTasksCounter: '({0}) tâches sélectionnées',

	// Mark complete (old):
	markCompleteNotAllowedModalDescription: 'Approbations manquantes pour les documents associés à la tâche connexe. Ouvrez la tâche, traitez les documents pour lesquels il manque une ou des approbations et essayez à nouveau de marquer la tâche comme étant terminée.',
	markCompleteInstructionalText: 'Tous les documents doivent être approuvés par au moins un préparateur et un responsable de la revue pour que l’activité soit marquée comme étant terminée.',

	// Adobe Analytics
	aaCookieConsentTitle: 'Bienvenue dans',
	aaCookieContentPrompt: 'Voulez-vous autoriser les témoins?',
	aaCookieConsentExplanation: '<p>Outre les témoins strictement nécessaires au fonctionnement de ce site Web, nous utilisons les types de témoins suivants afin d’améliorer votre expérience et nos services : <strong>les témoins de fonctionnalité<strong>, qui nous permettent d’améliorer votre expérience utilisateur (p. ex., en conservant en mémoire tous les paramètres que vous avez sélectionnés); <strong>les témoins de performance<strong>, qui nous aident à évaluer la performance du site Web et à améliorer votre expérience utilisateur, <strong>les témoins à des fins de publicité ou de ciblage<strong>, qui sont configurés par des tiers avec lesquels nous réalisons des campagnes publicitaires et qui nous permettent de vous présenter des annonces pertinentes pour vous.</p><p>Consultez le site<a target="_blank" href="https://www.ey.com/fr_ca/cookie-policy">Politique sur l’utilisation de témoins d’EY</a> pour de plus amples renseignements.</p>',
	aaCookieConsentExplanationWithDoNotTrack: '<p>Outre les témoins strictement nécessaires au fonctionnement de ce site Web, nous utilisons les types de témoins suivants afin d’améliorer votre expérience et nos services : <strong>les témoins de fonctionnalité<strong>, qui nous permettent d’améliorer votre expérience utilisateur (p. ex., en conservant en mémoire tous les paramètres que vous avez sélectionnés); <strong>les témoins de performance<strong>, qui nous aident à évaluer la performance du site Web et à améliorer votre expérience utilisateur, <strong>les témoins à des fins de publicité ou de ciblage<strong>, qui sont configurés par des tiers avec lesquels nous réalisons des campagnes publicitaires et qui nous permettent de vous présenter des annonces pertinentes pour vous.</p><p>Nous avons détecté que vous avez activé le paramètre Ne pas me suivre (DNT) dans votre navigateur; par conséquent, les témoins à des fins de publicité ou de ciblage sont automatiquement désactivés.</p><p>Consultez le site<a target="_blank" href="https://www.ey.com/fr_ca/cookie-policy">Politique sur l’utilisation de témoins d’EY</a> pour de plus amples renseignements.</p>',
	aaCookieConsentDeclineOptionalAction: 'Je refuse les témoins optionnels',
	aaCookieConsentAcceptAllAction: 'J’accepte tous les témoins',
	aaCookieConsentCustomizeAction: 'Personnaliser les témoins',
	aaCookieConsentCustomizeURL: 'https://www.ey.com/en_us/cookie-settings',

	// Cookie Settings
	cookieSettings: {
		title: 'Paramètres des témoins',
		explanation: 'Veuillez consentir à l’utilisation de témoins sur ey.com et sur la plateforme My EY. Sélectionnez un ou plusieurs types de témoins ci-dessous, puis sauvegardez votre choix. Consultez la liste ci-dessous pour connaître les détails sur les types de témoins et leur fonction.',
		emptyCookieListNotice: 'Les témoins de cette catégorie ne sont pas utilisés dans cette application.',
		nameTableHeader: 'Nom du témoin',
		providerTableHeader: 'Fournisseur du témoin',
		purposeTableHeader: 'Fonction du témoin',
		typeTableHeader: 'Type de témoin',
		durationTableHeader: 'Durée d’activité du témoin',
		formSubmit: 'Enregistrer ma sélection',
		requiredCookieListTitle: 'Témoins nécessaires',
		functionalCookieListTitle: 'Témoins fonctionnels',
		functionalCookieAcceptance: 'J’accepte les témoins fonctionnels ci-dessous.',
		functionalCookieExplanation: 'Les témoins fonctionnels, qui nous permettent d’améliorer votre expérience utilisateur (p. ex., en conservant en mémoire tous les paramètres que vous avez sélectionnés).',
		performanceCookieListTitle: 'Témoins de performance',
		performanceCookieAcceptance: 'J’accepte les témoins de performance ci-dessous.',
		performanceCookieExplanation: 'Les témoins de performance, qui nous aident à évaluer la performance du site Web et à améliorer votre expérience utilisateur. Quand nous utilisons des témoins de performance, nous ne stockons pas de données à caractère personnel, et nous n’utilisons les informations recueillies à l’aide de tels témoins que sous leur forme agrégée et anonyme.',
		advertisingCookieListTitle: 'Témoins de ciblage',
		advertisingCookieAcceptance: 'J’accepte les témoins publicitaires / de ciblage ci-dessous.',
		advertisingCookieExplanation: 'Les témoins publicitaires ou de ciblage, qui nous permettent de faire le suivi de l’activité et des sessions utilisateurs, de sorte que nous puissions fournir un service plus personnalisé. Dans le cas des témoins publicitaires, qui sont configurés par des tiers avec lesquels nous réalisons des campagnes publicitaires, ils nous permettent de vous présenter des annonces qui sont pertinentes pour vous.',
		doNotTrackNotice: 'Nous avons détecté que vous avez activé le paramètre Ne pas me suivre (DNT) dans votre navigateur; par conséquent, les témoins à des fins de publicité ou de ciblage sont automatiquement désactivés.',
	},
	accountFormsMissing: 'Formulaire de compte manquant pour {0} compte(s)',
	createAccountForms: 'Créer un ou des formulaires de compte',
	createAccountFormsDescription: 'Cliquer sur Confirmer pour créer le document pour les éléments connexes pour lesquels il manque actuellement un document.',
	createMissingDocuments: 'Comptes connexes pour lesquels il manque actuellement un ou des documents',
	accountDocumentsCreated: 'Envoi du contenu en cours. L’envoi du contenu peut prendre jusqu’à 10 minutes.',

	evidenceMissingPICSignoffs: 'Approbations de l’associé responsable manquantes pour les éléments probants',
	evidenceMissingEQRSignoffs: 'Approbations du RRQM manquantes pour les éléments probants',
	evidenceMissingPICEQRSignoffs: 'Approbations de l’associé responsable et/ou du RRQM manquantes pour les éléments probants',
	evidenceMissingPICSignoffRequirements: 'Exigences d’approbation de l’associé responsable manquantes pour les éléments probants',
	evidenceMissingEQRSignoffRequirements: 'Exigences d’approbation du RRQM manquantes pour les éléments probants',
	evidenceMissingPICEQRSignoffRequirements: 'Exigences d’approbation de l’associé responsable / du RRQM manquantes pour les éléments probants',
	evidenceMissingSignoffs: 'Éléments probants pour lesquels il manque des approbations',

	// Bulk task relate
	bulkTaskRelateFailureMessage: 'Certains des documents sélectionnés n’ont pas pu être associés à une ou plusieurs tâches sélectionnées.',
	/*endoflabels*/
	evidenceMissingPreparerOrReviwerSignoffs: 'Téléversement de documents - Approbations du préparateur ou du responsable de la revue manquantes',

	manageITProcess: 'Gérer le processus informatique',
	manageITRisk: 'Gérer le risque technologique',
	manageITControl: 'Gérer le contrôle informatique',
	manageITSP: 'Gérer la procédure de corroboration liée aux TI',
	manageITApp: 'Gérer l’application informatique',
	manageSCOT: 'Gérer les catégories d’opérations importantes',
	addAresCustomDescription: 'Sélectionner le type de contenu devant être ajouté à ce formulaire de flux de travail guidé, y saisir les données et cliquer sur Enregistrer.',

	documentImportSuccess: '{0} a bien été créé. L’envoi du contenu peut prendre jusqu’à 10 minutes.',
	documentImportFailure: 'Échec de la création du document. Veuillez actualiser la page ou réessayer plus tard. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	formNotAvailable: 'Aucun formulaire Canvas correspondant trouvé.',
	selectTask: 'Sélectionner une tâche à associer aux indications',
	canvas: 'Canvas',
	selectEngagement: 'Sélectionner une mission',

	//Modal Manage sub-scope
	manageSubScopeTitle: 'Gérer les sous-étendues',
	manageSubScopeDescription: 'Créer de nouvelles sous-étendues ou modifier/supprimer des sous-étendues existantes ci-après.',
	addSubScope: 'Ajouter une sous-étendue',
	subScopeName: 'Nom de la sous-étendue',
	knowledgeScope: 'Étendue de la base de connaissance',
	subScopeAlreadyExist: 'Le nom de la sous-étendue existe déjà.',
	subScopes: 'Sous-étendues',
	notAvailableSubScopes: 'Il n’y a aucune sous-étendue disponible.',
	SubScopeNameValidation: 'Le nom de la sous-étendue dépasse la longueur maximale de 255 caractères.',

	//CRA Summary
	manageAccount: 'Gérer le compte',
	newAccount: 'Nouveau compte',

	noRelatedObjectITProcessFlow: 'Aucun objet associé. Associer un objet pour commencer.',

	//Add New Flow Chart Steps
	flowChartNewSteps: {
		newStepTitle: 'Nouvelle étape',
		placeholderText_1: 'Entrer les détails relatifs à la nouvelle étape ci-dessous et sélectionner',
		placeholderText_2: "'Enregistrer et fermer' ",
		placeholderText_3: ' pour terminer. Pour créer une autre étape, sélectionner',
		placeholderText_4: "'Enregistrer et créer une autre étape'. ",
		columnLabel: 'Colonne (obligatoire)',
		counterOf: 'de',
		counterChar: 'caractères',
		stepNameLabel: 'Nom de l’étape (obligatoire)',
		errorMsgStepNameRequired: 'Le nom de l’étape est requis.',
		stepDescLabel: 'Description de l’étape (obligatoire)',
		stepDescPlaceholder: 'Entrer la description de l’étape',
		errorMsgStepDescRequired: 'La description de l’étape est requise.',
		required: 'Obligatoire',
		errorMsgStepDescExceedMaxLength: 'La description de l’étape dépasse le nombre maximal de caractères permis.',
		buttonCancel: 'Annuler',
		buttonSaveAndClose: 'Enregistrer et fermer',
		buttonSaveAndCreateAnother: 'Enregistrer et créer une autre étape',
		errorMsgColumnRequired: 'La colonne est requise.',
		headerNameForWCGW: 'Nom de l’erreur possible',
		headerNameForControl: 'Nom du contrôle',
		headerNameForITApp: 'Nom de l’application informatique',
		headerNameForServiceOrganisation: 'Nom de la société de services',
		relateLabelForWCGW: 'Associer les erreurs possibles',
		relateLabelForControl: 'Associer les contrôles',
		relateLabelForITApp: 'Lier des applications informatiques',
		relateLabelForServiceOrganisation: 'Lier des sociétés de services',
		designEffectiveness: 'Efficacité de la conception',
		testing: 'Tests',
		lowerRisk: 'Risque faible',
		wcgwNoRowsMessage: 'Aucune erreur possible n’a été associée. Cliquer sur {0} pour commencer.',
		controlNoRowsMessage: 'Aucun contrôle n’a été associé. Cliquer sur {0} pour commencer.',
		itAppNoRowsMessage: 'Aucune application informatique n’a été associée. Cliquer sur {0} pour commencer.',
		serviceOrganisationNoRowsMessage: 'Aucune société de services n’a été liée. Cliquez sur {0} pour commencer',
		wgcwTabLabel: 'Erreurs possibles',
		controlsTabLabel: 'Contrôles',
		itAppsTabLabel: 'Applications informatiques',
		serviceOrganisationTabLabel: 'Sociétés de services',
		connectionSuccessMessage: 'Association créée avec succès.',
		connectionFailedMessage: 'Impossible d’établir l’association. Veuillez réessayer.',
		selfConnectFailMessage: 'La source et la cible ne peuvent pas être identiques.',
		connectionDuplicateMessage: 'L’association existe déjà.',
		connectionDeleteSuccessMessage: 'L’association a bien été supprimée.',
		connectionDeleteFailMessage: 'Impossible de supprimer l’association. Veuillez réessayer.',
		editStepFailMessage: 'Impossible de modifier l’étape. Veuillez réessayer.',
		flowchartStepGetByIdFailMessage: 'Étape invalide. Veuillez actualiser la page et réessayer.',
		flowchartStepGetByIdFailureMessage: 'Cette étape de l’organigramme n’est plus disponible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
		newStepFailureMessage: 'Impossible de créer une nouvelle étape. Veuillez réessayer.',
		deleteConnector: 'Supprimer le connecteur',
		edgeConnectorOptions: 'Options du connecteur',
		edgeStartPoint: 'Point de départ',
		edgeEndPoint: 'End Point',
		relateDocumentToFlowchartStepError: 'L’opération ne peut pas être traitée pour le moment. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
		relateDocumentsOrObjects: 'Associer des documents ou des objets',
		thisstep: 'à cette étape'
	},

	flowChartWCGW: {
		wcgwsCounter: '{0} erreurs possibles',
		wcgwCounter: '{0} erreur possible',
		headerName: 'Associer les erreurs possibles',
		showOnlyRelatedText: 'Afficher seulement le texte lié',
		noResultsFound: 'Aucun résultat'
	},

	flowchartITAPPSO: {
		showOnlyRelatedText: 'Afficher seulement le texte lié',
		noResultsFound: 'Aucun résultat'
	},

	flowChartITApplication: {
		itApplicationsCounter: '{0} applications informatiques',
		itApplicationCounter: '{0} application informatique',
		headerName: 'Associer les applications informatiques',
		columnName: 'Nom de l’application informatique',
		noDataFound: 'Aucune application informatique trouvée'
	},

	flowChartITSO: {
		itSOsCounter: '{0} sociétés de services',
		itSOCounter: '{0} société de services',
		headerName: 'Associer des sociétés de services',
		columnName: 'Nom de la société de services',
		noDataFound: 'Aucune société de services trouvée'
	},

	flowChartControl: {
		controlsCounter: 'Associer les contrôles {0}',
		headerName: 'Associer les contrôles',
		showOnlyRelatedText: 'Afficher seulement le texte lié',
		noResultsFound: 'Aucun résultat',
		noWCGWs: 'Aucun contrôle n’a été créé'
	},

	relateSCOT: {
		header: 'Associer des catégories d’opérations importantes',
		estimate: 'Estimation',
		scotsCounter: '{0} catégories d’opérations importantes',
		scotCounter: '{0} catégorie d’opérations importante',
		headerName: 'Nom de la catégorie d’opérations importante',
		showOnlyRelated: 'Afficher seulement le texte lié',
		noResultsFound: 'Aucun résultat',
		noScotCreated: 'Aucune catégorie d’opérations importante créée dans la mission'
	},

	relatedStepObjects: {
		relatedWCGWs: 'Erreurs possibles associées',
		relatedControls: 'Contrôles associés',
		relatedDocuments: 'Éléments probants associés',
		relatedITApplications: 'Applications informatiques associées',
		relatedSOs: 'Sociétés de services associées'
	},

	flowchartEditSteps: {
		nextStep: 'Étape suivante',
		previousStep: 'Étape précédente',
		editStepTitle: 'Modifier l’étape',
		editPlaceholderText_1: 'Modifiez les détails relatifs aux étapes et aux objets associés ci-après. Cliquer sur',
		editPlaceholderText_2: "'Enregistrer et fermer' ",
		editPlaceholderText_3: 'pour enregistrer et retourner à l’organigramme. Le fait d’accéder aux autres étapes au moyen des options ci-après aura pour effet d’enregistrer vos mises à jour.',
		draftEditStepFailMessage: 'Impossible de créer l’étape de l’organigramme. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	},

	flowChartStepmoreMenu: {
		edit: 'Modifier',
		delete: 'Supprimer'
	},

	relateEstimate: {
		scot: 'Catégories d’opérations importantes',
		strategy: 'Stratégie axée sur la catégorie d’opérations importante',
		type: 'Type',
		noSCOT: 'Chaque estimation doit être associée à au moins une catégorie d’opérations importante. Cliquez sur',
		noSCOTmsg: ' pour commencer.',
		estimate: 'Estimation',
		routine: 'Courante',
		nonRoutine: 'Non courante',
		notSelected: 'Non sélectionnée',
		relateSCOTs: 'Associer des catégories d’opérations importantes',
		remove: 'Retirer',
		noEstimate: 'Aucune estimation disponible'
	},

	flowChartStepIcons: {
		wcgws: 'Erreurs possibles',
		controls: 'Contrôles',
		iTApps: 'Applications informatiques',
		serviceOrganisations: 'Sociétés de services'
	},

	flowChartStepIcon: {
		wcgw: 'ERREUR POSSIBLE',
		control: 'Contrôle',
		iTApp: 'Application informatique',
		serviceOrganisation: 'Société de services',
		evidence: 'Éléments probants'
	},

	flowChartErrorMessage: {
		stepOutsideOfTheColumns: 'Steps cannot be placed outside of the flowchart area',
		stepBetweenTheColumns: 'Steps cannot be placed between the columns',
		stepOnTopOrTooCloseToAnotherStep: 'Steps cannot be placed on top of the other steps'
	},

	//Delete Flow Chart Steps
	flowChartStepsDelete: {
		deletestep: 'Supprimer l’étape',
		deleteStepModalMessage: 'Voulez-vous vraiment supprimer cette étape? Les erreurs possibles, les contrôles, les applications informatiques, les sociétés de services et les éléments probants qui y sont associés seront tous dissociés.',
		cannotBeUndone: 'Contrats conclus avec des clients, nouveaux ou renouvelés',
		deleteStepFailMessage: 'Échec de la suppression de l’étape. Veuillez réessayer.',
		deleteDraftStepErrorMessage: 'L’étape créée à l’état d’ébauche n’a pas été supprimée. Pour supprimer cette étape, veuillez la sélectionner et recommencer la suppression.',
	},
	notEntered: 'Non saisie',
	estimateCategory: 'Catégorie d’estimation',
	noResultsFoundWithPeriod: 'Aucun résultat',
	noEstimateAvailable: 'Aucune estimation disponible',
	noRelatedObject: 'Aucun objet associé.',
	relateAnObject: 'Associer un objet',
	copyrightMessage: '© <année> Tous droits réservés.',
	leadsheet: 'Feuille maîtresse',
	controlName: 'Nom du contrôle',
	noControlAvailable: 'Aucun contrôle disponible',
	independenceError: 'Toutes les réponses incomplètes doivent avoir été traitées avant l’envoi de la confirmation d’indépendance. ',
	riskTypeNotAssociated: 'Le risque nouvellement ajouté ne correspond pas aux types de risque autorisés et n’apparaît donc pas ci-après. Ajouter un autre risque dont le type est autorisé ou sélectionner un risque dans la liste ci-après.',
	accountsAndRelatedEstimates: 'Comptes et estimations associées',
	noEstimatesAssociated: 'Aucune estimation associée',
	noAssertionsAvailable: 'Aucune assertion disponible',
	noAccountsOrDisclosuresAvailable: 'Aucun compte ni aucune information à fournir disponible',

	relateEstimateToRisk: {
		riskType: 'Type de risque',
		risk: 'Risque',
		hasestimate: "Has estimate?",
		accounts: 'Comptes ',
		isItRelevant: 'Le risque est-il pertinent?',
		assertions: 'Assertions',
		invalidRiskParentRiskErrMsg: 'Enregistrement introuvable. Veuillez actualiser la page pour continuer.',
		noEstimate: 'Aucune estimation disponible',
		invalidRelateRiskOrEstimateRelationErrMsg: 'L’objet a déjà été associé. Veuillez actualiser la page pour continuer.',
		invalidUnRelateRiskOrEstimateRelationErrMsg: 'L’objet a déjà été dissocié. Veuillez actualiser la page pour continuer.'
	},

	savingChanges: 'Enregistrement des modifications en cours',
	showEstimateAccountsWithoutEstimates: 'Afficher les comptes d’estimation auxquels aucune estimation n’est associée',
	showEstimateSCOTsWithoutEstimates: 'Afficher les catégories d’opérations d’estimation importantes auxquelles aucune estimation n’est associée',
	manageSCOTs: 'Gérer les catégories d’opérations importantes',
	sCOTsAndRelatedEstimates: 'Catégories d’opérations importantes et estimations associées',
	relateEstimateToRiskNoDataMessage: 'Aucun enregistrement disponible. Veuillez lier au moins un compte et une assertion présentant un risque associé s’il y a lieu.',
	maps: 'Schémas',
	mapsUpbutSomeLoadingErrorMessage: 'Un problème technique est survenu qui empêche la fonction Schémas de fonctionner. Veuillez actualiser la page et réessayer.',
	mapsDownErrorMessage: 'La fonction Schémas n’est pas disponible pour le moment. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	financialStatements: 'États financiers',
	serviceGatewayAutomation: 'Service Gateway et automatisation',
	priorPeriodCategory: 'Catégorie de la période précédente',
	relatedAccountWithColon: 'Comptes connexes : ',
	noRelatedAccount: 'Aucun compte connexe',
	noRetionaleAvailable: 'Aucune raison disponible',
	leftNavIconApprovals: 'Approbations',
	editDuplicateSectionHeader: 'Modifiez les détails de la section et cliquez sur Enregistrer.',

	relatedEvidences: 'Associer les éléments probants',
	relatedEvidencesInstruction: 'Associer un élément probant à partir de cette mission.',
	relatedTemporaryFilesInstruction: 'Associer un document temporaire à partir de cette mission.',
	noDataLabel: 'Aucune donnée trouvée',
	editDuplicateSection: 'Modifier la section',
	showOnlyRelated: 'Afficher seulement les éléments associés',
	aiChatbot: 'EYQ Assurance Knowledge',
	StEntityNoRecords: 'Aucun compte ni aucune information à fournir n’est mappé à l’entité sélectionnée.',
	versionLabel: 'Version',
	relatedEstimates: 'Estimations associées',
	viewEvidenceRelatedToBody: 'View evidence related to the body',
	selectHeaderFromRail: 'Select a header from left navigation pane to proceed',
	manageITProcesses: 'Gérer les processus informatiques',
	rationaleForLR: 'Raison justifiant le choix de compte présentant un risque limité',
	rationaleForInsignificant: 'Raison justifiant le choix de compte négligeable',
	rationalIsMissing: 'Raison manquante.',
	craSummaryText1: 'Chaque compte important ou information à fournir importante doit être associé à au moins une assertion. Cliquez sur',
	scotDetails223: {
		relatedAccounts: 'Comptes associés',
		scotType: 'Type',
		manageScot: 'Gérer les catégories d’opérations importantes',
		editScot: 'Modifier les catégories d’opérations importantes',
		scotNotAvailableMessage: 'Les catégories d’opérations importantes ne sont pas disponibles pour ce document.',
		relatedScotNotAvailableMessage: 'Aucune catégorie d’opérations importante associée. Associez une catégorie d’opérations importante à partir de la page des attributs pour commencer.',
		risksDocumented: 'Risques consignés dans ce test de cheminement',
		risksAvailableHeader: 'Oui',
		risksNotAvailableHeader: 'Non',
		viewRelatedRisks: 'Afficher les risques associés',
		noRelatedAccountsMessage: 'Aucun compte associé'
	},

	scotDetails226: {
		noscotsidentified: 'No SCOTs have been identified'
	},

	scotDetails224: {
		riskRelatedWalkthrough: 'Risques associés dans ce test de cheminement',
		relatedToWTDocuments: 'Related to other WT documents',
		riskNotRelatedWalkthrough: 'Risques non associés dans ce test de cheminement',
		substantiveNotSufficient: 'Corroboration insuffisante',
		journalEntry: 'Écriture de journal',
		noDirectRiskSourcesAreAvailable: 'Aucun risque associé',
		scotNotAvailableMessage: 'Les catégories d’opérations importantes ne sont pas disponibles pour ce document.',
		relatedScotNotAvailableMessage: 'Aucune catégorie d’opérations importante associée. Associez une catégorie d’opérations importante à partir de la page des attributs pour commencer.',
		relatedDocuments: 'Related documents',
		risk: "Risk:",
		riskSpecialCircumstances: 'Risk special circumstances',
		relateInstructionText: "This risk has been identified in another SCOT.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		unrelateInstructionText: "This risk has been identified in the critical path of another walkthrough.  Selecting or unselecting a special circumstance here will also update the selection in the other walkthrough.  Are you sure you want to proceed?",
		concurrencyErrorMessage: "This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.",
	},
	ipe: 'IPE',
	scotSummary198: {
		noAccountsDisclosureCreated: 'Aucun compte important ni aucune information à fournir importante n’ont été créés.',
		noScotEstimateIdentified: 'Aucune catégorie d’opérations importante ou estimation n’a été identifiée.',
		noScotIdentified: 'Aucune catégorie d’opérations importante identifiée.',
		scots: 'Catégories d’opérations importantes',
		estimates: 'Estimations',
		errorMessage: 'Cette action n’a pas pu être réalisée. Veuillez actualiser la page et réessayer. Si l’erreur persiste, veuillez communiquer avec le Service de dépannage.',
		noResultsFound: 'Aucun résultat',
		searchAccounts: 'Rechercher des comptes',
		searchScotEstimates: 'Rechercher des catégories d’opérations importantes / estimations',
		accounts: 'Comptes',
		scotsOrEstimate: 'Catégories d’opérations importantes / Estimations',
		accountNotRelatedToScotValidation: 'Chaque compte important ou information à fournir importante doit être associé à au moins une catégorie d’opérations importante. Cochez une case pour associer une catégorie d’opérations importante pertinente à ce compte important ou cette information à fournir importante.',
		scotNotRelatedToAccountValidation: 'Chaque catégorie d’opérations importante doit être associée à au moins un compte important ou une information à fournir importante. Cochez une case pour associer cette catégorie d’opérations importante au compte important ou à l’information à fournir importante correspondant(e).',
		showValidations: 'Afficher les validations',
	},
	scotSummary225: {
		relatedScots: 'Catégories d’opérations importantes associées',
		relatedAcconts: 'Comptes associés',
		scotListHeader: 'Catégories d’opérations importantes et estimations',
		noScotsMessage: 'Chaque compte important ou information à fournir importante doit être associé à au moins une catégorie d’opérations importante. Sélectionnez une catégorie d’opérations importante existante à associer à ce compte important ou cette information à fournir importante.',
		noAccountsMessage: 'No significant accounts or disclosures have been created.',
		noAccountsAvailableOnSearch: 'No results found',
		relateAccounts: 'Relate accounts and disclosures',
		noAccountsCreated: 'No accounts have been created',
		noScotsCreated: 'No SCOTs have been created',
		relateScots: 'Relate SCOTs',
	},
	bodyUnavailableInCCP: 'Ce contenu n’est pas disponible à partir du portail client d’EY Canvas.',
	pyBalance: 'Solde de l’exercice précédent',
	cyBalance: 'Solde de l’exercice considéré',
	designationNotDefined: 'Désignation non définie',
	controlRiskAssessment: 'Évaluation du risque lié au contrôle',
	first: 'Premier',
	noImportedTrialBalance: 'Aucune balance des comptes importée.',
	placeHolderMessageWhenHelixMappingIsTrue: 'Cliquez sur {0} pour associer un nouvel analyseur.',
	documentPrintSuccess: 'Document print in progress. It may take up to ten minutes. Once completed, the print will be added to the temporary files.',
	documentPrintError: 'Échec de l’impression du document. Veuillez actualiser la page ou réessayer plus tard. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	backToEvidenceWarningMessage: 'This action could not be completed. Please refresh and try again. If issue persists, please contact the Help Desk.',
	rationaleMissingForLR: 'Each limited risk account shall have a rationale provided',
	rationaleMissingForIR: 'Each insignificant account shall have a rationale provided',
	craSummaryText2: ' Each account shall have designation determined. Click',
	contentDrivingEntity: 'Content driving entity',
	contentDrivingEntityPlaceholder: 'Content driving entity has not been selected',
	rationaleForPlaceholder: 'Provide rationale for this account designation',
	contentDrivingEntityRequired: 'Content driving entity (required)',
	refreshContentLayers: 'Refresh content layers',
	noAccessLabel: 'Unauthorized. Contact your administrator and try again.',
	copyForHelpDeskDetails: 'Copy for Help Desk details',
	copyForHelpDeskDetailsSuccess: 'Details copied to clipboard',

	//toast activity as guest user
	sharedGuidedworkflowEvidenceWarning: 'This is a shared Guided Workflow Activity. The objects and evidence exist in the original engagement and will not be added to this engagement upon unlink. See <a style="color: #467cbe" href="https://live.atlas.ey.com/#library/104?pref=20058/9/5" target="_blank">enablement here</a> for further details.',
	sharedGuidedworkflowResponseWarning: "This is a shared Guided Workflow Activity. The responses are being shared with other activities in the same workspace. View relationships by accessing the'Show relationships' menu item from this activity's summary section."
};

export const groupStructure = {
	createComponent: 'Nouvelle composante',
	deleteComponent: 'Supprimer la composante',
	manageComponents: 'Gérer les composantes',
	emptyComponents: 'Aucune composante n’a été créée. Créer une {newComponent} pour commencer.',
	scope: 'Étendue',
	role: 'Rôle',
	pointOfContact: 'Personne-ressource',
	linkRequest: 'Demande de lien',
	instructions: 'Instructions',
	instructionsSent: 'Instructions envoyées',
	status: 'État',
	createComponentInstructionalText: "Entrez les détails de la composante ci-dessous et sélectionnez <b>'Enregistrer et fermer'</b> pour terminer. Pour créer une autre composante, sélectionnez <b>'Enregistrer et créer une autre composante'</b>. ",
	componentName: 'Nom de la composante',
	region: 'Zone',
	notUsingCanvas: 'N’utilisant pas EY Canvas',
	referenceOnly: 'Référence seulement',
	saveAndCreateAnother: 'Enregistrer et créer une autre composante',
	dueDate: 'Date d’échéance',
	components: 'Composantes',
	allocation: 'Distribution',
	documents: 'Evidence',
	discussions: 'Discussions',
	EDAP: 'Points de discussion et d’approbation par les cadres (EDAP)',
	siteVisits: 'Visites sur place',
	reviewWorkComponent: 'Travaux d’examen réalisés /Travaux réalisés dans le cadre de l’examen',
	other: 'Autre',
	significantUpdates: 'Mises à jour importantes',
	executionComplete: 'Exécution terminée',
	gaRoleTypesLabel: [{
		id: 1,
		displayName: 'Principal'
	},
	{
		id: 2,
		displayName: 'Régional'
	},
	{
		id: 3,
		displayName: 'Composante'
	}
	],
	gaLinkStatusLabel: [{
		id: GALinkStatus.NotSent,
		displayName: 'Envoyer'
	},
	{
		id: GALinkStatus.Sent,
		displayName: 'Envoyé/Non accepté'
	},
	{
		id: GALinkStatus.ComponentNotUsingCanvas,
		displayName: 'N’utilisant pas EY Canvas'
	},
	{
		id: GALinkStatus.ReferenceOnly,
		displayName: 'Référence seulement'
	},
	{
		id: GALinkStatus.Accepted,
		displayName: 'Accepté'
	},
	{
		id: GALinkStatus.Rejected,
		displayName: 'Rejeté'
	},
	{
		id: GALinkStatus.Unlinked,
		displayName: 'Non lié'
	},
	{
		id: GALinkStatus.Pending,
		displayName: 'Envoyé/Non accepté'
	}
	],
	notAvailable: 'Non disponible',
	search: labels.searchPlaceholder,
	noResultsFound: 'Aucun résultat.',
	noComponentsFound: 'Aucune composante trouvée.',
	contentSwitcher: [{
		id: gaRoleTypes.primary,
		displayName: 'Principal'
	},
	{
		id: gaRoleTypes.component,
		displayName: 'Composante'
	},
	{
		id: gaRoleTypes.regional,
		displayName: 'Zone'
	}
	],
	gaRegionTypesLabel: {
		id: gaRegion.notApplicable,
		displayName: 'Sans objet'
	},
	//TODO: To be removed
	pointOfContactValues: [{
		id: pointOfContactTypes.EYcontact,
		displayName: 'Personne-ressource d’EY'
	},
	{
		id: pointOfContactTypes.externalContact,
		displayName: 'Personne-ressource externe'
	}
	],
	saveAndClose: labels.modalSaveAndClose,
	cancelBtn: labels.modalCancelTitle,
	gaScopesValues: [{
		id: gaScopeType.full,
		displayName: 'Audit complet'
	},
	{
		id: gaScopeType.specific,
		displayName: 'Audit spécifique'
	},
	{
		id: gaScopeType.specifiedAuditProcedures,
		displayName: 'Procédures spécifiées'
	},
	{
		id: gaScopeType.review,
		displayName: 'Revue'
	}
	],
	edit: labels.edit,
	delete: labels.delete,
	tooltipIcon: 'Sélectionnez cette option si la composante n’utilise pas EY Canvas pour recevoir les instructions pour l’audit du groupe et envoyer les livrables interbureaux.',
	tooltipReferenceIcon: 'Les composantes désignées comme <b>Référence seulement</b> sont utilisées à des fins d’organisation seulement. Ces missions avec composante ne recevront pas les demandes de lien, les instructions ou les tâches, et cette équipe de mission principale ne recevra pas non plus de tâche pour le groupe.',
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	modalConfirmBtnLabel: labels.confirmLabel,
	clear: 'effacer',
	clearUpper: labels.clear,
	nameOrEmail: 'Entrer l’adresse électronique de la personne-ressource d’EY',
	editComponent: 'Modifier la composante',
	editComponentInstructionalText: 'Modifier les détails de la composante ci-dessous et sélectionner <b>\’Enregistrer\’</b> pour terminer.',
	linkAlreadyAcceptedInfo: 'Seul le champ de l’adresse courriel peut être modifié car la demande de lien a déjà été envoyée à l’équipe affectée à l’audit de la composante.',
	sendAll: 'Tout envoyer',
	send: 'Envoyer',
	resend: 'Renvoyer',
	scopeAndStrategy: 'Étendue et stratégie',
	execution: 'Exécution',
	conclusion: 'Conclusion',
	reportingForms: 'Formulaires de rapport',
	manageGroupPermission: 'Vous devez disposer de l’autorisation <b>Gérer le groupe</b> pour effectuer cette action. Veuillez demander l’autorisation <b>Gérer le groupe</b> auprès de l’administrateur de la mission.',
	manageComponentModalDesc: 'Créer de nouvelles composantes ou modifier et supprimer les composantes existantes ci-dessous.',
	editLinkInfo: 'Seul le champ de l’adresse courriel peut être modifié car la demande de lien a déjà été envoyée à l’équipe affectée à l’audit de la composante.',
	invalidPointOfContact: 'Il faut indiquer une personne-ressource pour envoyer une demande de lien. Modifier la composante pour y ajouter une personne-ressource.',
	manageComponentModalActions: 'Actions',
	manageComponentModalComponents: 'Composantes',
	manageComponentModalDelete: 'Supprimer',
	noThereAtLeastOneComponentToSendAll: 'Il n’y a aucune composante dont l’état permet d’envoyer une demande de lien. Les actions <b>Envoyer</b> ou <b>Renvoyer</b> doivent pouvoir être sélectionnées dans l’état de la composante pour pouvoir envoyer une demande de lien.',
	showKnowledgeDescription: 'Afficher le titre et la description obtenus de la base de connaissances',
	hideKnowledgeDescription: 'Masquer le titre et la description obtenus de la base de connaissances',
	instructionName: 'Entrer le nom de l’instruction',
	instructionDescriptionPlaceholder: 'Entrer la description de l’instruction',
	selectDueDate: 'Date d’échéance (obligatoire)',
	show: 'Afficher',
	allocationHeader: 'Distribution',
	allocationInstructionForKnowledge: 'Les instructions de la base des connaissances ne peuvent être attribuées que par étendue. Parmi les étendues figurant ci-dessous, sélectionner celles qui sont pertinentes : ',
	allocationInstructionForCustom: 'Les instructions personnalisées peuvent être attribuées par étendue ou par composante. Sélectionner les instructions ci-dessous et les attribuer aux étendues ou aux composantes pertinentes.',
	allocateScope: 'Affecter à des étendues de travaux',
	allocateComponent: 'Affecter à des composantes',
	pillScopesPlural: 'étendues',
	pillScopesSingular: 'étendue',
	pillComponentsPlural: 'composantes',
	pillComponentsSingular: 'composante',
	selectScopesPlaceholder: 'Sélectionner les étendues',
	selectComponentsPlaceholder: 'Sélectionner les composantes',
	searchNoResultFoundText: labels.searchNoResultFoundText,
	newCustomInstruction: 'Nouvelle instruction personnalisée',
	instructionNameNewCustomInstruction: 'Nom de l’instruction',
	addCustom: 'Ajouter une instruction personnalisée',
	custom: 'Personnalisé',
	required: 'Obligatoire',
	remove: 'Retirer',
	selectAll: 'Tout sélectionner',
	unselectAll: 'Tout désélectionner',
	lowerPoC: 'personne-ressource',
	editPoCTooltip: 'Entrée invalide ou aucune personne-ressource. Modifier la personne-ressource pour envoyer une demande de lien.',
	recomendationType: [{
		id: 1,
		label: 'Obligatoire'
	},
	{
		id: 2,
		label: 'Facultatif'
	},
	{
		id: 3,
		label: 'Sans objet'
	}
	],
	confirmLabel: labels.confirmLabel,
	deleteComponentInstructionalText: '<b>Voulez-vous vraiment supprimer cette composante de la structure du groupe?</b><br />Si la composante est supprimée, le lien vers cette composante sera retiré et l’échange de documents ne sera plus possible. De plus, toutes les associations entre la composante et ses comptes et instructions seront supprimées.',
	noActivitiesAvailable: 'Aucune activité disponible.',
	relatedComponents: 'Composantes associées',
	relatedComponentsSingular: 'composante associée',
	relatedComponentsPlural: 'composantes associées',
	publish: 'Publier',
	publishModalHeader: 'Publier les modifications',
	publishChangesInstructional: '<b>Voulez-vous vraiment publier les modifications apportées à la synthèse des instructions relatives à l’audit de groupe?</b><br />L’ensemble d’instructions relatives à l’audit de groupe précédent sera écrasé. Une fois les modifications publiées, les instructions à jour peuvent être envoyées à partir de la synthèse des instructions relatives à l’audit de groupe.',
	publishManageGroupPermission: 'Vous devez disposer de l’autorisation Gérer le groupe pour effectuer cette action. Veuillez demander l’autorisation auprès de l’administrateur de la mission.',
	lastPublished: 'Dernière publication : ',
	publishChangesNotAvailable: 'Pas encore disponible',
	noRecordsFound: labels.noRecordsFound,
	deleteInstruction: 'Supprimer l’instruction',
	deleteInstructionInstructionalText: '<b>Voulez-vous vraiment supprimer cette instruction? </b><br />Cette action ne peut être annulée.',
	sendInstructionsTitle: 'Envoyer les instructions',
	sendInstructionsInstructionalText: 'Assurez-vous que les dernières instructions ont été publiées en cliquant sur « Publier » dans la page correspondante. Passez ensuite en revue les instructions à l’intention de l’équipe affectée à l’audit de la composante ci-dessous et sélectionnez « Envoyer » pour lui transmettre les instructions.',
	instructionsAlreadySent: 'La version la plus récente des instructions a déjà été envoyée.',
	missingDueDates: 'Date d’échéance du formulaire de rapport manquante.',
	createInstructionsModalButton: 'Créer des instructions',
	createInstructionsModalActionToastMessageStart: 'Instructions relatives à l’évaluation des risques au niveau du groupe manquantes pour',
	createInstructionsModalActionToastMessageEnd: 'les composantes  // if we are naming components, the name should follow the word "components" after a space.',
	createInstructionsModalDescription: 'Instructions relatives à l’évaluation des risques au niveau du groupe manquantes pour les missions d’audit complet et d’audit spécifique des composantes suivantes. Pour créer une instruction relative à l’évaluation des risques au niveau du groupe pour chaque composante figurant dans la liste ci-après, sélectionner <b>Créer</b>.',
	createInstructionsModalScope: 'Étendue des travaux',
	createInstructionsModalHeader: 'Créer des instructions',
	createInstructionsModalmodalConfirmBtnLabel: 'Créer',
	createInstructionsModalmodalCancelBtnLabel: 'Annuler',
	createInstructionsModalmodalCloseBtnTitletip: 'Fermer',
	createInstructionsModalNewGraInstructionDescription: 'Voici l’évaluation des risques pour les comptes se rapportant à votre composante. Veuillez la passer en revue et vérifier que les comptes et les risques qui y figurent sont inclus dans votre mission. Tout autre risque identifié par l’équipe locale ou tout point de désaccord de sa part doit être communiqué à l’équipe principale de sorte que l’évaluation des risques soit révisée comme il se doit par l’équipe principale et par l’équipe affectée à l’audit de la composante.',
	createInstructionsModalErrorMessage: 'Échec de la création de l’instruction relative à l’évaluation des risques au niveau du groupe pour les composantes suivantes : <b>{0}</b>. Veuillez actualiser la page et réessayer.',
	createInstructionsDuplicatedModalErrorMessage: 'Échec de la création de l’instruction relative à l’évaluation des risques au niveau du groupe. Le nom de l’instruction ne peut pas être copié.',
	gaLinkActionTooltip: {
		NotUsingCanvasLabel: 'N’utilisant pas EY Canvas',
		NotUsingCanvas: 'En cliquant sur <b>Envoyer</b>, vous créerez des tâches pour l’audit de groupe (équipe principale) applicables à cette composante, mais aucune instruction ne sera envoyée.',
		NotLinkedLabel: 'Non associées',
		NotLinked: 'La demande de lien n’a pas été envoyée à <br/> l’équipe affectée à l’audit de la composante. Envoyer la demande de lien<br/> pour pouvoir envoyer les instructions.',
		Unlinked: 'Dissociées'
	},
	viewHistory: 'Afficher l’historique',
	viewSentInstructionsTitle: 'Afficher les instructions envoyées',
	save: labels.saveLabel,
	cancel: labels.cancelLabel,
	viewHistoryInstructionalText: 'Sélectionnez les instructions pour afficher les versions précédentes des instructions envoyées à l’équipe affectée à l’audit de la composante.',
	viewHistorySelectInstruction: 'Sélectionner les instructions',
	viewHistoryDateSent: 'Date d’envoi : ',
	viewHistoryStatus: 'État : ',
	viewHistoryStatusAccepted: 'Accepté',
	viewHistoryStatusPending: 'En attente',
	viewHistoryStatusRejected: 'Rejeté',
	viewHistoryStatusSystemError: 'System error',
	viewHistorySelectVersion: 'Sélectionner une version',
	noAccountsFound: 'Aucun compte ni aucune information à fournir n’a été trouvé dans cette mission ou dans d’autres missions. <br />Sélectionner {link} pour modifier des informations à fournir ou des comptes existants ou en créer de nouveaux.',
	generalCommunications: 'Communications générales',
	reportingDeliverables: 'Livrables ‒ Rapports',
	changesPublishedNotSent: 'Modifications non envoyées',
	changesPublishedBrNotSent: 'Modifications<br/>non envoyées',
	changesPublishedNotSentYes: 'Oui',
	deleteSubScopeInstructionalTextModal: 'Voulez-vous vraiment supprimer la <br/> sous-étendue sélectionnée?',
	deleteSubScopeTitleModal: 'Supprimer la sous-étendue',
	riskAssessmentModal: {
		headerText: 'Évaluation des risques',
		modalCloseBtnTitletip: labels.close,
		manageAndDisclosures: 'Lien vers Gérer les comptes et les informations à fournir',
		next: 'Composante suivante',
		back: 'Composante précédente'
	},
	riskAssessment: 'Évaluation des risques',
	preview: 'Aperçu',
	accountsAndDisclosureSummary: 'Compte et information à fournir',
	noAccountSnapshotPlaceholder: 'Aucune donnée relative au compte à afficher pour cette composante.',
	createOversightProjectButtonLabel: 'Créer un projet dans le module Surveillance d’EY Canvas',
	createOversightProjectTitle: 'Voulez-vous créer un projet dans le module Surveillance d’EY Canvas avec cette mission principale?',
	createOversightProjectDescription: 'Les missions EY Canvas de l’équipe zonale et/ou des équipes affectées à l’audit des composantes identifiées dans la structure de cet audit de groupe seront automatiquement intégrées lors de la configuration du projet dans le module Surveillance d’EY Canvas.',
	createOversightModalHeader: 'Nom du projet du module Surveillance d’EY Canvas',
	createOversightModalDescription: 'Entrez le nom du projet du module Surveillance d’EY Canvas.',
	createOversightModalTextLabel: 'Nom du projet',
	projectRedirectionButtonLabel: 'Projets du module Surveillance d’EY Canvas',
	projectAssociationTextLabel: 'Des projets du module Surveillance d’EY Canvas sont connectés à cette mission.',
	sendLinkDisableTooltip: 'Cette mission a été copiée, y compris les composantes dans le flux pour l’audit de groupe. Les liens ne peuvent pas être renvoyés. Créez une nouvelle composante et envoyez un lien, au besoin.',
	instructionsCannotBeSentUntilPublished: 'Instructions cannot be sent until they are published.'
};

export const groupInvolvement = {
	NoComponentsAvailables: 'Aucune composante n’a été créée. <b>Gérer les composantes</b> pour commencer.',
	GroupInvolvementToastMsgStart: 'Formulaire relatif à la participation à un audit de groupe manquant pour',
	GroupInvolvementToastMsgEnd: 'les composantes',
	CreateGroupInvolvementHeader: 'Créer un ou des formulaires de participation',
	GroupInvolvementInstructionalText: 'Aucun formulaire relatif à la participation à un audit groupe n’est attribué aux composantes suivantes.<br/> En sélectionnant &#39;<b>Créer</b>&#39;, un formulaire relatif à la participation à un audit groupe sera créé pour chaque composante ci-dessous.',
	createGroupInvolvementDocumentErrorMessage: 'Échec de la création du document relatif à la participation à un audit groupe pour les composantes suivantes : <b>{0}</b>. Veuillez actualiser la page et réessayer.',
	createGroupInvolvementDocumentSuccessMessage: 'Le ou les formulaires relatifs à la participation à un audit de groupe ont été créés avec succès. Actualisez la page dans 30 secondes pour afficher les documents disponibles.',
	involvementTypePlanned: 'Type de participation prévue',
	significantUpdatesToPlannedInvolvement: 'Mises à jour importantes à la participation prévue',
	executionComplete: 'Exécution terminée',
	generateGroupInvolvementCommunications: 'Imprimer un ou des formulaires de participation',
	generateGroupInvolvementInstructionalText: 'Des formulaires relatifs à la participation à un audit de groupe sont associés aux composantes suivantes. Sélectionner, ci-après, les formulaires de participation à un audit de groupe des composantes devant être inclus dans un seul document ci-après.<br /><br /> Après avoir sélectionné les composantes, cliquer sur <b>&#39;Créer&#39;</b> pour générer un document relatif à la participation à un audit de groupe comportant chaque document relatif à la participation à un audit de groupe des composantes.',
	componentTeams: 'Équipes affectées à l’audit des composantes',
	noComponentsSelectedErrorMessage: 'Sélectionner les composantes pour créer une communication relative à la participation à un audit de groupe',
	documentName: 'Paquet relatif à la participation à un audit de groupe pour la tâche {taskName}',
	selectAll: groupStructure.selectAll,
	unselectAll: groupStructure.unselectAll,
	modalConfirmBtnLabel: groupStructure.createInstructionsModalmodalConfirmBtnLabel,
	modalCancelBtnLabel: groupStructure.cancelBtn,
	modalCloseBtnTitletip: groupStructure.modalCloseBtnTitletip
};

export const itPlanning = {
	supportingITColumnsHeaders: {
		applicationTool: {
			name: 'Applications/Tools'
		},
		network: {
			name: 'Réseaux'
		},
		database: {
			name: 'Bases de données'
		},
		operatingSystem: {
			name: 'Systèmes d’exploitation'
		}
	},
	relatedITProcessesColumnsHeaders: {
		relatedITProcess: 'Related IT processes',
		category: 'Category'
	},
	itPlanningPlaceholders: {
		smartEvidenceSourceEntityId: 'La technologie associée n’est pas disponible pour ce document.',
		smartEvidenceSourceId: 'Aucun objet associé. Associer un objet pour commencer.',
	},
	relatedITProcessesPlaceholders: {
		smartEvidenceSourceEntityId: 'Related IT process not available for this document',
		smartEvidenceSourceId: 'Aucun objet associé. Associer un objet pour commencer.',
		relatedITProcessEmpty: 'No IT process related to the technology'
	},
	noTechnologiesIdentified: 'Aucune technologie n’a été identifiée.',
	supportingITEmpty: 'Aucune application ni aucun outil à l’appui pour la technologie.',
	supportingITNetworkEmpty: 'Aucun réseau compatible reconnu pour la technologie.',
	searchPlaceholder: 'Rechercher',
	newTechnology: 'Nouvelle technologie',
	noSupportingDatabases: 'Aucune base de données à l’appui pour la technologie.',
	createEntityFormDocument: 'Créer un document',
	noSupportingOperatingSystem: 'No supporting operating systems related to the technology',
	manageTechnology: 'Manage technology'
};

export const itRiskFactors = {
	accepted: 'Accepté',
	rejected: 'Rejeté',
	accept: 'Accepter',
	reject: 'Rejeter',
	rejectionRationale: 'Raison du rejet',
	rejectionCategory: 'Catégorie de rejet',
	rejectionRationaleRequired: 'Rejection rationale (required)',
	rejectionCategoryRequired: 'Rejection category (required)',
	riskName: 'Risk name',
	smartEvidenceValidations: {
		smartEvidenceSourceEntityId: 'Facteurs de risque non disponibles pour ce document.',
		smartEvidenceSourceId: 'No related object. Relate an object to get started.'
	},
	manageChangePlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'Tous les risques ont été rejetés.'
	},
	manageOperationsPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	},
	manageAccessPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	},
	SDLCPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'Tous les risques ont été rejetés.'
	},
	manageSecuritySettingsPlaceholders: {
		empty: 'No risk factor associated to the technology',
		emptyAccepted: 'All IT risk factors have been rejected'
	}
};

export const rejectionTypeResource = [{
	id: rejectionType.itRiskOther,
	label: 'ITRisk Other'
},
{
	id: rejectionType.itRiskOption2,
	label: 'ITRisk - "Option 2"'
},
{
	id: rejectionType.itRiskOption3,
	label: 'ITRisk - "Option 3"'
}
];

export const sampleList = {
	newSample: 'Nouvel échantillon',
	createSampleModalDescription: "Entrer les détails relatifs à l’échantillon ci-dessous et sélectionner'<b>{0}</b>' pour terminer. Pour créer un autre échantillon, sélectionner'<b>{1}</b>'. ",
	saveAndCreateAnother: 'Enregistrer et créer une autre composante',
	saveAndClose: 'Enregistrer et fermer',
	sampleDescription: 'Description de l’échantillon (obligatoire)',
	sampleDate: 'Date de l’échantillon (obligatoire)',
	sampleListId: 'Identifiant de la liste d’échantillons',
	ok: 'Ok',
	addSample: 'Ajouter un échantillon',
	cancel: 'Annuler',
	saveAndCloseForHeader: 'Enregistrer et fermer',
	saveAndCreateAnotherHeader: 'Enregistrer et créer un autre échantillon',
	required: 'Obligatoire',
	description: 'Description de l’échantillon',
	date: 'Date',
	attributeStatus: 'Attributs',
	tags: 'Balises',
	open: 'Ouvrir',
	notApplicableLabel: 'Sans objet',
	notPresent: 'Absente',
	present: 'Présente',
	pagingShowtext: 'Afficher',
	placeHolderMessage: 'Aucun échantillon disponible. Cliquer {clickHere} pour commencer.',
	noSampleListAvailable: 'Aucune liste d’échantillons disponible',
	editSample: 'Modifier l’échantillon',
	editSampleDescription: "Modifier les détails relatifs à l’échantillon ci-dessous et sélectionner'<b>{0}</b>' pour terminer. ",
	editSampleSave: 'Enregistrer',
	sampleCanNotBeCreated: 'Impossible de créer des échantillons pour ce document.',
	noRelatedObject: 'Aucun objet associé. Associer un objet pour commencer.',
	noResultsFound: 'Aucun résultat'
};

export const AdditionDocumentationLabels = {
	addAdditionalDocumentation: 'Ajouter des informations supplémentaires',
	editAdditionalDocTitle: 'Modifier les informations supplémentaires',
	removeAdditionalDocumentation: 'Supprimer des informations supplémentaires',
	cancel: 'Annuler',
	save: 'Enregistrer',
	of: 'sur',
	additionalDocTitlePlaceholder: 'Informations supplémentaires (obligatoires)',
	additionalDocTitle: 'Informations supplémentaires (obligatoires)',
	remove: 'Retirer',
	enterAdditionalDocTitle: "Entrez des informations supplémentaires ci-dessous et sélectionnez <b>'{0}'</b> pour terminer. ",
	editAdditionalDocDesc: "Modifiez les informations supplémentaires ci-dessous et sélectionnez <b>'{0}'</b> pour terminer. ",
	characters: 'caractères',
	required: 'Obligatoire',
	descriptionMaxLengthError: 'La réponse dépasse la longueur maximale permise.',
	attributeIndexLabel: 'Référence de l’attribut'
};

export const sampletAttributeConstants = [{
	id: 1,
	label: 'Ouvert(s)'
},
{
	id: 3,
	label: 'Présent'
},
{
	id: 7,
	label: 'Présent avec des commentaires'
},
{
	id: 5,
	label: 'Absent'
},
{
	id: 4,
	label: 'Sans objet'
},
];

export const groupInstructions = {
	ALRAPackageModalTitle: 'Nom du paquet Évaluation des risques au niveau du compte',
	ALRAPackageModalInstructionalText: 'Entrez le nom du paquet Évaluation des risques au niveau du compte que vous ajoutez aux éléments probants.',
	ALRAPackageModalNameField: 'Entrer le nom ',
	ALRAPackageSuccessToastMessage: 'Le processus de création du paquet est en cours. Le processus peut prendre jusqu’à 10 minutes.',
	ALRAPackageInProgressToastMessage: 'Le processus de création du paquet est en cours. Le processus peut prendre jusqu’à 10 minutes.',
	delete: labels.delete,
	deleteSectionModalTitle: labels.deleteSection,
	deleteSectionInstructionalText: '<b>Voulez-vous vraiment supprimer la section?</b><br />Cette action ne peut pas être annulée.',
	deleteSectionTooltipText: 'Il faut d’abord supprimer les instructions<br />pour pouvoir supprimer la section.',
	modalConfirmBtnLabel: labels.confirmLabel,
	modalCancelBtnLabel: labels.cancelLabel,
	modalCloseBtnTitletip: labels.closeLabel,
	missing: 'Manquantes',
	sendAllModalTriggerButton: 'Tout envoyer',
	sendAllModalTooltipText: 'Aucune instruction à envoyer aux équipes affectées à l’audit des composantes n’est disponible.',
	publishModalTooltipText: 'Les instructions relatives à l’audit de groupe doivent être publiées avant d’être envoyées. Une fois que les instructions ont été publiées, toute modification qui y est apportée est enregistrée comme nouvelle version des instructions et annule et remplace la version précédente. Ces nouvelles instructions peuvent alors être envoyées aux équipes affectées à l’audit des composantes.',
	sendAllModalErrorMessage: 'Group instructions for the following Components were not sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk. <br /> <b>{0}</b>',
	sendAllModalHeaderText: 'Envoyer toutes les instructions',
	sendAllModalConfirmBtnLabel: 'Envoyer',
	sendAllModalCancelBtnLabel: 'Annuler',
	sendAllModalCloseBtnTitletip: 'Fermer',
	sendAllModalDescription: 'En sélectionnant <b>Envoyer</b>, les instructions seront envoyées aux équipes affectées à l’audit des composantes suivantes.',
	generateGroupRiskAssessmentCommunications: 'Générer l’évaluation du risque au niveau du compte pour l’audit de groupe',
	bulkALRAPackageName: 'Paquet d’évaluation des risques au niveau du compte {instructionName}',
	groupInstructionSummaryReport: 'Rapport sur la synthèse des instructions relatives à l’audit de groupe',
	groupInstructionSummaryReportTitletip: 'Afficher et exporter les détails des instructions relatives à l’audit de groupe, l’historique des instructions et les modifications apportées au mappage des comptes des composantes.',
	exportGroupRiskAssessment: 'Exporter la synthèse',
	reportingDeliverables: groupStructure.reportingDeliverables,
	groupRiskAssessment: 'Évaluation des risques au niveau du groupe'
};

export const sectionTitles = [{
	id: KnowledgeSectionIds.GeneralCommunications,
	sectionTitle: groupStructure.generalCommunications
},
{
	id: KnowledgeSectionIds.ScopeOfWork,
	sectionTitle: 'Étendue des travaux'
},
{
	id: KnowledgeSectionIds.ReportingForms,
	sectionTitle: groupStructure.reportingDeliverables
},
{
	id: KnowledgeSectionIds.ProceduresPerformedCentrally,
	sectionTitle: 'Procédures centralisées'
},
{
	id: KnowledgeSectionIds.GroupRiskAssessment,
	sectionTitle: groupInstructions.groupRiskAssessment
},
{
	id: KnowledgeSectionIds.OtherCommunications,
	sectionTitle: 'Autres communications'
}
];

export const groupAuditToolbar = {
	search: labels.placeholderForSearch
};

export const AccountType = [{
	id: 1,
	accounttypename: 'Compte important'
},
{
	id: 2,
	accounttypename: 'Compte présentant un risque limité'
},
{
	id: 3,
	accounttypename: 'Compte négligeable'
},
{
	id: 4,
	accounttypename: 'Compte négligeable'
},
{
	id: 5,
	accounttypename: 'Information à fournir importante'
}
];

export const PriorityType = [{
	value: 1,
	label: 'Faible'
},
{
	value: 2,
	label: 'Modéré'
},
{
	value: 3,
	label: 'Élevée'
},
{
	value: 4,
	label: 'Critique'
}
];

export const AccountSummaryAccountType = [{
	id: '0',
	accounttypename: 'Tous les comptes'
},
{
	id: '1',
	accounttypename: 'Comptes importants'
},
{
	id: '2',
	accounttypename: 'Comptes présentant un risque limité '
},
{
	id: '3',
	accounttypename: 'Comptes négligeables'
},
{
	id: '4',
	accounttypename: 'Compte − Autres'
},
{
	id: '5',
	accounttypename: 'Informations à fournir importantes '
}
];

export const TaskStatus = [{
	id: 1,
	status: 'Ouverte'
},
{
	id: 2,
	status: 'En cours'
},
{
	id: 3,
	status: 'En cours de revue'
},
{
	id: 4,
	status: 'Terminée'
},
{
	id: 5,
	status: 'Supprimée'
}
];

export const reviewNoteLabels = {
	/*Review Notes*/
	emptyNoteDetailsMessage: 'Sélectionnez une note pour afficher les détails. Pour activer les contrôles en bloc, appuyez sur la touche Ctrl ou Maj et sélectionnez plusieurs notes de revue. Si vous souhaitez travailler sur une seule note, vous devez la sélectionner dans la liste.',
	documentReviewNotesLabel: 'Notes de document',
	addNewReviewNoteButtonText: 'Ajouter une note',
	noNotesAssociatedWithDocumentLabel: 'Aucune note n’est associée à ce document',
	allNotesLabel: 'Toutes les notes',
	charactersLabel: 'caractères',
	myNotesLabel: 'Mes notes',
	showClearedLabel: 'Afficher les éléments réglés',
	showClosedLabel: 'Afficher les éléments fermés',
	toLabel: 'à',
	toUserLabel: 'À',
	ofLabel: 'sur',
	textAreaPlaceholder: 'Entrer une note',
	addNewNoteModalClose: 'Fermer',
	addNewNoteModalTitleLabel: 'Ajouter une nouvelle note',
	editNoteModalTitleLabel: 'Modifier la note',
	deleteIconHoverText: 'Supprimer',
	deleteIconModalAcceptText: 'Supprimer',
	deleteIconModalConfirmMessage: 'Voulez-vous vraiment supprimer votre réponse à cette note?',
	deleteIconModalConfirmMessageParent: 'Voulez-vous vraiment supprimer la note sélectionnée?',
	deleteIconModalTitleLabel: 'Supprimer la note',
	deleteReplyIconModalTitle: 'Supprimer la réponse',
	emptyRepliesMessage: 'Aucune réponse pour l’instant',
	replyInputPlaceholder: 'Répondre à cette note',
	replyText: 'Texte de réponse',
	editReplyModelTitle: 'Modifier la réponse',
	noteDueDateLabel: 'Échéance : ',
	fromUserLabel: 'De',
	priorityLabel: 'Priorité',
	dueDateLabel: 'Date d’échéance',
	dueLabel: 'Échéance',
	status: 'État',
	noteModifiedDateLabel: 'Modifiée: ',
	cancelLabel: 'Annuler',
	saveLabel: 'Enregistrer',
	clearedBy: 'Réglée par',
	closedBy: 'Fermée par',
	reopenedBy: 'Rouverte par',
	reply: 'Répondre',
	editIconHoverTextLabel: 'Modifier',
	required: 'Obligatoire',
	closeTitle: 'Fermer',
	otherEngagementNotes: 'Autres notes relatives à la mission',
	closeLabel: 'Fermer',
	showMore: 'Afficher plus',
	showLess: 'Afficher moins',
	showMoreEllipsis: 'Afficher plus…',
	showLessEllipsis: 'Afficher moins…',
	noResultFound: 'Aucun résultat',
	engagementNameLabel: 'Nom de la mission : ',
	drag: 'Glisser',
	formMaxLength: 'Le texte ne doit pas dépasser {number} caractères.',
	voiceNoteButtonLabel: 'Note vocale',
	stopRecordingButtonLabel: 'Arrêter',
	reopen: 'Rouvrir',
	noNotesFound: 'Aucune note trouvée',
	noNotesFoundInstructional: 'Veuillez laisser une note en utilisant les données ci-après. Attribuez la note à un utilisateur et indiquez la priorité ainsi que la date d’échéance.',
	microphoneBlockedMessage: 'Autorisez le navigateur à accéder à votre microphone afin d’utiliser les notes vocales. Si vous l’avez déjà fait, veuillez actualiser la page et réessayer.',
	microphoneBlockedOnVideoMessage: 'Autorisez le navigateur à accéder à votre microphone afin d’utiliser l’enregistrement d’écran. Si vous l’avez déjà fait, veuillez actualiser la page et réessayer.',
	notInMainWindowVoice: 'Les enregistrements vocaux ne sont pas autorisés dans le tiroir. Veuillez ouvrir le document dans un nouvel onglet pour effectuer cette action.',
	notInMainWindowScreen: 'Les enregistrements d’écran ne sont pas autorisés dans le tiroir. Veuillez ouvrir le document dans un nouvel onglet pour effectuer cette action.',
	voiceNoteNotAvailable: 'La note vocale et l’enregistrement d’écran ne sont pas disponibles dans le tiroir. Passez en mode plein écran pour utiliser ces fonctionnalités.',
	playButtonTitle: 'Lire',
	deleteButtonTitle: 'Supprimer',
	pauseButtonTitle: 'Pause',
	screenRecord: 'Enregistrement de l’écran',
	playbackReview: 'Lire l’enregistrement '
};

export const IndividualAccountAttributeLabels = {
	attributesNotAvailableForDocument: 'Les attributs de compte ne sont pas disponibles pour ce document.',
	noRelatedOnject: 'Aucun objet associé. Associer un objet pour commencer.',
	noAttributesAvailable: 'Aucun attribut disponible',
	noRisksAvailable: 'Aucun risque disponible',
	attributeStandardRomms: 'Attribuer les risques d’anomalies significatives standard',
	continueButtonTitle: 'Continuer',
	closeButtonTitle: 'Annuler',
	newAssertionModalPlaceholder: 'Les assertions suivantes seront marquées comme présentant un risque inhérent faible, alors qu’elles n’étaient pas considérées comme pertinentes auparavant. Voulez-vous continuer?',
	assertion: 'Assertion',
	inherentRiskType: 'Risque inhérent',
	assertionModalTitle: 'Nouvelle assertion',
	riskType: 'Faible'
};

export const entities = [{
	id: 0,
	name: 'Tout'
},
{
	id: 1,
	name: 'Document'
},
{
	id: 2,
	name: 'FeuilleMaîtresse'
},
{
	id: 3,
	name: 'Compte'
},
{
	id: 4,
	name: 'Catégorie d’opérations importante'
},
{
	id: 5,
	name: 'Processus informatique'
},
{
	id: 6,
	name: 'Plan de mission'
},
{
	id: 7,
	name: 'Risque'
},
{
	id: 8,
	name: 'Tâche'
},
{
	id: 9,
	name: 'Anomalie'
},
{
	id: 10,
	name: 'Déficience'
},
{
	id: 11,
	name: 'Composante pour l’audit de groupe'
},
{
	id: 12,
	name: 'Instructions de la composante pour l’audit de groupe'
},
{
	id: 13,
	name: 'Éléments probants associés à la composante pour l’audit de groupe'
},
{
	id: 14,
	name: 'Étendue pour l’audit de groupe'
},
{
	id: 15,
	name: 'Instructions de l’équipe principale pour l’audit de groupe'
},
{
	id: 16,
	name: 'Équipe principale pour l’audit de groupe'
},
{
	id: 17,
	name: 'Demande client'
},
{
	id: 18,
	name: 'Erreur possible'
},
{
	id: 19,
	name: 'Contrôle'
},
{
	id: 20,
	name: 'Application informatique'
},
{
	id: 21,
	name: 'Formulaire Canvas'
},
{
	id: 22,
	name: 'Section de formulaire'
},
{
	id: 23,
	name: 'Corps de texte du formulaire'
},
{
	id: 24,
	name: 'Assertion'
},
{
	id: 25,
	name: 'Mission client'
},
{
	id: 26,
	name: 'Groupe de clients'
},
{
	id: 27,
	name: 'Balise de la mission'
},
{
	id: 28,
	name: 'Mission'
},
{
	id: 29,
	name: 'En-tête du formulaire'
},
{
	id: 30,
	name: 'État du formulaire'
},
{
	id: 31,
	name: 'Utilisateur de la mission'
},
{
	id: 32,
	name: 'Utilisateur du groupe de clients'
},
{
	id: 33,
	name: 'Référence PCB'
},
{
	id: 34,
	name: 'CGI'
},
{
	id: 35,
	name: 'Risque lié aux TI'
},
{
	id: 36,
	name: 'Nombre de jours total pour réaliser la revue'
}
];

export const PaceType = [{
	id: 1,
	paceTypename: 'Faible'
},
{
	id: 2,
	paceTypename: 'Modéré'
},
{
	id: 3,
	paceTypename: 'Élevé'
},
{
	id: 4,
	paceTypename: 'Surveillance étroite'
}
];

export const DocumentHelper = {
	401: 'L’opération ne peut pas être traitée pour le moment. Veuillez actualiser la page et réessayer. Si le problème persiste, communiquez avec le Service de dépannage.',
	413: 'Le document dépasse la taille de fichier maximale permise (250 Mo) et ne peut pas être téléversé. Veuillez diminuer la taille du fichier et réessayer.',
	412: 'Un document du même nom existe déjà dans cette mission',
	414: 'Le nom dépasse la longueur maximale (120 caractères).',
	4099: 'Un document ayant le même nom existe déjà',
	/*this is a hack as we dont always know why conflict happened.*/
	410: 'Ce document a été supprimé et ne peut donc pas être ouvert.',
	411: 'Les documents vides ne sont pas autorisés.'
};

export const Errors = {
	/*Doc Helper Custom Messages */
	0: 'Connexion perdue. Veuillez vous reconnecter et réessayez. Si le problème persiste, communiquez avec le Service de dépannage.',
	10: 'Un problème concernant EY Canvas Document Helper a été détecté. Cliquez sur <a style="color: #467cbe" href="https://eyt.service-now.com/kb_view.do?sysparm_article=KB0486774" target="_blank">ici</a> pour obtenir des instructions sur la façon de régler ce problème.',
	101: 'État de la mission non valide',
	102: 'Utilisateur de la mission valide introuvable',
	103: 'Information manquante : Conformité aux obligations en matière d’indépendance de l’utilisateur de la mission',
	104: 'Information manquante : Étendues requises dans Azure AD',
	105: 'Une erreur s’est produite lors de l’obtention des autorisations pour la mission. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	106: "Unauthorized. Contact your administrator and try again.",
	107: 'Utilisateur de la mission valide introuvable. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	108: 'Le profil de la mission est incomplet. Veuillez aller à la page d’accueil et créer le profil.',
	303: 'Un document ayant le même nom est en cours de téléchargement.',
	403: 'Access to this document is not available.  If this document is shared ensure you have access to the source engagement.  Refresh the page and try again.  If the error persists contact the Help Desk.',
	406: 'Le document ne peut être vide.',
	412: 'Un document du même nom existe déjà dans cette mission.',
	414: 'Le nom dépasse la longueur maximale (120 caractères).',
	411: 'Les documents vides ne sont pas autorisés.',
	500: 'Connexion perdue. Veuillez vous reconnecter et réessayez. Si le problème persiste, communiquez avec le Service de dépannage.',
	600: 'L’opération ne peut pas être traitée pour le moment. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	601: 'Erreur de téléchargement de la capture d’écran. Veuillez actualiser la page et réessayer. Si le problème persiste, communiquez avec le Service de dépannage.',
	602: 'Le document est déjà en mode collaboration.',
	935: 'L’utilisateur n’a pas les autorisations suffisantes pour effectuer l’opération.',
	zip: 'Le fichier .zip ne peut pas être téléchargé parce qu’il contient au moins un type de fichier qui n’est pas pris en charge ou il contient trop de fichiers .zip incorporés.',
	401000: 'Un changement de réseau a été détecté. Veuillez actualiser la page pour continuer.',

	/*Accounts*/
	1001: 'Échec de la demande de création du compte',
	1002: 'Information manquante : nom du compte',
	1003: 'The selected Account has been deleted. Close this modal to see the updated list.',
	1004: 'Aucun résultat. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	1005: 'Identifiant de la mission non valide',
	1006: 'Type de compte non valide',
	1007: 'Type d’état non valide',
	1008: 'Le compte sélectionné a été supprimé. Veuillez fermer cette fenêtre modale pour voir la liste à jour.',
	1009: 'Échec de la demande de récupération par identifiant de compte',
	1010: 'Échec de la demande de récupération par identifiant de mission',
	1011: 'Échec de la demande de récupération de la synthèse des procédures de corroboration pour un identifiant de compte en raison d’une demande non valide',
	1012: 'Type de synthèse non valide',
	1013: 'Échec de la demande de création de la revue des comptes',
	1014: 'Échec de la demande de suppression de la revue des comptes',
	1015: 'Demande de création de la revue des comptes non valide',
	1016: 'Identifiant de la revue des comptes non valide.',
	1017: 'Le compte ne fait pas partie de cette mission ou a été supprimé.',
	1018: 'La revue des comptes a été créée par un autre utilisateur.',
	1019: 'Le compte a été supprimé par un autre utilisateur. Actualisez la page et réessayez.',
	1020: 'Les PCB du compte doivent être mises à jour',
	1024: 'Le nom du compte ne doit pas dépasser 500 caractères.',
	1025: 'Le compte négligeable ou présentant un risque limité ne peut pas faire l’objet d’une estimation',
	1026: 'Le compte ne peut pas contenir des assertions ayant été dupliquées.',
	1027: 'Identifiant de l’assertion non valide',
	1037: 'Ne peut contenir des références PCB dupliquées',
	1039: 'Le compte sélectionné a été supprimé. Veuillez fermer cette fenêtre modale pour voir la liste à jour.',
	1048: 'Le type d’exécution pour le compte est invalide.',
	1053: 'Impossible de définir le compte comme un compte présentant un risque limité ou un compte négligeable, car un risque ou une estimation est associé au compte.',
	1054: 'Les assertions devant être supprimées sont associées à un risque ou une estimation.',
	1065: 'Vous ne pouvez pas modifier les assertions qui sont associées à un risque important, un risque de fraude, un risque d’anomalies significatives ou une estimation. Vous devez d’abord supprimer ces relations.',
	1070: "L'identifiant de la balance des comptes ne peut pas être nul lorsque des données  sont importées d'EY Helix. ",
	1072: 'L’action ne peut pas être réalisée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	1266: 'Le nombre maximal de documents en mode édition multiutilisateur a été atteint pour la mission. Veuillez réimporter certains documents et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	1267: 'Document is conflicted. Resolve conflicts and try again.  If the issue persists, contact the Help Desk.',
	1268: 'Document is already in co-edit mode. end co-edit mode and try again.  If the issue persists, contact the Help Desk.',
	1269: 'Document is a shared evidence. Unlink and try again.  If the issue persists, contact the Help Desk.',
	1270: 'Les versions du document ne peuvent pas être supprimées ou mises à jour en mode édition multiutilisateur. Veuillez quitter le mode édition multiutilisateur et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	1271: 'Le document n’est pas en mode coédition ou la fermeture du mode coédition est en cours. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/*Assertions*/
	2001: 'Demande de création non valide',
	2002: 'Information manquante : nom de l’assertion',
	2003: 'Assertion manquante',
	2004: 'Échec de la demande de récupération de l’assertion',
	2005: 'Identifiant de la mission non valide',
	2006: 'Échec de la demande de récupération par identifiant d’assertion',
	2007: 'Échec de la demande de récupération des erreurs possibles associées à l’assertion',

	/*Risks*/
	4001: 'L’opération ne peut pas être traitée pour le moment. Actualisez la page et réessayez. Si le problème persiste, communiquez avec le Service de dépannage.',
	4002: 'Information manquante : nom du risque',
	4003: 'Risque manquant',
	4004: 'Échec de la demande de récupération du risque',
	4005: 'Identifiant de la mission non valide',
	4006: 'Échec de la demande de récupération par identifiant de risque',
	4007: 'Demande de recherche non valide',
	4008: 'Cette estimation n’est plus disponible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	4009: 'Demande de mise à jour non valide',
	4010: 'Une erreur possible a déjà été associée à ce risque.',
	4011: 'La liste d’erreurs possibles ne peut pas être nulle.',
	4012: 'Échec de la récupération des types de risques',
	4013: 'Demande de création non valide',
	4014: 'L’assertion associée est non valide. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	4015: 'L’erreur possible sélectionnée est non valide. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	4016: 'Le risque/L’estimation sélectionné(e) a été supprimé(e). Veuillez fermer cette fenêtre modale pour voir la liste à jour.',
	4017: 'L’assertion associée au risque est non valide. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	4018: 'L’identifiant du type de risque transféré est non valide.',
	4019: 'Le nom du risque est invalide.',
	4020: 'Identifiant du document invalide.',
	4021: 'Le nom du risque ne doit pas dépasser 500 caractères.',
	4023: 'La liste des identifiants de l’assertion ne peut être nulle.',
	4024: "Le formulaire relatif à la documentation sur le compte présentant un risque limité n’a pas pu être créé en raison d'une erreur. Veuillez actualiser la page et réessayer. Si le problème persiste, communiquez avec le Service de dépannage. ",
	4025: 'Identifiant du compte non valide.',
	4026: 'Identifiant de l’insertion non valide.',
	4027: 'Le modèle de risque de l’assertion ne peut pas être vide',
	4031: 'Option de risque ou de corps de texte non valide.',
	4035: 'Impossible de modifier l’attribut de risque élevé pour les risques importants ou les risques de fraude.',
	4036: 'KnowledgeAssertionId cannot be empty if no AssertionId is passed in. KnowledgeAssertionId must be in enums',
	4037: 'KnowledgeAssertionId already exists for this account.',
	4038: 'L’identifiant du type de risque ne correspond pas aux options permises.',
	4062: 'Cette catégorie d’opérations importante n’est plus disponible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	4063: 'L’association de catégories d’opérations importantes ne peut pas être modifiée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	4076: 'This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	4079: 'This action could not be completed. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*TASK*/
	5001: 'Get all tasks failed.',
	5002: 'Cette tâche n’est plus disponible dans cette mission.',
	5003: 'Échec de la récupération des documents pour la tâche sélectionnée. Paramètres de la demande non valides.',
	5004: 'Échec de la récupération des documents pour la tâche associée. Paramètres de la demande non valides.',
	5005: 'Échec de la récupération par identifiant de tâche',
	5006: 'Échec de la demande de récupération des catégories de tâches',
	5007: 'Échec de la demande de récupération des sous-catégories de tâches',
	5008: 'Échec de la demande de récupération de la description de tâche',
	5009: 'Échec de la demande de récupération des demandes au client associées à la tâche',
	5010: 'Échec de la demande d’enregistrement des demandes au client associées à la tâche',
	5011: 'La tâche à laquelle vous voulez associer des éléments a été supprimée ou rejetée.',
	5012: 'L’élément que vous voulez associer a été supprimé.',
	5013: 'La tâche à laquelle vous voulez associer des éléments a été supprimée ou rejetée.',
	5014: 'Le document que vous voulez associer a été supprimé.',
	5015: 'Échec de l’appel Récupération des éléments probants associés à la tâche.',
	5016: 'Échec de l’appel Récupération de la tâche associée à l’erreur possible.',
	5017: 'L’identifiant de la mission doit être supérieur à zéro.',
	5018: 'L’association a échoué. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	5019: 'La description de la tâche est vide.',
	5020: 'La tâche sélectionnée a été supprimée ou rejetée. Par conséquent, cette action ne peut pas être réalisée pour le moment.',
	5021: 'Information manquante : identifiant de la mission source.',
	5022: 'Échec de l’enregistrement de l’annotation',
	5023: 'Utilisateur de la mission introuvable.',
	5024: 'Échec de l’appel Suppression de la tâche.',
	5025: 'Échec de l’appel Suppression des tâches.',
	5026: 'La liste des tâches est vide.',
	5027: 'Revue introuvable.',
	5028: 'Le nom du fichier est requis.',
	5029: 'L’extension de fichier est requise.',
	5030: 'Le nom du fichier ne peut pas contenir les caractères suivants : */:<>\\?|"',
	5031: 'Erreur dans la mise à jour du nom du document.',
	5032: 'Identifiant du document invalide.',
	5033: 'Type d’opération introuvable.',
	5034: 'Échec de la modification de l’état de la tâche',
	5035: 'L’action que vous essayez d’effectuer ne peut pas être réalisée pour le moment. Veuillez réessayer plus tard. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	5036: 'Le corps de texte ne peut pas être nul ou vide dans l’appel.',
	5037: 'La requête ne peut être nulle dans l’appel.',
	5038: 'Entrer un nom de fichier unique pour continuer.',
	5039: 'Le nom du fichier est requis.',
	5040: 'Le nom du fichier ne peut pas dépasser 100 caractères.',
	5041: 'Le nom du fichier ne peut pas contenir les caractères suivants : */:<>\\?|"',
	5042: 'La tâche sélectionnée est rejetée.',
	5043: 'La tâche est une tâche Étape de production.',
	5044: 'La tâche est une tâche Jalon.',
	5045: 'La tâche n’est pas une tâche de type PCB ni APC.',
	5046: 'La limite de caractères a été dépassée.',
	5047: 'La limite de caractères a été dépassée.',
	5048: 'Champ obligatoire.',
	5049: 'Le document sélectionné ne peut pas être retiré de cette tâche. Actualisez la page et réessayez. Si le problème persiste, communiquez avec le Service de dépannage.',
	5050: 'L’identifiant du groupe de tâches ne doit pas être zéro ou invalide.',
	5051: 'L’identifiant de la section de tâches ne doit pas être zéro ou invalide.',
	5052: 'Une erreur s’est produite lors de l’ajout de ce document à la tâche actuelle. Échec de l’appel.',
	5053: 'Une erreur s’est produite lors de la mise à jour de ce document dans la tâche actuelle. Échec de l’appel.',
	5054: 'Une erreur s’est produite lors de l’ajout d’une copie de ce document à la tâche actuelle. Échec de l’appel.',
	5055: 'Une erreur s’est produite au moment de renommer le document. Échec de l’appel.',
	5056: 'Le titre de la tâche ne peut être modifié dans la base de connaissances ou pour le groupe de tâches.',
	5057: 'Le type de tâche doit être réglé à ATS.',
	5058: 'Ce document n’a pas pu être retiré de la tâche, car il est associé par le système.',
	5059: 'Valeur de la période invalide.',
	5060: 'Erreur d’ajout aux éléments probants. Échec de l’appel.',
	5061: 'Un autre utilisateur a mis à jour l’information présentée. Actualisez la page et réessayez. Si l’erreur persiste, communiquer avec le Service de dépannage.',
	5062: 'Le document demandé n’est pas disponible dans l’édition EY Atlas sélectionnée pour la mission. Afin d’en demander l’inclusion, veuillez communiquer avec le Service de dépannage, qui pourra transmettre l’information aux auteurs de contenu.',
	5063: 'Opération de correction non valide',
	5064: 'La tâche sélectionnée a été supprimée ou rejetée. Par conséquent, cette action ne peut pas être réalisée à l’heure actuelle.',
	5065: 'Le type de source de tâche ne peut être mis à jour, car la demande n’est pas valide.',
	5066: 'Erreur lors de la récupération des indications. Échec de la demande.',
	5067: 'Le type de nature de tâche ne peut être mis à jour, car la demande n’est pas valide.',
	5068: 'Le type de nature de tâche ne peut être mis à jour, car la demande n’est pas valide.',
	5069: 'Échec de la demande de suppression de la tâche attribuée.',
	5070: 'Une ou plusieurs tâches sélectionnées ont été supprimées. Veuillez réessayer ou communiquez avec le Service de dépannage si le problème persiste.',
	5071: 'L’affectation ne peut être mise à jour., car la demande n’est pas valide.',
	5072: 'Préparateur introuvable. Requête invalide.',
	5073: 'Affectation introuvable.',
	5074: 'Échec de l’enregistrement de l’affectation de la tâche.',
	5075: 'Une seule tâche autre que celle de préparateur peut être attribuée à un même membre de l’équipe. Veuillez réessayer ou communiquez avec le Service de dépannage si le problème persiste.',
	5076: 'L’utilisateur sélectionné n’est pas un membre actif de cette mission. Veuillez réessayer ou communiquez avec le Service de dépannage si l’erreur persiste.',
	5077: 'Une ou plusieurs tâches sélectionnées ont été supprimées. Veuillez réessayer ou communiquez avec le Service de dépannage si l’erreur persiste.',
	5078: 'L’affectation de tâche sélectionnée a été retirée. Veuillez réessayer ou communiquez avec le Service de dépannage si l’erreur persiste.',
	5079: 'Une ou plusieurs tâches sélectionnées ont été supprimées. Veuillez réessayer ou communiquez avec le Service de dépannage si l’erreur persiste.',
	5080: 'L’enregistrement de la version du document n’existe pas.',
	5081: 'L’utilisateur actuellement affecté à cette tâche ne peut pas être réaffecté au rôle de préparateur. Veuillez réessayer ou communiquez avec le Service de dépannage si l’erreur persiste.',
	5082: 'Échec de la mise à jour. Le nom du document doit être unique pour une mission. Actualisez la page pour supprimer ce message.',
	5083: 'La limite de caractères pour les détails de la tâche est dépassée.',
	5084: 'Échec de l’appel de création de document pour la tâche.',
	5085: 'Échec de l’appel de suppression de document pour la tâche.',
	5086: 'Échec de l’appel de création de transmission de tâche.',
	5087: 'Échec de l’appel de correction de tâche.',
	5088: 'Cette tâche doit contenir des éléments probants à transmettre. Veuillez réessayer ou communiquez avec le Service de dépannage.',
	5089: 'Tous les éléments probants associés à cette tâche (sauf les profils papier) doivent avoir au moins un préparateur et un responsable de la revue pour être inscrits comme terminés. Veuillez réessayer et si l’erreur persiste, communiquez avec le Service de dépannage.',
	5091: 'Le nom du profil papier existe déjà dans la mission.',
	5092: 'Le nom ne peut pas contenir les caractères suivants : */:<>\\?|\\',
	5093: 'Le nom du profil papier dépasse la longueur maximale (100 caractères).',
	5111: 'Le compte, l’assertion ou le compte présentant un risque limité sélectionné a été supprimé. Actualisez la page et réessayez. Si le problème persiste, communiquez avec le Service de dépannage.',
	5116: 'Le type de document est non valide.',
	5139: 'L’association a échoué. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	5131: 'L’association a échoué. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	5146: 'La tâche ne peut pas être marquée comme étant terminée.',
	5156: 'L’association de tâches ne peut pas être modifiée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/*WCGW*/
	6001: 'Échec de l’appel de création de l’erreur possible.',
	6002: 'Information manquante : nom de l’erreur possible.',
	6003: 'Information manquante : erreur possible.',
	6004: 'Échec de l’appel de récupération de l’erreur possible.',
	6005: 'Identifiant de la mission invalide.',
	6006: 'Identifiant de l’assertion invalide.',
	6007: 'Échec de l’appel de récupération de l’erreur possible par identifiant.',
	6008: 'Requête invalide.',
	6009: 'Identifiant de l’erreur possible invalide.',
	6010: 'La tâche n’a pas pu être associée à l’erreur possible.',
	6011: 'L’erreur possible sélectionnée a été supprimée.',
	6012: 'La tâche et l’erreur possible ne sont pas associées à la même assertion.',
	6013: 'La tâche sélectionnée ne fait pas partie de la même mission.',
	6014: 'La tâche n’a pas pu être dissociée de l’erreur possible.',
	6015: 'La tâche n’a pas pu être associée à l’erreur possible.',
	6016: 'La tâche est rejetée et ne peut pas être associée à l’erreur possible.',
	6017: 'La tâche n’est pas une tâche Jalon et ne peut pas être associée à l’erreur possible.',
	6018: 'La tâche n’est pas une tâche Étape de production et ne peut pas être associée à l’erreur possible.',
	6019: 'La tâche n’est pas une tâche de type PCB ni APC et ne peut pas être associée à l’erreur possible.',
	6020: 'La tâche et l’erreur possible sont associées à la même assertion et ne peuvent pas être associées à l’erreur possible.',

	/*Engagement*/
	7001: 'Récupération par identifiant introuvable.',
	7002: 'Échec de l’appel de récupération des missions par identifiant d’espace de travail.',
	7003: 'Échec de l’appel de récupération de toutes les entités de la mission.',
	7004: 'Échec de l’appel de récupération de la mission par son identifiant.',
	7005: 'Échec de l’appel de récupération de tous les utilisateurs de la mission.',
	7006: 'Le nom de famille ne doit pas dépasser 250 caractères.',
	7007: 'Erreur – Type d’utilisateur invalide',
	7008: 'Le prénom ne doit pas dépasser 250 caractères.',
	7009: 'L’identificateur unique mondial (GUI) ne peut pas être nul.',
	7010: 'Erreur - État de l’utilisateur invalide.',
	7011: 'Échec de l’appel de création de l’utilisateur de la mission.',
	7012: '{0} {1} ne peut être invité(e) à participer, étant déjà un membre actif ou potentiel de l’équipe.',
	7013: 'Les initiales ne doivent pas dépasser 3 caractères.',
	7014: 'Vous ne pouvez pas accéder à la page d’accueil d’EY Canvas pour le moment. Veuillez réessayer et si le problème persiste, communiquez avec le Service de dépannage.',
	7015: '{0} {1} ne peut pas être invité(e), car le(s) groupe(s) d’accès suivant(s) ont été supprimés de la mission : {2}. Actualisez la page et réessayez.',
	7016: '{0} {1} ne peut pas être invité(e), car l’utilisateur est déjà un membre actif dans le(s) groupe(s) d’accès suivant(s) : {2}',
	7017: 'Le(s) groupe(s) sélectionné(s) n’ont pas accès au domaine : {0}',
	7018: 'L’adresse courriel ne doit pas être nulle.',
	7019: 'Le prénom ne doit pas être nul.',
	7020: 'Le nom de famille ne doit pas être nul.',
	7021: 'Les initiales de l’utilisateur ne doivent pas être nulles.',
	7022: 'Le bureau principal de l’utilisateur ne doit pas être nul.',
	7023: 'Le nom de connexion de l’utilisateur ne doit pas être nul.',
	7024: 'Le rôle de l’utilisateur EY ne doit pas être nul.',
	7025: 'Le rôle de l’utilisateur dans la mission ne doit pas être nul.',
	7026: 'L’opération ne peut pas être traitée pour le moment. Veuillez réessayer et si le problème persiste, communiquez avec le Service de dépannage.',
	7027: 'Adresse courriel invalide',
	7028: 'Échec de l’appel de correction de l’utilisateur de la mission.',
	7029: 'Utilisateurs de la mission - Id de l’état de l’utilisateur de la mission invalide.',
	7030: 'Utilisateurs de la mission - Id du rôle de l’utilisateur de la mission invalide.',
	7031: 'Un ou plusieurs identifiants utilisateur de la mission sont introuvables.',
	7032: 'L’adresse courriel ne doit pas dépasser 250 caractères.',
	7033: 'L’utilisateur demandé ne peut pas être nul.',
	7034: 'Échec de la file d’attente du système de messagerie universel. ',
	7035: 'Les initiales ne doivent pas dépasser 3 caractères.',
	7036: 'Le prénom ne doit pas dépasser 250 caractères.',
	7037: 'Le nom de famille ne doit pas dépasser 250 caractères.',
	7038: 'Échec de l’appel de création de l’utilisateur externe.',
	7039: 'Un ou plusieurs utilisateurs ne peuvent être invités étant déjà des membres actifs ou en attente de l’équipe. Actualisez la page et réessayez. Si le problème persiste, communiquer avec le Service de dépannage.',
	7040: 'Le prénom ne doit pas dépasser 250 caractères.',
	7041: 'Le nom de famille ne doit pas dépasser 250 caractères.',
	7042: 'Les initiales ne doivent pas dépasser 3 caractères.',
	7043: 'Le matricule mondial ne doit pas dépasser 250 caractères.',
	7044: 'L’identificateur unique mondial (GUI) ne doit pas dépasser 250 caractères.',
	7045: 'Échec de la récupération de l’utilisateur externe par son identifiant',
	7046: 'L’utilisateur ne doit pas être nul.',
	7047: 'Les modifications ne peuvent pas être enregistrées pour le moment. Essayez de nouveau et si le problème persiste, communiquez avec le Service de dépannage.',
	7048: 'La page d’accueil d’EY Canvas, requise pour la modification, n’est pas accessible pour le moment. Essayez de nouveau et si le problème persiste, communiquez avec le Service de dépannage.',
	7049: 'Échec de la demande de mise à jour de l’utilisateur de la mission',
	7050: 'Les membres ne peuvent pas être désactivés. Au moins un membre de la mission doit être autorisé à gérer la mission. Veuillez modifier votre sélection et réessayer. Si le problème persiste, communiquez avec le Service de dépannage.',
	7051: 'Échec de la récupération de l’utilisateur interne par son identifiant',
	7052: 'Utilisateur introuvable',
	7053: 'Extraction par identifiant introuvable',
	7054: 'Échec de la demande de récupération des liens rapides par identifiant de mission',
	7055: 'Vous ne disposez pas des droits requis pour ajouter d’anciens membres. Veuillez communiquer avec un membre de l’équipe de mission disposant des droits appropriés pour effectuer cette action.',
	7056: 'Les modifications n’ont pas pu être enregistrées dans EY Canvas pour le moment. Essayez de nouveau et si le problème persiste, communiquez avec le Service de dépannage.',
	7057: 'Vous ne disposez pas des droits requis pour ajouter de nouveaux membres. Veuillez communiquer avec un membre de l’équipe de mission disposant des droits appropriés pour effectuer cette action.',
	7058: 'L’état de l’utilisateur a changé. Actualisez la page et réessayez. Si le problème persiste, communiquez avec le Service de dépannage.',
	7062: 'Le portail client d’EY Canvas, requis pour la mise à jour des renseignements relatifs au membre existant, n’est pas accessible pour le moment. Veuillez réessayer et si le problème persiste, communiquez avec le Service de dépannage.',
	7063: 'Il n’est pas possible de désactiver les membres externes. Actualisez la page et réessayez. Si le problème persiste, communiquez avec le Service de dépannage.',
	7064: 'Opération de correction invalide.',
	7065: '{0} {1} ne peut pas être activé(e) dans un ou plusieurs groupes d’accès. Le(s) groupe(s) sélectionné(s) n’ont pas accès au domaine : {2}.',
	7066: '{0} n’est pas un utilisateur valide.',
	7067: 'Put external user call failed.',
	7068: 'Le portail client d’EY Canvas, requis pour la mise à jour des renseignements relatifs au membre existant, n’est pas accessible pour le moment. Veuillez réessayer et si le problème persiste, communiquez avec le Service de dépannage.',
	7069: 'Les groupes d’accès sélectionnés sont inactifs : {0}. Supprimez et réessayez.',
	7072: 'Une erreur s’est produite pendant le processus de détachement de la mission. Actualisez la page et réessayez. Si l’erreur persiste, communiquez avec le Service de dépannage.',
	7074: 'Les modifications ne peuvent pas être enregistrées. Au moins un membre actif de la mission doit être autorisé à gérer la mission et avoir résolu les questions d’indépendance. Si le problème persiste, communiquez avec le Service de dépannage.',
	7079: 'Échec de la demande d’envoi de la confirmation d’indépendance.',
	7080: 'L’identifiant de l’utilisateur n’est pas valide ou l’envoi de la confirmation d’indépendance n’est pas permis, car l’identifiant de l’utilisateur ne correspond pas à l’identifiant de l’utilisateur connecté.',
	7081: "Répondez à toutes les questions avant de cliquer sur'Envoyer'. Sélectionnez l’option'Afficher les éléments incomplets' pour afficher les réponses manquantes. Si le problème persiste, veuillez communiquer avec le Service de dépannage. ",
	7082: 'Aucun document relatif à l’indépendance n’a été trouvé pour cette demande.',
	7083: 'Échec de la demande de synthèse du modèle de prestation de services.',
	7084: 'L’identifiant de l’utilisateur connecté n’est pas valide ou la procédure relative à l’indépendance n’est pas autorisée.',
	7085: 'Le commentaire relatif à l’indépendance ne doit pas dépasser 4 000 caractères.',
	7086: 'Échec de la demande de mise en œuvre de la procédure relative à l’indépendance.',
	7087: 'Vous devez être l’associé responsable, l’associé responsable de la mission ou un associé délégué pour accorder ou refuser l’accès à cet utilisateur, ou pour le contourner.',
	7088: 'Échec de la soumission de la confirmation d’indépendance, réessayez plus tard.',
	7098: 'Invalid Pacetype Id.',
	7099: 'Aucun gabarit sur l’indépendance n’est disponible. Veuillez réessayer et si le problème persiste, communiquez avec le Service de dépannage.',
	7154: 'Aucun utilisateur trouvé pour le formulaire Canvas actuel',
	7155: 'Il n’est pas permis d’envoyer le profil d’une mission restaurée.',
	7156: 'Le contenu est en cours d’actualisation. Réessayez plus tard. Si le problème persiste, communiquez avec le Service de dépannage.',
	7158: 'Le document n’est pas disponible. Veuillez actualiser la page et réessayer. Si le problème persiste, communiquez avec le Service de dépannage.',

	/*SCOT*/
	8001: 'Échec de la demande de création de la catégorie importante d’opérations',
	8002: 'Information manquante : nom de la catégorie importante d’opérations',
	8003: 'Catégorie importante d’opérations manquante',
	8004: 'Échec de la demande de récupération de la catégorie importante d’opérations',
	8005: 'Identifiant de la mission non valide',
	8006: 'Identifiant de l’assertion non valide',
	8007: 'Échec de la demande de récupération de la catégorie importante d’opérations par son identifiant',
	8008: 'Demande non valide',
	8009: 'The selected SCOT has been deleted. Close this modal to see the updated list.',
	8010: 'L’identifiant de la catégorie importante d’opérations ne peut pas être nul ou vide.',
	8011: 'L’identifiant de la catégorie importante d’opérations doit être supérieur à zéro.',
	8012: 'L’identifiant du document n’est pas valide.',
	8013: 'Échec de la demande de mise à jour de la catégorie importante d’opérations.',
	8014: 'Le nom de la catégorie importante d’opérations ne peut pas être nul ou vide.',
	8015: 'Le nom de la catégorie importante d’opérations ne peut pas dépasser 500 caractères.',
	8016: 'Le type de stratégie associé à la catégorie importante d’opérations est non valide.',
	8017: 'Le type de catégorie importante d’opérations est non valide.',
	8018: 'Le type d’application informatique associé à la catégorie importante d’opérations est non valide.',
	8019: 'Le champ du type d’application informatique associée à la catégorie d’opérations importante devrait être vide lorsqu’aucune application informatique n’est appliquée.',
	8028: 'The selected SCOT has been deleted. Close this modal to see the updated list.',

	/*User*/
	10001: 'Préférences de l’utilisateur connecté introuvables',
	10002: 'User GetAll call failed.',
	10003: 'User Presence Get call failed.',
	10005: 'Impossible de récupérer les renseignements sur l’utilisateur.',

	/*Risk Type*/
	11001: 'Demande de création invalide.',
	11002: 'Information manquante : nom du type de risque.',
	11003: 'Information manquante : type de risque.',
	11004: 'Échec de la demande de récupération du type de risque.',
	11005: 'Identifiant de la mission invalide.',

	/*TaskDocuments*/
	80004: 'Une ou plusieurs tâches sont invalides.',
	80005: 'L’identifiant du document est invalide.',

	/*Edit Control*/
	83001: 'Échec de la demande de récupération des contrôles.',
	83002: 'Échec de la demande de récupération du contrôle par identifiant.',
	83003: 'L’identifiant du contrôle est nul ou vide.',
	83004: 'Identifiant du contrôle invalide.',
	83005: 'L’identifiant du document est invalide.',
	83006: 'Information manquante : nom du contrôle.',
	83007: 'Longueur du nom du contrôle invalide.',
	83008: 'Demande invalide.',
	83009: 'Identifiant de la fréquence du contrôle invalide.',
	83010: 'Identifiant du type de contrôle invalide.',
	83011: 'Applications informatiques du contrôle invalides.',
	83012: 'Identifiant du type d’efficacité de la conception du contrôle invalide.',
	83013: 'Identifiants d’application informatique invalides.',
	83014: 'Seules les applications informatiques de type SS sont autorisées lorsque le type de contrôle est un contrôle manuel de prévention ou un contrôle manuel de détection.',
	83015: 'Un test manuel des IPE ne peut être sélectionné que dans le cas d’un contrôle manuel lié aux TI.',
	83016: 'Les contrôles présentant un risque faible ne sont pas permis pour ce type de profil de mission.',
	83017: 'Filtre appliqué aux catégories d’opérations importantes déjà utilisé',
	83018: 'Le nom du contrôle ne doit pas dépasser 500 caractères.',
	83019: 'Identifiants d’erreur possible invalides.',
	83020: 'Il n’est pas permis d’utiliser des identifiants d’erreur possible plus d’une fois.',
	83021: 'Il n’est pas permis d’utiliser des identifiants d’application informatique plus d’une fois.',
	83022: 'Le paramètre {0} est invalide.',
	83023: 'Page active invalide.',
	83024: 'Taille de la page invalide.',
	83025: 'La chaîne de recherche ne doit pas dépasser 100 caractères.',
	83026: 'Une application informatique à laquelle aucune société de services n’est associée peut être associée seulement à un contrôle manuel lié aux TI ou un contrôle des applications informatiques.',
	83027: 'Filtre appliqué aux erreurs possibles déjà utilisé.',
	83028: 'Identifiant du contrôle de la base de connaissances invalide.',

	112000: 'Aucun document trouvé pour la source et la cible.',
	112001: 'Unable to perform call because the request is null.',
	112002: 'Request body cannot be null or empty in the call.',
	112003: 'Source and target documentId should not be equal.',
	112004: 'L’identifiant du document source ne doit pas être nul ou vide.',
	112005: 'L’identifiant du document cible ne doit pas être nul ou vide.',
	112006: 'Source EngagementId should not be null or empty.',
	112007: 'Target EngagementId should not be null or empty.',
	112008: 'Document source invalide pour cette mission.',
	112009: 'Document cible invalide pour cette mission.',
	112010: 'Identifiant de la mission cible introuvable.',
	112011: 'Rôles ne permettant pas de lier les formulaires pour la mission source. Vous pouvez obtenir les droits requis auprès de l’administrateur de la mission.',
	112012: 'Rôles ne permettant pas de lier les formulaires pour la mission cible. Vous pouvez obtenir les droits requis auprès de l’administrateur de la mission.',
	112013: 'Utilisateur pour la mission source invalide.',
	112014: 'Utilisateur pour la mission cible invalide.',
	112015: 'Type de document source non valide pour le partage.',
	112016: 'Type de document cible non valide pour le partage.',
	112017: 'Le type de document source ne correspond pas au type de document cible.',
	112018: 'Source and target knowledge formIds are not matched.',
	112019: 'Le lien de partage existe déjà pour la source et la cible.',
	112020: 'L’espace de travail de la mission source ne correspond pas à celui de la mission cible.',
	112021: 'Le document cible ne peut pas être une cible.',
	112022: 'L’activité sélectionnée est déjà partagée avec d’autres activités et ne peut pas être sélectionnée pour être à nouveau partagée.',
	112023: 'Le document source ne peut pas être une cible.',
	112024: 'Cannot find engagement Id of target documentId.',
	112025: 'Cannot find engagement Id of source documentId.',
	112026: 'Source document of source and target documentId should not be equal.',
	112027: 'Impossible de procéder à la transmission du profil, car des activités de l’outil FIT d’EY Canvas sont partagées avec cette mission. Dissocier les activités pour continuer.',
	112028: 'L’identifiant de la mission source ne doit pas être le même que celui de la mission cible.',
	112029: 'Source or Target EngagementId and documentId should match with route EngagementId and documentId.',
	112030: 'Le document n’est pas valide pour cette mission.',
	112031: 'L’identifiant du document ne doit pas être nul ou vide.',
	112032: 'L’identifiant de la mission ne doit pas être nul ou vide.',
	112033: 'L’identifiant d’un document cible doit être unique.',
	112034: 'Les documents cibles ou sources sont déjà partagés.',
	112035: 'Il ne peut y avoir qu’une seule cible pour un document associé existant avec une réponse liée.',

	/*MissingDocument*/
	116001: 'Create form failed.',
	116002: 'Aucun document de la base de connaissances trouvé pour l’identifiant de type de document indiqué.',
	116004: 'Échec de la création du document. Veuillez actualiser la page et réessayer plus tard. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/* Annotation Errors*/
	12001: 'Paramètres de la requête invalides.',
	12002: 'Échec de l’appel de création de l’annotation.',
	12003: 'Échec de la récupération de l’annotation.',
	12004: 'Identifiant de la mission invalide.',
	12005: 'Les identifiants dans la collection doivent être supérieurs à zéro.',
	12006: 'La collection d’identifiants ne peut pas être vide.',
	12007: 'Identifiant de la tâche invalide.',
	12008: 'Identifiant du document invalide.',
	12009: 'L’identifiant du document et l’identifiant de la tâche doivent être valides.',
	12010: 'Les réponses nécessitent un parent.',
	12011: 'Identifiant d’état invalide.',
	12012: 'Le type de document doit être 440GL.',
	12013: 'Type d’annotation invalide.',
	12014: 'Utilisateur de la mission invalide.',
	12015: 'Ce document a été supprimé par un autre utilisateur.',
	12016: 'Échec de l’appel de mise à jour de l’annotation.',
	12017: 'La note à laquelle vous répondez a été supprimée par un autre membre de l’équipe. Veuillez actualiser la page et réessayer.',
	12018: 'Le type de modification de l’annotation ne doit pas être nul.',
	12019: 'Note de revue supprimée.',
	12020: 'Action invalide.',
	12021: 'L’utilisateur n’est pas l’auteur de l’annotation.',
	12022: 'Information manquante : utilisateur auteur.',
	12023: 'L’utilisateur auteur n’existe pas ou ne fait pas partie de la mission.',
	12024: 'Utilisateur désigné requis.',
	12025: 'L’utilisateur désigné n’existe pas ou ne fait pas partie de la mission.',
	12026: 'Commentaires invalides.',
	12027: 'Les commentaires ne doivent pas être vides.',
	12028: 'Date d’échéance requise.',
	12029: 'Priorité valide requise.',
	12030: 'L’état de cette note a changé. Vous ne pouvez plus la modifier ou y répondre. Veuillez fermer la fenêtre et l’ouvrir à nouveau pour voir les données à jour et poursuivre la modification.',
	12031: 'L’annotation doit être de premier niveau.',
	12032: 'L’annotation ne doit pas être de premier niveau.',
	12033: 'Au moins une des valeurs suivantes ne doit pas être vide : type de priorité, date d’échéance, état ou utilisateur désigné.',
	12034: 'Le commentaire dépasse la longueur maximale (4 000 caractères).',
	12035: 'La chaîne de recherche dépasse la longueur maximale (500 caractères)',
	12036: 'Seul l’identifiant de la tâche ou l’identifiant du document peut être accepté. Il n’est pas permis d’accepter les deux identifiants.',
	12037: 'Opération de correction non valide',
	12038: 'Vous essayez de modifier une note qui n’existe plus. Actualisez la page et réessayez.',
	12039: 'Vous essayez de modifier la même note qu’un autre membre de l’équipe. Actualisez la page et réessayez.',
	12040: 'Échec de la demande de récupération des utilisateurs de l’annotation',
	12041: 'Échec de la récupération des utilisateurs de l’annotation. Valeur de recherche non valide.',
	12042: 'Le type de document ne permet pas de créer une annotation.',
	12043: 'Vous essayez d’ajouter une note à une tâche ou à un document qui n’existe plus. Actualisez la page et réessayez.',
	12044: 'Vous essayez de modifier une réponse à une note qui n’existe plus. Actualisez la page et réessayez.',
	12045: 'Note de revue introuvable',
	12046: 'La note sélectionnée a déjà été supprimée par un autre utilisateur.',
	12047: 'Vous pouvez uniquement supprimer les réponses aux notes ouvertes. Actualisez la page et réessayez.',
	12048: 'Vous essayez de modifier l’état d’une note qui n’existe plus. Actualisez la page et réessayez.',
	12049: 'Vous essayez de supprimer la réponse à une note qui a déjà été supprimée par un autre membre de l’équipe. Actualisez la page et réessayez.',
	12050: 'Vous pouvez uniquement supprimer les notes fermées.',
	12051: 'Les notes relatives au type de commentaire ne peuvent être créées que pour des documents Helix valides.',
	12052: 'La note relative au type de commentaire que vous recherchez a été supprimée.',
	12060: 'Le numéro de référence est requis et doit être compris entre 0 et 1000.',
	12061: 'Le numéro de référence doit être nul.',
	12062: 'Des annotations ne peuvent être créées que pour un commentaire.',
	12066: 'Vous essayez de répondre à une note qui n’est pas ouverte.',
	12067: 'Vous essayez de supprimer la réponse à un commentaire qui a déjà été supprimé par un autre membre de l’équipe. Veuillez actualiser la page et réessayer.',
	12068: 'Vous essayez d’ajouter une note avec un enregistrement qui n’est pas valide ou qui n’existe plus. Veuillez actualiser la page et réessayer.',
	12069: 'Vous essayez de mettre à jour une note avec un enregistrement qui n’est pas valide ou qui n’existe plus. Veuillez actualiser la page et réessayer.',
	12070: 'Vous ne pouvez ajouter qu’un commentaire par état financier. Veuillez modifier le commentaire existant.',
	12071: 'Échec de la suppression de l’information. Veuillez réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/*FlowchartStepControl*/
	123054: 'Control relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',
	123045: 'This control is no longer available. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepWCGW*/
	123022: 'Cette étape de l’organigramme n’est plus disponible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	123023: 'Cette étape de l’organigramme n’est plus disponible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	123055: 'WCGW relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the error persists.',

	/*FlowchartStepITApplicationSO*/
	123056: 'IT Application / service organization relation cannot be edited. Refresh the page and try again. Contact the Help Desk if the issue persists.',



	/*FlowchartStepDocument*/
	123048: 'L’association de documents ne peut pas être modifiée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	123033: 'Cette étape de l’organigramme n’est plus disponible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	123002: 'Ce document n’est plus disponible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/*Configuration*/
	13001: 'Configuration introuvable',
	13002: 'Échec de la demande de récupération des configurations de l’interface de programmation.',

	/*Documents*/
	14001: 'La demande ne peut pas être soumise, car l’objet de la requête est nul.',
	14002: 'Document introuvable. Échec de la demande.',
	14003: 'L’identifiant de la mission doit être supérieur à zéro.',
	14004: 'L’identifiant du document ne doit pas être nul ou vide.',
	14005: 'Une erreur s’est produite lors de la récupération des tâches associées. Échec de la demande.',
	14006: 'Le document sélectionné ne peut pas être supprimé. Réessayez plus tard. Si le problème persiste, communiquez avec le Service de dépannage.',
	14007: 'Une erreur imprévue s’est produite.',
	14008: 'Une erreur imprévue s’est produite lors de l’approbation.',
	14009: 'Une erreur s’est produite lors de l’ajout d’une approbation.',
	14010: 'L’identifiant de l’approbation doit être supérieur à zéro.',
	14011: 'Une erreur s’est produite lors de la suppression d’une approbation.',
	14012: 'Le corps de texte ne peut pas être vide.',
	14013: 'Le document sélectionné ne peut pas être détaché. Réessayez plus tard. Si le problème persiste, communiquez avec le Service de dépannage.',
	14014: 'Échec de la demande de récupération par identifiant de document',
	14015: 'Entité associée non valide',
	14016: 'Échec de la demande de récupération de l’approbation du document',
	14017: 'Échec de la demande de récupération de toutes les constatations du document',
	14018: 'Échec de la demande de récupération de tous les documents',
	14019: 'Approbation introuvable.',
	14020: 'Valeur de l’action non valide',
	14021: 'Type de constatation non valide',
	14022: 'Le document ne fait pas partie de cette mission.',
	14023: 'La modification apportée au document n’est pas valide.',
	14024: 'Échec de la demande de récupération de tous les documents. Les paramètres sont non valides.',
	14025: 'Une erreur s’est produite lors de la création de la revue du document. Échec de la demande de l’interface de programmation.',
	14026: 'Une erreur s’est produite lors de la suppression de la revue du document. Échec de la demande de l’interface de programmation.',
	14027: 'L’identifiant de la mission ne doit pas être nul ou vide.',
	14028: 'L’identifiant de l’utilisateur est non valide.',
	14029: 'L’utilisateur n’est pas autorisé à effectuer cette action.',
	14030: 'Aucun document avec l’identifiant transféré n’a été trouvé dans cette mission.',
	14031: 'L’identifiant de la revue du document est non valide.',
	14032: 'L’examen du document est introuvable.',
	14033: 'Le document a déjà été approuvé.',
	14034: 'Un processus de détachement est en cours pour une autre mission dans l’espace de travail ou ce document a déjà été détaché. Actualisez la page et réessayez. Si le problème persiste, communiquez avec le Service de dépannage.',
	14035: 'Le document n’est pas partagé avec la mission.',
	14036: 'Le numéro de la version doit être supérieur à zéro.',
	14037: 'L’opération ne peut pas être traitée pour le moment. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	14038: 'Échec de la récupération des raisons de la modification du document',
	14039: 'Échec de la récupération des raisons de la modification du document par identifiant',
	14040: 'Échec de la mise à jour de la raison de la modification',
	14041: 'Raison de la modification non valide',
	14042: 'Échec de la mise à jour de la raison de la modification',
	14043: 'Échec de la création de la raison de la modification',
	14044: 'Échec de la suppression de la raison de la modification',
	14045: 'Identifiant de la raison de la modification non valide',
	14046: 'Le document n’est pas disponible. Actualisez la page et réessayez. Si le problème persiste, communiquez avec le Service de dépannage.',
	14047: 'La raison de la modification apportée au document a déjà été indiquée.',
	14048: 'Le document comporte un conflit. Veuillez le résoudre avant de continuer.',
	14049: 'Les données indiquant si le document a été ajouté à l’équipe ou à l’utilisateur ne sont pas valides.',
	14050: 'Une erreur s’est produite lors de la suppression du document de référence.',
	14052: 'Le texte recherché dépasse la longueur maximale permise (500 caractères)',
	14053: 'La demande ne peut pas être soumise, car l’objet de la requête est nul.',
	14054: 'Opération de correction non valide',
	14055: 'Échec de la demande de résolution de l’historique des documents',
	14056: 'Une erreur s’est produite lors de l’ajout du message à la file d’attente.',
	14057: 'Un document du même nom existe déjà dans la mission.',
	14058: 'Plus d’une version du document avec le même numéro a été trouvée. Veuillez communiquer avec le Service de dépannage pour obtenir de l’aide.',
	14059: 'La version du document sélectionnée n’a pas pu être trouvée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage pour obtenir de l’aide.',
	14060: 'L’opération n’a pas pu être traitée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage pour obtenir de l’aide.',
	14061: 'Le document n’est pas partagé, il est impossible de le dissocier. Si le problème persiste, veuillez communiquer avec le Service de dépannage pour obtenir de l’aide.',
	14062: 'L’identifiant du type de niveau de confidentialité du document n’est pas valide.',
	14063: 'Le type de niveau de confidentialité de ce type document ne peut pas être modifié.',
	14064: 'L’utilisateur ne dispose pas des autorisations requises.',
	14065: 'Description personnalisée invalide.',
	14066: 'Échec de la mise à jour de l’extension.',
	14067: 'Extension invalide.',
	14068: 'Échec de la récupération des extensions du document.',
	14069: 'Ce document a déjà été dissocié par un autre membre de l’équipe. Veuillez actualiser la page et communiquer avec le Service de dépannage si le problème persiste.',
	14070: 'Invalid file type.',
	14071: 'The selected document version is no longer available. Please close and reopen this window to see the latest set of historical versions for the document.',
	14072: 'L’identifiant du document source ne doit pas être nul ou vide.',
	14073: 'L’identifiant du formulaire Canvas ne doit pas être nul ou vide.',
	14074: 'L’identifiant du formulaire Canvas ne doit pas être un doublon.',
	14075: 'Échec de l’association du document aux formulaires Canvas.',
	14076: 'Document source associé aux formulaires Canvas introuvable.',
	14077: 'Document source introuvable. Échec de l’appel.',
	14078: 'Le document actuel est déjà associé à des formulaires Canvas donnés.',
	14079: 'This document has been deleted and therefore it cannot be opened.',
	14080: 'The source approval user id is invalid.',
	14081: 'The source approval user id should valid GUID and must not be empty GUID.',
	14082: 'The modify user id is invalid.',
	14083: 'The modify user id should be a valid GUID and must not be empty GUID.',
	14084: 'File name cannot include: */:<>\\?|""',
	14085: 'The document name exceeded maximum length allowed.',
	14086: 'DocService failed while updating document details.',
	14087: 'The input is not valid.',
	14088: 'A input has duplicate document names.',
	14089: 'The bookmark observation is not valid.',
	14090: 'Request status has changed. Please refresh the page and try again if required. If the issue persists, contact the help desk.',
	14091: 'This request has been deleted. Refresh the page to view updated data. If the issue persists, contact the Help Desk.',
	14092: 'Document not eligible for update. Refresh the page and try again.  If the issue persists, contact the Help Desk.',

	/*SEM*/
	15001: 'Échec de la demande d’extraction de la synthèse des procédures de corroboration pour un identifiant de compte en raison d’une demande non valide',
	15002: 'Identifiant du compte non valide',
	15003: 'Identifiant de la mission non valide',
	15004: 'Type de synthèse non valide',
	15005: 'Compte associé introuvable. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/*Timephase*/
	16001: 'Échec de la récupération des périodes',
	16002: 'La mission doit être supérieure à zéro.',
	16003: 'La période doit être supérieure à zéro.',
	16004: 'Valeur de l’identifiant de la tâche non valide',
	16005: 'Valeur de la période non valide',

	/*Validations*/
	17001: 'Information manquante : identifiant de l’étape de production ou du type de document',
	17003: 'The document could not be found. Refresh the page and try again. If the issue persists, contact Help Desk.',

	/*TaskGroupSection*/
	18001: 'Échec de la demande de récupération de toutes les sections du groupe de tâches',

	/*Assignments*/
	19001: 'Échec de la demande de création de l’affectation',
	19002: 'Échec de la demande de récupération de l’affectation',

	/*Client Request*/
	21001: 'Échec de la demande d’association du client',

	/*Related Components*/
	22001: 'Échec de la demande de récupération des composantes connexes',

	/*Component Ceation*/
	22022: 'Le nom de la composante existe déjà dans cette mission.',
	22024: 'La composante à laquelle vous tentez d’envoyer des instructions n’est pas disponible ou a déjà été supprimée.',
	22027: 'Instructions pour le groupe sans date d’échéance.',
	22028: 'Les instructions relatives à l’étendue n’ont pas encore été publiées pour la composante.',
	22029: 'Aucune nouvelle instruction publiée pour la composante à envoyer.',

	22040: 'Les instructions n’ont pas pu être envoyées. Vérifier si le type de mission est le bon.',
	22048: 'La composante que vous essayez de mettre à jour est copiée d’une autre mission en cours de copie.',

	/*Send Instruction*/
	22049: 'Group instructions cannot be sent because one or more documents are in multi-user edit mode. End multi-editing mode and try to send instructions again. If the problem persists, contact EY Help Desk.',

	/*User Presence*/
	23001: 'Statut de l’utilisateur connecté introuvable',
	23002: 'Échec de la demande de récupération du statut de l’utilisateur',
	23003: 'Échec de la validation du document relatif au statut de l’utilisateur',
	23004: 'Échec de la demande de suppression du statut de l’utilisateur',
	23005: 'Le document n’est plus ouvert par d’autres utilisateurs. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/* Forms */
	24001: 'Échec de la demande de récupération du formulaire',
	24002: 'Identifiant de la mission non valide',
	24003: 'L’identifiant du document ne doit pas être nul ou vide.',
	24005: 'En-tête du formulaire introuvable.',
	24004: 'Le document est introuvable. Actualisez la page et réessayez. Si le problème persiste, communiquez avec le Service de dépannage.',
	24006: 'Section is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24007: 'Paramètres de la demande invalides.',
	24008: 'Header id should not be null or empty.',
	24009: 'Section id should not be null or empty.',
	24010: 'Form body response update operation failed.',
	24011: 'Invalid request for form body response update.',
	24012: 'Body is not available. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	24013: 'Invalid form body option id.',
	24014: 'Invalid form body type id.',
	24015: 'Invalid request body for given body type id.',
	24016: 'Invalid free text for given body type id.',
	24017: 'Le nombre maximal de caractères comprend les balises de formatage de texte enrichi. Veuillez réduire la longueur ou supprimer le formatage non nécessaire et réessayer.',
	24018: 'For given body type id response is not allowed.',
	24019: 'Body id should not be null or empty.',
	24020: 'Corps de texte de la demande non valide',
	24021: 'Le corps de texte a été supprimé.',
	24022: 'Le champ du pays ne doit pas être nul ou vide.',
	24023: 'Le champ de la langue ne doit pas être nul ou vide.',
	24024: 'Le champ de la sous-gamme de services ne doit pas être nul ou vide.',
	24025: 'Le champ du volet de la MMA ne doit pas être nul ou vide.',
	24026: 'Échec de la demande de création d’un en-tête.',
	24027: 'Invalid request for header creation.',
	24028: 'Échec de la demande de duplication de création d’unité.',
	24029: 'Format du type d’unité invalide.',
	24030: 'La section a été supprimée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	24031: 'Le corps du texte n’est pas personnalisé.',
	24032: 'Demande de création d’un en-tête invalide.',
	24033: 'Supplied document entity id is not valid.',
	24034: 'Supplied entity id is not valid.',
	24035: 'Une erreur s’est produite lors de la création du document associé.',
	24036: 'Une erreur s’est produite lors de la suppression du document associé.',
	24037: 'Le code du document associé ne doit pas être nul ou vide.',
	24038: 'Le document est non valide ou n’existe pas.',
	24039: 'Le document associé est non valide ou n’existe pas.',
	24040: 'Échec de la personnalisation du corps du texte.',
	24041: 'La demande de création du corps du texte est non valide.',
	24042: 'La demande de création de section est non valide.',
	24043: 'Échec de la demande d’extraction par code de section.',
	24044: 'Échec de la création de la section.',
	24045: 'La page active est non valide.',
	24046: 'La taille de la page est non valide.',
	24047: 'Invalid document related object id.',
	24048: 'L’objet est déjà associé au formulaire Canvas.',
	24049: 'Objet introuvable.',
	24050: 'L’identifiant unique de l’entité entré est non valide.',
	24051: 'Si l’identifiant de l’entité est fourni, l’identifiant unique de l’entité doit être fourni et inversement.',
	24052: 'Échec de la création de l’instantané du formulaire Canvas.',
	24053: 'Échec de la demande de récupération de l’en-tête par identifiant.',
	24054: 'Échec de la demande de récupération du corps de texte par identifiant.',
	24055: 'Une erreur s’est produite lors de la création du profil du formulaire.',
	24056: 'Le profil du formulaire existe déjà',
	24057: 'Échec de la validation du profil du formulaire.',
	24058: 'Le profil du formulaire n’existe pas',
	24059: 'Section non personnalisée.',
	24060: 'Échec de la validation du profil du formulaire. Si PCAOB-AI est vrai, alors PCAOB-EF doit aussi être vrai.',
	24061: 'Échec de la validation du profil du formulaire. Si PCAOB-EF est faux, alors PCAOB-AI doit aussi être faux.',
	24062: "Échec de la validation du profil du formulaire. Si'non-complexe', alors'PCAOB-EF'/'PCAOB-AI' doit être faux. ",
	24063: 'L’identifiant du pays est non valide.',
	24064: 'L’identifiant de la langue est non valide.',
	24065: 'L’en-tête n’est pas personnalisé.',
	24066: 'Échec de la création de l’objet associé à la section du formulaire.',
	24067: 'Objet introuvable.',
	24068: 'Erreur possible non associée à la catégorie importante d’opérations',
	24069: 'Le paramètre {0} est non valide.',
	24070: 'L’identifiant unique de l’entité mère entré est non valide.',
	24071: 'Échec de la récupération de l’objet associé à la section du formulaire.',
	24072: 'La section n’est pas disponible. Actualisez la page et réessayez. Si le problème persiste, communiquez avec le Service de dépannage.',
	24073: 'Impossible de récupérer l’instantané. Actualisez la page et réessayez. Si le problème persiste, communiquez avec le Service de dépannage.',
	24074: 'Les instantanés ne sont pas disponibles pour le document sélectionné. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	24075: 'Identifiant de l’instantané non valide.',
	24076: 'L’identifiant de l’entité ne doit pas être nul ou vide.',
	24077: 'L’identifiant de l’objet associé à la section du formulaire envoyé n’est pas valide.',
	24078: 'L’identifiant de l’objet associé à la section du formulaire ne doit pas être nul ou vide.',
	24079: 'L’identifiant de l’entité parent est non valide.',
	24080: 'FormSectionRelatedObject : Dossier de l’entité parent introuvable.',
	24081: 'Objet introuvable.',
	24082: 'Objet introuvable.',
	24083: 'Une erreur s’est produite lors de la mise à jour du profil du formulaire.',
	24084: 'Le profil du formulaire associé au document n’existe pas.',
	24085: 'L’identifiant de la langue doit être supérieur à zéro.',
	24086: 'L’identifiant du pays doit être supérieur à zéro.',
	24087: 'Les documents provenant de la base de connaissances ne peuvent pas être mis à jour. Pour modifier le profil des formulaires, mettre à jour le profil de la mission.',
	24088: 'Pour modifier le contenu, vous devez avoir un rôle plus élevé. Vous pouvez obtenir les droits requis auprès de l’administrateur de la mission.',
	24089: 'Le nom de l’en-tête personnalisé dépasse la longueur maximale (500 caractères). Veuillez corriger le nom et réessayer.',
	24090: 'Le nom de la section personnalisée dépasse la longueur maximale (500 caractères). Veuillez corriger le nom et réessayer.',
	24091: 'Le nom de l’étiquette personnalisée dépasse la longueur maximale (100 caractères). Veuillez corriger le nom et réessayer.',
	24092: 'Le nom du corps de texte personnalisé dépasse la longueur maximale (500 caractères). Veuillez corriger le nom et réessayer.',
	24093: 'Le nom de la section personnalisée ne doit pas être nul ou vide.',
	24094: 'Le nom du corps de texte ne doit pas être nul ou vide.',
	24096: 'Le nom de l’en-tête personnalisé ne doit pas être nul ou vide.',
	24097: 'L’écrasement ne peut pas être réalisé pour le moment.',
	24098: 'L’identifiant du type de document cible ou de document source n’est pas valide.',
	24099: 'L’identifiant du formulaire provenant de la base de connaissances du document source et du document cible n’est pas le même.',
	24100: 'L’identifiant du document source ne doit pas être nul ou vide.',
	24101: 'L’identifiant du document cible ne doit pas être nul ou vide.',
	24103: 'L’en-tête a été supprimé. Actualisez la page et réessayez. Si le problème persiste, communiquez avec le Service de dépannage.',
	24104: 'L’en-tête ne peut pas être modifié.',
	24105: 'Le corps de texte ne peut pas être modifié.',
	24106: 'La demande de mise à jour est non valide.',
	24107: "Échec de la validation du profil du formulaire associé au document. Si PCAOB-EF est vrai, alors'complexe' doit aussi être vrai. ",
	24108: 'Échec de la validation du profil du formulaire associé au document. Si PCAOB-AI est vrai, alors PCAOB-EF doit aussi être vrai.',
	24110: 'La mise à jour du contenu est en cours. Mettez à jour manuellement le contenu de ce formulaire à la page de mise à jour du contenu des formulaires Canvas une fois la mise à jour de contenu actuelle terminée.',
	24111: 'Les valeurs Associer et Détacher doivent être différentes.',
	24112: 'Le document source ne doit pas être partagé.',
	24114: 'Échec de l’ajout des éléments probants. Actualisez la page et réessayez.',
	24115: 'Échec du retrait des éléments probants. Actualisez la page et réessayez.',
	24116: 'Échec de l’envoi du profil. Veuillez actualiser la page et réessayer.',
	24117: 'Le formulaire contient des réponses manquantes. Veuillez actualiser la page et réessayer.',
	24118: 'L’identifiant du document et celui du document associé ne peuvent pas être identiques.',
	24119: 'Échec de la modification des objets associés au corps du texte.',
	24120: 'L’identifiant de l’objet associé au corps de texte ne doit pas être nul ou vide.',
	24121: 'Objet introuvable.',
	24122: 'Information manquante : jeton d’accès concurrentiel',
	24124: 'Un ou des documents cibles invalides ont été trouvés.',
	24125: 'Formulaires Canvas cibles introuvables.',
	24126: 'La demande ne devrait pas être nulle ou vide.',
	24127: 'Échec de l’importation de données d’EY Helix. Importer à nouveau des données avec les paramètres d’EY Helix et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	24155: 'Il n’est pas permis d’envoyer le profil d’une mission restaurée.',
	24164: 'Vous ne disposez pas des autorisations requises pour modifier le gabarit.',
	24166: 'L’enregistrement du profil n’est pas autorisé. Le formulaire n’est pas disponible pour ce profil.',
	24167: 'Le type de corps de texte ne peut pas être mis à jour',
	24168: 'L’identifiant de l’utilisateur ayant été modifié ne doit pas être nul ou vide.',
	24169: 'Il est possible d’inscrire une valeur pour l’identifiant de l’utilisateur ayant été modifié seulement lorsque la mise à jour de la réponse associée est fausse',
	24170: 'Identifiant de la tâche dans la demande invalide',
	24171: 'Identifiant du corps de texte déjà utilisé dans la demande',
	24172: 'Échec de la demande de comparaison de la demande',
	24173: 'Le champ des identifiants de document ne peut pas être vide et il ne peut pas contenir plus de 50 identifiants de documents distincts en même temps.',
	24174: 'Route document should be part of request body documentids',
	24175: 'Invalid smart evidence entity id in request body',
	24176: 'Body documentids should have same knowledge form id',
	24177: 'Identifiant de la section invalide',
	24178: 'Correspondance invalide entre les identifiants de document et la liste d’identifiants de document',
	24179: 'La liste d’identifiants de document ne peut pas contenir plus de 50 identifiants',
	24180: 'Mise à jour du seuil de signification impossible',
	24181: 'Identifiant du corps de texte du formulaire non valide',
	24182: 'Identifiant du formulaire de la base de connaissances ou du corps de texte du formulaire de la base de connaissances non valide',
	24183: 'Une erreur s’est produite lors de l’association du document à ce formulaire. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	24184: 'L’utilisateur n’est pas actif dans cette mission EY Canvas. Veuillez lui envoyer une invitation à partir de la page Gérer l’équipe et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage. ',
	24188: 'One or more records are no longer available. Please refresh the page',

	/*Document Change Types*/
	25001: 'Échec de la récupération des types de modifications apportées au document',
	25002: 'Identifiant de la mission non valide',
	25003: 'Demande non valide',

	/* Manage team */
	26001: 'ClientUserGroups GetAll call failed.',
	26002: 'Échec de la demande de création du nouveau groupe de clients / domaine de l’adresse courriel',
	26003: 'Le nom du groupe ne doit pas être nul.',
	26004: 'Le domaine de l’adresse courriel ne doit pas être nul.',
	26005: '{0} L’étiquette du domaine de l’adresse courriel ne doit pas dépasser 263 caractères.',
	26006: '{0} Le domaine de l’adresse courriel ne doit pas dépasser 263 caractères.',
	26007: '{0} La première partie du domaine de l’adresse courriel peut comprendre un astérisque (*) ou des caractères alphanumériques. Les autres caractères spéciaux ne sont pas permis.',
	26008: '{0} Si la première partie comporte un caractère de remplacement, elle doit être suivie de deux autres parties ou plus.',
	26009: '{0} La première partie peut comprendre un astérique (*) ou des caractères alphanumériques. Les caractères spéciaux ne sont pas permis dans les autres parties du domaine.',
	26010: 'Le domaine de l’adresse courriel doit être unique.',
	26011: 'L’identifiant du groupe d’accès client fourni est non valide.',
	26012: 'Une erreur s’est produite lors de la suppression du groupe d’accès. Assurez-vous qu’aucune demande ou tâche externe n’est attribuée à ce groupe ou aux membres de ce groupe et réessayez.',
	26013: 'Le groupe d’accès a déjà été supprimé. Veuillez actualiser la page pour afficher les données les plus récentes.',
	26014: 'Au moins un domaine de l’adresse courriel est requis.',
	26015: 'Le portail client d’EY Canvas, requis pour la mise à jour des renseignements relatifs au membre existant, n’est pas accessible pour le moment. Essayez de nouveau et si le problème persiste, communiquez avec le Service de dépannage.',
	26016: 'Une erreur s’est produite lors de l’opération de suppression.',
	26017: 'Échec de la récupération des données',
	26018: 'En raison d’un problème d’accès concurrentiel, le groupe d’accès n’est plus actif.',
	26019: 'Échec de l’enregistrement de l’opération',
	26020: 'Les domaines de l’adresse courriel ne peuvent pas être supprimés, car ils sont associés à des utilisateurs actifs. Les modifications n’ont donc pas été enregistrées.',

	/* TimePhaseTypes - Milestones */
	27001: 'Échec de l’extraction des détails du jalon',

	/*Client Request Counts*/
	28001: 'Get Client Requests Info call did not fetch any results.',

	/*Content updates Error messages*/
	29001: 'Extraction par identifiant introuvable',
	29002: 'Type d’action introuvable',
	29003: 'Identifiant(s) de contenu introuvable(s)',
	29004: 'Échec de la demande de mise à jour du contenu de l’interface de programmation',
	29005: 'La mise à jour du contenu est en cours. Veuillez réessayer et si le problème persiste, communiquez avec le Service de dépannage.',
	29006: 'Paramètres de la demande non valides.',

	/*IT Process*/
	30001: 'Get all ITProcess failed.',
	30002: 'Get all ITProcess - invalid engagement ID.',
	30003: 'Le nom du processus informatique ne peut être vide.',
	30004: 'Le nom du processus informatique ne doit pas dépasser 500 caractères.',
	30005: 'Cette action n’a pas pu être réalisée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	30006: 'Échec de la demande de récupération du processus informatique par son identifiant.',
	30007: 'Demande non valide.',
	30008: 'Le processus informatique n’est plus disponible. Veuillez actualiser la page et réessayer. Si l’erreur persiste, veuillez communiquer avec le Service de dépannage.',
	30009: 'Document introuvable.',
	30010: 'Suppression impossible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	30012: 'Suppression impossible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	30017: 'Cette action n’a pas pu être réalisée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	30018: 'Cette action n’a pas pu être réalisée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	30019: 'Cette action n’a pas pu être réalisée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/*Checklist*/
	31001: 'GetAll CheckLists call failed.',
	31002: 'Put CheckLists call failed.',
	31003: 'Invalid CheckList parameter.',
	31004: 'Invalid Engagement Error.',
	31005: 'Invalid Request Parameters Error.',
	31006: 'Invalid IsChecked request parameter Error.',

	/*Archive*/
	32001: 'L’identifiant de l’état de la mission est non valide.',
	32002: 'Échec de l’archivage',
	32003: 'Échec de la demande V1 du Centre des archives. Veuillez actualiser la page, résoudre les validations et relancer le processus d’archivage. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	32004: 'Engagement Status Update In LDC failed.',
	32005: 'Invalidate Engagement Cache Error.',
	32006: 'La mise à jour du contenu est en cours. Vous ne pouvez pas archiver cette mission avant la fin de la mise à jour. Veuillez réessayer plus tard et communiquer avec le Service de dépannage si le problème persiste.',
	32007: 'ArcGUID is null Or empty.',
	32008: 'FileGuid is null Or empty.',
	32009: 'FileStoreHostTcp is null Or empty.',
	32200: 'Cette mission comprend des validations non résolues. Veuillez actualiser la page, résoudre les validations et relancer le processus d’archivage. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	32300: 'Cette mission comprend des validations non résolues. Veuillez actualiser la page, résoudre les validations et relancer le processus d’archivage. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/*RBAC*/
	33001: 'Identifiant de la mission introuvable.',
	33002: 'Identifiant de l’utilisateur introuvable.',

	/*Helix Linked Projects*/
	34001: 'La demande ne peut pas être soumise, car l’objet de la requête est nul.',
	34002: 'L’identifiant de la mission ne doit pas être nul ou vide.',
	34003: 'Body cannot be null or empty in the call.',
	34004: 'L’identifiant du projet ne peut pas être nul ou vide.',
	34005: 'Le nom du projet ne peut pas être nul ou vide.',
	34006: 'Le projet a déjà été associé. Veuillez actualiser la page et réessayer.',
	34007: 'GetAll Helix Projects call failed.',
	34008: 'L’identifiant de la mission doit être supérieur à zéro.',
	34010: 'L’opération de sauvegarde n’a pas pu être traitée. Veuillez actualiser la page et réessayer. Si le problème persiste, communiquez avec le Service de dépannage.',
	34009: 'L’identifiant du projet a été modifié. Veuillez actualiser la page et réessayer.',
	34011: 'Le type de devise ne peut pas être nul dans l’appel.',
	34012: 'Le code de devise ne peut pas être nul dans l’appel.',
	34013: 'L’unité fonctionnelle ne peut pas être nulle ou vide dans l’appel.',
	34014: 'Le projet EY Helix associé a été modifié, les paramètres ne peuvent pas être mis à jour.',
	34017: 'Impossible de se connecter à EY Helix. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	34018: 'L’opération a échoué. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	34019: 'Importation terminée, mais données invalides. Veuillez actualiser la page et cliquer à nouveau sur Importer.',
	34027: 'Le projet EY Helix est en cours.',
	34036: 'Impossible de se connecter à EY Helix. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	34039: 'Ce projet EY Helix n’est plus le projet principal de votre mission. Veuillez actualiser la page et réessayer.',
	34040: 'Vous devez disposer de l’autorisation <b>Importer des données EY Helix</b> pour effectuer cette action. Communiquez avec un administrateur de la mission pour demander une autorisation.',

	/* PAANS */
	35001: 'Le service de rédaction des directives d’EY, d’approbation et de notification n’est pas disponible pour le moment. Nous ne sommes pas en mesure de confirmer si toutes les directives pertinentes sont prêtes. Si vous n’avez pas revu les directives pertinentes, actualisez la page et réessayez. Si le problème persiste, communiquez avec le Service de dépannage.',

	/*Engagement Comments*/
	38001: 'Échec de la demande de création de commentaires pour la mission',
	38002: 'Échec de la demande de récupération de commentaires pour la mission',
	38003: 'Cette action ne peut pas être traitée. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	38004: 'Cette action ne peut pas être traitée. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	38005: 'Cette action ne peut pas être traitée. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	38006: 'Cette action ne peut pas être traitée. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	38007: 'Cette action ne peut pas être traitée. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	38008: 'Cette action ne peut pas être traitée. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	38009: 'Cette action ne peut pas être traitée. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	38010: 'Le corps de texte ne doit pas être nul',
	38011: 'La zone de texte de commentaire ne doit pas être nulle ou vide',
	38012: 'L’entité n’existe pas pour cette mission',
	38013: 'L’identifiant de l’état des commentaires de la mission doit être supérieur à zéro',
	38014: 'L’identifiant des commentaires de la mission de l’entité parent doit être supérieur à zéro',
	38015: 'Aucun corps de texte ne correspond au critère spécifié pour un formulaire Canvas',
	38016: 'La note à laquelle vous répondez a été supprimée par un autre membre de l’équipe. Veuillez actualiser la page et réessayer.',
	38017: 'Le commentaire de la mission de l’entité parent est réglé',
	38018: 'Le commentaire de la mission de l’entité parent constitue une réponse                   ',
	38019: 'La zone de texte de commentaire doit se situer entre 1 et 4 000 caractères.',
	38020: 'L’identifiant unique de l’entité est invalide',
	38021: 'L’identifiant unique de l’entité parent est invalide.',
	38022: 'Échec de la demande de suppression des commentaires de la mission',
	38023: 'L’identifiant du commentaire ne doit pas être vide',
	38024: 'Cette action doit être valide',
	38025: 'Vous essayez de supprimer un commentaire qui n’existe plus. Veuillez actualiser la page et réessayer.',
	38026: 'Vous n’êtes pas le propriétaire du commentaire',
	38027: 'Aucun commentaire à mettre à jour n’a été trouvé',
	38028: 'Il n’est pas permis de mettre à jour les commentaires réglés',
	38029: 'Seul l’auteur peut modifier le texte de commentaires',
	38030: 'L’identifiant du commentaire de la mission est invalide',
	38031: 'L’identifiant de l’état du commentaire ne doit pas être vide',
	38032: 'Aucun enregistrement de la relation entre l’utilisateur demandé et la mission. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	38033: 'La demande de mise à jour est invalide',
	38034: 'L’identifiant du commentaire demandé est déjà utilisé pour un autre commentaire',
	38035: 'L’identifiant du commentaire est invalide',
	38036: 'L’identifiant du document ne doit pas être nul ou vide',
	38037: 'L’identifiant de l’utilisateur auquel le commentaire sera attribué ne doit pas être nul',
	38038: 'Le commentaire ne doit pas être réattribué pendant son ouverture ou sa fermeture',
	38039: 'La note pour laquelle vous essayez de supprimer une réponse a déjà été supprimée. Veuillez actualiser la page et réessayer.',
	38040: 'La réponse ne doit pas avoir de date d’échéance',
	38041: 'La réponse ne doit pas avoir de priorité',
	38042: 'Le commentaire doit avoir une date d’échéance',
	38043: 'Le commentaire doit avoir une priorité.',
	38044: 'Vous essayez de modifier une réponse à un commentaire qui n’existe plus. Veuillez actualiser la page et réessayer.',
	38045: 'Il n’est pas permis de modifier l’identifiant de l’état et le contenu de la mission, lorsqu’un utilisateur ou une priorité y sont associé',
	38046: 'Échec de la demande de mise à jour des commentaires de la mission',
	38047: 'Vous ne pouvez répondre qu’aux notes ouvertes.',
	39004: 'This tag group is no longer available. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/*Risk-Estimate*/
	4064: 'Ce risque n’est plus disponible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	4065: 'L’association de risques ne peut pas être modifiée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	4066: 'Cette estimation n’est plus disponible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/*IT App/SO*/
	81001: 'Échec de la récupération de toutes les applications informatiques.',
	81002: 'Récupérer toutes les applications informatiques ‒ identifiant de la mission invalide',
	81003: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81004: 'L’opération a échoué. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	81005: '’L’opération a échoué. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	81006: 'Type de stratégie non valide.',
	81007: 'Identifiant du document invalide.',
	81008: 'Échec de la récupération par identifiant de l’application informatique.',
	81009: 'L’opération a échoué. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	81010: '’L’opération a échoué. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	81011: 'L’opération a échoué. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	81012: "L'identifiant de l’application informatique ne doit pas être nul ou vide. ",
	81013: 'Le lien ou l’association ne peut pas être modifié. Veuillez actualiser la page et réessayer. Si l’erreur persiste, veuillez communiquer avec le Service de dépannage.',
	81014: 'Échec de la demande de suppression de l’application informatique/du processus informatique.',
	81015: 'Le champ de l’application informatique/du processus informatique ne doit pas être vide.',
	81016: '’Identifiant de l’application informatique/du processus informatique invalide.',
	81017: 'Le lien ou l’association ne peut pas être modifié. Veuillez actualiser la page et réessayer. Si l’erreur persiste, veuillez communiquer avec le Service de dépannage.',
	81018: 'Échec de la demande de création de l’application informatique/du processus informatique.',
	81019: 'Envoi de la société de services au lieu de l’application informatique.',
	81020: 'Suppression impossible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	81039: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',
	81041: 'Could not complete the operation. Refresh the page and try again. If the issue persists, contact the Help Desk.',

	/* ITControl */
	84001: 'Échec de la récupération des contrôles informatiques.',
	84002: 'Échec de la récupération du contrôle informatique par identifiant.',
	84003: 'L’identifiant du contrôle informatique est nul ou vide.',
	84004: 'Identifiant du contrôle informatique invalide.',
	84005: 'L’identifiant du document est invalide.',
	84006: 'Information manquante : nom du contrôle informatique.',
	84007: 'Longueur du nom du contrôle informatique invalide.',
	84008: 'Identifiant de la fréquence du contrôle informatique invalide.',
	84009: 'Identifiant du type d’approche pour le contrôle informatique invalide. ',
	84010: 'Identifiant du type d’efficacité de la conception du contrôle informatique invalide.',
	84011: 'Valeur de test du contrôle informatique invalide.',
	84012: 'Identifiant du type d’efficacité du fonctionnement du contrôle informatique invalide.',
	84013: 'Information manquante : fréquence du contrôle informatique.',
	84014: 'Échec de la suppression du contrôle informatique.',
	84015: 'La chaîne de recherche ne devrait pas dépasser 100 caractères.',
	84016: 'L’opération a échoué. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	84018: 'L’opération a échoué. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	84019: 'La mise à jour a échoué. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/*ITRisk*/
	86001: 'Le nom du risque lié aux TI ne peut être vide.',
	86002: 'Le nom du risque lié au TI ne doit pas dépasser 500 caractères.',
	86003: 'Cette action n’a pas pu être réalisée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	86004: 'Échec de l’appel de récupération du risque lié aux TI par identifiant.',
	86005: 'Cette action n’a pas pu être réalisée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	86006: 'L’association a échoué. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	86007: 'Cette action n’a pas pu être réalisée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	86008: 'L’association a échoué. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	86009: 'Cette action n’a pas pu être réalisée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	86010: 'Échec de la suppression du risque technologique',

	/*RiskFactorFormHeaders*/
	89001: 'Relation entre les facteurs de risque introuvable.',
	89002: 'Document introuvable.',
	89003: 'Information manquante : identifiant du document',
	89004: 'La raison dépasse la longueur maximale de 4 000 caractères.',
	89005: 'Le risque n’a pu être associé au facteur de risque sélectionné. Actualisez la page et réessayez. Si le problème persiste, communiquez avec le Service de dépannage.',
	89014: "L'identifiant du risque n’est pas valide ",
	89020: 'Le facteur de risque ne peut être enregistré.  Veuillez actualiser la page et réessayer.  Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/*Materiality*/
	91001: 'Seuil de signification introuvable.',
	91002: 'Les données n’ont pas été enregistrées. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	91003: 'Demande de mise à jour du seuil de signification non valide.',
	91004: 'Il n’est pas indiqué si le montant annualisé devrait être présenté.',
	91005: 'S’il est indiqué que le montant annualisé devrait être présenté, il est obligatoire d’indiquer un montant prévu de base.',
	91006: 'Le montant prévu de base ne peut pas compter plus 15 chiffres ou contenir des décimales.',
	91007: 'S’il est indiqué que le montant annualisé devrait être présenté, le champ où doit être indiqué le justificatif du montant prévu de base doit être vide.',
	91008: 'La justification du montant prévu pour la base de mesure est trop courte ou trop longue.',
	91009: 'Identifiant du seuil de signification invalide.',
	91010: 'Identifiant du type de corps de texte du seuil de signification invalide.',
	91011: 'La valeur nominale ne peut pas dépasser l’extrémité supérieure de la fourchette.',
	91012: 'Le seuil de signification ne peut pas compter plus de 15 chiffres et 4 décimales.',

	/*Group Structure - Sub Scopes */
	92013: 'La sous-étendue ne peut pas être supprimée, car elle est associée à au moins une zone / équipe affectée à l’audit de la composante.',
	92016: 'Le nom de la sous-étendue existe déjà.',

	/*Helix Account Mappings */
	94001: 'Le mappage des comptes n’a pas pu être enregistré. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	94004: 'Un ou plusieurs comptes EY Canvas ont été supprimés ou n’ont pas pu être mappés. Actualisez la page et réessayez.',
	94005: 'Impossible de reprendre le projet. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/*Group Instructions */
	98001: 'Une erreur s’est produite lors de la récupération des instructions pour l’audit de groupe. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	98002: 'Un ou plusieurs identifiants de la section des instructions de la base des connaissances ne sont pas valides.',
	98003: 'Une erreur s’est produite lors de la création des instructions pour l’audit de groupe. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	98004: 'Le nom de l’instruction ne peut pas être vide.',
	98005: 'Le nom de l’instruction ne peut pas dépasser 500 caractères.',
	98006: 'La description de l’instruction ne peut pas être vide.',
	98007: 'La description de l’instruction ne peut pas dépasser 30 000 caractères.',
	98009: 'Le nom de l’instruction ne peut pas être copié.',
	98010: 'La date d’échéance ne peut pas être vide.',
	98011: 'Instruction déjà supprimée.',
	98012: 'Instruction introuvable.',
	98013: 'L’identifiant de l’instruction doit être supérieur à zéro.',
	98014: 'Une erreur s’est produite lors de l’enregistrement des instructions pour l’audit de groupe. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	98015: 'Instruction mandatory scopes cannot be deleted.',
	98016: 'L’instruction a été supprimée.',
	98017: 'La tâche pour l’audit de groupe a déjà été supprimée.',
	98018: 'Edit group task after delete.',
	98019: 'Entité introuvable.',

	98020: 'Impossible de générer le paquet d’évaluation des risques. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	98021: 'Le nom du document ne peut pas être vide',
	98022: 'Le nom du document ne peut pas dépasser 115 caractères.',
	98023: 'Le nom du document ne peut contenir des caractères XML invalides.',
	98024: 'Le processus de création du paquet est en cours. Le processus peut prendre jusqu’à 10 minutes.',
	98025: 'Certaines instructions ne sont associées à aucune composante. Veuillez attribuer ces instructions à des composantes et générer à nouveau les communications relatives à l’évaluation des risques au niveau du groupe.',
	98026: 'Certaines instructions ne sont associées à aucun compte. Veuillez attribuer ces instructions à des comptes et générer à nouveau les communications relatives à l’évaluation des risques au niveau du groupe.',
	98027: 'Certains comptes ne sont associés à aucun document. Veuillez créer des documents pour ces comptes et générer à nouveau les communications relatives à l’évaluation des risques au niveau du groupe.',
	98028: 'Ce formulaire Canvas n’est pas associé à une tâche valide.',
	98029: 'Une ou plusieurs composantes ne sont là que comme Références.',
	98030: 'Une ou plusieurs composantes ne font pas partie de la mission de l’équipe principale.',
	98031: 'Une ou plusieurs étendues ne font pas partie de la mission de l’équipe principale.',
	98032: 'Le nombre de documents a dépassé la limite.',
	98033: 'Les étendues requises ne peuvent pas être supprimées des instructions.',

	/*Estimate */
	115017: 'Estimation introuvable',

	/* Group Audit */
	106003: 'Erreur. Veuillez actualiser la page et réessayer.',

	/* TasksOverview */
	117001: 'Get all TaskOverview call failed.',
	117002: 'Get all TaskOverview request cannot be empty.',
	117003: 'Identifiant de la mission non valide',
	117004: 'Invalid TaskCategory value.',
	117005: 'Valeur de l’affichage invalide.',
	117006: 'Invalid DocumentCategorySearch value.',
	117007: 'Identifiants de catégorie de tâches en double',
	117008: 'Identifiants de période en double',
	117009: 'Identifiants de tâche en double',
	117010: 'Identifiants de document en double',
	117011: 'Identifiants d’attribution à l’utilisateur en double',

	/* Multientity */
	114001: 'Échec de la récupération de toutes les entités multiples.',
	114002: 'Le nom de l’entité standard ne peut pas être  vide.',
	114003: 'Le nom de l’entité standard ne doit pas dépasser 500 caractères.',
	114004: 'Le nom de l’entité juridique standard ne peut pas être vide.',
	114005: 'Le nom de l’entité juridique standard ne doit pas dépasser 500 caractères.',
	114006: 'Les entités multiples ne peuvent être créées que dans le cadre d’une mission entités multiples-même équipe (MEST).',
	114007: 'Échec de la demande de création de compte pour entités multiples.',
	114008: 'L’entité même équipe sélectionnée a été supprimée. Veuillez fermer cette fenêtre modale pour voir la liste à jour.',
	114009: 'Identifiant du compte invalide.',
	114010: 'Le nom abrégé de l’entité standard ne doit pas dépasser 500 caractères.',
	114011: 'La demande d’envoi du profil de l’entité standard est invalide.',
	114012: 'Le corps de la demande d’envoi du profil de l’entité standard est invalide.',
	114013: 'L’identifiant du corps de la demande d’envoi du profil de l’entité standard doit être différent de celui de l’entité standard.',
	114014: 'STEntityIDs for submit profile request are invalid.',
	114015: 'Le formulaire contient des réponses incomplètes. Veuillez actualiser la page et réessayer.',
	114016: 'The content update is disabled for this.',
	114017: 'Mission sans document relatif au profil de la mission entités multiples individuel.',
	114018: 'STEntity IDs missing Multi entity individual profile.',
	114019: 'Une ou plusieurs entités que vous essayez de mapper n’existent plus dans la mission. Veuillez actualiser la page et réessayer.',
	114020: 'Le nom abrégé de l’entité standard ne doit pas être vide.',
	114021: 'Identifiant du document non valide.',
	114022: 'Identifiant de la mission non valide.',
	114023: 'STEntityDocument record already exists.',
	114024: 'STEntityDocument record does not exist.',
	114025: 'STEntityDocument record IsSystemAssociated.',
	114026: 'Corps de texte de la demande non valide.',
	114028: 'Chaque entité n’a pas un document de profil.',
	114035: 'La relation entité même équipe existe déjà.',
	114036: 'Au moins un document de profil doit être valide pour une demande de mise à jour de toutes les entités.',
	114037: 'La relation entité même équipe et compte a déjà été supprimée.',
	114038: 'Get all MultiEntity layers failed.',
	114039: 'Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',
	114040: 'Profile can only be submitted when a Primary Entity has been selected. Once selected, submit the Profile again. If the issue persists, contact the Help Desk.',

	/* Sample List */
	121101: 'Identifiant de la liste d’échantillons invalide.',
	121008: 'Identifiant de la liste d’échantillons invalide.',
	121011: 'Cet échantillon n’est plus disponible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	121013: 'Cet échantillon n’est plus disponible dans cette mission. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	121016: 'Cet échantillon n’est plus disponible. Veuillez actualiser la page et réessayer. Si l’erreur persiste, veuillez communiquer avec le Service de dépannage.',
	121037: 'Une erreur s’est produite. Veuillez actualiser la page et réessayer. Si l’erreur persiste, veuillez communiquer avec le Service de dépannage.',
	121012: 'L’état de l’attribut ne peut pas être mis à jour. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	121025: 'Ce document n’est plus disponible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	121027: 'L’association de documents ne peut pas être modifiée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	121028: 'L’association de documents ne peut pas être modifiée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	121014: 'L’état de l’attribut ne peut pas être modifié. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	121029: 'Cet attribut n’est plus disponible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	121021: 'L’état de l’attribut ne peut pas être mis à jour. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/*Control Attributes */
	122018: 'Impossible de réaliser l’action. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	122021: 'Une erreur s’est produite. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/*Flow chart*/
	123031: 'L’identifiant du corps du formulaire ne peut pas être lié à plus d’un organigramme.',

	1034: 'This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	1035: 'This action could not be completed. Refresh the page and try again. Contact the Help Desk if the error persists.',
	/*Group Instructions */
	196033: 'Les instructions doivent comporter au moins 1 élément d’attribution.',

	/*Information Security */
	200001: 'Cette action est interdite par les services de sécurité de l’information d’EY. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',

	/*Tags */
	40007: 'Cette balise n’est plus disponible. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	40029: 'L’association de balises ne peut pas être modifiée. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
};

export const roleForMember = [{
	id: 1,
	role: 'Associé responsable',
	roleAbbreviation: 'PIC'
},
{
	id: 2,
	role: 'Associé responsable de la mission',
	roleAbbreviation: ' Associé responsable de la mission'
},
{
	id: 3,
	role: 'Associé délégué',
	roleAbbreviation: 'Associé délégué/directeur'
},
{
	id: 4,
	role: 'Principal',
	roleAbbreviation: 'Principal'
},
{
	id: 5,
	role: 'Chef d’équipe senior',
	roleAbbreviation: 'Sr. Manager'
},
{
	id: 6,
	role: 'Chef d’équipe',
	roleAbbreviation: 'Chef d’équipe'
},
{
	id: 7,
	role: 'Professionnel senior',
	roleAbbreviation: 'Professionnel senior'
},
{
	id: 8,
	role: 'Membre du personnel',
	roleAbbreviation: 'Stagiaire'
},
{
	id: 9,
	role: 'Stagiaire',
	roleAbbreviation: 'Intrn'
},
{
	id: 10,
	role: 'Responsable de la revue de la qualité de la mission',
	roleAbbreviation: 'RRQM'
},
{
	id: 11,
	role: 'Autre associé',
	roleAbbreviation: 'Autre associé'
},
{
	id: 12,
	role: 'Groupe PSM – Membre du personnel',
	roleAbbreviation: 'Groupe PSM – Stagiaire'
},
{
	id: 13,
	role: 'Consultation (groupe Risque et certification en TI, SCT, Capital humain ou autre)',
	roleAbbreviation: 'ADV'
},
{
	id: 14,
	role: 'Fiscalité',
	roleAbbreviation: 'Tax'
},
{
	id: 15,
	role: 'Services de soutien aux cadres',
	roleAbbreviation: 'ESS'
},
{
	id: 16,
	role: 'Affaires juridiques',
	roleAbbreviation: 'BUREAU MONDIAL DE LA CONFORMITÉ'
},
{
	id: 17,
	role: 'Responsable du contrôle qualité de l’audit',
	roleAbbreviation: 'AQR'
},
{
	id: 18,
	role: 'Équipe affectée à l’audit d’une composante pour les entités à établissements multiples',
	roleAbbreviation: 'MCT'
},
{
	id: 19,
	role: 'Superviseur du client',
	roleAbbreviation: 'Superviseur du client'
},
{
	id: 20,
	role: 'Personnel du client',
	roleAbbreviation: 'Personnel du client'
},
{
	id: 21,
	role: 'Superviseur de la fonction d’audit interne',
	roleAbbreviation: 'Superviseur de la fonction d’audit interne'
},
{
	id: 22,
	role: 'Personnel de la fonction d’audit interne',
	roleAbbreviation: 'Personnel de la fonction d’audit interne'
},
{
	id: 23,
	role: 'Autorité de réglementation',
	roleAbbreviation: 'Autorité de réglementation'
},
{
	id: 24,
	role: 'Autre (p. ex., revue de contrôle préalable)',
	roleAbbreviation: 'Autre '
},
{
	id: 25,
	role: 'Bureau',
	roleAbbreviation: 'Bureau'
},
{
	id: 26,
	role: 'Région',
	roleAbbreviation: 'Région'
},
{
	id: 27,
	role: 'Secteur',
	roleAbbreviation: 'IND'
},
{
	id: 28,
	role: 'National',
	roleAbbreviation: 'NAT'
},
{
	id: 29,
	role: 'Mondial',
	roleAbbreviation: 'GBL'
},
{
	id: 30,
	role: 'Groupe PSM – Professionnel senior',
	roleAbbreviation: 'Groupe PSM – Professionnel senior'
},
{
	id: 31,
	role: 'Groupe PSM – Chef d’équipe',
	roleAbbreviation: 'Groupe PSM – Chef d’équipe'
}
];

export const accountConclusionTabs = {
	conclusions: 'Conclusions'
};

export const assertions = [{
	id: 1,
	assertionname: 'Exhaustivité',
	assertionabbreviation: 'Exh',
	statementtypeid: 2,
	displayorder: 1
},
{
	id: 2,
	assertionname: 'Existence',
	assertionabbreviation: 'Exi',
	statementtypeid: 2,
	displayorder: 2
},
{
	id: 3,
	assertionname: 'Évaluation',
	assertionabbreviation: 'Év',
	statementtypeid: 2,
	displayorder: 3
},
{
	id: 4,
	assertionname: 'Droits et obligations',
	assertionabbreviation: 'D&O',
	statementtypeid: 2,
	displayorder: 4
},
{
	id: 5,
	assertionname: 'Présentation et informations fournies',
	assertionabbreviation: 'P&I',
	statementtypeid: 2,
	displayorder: 5
},
{
	id: 6,
	assertionname: 'Exhaustivité',
	assertionabbreviation: 'Exh',
	statementtypeid: 1,
	displayorder: 6
},
{
	id: 7,
	assertionname: 'Réalité',
	assertionabbreviation: 'R',
	statementtypeid: 1,
	displayorder: 7
},
{
	id: 8,
	assertionname: 'Mesure',
	assertionabbreviation: 'M',
	statementtypeid: 1,
	displayorder: 8
},
{
	id: 9,
	assertionname: 'Présentation et informations fournies',
	assertionabbreviation: 'P&I',
	statementtypeid: 1,
	displayorder: 9
},
{
	id: 10,
	assertionname: 'Exhaustivité',
	assertionabbreviation: 'Exh',
	statementtypeid: 3,
	displayorder: 10
},
{
	id: 11,
	assertionname: 'Existence/Réalité',
	assertionabbreviation: 'Exi/R',
	statementtypeid: 3,
	displayorder: 11
},
{
	id: 12,
	assertionname: 'Mesure/Évaluation',
	assertionabbreviation: 'M/Év',
	statementtypeid: 3,
	displayorder: 12
},
{
	id: 13,
	assertionname: 'Droits et obligations',
	assertionabbreviation: 'D&O',
	statementtypeid: 3,
	displayorder: 13
},
{
	id: 14,
	assertionname: 'Présentation et informations fournies',
	assertionabbreviation: 'P&I',
	statementtypeid: 3,
	displayorder: 14
}
];

export const documentChangeTypesOptions = [{
	value: 1,
	label: 'Changement autre que de nature administrative'
},
{
	value: 2,
	label: 'Accepter les révisions lorsque le mode de suivi des modifications a été utilisé'
},
{
	value: 3,
	label: 'Ajouter des renvois à des éléments probants existants'
},
{
	value: 4,
	label: 'Ajouter une lettre de confirmation originale reçue précédemment par télécopie ou par courriel'
},
{
	value: 5,
	label: 'Modification superficielle'
},
{
	value: 6,
	label: 'Formulaire AP et évaluation des rôles importants en cours'
},
{
	value: 7,
	label: 'Éliminer les documents remplacés par d’autres'
},
{
	value: 8,
	label: 'Préparer la lettre d’affirmation de la direction'
},
{
	value: 9,
	label: 'Approuver les listes de contrôle relatives au processus d’archivage'
},
{
	value: 10,
	label: 'Classer et collationner les feuilles de travail définitives et y ajouter des références croisées',
},
{
	value: 12,
	label: "Mission entités multiples-même équipe (MEST) seulement : Modification apportée aux éléments probants associés à des entités pour lesquelles la date du rapport d'audit est postérieure à la date du rapport dans EY Canvas"
},
{
	value: 11,
	label: 'Lorsqu’elle est réglée au fuseau horaire local, la modification est effectuée au plus tard à la date du rapport (Amériques seulement).',
}
];

export const KnowledgeFormProfileAnswer = [{
	value: 1,
	label: 'Complexe',
	display: true
},
{
	value: 2,
	label: 'Non complexe',
	display: true
},
{
	value: 3,
	label: 'Cotée',
	display: true
},
{
	value: 4,
	label: 'Non cotée',
	display: false
},
{
	value: 5,
	label: 'PCAOB - AI',
	display: true
},
{
	value: 6,
	label: 'Autre que PCAOB-AI',
	display: false
},
{
	value: 7,
	label: 'PCAOB - ÉF',
	display: true
},
{
	value: 8,
	label: 'Autre que PCAOB-ÉF',
	display: false
},
{
	value: 9,
	label: 'Audit de groupe',
	display: true
},
{
	value: 10,
	label: 'Autre qu’un audit de groupe',
	display: false
},
{
	value: 11,
	label: 'Numérique',
	display: true
},
{
	value: 12,
	label: 'De base',
	display: true
}
];

export const strategyType = [{
	StrategyTypeId: 1,
	StrategyTypeName: 'Contrôles'
},
{
	StrategyTypeId: 2,
	StrategyTypeName: 'Corroboration'
}
];

export const controlTypeName = {
	1: 'Application',
	2: 'Contrôle manuel lié aux TI',
	3: 'Contrôle manuel de prévention',
	4: 'Contrôle manuel de détection'
};

export const inCompleteList = [{
	value: 1,
	label: 'Incomplète',
	title: 'Incomplète'
}];

export const scotTypes = [{
	value: 1,
	label: 'Courante',
	title: 'Courante',
	isDisabled: false
},
{
	value: 2,
	label: 'Non courante',
	title: 'Non courante',
	isDisabled: false
},
{
	value: 3,
	label: 'Estimation',
	title: 'Estimation',
	isDisabled: false
}
];

export const scotTypesNew = [{
	value: 1,
	label: 'Courante',
	title: 'Courante',
	isDisabled: false
},
{
	value: 2,
	label: 'Non courante',
	title: 'Non courante',
	isDisabled: false
},
{
	value: 3,
	label: 'Estimation',
	title: 'Estimation',
	isDisabled: false
},
{
	value: 4,
	label: 'PCEF',
	title: 'PCEF',
	isDisabled: false
}
];

export const estimationTypes = [{
	value: 1,
	label: 'Non',
	title: 'Non',
	isDisabled: false
},
{
	value: 2,
	label: 'Oui',
	title: 'Oui',
	isDisabled: false
}
];

/* IT Control */
export const itApproachType = [{
	ITApproachTypeId: 1,
	ITApproachTypeName: 'Contrôles'
},
{
	ITApproachTypeId: 2,
	ITApproachTypeName: 'Corroboration'
}
];

/*Controls */
export const controlFrequencyType = [{
	value: 1,
	label: 'Plusieurs fois par jour',
	title: 'Plusieurs fois par jour'
},
{
	value: 2,
	label: 'Une fois par jour',
	title: 'Une fois par jour'
},
{
	value: 3,
	label: 'Une fois par semaine',
	title: 'Une fois par semaine'
},
{
	value: 4,
	label: 'Une fois par mois',
	title: 'Une fois par mois'
},
{
	value: 5,
	label: 'Une fois par trimestre',
	title: 'Une fois par trimestre'
},
{
	value: 6,
	label: 'Une fois par année',
	title: 'Une fois par année'
},
{
	value: 8,
	label: 'Autre',
	title: 'Autre'
}
];

export const controlTypes = [{
	value: 1,
	label: 'Contrôle des applications informatiques'
},
{
	value: 2,
	label: 'Contrôle manuel lié aux TI'
},
{
	value: 3,
	label: 'Contrôle manuel de prévention'
},
{
	value: 4,
	label: 'Contrôle manuel de détection'
}
];

export const strategyTypeCheck = {
	1: 'Contrôles',
	2: 'Corroboration'
};

export const designEffectivenessType = [{
	DesignEffectivenessTypeId: 1,
	DesignEffectivenessTypeName: 'Efficace'
},
{
	DesignEffectivenessTypeId: 2,
	DesignEffectivenessTypeName: 'Inefficace'
}
];

export const controlEffectivenessType = [{
	ControlEffectivenessTypeId: 1,
	ControlEffectivenessTypeName: 'Efficace'
},
{
	ControlEffectivenessTypeId: 2,
	ControlEffectivenessTypeName: 'Inefficace'
}
];

export const testing = [{
	testingId: 1,
	testingDescription: 'Oui'
},
{
	testingId: 2,
	testingDescription: 'Non'
}
];

export const controlType = [{
	value: 1,
	label: 'Contrôle des applications informatiques',
	title: 'Contrôle des applications informatiques'
},
{
	value: 2,
	label: 'Contrôle manuel lié aux TI',
	title: 'Contrôle manuel lié aux TI'
},
{
	value: 3,
	label: 'Contrôle manuel de prévention',
	title: 'Contrôle manuel de prévention'
},
{
	value: 4,
	label: 'Contrôle manuel de détection',
	title: 'Contrôle manuel de détection'
}
];

export const aggregateITEvaluationType = [{
	value: 1,
	label: 'Appui',
	title: 'Appui'
},
{
	value: 2,
	label: 'Pas d’appui',
	title: 'Pas d’appui'
},
{
	value: 3,
	label: 'Appui - ÉF et CIIF',
	title: 'Appui - ÉF et CIIF'
},
{
	value: 4,
	label: 'Appui -  ÉF seulement',
	title: 'Appui -  ÉF seulement'
}
];

export const KnowledgeFormProfileQuestion = [{
	value: 1,
	label: 'Complexe'
},
{
	value: 2,
	label: 'Cotée'
},
{
	value: 3,
	label: 'PCAOB - AI'
},
{
	value: 4,
	label: 'PCAOB - ÉF'
},
{
	value: 5,
	label: 'Emplacement'
},
{
	value: 6,
	label: 'Langue'
},
{
	value: 7,
	label: 'Audit de groupe'
},
{
	value: 8,
	label: 'Digital'
}
];

export const KnowledgeLanguage = [{
	value: 1,
	label: 'Anglais'
},
{
	value: 2,
	label: 'Espagnol (Amérique latine)'
},
{
	value: 3,
	label: 'Français (Canada)'
},
{
	value: 4,
	label: 'Néerlandais'
},
{
	value: 5,
	label: 'Croate'
},
{
	value: 6,
	label: 'Tchèque'
},
{
	value: 7,
	label: 'Danois'
},
{
	value: 8,
	label: 'Finois'
},
{
	value: 9,
	label: 'Allemand (Allemagne, Autriche)'
},
{
	value: 10,
	label: 'Hongrois'
},
{
	value: 11,
	label: 'Italien'
},
{
	value: 12,
	label: 'Japonais (Japon)'
},
{
	value: 13,
	label: 'Norvégien (Norvège)'
},
{
	value: 14,
	label: 'Polonais'
},
{
	value: 15,
	label: 'Slovaque'
},
{
	value: 16,
	label: 'Slovène'
},
{
	value: 17,
	label: 'Suédois'
},
{
	value: 18,
	label: 'Arabe'
},
{
	value: 19,
	label: 'Chinois simplifié (Chine)'
},
{
	value: 20,
	label: 'Chinois traditionnel (Taïwan)'
},
{
	value: 21,
	label: 'Grec'
},
{
	value: 22,
	label: 'Hébreu (Israël)'
},
{
	value: 23,
	label: 'Indonésien'
},
{
	value: 24,
	label: 'Coréen (République de Corée)'
},
{
	value: 25,
	label: 'Portugais (Brésil)'
},
{
	value: 26,
	label: 'Roumain'
},
{
	value: 27,
	label: 'Russe (Russie)'
},
{
	value: 28,
	label: 'Thaï'
},
{
	value: 29,
	label: 'Turc'
},
{
	value: 30,
	label: 'Vietnamien'
},
{
	value: 31,
	label: 'PCAOB ‒ Anglais'
},
{
	value: 32,
	label: 'PCAOB ‒ Espagnol (Amérique latine)'
},
{
	value: 33,
	label: 'PCAOB ‒ Français (Canada)'
},
{
	value: 34,
	label: 'PCAOB ‒ Néerlandais'
},
{
	value: 35,
	label: 'PCAOB ‒ Croate'
},
{
	value: 36,
	label: 'PCAOB ‒ Tchèque'
},
{
	value: 37,
	label: 'PCAOB ‒ Danois'
},
{
	value: 38,
	label: 'PCAOB ‒ Finlandais'
},
{
	value: 39,
	label: 'PCAOB ‒ Allemand (Allemagne, Autriche)',
},
{
	value: 40,
	label: 'PCAOB ‒ Hongrois'
},
{
	value: 41,
	label: 'PCAOB ‒ Italien'
},
{
	value: 42,
	label: 'PCAOB ‒ Japonais (Japon)'
},
{
	value: 43,
	label: 'PCAOB ‒ Norvégien (Norvège)'
},
{
	value: 44,
	label: 'PCAOB ‒ Polonais'
},
{
	value: 45,
	label: 'PCAOB ‒ Slovaque'
},
{
	value: 46,
	label: 'PCAOB ‒ Slovène'
},
{
	value: 47,
	label: 'PCAOB ‒ Suédois'
},
{
	value: 48,
	label: 'PCAOB ‒ Arabe'
},
{
	value: 49,
	label: 'PCAOB ‒ Chinois simplifié (Chine)'
},
{
	value: 50,
	label: 'Chinois traditionnel (Taïwan)'
},
{
	value: 51,
	label: 'PCAOB ‒ Grec'
},
{
	value: 52,
	label: 'PCAOB ‒ Hébreu (Israël)'
},
{
	value: 53,
	label: 'PCAOB ‒ Indonésien'
},
{
	value: 54,
	label: 'PCAOB ‒ Coréen (République de Corée)'
},
{
	value: 55,
	label: 'PCAOB ‒ Portugais (Brésil)'
},
{
	value: 56,
	label: 'PCAOB ‒ Roumain'
},
{
	value: 57,
	label: 'PCAOB ‒ Russe (Russie)'
},
{
	value: 58,
	label: 'PCAOB ‒ Thaï'
},
{
	value: 59,
	label: 'PCAOB ‒ Turc'
},
{
	value: 60,
	label: 'PCAOB ‒ Vietnamien'
}
];

export const KnowledgeCountry = [{
	value: 1,
	label: 'Mayotte'
},
{
	value: 2,
	label: 'Îles Vierges britanniques'
},
{
	value: 3,
	label: 'Espagne'
},
{
	value: 4,
	label: 'Belize'
},
{
	value: 5,
	label: 'Pérou'
},

{
	value: 6,
	label: 'Slovaquie'
},
{
	value: 7,
	label: 'Venezuela'
},
{
	value: 8,
	label: 'Norvège'
},
{
	value: 9,
	label: 'Îles Falkland (Malvinas)'
},
{
	value: 10,
	label: 'Mozambique'
},

{
	value: 11,
	label: 'Chine'
},
{
	value: 12,
	label: 'Soudan'
},
{
	value: 13,
	label: 'Israël'
},
{
	value: 14,
	label: 'Belgique'
},
{
	value: 15,
	label: 'Arabie saoudite'
},

{
	value: 16,
	label: 'Gibraltar'
},
{
	value: 17,
	label: 'Guam'
},
{
	value: 18,
	label: 'Île Norfolk'
},
{
	value: 19,
	label: 'Zambie'
},
{
	value: 20,
	label: 'Réunion'
},

{
	value: 21,
	label: 'Azerbaïjan'
},
{
	value: 22,
	label: 'Sainte-Hélène'
},
{
	value: 23,
	label: 'Iran'
},
{
	value: 24,
	label: 'Monaco'
},
{
	value: 25,
	label: 'Saint-Pierre-et-Miquelon'
},

{
	value: 26,
	label: 'Nouvelle-Zélande'
},
{
	value: 27,
	label: 'Îles Cook'
},
{
	value: 28,
	label: 'Sainte-Lucie'
},
{
	value: 29,
	label: 'Zimbabwe'
},
{
	value: 30,
	label: 'Iraq'
},

{
	value: 31,
	label: 'Tonga'
},
{
	value: 32,
	label: 'Samoa américaine'
},
{
	value: 33,
	label: 'Maldives'
},
{
	value: 34,
	label: 'Maroc'
},
{
	value: 35,
	label: 'Normes internationales d’audit (ISA)'
},

{
	value: 36,
	label: 'Albanie'
},
{
	value: 37,
	label: 'Afghanistan'
},
{
	value: 38,
	label: 'Gambie'
},
{
	value: 39,
	label: 'Burkina Faso'
},
{
	value: 40,
	label: 'Tokelau'
},

{
	value: 41,
	label: 'Libye'
},
{
	value: 42,
	label: 'Canada'
},
{
	value: 43,
	label: 'Émirats arabes unis'
},
{
	value: 44,
	label: 'Corée, République populaire démocratique de'
},
{
	value: 45,
	label: 'Montserrat'
},

{
	value: 46,
	label: 'Groenland'
},
{
	value: 47,
	label: 'Rwanda'
},
{
	value: 48,
	label: 'Fidji'
},
{
	value: 49,
	label: 'Djibouti'
},
{
	value: 50,
	label: 'Botswana'
},

{
	value: 51,
	label: 'Koweït'
},
{
	value: 52,
	label: 'Madagascar'
},
{
	value: 53,
	label: 'Île de Man'
},
{
	value: 54,
	label: 'Hongrie'
},
{
	value: 55,
	label: 'Namibie'
},

{
	value: 56,
	label: 'Malte'
},
{
	value: 57,
	label: 'Jersey'
},
{
	value: 58,
	label: 'Thaïlande'
},
{
	value: 59,
	label: 'Saint-Christophe-et-Niévès'
},
{
	value: 60,
	label: 'Bhoutan'
},

{
	value: 61,
	label: 'Panama'
},
{
	value: 62,
	label: 'Somalie'
},
{
	value: 63,
	label: 'Bahreïn'
},
{
	value: 64,
	label: 'Bosnie-Herzégovine'
},
{
	value: 65,
	label: 'France'
},

{
	value: 66,
	label: 'Corée, République de'
},
{
	value: 67,
	label: 'Islande'
},
{
	value: 68,
	label: 'Portugal'
},
{
	value: 69,
	label: 'Tunisie'
},
{
	value: 70,
	label: 'Ghana'
},

{
	value: 71,
	label: 'Cameroun'
},
{
	value: 72,
	label: 'Grèce'
},
{
	value: 73,
	label: 'Terres australes françaises'
},
{
	value: 74,
	label: 'Îles Heard et McDonald'
},
{
	value: 75,
	label: 'Andorre'
},

{
	value: 76,
	label: 'Luxembourg'
},
{
	value: 77,
	label: 'Samoa'
},
{
	value: 78,
	label: 'Anguilla'
},
{
	value: 79,
	label: 'Pays-Bas'
},
{
	value: 80,
	label: 'Guinée-Bissau'
},

{
	value: 81,
	label: 'Nicaragua'
},
{
	value: 82,
	label: 'Paraguay'
},
{
	value: 83,
	label: 'Antigua and Barbuda'
},
{
	value: 84,
	label: 'Normes internationales d’information financière (IFRS)'
},
{
	value: 85,
	label: 'Niger'
},

{
	value: 86,
	label: 'Égypte'
},
{
	value: 87,
	label: 'État de la Cité du Vatican'
},
{
	value: 88,
	label: 'Lettonie'
},
{
	value: 89,
	label: 'Chypre'
},
{
	value: 90,
	label: 'Îles mineures éloignées des États-Unis'
},

{
	value: 91,
	label: 'Russie'
},
{
	value: 92,
	label: 'Saint-Vincent-et-les-Grenadines'
},
{
	value: 93,
	label: 'Guernesey'
},
{
	value: 94,
	label: 'Burundi'
},
{
	value: 95,
	label: 'Cuba'
},

{
	value: 96,
	label: 'Guinée équatoriale'
},
{
	value: 97,
	label: 'Territoire britannique de l’océan Indien'
},
{
	value: 98,
	label: 'Suède'
},
{
	value: 99,
	label: 'Ouganda'
},
{
	value: 100,
	label: 'Macédoine, ancienne République yougoslave de'
},

{
	value: 101,
	label: 'Swaziland'
},
{
	value: 102,
	label: 'Salvador'
},
{
	value: 103,
	label: 'Kyrgyzstan'
},
{
	value: 104,
	label: 'Irlande'
},
{
	value: 105,
	label: 'Kazakhstan'
},

{
	value: 106,
	label: 'Honduras'
},
{
	value: 107,
	label: 'Uruguay'
},
{
	value: 108,
	label: 'Géorgie'
},
{
	value: 109,
	label: 'Trinité-et-Tobago'
},
{
	value: 110,
	label: 'Autorité palestinienne'
},

{
	value: 111,
	label: 'Martinique'
},
{
	value: 112,
	label: 'Îles d’Aland'
},
{
	value: 113,
	label: 'Polynésie française'
},
{
	value: 114,
	label: 'Côte dIvoire'
},
{
	value: 115,
	label: 'Monténégro'
},

{
	value: 116,
	label: 'Afrique du Sud'
},
{
	value: 117,
	label: 'Géorgie du Sud et Îles Sandwich-du-Sud'
},
{
	value: 118,
	label: 'Yémen'
},
{
	value: 119,
	label: 'Hong Kong, Chine'
},
{
	value: 120,
	label: 'Kenya'
},

{
	value: 121,
	label: 'Tchad'
},
{
	value: 122,
	label: 'Colombie'
},
{
	value: 123,
	label: 'Costa Rica'
},
{
	value: 124,
	label: 'Angola'
},
{
	value: 125,
	label: 'Lituanie'
},

{
	value: 126,
	label: 'Syrie'
},
{
	value: 127,
	label: 'Malaisie'
},
{
	value: 128,
	label: 'Sierra Leone'
},
{
	value: 129,
	label: 'Serbie'
},
{
	value: 130,
	label: 'Pologne'
},

{
	value: 131,
	label: 'Suriname'
},
{
	value: 132,
	label: 'Haïti'
},
{
	value: 133,
	label: 'Nauru'
},
{
	value: 134,
	label: 'Sao Tomé-et-Principe'
},
{
	value: 135,
	label: 'Svalbard et Jan Mayen'
},

{
	value: 136,
	label: 'Singapour'
},
{
	value: 137,
	label: 'Moldavie'
},
{
	value: 138,
	label: 'Taïwan'
},
{
	value: 139,
	label: 'Sénégal'
},
{
	value: 140,
	label: 'Gabon'
},

{
	value: 141,
	label: 'Mexique'
},
{
	value: 142,
	label: 'Seychelles'
},
{
	value: 143,
	label: 'Micronésie, États fédérés de'
},
{
	value: 144,
	label: 'Algérie'
},
{
	value: 145,
	label: 'Italie'
},

{
	value: 146,
	label: 'Saint-Marin'
},
{
	value: 147,
	label: 'Libéria'
},
{
	value: 148,
	label: 'Brésil'
},
{
	value: 149,
	label: 'Croatie'
},
{
	value: 150,
	label: 'Îles Féroé'
},

{
	value: 151,
	label: 'Palaos'
},
{
	value: 152,
	label: 'Finlande'
},
{
	value: 153,
	label: 'Philippines'
},
{
	value: 154,
	label: 'Jamaïque'
},
{
	value: 155,
	label: 'Guyane française'
},

{
	value: 156,
	label: 'Cap-Vert'
},
{
	value: 157,
	label: 'Myanmar'
},
{
	value: 158,
	label: 'Lesotho'
},
{
	value: 159,
	label: 'Îles Vierges américaines'
},
{
	value: 160,
	label: 'Îles Caïmans'
},

{
	value: 161,
	label: 'Niué'
},
{
	value: 162,
	label: 'Togo'
},
{
	value: 163,
	label: 'Bélarus'
},
{
	value: 164,
	label: 'Dominique'
},
{
	value: 165,
	label: 'Indonésie'
},

{
	value: 166,
	label: 'Uzbékistan'
},
{
	value: 167,
	label: 'Nigéria'
},
{
	value: 168,
	label: 'Wallis-et-Futuna'
},
{
	value: 169,
	label: 'Barbade'
},
{
	value: 170,
	label: 'Sri Lanka'
},

{
	value: 171,
	label: 'Royaume-Uni'
},
{
	value: 172,
	label: 'Équateur'
},
{
	value: 173,
	label: 'Guadeloupe'
},
{
	value: 174,
	label: 'Laos'
},
{
	value: 175,
	label: 'Jordanie'
},

{
	value: 176,
	label: 'Îles Salomon'
},
{
	value: 177,
	label: 'Timor-Oriental'
},
{
	value: 178,
	label: 'Liban'
},
{
	value: 179,
	label: 'République centrafricaine'
},
{
	value: 180,
	label: 'Inde'
},

{
	value: 181,
	label: 'Île Christmas'
},
{
	value: 182,
	label: 'Vanuatu'
},
{
	value: 183,
	label: 'Brunei'
},
{
	value: 184,
	label: 'Bangladesh'
},
{
	value: 185,
	label: 'Antarctique'
},

{
	value: 186,
	label: 'Bolivie'
},
{
	value: 187,
	label: 'Turquie'
},
{
	value: 188,
	label: 'Bahamas'
},
{
	value: 189,
	label: 'Comores'
},
{
	value: 190,
	label: 'Sahara occidental'
},

{
	value: 191,
	label: 'République tchèque'
},
{
	value: 192,
	label: 'Ukraine'
},
{
	value: 193,
	label: 'Estonie'
},
{
	value: 194,
	label: 'Bulgarie'
},
{
	value: 195,
	label: 'Mauritanie'
},

{
	value: 196,
	label: 'Congo, République démocratique du'
},
{
	value: 197,
	label: 'Liechtenstein'
},
{
	value: 198,
	label: 'Pitcairn'
},
{
	value: 199,
	label: 'Danemark'
},
{
	value: 200,
	label: 'Îles Marshall'
},

{
	value: 201,
	label: 'Japon'
},
{
	value: 202,
	label: 'Autriche'
},
{
	value: 203,
	label: 'Oman'
},
{
	value: 204,
	label: 'Mongolie'
},
{
	value: 205,
	label: 'Tadjikistan'
},

{
	value: 206,
	label: 'Suisse'
},
{
	value: 207,
	label: 'Guatemala'
},
{
	value: 208,
	label: 'Érythrée'
},
{
	value: 209,
	label: 'Népal'
},
{
	value: 210,
	label: 'Mali'
},

{
	value: 211,
	label: 'Slovénie'
},
{
	value: 212,
	label: 'Îles Mariannes du Nord'
},
{
	value: 213,
	label: '(Sans objet)'
},
{
	value: 214,
	label: 'Aruba'
},
{
	value: 215,
	label: 'Congo'
},

{
	value: 216,
	label: 'Qatar'
},
{
	value: 217,
	label: 'Guinée'
},
{
	value: 218,
	label: 'États-Unis'
},
{
	value: 219,
	label: 'Éthiopie'
},
{
	value: 220,
	label: 'Autre'
},

{
	value: 221,
	label: 'Guyane'
},
{
	value: 222,
	label: 'Allemagne'
},
{
	value: 223,
	label: 'Bermude'
},
{
	value: 224,
	label: 'Îles Turques-et-Caïques'
},
{
	value: 225,
	label: 'Australie'
},

{
	value: 226,
	label: 'Kiribati'
},
{
	value: 227,
	label: 'Porto Rico'
},
{
	value: 228,
	label: 'Pakistan'
},
{
	value: 229,
	label: 'Île Maurice'
},
{
	value: 230,
	label: 'Malawi'
},

{
	value: 231,
	label: 'Turkménistan'
},
{
	value: 232,
	label: 'Cambodge'
},
{
	value: 233,
	label: 'Chili'
},
{
	value: 234,
	label: 'Nouvelle-Calédonie'
},
{
	value: 235,
	label: 'Papouasie-Nouvelle-Guinée'
},

{
	value: 236,
	label: 'Île Bouvet'
},
{
	value: 237,
	label: 'Tuvalu'
},
{
	value: 238,
	label: 'Curaçao'
},
{
	value: 239,
	label: 'République dominicaine'
},
{
	value: 240,
	label: 'Vietnam'
},

{
	value: 241,
	label: 'Îles Cocos (Keeling)'
},
{
	value: 242,
	label: 'Grenade'
},
{
	value: 243,
	label: 'Tanzanie'
},
{
	value: 244,
	label: 'Argentine'
},
{
	value: 245,
	label: 'Macao, Chine'
},

{
	value: 246,
	label: 'Bénin'
},
{
	value: 247,
	label: 'Roumanie'
},
{
	value: 248,
	label: 'Arménie'
},
{
	value: 249,
	label: 'mondial'
},
{
	value: 250,
	label: 'IFRS pour les PME'
},

{
	value: 251,
	label: 'PCGR des É.-U.'
},
{
	value: 252,
	label: 'Référentiel d’information financière de l’AICPA pour les petites et moyennes entités'
},
{
	value: 253,
	label: 'Soudan du Sud'
}
];

export const pagingSvgHoverText = {
	first: 'Première page',
	previous: 'Page précédente',
	next: 'Page suivante',
	last: 'Dernière page'
};

export const priorityTypesForDropdown = [{
	value: 1,
	label: 'Faible',
	className: 'Low'
},
{
	value: 2,
	label: 'Modéré',
	className: 'Medium'
},
{
	value: 3,
	label: 'Élevée',
	className: 'High'
},
{
	value: 4,
	label: 'Critique',
	className: 'Critical'
}
];

export const reviewNoteFilterTypes = [{
	value: 0,
	label: 'Toutes'
},
{
	value: 1,
	label: 'Ouvertes'
},
{
	value: 2,
	label: 'Réglées'
},
{
	value: 3,
	label: 'Fermées'
}
];

export const reviewStatus = [{
	id: 1,
	name: 'Ouverte'
},
{
	id: 2,
	name: 'Réglée'
},
{
	id: 3,
	name: 'Fermée'
}
];

export const reviewNoteOpenStatusOption = [{
	value: 2,
	label: 'Régler'
},
{
	value: 3,
	label: 'Fermer'
}
];

export const reviewNoteClearedStatusOption = [{
	value: 1,
	label: 'Rouvrir'
},
{
	value: 3,
	label: 'Fermer'
}
];

export const reviewNoteBulkClearedStatusOption = [{
	value: 1,
	label: 'Rouvrir'
},
{
	value: 2,
	label: 'Régler'
},
{
	value: 3,
	label: 'Fermer'
}
];

export const reviewNoteClosedStatusOption = [{
	value: 1,
	label: 'Rouvrir'
},
{
	value: 4,
	label: 'Supprimer'
}
];

export const taskTypeBadge = {
	1: 'OST',
	2: 'PST',
	3: 'WT',
	4: 'TOC',
	5: 'OSP',
	6: 'PSP',
	7: 'RT',
	8: 'GT',
	9: 'PIC',
	10: 'EQR',
	11: 'PIC/EQR',
	22: 'ACT',
	23: 'UDP'
};

export const riskTypes = [{
	id: 1,
	name: 'Risques importants',
	abbrev: 'RI',
	label: 'Important',
	title: 'Risque important'
},
{
	id: 2,
	name: 'Risques de fraude',
	abbrev: 'RF',
	label: 'Fraude',
	title: 'Risque de fraude'
},
{
	id: 3,
	name: 'Risque d’anomalies significatives',
	abbrev: 'R',
	label: 'Risque d’anomalies significatives',
	title: 'Risque d’anomalies significatives'
},
{
	id: 4,
	name: 'Estimation présentant un risque très faible',
	abbrev: 'VLRS',
	label: 'Estimation présentant un risque très faible',
	title: 'Estimation présentant un risque très faible'
},
{
	id: 5,
	name: 'Estimation présentant un risque faible',
	abbrev: 'EPRF',
	label: 'Estimation présentant un risque faible',
	title: 'Estimation présentant un risque faible'
},
{
	id: 6,
	name: 'Estimation présentant un risque élevé',
	abbrev: 'EPRE',
	label: 'Estimation présentant un risque élevé',
	title: 'Estimation présentant un risque élevé'
},
{
	id: 7,
	name: 'Estimation ‒ Non sélectionné',
	abbrev: 'ENS',
	label: 'Estimation ‒ Non sélectionné',
	title: 'Estimation ‒ Non sélectionné'
}

];

export const relatedRisksDropdownRiskTypes = [{
	id: 1,
	name: 'Risques importants',
	abbrev: 'SR',
	label: 'Important',
	title: 'Risque important'
},
{
	id: 2,
	name: 'Risques de fraude',
	abbrev: 'FR',
	label: 'Fraude',
	title: 'Risque de fraude'
},
{
	id: 3,
	name: 'Risque d’anomalies significatives',
	abbrev: 'R',
	label: 'Risque d’anomalies significatives',
	title: 'Risque d’anomalies significatives'
}
];

export const estimateTypes = [{
	id: 4,
	name: 'Estimation présentant un risque très faible',
	abbrev: 'ERTF',
	label: 'Très faible',
	title: 'Estimation présentant un risque très faible'
},
{
	id: 5,
	name: 'Estimation présentant un risque faible',
	abbrev: 'ERF',
	label: 'Faible',
	title: 'Estimation présentant un risque faible'
},
{
	id: 6,
	name: 'Estimation présentant un risque élevé',
	abbrev: 'ERE',
	label: 'Élevé',
	title: 'Estimation présentant un risque élevé'
},
{
	id: 7,
	name: 'Estimation ‒ Non sélectionnée',
	abbrev: 'S. O.',
	label: 'Non sélectionnée',
	title: 'Estimation ‒ Non sélectionnée'
}
];

export const statementTypes = [{
	id: 1,
	name: 'État des résultats'
},
{
	id: 2,
	name: 'Bilan'
},
{
	id: 3,
	name: 'Les deux'
}
];

export const RbacErrors = {
	106: 'Vous ne disposez pas des autorisations requises pour modifier le contenu. Vous pouvez obtenir les droits requis auprès de l’administrateur de la mission.'
};

export const HelixProjectValidationErrors = {
	800: 'Il semblerait que vous n’avez jamais accédé à EY Helix. Accéder à',
	801: 'Vous n’êtes pas un membre autorisé de l’équipe du projet associé. Veuillez communiquer avec l’administrateur du projet EY Helix pour obtenir l’accès.',
	901: 'Le projet EY Helix sélectionné n’est plus disponible. Cliquez sur Projets EY Helix pour associer un nouveau projet.',
	902: 'Le projet EY Helix sélectionné est inscrit comme étant à supprimer. Cliquez sur Projets EY Helix pour associer un nouveau projet.',
	903: 'Le projet EY Helix sélectionné est inscrit comme étant à entreposer. Cliquez sur Projets EY Helix pour associer un nouveau projet.',
	926: 'L’analyseur sélectionné n’est pas disponible dans EY Helix. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	927: 'L’analyse de données n’est pas disponible pour le projet associé. Veuillez accéder à EY Helix et réaliser les étapes de traitement et d’analyse de données pour continuer.',
	928: 'L’analyseur est invalide ou manquant dans EY Helix. Actualisez la page et réessayez. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
	929: 'Une erreur relative au projet EY Helix associé s’est produite. Les données ne peuvent pas être importées.'
};

export const EngagementProfileRequirementErrors = {
	108: 'Le profil de la mission n’est pas complet.'
};

export const IndependenceRequirementErrors = {
	103: 'Information manquante : Conformité aux obligations en matière d’indépendance de l’utilisateur de la mission'
};

export const strategyTypes = [{
	id: 3,
	name: 'Visée'
},
{
	id: 4,
	name: 'Non visée'
}
];

export const itAppTypes = [{
	value: 0,
	label: 'Application informatique'
},
{
	value: 1,
	label: 'Société de services'
}
];

export const confidentialityLevels = {
	[confidentialityTypes.DEFAULT]: 'Par défaut',
	[confidentialityTypes.LOW]: 'Faible',
	[confidentialityTypes.MODERATE]: 'Modéré',
	[confidentialityTypes.HIGH]: 'Élevé'

	// This has been disabled for release 2.5, uncomment if required
	// [confidentialityTypes.CONFIDENTIAL]: 'Confidentiel'
};

export const formBodyOptionRiskTypes = [{
	id: 1,
	label: 'Risque important'
},
{
	id: 2,
	label: 'Risque de fraude'
},
{
	id: 3,
	label: 'Risque d’anomalies significatives'
}
];

export const formViewTypes = [{
	value: 0,
	label: 'Formulaire'
},
{
	value: 1,
	label: 'Modifications'
},
{
	value: 2,
	label: 'Détails'
}
];

export const railFilterValidations = [{
	value: 0,
	label: 'Tout'
},
{
	value: 1,
	label: 'Réponses incomplètes'
},
{
	value: 2,
	label: 'Commentaires non résolus'
}
];

export const aresRiskTypes = [{
	id: 1,
	name: 'Risque important'
},
{
	id: 2,
	name: 'Risque de fraude'
},
{
	id: 3,
	name: 'Risque d’anomalies significatives'
},
{
	id: 4,
	name: 'Estimation présentant un risque très faible'
},
{
	id: 5,
	name: 'Estimation présentant un risque faible'
},
{
	id: 6,
	name: 'Estimation présentant un risque élevé'
},
{
	id: 7,
	name: 'Estimation ‒ Non sélectionnée'
}
];

export const materialityTypes = [{
	value: 1,
	label: 'Bénéfice avant impôts'
},
{
	value: 2,
	label: 'BAII (Bénéfice avant intérêts et impôts)'
},
{
	value: 3,
	label: 'BAIIA (Bénéfice avant intérêts, impôts et amortissement)'
},
{
	value: 4,
	label: 'Marge brute'
},
{
	value: 5,
	label: 'Produits'
},
{
	value: 6,
	label: 'Charges d’exploitation'
},
{
	value: 7,
	label: 'Capitaux propres'
},
{
	value: 8,
	label: 'Actifs'
},
{
	value: 9,
	label: 'Mesures fondées sur les activités (Autre)'
},
{
	value: 10,
	label: 'Pertes avant impôts'
},
{
	value: 11,
	label: 'Mesures fondées sur le capital (Autre)'
},
{
	value: 12,
	label: 'Mesures fondées sur les résultats (Autre)'
}
];

export const helixCurrencyType = {
	[currencyType.Functional]: 'Fonctionnelle',
	[currencyType.Reporting]: 'De présentation'
};

export const controlRiskType = [{
	id: 1,
	name: 'Appui'
},
{
	id: 2,
	name: "Pas d'appui"
},
{
	id: 3,
	name: 'Tester les procédures de la direction'
}
];

export const inherentRiskType = [{
	id: 1,
	name: 'Élevé'
},
{
	id: 2,
	name: 'Faible'
},
{
	id: 3,
	name: 'Très faible'
}
];

export const AlraInherentRiskType = [{
	id: 3,
	name: 'Non pertinent'
},
{
	id: 2,
	name: 'Faible'
},
{
	id: 1,
	name: 'Élevé'
}
];

export const scotInherentRiskType = [{
	id: 1,
	name: 'Élevé'
},
{
	id: 2,
	name: 'Faible'
},
{
	id: 3,
	name: 'Catégorie d’opérations importante non courante'
}
];

export const CRAStrings = {
	Minimal: 'Minime',
	Low: 'Faible',
	'Low +SC': "Faible + cons. part.",
	Moderate: 'Modéré',
	High: 'Élevé',
	'High +SC': "Élevé + cons. part."
};

export const priorityType = [{
	id: 1,
	name: 'Faible',
	className: 'Low',
	label: 'F'
},
{
	id: 2,
	name: 'Modéré',
	className: 'Medium',
	label: 'M'
},
{
	id: 3,
	name: 'Élevé',
	className: 'High',
	label: 'É'
},
{
	id: 4,
	name: 'Critique',
	className: 'Critical',
	label: 'C'
}
];

export const kendoLabels = {
	addComment: 'Ajouter un commentaire',
	addColumnBefore: 'Ajouter une colonne à gauche',
	addColumnAfter: 'Ajouter une colonne à droite',
	addInlineComment: 'Ajouter un commentaire incorporé',
	addRowAbove: 'Ajouter une ligne au-dessus',
	addRowBelow: 'Ajouter une ligne en dessous',
	alignLeft: 'Aligner le texte à gauche',
	alignRight: 'Aligner le texte à droite',
	alignCenter: 'Centrer le texte',
	alignFull: 'Justifier le texte',
	backgroundColor: 'Couleur de l’arrière-plan',
	bulletList: 'Insérer une liste non triée',
	bold: 'Gras',
	backColor: 'Surbrillance',
	createLink: 'Insérer un hyperlien',
	createTable: 'Créer un tableau',
	cleanFormatting: 'Effacer la mise en forme',
	deleteRow: 'Supprimer la ligne',
	deleteColumn: 'Supprimer la colonne',
	deleteTable: 'Supprimer le tableau',
	fontSizeInherit: 'Taille de la police',
	foreColor: 'Couleur de la police',
	format: 'Format',
	fontSize: 'Taille de la police',
	hyperlink: 'Insérer un lien',
	italic: 'Italique',
	indent: 'Retrait',
	insertTableHint: 'Créer un tableau {0} par {1}',
	huge: 'Très grande',
	'hyperlink-dialog-content-address': "Adresse de la page Web",
	'hyperlink-dialog-title': "Insérer un lien hypertexte",
	'hyperlink-dialog-content-title': "Titre",
	'hyperlink-dialog-content-newwindow': "Ouvrir le lien dans une nouvelle fenêtre",
	'hyperlink-dialog-cancel': "Annuler",
	'hyperlink-dialog-insert': "Insérer",
	large: 'Grande',
	noDataPlaceholder: 'Entrer le texte',
	normal: 'Normale',
	orderedList: 'Insérer une liste triée',
	outdent: 'Retrait négatif',
	paragraphSize: 'Taille de paragraphe',
	print: 'Imprimer',
	pdf: 'exporter en PDF',
	redo: 'Recommencer',
	removeFormatting: 'Supprimer le formatage',
	strikethrough: 'Barré',
	small: 'Petite',
	subscript: 'Indice',
	superscript: 'Exposant',
	underline: 'Souligné',
	undo: 'Annuler',
	unlink: 'Détacher'
};

export const kendoFormatOptions = [{
	text: 'Paragraphe',
	value: 'p'
},
{
	text: 'En-tête 1',
	value: 'h1'
},
{
	text: 'En-tête 2',
	value: 'h2'
},
{
	text: 'En-tête 3',
	value: 'h3'
},
{
	text: 'En-tête 4',
	value: 'h4'
},
{
	text: 'En-tête 5',
	value: 'h5'
},
{
	text: 'En-tête 6',
	value: 'h6'
}
];

export const kendoFontSize = [{
	text: '8',
	value: '8px'
},
{
	text: '9',
	value: '9px'
},
{
	text: '10',
	value: '10px'
},
{
	text: '11',
	value: '11px'
},
{
	text: '12',
	value: '12px'
},
{
	text: '14',
	value: '14px'
},
{
	text: '16',
	value: '16px'
},
{
	text: '18',
	value: '18px'
},
{
	text: '20',
	value: '20px'
},
{
	text: '22',
	value: '22px'
},
{
	text: '24',
	value: '24px'
},
{
	text: '26',
	value: '26px'
},
{
	text: '28',
	value: '28px'
},
{
	text: '36',
	value: '36px'
},
{
	text: '48',
	value: '48px'
},
{
	text: '72',
	value: '72px'
}
];

export const ItFlowValidationLabels = {
	ITAppWithoutAtLeastOneRelatedITProcess: 'Application informatique non associée',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: 'CGI non associé',
	ITSPHasNorelatedITRisk: 'Procédure de corroboration liée aux TI non associée',
	ITProcessHasNoRelatedITApplication: 'Processus informatique non associé',
	ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication: 'Risque lié aux TI manquant',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: 'CGI manquant ou « Aucun » indiqué',
	ITDMorITACWithNoRelatedITApplication: 'Contrôle des applications/manuel lié aux TI sans application informatique',
	ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'Procédure de corroboration liée aux TI à supprimer',
	ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'CGI à supprimer',
	ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed: 'Risque lié aux TI à supprimer',
	ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed: 'Stratégie de test associée au CGI invalide',
	ITGCWithoutASelectedDesignEffectiveness: 'Évaluation de la conception du CGI manquante',
	SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero: 'Catégorie d’opérations importante non associée',
	AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed: 'Réponse incohérente pour les tests des contrôles',
	SCOTWithHasNoITApplicationHasITDMOrAppControls: 'Application informatique manquante pour la catégorie d’opérations importante'
};

export const ISA315ITFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: ItFlowValidationLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: ItFlowValidationLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: ItFlowValidationLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: ItFlowValidationLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITSPNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITRiskIsNotDeletedWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed,
	label: ItFlowValidationLabels.ITGCWithHasItControlTestingIsOneExistsWhenCorrespondingBodyIsNotDisplayed
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: ItFlowValidationLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed,
	label: ItFlowValidationLabels.AnyITDMorITACWithHasControlTestingIsOneExistsAndFormBodyTypeId111IsNotDisplayed
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: ItFlowValidationLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
}
];

/* Notes modal labels */

export const reviewNoteModalLabels = {
	/*Review Notes*/
	engagement: 'Mission',
	emptyReplyErrorMsg: 'Ajouter du texte pour continuer',
	lengthReplyErrorMsg: 'La réponse ne doit pas dépasser 4 000 caractères.',
	documentLabel: 'Document',
	task: 'Tâche',
	allEngagementFilterLabel: 'Toutes les autres missions',
	otherEngagementComments: 'Autres notes relatives à la mission',
	notesModalInstructionalText: 'Affichez les notes pour le document/la tâche {0} sélectionné(e) ci-après et y répondre.',
	commentThread: 'Fil de la note',
	singleNoteInstructionalText: 'Afficher la note sélectionnée ci-après et y répondre.',
	emptyNoteDetailsMessage: 'Sélectionnez une note pour afficher les détails. Pour activer les contrôles en bloc, appuyez sur la touche Ctrl ou Maj et sélectionnez plusieurs notes de revue. Si vous souhaitez travailler sur une seule note, vous devez la sélectionner dans la liste.',
	documentReviewNotesLabel: 'Notes de document',
	addNewReviewNoteButtonText: 'Ajouter une note',
	noNotesAssociatedWithDocumentLabel: 'Veuillez laisser une note en utilisant les données ci-après. Attribuez la note à un utilisateur et indiquez la priorité ainsi que la date d’échéance.',
	noNotesFound: 'Aucune note trouvée',
	noNotesAssociatedWithTaskLabel: 'Aucune note de {0} n’est associée à la tâche.',
	allNotesLabel: 'Toutes les notes',
	charactersLabel: 'caractères',
	myNotesLabel: 'Mes notes',
	showClearedLabel: 'Afficher les commentaires réglés',
	showClosedLabel: 'Afficher les commentaires fermés',
	assignedToLabel: 'Attribué à',

	ofLabel: 'sur',
	enterNoteText: 'Entrer une note',
	addNewNoteModalClose: 'Fermer',
	addNewNoteModalTitleLabel: 'Ajouter une nouvelle note',
	editNoteModalTitleLabel: 'Modifier la note',
	deleteIconHoverText: 'Supprimer',
	deleteIconModalAcceptText: 'Supprimer',
	deleteIconModalConfirmMessage: 'Voulez-vous vraiment supprimer votre réponse à cette note?',
	deleteIconModalConfirmMessageParent: 'Voulez-vous vraiment supprimer la note sélectionnée?',
	deleteIconModalTitleLabel: 'Supprimer la note',
	deleteReplyIconModalTitle: 'Supprimer la réponse',
	emptyRepliesMessage: 'Aucune réponse pour l’instant',
	replyInputPlaceholder: 'Répondre à cette note',
	replyInputPlaceholderEdit: 'Modifier la réponse avec une note ou une note vocale',
	noteInputPlaceholderEdit: 'Modifier avec une note ou une note vocale',
	replyText: 'Texte de réponse',
	editReplyModelTitle: 'Modifier la réponse',
	editReplyPlaceholder: 'Saisir la réponse',
	noteDueDateLabel: 'Échéance',

	priorityLabel: 'Priorité',
	dueDateLabel: 'Date d’échéance',
	dueLabel: 'Échéance',
	status: 'État',
	noteModifiedDateLabel: 'Modifié',
	cancelLabel: 'Annuler',
	saveLabel: 'Enregistrer',
	clearedBy: 'Effacé par',
	closedBy: 'Fermé par',
	reopenedBy: 'Rouvert par',
	reply: 'Répondre',
	editIconHoverTextLabel: 'Modifier',
	required: 'Champ requis',
	closeTitle: 'Fermer',
	otherEngagementNotes: 'Autres notes relatives à la mission',
	closeLabel: 'Fermer',
	showMore: 'Afficher plus',
	showLess: 'Afficher moins',
	showMoreEllipsis: 'Afficher plus...',
	showLessEllipsis: 'Afficher moins...',
	noResultFound: 'Aucun résultat',
	engagementNameLabel: 'Nom de la mission : ',
	taskReviewNotesLabel: 'Notes de tâche',
	fromUserLabel: 'De',
	toUserLabel: 'À',
	view: 'Afficher',
	dueDateRequiredTextError: 'La date d’échéance est requise'
};

export const notesFilterLabels = [{
	id: notesFilter.allNotes,
	label: 'Toutes les notes',
	value: notesFilter.allNotes
},
{
	id: notesFilter.myNotes,
	label: 'Mes notes',
	value: notesFilter.myNotes
},
{
	id: notesFilter.authoredByMeNotes,
	label: 'Attribués à moi',
	value: notesFilter.authoredByMeNotes
}
];

export const reviewerAssignments = {
	taskLayoutHeaderAssignments: 'Affectations',
	manageAssigmentsStep2: 'Modifier la tâche attribuée',
	editAssignment: 'Modifier l’affectation',
	deleteAssignment: 'Supprimer l’affectation',
	manageAssigmentsStep3: 'Terminer l’affectation',
	taskAssigmentStatusHeader: 'État de l’affectation',
	taskAssignmentName: 'Affectation',
	dueDateAssigment: 'Échéance',
	editDueDate: 'Facultatif : Modifier le nombre de jours avant la date de fin',
	teamMemberAssigmentLabel: 'Membre de l’équipe',
	currentAssigmentLabel: 'En cours',
	handOffToAssigmentLabel: 'Transmise à : ',
	priorToEndDateLabel: 'avant la date de fin',
	noTimePhaseAssigmentLabel: 'Aucune période attribuée',
	closedByAssigmentLabel: 'Fermée par',
	onAssingmentLabel: 'le',
	preparerAssigmentOpenTitleTip: 'Transmettre cette tâche pour fermer la tâche attribuée',
	reviewerAssigmentOpenTitleTip: 'Inscrire la tâche attribuée comme étant fermée',
	reviewerAssigmentClosedTitleTip: 'Inscrire la tâche attribuée comme étant ouverte',
	AssigmentLabel: 'Transmettre cette tâche pour fermer la tâche attribuée',
	timePhaseName: 'Période : ',
	timePhaseEndDate: 'Date de fin : ',
	AssignmentType: [{
		id: 1,
		displayName: 'Préparateur'
	},
	{
		id: 2,
		displayName: 'Responsable de la revue détaillée'
	},
	{
		id: 3,
		displayName: 'Responsable de la revue générale'
	},
	{
		id: 4,
		displayName: 'Associé'
	},
	{
		id: 5,
		displayName: 'RRQM'
	},
	{
		id: 6,
		displayName: 'Autre'
	}
	],
	AssignmentStatus: [{
		id: 1,
		displayName: 'Ouverte'
	},
	{
		id: 2,
		displayName: 'En cours'
	},
	{
		id: 3,
		displayName: 'Fermée'
	},
	{
		id: 4,
		displayName: 'Non attribuée'
	}
	],
	assignmentTableColumnHeader: 'Affectation',
	teamMemberTableColumnHeader: 'Membre de l’équipe',
	dueDaysTableColumnHeader: 'Échéance',
	daysPriorToEndDate: 'jours avant la date de fin',
	handoffButton: 'Transmettre'
};
/* Notes modal labels */

export const handOffModal = {
	title: 'Transmettre',
	description: 'Transmettre cette tâche au prochain membre de l’équipe. Pour approuver un élément probant, sélectionnez une option parmi les suivantes.',
	dropdownLabel: 'Transmettre à',
	closeTitle: 'Annuler',
	confirmButton: 'Transmettre',
	evidence: 'Éléments probants',
	evidenceSignOffTitle: 'Approuver tout en tant que : ',
	existingSignOffs: 'Approbations existantes',
	noDocumentsAvailable: 'Aucun document disponible'
};

// manage scot modal labels
export const manageSCOTModal = {
	title: 'Gérer les catégories d’opérations importantes',
	description: 'Vous pouvez créer de nouvelles catégories d’opérations importantes ou modifier et supprimer des catégories d’opérations importantes existantes. Les modifications seront appliquées après l’enregistrement.',
	addSCOTLink: 'Ajouter une catégorie d’opérations importante'
};

export const deleteSCOTModal = {
	title: 'Supprimer une catégorie d’opérations importante',
	description: 'La ou les catégories d’opérations importantes suivantes seront supprimées. Cette action ne peut pas être annulée.'
};

export const manageITAppModalLabels = {
	title: 'Gérer les applications informatiques',
	description: 'Créer de nouvelles applications informatiques ou modifier/supprimer des applications informatiques existantes ci-après.',
	inputNameTitle: 'Nom de l’application informatique',
	deleteConfirmMessage: 'Voulez-vous vraiment supprimer l’application informatique <b>{0}</b>? Cette action ne peut pas être annulée.',
	addSuccessMessage: "L’application informatique'{0}' a bien été créée",
	editSuccessMessage: "Les modifications apportées à l’application informatique'{0}' ont bien été sauvegardées",
	deleteSuccessMessage: "'{0}' a bien été supprimée."
};

export const manageSOModalLabels = {
	title: 'Gérer les sociétés de services',
	description: 'Créer de nouvelles sociétés de services ou modifier/supprimer des sociétés de services existantes ci-après.',
	inputNameTitle: 'Nom de la société de services',
	deleteConfirmMessage: 'Voulez-vous vraiment supprimer la société de services <b>{0}</b>? Cette action ne peut pas être annulée.',
	addSuccessMessage: "La société de services'{0}' a bien été créée",
	editSuccessMessage: "Les modifications apportées à la société de services'{0}' ont bien été sauvegardées",
	deleteSuccessMessage: "La société de services'{0}' a bien été supprimée",
    addServiceOrganization: 'Ajouter une société de services',
	editServiceOrganization: 'Modifier la société de services',
	deleteServiceOrganization: 'Supprimer la société de services'
};

export const customNameModal = {
	title: 'Suffixe du nom de la source de l’écriture de journal',
	closeTitle: 'Annuler',
	save: 'Enregistrer',
	suffixRequired: 'Suffixe requis!',
	suffix: 'Suffixe',
	addSuffix: 'Ajouter un suffixe',
	editSuffix: 'Modifier le suffixe'
};

export const GuidedWorkFlowLabels = {
	RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM: 'Événements et situations / risque d’anomalie non associés',
	RisksUnrelatedToAnAssertionForGuidedWorkflow: 'Risques non associés',
	IncompleteMeasurementBasisForecastAmount: 'La base est incomplète',
	IncompleteMeasurementBasisForecastAmountRationale: 'Une explication doit être fournie pour la base sélectionnée',
	IncompleteMeasurementBasisAdjustedAmount: 'Le montant ajusté est incomplet',
	IncompletePlanningMateriality: 'Le SSI est incomplet',
	PlanningMaterialityGreaterThanMaximumAmount: 'Le SSI est trop élevé',
	IncompletePlanningMaterialityRationale: 'Une explication est requise pour le SSI choisi',
	IncompleteTolerableError: 'L’EA est incomplète',
	TENotWithinRangeOfAllowedValues: 'Le pourcentage de l’EA est non valide',
	IncompleteTolerableErrorRationale: 'Une explication pour l’EA est requise',
	IncompleteSAD: 'Le seuil minimal de report des anomalies est incomplet',
	SADGreaterThanMaximum: 'Le seuil minimal de report des anomalies est trop élevé',
	IncompleteSADRationale: 'Une explication est requise pour le seuil minimal de report des anomalies choisi',
	IncompletePACESelection: 'Le formulaire PACE est incomplet',
	AccountWithoutIndividualRiskAssessmentForm: 'Document manquant pour le compte {0}',
	EstimateWithoutIndividualEstimateForm: 'Document manquant pour l’estimation {0}',
	AccountWithoutIndividualAnalyticForm: 'Document manquant pour le compte {0}',
	MultiEntityWithoutIndividualProfileForm: 'Entité sans document relatif au profil de la mission entités multiples individuel',
	AccountAccountTypeIDDoesNotMatchAction: 'Sélection de la désignation de compte incohérente',
	AccountHasEstimateDoesNotMatchAction: 'Estimation comptable sélectionnée incohérente',
	AccountFormOptionHasRelatedRisksNotAssociatedToAccount: 'Risque sans compte associé',
	AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk: 'Assertion présentant un risque inhérent élevé sans risque',
	AccountMissingSubstantiveProcedure: 'Compte/entités sans procédure de corroboration',
	MultiEntityNotRelatedToALLPSTACTForRelatedAccount: 'Mise à jour du contenu obligatoire pour le compte / les entités',
	ComponentWithoutGroupInvolvementForm: 'Composante sans formulaire relatif à la participation à un audit de groupe (exclusion faite des composantes désignées comme Référence seulement)',
	ComponentWithoutRelatedGroupAssessmentInstruction: 'Composante sans instruction relative à l’évaluation des risques au niveau du groupe',
	IncompleteAssertionRiskLevel: 'Niveau de risque lié à l’assertion incomplet',
	EstimateAccountWithoutEsimatePSPIndex: 'Aucune référence PCB n’est associée à l’estimation',
	AccountExecutedWithoutRelatedComponent: 'Groupe ‒ Comptes (exécutés dans d’autres missions) sans composante devant faire l’objet d’un audit normal ou d’un audit spécifique associée',
	MultiEntityAccountWithoutRelatedToAnyMultiEntity: 'Compte sans entité associée',
	ChangeNotSubmittedMultiEntityFullProfile: 'Modifications non envoyées',
	ChangeNotSubmittedMultiEntityIndividualDocument: 'Modifications non envoyées',
	AccountTypeWithMissingInformation: 'Information manquante sur le compte',
	DocumentUploadMissingRequiredPICEQRSignOffs: 'Approbations manquantes pour les éléments probants',
	DocumentUploadMissingRequiredPICEQRSignOffRequirements: 'Exigences d’approbation manquantes pour les éléments probants',
	DocumentUploadMissingPreparerOrReviewerSignOffs: 'Téléversement de documents - Approbations du préparateur ou du responsable de la revue manquantes',
	ITAppWithoutAtLeastOneRelatedITProcess: 'Application informatique non associée',
	ITProcessHasNoRelatedITApplication: 'Processus informatique non associé',
	ITGCWithoutASelectedDesignEffectiveness: 'Évaluation de la conception du CGI manquante',
	ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne: 'CGI non associé',
	ITSPHasNorelatedITRisk: 'Procédure de corroboration liée aux TI non associée',
	ITRiskHasNoITGCIsZeroHasNoRelatedITGC: 'CGI manquant ou « Aucun » indiqué',
	EstimateWithoutAccountRelated: 'Estimation sans compte associé',
	EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount: 'Estimation présentant un risque élevé/faible associée à un compte non fondé sur une estimation',
	RiskEstimateRiskTypeIDDoesNotMatchAction: 'Réponse relative à la catégorie d’estimation non alignée sur la désignation dans « Modifier l’estimation »',
	LowerorHigherRiskEstimateWithoutEstimateSCOT: 'Estimation sans catégorie d’opérations importante valide',
	EstimateWithoutIndividualEstimateDocument: 'Document individuel manquant pour l’estimation',
	EstimateAccountWithoutHigherOrLowerRiskEstimate: 'Compte sans estimation valide',
	EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary: 'Compte d’estimation sans estimation présentant un risque élevé ou faible',
	EstimateScotWithoutHigherOrLowerRiskEstimate: 'Catégorie d’opérations d’estimation importante sans estimation présentant un risque élevé ou faible',
	HigherRiskEstimateWithoutRisk: 'Estimation présentant un risque élevé sans risque valide associé',
	PICEQRSignOffRequirements: 'PIC or EQR Signoff requirement does not match response',
	AdjustmentsWithoutAnyEvidence: 'Ajustements sans éléments probants',
	AdjustmentsThatDoNotNet: 'Ajustements non compensés',
	DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration: 'Document extrait ou en cours d’extraction pour collaboration',
	NonEngagementWideTasksMissingEvidence: 'Éléments probants manquants pour les tâches qui ne s’appliquent pas à l’ensemble de la mission',
	EstimatesMustBeMarkedHigherRisk: 'Risque important/risque de fraude associé à une estimation qui ne présente pas un risque élevé',
	SCOTEstimateNoRelatedWalkthroughForm: 'Catégorie d’opérations importante / Estimation sans test de cheminement',
	SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2: 'Catégorie d’opérations importante sans compte associé',
	AccountSignificantDisclosureWithNoRelatedSCOTV2: 'Account - Significant Account / Significant Disclosure that is not a CT only Account with no related SCOT or an Estimate when it is an Estimate Account',
	ITApplicationWithoutITAppRiskAssessmentIndividualDocument: 'Technology risk assessment missing document',
	ITApplicationWithoutITAppPlanningIndividualDocument: 'Technology missing document',
	FormContentWithoutHeader: 'Form Content without Header',
	RisksWithoutAnyRelatedAssertions: 'There are risks that have not been related to at least one assertion',
	AssertionsWithIncompleteCRA: 'There are assertions missing an inherent and/or control risk assessment',
	LimitedRiskOrInsignificantAccountMissingRationale: 'All limited risk and insignificant accounts shall have rationale provided',
	ITProcessWithoutWalkthroughDocument: 'ITProcess without IT process - Walkthrough - Individual',
	ITProcessIsUncategorized: 'IT Process - ITProcessTypeID is Uncategorized',
	ITProcessWithNoRelatedITApplication: 'ITProcess - ITProcess with no related IT Application'
};

export const GuidedWorkFlowValidationTypeResourceMapping = [{
	validationId: validationTypes.RiskEstimateRiskTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.RiskEstimateRiskTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.FormContentWithoutHeader,
	label: GuidedWorkFlowLabels.FormContentWithoutHeader
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimateEstimateSummary
},
{
	validationId: validationTypes.EstimateScotWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateScotWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.HigherRiskEstimateWithoutRisk,
	label: GuidedWorkFlowLabels.HigherRiskEstimateWithoutRisk
},
{
	validationId: validationTypes.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM,
	label: GuidedWorkFlowLabels.RiskFactorsUnrelatedToARiskOrMarkedAsNotAROMM
},
{
	validationId: validationTypes.EstimateAccountWithoutHigherOrLowerRiskEstimate,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutHigherOrLowerRiskEstimate
},
{
	validationId: validationTypes.RisksUnrelatedToAnAssertionForGuidedWorkflow,
	label: GuidedWorkFlowLabels.RisksUnrelatedToAnAssertionForGuidedWorkflow
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmount
},
{
	validationId: validationTypes.IncompleteMeasurementBasisForecastAmountRationale,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisForecastAmountRationale
},
{
	validationId: validationTypes.IncompleteMeasurementBasisAdjustedAmount,
	label: GuidedWorkFlowLabels.IncompleteMeasurementBasisAdjustedAmount
},
{
	validationId: validationTypes.IncompletePlanningMateriality,
	label: GuidedWorkFlowLabels.IncompletePlanningMateriality
},
{
	validationId: validationTypes.PlanningMaterialityGreaterThanMaximumAmount,
	label: GuidedWorkFlowLabels.PlanningMaterialityGreaterThanMaximumAmount
},
{
	validationId: validationTypes.IncompletePlanningMaterialityRationale,
	label: GuidedWorkFlowLabels.IncompletePlanningMaterialityRationale
},
{
	validationId: validationTypes.IncompleteTolerableError,
	label: GuidedWorkFlowLabels.IncompleteTolerableError
},
{
	validationId: validationTypes.TENotWithinRangeOfAllowedValues,
	label: GuidedWorkFlowLabels.TENotWithinRangeOfAllowedValues
},
{
	validationId: validationTypes.IncompleteTolerableErrorRationale,
	label: GuidedWorkFlowLabels.IncompleteTolerableErrorRationale
},
{
	validationId: validationTypes.IncompleteSAD,
	label: GuidedWorkFlowLabels.IncompleteSAD
},
{
	validationId: validationTypes.SADGreaterThanMaximum,
	label: GuidedWorkFlowLabels.SADGreaterThanMaximum
},
{
	validationId: validationTypes.IncompleteSADRationale,
	label: GuidedWorkFlowLabels.IncompleteSADRationale
},
{
	validationId: validationTypes.IncompletePACESelection,
	label: GuidedWorkFlowLabels.IncompletePACESelection
},
{
	validationId: validationTypes.AccountWithoutIndividualRiskAssessmentForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualRiskAssessmentForm
},
{
	validationId: validationTypes.EstimateWithoutIndividualEstimateForm,
	label: GuidedWorkFlowLabels.EstimateWithoutIndividualEstimateForm
},
{
	validationId: validationTypes.AccountWithoutIndividualAnalyticForm,
	label: GuidedWorkFlowLabels.AccountWithoutIndividualAnalyticForm
},
{
	validationId: validationTypes.MultiEntityWithoutIndividualProfileForm,
	label: GuidedWorkFlowLabels.MultiEntityWithoutIndividualProfileForm
},
{
	validationId: validationTypes.AccountAccountTypeIDDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountAccountTypeIDDoesNotMatchAction
},
{
	validationId: validationTypes.AccountHasEstimateDoesNotMatchAction,
	label: GuidedWorkFlowLabels.AccountHasEstimateDoesNotMatchAction
},
{
	validationId: validationTypes.AccountFormOptionHasRelatedRisksNotAssociatedToAccount,
	label: GuidedWorkFlowLabels.AccountFormOptionHasRelatedRisksNotAssociatedToAccount
},
{
	validationId: validationTypes.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk,
	label: GuidedWorkFlowLabels.AccountHigherInherentRiskAssertionWithoutRelatedIsHigherRisk
},
{
	validationId: validationTypes.MultiEntityNotRelatedToALLPSTACTForRelatedAccount,
	label: GuidedWorkFlowLabels.MultiEntityNotRelatedToALLPSTACTForRelatedAccount
},
{
	validationId: validationTypes.AccountMissingSubstantiveProcedure,
	label: GuidedWorkFlowLabels.AccountMissingSubstantiveProcedure
},
{
	validationId: validationTypes.ComponentWithoutGroupInvolvementForm,
	label: GuidedWorkFlowLabels.ComponentWithoutGroupInvolvementForm
},
{
	validationId: validationTypes.ComponentWithoutRelatedGroupAssessmentInstruction,
	label: GuidedWorkFlowLabels.ComponentWithoutRelatedGroupAssessmentInstruction
},
{
	validationId: validationTypes.EstimateAccountWithoutEstimatePSPIndex,
	label: GuidedWorkFlowLabels.EstimateAccountWithoutEsimatePSPIndex
},
{
	validationId: validationTypes.AssertionInherentRiskWithoutRelatedHigherRisk,
	label: GuidedWorkFlowLabels.IncompleteAssertionRiskLevel
},
{
	validationId: validationTypes.AccountGroupWithoutAComponent,
	label: GuidedWorkFlowLabels.AccountExecutedWithoutRelatedComponent
},
{
	validationId: validationTypes.MultiEntityAccountWithoutRelatedToAnyMultiEntity,
	label: GuidedWorkFlowLabels.MultiEntityAccountWithoutRelatedToAnyMultiEntity
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityFullProfile,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityFullProfile
},
{
	validationId: validationTypes.ChangeNotSubmittedMultiEntityIndividualDocument,
	label: GuidedWorkFlowLabels.ChangeNotSubmittedMultiEntityIndividualDocument
},
{
	validationId: validationTypes.AccountWithMissingValues,
	label: GuidedWorkFlowLabels.AccountTypeWithMissingInformation
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffs
},
{
	validationId: validationTypes.DocumentUploadMissingRequiredPICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.DocumentUploadMissingRequiredPICEQRSignOffRequirements
},
{
	validationId: validationTypes.DocumentUploadMissingPreparerOrReviewerSignOffs,
	label: GuidedWorkFlowLabels.DocumentUploadMissingPreparerOrReviewerSignOffs
},
{
	validationId: validationTypes.ITAppWithoutAtLeastOneRelatedITProcess,
	label: GuidedWorkFlowLabels.ITAppWithoutAtLeastOneRelatedITProcess
},
{
	validationId: validationTypes.ITProcessHasNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessHasNoRelatedITApplication
},
{
	validationId: validationTypes.ITGCWithoutASelectedDesignEffectiveness,
	label: GuidedWorkFlowLabels.ITGCWithoutASelectedDesignEffectiveness
},
{
	validationId: validationTypes.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne,
	label: GuidedWorkFlowLabels.ITGCHasNoRelatedITRiskOrIsRelatedToITRiskWhereHasNoITCGIsOne
},
{
	validationId: validationTypes.ITSPHasNorelatedITRisk,
	label: GuidedWorkFlowLabels.ITSPHasNorelatedITRisk
},
{
	validationId: validationTypes.ITRiskHasNoITGCIsZeroHasNoRelatedITGC,
	label: GuidedWorkFlowLabels.ITRiskHasNoITGCIsZeroHasNoRelatedITGC
},
{
	validationId: validationTypes.EstimateWithoutAccountRelated,
	label: GuidedWorkFlowLabels.EstimateWithoutAccountRelated
},
{
	validationId: validationTypes.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount,
	label: GuidedWorkFlowLabels.EstimateHigherOrLowerRiskEstimateRelatedToNonEstimateAccount
},
{
	validationId: validationTypes.LowerorHigherRiskEstimateWithoutEstimateSCOT,
	label: GuidedWorkFlowLabels.LowerorHigherRiskEstimateWithoutEstimateSCOT
},
{
	validationId: validationTypes.PICEQRSignOffRequirements,
	label: GuidedWorkFlowLabels.PICEQRSignOffRequirements
},
{
	validationId: validationTypes.EstimatesMustBeMarkedHigherRisk,
	label: GuidedWorkFlowLabels.EstimatesMustBeMarkedHigherRisk
},
{
	validationId: validationTypes.ITDMorITACWithNoRelatedITApplication,
	label: ItFlowValidationLabels.ITDMorITACWithNoRelatedITApplication
},
{
	validationId: validationTypes.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero,
	label: ItFlowValidationLabels.SCOTWithoutITApplicationsWhereHasNoITApplicationIsZero
},
{
	validationId: validationTypes.SCOTWithHasNoITApplicationHasITDMOrAppControls,
	label: ItFlowValidationLabels.SCOTWithHasNoITApplicationHasITDMOrAppControls
},
{
	validationId: validationTypes.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication,
	label: ItFlowValidationLabels.ITProcessWithNoITRiskWhereThereIsAITACOrITDMRelatedToITApplication
},
{
	validationId: validationTypes.NonEngagementWideTasksMissingEvidence,
	label: GuidedWorkFlowLabels.NonEngagementWideTasksMissingEvidence
},
{
	validationId: validationTypes.AdjustmentsWithoutAnyEvidence,
	label: GuidedWorkFlowLabels.AdjustmentsWithoutAnyEvidence
},
{
	validationId: validationTypes.AdjustmentsThatDoNotNet,
	label: GuidedWorkFlowLabels.AdjustmentsThatDoNotNet
},
{
	validationId: validationTypes.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration,
	label: GuidedWorkFlowLabels.DocumentCheckedOutOrInTheProcessOfBeingCheckedOutForCollaboration
},
{
	validationId: validationTypes.ITApplicationWithoutITAppRiskAssessmentIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppRiskAssessmentIndividualDocument
},
{
	validationId: validationTypes.SCOTEstimateNoRelatedWalkthroughForm,
	label: GuidedWorkFlowLabels.SCOTEstimateNoRelatedWalkthroughForm
},
{
	validationId: validationTypes.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2,
	label: GuidedWorkFlowLabels.SCOTWithNoRelatedSignificantAccountOrSignificantDisclosureV2
},
{
	validationId: validationTypes.AccountSignificantDisclosureWithNoRelatedSCOTV2,
	label: GuidedWorkFlowLabels.AccountSignificantDisclosureWithNoRelatedSCOTV2
},
{
	validationId: validationTypes.ITApplicationWithoutITAppPlanningIndividualDocument,
	label: GuidedWorkFlowLabels.ITApplicationWithoutITAppPlanningIndividualDocument
},
{
	validationId: validationTypes.RisksWithoutAnyRelatedAssertions,
	label: GuidedWorkFlowLabels.RisksWithoutAnyRelatedAssertions
},
{
	validationId: validationTypes.AssertionsWithIncompleteCRA,
	label: GuidedWorkFlowLabels.AssertionsWithIncompleteCRA
},
{
	validationId: validationTypes.LimitedRiskOrInsignificantAccountMissingRationale,
	label: GuidedWorkFlowLabels.LimitedRiskOrInsignificantAccountMissingRationale
},
{
	validationId: validationTypes.ITProcessWithoutWalkthroughDocument,
	label: GuidedWorkFlowLabels.ITProcessWithoutWalkthroughDocument
},
{
	validationId: validationTypes.ITProcessIsUncategorized,
	label: GuidedWorkFlowLabels.ITProcessIsUncategorized
},
{
	validationId: validationTypes.ITProcessWithNoRelatedITApplication,
	label: GuidedWorkFlowLabels.ITProcessWithNoRelatedITApplication
}

];

// Label overrides (redefine here labels / objects that apply for a different part of the application)
export const resourceOverrides = {
	['Ares']: {
		labels: {
			notAROMM: 'Il ne s’agit pas d’un risque d’anomalies significatives',
			fraudRisk: 'Risque de fraude',
			significantRisk: 'Risque important',
			identifiedRiskFactors: 'Événement/situation, risque d’anomalies significatives, risques importants et risques de fraude identifiés',
			countUnassociatedRisk: 'Situations/événements non associés/ne portant pas la mention « Il ne s’agit pas d’un risque d’anomalies significatives ».'
		},
		riskTypes: [{
			id: 1,
			name: 'Risque important',
			abbrev: 'Imp.',
			label: 'Important',
			title: 'Risque important'
		},
		{
			id: 2,
			name: 'Risque de fraude',
			abbrev: 'F',
			label: 'Fraude',
			title: 'Risque de fraude'
		},
		{
			id: 3,
			name: 'Risque d’anomalies significatives',
			abbrev: 'R',
			label: 'Risque d’anomalies significatives',
			title: 'Risque d’anomalies significatives'
		}
		]
	}
};

export const jeSourceTypes = [{
	value: 1,
	label: 'Automatisée'
},
{
	value: 2,
	label: 'Manuelle'
},
{
	value: 3,
	label: 'Les deux'
}
];

export const hasJournalEntriesOption = [{
	value: 1,
	label: 'Oui'
},
{
	value: 2,
	label: 'Non'
}
];

export const filterReviewNoteStatus = [{
	value: 1,
	label: 'Ouvertes'
},
{
	value: 2,
	label: 'Réglées'
},
{
	value: 3,
	label: 'Fermées'
}
];

export const EntitiesLabels = {
	close: 'Fermer',
	cancel: 'Annuler',
	repNoRecordMessage: 'Aucun résultat trouvé',
	edit: 'Modifier',
	delete: 'Supprimer',
	actions: 'Actions',
	show: 'Afficher',
	first: 'Première',
	last: 'Dernière',
	prev: 'Page précédente',
	next: 'Page suivante',
	search: 'Rechercher',
	primary: 'Primary',
	knowledgeRiskLabel: 'Risks from knowledge cannot be edited or deleted',


	[Entity.Account]: {
		manageEntity: 'Gérer les comptes et les informations à fournir',
		searchEntity: 'Rechercher des comptes',
		createEntity: 'Nouveau compte',
		entityName: 'compte',
		entityNameCaps: 'Compte',
		entityNamePlural: 'Comptes',
		placeholderText: 'Créez de nouveaux comptes et informations à fournir ou modifiez et supprimez des comptes et informations à fournir existants ci-dessous.',
		deleteConfirmLabel: 'Voulez-vous vraiment supprimer ce compte? Toutes les relations existantes seront retirées. Cette action ne peut pas être annulée.'
	},
	[Entity.Estimate]: {
		manageEntity: 'Gérer les estimations',
		searchEntity: 'Rechercher des estimations',
		createEntity: 'Nouvelle estimation',
		entityName: 'estimation',
		entityNameCaps: 'Estimation',
		entityNamePlural: 'Estimations',
		placeholderText: 'Créer de nouvelles estimations ou modifier/supprimer les estimations existantes ci-après.',
		deleteConfirmLabel: 'Voulez-vous vraiment supprimer cette estimation? Toutes les relations existantes seront retirées. Cette action ne peut pas être annulée.'
	},
	[Entity.Risk]: {
		manageEntity: 'Gérer les risques',
		searchEntity: 'Rechercher un risque',
		createEntity: 'Nouveau risque',
		entityName: 'risque',
		entityNameCaps: 'Risque',
		entityNamePlural: 'Risques',
		placeholderText: 'Créer de nouveaux risques ou modifier/supprimer les risques existants ci-après.',
		deleteConfirmLabel: 'Voulez-vous vraiment supprimer ce risque? Toutes les relations existantes seront retirées. Cette action ne peut pas être annulée.'
	},
	[Entity.STEntity]: {
		manageEntity: 'Gérer les entités',
		searchEntity: 'Rechercher des entités',
		createEntity: 'Nouvelle entité',
		entityName: 'entité',
		entityNameCaps: 'Entité',
		entityNamePlural: 'Entités',
		placeholderText: 'Créer de nouvelles entités ou modifier/supprimer les entités existantes ci-après.',
		deleteEntity: 'Supprimer l’entité',
		deleteConfirmLabel: 'Voulez-vous vraiment supprimer cette entité? Toutes les relations existantes seront retirées. Cette action ne peut pas être annulée.'
	},
	[Entity.Control]: {
		manageEntity: 'Gérer le contrôle',
		searchEntity: 'Rechercher un contrôle',
		createEntity: 'Nouveau contrôle',
		entityName: 'contrôle',
		entityNameCaps: 'Contrôle',
		entityNamePlural: 'Contrôles',
		placeholderText: 'Créer de nouveaux contrôles ou modifier/supprimer des contrôles existants ci-après.',
		deleteEntity: 'Supprimer le contrôle',
		deleteConfirmLabel: 'Voulez-vous vraiment supprimer ce contrôle? Toutes les relations existantes seront retirées. Cette action ne peut pas être annulée.'
	},
	[Entity.ITProcess]: {
		manageEntity: 'Gérer les processus informatiques',
		searchEntity: 'Rechercher un processus informatique',
		createEntity: 'Nouveau processus informatique',
		entityName: 'Processus informatique',
		entityNameCaps: 'Processus informatique',
		entityNamePlural: 'Processus informatiques',
		placeholderText: "Créer de nouveaux processus informatiques ou modifier/supprimer des processus informatiques existants ci-après.",
		deleteEntity: 'Supprimer le processus informatique',
		deleteConfirmLabel: 'Voulez-vous vraiment supprimer ce processus informatique? Toutes les relations existantes seront retirées. Cette action ne peut pas être annulée.'
	},
	[Entity.ITRisk]: {
		manageEntity: 'Gérer les risques technologiques',
		searchEntity: 'Rechercher des risques technologiques',
		createEntity: 'Nouveau risque technologique',
		entityName: 'Risque technologique',
		entityNameCaps: 'Risques technologiques',
		entityNamePlural: 'Risques technologiques',
		placeholderText: 'Créer de nouveaux risques technologiques ou modifier/supprimer des risques technologiques existants ci-après.',
		deleteEntity: 'Supprimer le risque technologique',
		deleteConfirmLabel: 'Voulez-vous vraiment supprimer ce risque technologique? Toutes les relations existantes seront retirées. Cette action ne peut pas être annulée.'
	},
	[Entity.ITControl]: {
		ITGC: {
			manageEntity: 'Gérer les CGI',
			searchEntity: 'Rechercher des CGI',
			createEntity: 'Nouveau CGI',
			editEntity: 'Modifier le CGI',
			viewEntity: 'Afficher le CGI',
			entityName: 'CGI',
			entityNameCaps: 'CGI',
			entityNamePlural: 'CGI',
			placeholderText: 'Créer un nouveau CGI ou modifier/supprimer des CGI existants ci-après.',
			deleteEntity: 'Supprimer le CGI',
			close: 'Fermer',
			cancel: 'Annuler',
			processIdRequired: 'Le processus informatique est requis.',
			save: 'Enregistrer',
			confirm: 'Confirmer',
			iTProcesslabel: 'Processus informatique',
			saveAndCloseLabel: 'Enregistrer et fermer',
			saveAndCreateLabel: 'Enregistrer et créer un autre compte',
			deleteConfirmLabel: 'Voulez-vous vraiment supprimer ce CGI? Toutes les relations existantes seront retirées. Cette action ne peut pas être annulée.',
			operationEffectiveness: 'Efficacité du fonctionnement',
			itDesignEffectivenessHeader: 'Efficacité de la conception',
			itTestingColumnHeader: 'Tests',
			testingTitle: 'Tests',
			frequency: 'Fréquence',
			controlOpertaingEffectiveness: 'Efficacité du fonctionnement',
			designEffectiveness: 'Efficacité de la conception',
			frequencyITGC: 'Sélectionner la fréquence',
			nameITGC: 'Nom du CGI (obligatoire)',
			itspNameRequired: 'Nom de la procédure de corroboration liée aux TI (obligatoire)',
			noResultsFound: 'Aucun résultat',
			selectITRisk: 'Sélectionner un risque technologique (obligatoire)',
			itRiskRequired: 'Risque technologique (obligatoire)',
			itRiskName: 'Risque technologique',
			inputInvaildCharacters: 'L’entrée ne peut pas contenir les caractères suivants : */:<>\\?|"',
			itControlNameRequired: 'Le nom du CGI est requis.',
			itgcTaskDescription: 'Mettre en œuvre les tests des CGI que nous avons conçus de manière à obtenir des éléments probants suffisants et appropriés sur l’efficacité de leur fonctionnement pour toute la période d’appui.',
			selectITProcess: 'Sélectionner le processus informatique (obligatoire)',
			itProcessRequired: 'Processus informatique (obligatoire)',
			riskNameRequired: 'Le risque technologique est requis.',
			addModalDescription: 'Entrer la description du CGI.',
			editModalDescription: 'Modifier le CGI et les attributs qui y sont associés.',
			controlDesignEffectiveness: {
				[0]: {
					description: 'Non sélectionné'
				},
				[1]: {
					description: 'Efficace'
				},
				[2]: {
					description: 'Inefficace'
				}
			},
			controlTesting: {
				[0]: {
					description: 'Non sélectionné'
				},
				[1]: {
					description: 'Oui'
				},
				[2]: {
					description: 'Non'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: 'Non sélectionné'
				},
				[1]: {
					description: 'Efficace'
				},
				[2]: {
					description: 'Inefficace'
				}
			}
		},
		ITSP: {
			manageEntity: 'Gérer les procédures de corroboration liées aux TI',
			searchEntity: 'Rechercher des procédures de corroboration liées aux TI',
			createEntity: 'Nouvelle procédure de corroboration liée aux TI',
			editEntity: 'Modifier la procédure de corroboration liée aux TI',
			viewEntity: 'Afficher la procédure de corroboration liée aux TI',
			inputInvaildCharacters: 'L’entrée ne peut pas contenir les caractères suivants : */:<>\\?|"',
			addModalDescription: 'Saisir la description de la procédure de corroboration liée aux TI.',
			editModalDescription: 'Modifier la procédure de corroboration liée aux TI et les attributs qui y sont associés.',
			entityName: 'Procédure de corroboration liée aux TI',
			selectITProcess: 'Sélectionner le processus informatique (obligatoire)',
			entityNameCaps: 'Procédure de corroboration liée aux TI',
			processIdRequired: 'Le processus informatique est requis.',
			entityNamePlural: 'Procédures de corroboration liées aux TI',
			itspRequired: 'Le nom de la procédure de corroboration liée aux TI est requis.',
			close: 'Fermer',
			cancel: 'Annuler',
			iTProcesslabel: 'Processus informatique',
			save: 'Enregistrer',
			confirm: 'Confirmer',
			saveAndCloseLabel: 'Enregistrer et fermer',
			riskNameRequired: 'Le risque technologique est requis.',
			saveAndCreateLabel: 'Enregistrer et créer un autre compte',
			placeholderText: 'Créer une nouvelle procédure de corroboration liée aux TI ou modifier/supprimer des procédures de corroboration liées aux TI  existantes ci-après.',
			deleteEntity: 'Supprimer le CGI',
			deleteConfirmLabel: 'Voulez-vous vraiment supprimer cette procédure de corroboration liée aux TI? Toutes les relations existantes seront retirées. Cette action ne peut pas être annulée.',
			itspTaskDescription: 'Adapter la description de cette tâche pour que la nature, le calendrier et l’étendue des procédures de corroboration liées aux TI permettent d’obtenir des éléments probants suffisants et appropriés confirmant que les risques technologiques sont traités efficacement pour toute la durée de la période d’appui.<br />Si les procédures de corroboration liées aux TI sont mises en œuvre à une date intermédiaire, concevoir et mettre en œuvre les procédures de manière à obtenir des éléments probants supplémentaires confirmant que les risques technologiques sont traités pour la période visée par nos procédures intermédiaires jusqu’à la fin de la période.<br />Nous formulons une conclusion sur les résultats des procédures de corroboration liées aux TI.',
			operationEffectiveness: 'Efficacité du fonctionnement',
			itDesignEffectivenessHeader: 'Efficacité de la conception',
			itTestingColumnHeader: 'Tests',
			testingTitle: 'Tests',
			frequency: 'Fréquence',
			controlOpertaingEffectiveness: 'Efficacité du fonctionnement',
			designEffectiveness: 'Efficacité de la conception',
			frequencyITGC: 'Sélectionner la fréquence',
			nameITGC: 'Nom du CGI (obligatoire)',
			itspNameRequired: 'Nom de la procédure de corroboration liée aux TI (obligatoire)',
			noResultsFound: 'Aucun résultat',
			selectITRisk: 'Sélectionner un risque technologique (obligatoire)',
			itRiskRequired: 'Risque technologique (obligatoire)',
			itRiskName: 'Risque technologique',
			itProcessRequired: 'Processus informatique (obligatoire)',
			controlDesignEffectiveness: {
				[0]: {
					description: 'Non sélectionné'
				},
				[1]: {
					description: 'Efficace'
				},
				[2]: {
					description: 'Inefficace'
				}
			},
			controlTesting: {
				[0]: {
					description: 'Non sélectionné'
				},
				[1]: {
					description: 'Oui'
				},
				[2]: {
					description: 'Non'
				}
			},
			controlOperationEffectiveness: {
				[0]: {
					description: 'Non sélectionné'
				},
				[1]: {
					description: 'Efficace'
				},
				[2]: {
					description: 'Inefficace'
				}
			},
		}
	},
	[Entity.ITSOApplication]: {
		manageEntity: 'Gérer l’application informatique',
		searchEntity: 'Rechercher une application informatique',
		createEntity: 'Nouvelle application informatique',
		entityName: 'Application informatique',
		entityNameCaps: 'Applications informatiques',
		entityNamePlural: 'Applications informatiques',
		placeholderText: 'Créer de nouvelles applications informatiques ou modifier/supprimer des applications informatiques existantes ci-après.',
		deleteEntity: 'Supprimer l’application informatique',
		deleteConfirmLabel: 'Voulez-vous vraiment supprimer cette application informatique? Toutes les relations existantes seront retirées. Cette action ne peut pas être annulée.'
	},
	[Entity.SCOT]: {
		manageEntity: 'Gérer les catégories d’opérations importantes',
		searchEntity: 'Rechercher des catégories d’opérations importantes',
		createEntity: 'Nouvelle catégorie d’opérations importante',
		entityName: 'Catégorie d’opérations importante',
		entityNameCaps: 'Catégories d’opérations importantes',
		entityNamePlural: 'Catégories d’opérations importantes',
		placeholderText: 'Créer de nouvelles catégories d’opérations importantes ou modifier/supprimer des catégories d’opérations importantes existantes ci-après.',
		deleteEntity: 'Supprimer la catégorie d’opérations importante',
		deleteConfirmLabel: 'Voulez-vous vraiment supprimer cette catégorie d’opérations importante? Toutes les relations existantes seront retirées. Cette action ne peut pas être annulée.'
	},
	[Entity.SampleItem]: {
		manageEntity: 'Manage sample tags',
		searchEntity: 'Rechercher des balises',
		createEntity: 'Nouvelle balise',
		createManageTagEntity: 'Gérer des groupes de balises',
		entityName: 'balise d’échantillon',
		entityNamePlural: 'Balises',
		entityNameForTagGroupPlural: 'Groupe de balises',
		placeholderText: 'Créer de nouvelles balises ou modifier/supprimer des balises existantes ci-après. Pour créer de nouveaux groupes de balises, cliquez sur <b>« Gérer les groupes de balises »</b>',
		deleteEntity: 'Supprimer la balise d’échantillon',
		deleteConfirmLabel: 'Voulez-vous vraiment supprimer la balise sélectionnée? Elle sera supprimée de tous les échantillons auxquels elle est associée. Cette action ne peut pas être annulée.'
	},
	[Entity.SampleTagGroups]: {
		manageEntity: 'Gérer les groupes de balises d’échantillon',
		searchEntity: 'Search tag groups',
		createEntity: 'Nouveau groupe de balises',
		entityName: 'sample tag group',
		entityNameCaps: 'Groupe de balises',
		entityNamePlural: 'Groupes de balises',
		placeholderText: 'Créer de nouveaux groupes de balises ou modifier/supprimer des groupes de balises d’échantillon existants ci-après.',
		deleteConfirmLabel: 'Voulez-vous vraiment supprimer la balise sélectionnée? Elle sera supprimée de tous les échantillons auxquels elle est associée. Cette action ne peut pas être annulée.'
	},
};

export const inherentRiskFactorTypes = [{
	id: 1,
	label: 'Complexité',
	displayOrder: 1
},
{
	id: 2,
	label: 'Incertitude liée au degré de subjectivité',
	displayOrder: 2
},
{
	id: 3,
	label: 'Fraude ou erreur',
	displayOrder: 3
},
{
	id: 4,
	label: 'Modification',
	displayOrder: 4
},
{
	id: 5,
	label: 'Nature du compte',
	displayOrder: 5
},
{
	id: 6,
	label: 'Parties liées',
	displayOrder: 6
}
];

export const executionType = [{
	id: 1,
	label: 'PT',
	toolTip: 'Procédures dans cette mission [équipe principale seulement]',
	value: 'Dans cette mission [équipe principale seulement]'
},
{
	id: 2,
	label: 'CT',
	toolTip: 'Procédures dans d’autres missions [équipe composante seulement]',
	value: 'Dans d’autres missions [équipe composante seulement]'
},
{
	id: 3,
	label: 'PT/CT',
	toolTip: 'Procédures dans cette mission et dans d’autres missions [équipe principale/composante]',
	value: 'Dans cette mission et dans d’autres missions [équipe principale/composante]'
}
];

export const createEditAccountModalLabels = {
	createModalDescription: 'Entrez les détails relatifs au nouveau compte et sélectionnez ’<b>{0}</b>’ pour terminer. Pour créer un autre compte, sélectionnez ’<b>{1}</b>’.',
	editModalDescription: "Modifier les détails du compte ci-dessous et sélectionner '<b>{0}</b>' pour terminer.",
	close: 'Fermer',
	cancel: 'Annuler',
	createAccount: 'Nouveau compte',
	editAccount: 'Modifier le compte',
	newSignificantDisclosure: 'Nouvelle information à fournir importante',
	save: 'Enregistrer',
	confirm: 'Confirmer',
	saveAndCloseLabel: 'Enregistrer et fermer',
	saveAndCreateLabel: 'Enregistrer et créer un autre compte',
	accountNameLabel: 'Nom du compte (obligatoire)',
	accountDesignationLabel: 'Désignation',
	accountExecutionTypeLabel: 'Dans quelle mission Canvas les procédures pour ce compte seront-elles mises en œuvre et documentées?',
	accountEstimateLabel: 'Une estimation a-t-elle une incidence sur ce compte?',
	yes: 'Oui',
	no: 'Non',
	accountStatementTypeLabel: 'Type d’état',
	pspIndexDropdownLabel: 'Référence PCB (obligatoire, maximum 5)',
	removePSPIndexLabel: 'Supprimer la référence PCB',
	assertionsLabel: 'Sélectionner les assertions pertinentes',
	accountTypeOptions: AccountType,
	assertionOptions: assertions,
	executionTypeOptions: executionType,
	statementTypeOptions: statementTypes,
	noOptionsMessage: 'Aucun résultat',
	accountNameErrorMsg: 'Obligatoire',
	pspIndexErrorMsg: 'Obligatoire',
	assertionWarningMessage: 'Vous ne pouvez pas modifier les assertions qui sont associées à un risque important, un risque de fraude, un risque d’anomalies significatives ou une estimation. Vous devez d’abord supprimer ces relations.',
	confirmChanges: 'Confirmer les modifications',
	executionTypeWarningMessage: 'Les modifications que vous vous apprêtez à enregistrer pour ce compte auront une incidence sur les assertions et les PCB existantes : les liens seront retirés. Voulez-vous vraiment continuer? Cette action ne peut pas être annulée.',
	contentUpdateToastMessage: 'Mise à jour de contenu disponible pour {0}. Veuillez lancer la mise à jour du contenu à partir de la page Mise à jour du contenu.',
	assertionsRequired: 'Au moins une assertion doit être sélectionnée',
	pspIndexDisabledLabel: 'Sélectionner jusqu’à cinq références PCB. Désélectionner une ou plusieurs options pour continuer.'
};

export const createEditRiskModalLabels = {
	createModalDescription: 'Entrez les détails relatifs au nouveau risque ci-dessous et sélectionnez ’<b>{0}</b>’ pour terminer. Pour créer un autre risque, sélectionnez ’<b>{1}</b>’.',
	editModalDescription: 'Modifier les détails relatifs au risque ci-dessous et sélectionner ’<b>{0}</b>’ pour terminer.',
	close: 'Fermer',
	cancel: 'Annuler',
	createRisk: 'Nouveau risque',
	editRisk: 'Modifier le risque',
	riskType: 'Type de risque (obligatoire)',
	riskTypeOptions: [{
		value: 1,
		label: 'Risque important'
	},
	{
		value: 2,
		label: 'Risque de fraude'
	},
	{
		value: 3,
		label: 'Risque d’anomalies significatives'
	}
	],
	save: 'Enregistrer',
	saveAndCloseLabel: 'Enregistrer et fermer',
	saveAndCreateLabel: 'Enregistrer et créer un autre risque',
	riskNameLabel: 'Nom du risque (obligatoire)',
	relatedAccountsAssertionsLabel: 'Comptes et assertions connexes (facultatif)',
	relateAccounts: 'Comptes connexes',
	assertionsLabel: 'Sélectionner les assertions pertinentes',
	riskNameErrorMsg: 'Obligatoire',
	riskTypeErrorMsg: 'Obligatoire',
	assertionOptions: assertions,
	removeAccountLabel: 'Supprimer le compte',
	required: 'Obligatoire',
	assertionsRequired: 'Au moins une assertion doit être sélectionnée'
};

export const CreateEditMestLabels = {
	createModalTitle: 'Nouvelle entité',
	createModalDescription: 'Entrez les détails relatifs à la nouvelle entité ci-dessous et sélectionnez ’<b>{0}</b>’ pour terminer. Pour créer une autre entité, sélectionnez ’<b>{1}</b>’.',
	close: 'Fermer',
	cancel: 'Annuler',
	save: 'Enregistrer',
	confirm: 'Confirm',
	primary: 'Primary',
	saveAndCloseLabel: 'Enregistrer et fermer',
	saveAndCreateLabel: 'Enregistrer et créer une autre entité',
	entityNameLabel: 'Nom de l’entité (obligatoire)',
	entityStandardIndexLabel: 'Référence standard de l’entité (obligatoire)',
	entityDescriptionLabel: 'Description de l’entité',
	entityNameErrorMsg: 'Obligatoire',
	entityStandardIndexErrorMsg: 'Obligatoire',
	editModalTitle: 'Modifier l’entité',
	editModalDescription: "Modifiez les détails relatifs à l’entité ci-dessous et sélectionnez'<b>{0}</b>' pour terminer.",
	primaryEntitySelectionLabel: 'Select as the primary entity',
	primaryEntitySelectionMsg: "Only one entity in the engagement can be selected as the primary entity, which will be the determinant for the content delivered to the engagement. An entity will need to be selected as the primary to be able to submit the engagement profile. \'Update content\' permission is required to make or edit the primary entity selection.",
	primaryEntityDisableSelectionLabel: "To change the primary entity designation, select from the \'Edit\' of the entity you wish to designate as primary",
	noAccessLabel: 'Unauthorized. Contact your administrator and try again.',
	primaryEntityConfirmationLabel: 'Primary entity confirmation',
	primaryEntityConfirmationDisplay: '{0} is currently selected as the primary entity. Are you sure you want to change the primary entity?',
	profileV2ChangeNotSubmittedBannerMessage: 'Changes have been made to the profile that will result in content updates. Submit the profile to receive the new content or revert the answers to the previous state.',
};

export const CreateEditITProcessLabels = {
	close: 'fermer',
	cancel: 'annuler',
	yes: 'Oui',
	no: 'Non',
	delete: 'supprimer',
	save: 'Enregistrer',
	saveAndCloseLabel: 'Enregistrer et fermer',
	saveAndCreateLabel: 'Enregistrer et créer un autre compte',
	newITProcessLabel: 'Nouveau processus informatique',
	editITProcessLabel: 'Modifier le processus informatique',
	viewITProcessLabel: 'Afficher le processus informatique',
	addDescriptionLabel: 'Entrez les détails relatifs au nouveau processus informatique ci-dessous et sélectionnez ’<b>{0}</b>’ pour terminer. Pour créer un autre processus informatique, sélectionnez ’<b>{1}</b>’.',
	editDescriptionLabel: "Modifiez les détails relatifs au processus informatique ci-dessous et sélectionnez'<b>{0}</b>' pour terminer.",
	iTProcessNameLabel: 'Nom du processus informatique (obligatoire)',
	confirm: 'Confirmer',
	confirmChanges: 'Confirmer',
	iTProcessNameErrorMsg: 'Obligatoire',
	inputInvaildCharacters: 'L’entrée ne peut pas contenir les caractères suivants : */:<>\\?|"',
	remove: 'Retirer'
};

export const CreateEditITRiskLabels = {
	close: 'Fermer',
	cancel: 'Annuler',
	yes: 'Oui',
	no: 'Non',
	delete: 'supprimer',
	save: 'Enregistrer',
	saveAndCloseLabel: 'Enregistrer et fermer',
	saveAndCreateLabel: 'Enregistrer et créer un autre',
	newITRiskLabel: 'Nouveau risque technologique',
	editITRiskLabel: 'Modifier le risque technologique',
	itRiskNameLabel: 'Risque technologique (obligatoire)',
	confirm: 'Confirmer',
	confirmChanges: 'Confirmer',
	itRiskNameErrorMsg: 'Obligatoire',
	itProcessNotSelectedErrorMsg: 'Obligatoire',
	hasNoITGCLabel: 'Aucun CGI ne répond au risque technologique.',
	editModalDescription: 'Modifier la description du risque technologique.',
	createModalDescription: 'Entrer la description du risque technologique.',
	selectITProcess: 'Sélectionner le processus informatique (obligatoire)',
	noITProcessAvailable: 'Aucun processus informatique créé',
	relatedITProcessLabel: 'Processus informatique associé',
	inputInvaildCharacters: 'L’entrée ne peut pas contenir les caractères suivants : */:<>\\?|"',
	remove: 'Retitrer'
};

export const CreateEditEstimateLabels = {
	createModalDescription: 'Entrez les détails relatifs à la nouvelle estimation ci-dessous et sélectionnez ’<b>{0}</b>’ pour terminer. Pour créer une autre estimation, sélectionnez ’<b>{1}</b>’.',
	editModalDescription: "Modifier les détails relatifs à l’estimation ci-dessous et sélectionner'<b>{0}</b>' pour terminer.",
	close: 'Fermer',
	cancel: 'Annuler',
	save: 'Enregistrer',
	saveAndCloseLabel: 'Enregistrer et fermer',
	saveAndCreateLabel: 'Enregistrer et créer une nouvelle estimation',
	createModalTitle: 'Nouvelle estimation',
	editEstimateLabel: 'Modifier l’estimation',
	estimateNameLabel: 'Nom de l’estimation (obligatoire)',
	analysisPeriodBalance: 'Solde à la date de l’analyse (obligatoire)',
	analysisPeriodDate: 'Date de l’analyse (obligatoire)',
	comparativePeriodBalance: 'Solde à la date de comparaison (obligatoire)',
	comparativePeriodDate: 'Date de comparaison (obligatoire)',
	estimateCategory: 'Catégorie d’estimation (obligatoire)',
	confirm: 'Confirmer',
	estimateNameErrorMsg: 'Obligatoire',
	analysisPeriodBalanceErrorMsg: 'Requis',
	analysisPeriodDateErrorMsg: 'Requis',
	comparativePeriodBalanceErrorMsg: 'Requis',
	comparativePeriodDateErrorMsg: 'Requis',
	estimateCategoryErrorMsg: 'Obligatoire',
	remove: 'Retirer',
	balanceNOTApplicable: 'Soldes non applicables',
	wtDetailPrefixForEstimate: '<p>Complete the estimate walkthrough task.</p>',

	riskLevelOptions: [{
		value: 4,
		label: 'Risque très faible'
	},
	{
		value: 5,
		label: 'Risque faible'
	},
	{
		value: 6,
		label: 'Risque élevé'
	},
	{
		value: 7,
		label: 'Non sélectionné'
	}
	]
};

export const CreateEditControlLabels = {
	createModalTitle: 'Nouveau contrôle',
	editModalTitle: 'Modifier le contrôle',
	viewModalTitle: 'Afficher le contrôle',
	close: 'Fermer',
	cancel: 'Annuler',
	save: 'Enregistrer',
	saveAndCloseLabel: 'Enregistrer et fermer',
	saveAndCreateLabel: 'Enregistrer et créer un autre contrôle',
	controlNameLabel: 'Nom du contrôle (obligatoire)',
	frequency: 'Fréquence',
	controlType: 'Type de contrôle',
	frequencyTypeOptions: controlFrequencyType,
	controlTypeOptions: controlTypes,
	designEffectiveness: 'Efficacité de la conception',
	operatingEffectiveness: 'Efficacité du fonctionnement',
    testingLabel: 'Test',
	lowerRiskLabel: 'Is control lower risk?',
	effective: 'Efficace',
	ineffective: 'Inefficace',
	yes: 'Oui',
	no: 'Non',
	required: 'Obligatoire',
	remove: 'Retitrer',
	noOptionsMessage: 'Aucun résultat',
	disabledTabTooltipMessage: 'Sélectionner « Contrôle des applications informatiques » ou « Contrôle manuel lié aux TI » comme « Type de contrôle » pour associer les applications informatiques',
	itAppLabels: {
		tabLabel: 'Applications informatiques',
		dropdownLabel: 'Associer les applications informatiques',
		noRelatedItems: 'Aucune application informatique associée',
		itApplications: 'Applications informatiques'
	},
	soLabels: {
		tabLabel: 'SS',
		dropdownLabel: 'Associer des sociétés de services',
		noRelatedItems: 'Aucune société de services associée',
		serviceOrganizations: 'Sociétés de services'
	},
	controlNameErrorMsg: 'Obligatoire',
	createModalDescriptionLabel: 'Entrez les détails relatifs au nouveau contrôle ci-dessous et sélectionnez ’<b>{0}</b>’ pour terminer. Pour créer un autre contrôle, sélectionnez ’<b>{1}</b>’.',
	editModalDescriptionLabel: "Modifiez les détails relatifs au contrôle ci-dessous et sélectionnez'<b>{0}</b>' pour terminer.",
	viewModalDescriptionLabel: 'Afficher le contrôle et les applications informatiques et les sociétés de services associées.',
	wcgwLabel: 'Erreur possible'
};

export const ITApplicationTypeLabels = [{
	value: 1,
	label: 'Application/Outil'
},
{
	value: 2,
	label: 'Base de données'
},
{
	value: 3,
	label: 'Système d’exploitation'
},
{
	value: 4,
	label: 'Réseau'
},
{
	value: 6,
	label: 'Non classé'
}
];

export const CreateEditITApplicationLabels = {
	close: 'Fermer',
	cancel: 'Annuler',
	yes: 'Oui',
	no: 'Non',
	delete: 'Supprimer',
	save: 'Enregistrer',
	saveAndCloseLabel: 'Enregistrer et fermer',
	saveAndCreateLabel: 'Enregistrer et créer une autre application informatique',
	newITApplicationLabel: 'Nouvelle application informatique',
	editITApplicationLabel: 'Modifier l’application informatique',
	iTApplicationNameLabel: 'Nom de l’application informatique',
	confirm: 'Confirmer',
	confirmChanges: 'Confirmer les modifications',
	noOptionsMessage: 'Aucun résultat',
	iTAppNameErrorMsg: 'Obligatoire',
	controls: 'Contrôles',
	substantive: 'Corroboration',
	remove: 'Retirer',
	iTApplicationStrategyLabel: 'Stratégie liée à l’application informatique',
	SCOTsLabel: 'Nom de la catégorie d’opérations importante',
	StrategyLabel: 'Stratégie',
	ControlsLabel: 'Contrôles',
	ControlTypeLabel: 'Type',
	addDescriptionLabel: 'Entrez les détails relatifs à la nouvelle application informatique ci-dessous et sélectionnez ’<b>{0}</b>’ pour terminer. Pour créer une autre application informatique, sélectionnez ’<b>{1}</b>’.',
	editDescriptionLabel: 'Modifiez les détails relatifs à l’application informatique ci-dessous et sélectionnez ’<b>{0}</b>’ pour terminer.',
	scotErrorMessage: 'La catégorie d’opérations importante ne peut pas être dissociée de l’application informatique, car des contrôles sont associés.',
	SCOTsLabels: {
		tabLabel: 'Catégories d’opérations importantes',
		dropdownLabel: 'Associer des catégories d’opérations importantes',
		noRelatedItems: 'Aucune catégorie d’opérations importante associée'
	},
	ControlsLabels: {
		tabLabel: 'Contrôles',
		dropdownLabel: 'Associer des contrôles',
		noRelatedItems: 'Aucun contrôle associé'
	},
	strategyType: {
		1: 'Contrôles',
		2: 'Corroboration',
		3: 'Appui',
		4: 'Pas d’appui'
	},
	controlType: {
		1: 'Application informatique',
		2: 'Contrôle manuel lié aux TI',
		3: 'Contrôle manuel de prévention',
		4: 'Contrôle manuel de détection'
	},
	technologyTypeOptions: ITApplicationTypeLabels,
	technologyType: 'Select technology type'
};

export const CreateEditSCOTLabels = {
	createModalTitle: 'Nouvelle catégorie d’opérations importante',
	editModalTitle: 'Modifier la catégorie d’opérations importante',
	viewModalTitle: 'Afficher la catégorie d’opérations importante',
	createModalDescription: 'Entrez les détails relatifs à la nouvelle catégorie d’opérations importante ci-dessous et sélectionnez ’<b>{0}</b>’ pour terminer. Pour créer une autre catégorie d’opérations importante, sélectionnez ’<b>{1}</b>’.',
	editModalDescription: "Modifiez les détails relatifs à la catégorie d’opérations importante ci-dessous et sélectionnez'<b>{0}</b>' pour terminer.",
	close: 'Fermer',
	cancel: 'Annuler',
	save: 'Enregistrer',
	saveAndCloseLabel: 'Enregistrer et fermer',
	saveAndCreateLabel: 'Enregister et créer une autre catégorie d’opérations importante',
	scotNameLabel: 'Nom de la catégorie d’opérations importante (obligatoire)',
	scotStrategyLabel: 'Stratégie axée sur la catégorie d’opérations importante',
	scotTypeLabel: 'Type de catégorie d’opérations importante',
	hasEstimateLabel: 'Une estimation a-t-elle une incidence sur cette catégorie d’opérations importante?',
	itAPPUsedLabel: 'Une application informatique est-elle utilisée?',
	routine: 'Courante',
	nonRoutine: 'Non courante',
	controls: 'Contrôles',
	substantive: 'Corroboration',
	yes: 'Oui',
	scotNameErrorMsg: 'Obligatoire',
	remove: 'Retirer',
	noOptionsMessage: 'Aucun résultat',
	disabledTabTooltipMessage: 'Sélectionner « Une application informatique est-elle utilisée? » pour associer les applications informatiques.',
	itAppLabels: {
		itApplications: 'Applications informatiques associées',
		tabLabel: 'Applications informatiques',
		dropdownLabel: 'Associer les applications informatiques',
		noRelatedItems: 'Aucune application informatique associée'
	},
	soLabels: {
		serviceOrganizations: 'Sociétés de services associées',
		tabLabel: 'SS',
		dropdownLabel: 'Associer des sociétés de services',
		noRelatedItems: 'Aucune société de services associée'
	},
	wtDetailPrefix: '<p>Nous confirmons notre compréhension de toutes les catégories d’opérations importantes courantes et non courantes et de tous les processus liés aux informations à fournir importantes à chaque période au moyen de tests de cheminement. De plus, dans le cas des audits réalisés selon les normes du PCAOB, nous effectuons des tests de cheminement des catégories d’opérations d’estimation importantes.<br/>Dans le cas de toutes les catégories d’opérations importantes où nous recourons à une stratégie axée sur les contrôles et dans le cas des contrôles qui répondent à des risques importants, nous confirmons que les contrôles pertinents ont été conçus et mis en place de façon appropriée. Nous confirmons que notre décision de recourir à une stratégie axée sur les contrôles convient toujours.<br/><br/>Nous concluons que notre documentation décrit avec exactitude le fonctionnement de la catégorie d’opérations importante et que nous avons relevé toutes les erreurs possibles pertinentes, y compris celles qui découlent du recours à l’informatique, et les contrôles connexes (le cas échéant).<br/><br/> Nous devons déterminer si notre compréhension des catégories d’opérations d’estimation importantes qui font l’objet d’une stratégie corroborative est appropriée à la lumière de nos procédures de corroboration.</p>',
};

export const ViewSampleItemLabels = {
	previous: 'Précédente',
	next: 'Suivante',
	sampleDateLabel: 'Date de l’échantillon',
	attributesHeader: 'Attributs',
	statusHeader: 'État',
	noAttributesLabel: 'Aucun attribut disponible.',
	present: 'Présente',
	presentWithComments: 'Présent avec des commentaires',
	notPresent: 'Absente',
	notApplicatable: 'Sans objet',
	naLabel: 'S. O.',
	additionDocumentation: 'Documentation additionnelle',
	deleteSampleHeader: 'Supprimer l’échantillon',
	deleteSmapleDescription: 'Voulez-vous vraiment supprimer l’échantillon sélectionné? Cette action ne peut pas être annulée.',
	deleteSamplePreText: 'Description de l’échantillon',
	relateTagModalTitle: 'Associer des balises à l’échantillon',
	relateTagModalDescription: "Associer une ou plusieurs balises à l’échantillon. Pour ajouter une nouvelle balise, cliquez sur <b>'Gérer les balises'</b>. Les associations aux balises ne seront pas archivées, mais les balises le seront et pourront donc être récupérées.",
	relateTagTableHeader: 'Nom de la balise',
	relateTagTableSubHeader: 'Groupe de balises',
	tagsCounter: '{0} balises',
	tagCounter: '{0} balise',
	relateTagGroupLabel: 'Groupe de balises',
	relateTagSearchPlaceholder: 'Rechercher',
	relateTagClearSearch: 'Effacer',
	relateTagShowSelectedOnly: 'Afficher seulement la balise associée',
	manageTagsLabel: 'Gérer les balises',
	addTag: 'Ajouter une balise',
	supportingDocumentationTitle: 'Pièces justificatives',
	dropdownAll: 'Toutes',
	noResultsLabel: 'Aucun résultat',
	noDataLabel: 'Aucune donnée trouvée',
	attributeStatusModalTitle: 'Mark all as present',
	attributeStatusModalCancelButton: 'Annuler',
	attributeStatusModalConfirmButton: 'Enregistrer',
	attributeStatusModalDescription: 'Voulez-vous vraiment marquer ces attributs comme étant présents? Seuls les attributs pour lesquels aucun état n’est sélectionné seront marqués comme étant présents.',
	attributeModalDeleteErrorMessage: 'L’état de l’attribut ne peut pas être mis à jour. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.',
};

export const ShortRiskTypeForAres = {
	1: 'Important',
	2: 'Fraude',
	3: 'Inhérent',
	4: 'Risque très faible',
	5: 'Risque faible',
	6: 'Risque élevé',
	7: 'Non sélectionné'
};

export const RelateEstimateToAssertionLabels = {
	relateAccountsAndAssertions: 'Associer des comptes et des assertions',
	relateAccountsToEstimate: 'Associer des comptes à une estimation',
	accounts: 'Comptes',
	designation: 'Désignation',
	relatedAssertions: 'Assertions associées',
	accountNameField: 'accountName',
	accountTypeIdField: 'accountTypeId',
	assertionsField: 'assertions',
	executionTypeIdField: 'executionTypeId',
	notSelected: 'Non sélectionnée',
	pathField: 'chemin',
	noAccountsAvailable: 'Aucun compte disponible',
	noRelatedAccounts: 'Aucun compte associé à l’estimation',
	accountId: 'Identifiant du compte',
	remove: 'Supprimer'
};

//Send instructions switcher
export const sendIntructionsSwitcherLabels = {

	[sendInstructionsSwitcherIds.groupInstructions]: 'Instructions relatives à l’audit de groupe',
	[sendInstructionsSwitcherIds.groupRiskAssessment]: 'Évaluation des risques au niveau du groupe'
};

export const RelateEstimateToAccountLabels = {
	relatEstimatesToAccount: 'Associer des estimations au compte',
	showOnlyRelatedEstimates: 'Afficher seulement les estimations associées',
	noEstimatesResult: labels.noResultsFound,
	noEstimatesLabel: 'Aucune estimation n’a été créée dans la mission',
	estimateNameHeader: 'Nom de l’estimation',
	relatedEstimateCounter: '{0} estimation',
	relatedEstimatesCounter: '{0} estimations',
	relatedAccount: 'Compte / information à fournir',
	close: labels.close
};

export const RelateAccountsToEstimateLabels = {
	relateAccountsToEstimate: 'Associer des comptes à une estimation',
	showOnlyRelatedAccounts: 'Afficher seulement les comptes associés',
	noAccountsResult: labels.noResultsFound,
	noAccountsLabel: 'Aucun compte créé dans la mission',
	accountNameHeader: 'Nom du compte',
	relatedAccountCounter: '{0} compte',
	relatedAccountsCounter: '{0} comptes',
	relatedEstimate: labels.estimate,
	close: labels.close
};

export const SupportingDocumentationLabels = {
	evidence: 'Éléments probants',
	priorPeriod: 'Période précédente',
	temporaryFiles: 'Fichiers temporaires',
	externalDocuments: 'Documents externes',
	addEvidenceBtn: 'Ajouter un élément probant',
	addTemporaryFilesBtn: 'Ajouter un fichier temporaire',
	notes: 'Notes',
	signOffs: 'Approbations',
	name: 'Nom',
	supportingDocumentationTitle: 'Pièces justificatives',
	temporaryFilesEmptyPlaceholder1: 'Aucun document temporaire associé.',
	temporaryFilesEmptyPlaceholder2: 'Pour associer un document temporaire, cliquez sur {addTemporaryFiles}.',
	evidencePlaceholderLine1: 'Aucun élément probant associé.',
	evidencePlaceholderLine2: 'Pour associer un élément probant, cliquez sur {addEvidenceBtn}.',
	removeFromSample: 'Supprimer de l’échantillon',
	unlink: 'Dissocier',
	retailControlEvidenceLabel: 'Avons-nous conservé les éléments probants à l’appui du contrôle afin d’étayer nos tests des attributs pour cet élément d’échantillon précis?',
	removeEvidenceModalTitle: 'Supprimer les éléments probants de l’échantillon',
	removeEvidenceModalDesc: 'Voulez-vous vraiment supprimer tous les éléments probants de cet échantillon?',
	removeEvidenceErrorMessage: 'Ces documents ne sont plus disponibles pour cet échantillon. Veuillez actualiser la page et réessayer. Si le problème persiste, veuillez communiquer avec le Service de dépannage.'
}

export const deleteSampleItemAttributeModal = {
	modalDescription: 'Voulez-vous vraiment supprimer la sélection pour cet attribut? Les informations supplémentaires seront supprimées.',
	modalTitle: 'Supprimer la sélection',
	modalConfirmButton: 'Supprimer',
	modalCancelButton: 'Annuler',
	additionalDocumentationLabel: 'Informations supplémentaires'
}
export const accountsFilterLabels = [{
	id: accountsFilter.allAccounts,
	label: 'Tous les comptes',
	value: accountsFilter.allAccounts
},
{
	id: accountsFilter.accountsWithRelatedEstimates,
	label: 'Comptes avec des estimations associées',
	value: accountsFilter.accountsWithRelatedEstimates
},
{
	id: accountsFilter.accountsWithoutRelatedEstimates,
	label: 'Comptes sans estimations associées',
	value: accountsFilter.accountsWithoutRelatedEstimates
}
];

export const changeSampleItemAttributeModal = {
	modalDescription: 'Voulez-vous vraiment modifier la sélection pour cet attribut? Les informations supplémentaires seront supprimées.',
	modalTitle: 'Modifier la sélection',
	modalConfirmButton: 'Modifier',
	modalCancelButton: 'Annuler'
}
export const scotsFilterLabels = [{
	id: scotsFilter.allScots,
	label: 'Toutes les catégories d’opérations importantes',
	value: scotsFilter.allScots
},
{
	id: scotsFilter.scotsWithRelatedEstimates,
	label: 'Catégories d’opérations importantes avec des estimations associées',
	value: scotsFilter.scotsWithRelatedEstimates
},
{
	id: scotsFilter.scotsWithoutRelatedEstimates,
	label: 'Catégories d’opérations importantes sans estimations associées',
	value: scotsFilter.scotsWithoutRelatedEstimates
}
];

export const CreateEditTagGroupLabels = {
	createModalTitle: 'Nouveau groupe de balises d’échantillon',
	editModalTitle: 'Modifier le groupe de balises d’échantillon',
	createModalDescription: "Entrez les détails relatifs au groupe de balises ci-dessous et sélectionnez <b>\'Enregistrer et fermer\'</b> pour terminer. Pour créer un autre groupe de balises, sélectionnez <b>\’Enregistrer et créer\'</b>.",
	editModalDescription: "Modifiez les détails relatifs au groupe de balises ci-dessous et sélectionnez'<b>{0}</b>' pour terminer.",
	close: 'Fermer',
	cancel: 'Annuler',
	save: 'Enregistrer',
	saveAndCloseLabel: 'Enregistrer et fermer',
	saveAndCreateLabel: 'Enregistrer et créer',
	tagGroupNameLabel: 'Nom du groupe de balises (obligatoire)',
	required: 'Obligatoire'
};

export const CreateEditTagLabels = {
	createModalTitle: 'Nouvelle balise d’échantillon',
	editModalTitle: 'Edit sample tag',
	createModalDescription: 'Entrez les détails relatifs à la balise ci-dessous et sélectionnez ’<b>Enregistrer et fermer</b>’ pour terminer. Pour créer une autre balise, sélectionnez ’<b>Enregister et créer</b>’.',
	editModalDescription: `Edit the tag details below and select'<b>{0}</b>' to finish.`,
	tagNameLabel: 'Nom de la balise (obligatoire)',
	tagGroupNameLabel: 'Nom du groupe de balises (obligatoire)',
	tagColorLabel: 'Couleur (obligatoire)',
	saveAndCloseLabel: 'Enregistrer et fermer',
	saveAndCreateLabel: 'Enregistrer et créer',
	cancelLabel: 'Annuler',
	required: 'Obligatoire',
	save: 'Enregistrer',
	noresultsLabel: 'Aucun groupe de balises disponible',
	tagColors: [{
		text: 'Rouge',
		color: 'rouge'
	},
	{
		text: 'Orange',
		color: 'orange'
	},
	{
		text: 'Turquoise',
		color: 'turquoise'
	},
	{
		text: 'Bleu',
		color: 'bleu'
	},
	{
		text: 'Violet',
		color: 'violet'
	},
	{
		text: 'Vert',
		color: 'vert'
	}
	]
};

export const ITProcessFlowLabels = {
	itProcess: 'Processus informatique',
	technology: 'Technologie',
	technologies: 'Technologies',
	technologyName: 'Nom de la technologie',
	supportingTechnologyName: 'Nom de la technologie à l’appui',
	technologyType: 'Type de technologie',
	showOnlyRelated: 'Afficher seulement la technologie associée',
	technologiesCounter: '{0} technologies',
	technologyCounter: '{0} technologie',
	supportingTechnologyLabel: 'Technologie à l’appui',
	relatedITAppNoDataPlaceholder: 'Aucune technologie n’est associée au processus informatique.',
	relateTechnology: 'Associer une technologie',
	supportingITPAppNoDataPlaceholder: 'Aucune technologie à l’appui pour le processus informatique.',
	itRiskHeader: 'Risques liés aux TI',
	itgcHeader: 'CGI',
	itspHeader: 'Procédures de corroboration liées aux TI',
	noDataPlaceholderITGC: 'Au moins un CGI doit être indiqué ou il doit être indiqué que le risque lié aux TI n’est associé à aucun CGI. Créez {createNewITGC}, {relateITGC} ou indiquez qu’aucun {noITGC} ne répond au risque lié aux TI.',
	noDataPlaceholderITSP: 'Si nous avons évalué que les CGI sont inefficaces ou que nous n’avons relevé aucun CGI qui réponde au risque lié aux TI, il se peut que nous puissions mettre en œuvre des procédures de corroboration liées aux TI pour obtenir l’assurance raisonnable que le risque lié aux TI dans le processus informatique associé au CGI inefficace n’a pas été exploité. Créez {createNewITSP} ou {relateITSP}.',
	noRecordsFound: 'Aucun risque lié aux TI identifié pour ce processus informatique.',
	noITGCPlaceholder: 'Aucun CGI ne répond au risque lié aux TI.',
	relateSupportingTechnology: 'Associer une technologie à l’appui',
	relatedTechnologiesNotAvailable: 'Les technologies associées ne sont pas disponibles pour ce document.',
	supportingTechNotAvailable: 'Les technologies à l’appui ne sont pas disponibles pour ce document.',
	relatedITRisksNotAvailable: 'Les risques liés aux TI associés ne sont pas disponibles pour ce document.',
	relateITGC: 'Relate ITGCs',
	itRisk: 'Risque lié aux TI',
	itgcCounter: '{0} CGI',
	itgcsCounter: '{0} CGI',
	itgcName: 'Nom du CGI',

};

export const ITRisksFlowLabels = {
	itRisk: 'Risque lié aux TI',
	relatedITRiskNoDataPlaceholder: 'Aucun risque lié aux TI associé au processus informatique.',
	newITRisk: 'Nouveau risque lié aux TI',
	relatedITRisksNotAvailable: 'Les risques liés aux TI associés ne sont pas disponibles pour ce document.',
	deleteConfirmLabel: 'Voulez-vous vraiment supprimer le risque lié aux TI sélectionné? Cette action ne peut pas être annulée.',
	deleteITRisk: 'Supprimer le risque lié aux TI',
	CreateITFlowITRiskLabels: {
		close: 'Fermer',
		cancel: 'Annuler',
		yes: 'Oui',
		no: 'Non',
		delete: 'supprimer',
		save: 'Enregistrer',
		saveAndCloseLabel: 'Enregistrer et fermer',
		saveAndCreateLabel: 'Enregistrer et ajouter',
		newITRiskLabel: 'Nouveau risque lié aux TI',
		itRiskNameLabel: 'Nom du risque lié aux TI (obligatoire)',
		itRiskCharactersLabel: 'caractères',
		itRiskOfLabel: 'de',
		confirm: 'Confirmer',
		confirmChanges: 'Confirmer',
		itRiskNameErrorMsg: 'Obligatoire',
		itProcessNotSelectedErrorMsg: 'Obligatoire',
		hasNoITGCLabel: 'Aucun CGI ne répond au risque lié aux TI.',
		createModalDescription: 'Entrez les détails relatifs au risque lié aux TI ci-dessous et sélectionnez  ’<b>Enregistrer et fermer</b>’ pour terminer. Pour créer un autre risque lié aux TI, sélectionnez ’<b>Enregistrer et ajouter</b>’.',
		relatedITProcessLabel: 'Processus informatique',
		inputInvaildCharacters: 'L’entrée ne peut pas contenir les caractères suivants : */:<>\\?|"',
		remove: 'Remove',
		editModalDescription: 'Modifiez les détails relatifs au risque lié aux TI ci-dessous et sélectionnez ’<b>Enregistrer</b>’ pour terminer.',
		editITRiskLabel: 'Edit IT risk'
	}
};

export const ITProcessListingLabels = {
	all: 'Tous',
	manageChange: 'Gérer les modifications',
	manageAccess: 'Gérer les accès',
	manageSecuritySettings: 'Gérer des paramètres de sécurité',
	itOperations: 'Opérations informatiques',
	systemImplementation: 'Mise en œuvre de systèmes',
	category: 'Catégorie',
	uncategorized: 'Non classé',
	technologies: 'Technologies',
};

export const ITProcessQuickFilterOptions = {
	0: ITProcessListingLabels.all,
	1: ITProcessListingLabels.manageChange,
	2: ITProcessListingLabels.manageAccess,
	3: ITProcessListingLabels.manageSecuritySettings,
	4: ITProcessListingLabels.itOperations,
	5: ITProcessListingLabels.systemImplementation
}

// Relate Technology Modal
export const RelateTechnologyModalLabels = {
	relateTechnology: 'Associer une technologie',
	relatedTechnologiesDescription: 'Nom de la technologie',
	supportingTechnologyName: 'Nom de la technologie à l’appui',
	technology: '{0} technology',
	technologies: '{0} technologies'
};

export const AccountStandardROMMListingLabels = {
	accountRisksNotAvailableForDocument: 'Les risques au niveau du compte ne sont pas disponibles pour ce document.',
	noRelatedObject: 'Aucun objet associé. Associer un objet pour commencer.',
	noResultsFound: 'Aucun risque disponible.',
	acceptedText: 'Accepté',
	rejectedText: 'Rejeté',
	allRisksRejected: 'Tous les risques ont été rejetés.',
	relevantAssertions: 'Assertions pertinentes',
	rejectLabel: 'Rejeter',
	acceptLabel: 'Accepter',
	rejectionRationaleLabel: 'Raison du rejet',
	rejectionCategoryText: 'Catégorie du rejet',
	editRejectionRationaleText: 'Modifier la raison du rejet',
	rejectionRationalePlaceholder: "Are you sure you want to reject the selected risk? Enter the details below and select <strong>\'Reject\'</strong>.",
	cancel: 'Cancel',
	rejectionRationaleTextAreaPlaceholder: 'Rationale (required)',
	rejectionCategoryDropdownPlaceholder: 'Rejection category (required)',
	required: 'Required',
	preRejectedText: 'Pre-rejected',
	additionalContextLabel: 'Additional context',
	additionalContextwhyRiskShouldBeRejected: 'Click {0} to add additional context specific to this client for why this risk should be rejected.',
	hereLink: 'here',
	editAdditionalContextText: 'Edit additional context'
}

export const RejectionCategory = [{
	id: 1,
	label: 'Probabilité ‒ aucune possibilité raisonnable de se produire (sans tenir compte des contrôles)'
},
{
	id: 2,
	label: 'Ampleur ‒ anomalie potentielle non significative'
},
{
	id: 3,
	label: 'Risque non significatif pour cette mission'
},
{
	id: 4,
	label: 'Risque d’anomalies significatives traité dans le cadre d’une autre mission (audits de groupe seulement)'
},
]

export const formLabels = {
	required: labels.required,
	maxLength: labels.maxLength
};

export const paginationLabels = {
	show: labels.pagingShowtext,
	first: 'Première page',
	last: 'Dernière page',
	prev: 'Page précédente',
	next: 'Page suivante'
};
